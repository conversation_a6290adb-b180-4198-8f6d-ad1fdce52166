package vn.fis.eapprove.business.consumer;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.config.broker.KafkaConfig;
import vn.fis.eapprove.business.domain.dashboard.service.DashboardTaskService;
import vn.fis.eapprove.business.domain.report.service.ReportByChartNodeServiceNew;

@Slf4j
@Component
@ConditionalOnBean(KafkaConfig.class)
@AllArgsConstructor
public class ReportByChartNodeConsumer {

    private ReportByChartNodeServiceNew reportByChartNodeServiceNew;
    private DashboardTaskService dashboardTaskService;

    @KafkaListener(topics = "${spring.kafka.consumer.topic.insert-report-by-chart-node}",
            groupId = "${spring.kafka.consumer.group-id.insert-report-by-chart-node}",
            clientIdPrefix = "json",
            containerFactory = "kafkaListenerObjectContainerFactory", concurrency = "1")
    public void processInsertReportByChartNode(@Payload String taskId, Acknowledgment acknowledgment) {
        try {
            log.debug("Processing insert report by chart node with task Id: {}", taskId);
            reportByChartNodeServiceNew.createReportByChartNode(taskId);

            // dashboard
            dashboardTaskService.createDashboardTask(taskId);
        } finally {
            acknowledgment.acknowledge();
        }
    }
}
