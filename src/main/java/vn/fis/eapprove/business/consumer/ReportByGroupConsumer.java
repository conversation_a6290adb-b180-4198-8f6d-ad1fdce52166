package vn.fis.eapprove.business.consumer;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.config.broker.KafkaConfig;
import vn.fis.eapprove.business.domain.dashboard.service.DashboardTicketService;
import vn.fis.eapprove.business.domain.report.service.ReportByGroupServiceNew;

@Slf4j
@Component
@ConditionalOnBean(KafkaConfig.class)
@AllArgsConstructor
public class ReportByGroupConsumer {

    private ReportByGroupServiceNew reportByGroupServiceNew;
    private DashboardTicketService dashboardTicketService;

    @KafkaListener(topics = "${spring.kafka.consumer.topic.insert-report-by-group}",
            groupId = "${spring.kafka.consumer.group-id.insert-report-by-group}",
            clientIdPrefix = "json",
            containerFactory = "kafkaListenerObjectContainerFactory", concurrency = "1")
    public void processInsertReportByGroup(@Payload Long procInstId, Acknowledgment acknowledgment) {
        try {
            log.debug("Processing insert report by group with ticket Id: {}", procInstId);
            reportByGroupServiceNew.createReportByGroup(procInstId);

            // dashboard
            dashboardTicketService.createDashboardTicket(procInstId);
        } finally {
            acknowledgment.acknowledge();
        }
    }
}
