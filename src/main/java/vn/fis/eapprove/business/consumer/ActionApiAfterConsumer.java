package vn.fis.eapprove.business.consumer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.config.broker.KafkaConfig;
import vn.fis.eapprove.business.model.ActionApiExecuteResult;
import vn.fis.eapprove.business.tenant.manager.ActionApiService;

@Slf4j
@Component
@ConditionalOnBean(KafkaConfig.class)
public class ActionApiAfterConsumer {

    @Autowired
    private ActionApiService actionApiService;

    @KafkaListener(topics = "${spring.kafka.consumer.topic.action-api-after}", groupId = "${spring.kafka.consumer.group-id.group-action-api-after}", clientIdPrefix = "json", containerFactory = "kafkaListenerObjectContainerFactory")
    public void processCallActionApiAfter(ActionApiExecuteResult actionApi, Acknowledgment ack) {
        try {
            log.debug("(KAFKA) Start process call api after ............");
            actionApiService.callApi(actionApi, false, true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            ack.acknowledge();
        }
    }
}
