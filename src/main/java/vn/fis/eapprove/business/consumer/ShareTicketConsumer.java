package vn.fis.eapprove.business.consumer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.config.broker.KafkaConfig;
import vn.fis.eapprove.business.domain.manageApi.service.ManageShareTicketService;
import vn.fis.eapprove.business.model.request.ProcessShareTicketRequest;

@Slf4j
@Component
@ConditionalOnBean(KafkaConfig.class)
public class ShareTicketConsumer {
    @Autowired
    private ManageShareTicketService manageShareTicketService;

    @KafkaListener(topics = "${spring.kafka.consumer.topic.share-ticket}",
            groupId = "${spring.kafka.consumer.group-id.group-share-ticket}", clientIdPrefix = "json", containerFactory = "kafkaListenerObjectContainerFactory")
    public void processShareTicket(ProcessShareTicketRequest payload, Acknowledgment ack) {
        try {
            log.debug("(KAFKA) Start process share ticket, payload = {}", payload);
            manageShareTicketService.processShareTicket(
                    payload.getProcInstId(),
                    payload.getProcDefId(),
                    payload.getCompanyCode(),
                    payload.getCreatedUser(),
                    payload.getChartNodeCode(),
                    payload.getServiceId(),
                    payload.getCreatedDate()
            );
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            ack.acknowledge();
        }
    }
}
