//package vn.fis.eapprove.business.consumer;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
//import org.springframework.kafka.annotation.KafkaListener;
//import org.springframework.messaging.handler.annotation.Header;
//import org.springframework.messaging.handler.annotation.Payload;
//import org.springframework.stereotype.Component;
//import vn.fis.eapprove.business.config.broker.HeaderConfig;
//import vn.fis.eapprove.business.config.broker.KafkaConfig;
//import vn.fis.eapprove.business.tenant.manager.BusinessService;
//import vn.fis.spro.common.constants.TopicConstants;
//import vn.fis.spro.common.model.request.RequestCallLogAdmin;
//
//@Slf4j
//@Component
//@ConditionalOnBean(KafkaConfig.class)
//public class CallLogAdminConsumer {
//
//    @Autowired
//    private BusinessService businessService;
//
//    @KafkaListener(topics = TopicConstants.TOPIC_CALL_LOG_ADMIN, groupId = TopicConstants.GROUP_CALL_LOG_ADMIN, clientIdPrefix = "json", containerFactory = "kafkaListenerObjectContainerFactory")
//    public void callSaveLogAdmin(@Payload RequestCallLogAdmin request, @Header(HeaderConfig.TENANT) String tenant) {
//        try {
//            log.info("START CALL LOG ADMIN ={}",request);
//            businessService.callSaveLogAdmin(request.getParams(), request.getUuid());
//        } catch (Exception e) {
//            log.error("ERROR: ", e);
//        }
//    }
//
//    @KafkaListener(topics = TopicConstants.TOPIC_CALL_LOG_ADMIN, groupId = TopicConstants.GROUP_CALL_LOG_ADMIN, clientIdPrefix = "json", containerFactory = "kafkaListenerObjectContainerFactory")
//    public void callSaveLogAdmin2(@Payload RequestCallLogAdmin request, @Header(HeaderConfig.TENANT) String tenant) {
//        try {
//            log.info("START CALL LOG ADMIN. ={}",request);
//            businessService.callSaveLogAdmin(request.getParams(), request.getUuid());
//        } catch (Exception e) {
//            log.error("ERROR: ", e);
//        }
//    }
//}
