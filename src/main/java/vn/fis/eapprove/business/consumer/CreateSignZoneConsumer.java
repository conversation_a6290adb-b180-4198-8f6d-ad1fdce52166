package vn.fis.eapprove.business.consumer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.config.broker.KafkaConfig;
import vn.fis.eapprove.business.domain.bpm.service.BpmTpSignZoneManager;
import vn.fis.eapprove.business.model.request.BpmPrintSignZoneRequest;

@Slf4j
@Component
@ConditionalOnBean(KafkaConfig.class)
public class CreateSignZoneConsumer {

    @Autowired
    private BpmTpSignZoneManager bpmTpSignZoneManager;

    @KafkaListener(topics = "${spring.kafka.consumer.topic.create-sign-zone}", groupId = "${spring.kafka.consumer.group-id.create-sign-zone}", clientIdPrefix = "json", containerFactory = "kafkaListenerObjectContainerFactory")
    public void processCreateSignZoneConsumer(BpmPrintSignZoneRequest printSignZoneRequest, Acknowledgment ack) {
        try {
            log.debug("KAFKA - Processing create sign zone: {}", printSignZoneRequest);
            bpmTpSignZoneManager.processCreateSignZone(printSignZoneRequest);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            ack.acknowledge();
        }
    }
}
