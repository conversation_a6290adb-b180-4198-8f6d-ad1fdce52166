package vn.fis.eapprove.business.consumer;

//@Slf4j
//@Component
//@ConditionalOnBean(KafkaConfig.class)
//public class AutoCompleteTaskConsumer {
//    @Autowired
//    private BpmTaskManager bpmTaskManager;
//
//    @KafkaListener(topics = TopicConstants.TOPIC_AUTO_COMPLETE_TASK, groupId = "${spring.kafka.consumer.group-id.group-auto-complete-task}", clientIdPrefix = "json", containerFactory = "kafkaListenerObjectContainerFactory")
//    public void processShareTicket(ProcessAutoCompleteTask payload, Acknowledgment ack) {
//        try {
//            log.debug("(KAFKA) Start process auto complete task, payload = {}", payload);
//            bpmTaskManager.autoCompleteTask(payload.getCompletedTaskId(), payload.getProcInstId(), payload.getOldTaskDefKey(), payload.getBpmTask());
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//        } finally {
//            ack.acknowledge();
//        }
//    }
//}
