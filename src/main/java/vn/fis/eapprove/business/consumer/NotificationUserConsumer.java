package vn.fis.eapprove.business.consumer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.config.broker.KafkaConfig;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcdefNotificationService;
import vn.fis.eapprove.business.model.NotificationUser;

@Slf4j
@Component
@ConditionalOnBean(KafkaConfig.class)
public class NotificationUserConsumer {

    @Autowired
    private BpmProcdefNotificationService bpmProcdefNotificationService;

    @KafkaListener(topics = "${spring.kafka.consumer.topic.notification-user}",
            groupId = "${spring.kafka.consumer.group-id.group-notification-user}",
            clientIdPrefix = "json", containerFactory = "kafkaListenerObjectContainerFactory")
    public void processNotificationUser(NotificationUser payload, Acknowledgment ack) {
        try {
            log.debug("(KAFKA) Start process create notification user, action = {}, payload = {}", payload.getActionCode(), payload);
            bpmProcdefNotificationService.addNotificationsByConfig(
                    payload.getBpmProcdefId(),
                    payload.getNextTaskDefKey(),
                    payload.getVariables(),
                    payload.getTicketId(),
                    payload.getIsGetOldVariable(),
                    payload.getLstCustomerEmails(),
                    payload.getActionCode(),
                    payload.getEmailExe()
            );
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            ack.acknowledge();
        }
    }
}
