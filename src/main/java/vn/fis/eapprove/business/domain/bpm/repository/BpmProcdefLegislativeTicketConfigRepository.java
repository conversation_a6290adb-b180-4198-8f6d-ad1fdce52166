package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefInherits;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefLegislativeTicketConfig;

import java.util.List;

@Repository
public interface BpmProcdefLegislativeTicketConfigRepository extends JpaRepository<BpmProcdefLegislativeTicketConfig, Long>, JpaSpecificationExecutor<BpmProcdefInherits> {

    List<BpmProcdefLegislativeTicketConfig> getAllByBpmProcdefIdAndProcDefId(Long bpmProcdefId, String procDefId);

    List<BpmProcdefLegislativeTicketConfig> getAllByBpmProcdefId(Long bpmProcdefId);
}
