package vn.fis.eapprove.business.domain.location.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.location.entity.LocationManagement;


import java.util.List;

@Repository
public interface LocationManagementRepository extends JpaRepository<LocationManagement, Long>, JpaSpecificationExecutor<LocationManagement> {
    @Query("SELECT u FROM LocationManagement u " +
            "WHERE LOWER(u.locationName)  LIKE lower (CONCAT('%',:search,'%'))")
    List<LocationManagement> getListLocation(@Param("search") String search);

    @Query("SELECT COUNT (u) FROM LocationManagement u WHERE u.locationName = :location")
    Long numLocation(String location);

    List<LocationManagement> findByLocationName(String name);

    @Query("SELECT distinct l.workingTimeCode FROM LocationManagement l ")
    List<String> getAllListWorkingCodeInLocationManagement();

}
