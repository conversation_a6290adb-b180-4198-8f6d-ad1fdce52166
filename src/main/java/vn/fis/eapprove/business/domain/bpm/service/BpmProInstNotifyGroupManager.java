package vn.fis.eapprove.business.domain.bpm.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProInstNotifyGroup;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProInstNotifyGroupRepository;


import java.util.List;


@Service("BpmProInstNotifyGroupManagerV1")
@Slf4j
@Transactional

public class BpmProInstNotifyGroupManager {

    @Autowired
    BpmProInstNotifyGroupRepository bpmProInstNotifyGroupRepository;

    public void saveAll(List<BpmProInstNotifyGroup> bpmProInstNotifyGroups) {
        bpmProInstNotifyGroupRepository.saveAll(bpmProInstNotifyGroups);
    }

    public List<BpmProInstNotifyGroup> getDetail(Long ticketId) {
        return bpmProInstNotifyGroupRepository.getNotifyGroupsByBpmProcinstId(ticketId);
    }

    public void deleteAllByBpmProcinstId(Long ticketId) {
        bpmProInstNotifyGroupRepository.deleteAllByBpmProcinstId(ticketId);
    }
}
