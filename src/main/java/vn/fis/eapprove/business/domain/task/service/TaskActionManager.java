package vn.fis.eapprove.business.domain.task.service;


import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.task.entity.TaskAction;
import vn.fis.eapprove.business.domain.task.repository.TaskActionRepository;
import vn.fis.eapprove.business.model.response.TaskActionResponse;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class TaskActionManager {

    @Autowired
    private TaskActionRepository taskActionRepository;

    @Autowired
    private ModelMapper modelMapper;

    public List<TaskActionResponse> getAllTaskActionResponse() {
        try {
            List<TaskAction> taskActions = taskActionRepository.findAll();
            return taskActions.stream().map(x -> modelMapper.map(x, TaskActionResponse.class)).collect(Collectors.toList());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * Find all
     *
     * <AUTHOR>
     */
    public List<TaskAction> findAll() {
        return taskActionRepository.findAll();
    }

    /**
     * Find all active task action
     *
     * <AUTHOR>
     */
    public List<TaskAction> findAllActiveTaskAction() {
        return taskActionRepository.findAllActiveTaskAction();
    }

    /**
     * Get data map action code to task action
     *
     * <AUTHOR>
     */
    public Map<String, TaskAction> getActionCodeToTaskActionMap() {
        Map<String, TaskAction> result = new HashMap<>();
        List<TaskAction> taskActions = findAllActiveTaskAction();
        if (!ValidationUtils.isNullOrEmpty(taskActions)) {
            result.putAll(taskActions.stream().collect(Collectors.toMap(TaskAction::getCode, Function.identity())));
        }

        return result;
    }
}
