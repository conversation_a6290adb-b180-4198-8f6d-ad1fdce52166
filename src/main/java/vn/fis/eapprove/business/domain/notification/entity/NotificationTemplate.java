package vn.fis.eapprove.business.domain.notification.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;

import jakarta.persistence.*;
import java.util.Date;
import java.util.Set;

@Data
@Builder
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "notification_template")
public class NotificationTemplate {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "type")
    private String type;
    @Column(name = "title")
    private String title;
    @Column(name = "action_code")
    private String actionCode;
    @Column(name = "notification_object")
    private String notificationObject;
    @Column(name = "source_type")
    private String sourceType;
    @Column(name = "share_with")
    private String shareWith;
    @Column(name = "content")
    private String content;
    @Column(name = "create_at")
    private Date createAt;
    @Column(name = "update_at")
    private Date updateAt;
    @Column(name = "user_create")
    private String userCreate;
    @Column(name = "type_noti")
    private String typeNoti;
    @Column(name = "user_update")
    private String userUpdate;
    @Column(name = "company_code")
    private String companyCode;
    @Column(name = "company_name")
    private String companyName;

    @OneToMany(mappedBy = "notificationTemplate")
    @JsonIgnore
    private Set<PermissionDataManagement> permissionDataManagements;
}
