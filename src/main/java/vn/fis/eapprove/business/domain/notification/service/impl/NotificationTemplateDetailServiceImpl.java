package vn.fis.eapprove.business.domain.notification.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.notification.entity.NotificationTemplateDetail;
import vn.fis.eapprove.business.domain.notification.repository.NotificationTemplateDetailRepository;
import vn.fis.eapprove.business.domain.notification.service.NotificationTemplateDetailService;
import vn.fis.eapprove.business.utils.ResponseUtils;

import java.util.List;

/**
 * Author: AnhVTN
 * Date: 28/02/2023
 */

@Service
@Slf4j
@Transactional
public class NotificationTemplateDetailServiceImpl implements NotificationTemplateDetailService {

    private final NotificationTemplateDetailRepository notificationTemplateRepository;
    private final ResponseUtils response;

    @Autowired
    public NotificationTemplateDetailServiceImpl(NotificationTemplateDetailRepository notificationTemplateRepository, ResponseUtils response) {
        this.notificationTemplateRepository = notificationTemplateRepository;
        this.response = response;
    }

    @Override
    public List<NotificationTemplateDetail> templateDetail(Long templateId) {
        return this.notificationTemplateRepository.getDetailFromTemplateId(templateId);
    }

    @Override
    public void deleteAllByNotificationTemplateId(Long templateId) {
        notificationTemplateRepository.deleteAllByNotificationTemplateId(templateId);
    }
}
