package vn.fis.eapprove.business.domain.system.service;


import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.system.entity.SystemGroup;
import vn.fis.eapprove.business.model.request.AddSystemGroupRequest;
import vn.fis.eapprove.business.model.request.SystemGroupRequest;
import vn.fis.eapprove.business.model.request.SystemGroupSearch;
import vn.fis.eapprove.business.model.response.SystemGroupResponse;


import java.util.List;

@Service
@Transactional
public interface SystemGroupService {
    ResponseEntity<?> createUpdate(SystemGroupRequest systemGroup) ;

    ResponseEntity<SystemGroupResponse> findAllById(Long id) ;

    ResponseEntity<List<SystemGroupResponse>> findByGroupType(SystemGroupSearch request) ;

    List<SystemGroup> findByGroupTypeFilter(SystemGroupSearch request) ;

    ResponseEntity<Object> findDataByTableName(String tableName,Object filter) ;

    ResponseEntity<Object> getUserGroup() ;

    ResponseEntity<?> addSystemGroup(AddSystemGroupRequest request) ;

    Boolean update(List<Long> ids,Integer status) ;

    ResponseEntity<Boolean> checkDuplicateName(String tableName,String name,Long id);

    List<Long> getListIdSystemGroup(String groupType, String username);

    Boolean checkIdInSystemGroup(String groupType, String username, String groupValue);
}
