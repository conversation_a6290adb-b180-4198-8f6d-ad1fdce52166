package vn.fis.eapprove.business.domain.legislative.repository;

import jakarta.persistence.Tuple;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.legislative.model.entity.LegislativeProgram;

import java.util.List;

@Repository
public interface LegislativeProgramRepository extends JpaRepository<LegislativeProgram, Long>, JpaSpecificationExecutor<LegislativeProgram> {

    LegislativeProgram findLegislativeProgramById(Long id);

    @Query(value = "select bp.ticketTitle as ticketTitle," +
            "bp.ticketId as ticketId ," +
            "bp.requestCode as requestCode,bp.ticketProcInstId as procInstId," +
            "s.bpmProcdef.procDefId as procDefId, " +
            "bp.ticketStatus as ticketStatus, " +
            "bp.ticketStartActId as startKey " +
            "from BpmProcInst bp " +
            "         INNER JOIN ServicePackage s on bp.serviceId = s.id " +
            "where (bp.ticketStartUserId = :username " +
            "   or bp.createdUser = :username) " +
            "    and s.status != 'DRAFT' " +
            "    and s.legislativeRequirement = true" +
            " and bp.legislativeId is null " +
            "ORDER BY bp.id DESC ")
    List<Tuple> initialCreateTicket(@Param("username") String username);

    @Query(value = "SELECT bp " +
            "FROM BpmProcdef bp " +
            "INNER JOIN PermissionDataManagement pdm ON bp.id = pdm.typeId " +
            "WHERE '-1' IN (:listCompanyCode) OR ( " +
            " pdm.typeName = 'bpm_procdef' " +
            "  AND ( " +
            "      CASE  " +
            "          WHEN :hasGroupPermission = true THEN ( " +
            "              pdm.companyCode IN (:listCompanyCodeMemberAdmin) " +
            "              OR pdm.companyCode = '-1' " +
            "              OR bp.id IN (:lstGroupPermissionId) " +
            "          ) " +
            "          ELSE ( " +
            "              pdm.companyCode IN (:listCompanyCode) " +
            "              OR pdm.companyCode = '-1' " +
            "          ) " +
            "      END = true " +
            "  ))")
    List<Tuple> initialProdef(@Param("hasGroupPermission") Boolean hasGroupPermission,
                              @Param("listCompanyCodeMemberAdmin") List<String> listCompanyCodeMemberAdmin,
                              @Param("listCompanyCode") List<String> listCompanyCode,
                              @Param("lstGroupPermissionId") List<Long> lstGroupPermissionId
    );

    @Query(value = "select lp from LegislativeProgram lp " +
            " where :isLoadAll is true " +
            " or (lp.activityStatus = 'PROCESSING' " +
            " and (lp.ticketId is null or lp.ticketStatus = 'CANCEL')) ")
    List<LegislativeProgram> getLegislativeProgramActive(Boolean isLoadAll);
}
