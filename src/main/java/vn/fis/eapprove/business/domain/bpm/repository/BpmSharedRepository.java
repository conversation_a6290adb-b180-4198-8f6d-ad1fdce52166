package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vn.fis.eapprove.business.domain.bpm.entity.BpmShared;


import java.util.List;

public interface BpmSharedRepository extends JpaRepository<BpmShared, Long>, JpaSpecificationExecutor<BpmShared> {

    List<BpmShared> getAllByProcInstId(String procInstId);

    @Query("SELECT s.sharedUser " +
            "FROM BpmShared s " +
            "WHERE s.procInstId = :procInstId AND s.sharedUser in :listUser " +
            "AND s.isDeleted <> true " +
            "AND s.type = 'SHARED'")
    List<String> listExist(@Param("procInstId") String procInstId, @Param("listUser") List<String> listUser);

    @Query("SELECT s.sharedUser " +
            " FROM BpmShared s " +
            " WHERE s.procInstId = :procInstId")
    List<String> getAllShareUserByProcInstId(@Param("procInstId") String procInstId);

    @Query("SELECT COUNT(DISTINCT a.procInstId) " +
            "FROM BpmShared a " +
            "WHERE a.sharedUser = :email " +
            "AND EXISTS (SELECT 1 FROM BpmProcInst b WHERE a.procInstId = b.ticketProcInstId " +
            "AND b.ticketStatus NOT IN ('COMPLETED', 'CLOSED') " +
            "AND EXISTS (SELECT 1 FROM BpmProcdef c WHERE b.ticketProcDefId = c.procDefId) " +
            "AND EXISTS (SELECT 1 FROM ServicePackage c WHERE b.serviceId = c.id) " +
            ")")
    Long countShared(@Param("email") String email);

    List<BpmShared> findBpmSharedByProcInstIdIn(List<String> procInstId);

    List<BpmShared> findBpmSharedByIdIn(List<Long> ids);

    @Modifying
    void deleteAllByProcInstIdAndSharedUser(String procInstId, String username);

    List<BpmShared> findAllByProcInstIdIn(List<String> ids);
}
