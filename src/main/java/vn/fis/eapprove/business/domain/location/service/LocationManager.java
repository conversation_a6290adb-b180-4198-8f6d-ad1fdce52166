package vn.fis.eapprove.business.domain.location.service;


import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;
import vn.fis.eapprove.business.constant.TypeExcel;
import vn.fis.eapprove.business.domain.authority.service.AuthService;
import vn.fis.eapprove.business.domain.location.entity.LocationManagement;
import vn.fis.eapprove.business.domain.location.repository.LocationManagementRepository;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.dto.*;
import vn.fis.eapprove.business.exception.ErrorMessage;
import vn.fis.eapprove.business.model.response.CreateImportResponse;
import vn.fis.eapprove.business.model.response.LocationManagementDto;
import vn.fis.eapprove.business.model.response.NameAndCodeCompanyResponse;
import vn.fis.eapprove.business.specification.LocationManagementSpecification;
import vn.fis.eapprove.business.tenant.manager.impl.CustomerServiceImpl;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.business.utils.TimeUtils;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.constants.MapKeyEnum;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.manager.FileManager;

import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static vn.fis.eapprove.business.constant.Constant.*;

@Slf4j
@Service("LocationManagerV1")
@Transactional
public class LocationManager {

    @Autowired
    ResponseUtils responseUtils;

    @Autowired
    LocationManagementRepository locationManagementRepository;

    @Autowired
    ModelMapper modelMapper;

    @Autowired
    RestTemplate restTemplate;
    @Autowired
    LocationManagementSpecification locationManagementSpecification;
    @Autowired
    private SproProperties sproProperties;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private CustomerServiceImpl customerService;

    @Autowired
    private PermissionDataManagementRepository permissionDataManagementRepository;

    @Autowired
    private AuthService authService;

    @Autowired
    private CredentialHelper credentialHelper;
    @Value("${app.s3.bucket}")
    private String bucket;

    public List<LocationManagementDto> getListLocation(String search) {
        try {
            List<LocationManagement> locationManagements = locationManagementRepository.getListLocation(search);
            List<LocationManagementDto> listRp = locationManagements.stream().map(x -> modelMapper.map(x, LocationManagementDto.class)).collect(Collectors.toList());
            return listRp;

        } catch (Exception e) {
            log.error("Error getChartInfoRole {}", e.getMessage());
            return null;
        }
    }

    public List<ChartDto> getChart(String name) {
        try {
            Long id = 0l;
            String token = credentialHelper.getJWTToken();
            //String realm = credentialHelper.getRealm();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("key_role", "public");
            //headers.set("realm", realm);
            headers.set("Authorization", "Bearer " + token);
            HttpEntity<?> requestEntity = new HttpEntity<>(headers);
            String url1 = UriComponentsBuilder.fromHttpUrl(sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key) + "/chart/getChartInfo").queryParam("name", name).queryParam("chartId", id).encode(StandardCharsets.UTF_8).toUriString();
            String url = URLDecoder.decode(url1, StandardCharsets.UTF_8.name());
//            StringBuilder builder = new StringBuilder();
//            builder.append(sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key) + "/chart/getChartInfo");
//            builder.append("?name=").append(name);
//            builder.append("&&chartId=").append(id);
//            String url = builder.toString();

            ResponseEntity<ResponseDto> responseCustomer = restTemplate.exchange(url,
                    HttpMethod.GET, requestEntity, new ParameterizedTypeReference<>() {
                    });
            ResponseDto responseDto = responseCustomer.getBody();
            List<ChartDto> chartDtoList = (List<ChartDto>) responseDto.getData();
            return chartDtoList;
        } catch (Exception e) {
            log.error("Error getChartInfoRole {}", e.getMessage());
            return null;
        }
    }

    public String createManual(LocationManagementDto createLocationManualRequest) {
        try {
            String account = credentialHelper.getJWTPayload().getUsername();
            LocationManagement locationManagement = null;
            if (createLocationManualRequest.getId() == null) {
                locationManagement = new LocationManagement();
                locationManagement.setCreatedDate(LocalDateTime.now());
                locationManagement.setCreatedUser(account);
                locationManagement.setStatus(MANUAL);
            } else {
                locationManagement = locationManagementRepository.getById(createLocationManualRequest.getId());
                locationManagement.setModifiedUser(account);
                locationManagement.setModifiedDate(LocalDateTime.now());
                locationManagement.setStatus(createLocationManualRequest.getStatus());

                // Xóa data cũ
                List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(createLocationManualRequest.getId(), PermissionDataConstants.Type.LOCATION_MANAGEMENT.code);
                if (!ValidationUtils.isNullOrEmpty(oldData)) {
                    permissionDataManagementRepository.deleteAll(oldData);
                }
            }

            locationManagement.setLocationName(createLocationManualRequest.getLocationName());
            locationManagement.setWorkingTimeCode(createLocationManualRequest.getWorkingTimeChoose().getValue());
            locationManagement.setWorkingTimeName(createLocationManualRequest.getWorkingTimeChoose().getLabel());
//            locationManagement.setCompanyName(createLocationManualRequest.getChartChoose().getCompanyName());
//            locationManagement.setChartId(createLocationManualRequest.getChartChoose().getChartId());
            locationManagement.setAddress(createLocationManualRequest.getAddress());
            locationManagement.setAbbreviations(createLocationManualRequest.getAbbreviations());
//            locationManagement.setType(createLocationManualRequest.getChartChoose().getType());

            List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
            for(NameAndCodeCompanyResponse response : listCompanyCodeAndName){
                locationManagement.setCompanyCode(response.getCompanyCode());
                locationManagement.setCompanyName(response.getCompanyName());
            }

            locationManagement = locationManagementRepository.save(locationManagement);

            // Lưu phân quyền dữ liệu
            if (!ValidationUtils.isNullOrEmpty(createLocationManualRequest.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : createLocationManualRequest.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(locationManagement.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.LOCATION_MANAGEMENT.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                permissionDataManagementRepository.saveAll(permissionDataManagements);
            }
            return messageSource.getMessage("message.location.success", null, Locale.getDefault());
        } catch (Exception e) {
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }

    }

    private boolean checkRowNull(Row row) {
        for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
            if (cell != null && !cell.getCellType().equals(CellType.BLANK)) {
                return false;
            }
        }
        return true;
    }

    public CreateImportResponse createImport(CreateLocationImportRequest createLocationRequest) {
        try {
            InputStream inputStream = fileManager.getFileInputStream(bucket, createLocationRequest.getFileName());
            Workbook wb = getWorkbook(inputStream, createLocationRequest.getFileName());
            Sheet datatypeSheet = wb.getSheetAt(createLocationRequest.getSheet());
            DataFormatter fmt = new DataFormatter();
            Iterator<Row> iterator = datatypeSheet.iterator();
            List<LocationManagement> listLocationManagements = new LinkedList<>();
//            List<ChartDto> dtoList = checkCodeExist();
            List<WorkingTimeDto> workingTimeDtoList = checkWorkingTime();
            List<String> listWorkTimeCode = workingTimeDtoList.stream().map(WorkingTimeDto::getWorkScheduleCode).collect(Collectors.toList());
            List<LocationManagement> locationManagements = locationManagementRepository.findAll();
            List<String> listLocation = locationManagements.stream().map(LocationManagement::getLocationName).collect(Collectors.toList());
            Boolean checkAll = false;

            int totalRecord = 0;
            int recordError = 0;
            while (iterator.hasNext()) {
                Boolean check = false;
//                Boolean checkDup = false;
                StringBuilder stringBuilder = new StringBuilder();
                Row currentRow = iterator.next();
                Cell newCell = currentRow.createCell(6);
                CellStyle style = wb.createCellStyle();
                if (currentRow.getRowNum() == 0) {
                    newCell.setCellValue("RESULT");
                    continue;
                }

                if (checkRowNull(currentRow)) {
                    continue;
                }
                totalRecord++;
                LocationManagement locationManagement = new LocationManagement();
//                if (ValidationUtils.isNullOrEmpty(fmt.formatCellValue(currentRow.getCell(createLocationRequest.getOrgChart().getIndex())))) {
//                    check = true;
//
//                }else {
//                    for (ChartDto chartDto : dtoList) {
//                        if (chartDto.getCode().equalsIgnoreCase(fmt.formatCellValue(currentRow.getCell(createLocationRequest.getOrgChart().getIndex())))) {
//                            locationManagement.setChartId(fmt.formatCellValue(currentRow.getCell(createLocationRequest.getOrgChart().getIndex())));
//                            locationManagement.setType(chartDto.getStatus());
//                            locationManagement.setCompanyName(chartDto.getName());
//                            checkDup = false;
//                            break;
//                        } else {
//                            checkDup = true;
//                        }
//                    }
//                    if (checkDup) {
//                        stringBuilder.append(messageSource.getMessage("message.location.exsitCode", null, Locale.getDefault()));
//                        stringBuilder.append("\n");
//                    }
//
//                }
                if (ValidationUtils.isNullOrEmpty(fmt.formatCellValue(currentRow.getCell(createLocationRequest.getLocation().getIndex())))) {
                    check = true;
                }
                if (ValidationUtils.isNullOrEmpty(fmt.formatCellValue(currentRow.getCell(createLocationRequest.getAddress().getIndex())))) {
                    check = true;
                }
                if (ValidationUtils.isNullOrEmpty(fmt.formatCellValue(currentRow.getCell(createLocationRequest.getWorkScheduleCode().getIndex())))) {
                    check = true;
                }
                if (check) {
                    stringBuilder.append(messageSource.getMessage("message.location.isNotEmpty", null, Locale.getDefault()));
                    stringBuilder.append("\n");
                }

                locationManagement.setAbbreviations(fmt.formatCellValue(currentRow.getCell(createLocationRequest.getAbbreviations().getIndex())));
                if (listLocation.contains(fmt.formatCellValue(currentRow.getCell(createLocationRequest.getLocation().getIndex())))) {
                    check = true;
                    stringBuilder.append(messageSource.getMessage("message.location.exsitLocationName", null, Locale.getDefault()));
                    stringBuilder.append("\n");
                }
                locationManagement.setLocationName(fmt.formatCellValue(currentRow.getCell(createLocationRequest.getLocation().getIndex())));

                locationManagement.setAddress(fmt.formatCellValue(currentRow.getCell(createLocationRequest.getAddress().getIndex())));
                if (listWorkTimeCode.contains(fmt.formatCellValue(currentRow.getCell(createLocationRequest.getWorkScheduleCode().getIndex())))) {
                    locationManagement.setWorkingTimeCode(fmt.formatCellValue(currentRow.getCell(createLocationRequest.getWorkScheduleCode().getIndex())));
                    for (WorkingTimeDto workingTimeDto : workingTimeDtoList) {
                        if (fmt.formatCellValue(currentRow.getCell(createLocationRequest.getWorkScheduleCode().getIndex())).equalsIgnoreCase(workingTimeDto.getWorkScheduleCode())) {
                            locationManagement.setWorkingTimeName(workingTimeDto.getName());
                        }
                    }
                } else {
                    locationManagement.setWorkingTimeCode("DEFAULT");
                    locationManagement.setWorkingTimeName("Mặc định");
                }
                locationManagement.setStatus(IMPORT);
                locationManagement.setCreatedDate(LocalDateTime.now());
                locationManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                for (int i = 0; i < listLocationManagements.size(); i++) {
                    if (ValidationUtils.isNullOrEmpty(listLocationManagements.get(i).getLocationName())) {
                        continue;
                    }
                    if (listLocationManagements.get(i).getLocationName().equalsIgnoreCase(fmt.formatCellValue(currentRow.getCell(createLocationRequest.getLocation().getIndex())))) {
                        check = true;
                        stringBuilder.append(messageSource.getMessage("message.location.notDup", null, Locale.getDefault()));
                    }
                }
                listLocationManagements.add(locationManagement);
                if (check) {
                    newCell.setCellValue(stringBuilder.toString());
                    style.setWrapText(true);
                    newCell.setCellStyle(style);
                    checkAll = true;
                    recordError++;
                } else {
                    stringBuilder.append(messageSource.getMessage("message.location.correct", null, Locale.getDefault()));
                    newCell.setCellValue(stringBuilder.toString());
                    style.setWrapText(true);
                    newCell.setCellStyle(style);
                }
            }
            if (checkAll) {
                File createTempFile = null;
                if (createLocationRequest.getFileName().endsWith(".xlsx")) {
                    createTempFile = File.createTempFile("lỗi nhập dữ liệu_" + createLocationRequest.getFileName().replaceAll(".xlsx", ""), ".xlsx");
                } else {
                    createTempFile = File.createTempFile("lỗi nhập dữ liệu_" + createLocationRequest.getFileName().replaceAll(".xls", ""), ".xls");
                }
                OutputStream outputStream = new FileOutputStream(createTempFile);
                wb.write(outputStream);
                InputStream inputStream1 = new FileInputStream(createTempFile);
                uploadFile(createTempFile, inputStream1);
                String messageResult = messageSource.getMessage("message.location.totalError", null, Locale.getDefault()) + recordError + "\n"
                        + messageSource.getMessage("message.location.totalSuccess", null, Locale.getDefault()) + (totalRecord - recordError) +
                        messageSource.getMessage("message.location.fileMess", null, Locale.getDefault());
                String url = createTempFile.getName();
                return CreateImportResponse.builder().message(messageResult).urlFile(url).build();
            } else {
                locationManagementRepository.saveAll(listLocationManagements);
                return CreateImportResponse.builder().message(messageSource.getMessage("message.location.success", null, Locale.getDefault())).build();
            }

        } catch (Exception e) {
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }

    }

//    public void addComment(Drawing<Shape> drawing, CreationHelper creationHelper, Cell cell,String message) {
//
//        ClientAnchor clientAnchorOther = drawing.createAnchor(0, 0, 0, 0, 0, 7, 12, 17);
//
//        Comment commentOther = (Comment) drawing.createCellComment(clientAnchorOther);
//
//        RichTextString richTextStringOther = creationHelper.createRichTextString(message);
//
//        commentOther.setString(richTextStringOther);
//        commentOther.setAuthor("Roy Tutorials");
//        cell.setCellComment(commentOther);
//    }


    public void uploadFile(File file, InputStream inputStream) {
        try {
            if (!fileManager.isBucketExisting(bucket)) {
                fileManager.createBucket(bucket);
            }
            fileManager.putFile(bucket, file.getName(), file.length(), inputStream);
        } catch (Exception e) {
            log.error("Happened error when upload file: ");
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    //    public void checkCellEmpty(Cell cellCheck, Boolean check) {
//        if (ValidationUtils.isNullOrEmpty(cellCheck)) {
//            check = true;
//        } else {
//            changeCellBackgroundColor(cellCheck, IndexedColors.WHITE.getIndex());
//        }
//    }
//
    public void changeCellBackgroundColor(Cell cell, short color) {
        CellStyle cellStyle = cell.getCellStyle();
        if (cellStyle == null) {
            cellStyle = cell.getSheet().getWorkbook().createCellStyle();
        }
        cellStyle.setFillForegroundColor(color);
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cell.setCellStyle(cellStyle);
    }

    private Workbook getWorkbook(InputStream inputStream, String excelFilePath) {
        Workbook workbook = null;
        try {
            if (excelFilePath.endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(inputStream);
            } else if (excelFilePath.endsWith(".xls")) {
                workbook = new HSSFWorkbook(inputStream);
            } else {
                throw new IllegalArgumentException("The specified file is not Excel file");
            }

            return workbook;
        } catch (Exception e) {
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

//    private List<ChartDto> checkCodeExist() {
//        String token = credentialHelper.getJWTToken();
//        //String realm = credentialHelper.getRealm();
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
////        headers.set("key_role", "public");
//        //headers.set("realm", realm);
//        headers.set("Authorization", "Bearer " + token);
//        HttpEntity<?> requestEntity = new HttpEntity<>(headers);
//        String url = UriComponentsBuilder.fromHttpUrl(sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key) + "/chart/checkCode").encode().toUriString();
//        ResponseEntity<List<ChartDto>> responseCustomer = restTemplate.exchange(url,
//                HttpMethod.GET, requestEntity, new ParameterizedTypeReference<>() {
//                });
//        List<ChartDto> chartDtoList = responseCustomer.getBody();
//        return chartDtoList;
//    }

    public List<WorkingTimeDto> checkWorkingTime()  {
        String token = credentialHelper.getJWTToken();
        //String realm = credentialHelper.getRealm();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("key_role", "public");
        //headers.set("realm", realm);
        headers.set("Authorization", "Bearer " + token);
        HttpEntity<?> requestEntity = new HttpEntity<>(headers);
        String url = UriComponentsBuilder.fromHttpUrl(sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key) + "/working-time/getAllWorking").encode().toUriString();
        ResponseEntity<List<WorkingTimeDto>> responseCustomer = restTemplate.exchange(url,
                HttpMethod.GET, requestEntity, new ParameterizedTypeReference<>() {
                });
        List<WorkingTimeDto> workingTimeDtoList = responseCustomer.getBody();

        return workingTimeDtoList;
    }

    public List<WorkingTimeDto> getWorkingTimeCode(String code) {
        try {
            String token = credentialHelper.getJWTToken();
            //String realm = credentialHelper.getRealm();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("key_role", "public");
            //headers.set("realm", realm);
            headers.set("Authorization", "Bearer " + token);
            HttpEntity<?> requestEntity = new HttpEntity<>(headers);
            String url1 = UriComponentsBuilder.fromHttpUrl(sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key) + "/working-time/getWorkingByCode").queryParam("workingTimeCode", code).toUriString();
            String url = URLDecoder.decode(url1, StandardCharsets.UTF_8.name());
            ResponseEntity<List<WorkingTimeDto>> responseCustomer = restTemplate.exchange(url,
                    HttpMethod.GET, requestEntity, new ParameterizedTypeReference<>() {
                    });
            List<WorkingTimeDto> workingTimeDtoList = responseCustomer.getBody();

            return workingTimeDtoList;

        } catch (Exception e) {
            log.error("Error get working time {}", e.getMessage());
            return null;
        }

    }

    //    public List<String> upload(MultipartFile file)  {
//        try {
//            Workbook workbook = getWorkbook(file);
//            List<String> sheetNames = new ArrayList<String>();
//            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
//                sheetNames.add(workbook.getSheetName(i));
//
//            }
//            fileManager.putFile(bucket,file.getOriginalFilename(),file.getSize(),file.getInputStream());
//            return sheetNames;
//        }catch (Exception e){
//            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
//        }
//
//    }
    private void upload(MultipartFile file) {
        try {
            if (!fileManager.isBucketExisting(bucket)) {
                fileManager.createBucket(bucket);
            }
            fileManager.putFile(bucket, file.getOriginalFilename(), file.getSize(), file.getInputStream());
        } catch (Exception e) {
            log.error("Happened error when upload file: ");
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    public String checkWorkbook(MultipartFile file) {
        try {
            List<TypeExcel> list = TypeExcel.stream().filter(s -> file.getOriginalFilename().endsWith(s.getTypeOfExcel())).collect(Collectors.toList());
            if (list.isEmpty()) {
                throw new ErrorMessage(messageSource.getMessage("message.location.fileCheck", null, Locale.getDefault()));
            }
            upload(file);
            return messageSource.getMessage("message.location.success", null, Locale.getDefault());

        } catch (Exception e) {
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    public List<String> listUserByChartId(Long chartId)  {
        String token = credentialHelper.getJWTToken();
        //String realm = credentialHelper.getRealm();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("key_role", "public");
        //headers.set("realm", realm);
        headers.set("Authorization", "Bearer " + token);
        HttpEntity<?> requestEntity = new HttpEntity<>(headers);
        String url = UriComponentsBuilder.fromHttpUrl(sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key) + "/groupUser/getUserInfoByChart").encode().queryParam("chartId", chartId).toUriString();
        ResponseEntity<ResponseDto<List<UserInfoDto>>> responseCustomer = restTemplate.exchange(url,
                HttpMethod.GET, requestEntity, new ParameterizedTypeReference<>() {
                });
        List<UserInfoDto> userInfoDtoList = responseCustomer.getBody().getData();
        List<String> list = userInfoDtoList.stream().map(UserInfoDto::getEmail).collect(Collectors.toList());
        return list;
    }

    public Boolean isUsed(Long locationId)  {
        String token = credentialHelper.getJWTToken();
        //String realm = credentialHelper.getRealm();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("key_role", "public");
        //headers.set("realm", realm);
        headers.set("Authorization", "Bearer " + token);
        HttpEntity<?> requestEntity = new HttpEntity<>(headers);
        String url = UriComponentsBuilder.fromHttpUrl(sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key) + "/userInfo/isUsedLocation").encode().queryParam("id", locationId).toUriString();
        ResponseEntity<Boolean> responseCustomer = restTemplate.exchange(url,
                HttpMethod.GET, requestEntity, new ParameterizedTypeReference<>() {
                });
        Boolean isUsedLocation = responseCustomer.getBody();
        return isUsedLocation;
    }

    public PageDto search(LocationManagementFilterDto criteria) {
        try {
            Integer pageNum = criteria.getPage() - 1;
            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());

            String username = credentialHelper.getJWTPayload().getUsername();
            // Lấy list companyCode cấu hình QL vai trò người dùng
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
            // Lấy list companyCode mặc định - 25/09/2023 -> bỏ
//            List<ChartDto> defaultChart = customerService.getLstChartByUsername(username);
//            if (!ValidationUtils.isNullOrEmpty(defaultChart)) {
//                List<String> defaultCompanyCode = defaultChart.stream().map(ChartDto::getCode).distinct().collect(Collectors.toList());
//                if (!ValidationUtils.isNullOrEmpty(defaultCompanyCode)) {
//                    lstCompanyCode.addAll(defaultCompanyCode);
//                }
//            }

            Page<LocationManagement> page = locationManagementRepository.findAll(locationManagementSpecification.filter(criteria, lstCompanyCode),
                    PageRequest.of(pageNum, criteria.getSize(), sort));

            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.LOCATION_MANAGEMENT.code);

            int pageNumber = page.getNumber() + 1;
            List<LocationManagementDto> locationManagementResponses = new ArrayList<>();
            for (LocationManagement locationManagement : page.getContent()) {
                LocationManagementDto locationManagementResponse = new LocationManagementDto();
                locationManagementResponse.setIsUsed(isUsed(locationManagement.getId()));
                locationManagementResponse.setId(locationManagement.getId());
                locationManagementResponse.setLocationName(locationManagement.getLocationName());
                locationManagementResponse.setAbbreviations(locationManagement.getAbbreviations());
                WorkingTimeChoose w = new WorkingTimeChoose();
                w.setValue(locationManagement.getWorkingTimeCode());
                w.setLabel(locationManagement.getWorkingTimeName());
                locationManagementResponse.setWorkingTimeChoose(w);
                locationManagementResponse.setWorkingTimeCode(locationManagement.getWorkingTimeCode());
                locationManagementResponse.setAddress(locationManagement.getAddress());
                locationManagementResponse.setStatus(locationManagement.getStatus());
                locationManagementResponse.setCreatedUser(locationManagement.getCreatedUser());
                locationManagementResponse.setCreatedDate(TimeUtils.localDateTimeToString(locationManagement.getCreatedDate(), LOCALFORMAT));
                locationManagementResponse.setModifiedUser(locationManagement.getModifiedUser());
                locationManagementResponse.setModifiedDate(ValidationUtils.isNullOrEmpty(locationManagement.getModifiedDate()) ? null : TimeUtils.localDateTimeToString(locationManagement.getModifiedDate(), LOCALFORMAT));
                locationManagementResponse.setCompanyCode(locationManagement.getCompanyCode());
                locationManagementResponse.setCompanyName(locationManagement.getCompanyName());
                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(locationManagement.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
                if (!ValidationUtils.isNullOrEmpty(companyCodes)) {
                    locationManagementResponse.setApplyFor(companyCodes);
                }

                locationManagementResponses.add(locationManagementResponse);
            }
            return PageDto.builder()
                    .content(locationManagementResponses)
                    .number(pageNumber)
                    .numberOfElements(page.getNumberOfElements())
                    .page(pageNumber)
                    .size(page.getSize())
                    .totalPages(page.getTotalPages())
                    .totalElements(page.getTotalElements())
                    .build();
        } catch (Exception e) {
            log.info("search failed ");
            e.printStackTrace();
            return null;
        }
    }

    public  List<LocationManagementDto> searchFilter(LocationManagementFilterDto criteria) {
        try {
//            Integer pageNum = criteria.getPage() - 1;
//            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());

            String username = credentialHelper.getJWTPayload().getUsername();
            // Lấy list companyCode cấu hình QL vai trò người dùng
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
            // Lấy list companyCode mặc định - 25/09/2023 -> bỏ
//            List<ChartDto> defaultChart = customerService.getLstChartByUsername(username);
//            if (!ValidationUtils.isNullOrEmpty(defaultChart)) {
//                List<String> defaultCompanyCode = defaultChart.stream().map(ChartDto::getCode).distinct().collect(Collectors.toList());
//                if (!ValidationUtils.isNullOrEmpty(defaultCompanyCode)) {
//                    lstCompanyCode.addAll(defaultCompanyCode);
//                }
//            }

            List<LocationManagement> page = locationManagementRepository.findAll(locationManagementSpecification.filter(criteria, lstCompanyCode));

            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.LOCATION_MANAGEMENT.code);

            List<LocationManagementDto> locationManagementResponses = new ArrayList<>();
            for (LocationManagement locationManagement : page) {
                LocationManagementDto locationManagementResponse = new LocationManagementDto();
                locationManagementResponse.setIsUsed(isUsed(locationManagement.getId()));
                locationManagementResponse.setId(locationManagement.getId());
                locationManagementResponse.setLocationName(locationManagement.getLocationName());
                locationManagementResponse.setAbbreviations(locationManagement.getAbbreviations());
                WorkingTimeChoose w = new WorkingTimeChoose();
                w.setValue(locationManagement.getWorkingTimeCode());
                w.setLabel(locationManagement.getWorkingTimeName());
                locationManagementResponse.setWorkingTimeChoose(w);
                locationManagementResponse.setWorkingTimeCode(locationManagement.getWorkingTimeCode());
                locationManagementResponse.setAddress(locationManagement.getAddress());
                locationManagementResponse.setStatus(locationManagement.getStatus());
                locationManagementResponse.setCreatedUser(locationManagement.getCreatedUser());
                locationManagementResponse.setCreatedDate(TimeUtils.localDateTimeToString(locationManagement.getCreatedDate(), LOCALFORMAT));
                locationManagementResponse.setModifiedUser(locationManagement.getModifiedUser());
                locationManagementResponse.setModifiedDate(ValidationUtils.isNullOrEmpty(locationManagement.getModifiedDate()) ? null : TimeUtils.localDateTimeToString(locationManagement.getModifiedDate(), LOCALFORMAT));

                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(locationManagement.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
                if (!ValidationUtils.isNullOrEmpty(companyCodes)) {
                    locationManagementResponse.setApplyFor(companyCodes);
                }

                locationManagementResponses.add(locationManagementResponse);
            }
            return locationManagementResponses;
        } catch (Exception e) {
            log.info("search failed ");
            e.printStackTrace();
            return null;
        }
    }

//    public LocationManagementDto loadInfor(Long id) {
//        LocationManagement locationManagement = locationManagementRepository.getById(id);
//        LocationManagementDto locationManagementResponse = new LocationManagementDto();
//        locationManagementResponse.setId(locationManagement.getId());
//        locationManagementResponse.setLocationName(locationManagement.getLocationName());
//        locationManagementResponse.setAbbreviations(locationManagement.getAbbreviations());
//        locationManagementResponse.setWorkingTimeCode(locationManagement.getWorkingTimeCode());
//        locationManagementResponse.setChartId(locationManagement.getChartId());
//        locationManagementResponse.setAddress(locationManagement.getAddress());
//        locationManagementResponse.setStatus(locationManagement.getStatus());
//        locationManagementResponse.setType(locationManagement.getType());
//        locationManagementResponse.setCreatedUser(locationManagement.getCreatedUser());
//        locationManagementResponse.setCreatedDate(locationManagement.getCreatedDate());
//        locationManagementResponse.setModifiedUser(locationManagement.getModifiedUser());
//        locationManagementResponse.setModifiedDate(locationManagement.getModifiedDate());
//        return locationManagementResponse;
//    }

    public String deleteLocation(List<Long> id) {
        try {
            for (Long idLocation : id) {
                if (!ValidationUtils.isNullOrEmpty(isUsed(idLocation))) {
                    return messageSource.getMessage("message.location.deleteFail", null, Locale.getDefault());
                } else {
                    locationManagementRepository.deleteById(idLocation);
                }
            }
            return messageSource.getMessage("message.location.deleteSuccess", null, Locale.getDefault());
        } catch (Exception e) {
            log.info("delete fail ");
            e.printStackTrace();
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    public boolean checkName(String name, Long id) {
        try {
            if (id != null) {
                LocationManagement locationManagement = locationManagementRepository.getById(id);
                if (!name.equalsIgnoreCase(locationManagement.getLocationName())) {
                    if (checkExistName(name)) {
//                        return messageSource.getMessage("message.location.checkName", null, Locale.getDefault());
                        return true;
                    }
                }
            } else {
                if (checkExistName(name)) {
//                   return messageSource.getMessage("message.location.checkName", null, Locale.getDefault());
                    return true;
                }
            }
//            return messageSource.getMessage("message.location.nameOK", null, Locale.getDefault());
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }

    }


    public boolean checkExistName(String name) {
        try {
            List<LocationManagement> locationManagementList = locationManagementRepository.findByLocationName(name);
            if (locationManagementList.size() > 0) {
                return true;
            }
            return false;
        } catch (Exception e) {
            return true;
        }
    }

    public String getCode(Long id) {
        try {
            LocationManagement locationManagement = locationManagementRepository.findById(id).get();
            String code = locationManagement.getWorkingTimeCode();
            return code;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    public LocationManagementDto locationInfo(Long id) {
        LocationManagement locationManagement = locationManagementRepository.getById(id);
        LocationManagementDto locationManagementDto = new LocationManagementDto();
        locationManagementDto.setId(locationManagement.getId());
        locationManagementDto.setLocationName(locationManagement.getLocationName());
        return locationManagementDto;
    }

    public List<String> getAllListWorkingCodeInLocationManagement() {
        return locationManagementRepository.getAllListWorkingCodeInLocationManagement();
    }
}
