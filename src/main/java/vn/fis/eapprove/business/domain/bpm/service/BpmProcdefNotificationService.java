package vn.fis.eapprove.business.domain.bpm.service;


import vn.fis.eapprove.business.domain.bpm.entity.BpmNotifyUser;
import vn.fis.eapprove.business.model.request.VariableValueDto;


import java.util.List;
import java.util.Map;

public interface BpmProcdefNotificationService {
    List<BpmNotifyUser> addNotificationsByConfig(Long bpmProcdefId, String nextTaskDefKey, Map<String, VariableValueDto> variables,
                                                 Long ticketId, Boolean isGetOldVariable, List<String> lstCustomerEmails, String actionCode, String emailExe) ;

    List<BpmNotifyUser> addNotification(Long ticketId, String objectNotification, List<String> lstRecipient, String actionCode, Map<String, VariableValueDto> variables, List<String> typeSend, String nextTaskDefKey) ;
}
