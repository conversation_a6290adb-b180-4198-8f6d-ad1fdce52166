package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefInherits;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefViewFileApi;


import java.util.List;

@Repository
public interface BpmProcdefViewFileApiRepository extends JpaRepository<BpmProcdefViewFileApi, Long>, JpaSpecificationExecutor<BpmProcdefInherits> {

    List<BpmProcdefViewFileApi> getAllByBpmProcdefIdAndProcDefId(Long bpmProcdefId,String procDefId);

    List<BpmProcdefViewFileApi> getAllByProcDefId(String ProcdefId);

    List<BpmProcdefViewFileApi> getAllByProcDefIdIn(List<String> procDefIds);

    @Query("SELECT c FROM BpmProcdefViewFileApi c WHERE c.bpmProcdefId =:bpmProcdefId and c.status = true")
    List<BpmProcdefViewFileApi> getBpmProcdefViewFileApi(@Param("bpmProcdefId") Long bpmProcdefId);
}
