package vn.fis.eapprove.business.domain.report.service;


import vn.fis.eapprove.business.domain.report.entity.ReportByGroup;

public interface ReportByGroupService {
    ReportByGroup insert(Long procInstId);

    ReportByGroup update(Long procInstId);

    ReportByGroup findByTicketId(Long procInstId);

    boolean isTicketExist(Long procInstId);

    void createReportByGroup(Long procInstId);

    public void syncReportByGroup(String fromDate, String toDate);

    void deleteTaskReturnExist(Long ticketId);

}
