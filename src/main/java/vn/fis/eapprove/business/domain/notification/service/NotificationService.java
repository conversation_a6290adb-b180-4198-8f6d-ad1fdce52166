package vn.fis.eapprove.business.domain.notification.service;



import vn.fis.eapprove.business.domain.bpm.entity.BpmNotifyUser;
import vn.fis.eapprove.business.domain.notification.entity.Notification;

import java.util.List;

/**
 * Author: AnhVTN
 * Date: 03/01/2023
 */

public interface NotificationService {
    void saveBatch(List<Notification> notifications);

    void pushNotification(List<BpmNotifyUser> bpmNotifyUsers);
}
