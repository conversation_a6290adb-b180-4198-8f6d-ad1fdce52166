package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefInherits;


import java.util.List;

@Repository
public interface BpmProcdefInheritsRepository extends JpaRepository<BpmProcdefInherits, Long>, JpaSpecificationExecutor<BpmProcdefInherits> {

    List<BpmProcdefInherits> getAllByBpmProcdefId(Long bpmProcdefId);

    @Query("SELECT c FROM BpmProcdefInherits c WHERE c.bpmProcdefId =:bpmProcdefId and c.status = '1'")
    List<BpmProcdefInherits> getBpmProcdefInheritsInfo(@Param("bpmProcdefId") Long bpmProcdefId);

    @Query("SELECT c FROM BpmProcdefInherits c WHERE c.procDefId = :procDefId and c.status = '1'")
    List<BpmProcdefInherits> getBpmProcdefInheritsInfoHistory(@Param("procDefId") String procDefId);
}
