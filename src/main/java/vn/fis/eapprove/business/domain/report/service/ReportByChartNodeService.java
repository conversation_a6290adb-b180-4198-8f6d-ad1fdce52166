package vn.fis.eapprove.business.domain.report.service;




import vn.fis.eapprove.business.domain.report.entity.ReportByChartNode;

import java.util.List;

public interface ReportByChartNodeService {

    ReportByChartNode insert(String taskId);

    ReportByChartNode update(String taskId);

    ReportByChartNode findByTaskId(String taskId);

    boolean isTaskExist(String taskId);

    void createReportByChartNode(String taskId);

    void syncReportByChartNode(String fromDate, String toDate);

    List<String> getListUserFilter(List<String> usernames);
}
