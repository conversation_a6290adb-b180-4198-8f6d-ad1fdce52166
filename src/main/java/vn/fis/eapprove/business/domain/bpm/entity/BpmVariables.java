package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;

@Entity
@Getter
@Setter
@Table(name = "bpm_variables")
public class BpmVariables {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "task_id")
    private String taskId;

    @Column(name = "proc_inst_id")
    private String procInstId;

    @Column(name = "is_draft")
    private Integer isDraft;

    @Column(name = "type")
    private String type;

    @Column(name = "string_val")
    private String stringVal;

    @Column(name = "double_val")
    private Double doubleVal;

    @Column(name = "long_val")
    private Long longVal;

    @Column(name = "json_val")
    private String jsonVal;

    @Column(name = "download_url")
    private String downloadUrl;

    @Column(name = "additional_val")
    private String additionalVal;
}
