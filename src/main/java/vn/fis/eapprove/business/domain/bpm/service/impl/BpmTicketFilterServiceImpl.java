package vn.fis.eapprove.business.domain.bpm.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTaskUser;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTicketFilter;
import vn.fis.eapprove.business.domain.bpm.repository.BpmDiscussionRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTaskRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTicketFilterRepository;
import vn.fis.eapprove.business.domain.bpm.service.BpmTaskUserService;
import vn.fis.eapprove.business.domain.bpm.service.BpmTicketFilterService;
import vn.fis.eapprove.business.dto.FilterTaskOptionDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.PrivateFilterRequest;
import vn.fis.eapprove.business.model.request.BpmTicketFilterRequest;
import vn.fis.eapprove.business.model.request.BpmTicketFilterSearchRequest;
import vn.fis.eapprove.business.model.response.BpmTicketFilterResponse;
import vn.fis.eapprove.business.model.response.PrivateFilterResponse;
import vn.fis.eapprove.business.model.response.TaskDetailResponse;
import vn.fis.eapprove.business.specification.BpmProcInstSpecification;
import vn.fis.eapprove.business.specification.BpmTaskSpecification;

import vn.fis.eapprove.business.utils.Common;
import vn.fis.spro.common.constants.ProcInstConstants;
import vn.fis.spro.common.constants.TaskConstants;
import vn.fis.spro.common.util.DateTimeUtils;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("BpmTicketFilterServiceImplV1")
@Slf4j
public class BpmTicketFilterServiceImpl implements BpmTicketFilterService {

    private final BpmTicketFilterRepository bpmTicketFilterRepository;
    private final CredentialHelper credentialHelper;
    private final BpmProcInstSpecification bpmProcInstSpecification;
    private final Common common;
    private final BpmTaskRepository bpmTaskRepository;
    private final BpmTaskUserService bpmTaskUserService;
    private final ModelMapper modelMapper;
    private final BpmTaskSpecification bpmTaskSpecification;
    private final BpmDiscussionRepository bpmDiscussionRepository;

    public BpmTicketFilterServiceImpl(
            BpmTicketFilterRepository bpmTicketFilterRepository,
            CredentialHelper credentialHelper,
            Common common,
            BpmTaskRepository bpmTaskRepository,
            BpmTaskUserService bpmTaskUserService,
            ModelMapper modelMapper,
            BpmProcInstSpecification bpmProcInstSpecification, BpmTaskSpecification bpmTaskSpecification, BpmDiscussionRepository bpmDiscussionRepository) {
        this.bpmTicketFilterRepository = bpmTicketFilterRepository;
        this.credentialHelper = credentialHelper;
        this.common = common;
        this.bpmTaskRepository = bpmTaskRepository;
        this.bpmTaskUserService = bpmTaskUserService;
        this.modelMapper = modelMapper;
        this.bpmProcInstSpecification = bpmProcInstSpecification;
        this.bpmTaskSpecification = bpmTaskSpecification;
        this.bpmDiscussionRepository = bpmDiscussionRepository;
    }

    @Override
    public Map<String, Object> createUpdate(BpmTicketFilterRequest request)  {
        String username = credentialHelper.getJWTPayload().getUsername();

        BpmTicketFilter bpmTicketFilter;
        if (!ValidationUtils.isNullOrEmpty(request.getId())) {
            bpmTicketFilter = bpmTicketFilterRepository.getBpmTicketFilterById(request.getId());
            if (bpmTicketFilter == null) {
                return null;
            }
            bpmTicketFilter.setUpdatedDate(LocalDateTime.now());
        } else {
            bpmTicketFilter = new BpmTicketFilter();
            bpmTicketFilter.setCreatedUser(username);
            bpmTicketFilter.setCreatedDate(LocalDateTime.now());
            bpmTicketFilter.setStatus("unpin");
        }

        bpmTicketFilter.setName(request.getName());
        bpmTicketFilter.setFilterBody(request.getFilterBody());
        bpmTicketFilter.setType(request.getType());

        bpmTicketFilter = bpmTicketFilterRepository.save(bpmTicketFilter);

        Map<String, Object> response = new HashMap<>();
        response.put("id", bpmTicketFilter.getId());
        response.put("status", bpmTicketFilter.getStatus());
        return response;
    }

    @Override
    public boolean updateStatus(BpmTicketFilterRequest request) {
        BpmTicketFilter bpmTicketFilter = bpmTicketFilterRepository.getBpmTicketFilterById(request.getId());
        if (bpmTicketFilter == null) {
            return false;
        }
        bpmTicketFilter.setStatus(request.getStatus());
        bpmTicketFilter.setUpdatedDate(LocalDateTime.now());
        bpmTicketFilterRepository.save(bpmTicketFilter);
        return true;
    }

    @Override
    public String validate(BpmTicketFilterRequest request, String username) {
        String errorMessage = "";
        BpmTicketFilter data = bpmTicketFilterRepository.findByNameAndCreatedUserAndType(request.getName(), username, request.getType());
        if (data != null && !data.getId().equals(request.getId())) {
            errorMessage = "Tên bộ lọc đã tồn tại. Vui lòng kiểm tra và thử lại.";
        }

        if (request.getStatus().equals("pin")) {
            Long countPin = bpmTicketFilterRepository.countPinByTypeAndCreatedUser(request.getId(), request.getType(), username);
            if (countPin >= 5) {
                errorMessage = "Chỉ ghim tối đa 5 bộ lọc. Vui lòng kiểm tra và thử lại.";
            }
        }
        return errorMessage;
    }

    @Override
    public List<BpmTicketFilterResponse> searchFilter(BpmTicketFilterSearchRequest request) {
        List<BpmTicketFilterResponse> lstResults = new ArrayList<>();
        List<BpmTicketFilter> lstData = bpmTicketFilterRepository.findByCreatedUserAndType(request.getUsername(), request.getSearch(), request.getType());
        if (!ValidationUtils.isNullOrEmpty(lstData)) {
            for (BpmTicketFilter bpmTicketFilter : lstData) {
                BpmTicketFilterResponse bpmTicketFilterResponse = new BpmTicketFilterResponse();
                bpmTicketFilterResponse.setId(bpmTicketFilter.getId());
                bpmTicketFilterResponse.setName(bpmTicketFilter.getName());
                bpmTicketFilterResponse.setStatus(bpmTicketFilter.getStatus());
                bpmTicketFilterResponse.setType(bpmTicketFilter.getType());

                lstResults.add(bpmTicketFilterResponse);
            }
        }

        return lstResults;
    }

    @Override
    public Map<String, Object> getDetailById(Long id, String username) {
        Map<String, Object> response = new HashMap<>();

        BpmTicketFilter data = bpmTicketFilterRepository.getBpmTicketFilterById(id);
        if (data == null) {
            throw new RuntimeException("Bản ghi không tồn tại.");
        }

        response.put("id", data.getId());
        response.put("name", data.getName());
        response.put("status", data.getStatus());
        response.put("type", data.getType());
        if (ObjectUtils.isValidJSON(data.getFilterBody())) {
            Map<String, Object> filterData = ObjectUtils.toObject(data.getFilterBody(), new TypeReference<>() {});
            if (filterData != null) {
                response.putAll(filterData);
            }
        }

        return response;
    }

    @Override
    public void cloneById(Long id) {
        BpmTicketFilter data = bpmTicketFilterRepository.getBpmTicketFilterById(id);
        if (data == null) {
            throw new RuntimeException("Bản ghi không tồn tại.");
        }
        String cloneName = data.getName() + " - clone";
        int copyNumber = 1;

        while (bpmTicketFilterRepository.existsBpmTicketFilterByNameAndTypeAndCreatedUser(cloneName, data.getType(), data.getCreatedUser())) {
            copyNumber++;
            cloneName = data.getName() + " - clone(" + copyNumber + ")";
        }

        if (cloneName.length() > 100) {
            throw new RuntimeException("Tên bộ lọc vượt quá 100 ký tự.");
        }

        BpmTicketFilter cloneData = new BpmTicketFilter();
        cloneData.setName(cloneName);
        cloneData.setFilterBody(data.getFilterBody());
        cloneData.setType(data.getType());
        cloneData.setStatus("unpin");
        cloneData.setCreatedDate(LocalDateTime.now());
        cloneData.setUpdatedDate(LocalDateTime.now());
        cloneData.setCreatedUser(data.getCreatedUser());

        bpmTicketFilterRepository.save(cloneData);
    }

    @Override
    public void deleteById(Long id) {
        BpmTicketFilter data = bpmTicketFilterRepository.getBpmTicketFilterById(id);
        if (data == null) {
            throw new RuntimeException("Bản ghi không tồn tại.");
        }
        bpmTicketFilterRepository.delete(data);
    }

    @Override
    public PageDto searchFilterTicket(BpmTicketFilterSearchRequest request) {
        List<Map<String, Object>> lstResults = new ArrayList<>();
        BpmTicketFilter filterData = bpmTicketFilterRepository.getBpmTicketFilterById(request.getFilterId());
        if (filterData == null) {
            throw new RuntimeException("Bản ghi không tồn tại.");
        }
        Map<String, Object> mapResponse = new HashMap<>();
        if (ObjectUtils.isValidJSON(filterData.getFilterBody())) {
            PrivateFilterRequest filter = ObjectUtils.toObject(filterData.getFilterBody(), PrivateFilterRequest.class);
            if (!ValidationUtils.isNullOrEmpty(request.getListTicketStatus()) && request.getListTicketStatus().contains(ProcInstConstants.Status.PROCESSING.code)) {
                request.getListTicketStatus().add(ProcInstConstants.Status.OPENED.code);
            }
            List<Long> additionalTicketIds;
            if (filter != null) {
                switch (request.getType()) {
                    case "ticket":
                        mapResponse = bpmProcInstSpecification.searchPrivateFilterTicket(filter, request);
                        break;
                    case "approval":
                    case "execution":
                        // ticket additional lấy theo user ycbs
                        additionalTicketIds = bpmDiscussionRepository.findAllTicketIdAdditionalRequestByCreatedUser(request.getUsername());
                        mapResponse = bpmTaskSpecification.searchPrivateFilterTask(filter, request, additionalTicketIds);
                        break;
                    case "assistant":
                        additionalTicketIds = bpmDiscussionRepository.findAllTicketIdAdditionalRequest();
                        mapResponse = bpmProcInstSpecification.searchPrivateFilterAssistant(filter, request, additionalTicketIds);
                        break;
                    default:
                        break;
                }
            }
        } else {
            throw new RuntimeException("Bản ghi không hợp lệ.");
        }
        Long totalItems = (Long) mapResponse.get("count");
        Integer totalPage = common.getPageCount(totalItems, request.getLimit());
        List<PrivateFilterResponse> listRes = (List<PrivateFilterResponse>) mapResponse.get("data");
        List<String> procInstId = listRes.stream().map(PrivateFilterResponse::getTicketProcInstId).collect(Collectors.toList());
        List<BpmTask> listTask = bpmTaskRepository.getBpmTaskByTaskStatusInAndTaskProcInstIdIn(TaskConstants.TabStatus.PROCESSING, procInstId);
        Map<String, List<BpmTask>> groupByProcId = listTask.stream().collect(Collectors.groupingBy(BpmTask::getTaskProcInstId));
        List<BpmTaskUser> lstTaskUser = bpmTaskUserService.getAllTaskUserByTaskIdIn(listTask.stream().map(BpmTask::getTaskId).collect(Collectors.toList()));

        for (PrivateFilterResponse res : listRes) {
            Map<String, Object> result = new HashMap<>();
            result.put("id", res.getId());
            result.put("requestCode", res.getRequestCode());
            result.put("ticketTitle", res.getTicketTitle());
            result.put("ticketStatus", res.getTicketStatus());
            result.put("serviceName", res.getServiceName());
            result.put("ticketCreatedTime", DateTimeUtils.localDateTimeToDate(res.getTicketCreatedTime()));
            result.put("createdUser", res.getCreatedUser());
            result.put("companyCode", res.getCompanyCode());
            result.put("chartNodeName", res.getChartNodeName());
            result.put("ticketPriority", res.getTicketPriority());
            result.put("ticketEditTime", DateTimeUtils.localDateTimeToDate(res.getTicketEditTime()));
            result.put("procDefId", res.getProcDefId());
            result.put("startKey", res.getStartKey());
            result.put("cancelUser", res.getCancelUser());
            result.put("serviceId", res.getServiceId());
            result.put("chartId", res.getChartId());
            result.put("procInstId", res.getTicketProcInstId());

            List<BpmTask> listTaskById = groupByProcId.get(res.getTicketProcInstId());
            if (listTaskById != null && !listTaskById.isEmpty()) {
                List<TaskDetailResponse> ticketTaskDtoList = listTaskById.stream().map(x -> modelMapper.map(x, TaskDetailResponse.class)).collect(Collectors.toList());
                for (TaskDetailResponse taskDetailResponse : ticketTaskDtoList) {
                    if (lstTaskUser != null) { // check đồng duyệt
                        List<String> userCandidates = lstTaskUser.stream()
                                .filter(e -> e.getTaskId().equalsIgnoreCase(taskDetailResponse.getTaskId()))
                                .map(BpmTaskUser::getUserName).collect(Collectors.toList());
                        if (!ValidationUtils.isNullOrEmpty(userCandidates)) {
                            taskDetailResponse.setTaskUsers(userCandidates);
                        }
                    }
                }
                result.put("ticketTaskDtoList", ticketTaskDtoList);
            } else {
                result.put("ticketTaskDtoList", new ArrayList<>());
            }
            lstResults.add(result);
        }

        return PageDto.builder()
                .content(lstResults)
                .number(request.getPage())
                .numberOfElements(request.getPage())
                .page(request.getPage())
                .size(request.getLimit())
                .totalPages(totalPage)
                .totalElements(totalItems)
                .build();
    }

    @Override
    public List<Map<String, Object>> searchFilterOption(BpmTicketFilterSearchRequest request) {
        BpmTicketFilter filterData = bpmTicketFilterRepository.getBpmTicketFilterById(request.getFilterId());
        if (filterData == null) {
            throw new RuntimeException("Bản ghi không tồn tại.");
        }
        Map<String, Object> mapResponse = new HashMap<>();
        if (ObjectUtils.isValidJSON(filterData.getFilterBody())) {
            PrivateFilterRequest filter = ObjectUtils.toObject(filterData.getFilterBody(), new TypeReference<>() {});
            if (filter != null) {
                List<Long> additionalTicketIds = new ArrayList<>();
                switch (request.getType()) {
                    case "ticket":
                        mapResponse = bpmProcInstSpecification.searchPrivateFilterTicket(filter, request);
                        break;
                    case "approval":
                    case "execution":
                        additionalTicketIds = bpmDiscussionRepository.findAllTicketIdAdditionalRequestByCreatedUser(request.getUsername());
                        mapResponse = bpmTaskSpecification.searchPrivateFilterTask(filter, request, additionalTicketIds);
                        break;
                    case "assistant":
                        additionalTicketIds = bpmDiscussionRepository.findAllTicketIdAdditionalRequest();
                        mapResponse = bpmProcInstSpecification.searchPrivateFilterAssistant(filter, request, additionalTicketIds);
                        break;
                    default:
                        break;
                }
            }
        } else {
            throw new RuntimeException("Bản ghi không hợp lệ.");
        }

        List<Map<String, Object>> lstResults = new ArrayList<>();
        List<PrivateFilterResponse> listRes = (List<PrivateFilterResponse>) mapResponse.get("data");
        List<String> procInstId = listRes.stream().map(PrivateFilterResponse::getTicketProcInstId).collect(Collectors.toList());
        List<FilterTaskOptionDto> listTask = bpmTaskRepository.getFilterTaskOptions(TaskConstants.TabStatus.PROCESSING, procInstId);
        Map<String, List<FilterTaskOptionDto>> groupByProcId = listTask.stream().collect(Collectors.groupingBy(FilterTaskOptionDto::getProcInstId));
        List<BpmTaskUser> lstTaskUser = bpmTaskUserService.getAllTaskUserByTaskIdIn(listTask.stream().map(FilterTaskOptionDto::getTaskId).collect(Collectors.toList()));

        for (PrivateFilterResponse res : listRes) {
            Map<String, Object> result = new HashMap<>();
            result.put("id", res.getId());
            result.put("requestCode", res.getRequestCode());
            result.put("ticketTitle", res.getTicketTitle());
            result.put("ticketStatus", res.getTicketStatus());
            result.put("serviceName", res.getServiceName());
            result.put("ticketCreatedTime", res.getTicketCreatedTime());
            result.put("createdUser", res.getCreatedUser());
            result.put("companyCode", res.getCompanyCode());
            result.put("chartNodeName", res.getChartNodeName());
            result.put("ticketPriority", res.getTicketPriority());
            result.put("ticketEditTime", res.getTicketEditTime());
            result.put("procDefId", res.getProcDefId());
            result.put("startKey", res.getStartKey());
            result.put("cancelUser", res.getCancelUser());

            List<FilterTaskOptionDto> listTaskById = groupByProcId.get(res.getTicketProcInstId());
            if (listTaskById != null && !listTaskById.isEmpty()) {
                List<String> lstTaskName;
                List<String> lstTaskAssignee = new ArrayList<>();
                for (FilterTaskOptionDto taskDetailResponse : listTaskById) {
                    if (lstTaskUser != null) { // check đồng duyệt
                        List<String> userCandidates = lstTaskUser.stream()
                                .filter(e -> e.getTaskId().equalsIgnoreCase(taskDetailResponse.getTaskId()))
                                .map(BpmTaskUser::getUserName).collect(Collectors.toList());
                        if (!ValidationUtils.isNullOrEmpty(userCandidates)) {
                            lstTaskAssignee.addAll(userCandidates);
                        }
                    }
                    if (taskDetailResponse.getTaskAssignee() != null) {
                        lstTaskAssignee.add(taskDetailResponse.getTaskAssignee());
                    }
                }
                lstTaskName = listTaskById.stream().map(FilterTaskOptionDto::getTaskName).distinct().collect(Collectors.toList());
                result.put("ticketTaskDtoList", lstTaskName);
                result.put("ticketTaskDtoTaskAssignee", lstTaskAssignee.stream().distinct().collect(Collectors.toList()));
            } else {
                result.put("ticketTaskDtoList", new ArrayList<>());
                result.put("ticketTaskDtoTaskAssignee", new ArrayList<>());
            }
            lstResults.add(result);
        }
        return lstResults;
    }

    @Override
    public Map<Long, Object> countSearchFilter(String username, String type) {
        Map<Long, Object> result = new HashMap<>();
        List<BpmTicketFilter> lstFilter = bpmTicketFilterRepository.getPinByTypeAndCreatedUser(username, type);
        if (!ValidationUtils.isNullOrEmpty(lstFilter)) {
            for (BpmTicketFilter filterData : lstFilter) {
                PrivateFilterRequest filter = ObjectUtils.toObject(filterData.getFilterBody(), new TypeReference<>() {});
                if (filter != null) {
                    Long count = 0L;
                    List<Long> additionalTicketIds;
                    switch (type) {
                        case "ticket":
                            count = bpmProcInstSpecification.countFilterTicket(filter, username);
                            break;
                        case "approval":
                        case "execution":
                            additionalTicketIds = bpmDiscussionRepository.findAllTicketIdAdditionalRequestByCreatedUser(username);
                            count = bpmTaskSpecification.countFilterTask(filter, username, type, additionalTicketIds);
                            break;
                        case "assistant":
                            additionalTicketIds = bpmDiscussionRepository.findAllTicketIdAdditionalRequest();
                            count = bpmProcInstSpecification.countFilterAssistant(filter, username, additionalTicketIds);
                            break;
                        default:
                            break;
                    }
                    result.put(filterData.getId(), count);
                }
            }
        }

        return result;
    }
}
