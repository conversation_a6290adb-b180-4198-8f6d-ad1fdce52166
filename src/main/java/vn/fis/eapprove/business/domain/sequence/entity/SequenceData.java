package vn.fis.eapprove.business.domain.sequence.entity;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;

@Data
@Entity
@Table(name = "sequence_data")
public class SequenceData {
    @Id
    @Column(name = "sequence_name")
    private String sequenceName;

    @Column(name = "sequence_increment")
    private Integer sequenceIncrement;

    @Column(name = "sequence_cur_value")
    private Long sequenceCurValue;

    @Column(name = "created_date")
    private Date createdDate;

    @Column(name = "last_update")
    private Date lastUpdate;
}
