package vn.fis.eapprove.business.domain.legislative.model.mapper;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LegislativeTaskMapper {
    private String taskDefKey;
    private String taskStatus;
    private String legislativeStatus;
    private LocalDateTime createdTime;
    private LocalDateTime finishTime;
}
