package vn.fis.eapprove.business.domain.bpm.service;

import vn.fis.eapprove.security.CredentialHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.domain.bpm.entity.BpmConfigPrintTemplate;
import vn.fis.eapprove.business.domain.bpm.repository.BpmConfigPrintTemplateRepository;
import vn.fis.eapprove.business.dto.BaseDto;
import vn.fis.eapprove.business.dto.BpmPrintTemplateDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.specification.BpmPrintTemplateSpecification;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service("BpmConfigPrintTemplateManagerV1")
public class BpmConfigPrintTemplateManager {

    @Autowired
    private BpmConfigPrintTemplateRepository bpmPrintTemplateRepository;

    @Autowired
    private CredentialHelper credentialHelper;

    @Autowired
    private BpmPrintTemplateSpecification bpmPrintTemplateSpecification;

    public boolean save(BpmPrintTemplateDto bpmPrintTemplateDto) {
        try {
            String createdUser = credentialHelper.getJWTPayload().getUsername();

            BpmConfigPrintTemplate bpmPrintTemplate = new BpmConfigPrintTemplate();
            bpmPrintTemplate.setCreatedDate(new Date());
            bpmPrintTemplate.setCreatedUser(createdUser);
            bpmPrintTemplate.setTemplate(bpmPrintTemplateDto.getTemplate());
            bpmPrintTemplate.setName(bpmPrintTemplateDto.getName());
            bpmPrintTemplate.setDescr(bpmPrintTemplateDto.getDescr());
            bpmPrintTemplate.setTemplate(bpmPrintTemplateDto.getTemplate());
            if (checkExistName(bpmPrintTemplateDto.getName()) == true) {
                return false;
            }
            bpmPrintTemplateRepository.save(bpmPrintTemplate);


            // BpmPrintTemplate updateBpmPrintTemplate = bpmPrintTemplateRepository.findBpmPrintTemplateByName(bpmPrintTemplateDto.getName());
            // String header = "<div id=\"config_print_header_"+updateBpmPrintTemplate.getId()+"\">"+ updateBpmPrintTemplate.getHeader() +"</div>";
            // String footer = "<div id=\"config_print_footer_"+updateBpmPrintTemplate.getId()+"\">"+ updateBpmPrintTemplate.getFooter() +"</div>";
            // bpmPrintTemplate.setHeader(header);
            // bpmPrintTemplate.setFooter(footer);
            // bpmPrintTemplateRepository.save(bpmPrintTemplate);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public BpmConfigPrintTemplate getPrintTemplateById(Long id) {
        try {
            return bpmPrintTemplateRepository.findBpmPrintTemplateById(id);
        } catch (Exception e) {
            return null;
        }
    }

    public boolean updatePrintTemplate(BpmPrintTemplateDto bpmPrintTemplateDto) {
        try {
            String createdUser = credentialHelper.getJWTPayload().getUsername();
            BpmConfigPrintTemplate bpmPrintTemplate = getPrintTemplateById(bpmPrintTemplateDto.getId());
            if (bpmPrintTemplate != null) {
                bpmPrintTemplate.setName(bpmPrintTemplateDto.getName());
                bpmPrintTemplate.setDescr(bpmPrintTemplateDto.getDescr());
                bpmPrintTemplate.setUpdatedUser(createdUser);
                bpmPrintTemplate.setUpdatedDate(new Date());
                bpmPrintTemplate.setTemplate(bpmPrintTemplateDto.getTemplate());
                if (bpmPrintTemplate.getName() == bpmPrintTemplateDto.getName()) {
                    bpmPrintTemplateRepository.save(bpmPrintTemplate);
                    return true;
                } else if (checkExistName(bpmPrintTemplateDto.getName()) == true) {
                    return false;
                }
                bpmPrintTemplateRepository.save(bpmPrintTemplate);
                return true;
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean deletePrintTemplate(Long id) {
        try {
            BpmConfigPrintTemplate bpmPrintTemplate = getPrintTemplateById(id);
            if (bpmPrintTemplate != null) {
                bpmPrintTemplateRepository.delete(bpmPrintTemplate);
                return true;
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public List<BpmConfigPrintTemplate> getAllPrintTemplate() {
        try {
            return bpmPrintTemplateRepository.getAll();
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public boolean checkExistName(String name) {
        try {
            BpmConfigPrintTemplate bpmPrintTemplate = bpmPrintTemplateRepository.findBpmPrintTemplateByName(name);
            if (bpmPrintTemplate != null) {
                return true;
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public PageDto getPagingPrintTemplate(BaseDto criteria) {
        try {
            Integer pageNum = criteria.getPage() - 1;
            Page<BpmConfigPrintTemplate> page = bpmPrintTemplateRepository.findAll(bpmPrintTemplateSpecification.filter(criteria), PageRequest.
                    of(pageNum, criteria.getSize(), Sort.by(Sort.Direction.valueOf(criteria.getSortBy()), criteria.getSortType())));
            List<BpmPrintTemplateDto> listBpmDto = new ArrayList<>();
            for (BpmConfigPrintTemplate bpmPrintTemplate : page) {
                BpmPrintTemplateDto bpmPrintTemplateDto = new BpmPrintTemplateDto();
                bpmPrintTemplateDto.setId(bpmPrintTemplate.getId());
                bpmPrintTemplateDto.setName(bpmPrintTemplate.getName());
                bpmPrintTemplateDto.setDescr(bpmPrintTemplate.getDescr());
                bpmPrintTemplateDto.setCreatedUser(bpmPrintTemplate.getCreatedUser());
                bpmPrintTemplateDto.setCreatedDate(bpmPrintTemplate.getCreatedDate());
                bpmPrintTemplateDto.setUpdatedUser(bpmPrintTemplate.getCreatedUser());
                bpmPrintTemplateDto.setUpdatedDate(bpmPrintTemplate.getUpdatedDate());
                listBpmDto.add(bpmPrintTemplateDto);
            }
            return PageDto.builder()
                    .content(listBpmDto)
                    .number(page.getNumber() + 1)
                    .numberOfElements(page.getNumberOfElements())
                    .page(page.getNumber() + 1)
                    .size(page.getSize())
                    .totalPages(page.getTotalPages())
                    .totalElements(page.getTotalElements())
                    .build();
        } catch (Exception e) {
            return null;
        }
    }

}
