package vn.fis.eapprove.business.domain.sequence.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.support.atomic.RedisAtomicInteger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.sequence.entity.SequenceData;
import vn.fis.eapprove.business.domain.sequence.repository.SequenceDataRepository;
import vn.fis.eapprove.business.domain.sequence.service.SequenceDataManager;


import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
@Transactional
public class SequenceDataManagerImpl implements SequenceDataManager {
    @Autowired
    SequenceDataRepository sequenceDataRepository;

    @Autowired
    RedisConnectionFactory connectionFactory;

    @Override
    public SequenceData getSequenceDataInfo(String sequenceName) {
        if (sequenceName == null || sequenceName.trim().isEmpty()) {
            return null;
        }
        return sequenceDataRepository.getSequenceDataInfo(sequenceName);
    }

    @Override
    public SequenceData createNew(String sequenceName) {
        if (sequenceName == null || sequenceName.trim().isEmpty()) {
            return null;
        }
        SequenceData sequenceData = new SequenceData();
        sequenceData.setSequenceName(sequenceName);
        sequenceData.setSequenceIncrement(1);
        sequenceData.setSequenceCurValue(0l);
        sequenceData.setCreatedDate(new Date());
        sequenceData.setLastUpdate(new Date());

        return sequenceDataRepository.save(sequenceData);
    }

    @Override
    public SequenceData update(SequenceData sequenceData) {
        if (sequenceData == null) {
            return null;
        }
        return sequenceDataRepository.save(sequenceData);
    }

    @Override
    public Long getNextval(String sequenceName) {
        if (sequenceName == null || sequenceName.trim().isEmpty()) {
            return null;
        }
        SequenceData sequenceData = getSequenceDataInfo(sequenceName);
        Long nextVal = 1l;
        //Chưa có seq thì tạo mới
        if (sequenceData == null || sequenceData.getSequenceName() == null || sequenceData.getSequenceName().trim().isEmpty()) {
            sequenceData = new SequenceData();
            sequenceData.setSequenceName(sequenceName);
            sequenceData.setSequenceIncrement(1);
            sequenceData.setCreatedDate(new Date());
            sequenceData.setLastUpdate(new Date());
        } else {
            nextVal = sequenceData.getSequenceCurValue() + sequenceData.getSequenceIncrement();
        }
        sequenceData.setSequenceCurValue(nextVal);
        update(sequenceData);
        return nextVal;
    }

    @Override
    public Long getCurVal(String sequenceName) {
        if (sequenceName == null || sequenceName.trim().isEmpty()) {
            return null;
        }
        SequenceData sequenceData = getSequenceDataInfo(sequenceName);
        //Chưa có seq thì tạo mới
        if (sequenceData != null && sequenceData.getSequenceName() != null && !sequenceData.getSequenceName().trim().isEmpty()) {
            return sequenceData.getSequenceCurValue();
        }
        return null;
    }

    @Override
    public String generateIDBySequenceTemplate(String sequenceTemplate) {
        Long genId = getNextval(sequenceTemplate);
        String regex = ".*?(\\{.*?\\}).*?";
        List<String> subItemConfig = new ArrayList<>();
        Pattern pattern = Pattern.compile(regex);
        Matcher m = pattern.matcher(sequenceTemplate);
        while (m.find()) {
            subItemConfig.add(m.group(1));
        }
        String dateFormat = "";
        String Stt = "";
        String genFinalId = "";
        if (subItemConfig != null && subItemConfig.size() >= 2) {
            sequenceTemplate = sequenceTemplate.replaceAll("\\{", "").replaceAll("\\}", "");
            dateFormat = subItemConfig.get(0).replaceAll("\\{", "").replaceAll("\\}", "");
            Stt = subItemConfig.get(1).replaceAll("\\{", "").replaceAll("\\}", "");
            SimpleDateFormat format = new SimpleDateFormat(dateFormat);
            genFinalId = sequenceTemplate.replaceAll(dateFormat, format.format(new Date())).replaceAll(Stt, String.valueOf(genId));
        } else {
            genFinalId = sequenceTemplate + genId;
        }
        return genFinalId;
    }

    @Override
    public Integer redisAtomicInteger() {
        RedisAtomicInteger redisAtomicInteger = new RedisAtomicInteger("key", connectionFactory);
        return redisAtomicInteger.getAndIncrement();
    }
}
