package vn.fis.eapprove.business.domain.bpm.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import vn.fis.eapprove.business.domain.notification.service.NotificationService;
import vn.fis.eapprove.security.CredentialHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.assistant.repository.AssistantRepository;
import vn.fis.eapprove.business.domain.bpm.entity.*;
import vn.fis.eapprove.business.domain.bpm.repository.*;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcdefNotificationService;
import vn.fis.eapprove.business.domain.bpm.service.BpmService;
import vn.fis.eapprove.business.domain.changeAssignee.repository.ChangeAssigneeHistoryRepository;
import vn.fis.eapprove.business.domain.notification.entity.NotificationTemplate;
import vn.fis.eapprove.business.domain.notification.entity.NotificationTemplateDetail;
import vn.fis.eapprove.business.domain.notification.repository.NotificationTemplateDetailRepository;
import vn.fis.eapprove.business.domain.notification.repository.NotificationTemplateRepository;
import vn.fis.eapprove.business.dto.UserInfoDto;
import vn.fis.eapprove.business.model.AccountModel;
import vn.fis.eapprove.business.model.request.ChartNodeDtoRequest;
import vn.fis.eapprove.business.model.request.VariableValueDto;
import vn.fis.eapprove.business.model.response.UserTitleResponse;
import vn.fis.eapprove.business.tenant.manager.ActHiVarInstManager;
import vn.fis.eapprove.business.tenant.manager.CustomerService;

import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.spro.common.constants.ProcInstConstants;
import vn.fis.spro.common.constants.TaskConstants;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static vn.fis.spro.common.constants.ProcInstConstants.Notifications;

@Slf4j
@Service("BpmProcdefNotificationServiceImplV1")
@Transactional
@RequiredArgsConstructor
public class BpmProcdefNotificationServiceImpl implements BpmProcdefNotificationService {

    private final BpmProcdefNotificationRepository bpmProcdefNotificationRepository;
    private final BpmProcInstRepository bpmProcInstRepository;
    private final BpmProcdefRepository bpmProcdefRepository;
    private final ActHiVarInstManager actHiVarInstManager;
    private final BpmProcInstNotifyUserRepository bpmProcInstNotifyUserRepository;
    private final BpmProInstNotifyGroupRepository bpmProInstNotifyGroupRepository;
    private final NotificationTemplateRepository notificationTemplateRepository;
    private final NotificationTemplateDetailRepository notificationTemplateDetailRepository;
    private final BpmNotifyUserRepository bpmNotifyUserRepository;
    private final AssistantRepository assistantRepository;
    private final BpmSharedRepository bpmSharedRepository;
    private final BpmTaskRepository bpmTaskRepository;
    private final CredentialHelper credentialHelper;
    private final CustomerService customerService;
    private final ChangeAssigneeHistoryRepository changeAssigneeHistoryRepository;
    private final BpmService bpmService;
    private final BpmProcDefHistoryRepository bpmProcDefHistoryRepository;
    private final NotificationService notificationService;

    @Value("${app.superAdmin.account}")
    private String appSuperAdminAccount;


    @Override
    public List<BpmNotifyUser> addNotificationsByConfig(Long bpmProcdefId, String nextTaskDefKey, Map<String, VariableValueDto> variables,
                                                        Long ticketId, Boolean isGetOldVariable, List<String> lstCustomerEmails,
                                                        String actionCode, String emailExe)  {
        if (ValidationUtils.isNullOrEmpty(nextTaskDefKey)) {
            return Collections.emptyList();
        }
        if (variables == null) {
            variables = new HashMap<>();
        }

        BpmProcdef bpmProcdef;
        BpmProcDefHistory bpmProcDefHistory = null;
        String procDefId = null;
        //Lấy danh sách các biến theo ticketId
        BpmProcInst currentTicket;
        if (ticketId != null && !Objects.equals(ticketId, 0L)) {
            currentTicket = bpmProcInstRepository.getBpmProcInstByTicketId(ticketId);
            // lấy lịch sử quy trình
            bpmProcDefHistory = bpmProcDefHistoryRepository.findByProcDefId(currentTicket.getTicketProcDefId());
            // ko có lịch sử thì lấy theo procDef
            if (bpmProcDefHistory == null) {
                //Nếu không truyền lấy lại theo ticketId và kiểm tra xem có tắt thông báo không
                if (bpmProcdefId != null) {
                    bpmProcdef = bpmProcdefRepository.getBpmProcDefById(bpmProcdefId);
                } else {
                    bpmProcdef = bpmProcdefRepository.findByServiceId(currentTicket.getServiceId());
                }
                //Tắt thông báo rồi thì không làm gì nữa
                if (bpmProcdef != null && bpmProcdef.getOffNotification() != null && bpmProcdef.getOffNotification()) {
                    return null;
                }
                procDefId = bpmProcdef.getProcDefId();
            } else {
                if (bpmProcDefHistory.getOffNotification() != null && bpmProcDefHistory.getOffNotification()) {
                    return null;
                }
                procDefId = bpmProcDefHistory.getProcDefId();
            }

            //Đã có ticket thì lấy all biến đã lưu theo ProcInstId
            if (isGetOldVariable) {
                Map<String, VariableValueDto> variableValueDtoMap = actHiVarInstManager.getVariByTicketAndConvertToMap(currentTicket.getTicketProcInstId());
                if (variableValueDtoMap != null) {
                    variables.putAll(variableValueDtoMap);
                }
            }
        } else {
            currentTicket = null;
        }

        //Map lại actionCode do chia lại trạng thái của phiếu
        String actStatus = getActStatus(currentTicket, actionCode, nextTaskDefKey);

        //Email thao tác
        if (ValidationUtils.isNullOrEmpty(emailExe)) {
            emailExe = appSuperAdminAccount;
        }

        //Add thông tin biến mặc định
        addDefaultVariables(currentTicket, variables, nextTaskDefKey, emailExe);


        List<BpmNotifyUser> bpmNotifyUsers = new ArrayList<>();
        List<String> lstNotificationObjectChecks = new ArrayList<>();
        List<String> lstActionIgnoreGetUser = Arrays.asList(
                Notifications.ADDITIONAL_REQUEST_COMPLETED.code,
                Notifications.CLOSED.code,
                Notifications.AUTO_CLOSED.code,
                Notifications.DISCUSSION.code,
                Notifications.DISCUSSION_FOR_CREATED_USER.code,
                Notifications.SHARE.code,
                Notifications.REMOVE_SHARE.code,
                Notifications.RECALLING.code,
                Notifications.CHANGE_EXECUTOR.code,
                Notifications.ORG_CHANGE_EXECUTOR.code,
                Notifications.CANCEL_CHANGE_EXECUTOR.code,
                Notifications.HANDOVER_MY_TICKET.code,
                Notifications.HANDOVER_MY_ASSISTANT.code,
                Notifications.HANDOVER_MY_TASK.code,
                Notifications.RECALLED.code);
        if (lstCustomerEmails == null) {
            lstCustomerEmails = new ArrayList<>();
        }

        List<BpmProcdefNotification> bpmProcdefNotificationList;
        //Lấy danh sách tất cả cấu hình theo processId -> lấy danh sách cấu hình theo procDefId (version)
        bpmProcdefNotificationList = bpmProcdefNotificationRepository.findBpmProcdefNotificationsByProcDefIdAndStatus(procDefId, "1");
        List<BpmProcdefNotification> bpmProcdefNotifications = getBpmProcdefNotifications(bpmProcdefNotificationList, actStatus, nextTaskDefKey);

        //Nếu tìm thấy cấu hình thì tiến hành gửi message
        if (bpmProcdefNotifications != null && !bpmProcdefNotifications.isEmpty()) {
            //Lấy ra danh sách user thực hiện - approval

//            List<String> lstUserAssign = getLstUserAssignees(currentTicket, actionCode, nextTaskDefKey, lstCustomerEmails);

            for (BpmProcdefNotification bpmProcdefNotification : bpmProcdefNotifications) {
                if (bpmProcdefNotification != null) {
                    Long detailFromTemplateId = bpmProcdefNotification.getNotificationTemplateId();

                    if (detailFromTemplateId != null) {
                        //Lấy danh sách người gửi
                        List<String> lstFieldRecipientEmail;
                        if (bpmProcdefNotification.getFieldNotification() != null) {
                            lstFieldRecipientEmail = ObjectUtils.toObject(bpmProcdefNotification.getFieldNotification(), new TypeReference<>() {
                            });
                        } else {
                            lstFieldRecipientEmail = new ArrayList<>();
                        }

                        //Ngoài các action này cần tự tính toán user trong flow
                        if (!lstActionIgnoreGetUser.contains(actionCode)) {
                            lstCustomerEmails.addAll(getLstUserCustomer(currentTicket, actionCode, nextTaskDefKey, bpmProcdefNotification, lstNotificationObjectChecks));
                        }

                        //Nếu có danh sách người gửi
                        if ((lstFieldRecipientEmail != null && !lstFieldRecipientEmail.isEmpty()) || !lstCustomerEmails.isEmpty()) {
                            //Lấy cấu hình của thông báo
                            List<NotificationTemplateDetail> notificationTemplateDetails = notificationTemplateDetailRepository.getDetailFromTemplateId(detailFromTemplateId);
                            if (notificationTemplateDetails != null && !notificationTemplateDetails.isEmpty()) {
                                for (NotificationTemplateDetail notificationTemplateDetail : notificationTemplateDetails) {
                                    //Lấy nội dung thông báo
                                    String typeSend = StringUtil.nvl(notificationTemplateDetail.getType(), "MAIL").trim().toUpperCase();

                                    String title = StringUtil.nvl(notificationTemplateDetail.getTitle(), "This is the default title of the notification!");

                                    String contentNotification = StringUtil.nvl(notificationTemplateDetail.getContent(), "This is the default content of the notification!");
                                    //Kiểm tra xem title thông báo có cấu hình động không
                                    if (!ValidationUtils.isNullOrEmpty(notificationTemplateDetail.getTitle())) {
                                        title = replaceContentText(title, variables, false);
                                        log.info("title after replace = {}", title);
                                    }

                                    //Kiểm tra xem nội dung thông báo có cấu hình động không
                                    if (!ValidationUtils.isNullOrEmpty(notificationTemplateDetail.getContent())) {
                                        contentNotification = replaceContentText(contentNotification, variables, true);
                                        log.info("ContentNotification after replace = {}", contentNotification);
                                    }

                                    String actTitle = getPrefixTitle(actionCode) + " " + title;
                                    //Có danh sách emailExe truyền vào thì ưu tiên lấy theo danh sách
                                    if (!ValidationUtils.isNullOrEmpty(lstCustomerEmails)) {
                                        //Add tất cả user trong list
                                        for (String recipientEmailItem : lstCustomerEmails) {
                                            if (!ValidationUtils.isNullOrEmpty(recipientEmailItem)) {
                                                bpmNotifyUsers.add(getBpmNotifyUser(ticketId, recipientEmailItem, actTitle, contentNotification, typeSend, emailExe, recipientEmailItem, bpmProcdefNotification.getNotificationObject()));
                                            }
                                        }
                                    }

                                    //Từ tên trường trong biểu mẫu map lại lấy giá trị user cụ thể
                                    if (bpmProcdefNotification.getAddMoreConfig() != null && bpmProcdefNotification.getAddMoreConfig()
                                            && lstFieldRecipientEmail != null && !lstFieldRecipientEmail.isEmpty()) {
                                        for (String fieldItem : lstFieldRecipientEmail) {
                                            if (!ValidationUtils.isNullOrEmpty(fieldItem)) {
                                                String strRecipientEmails = StringUtil.nvl(findObjectInMapVariable(variables, bpmProcdefNotification.getTaskDefKey() + "_" + fieldItem, false), "");
                                                //Chia trường hợp xem xét giá trị là mảng hay giá trị đơn lẻ
                                                if (!strRecipientEmails.isEmpty()) {
                                                    if (strRecipientEmails.contains("[")) {//Mảng user
                                                        List<String> lstRecipientEmails = ObjectUtils.toObject(strRecipientEmails, new TypeReference<>() {});
                                                        //Add tất cả user trong list
                                                        if (!ValidationUtils.isNullOrEmpty(lstRecipientEmails)) {
                                                            for (String recipientEmailItem : lstRecipientEmails) {
                                                                if (!ValidationUtils.isNullOrEmpty(recipientEmailItem)) {
                                                                    bpmNotifyUsers.add(getBpmNotifyUser(ticketId, recipientEmailItem, actTitle, contentNotification, typeSend, emailExe, recipientEmailItem, bpmProcdefNotification.getNotificationObject()));
                                                                }
                                                            }
                                                        }
                                                    } else { //User đơn lẻ
                                                        bpmNotifyUsers.add(getBpmNotifyUser(ticketId, strRecipientEmails, actTitle, contentNotification, typeSend, emailExe, strRecipientEmails, bpmProcdefNotification.getNotificationObject()));
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    //Reset cho vòng mới
                    lstCustomerEmails = new ArrayList<>();
                }
            }

            if (!ValidationUtils.isNullOrEmpty(bpmNotifyUsers)) {
                bpmNotifyUsers = bpmNotifyUsers.stream().peek(e -> e.setRecipient(e.getRecipient().toLowerCase())).collect(Collectors.toList());
                List<BpmNotifyUser> bpmNotifyUsersFinal = new ArrayList<>();
                // get account emailExe map
                List<String> lstAccounts = bpmNotifyUsers.stream().distinct().map(BpmNotifyUser::getRecipient).collect(Collectors.toList());
                Map<String, AccountModel> userAccountMap = getUserAccountMap(lstAccounts);
                List<String> lstAccountsDistinctFinal = new ArrayList<>();

                if (userAccountMap != null) {
                    for (BpmNotifyUser bpmNotifyUser : bpmNotifyUsers) {
                        //Lọc trùng lần cuối theo hệ thống gửi
                        if (!ValidationUtils.isNullOrEmpty(bpmNotifyUser.getRecipient()) && !lstAccountsDistinctFinal.contains(bpmNotifyUser.getRecipient() + "_" + bpmNotifyUser.getType())) {
                            bpmNotifyUser.setAddress(getAddress(userAccountMap, bpmNotifyUser.getRecipient(), bpmNotifyUser.getType()));
                            //Replace Dear @userName!
                            String strMessage = StringUtil.nvl(bpmNotifyUser.getMessage(), "");
                            if (strMessage.contains("@{{fullName}}")) {
                                AccountModel account = userAccountMap.get(bpmNotifyUser.getRecipient());
                                String fullName = "";
                                if (account != null) {
                                    fullName = StringUtil.nvl(account.getLastname(), "") + " " + StringUtil.nvl(account.getFirstname(), "");
                                } else {
                                    fullName = bpmNotifyUser.getRecipient();
                                }
                                strMessage = strMessage.replaceAll("\\@\\{\\{fullName\\}\\}", fullName.trim());

                                bpmNotifyUser.setMessage(strMessage);
                            }
                            // replace dear userTitle
                            if (strMessage.contains("@{{recipientTitle}}")) {
                                AccountModel account = userAccountMap.get(bpmNotifyUser.getRecipient());
                                String finalTitle = "";
                                if (account != null) {
                                    finalTitle = StringUtil.nvl(account.getFinalTitle(), "");
                                }
                                strMessage = strMessage.replaceAll("\\@\\{\\{recipientTitle\\}\\}", finalTitle.trim());

                                bpmNotifyUser.setMessage(strMessage);
                            }
                            lstAccountsDistinctFinal.add(bpmNotifyUser.getRecipient() + "_" + bpmNotifyUser.getType());

                            //Lưu thêm companyCode để call mail admin
                            List<Map<String, Object>> chartObj = bpmService.findCompanyCodeByUsername(bpmNotifyUser.getCreatedUser());
                            bpmNotifyUser.setCompanyCode(chartObj.isEmpty() ? null : chartObj.get(0).get("companyCode").toString());
                            bpmNotifyUsersFinal.add(bpmNotifyUser);
                        }
                    }
                }
                bpmNotifyUserRepository.saveAll(bpmNotifyUsersFinal);
                bpmNotifyUsers = bpmNotifyUsersFinal;
            }
        }

        // push noti
        notificationService.pushNotification(bpmNotifyUsers);

        return bpmNotifyUsers;
    }

    private String getPrefixTitle(String actionCode) {
        if (actionCode == null) {
            return "[DEFAULT]";
        }
        return "[" + Notifications.valueOf(actionCode).name + "]";
    }

    private String getActStatus(BpmProcInst currentTicket, String actionCode, String nextTaskDefKey) {
        if (currentTicket == null || actionCode == null) {
            return actionCode;
        }
        String actStatus = StringUtil.nvl(actionCode, "");
        //Với 3 task này cần check lại xem có phải Cập nhật lại sau khi trả lại
        if (actionCode.equalsIgnoreCase(Notifications.OPENED.code)
                || actionCode.equalsIgnoreCase(Notifications.EXECUTION.code)
                || actionCode.equalsIgnoreCase(Notifications.APPROVAL.code)) {
            //Kiểm tra có task trạng thái RE_CREATED_BY_RU không? nếu có => task này là task thực hiện lại
            List<BpmTask> lstTask = bpmTaskRepository.findBpmTasksByTaskProcInstIdAndTaskDefKeyAndTaskStatusIn(currentTicket.getTicketProcInstId(), nextTaskDefKey, Arrays.asList(TaskConstants.Status.DELETED_BY_RU.code, TaskConstants.Status.RE_CREATED_BY_RU.code));
            if (lstTask != null && lstTask.size() > 0) {
                actStatus = Notifications.RE_CREATED_BY_RU.code;
            }
        }

        // Xử lý actStatus cho user tạo phiếu
        if (actionCode.equalsIgnoreCase(Notifications.OPENED_FOR_CREATED_USER.code)) {
            actStatus = Notifications.OPENED.code;
        }
        return actStatus;
    }


    //todo: Đoạn mã này có chức năng lấy danh sách người dùng được thông báo dựa trên thông tin liên quan đến phiếu (currentTicket),
    // mã hành động (actionCode), nextTaskDefKey, cấu hình thông báo BPM (bpmProcdefNotification),
    // và danh sách kiểm tra đối tượng thông báo (lstNotificationObjectChecks)
    private List<String> getLstUserAssignees(BpmProcInst currentTicket, String actionCode, String nextTaskDefKey, List<String> lstCustomerEmails) {
        List<String> lstUserAssignees = new ArrayList<>();
        if (lstCustomerEmails != null) {
            lstUserAssignees.addAll(lstCustomerEmails);
        }
        if (currentTicket != null) {
            //Nếu là bước bắt đầu thì add thêm user tạo ticket - 25/09 check nếu có Nhận Thông báo mail mới add user tạo phiếu
            if (actionCode.equalsIgnoreCase(Notifications.OPENED.code)
                    || actionCode.equalsIgnoreCase(Notifications.FINISH.code)
                    || (!ValidationUtils.isNullOrEmpty(currentTicket.getEmailNotification())
                    && currentTicket.getEmailNotification().equals(Boolean.TRUE)
                    && (nextTaskDefKey.equalsIgnoreCase(currentTicket.getTicketStartActId())
                    || actionCode.equalsIgnoreCase(Notifications.CLOSED.code)
                    || actionCode.equalsIgnoreCase(Notifications.AUTO_CLOSED.code))
            )

            ) {
                lstUserAssignees.add(currentTicket.getCreatedUser());
            }

            //Lấy ra danh sách các user task thực hiện lại của bước đó
            if (actionCode.equalsIgnoreCase(Notifications.RE_CREATED_BY_RU.code)) {
                List<String> lstUsers = bpmTaskRepository.getAllUserAssigneeByTaskDefKeyAndStatus(currentTicket.getTicketProcInstId(), Arrays.asList(nextTaskDefKey), Arrays.asList("RE_CREATED_BY_RU"));
                if (lstUsers != null) {
                    lstUserAssignees.addAll(lstUsers);
                }
            }

            //Lấy ra danh sách các user task thực hiện theo taskdefkey
            if (actionCode.equalsIgnoreCase(Notifications.ADDITIONAL_REQUEST_COMPLETED.code)
                    || actionCode.equalsIgnoreCase(Notifications.EXECUTION.code)
                    || actionCode.equalsIgnoreCase(Notifications.APPROVAL.code)
                    || actionCode.equalsIgnoreCase(Notifications.OPENED.code)
            ) {
                List<String> lstUsers = bpmTaskRepository.getAllUserAssigneeByTaskDefKeyAndStatus(currentTicket.getTicketProcInstId(),
                        Arrays.asList(nextTaskDefKey),
                        Arrays.asList(TaskConstants.Status.ACTIVE.code,
                                TaskConstants.Status.PROCESSING.code,
                                TaskConstants.Status.RE_CREATED_BY_RU.code,
                                TaskConstants.Status.WAIT.code));
                // add orgAssignee nếu có
                List<String> lstOrgAssignees = changeAssigneeHistoryRepository.getListOrgAssigneeByProcInstIdAndTaskDefKeyAndToAssigneeIn(currentTicket.getTicketProcInstId(), nextTaskDefKey, lstUsers);
                lstUserAssignees = Stream.concat(lstUserAssignees.stream(), lstOrgAssignees.stream()).collect(Collectors.toList());
                if (lstUsers != null) {
                    lstUserAssignees.addAll(lstUsers);
                }
            }

//            //Khi đã có danh sách user thì lấy thêm các user gốc ủy quyền; đổi người thực hiện đã tính toán bên ngoài đủ nên k cần get lại
//            if(lstUserAssignees != null && lstUserAssignees.size() > 0 && currentTicket != null
//                    && !actionCode.equalsIgnoreCase(Notifications.CHANGE_EXECUTOR.code)){
//                List<String> lstUserRootAssignees = authorityManagementRepository.findAuthorityManagementsByTicketIdAndToAccountIn(currentTicket.getTicketId(), lstCustomerEmails);
//                if (lstUserRootAssignees != null) {
//                    lstUserAssignees.addAll(lstUserRootAssignees);
//                }
//            }
        }
        return lstUserAssignees.stream().distinct().collect(Collectors.toList());
    }


    //todo:kiểm tra xem một đối tượng thông báo cụ thể nên được thêm vào danh sách thông báo hay không.
    private boolean isNotificationObjectAdd(BpmProcdefNotification bpmProcdefNotification, List<String> lstNotificationObjectChecks, String notificationObjectCheck) {
        boolean isResults = false;
        if (!ValidationUtils.isNullOrEmpty(bpmProcdefNotification.getNotificationObject())) {
            //đối tượng trùng với đối tượng đang check thì cho phép add
            if (bpmProcdefNotification.getNotificationObject().equalsIgnoreCase(notificationObjectCheck)
                    && !lstNotificationObjectChecks.contains(notificationObjectCheck)) {
                lstNotificationObjectChecks.add(notificationObjectCheck);
                isResults = true;
            } else if (bpmProcdefNotification.getNotificationObject().equalsIgnoreCase(ProcInstConstants.NotificationsObject.ALL.code)//Đối tượng gửi All mà chưa add thì cho phép add
                    && !lstNotificationObjectChecks.contains(notificationObjectCheck)) {
                lstNotificationObjectChecks.add(notificationObjectCheck);
                isResults = true;
            }
        }
        return isResults;
    }


    //todo: kiểm tra người dùng được nhận thông báo
    private List<String> getLstUserCustomer(BpmProcInst currentTicket, String actionCode, String nextTaskDefKey, BpmProcdefNotification bpmProcdefNotification, List<String> lstNotificationObjectChecks) {
        List<String> lstUserCustomer = new ArrayList<>();
        if (currentTicket != null) {
            if (actionCode.equalsIgnoreCase(Notifications.OPENED.code)
                    || actionCode.equalsIgnoreCase(Notifications.EXECUTION.code)
                    || actionCode.equalsIgnoreCase(Notifications.APPROVAL.code)
                    || actionCode.equalsIgnoreCase(Notifications.FINISH.code)
                    || actionCode.equalsIgnoreCase(Notifications.CANCEL.code)
                    || actionCode.equalsIgnoreCase(Notifications.AUTO_CANCEL.code)) {

                if (isNotificationObjectAdd(bpmProcdefNotification, lstNotificationObjectChecks, ProcInstConstants.NotificationsObject.USER_NOTIFICATION.code)) {
                    //Lấy danh sách người được thông báo
                    List<String> userNotifys = bpmProcInstNotifyUserRepository.getLstUsersByTicketId(currentTicket.getTicketId());
                    if (userNotifys != null) {
                        lstUserCustomer.addAll(userNotifys);
                    }
                }

                if (isNotificationObjectAdd(bpmProcdefNotification, lstNotificationObjectChecks, ProcInstConstants.NotificationsObject.GROUP_NOTIFICATION.code)) {
                    //Lấy danh sách nhóm được thông báo
                    List<BpmProInstNotifyGroup> bpmProInstNotifyGroups = bpmProInstNotifyGroupRepository.getNotifyGroupsByBpmProcinstId(currentTicket.getTicketId());
                    if (bpmProInstNotifyGroups != null && bpmProInstNotifyGroups.size() > 0) {
                        List<Long> lstGroupId = new ArrayList<>();
                        List<Long> lstChartId = new ArrayList<>();
                        for (BpmProInstNotifyGroup bpmProInstNotifyGroup : bpmProInstNotifyGroups) {
                            lstGroupId.add(Long.valueOf(bpmProInstNotifyGroup.getGroupId()));
                            if (!lstChartId.contains(bpmProInstNotifyGroup.getChartId())) {
                                lstChartId.add(bpmProInstNotifyGroup.getChartId());
                            }
                        }
                        //Call api lấy danh sách user từ nhóm thông báo
                        if (lstGroupId.size() > 0 && lstChartId.size() > 0) {
                            ChartNodeDtoRequest chartNodeDtoRequest = new ChartNodeDtoRequest().builder()
                                    .lstIds(lstChartId)
                                    .lstNodeIds(lstGroupId)
                                    .isGetChildren(false).build();
                            List<UserInfoDto> userInfoDtos = customerService.getUserInfoByChartIdAndCharNodeIds(chartNodeDtoRequest);
                            if (userInfoDtos != null && userInfoDtos.size() > 0) {
                                List<String> userGroupNotifys = userInfoDtos.stream().collect(Collectors.mapping(userInfoDto ->
                                        ValidationUtils.isNullOrEmpty(userInfoDto.getUsername()) ? userInfoDto.getEmail() : userInfoDto.getUsername(), Collectors.toList()));
                                if (userGroupNotifys != null && userGroupNotifys.size() > 0) {
                                    lstUserCustomer.addAll(userGroupNotifys);
                                }
                            }
                        }
                    }
                }

                if (!actionCode.equalsIgnoreCase(Notifications.FINISH.code) && isNotificationObjectAdd(bpmProcdefNotification, lstNotificationObjectChecks, ProcInstConstants.NotificationsObject.ASSISTANT_NOTIFICATION.code)) {
                    //Lấy danh sách trợ lý
                    List<String> lstAssistantEmails = assistantRepository.getAssistantEmailByTicketId(StringUtil.nvl(currentTicket.getTicketId(), ""));
                    if (lstAssistantEmails != null) {
                        lstUserCustomer.addAll(lstAssistantEmails);
                    }
                }
            }

            if (isNotificationObjectAdd(bpmProcdefNotification, lstNotificationObjectChecks, ProcInstConstants.NotificationsObject.COMPLETED_NOTIFICATION.code)) {
                //Lấy ra danh sách các user task đã hoàn thành
                if (actionCode.equalsIgnoreCase(Notifications.CANCEL.code)) {
                    List<String> lstUserAssignees = bpmTaskRepository.getAllUserAssigneeByStatus(currentTicket.getTicketProcInstId(), Arrays.asList(TaskConstants.Status.COMPLETED.code));
                    if (lstUserAssignees != null) {
                        lstUserCustomer.addAll(lstUserAssignees);
                    }
                }
            }

            if (isNotificationObjectAdd(bpmProcdefNotification, lstNotificationObjectChecks, ProcInstConstants.NotificationsObject.DELETED_BY_RU_NOTIFICATION.code)) {
                //Lấy ra danh sách các user task đã trả về
                if (actionCode.equalsIgnoreCase(Notifications.DELETED_BY_RU.code)
                        || actionCode.equalsIgnoreCase(Notifications.CANCEL.code)
                        || actionCode.equalsIgnoreCase(Notifications.AUTO_CANCEL.code)) {
                    List<String> lstUserAssignees = bpmTaskRepository.getAllTaskUserByStatus(
                            currentTicket.getTicketProcInstId(),
                            Arrays.asList(
                                    TaskConstants.Status.DELETED_BY_RU.code,
                                    TaskConstants.Status.COMPLETED.code,
                                    TaskConstants.Status.ACTIVE.code,
                                    TaskConstants.Status.PROCESSING.code
                            )
                    );
                    if (lstUserAssignees != null) {
                        lstUserCustomer.addAll(lstUserAssignees);
                    }
                }
            }

            if (isNotificationObjectAdd(bpmProcdefNotification, lstNotificationObjectChecks, ProcInstConstants.NotificationsObject.PROCESSING_NOTIFICATION.code)) {
                //Lấy ra danh sách các user task thực hiện theo taskdefkey
                if (actionCode.equalsIgnoreCase(Notifications.ADDITIONAL_REQUEST_COMPLETED.code)
                        || actionCode.equalsIgnoreCase(Notifications.EXECUTION.code)
                        || actionCode.equalsIgnoreCase(Notifications.APPROVAL.code)
                        || actionCode.equalsIgnoreCase(Notifications.OPENED.code)
                        || actionCode.equalsIgnoreCase(Notifications.CANCEL.code)
                        || actionCode.equalsIgnoreCase(Notifications.AUTO_CANCEL.code)
                ) {
//                List<String> lstUserAssignees = bpmTaskRepository.getAllUserAssigneeByTaskDefKeys(currentTicket.getTicketProcInstId(), Arrays.asList(nextTaskDefKey));
                    List<String> lstUserAssignees = bpmTaskRepository.getAllUserAssigneeByTaskDefKeyAndStatus(currentTicket.getTicketProcInstId(),
                            Arrays.asList(nextTaskDefKey),
                            Arrays.asList(TaskConstants.Status.ACTIVE.code,
                                    TaskConstants.Status.PROCESSING.code,
                                    TaskConstants.Status.RE_CREATED_BY_RU.code,
                                    TaskConstants.Status.WAIT.code));
                    // add orgAssignee nếu có
                    List<String> lstOrgAssignees = changeAssigneeHistoryRepository.getListOrgAssigneeByProcInstIdAndTaskDefKeyAndToAssigneeIn(currentTicket.getTicketProcInstId(), nextTaskDefKey, lstUserAssignees);
                    lstUserCustomer = Stream.concat(lstUserAssignees.stream(), lstOrgAssignees.stream()).collect(Collectors.toList());
                }
            }

            if (isNotificationObjectAdd(bpmProcdefNotification, lstNotificationObjectChecks, ProcInstConstants.NotificationsObject.ALL_USER_NOTIFICATION.code)) {
                //Lấy ra danh sách các user task trong quy trình
                if (actionCode.equalsIgnoreCase(Notifications.ASSISTANT_OPINION.code)) {
                    List<String> lstUserAssignees = bpmTaskRepository.getAllUserAssigneeByProcInstId(currentTicket.getTicketProcInstId());
                    if (lstUserAssignees != null) {
                        lstUserCustomer.addAll(lstUserAssignees);
                    }
                }
            }

            if (isNotificationObjectAdd(bpmProcdefNotification, lstNotificationObjectChecks, ProcInstConstants.NotificationsObject.SHARE_NOTIFICATION.code)) {
                //Lấy ra danh sách user được chia sẻ
                if (actionCode.equalsIgnoreCase(Notifications.CANCEL.code)
                        || actionCode.equalsIgnoreCase(Notifications.AUTO_CANCEL.code)
                        || actionCode.equalsIgnoreCase(Notifications.FINISH.code)
                        || actionCode.equalsIgnoreCase(Notifications.APPROVAL.code)
                        || actionCode.equalsIgnoreCase(Notifications.EXECUTION.code)
                        || actionCode.equalsIgnoreCase(Notifications.COMPLETED.code)
                ) {
                    List<String> lstShareUsers = bpmSharedRepository.getAllShareUserByProcInstId(currentTicket.getTicketProcInstId());
                    if (lstShareUsers != null) {
                        lstUserCustomer.addAll(lstShareUsers);
                    }
                }
            }

            if (isNotificationObjectAdd(bpmProcdefNotification, lstNotificationObjectChecks, ProcInstConstants.NotificationsObject.CREATE_USER_NOTIFICATION.code)) {
                // Add user tạo phiếu
                if (
//                        actionCode.equalsIgnoreCase(Notifications.OPENED.code) || // case tạo phiếu -> user tạo phiếu xử lý = actionCode riêng
                        actionCode.equalsIgnoreCase(Notifications.OPENED_FOR_CREATED_USER.code) ||
                        actionCode.equalsIgnoreCase(Notifications.FINISH.code)
                ) {
                    lstUserCustomer.add(currentTicket.getCreatedUser());
                }

                // Nếu có Nhận Thông báo mail mới add user tạo phiếu
                if (!ValidationUtils.isNullOrEmpty(currentTicket.getEmailNotification())
                        && currentTicket.getEmailNotification().equals(Boolean.TRUE)
                        && (actionCode.equalsIgnoreCase(Notifications.ADDITIONAL_REQUEST.code)
                        || actionCode.equalsIgnoreCase(Notifications.EXECUTION.code)
                        || actionCode.equalsIgnoreCase(Notifications.APPROVAL.code)
                        || actionCode.equalsIgnoreCase(Notifications.ASSISTANT_OPINION.code)
                        || actionCode.equalsIgnoreCase(Notifications.CANCEL.code)
                        || actionCode.equalsIgnoreCase(Notifications.AUTO_CANCEL.code)
                        || actionCode.equalsIgnoreCase(Notifications.DELETED_BY_RU.code)
                        || actionCode.equalsIgnoreCase(Notifications.CLOSED.code)
                        || actionCode.equalsIgnoreCase(Notifications.AUTO_CLOSED.code)
                        || actionCode.equalsIgnoreCase(Notifications.DISCUSSION.code)
                )) {
                    lstUserCustomer.add(currentTicket.getCreatedUser());
                }
            }
        }
        return lstUserCustomer.stream().distinct().collect(Collectors.toList());
    }

    //todo: Lấy cấu hình thông báo
    private List<BpmProcdefNotification> getBpmProcdefNotifications(List<BpmProcdefNotification> bpmProcdefNotificationList, String actionCode, String nextTaskDefKey) {
        //Trường hợp tìm thấy action bị off thông báo thì không tìm cấu hình thông báo trung toàn hệ thống nữa
        List<BpmProcdefNotification> bpmProcdefNotifications = bpmProcdefNotificationList.stream().collect(Collectors.filtering(s ->
                s.getActionCode() != null
                        && s.getOffNotification()
                        && (!s.getAddMoreConfig() || s.getTaskDefKeyNotification().equalsIgnoreCase(nextTaskDefKey))
                        && s.getActionCode().equalsIgnoreCase(actionCode), Collectors.toList()));
        if (bpmProcdefNotifications != null && !bpmProcdefNotifications.isEmpty()) {
            return null;
        }

        //Thử tìm xem có cấu hình cho từng bước cụ thể không
        bpmProcdefNotifications = bpmProcdefNotificationList.stream().collect(Collectors.filtering(s ->
                s.getActionCode() != null
                        && s.getActionCode().equalsIgnoreCase(actionCode)
                        && s.getTaskDefKeyNotification() != null
                        && !s.getOffNotification()
                        && s.getAddMoreConfig()
                        && s.getTaskDefKeyNotification().equalsIgnoreCase(nextTaskDefKey), Collectors.toList()));
        if (bpmProcdefNotifications == null || bpmProcdefNotifications.isEmpty()) {//Nếu không có cấu hình cho bước cụ thể tìm lại theo action trung của quy trình
            bpmProcdefNotifications = bpmProcdefNotificationList.stream().collect(Collectors.filtering(s ->
                    s.getActionCode() != null
                            && !s.getOffNotification()
                            && !s.getAddMoreConfig()
                            && s.getActionCode().equalsIgnoreCase(actionCode), Collectors.toList()));
            if (bpmProcdefNotifications == null || bpmProcdefNotifications.isEmpty()) {//Nếu không có cấu hình trung của quy trình thử tìm cấu hình trung của hệ thống
                if (bpmProcdefNotifications == null) {
                    bpmProcdefNotifications = new ArrayList<>();
                }
                //Lấy thông báo trung toàn hệ thống theo action
                List<NotificationTemplate> notificationTemplates = notificationTemplateRepository.findNotificationTemplatesByActionCodeAndType(actionCode, "SYSTEM");
                if (notificationTemplates != null && !notificationTemplates.isEmpty()) {//Có cấu hình thông báo trung toàn hệ thống
                    for (NotificationTemplate notificationTemplate : notificationTemplates) {
                        if (notificationTemplate != null) {
                            bpmProcdefNotifications.add(BpmProcdefNotification.builder()
                                    .actionCode(notificationTemplate.getActionCode())
                                    .taskDefKey(nextTaskDefKey)
                                    .addMoreConfig(false)
                                    .notificationTemplateId(notificationTemplate.getId())
                                    .notificationObject(StringUtil.nvl(notificationTemplate.getNotificationObject(), ProcInstConstants.NotificationsObject.ALL.code))
                                    .build());
                        }
                    }
                }
            }
        }

        //Ưu tiên các đối tượng không phải all trước
        if (bpmProcdefNotifications != null && bpmProcdefNotifications.size() > 0) {
            bpmProcdefNotifications = bpmProcdefNotifications.stream().map(bpmProcdefNotification -> {
                if (ValidationUtils.isNullOrEmpty(bpmProcdefNotification.getNotificationObject())) {
                    bpmProcdefNotification.setNotificationObject(ProcInstConstants.NotificationsObject.ALL.code);
                }
                return bpmProcdefNotification;
            }).collect(Collectors.toList());

            Map<Boolean, List<BpmProcdefNotification>> evenOddMap = bpmProcdefNotifications.stream().collect(Collectors.partitioningBy(item -> (item.getNotificationObject().equalsIgnoreCase(ProcInstConstants.NotificationsObject.ALL.code))));
            List<BpmProcdefNotification> notificationAllObject = evenOddMap.get(true);//All
            List<BpmProcdefNotification> notificationObjects = evenOddMap.get(false);//Đối tượng cụ thể
            List<BpmProcdefNotification> bpmProcdefNotificationsFinal = new ArrayList<>();
//            if(notificationObjects != null && notificationObjects.size() == 0){
//                BpmProcdefNotification test = BpmProcdefNotification.builder()
//                        .actionCode(notificationAllObject.get(0).getActionCode())
//                        .taskDefKey(nextTaskDefKey)
//                        .addMoreConfig(false)
//                        .notificationTemplateId(notificationAllObject.get(0).getNotificationTemplateId())
//                        .notificationObject(ProcInstConstants.NotificationsObject.USER_NOTIFICATION.code).build();
//                notificationObjects.add(test);
//            }
            bpmProcdefNotificationsFinal.addAll(notificationObjects);
            bpmProcdefNotificationsFinal.addAll(notificationAllObject);
            return bpmProcdefNotificationsFinal;
        }
        return bpmProcdefNotifications;
    }

    //todo: Lấy thông tin biến được gán giá trị
    private Map<String, VariableValueDto> addDefaultVariables(BpmProcInst currentTicket, Map<String,
            VariableValueDto> variables, String nextTaskDefKey, String emailExe) {
        try {
            //Lấy theo task
            VariableValueDto variableDto = null;
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
            if (currentTicket != null) {
                List<BpmTask> bpmTasks = bpmTaskRepository.getBpmTaskByTaskProcInstId(currentTicket.getTicketProcInstId());
                if (bpmTasks != null && bpmTasks.size() > 0) {
                    for (BpmTask itemTask : bpmTasks) {
                        //Tên bước
                        variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(itemTask.getTaskName());
                        variables.put(itemTask.getTaskDefKey() + "_txt_tenBuoc", variableDto);
                        //Thời gian tạo bước
                        variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(itemTask.getTaskCreatedTime() != null ? formatter.format(itemTask.getTaskCreatedTime()) : "");
                        variables.put(itemTask.getTaskDefKey() + "_dtm_thoiGianTaoBuoc", variableDto);
                        //Người thực hiện bước
                        variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(itemTask.getTaskAssignee());
                        variables.put(itemTask.getTaskDefKey() + "_txt_nguoiThucHien", variableDto);
                        //Người thực hiện bước fullname + title
                        if (!ValidationUtils.isNullOrEmpty(itemTask.getTaskAssignee())) {
                            List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(itemTask.getTaskAssignee());
                            if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
                                String userTitle = lstUserTitle.stream()
                                        .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                                        .map(title -> {
                                            String strTitle = StringUtil.nvl(title.getTitle(), "");
                                            int concurrently = title.getConcurrently();
                                            return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                                        })
                                        .collect(Collectors.joining(" "));
                                String fullName = lstUserTitle.get(0).getFullName();
                                // fullName
                                variableDto = new VariableValueDto();
                                variableDto.setType("String");
                                variableDto.setValue(StringUtil.nvl(fullName, ""));
                                variables.put(itemTask.getTaskDefKey() + "_txt_nguoiThucHien_name", variableDto);
                                // title
                                variableDto = new VariableValueDto();
                                variableDto.setType("String");
                                variableDto.setValue(StringUtil.nvl(userTitle, ""));
                                variables.put(itemTask.getTaskDefKey() + "_txt_nguoiThucHien_title", variableDto);
                            }
                        }
                        //Trạng thái của bước
                        variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(itemTask.getTaskStatus());
                        variables.put(itemTask.getTaskDefKey() + "_txt_trangThai", variableDto);
                    }
                }
                // Người thực hiện
                variableDto = new VariableValueDto();
                variableDto.setType("String");
                variableDto.setValue(emailExe);
                variables.put("txt_nguoiThucHien", variableDto);
                // người thực hiện fullname + title
                if (!ValidationUtils.isNullOrEmpty(emailExe)) {
                    List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(emailExe);
                    if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
                        String userTitle = lstUserTitle.stream()
                                .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                                .map(title -> {
                                    String strTitle = StringUtil.nvl(title.getTitle(), "");
                                    int concurrently = title.getConcurrently();
                                    return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                                })
                                .collect(Collectors.joining(" "));
                        String fullName = lstUserTitle.get(0).getFullName();
                        // fullName
                        variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(StringUtil.nvl(fullName, ""));
                        variables.put("txt_nguoiThucHien_name", variableDto);
                        // title
                        variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(StringUtil.nvl(userTitle, ""));
                        variables.put("txt_nguoiThucHien_title", variableDto);
                    }
                }
                //Mã phiếu
                variableDto = new VariableValueDto();
                variableDto.setType("String");
                variableDto.setValue(currentTicket.getTicketId());
                variables.put("txt_maPhieu", variableDto);
                //Mã tờ trình
                variableDto = new VariableValueDto();
                variableDto.setType("String");
                variableDto.setValue(currentTicket.getRequestCode());
                variables.put("txt_maToTrinh", variableDto);
                //Mã quy trình
                variableDto = new VariableValueDto();
                variableDto.setType("String");
                variableDto.setValue(currentTicket.getTicketProcDefId());
                variables.put("ticketProcDefId", variableDto);
                //Mã id dịch vụ
                variableDto = new VariableValueDto();
                variableDto.setType("String");
                variableDto.setValue(currentTicket.getServiceId());
                variables.put("serviceId", variableDto);
                //nextTaskDefKey
                variableDto = new VariableValueDto();
                variableDto.setType("String");
                variableDto.setValue(nextTaskDefKey);
                variables.put("nextTaskDefKey", variableDto);
                //Người tạo
                variableDto = new VariableValueDto();
                variableDto.setType("String");
                variableDto.setValue(currentTicket.getCreatedUser());
                variables.put("createdUser", variableDto);
                // Người tạo fullname + title
                if (!ValidationUtils.isNullOrEmpty(currentTicket.getCreatedUser())) {
                    List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(currentTicket.getCreatedUser());
                    if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
                        String userTitle = lstUserTitle.stream()
                                .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                                .map(title -> {
                                    String strTitle = StringUtil.nvl(title.getTitle(), "");
                                    int concurrently = title.getConcurrently();
                                    return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                                })
                                .collect(Collectors.joining(" "));
                        String fullName = lstUserTitle.get(0).getFullName();
                        // fullName
                        variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(StringUtil.nvl(fullName, ""));
                        variables.put("createdUser_name", variableDto);
                        // title
                        variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(StringUtil.nvl(userTitle, ""));
                        variables.put("createdUser_title", variableDto);
                    }
                }
                //Thời gian tạo
                variableDto = new VariableValueDto();
                variableDto.setType("String");
                variableDto.setValue(currentTicket.getTicketCreatedTime() != null ? formatter.format(currentTicket.getTicketCreatedTime()) : "");
                variables.put("ticketCreatedTime", variableDto);
            }
            //Ngày giờ hiện tại
            variableDto = new VariableValueDto();
            variableDto.setType("String");
            variableDto.setValue(dateFormat.format(new Date()));
            variables.put("dtm_ngayGioHienTai", variableDto);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return variables;
    }

    private String replaceContentText(String contentText, Map<String, VariableValueDto> mVariable, boolean isReplacelink) {
        try {
            String regex = "\\@\\{\\{(.*?)\\}\\}";
            Pattern pattern = Pattern.compile(regex);
            Matcher m = pattern.matcher(contentText);
            List<String> lstVariableIgnore = Arrays.asList("fullName", "recipientTitle");
            while (m.find()) {
                String textReplace = StringUtil.nvl(m.group(1), "");
                if (!ValidationUtils.isNullOrEmpty(textReplace) && !lstVariableIgnore.contains(textReplace)) {
                    contentText = contentText.replaceAll("\\@\\{\\{" + textReplace + "\\}\\}",
                            StringUtil.nvl(findObjectInMapVariable(mVariable, textReplace, true), ""));
                }
            }
            //Replace link động
            if (isReplacelink && contentText.contains("href")) {
                Document doc = Jsoup.parse(contentText);
                List<Element> elements = doc.getElementsByAttribute("href");
                for (Element element : elements) {
                    String value = StringUtil.nvl(element.attr("href"), "");
                    String[] splitValue = value.split("\\?");
                    if(splitValue.length>1){
                        value = splitValue[0];
                    }
                    if (!value.endsWith("/")) {
                        value = value + "/";
                    }
                    System.out.println("Jsoup.parse link=" + value);
                    regex = "\\@(.*?)/";
                    pattern = Pattern.compile(regex);
                    m = pattern.matcher(value);
                    while (m.find()) {
                        String textReplace = StringUtil.nvl(m.group(1), "");
                        if (!ValidationUtils.isNullOrEmpty(textReplace)) {
                            contentText = contentText.replaceAll("\\@" + textReplace,
                                    StringUtil.nvl(findObjectInMapVariable(mVariable, textReplace, true), ""));
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return contentText;
        }
        return contentText;
    }

    private String convertArrStringToString(String newValue) {
        try {
            if (newValue != null && newValue.indexOf("[") > -1) {
                System.out.println(newValue);
                return StringUtil.nvl(newValue, "").replaceAll("\\[", "").replaceAll("\\]", "").replaceAll("\"", "").replaceAll(",", ", ");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return newValue;
        }
        return newValue;
    }

    private Object findObjectInMapVariable(Map<String, VariableValueDto> mVariable, String value, Boolean arrStringToString) {
        var ref = new Object() {
            Object newValue = null;
            String[] lstValue = new String[0];
        };
        //Bắt đầu @test_select mới tìm trong lstVariable
        if (!ValidationUtils.isNullOrEmpty(value)) {
            if (value.indexOf(".") > -1) {//Trường hợp lấy giá trị trong matrix @mtx_matrix1.txt_text2
                ref.lstValue = value.split("\\.");
                value = ref.lstValue[0];
            }
            if (mVariable != null) {
                try {
                    String finalValue = value;
                    //Thử tìm key
                    Set<String> lstProperties = mVariable.keySet();
                    String keyMatch = lstProperties.stream().filter(s -> s.equalsIgnoreCase(finalValue)).findFirst().orElse(null);
                    if (keyMatch != null) {
                        //Các biến thông thường replace lại nếu là dạng chuổi mảng string
                        if (mVariable.get(keyMatch) != null) {
                            ref.newValue = mVariable.get(keyMatch).getValue();
                            try {
                                if (arrStringToString && ref.newValue != null && mVariable.get(keyMatch).getType() != null
                                        && mVariable.get(keyMatch).getType().equalsIgnoreCase("String")) {
                                    System.out.println(ref.newValue);
                                    ref.newValue = convertArrStringToString(StringUtil.nvl(ref.newValue, ""));
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                        //Matrix thì cần xử lý tiếp type của matrix, table luôn là Json
                        if (ref.newValue != null && ref.lstValue.length >= 2) {
                            Map<String, Object> objectMap = ObjectUtils.toObject((String) ref.newValue, new TypeReference<>() {
                            });
                            if (objectMap != null) {
                                Map<String, Object> data = (Map<String, Object>) objectMap.get("data");
                                if (data != null) {
                                    List<Map<String, Object>> lstData = (List<Map<String, Object>>) data.get("data");
                                    if (lstData != null && lstData.size() > 0) {//Dữ liệu của matrix luôn chị lưu ở vị trí 0
                                        ref.newValue = lstData.get(0).get(ref.lstValue[1]);
                                        if (arrStringToString && (ref.newValue instanceof List || ref.newValue instanceof String)) {
                                            ref.newValue = convertArrStringToString(StringUtil.nvl(ref.newValue, ""));
                                        }
                                    }
                                }
                            }

                        }
                        //trả về luôn
                        return ref.newValue;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    throw e;
                }
            }
        }
        return ref.newValue;
    }

    private Map<String, AccountModel> getUserAccountMap(List<String> usernames) {
        return customerService.getAccountMapByUsernames(usernames);
    }

    private BpmNotifyUser getBpmNotifyUser(Long ticketId, String recipientEmailItem, String title, String contentNotification, String typeSend, String email, String address, String notificationObject) {
        return BpmNotifyUser.builder()
                .ticketId(ticketId)
                .recipient(recipientEmailItem)
                .title(title)
                .message(contentNotification)
                .type(typeSend)
                .status("1")
                .createdUser(email)
                .createdTime(LocalDateTime.now())
                .address(address)
                .notificationObject(notificationObject)
                .build();
    }

    private String getAddress(@NonNull Map<String, AccountModel> userAccountMap, @NonNull String username, @NonNull String typeSend) {
        AccountModel account = userAccountMap.get(username);
        if (typeSend.equalsIgnoreCase("MAIL")) {
            return account != null ? account.getEmail() : username;
        }

        return username;
    }

    @Override
    public List<BpmNotifyUser> addNotification(Long ticketId, String objectNotification, List<String> lstRecipient, String actionCode, Map<String, VariableValueDto> variables, List<String> typeSend, String nextTaskDefKey)  {
        if (variables == null) {
            variables = new HashMap<>();
        }
        String emailExe = credentialHelper.getJWTPayload().getUsername();
        List<BpmNotifyUser> bpmNotifyUsers = new ArrayList<>();
        BpmProcInst currentTicket;
        if (ticketId != null && !Objects.equals(ticketId, 0L)) {
            currentTicket = bpmProcInstRepository.getBpmProcInstByTicketId(ticketId);
            //Đã có ticket thì lấy all biến đã lưu theo ProcInstId
            if (currentTicket != null) {
                Map<String, VariableValueDto> variableValueDtoMap = actHiVarInstManager.getVariByTicketAndConvertToMap(currentTicket.getTicketProcInstId());
                if (variableValueDtoMap != null) {
                    variables.putAll(variableValueDtoMap);
                }
            }
        } else {
            currentTicket = null;
        }
        addDefaultVariables(currentTicket, variables, nextTaskDefKey, emailExe);
        List<NotificationTemplate> notificationTemplates = notificationTemplateRepository.findNotificationTemplatesByActionCodeAndTypeAndNotificationObject(actionCode, "SYSTEM", objectNotification);
        if (ValidationUtils.isNullOrEmpty(notificationTemplates)) {
            return null;
        }
        List<NotificationTemplateDetail> notificationTemplateDetails = notificationTemplateDetailRepository.getTemplateDetailFromTemplateIdAndType(notificationTemplates.get(0).getId(), typeSend);
        for (NotificationTemplateDetail notificationTemplateDetail : notificationTemplateDetails) {
            //Lấy nội dung thông báo
            String title = null;
            if (ValidationUtils.isNullOrEmpty(notificationTemplateDetail.getTitle())) {
                for (Notifications e : Notifications.values()) {
                    if (actionCode.equalsIgnoreCase(e.code)) {
                        title = "[" + e.name + "]";
                    }
                }
            }
            String contentNotification = StringUtil.nvl(notificationTemplateDetail.getContent(), "This is the default content of the notification!");
            //Kiểm tra xem title thông báo có cấu hình động không
            if (!ValidationUtils.isNullOrEmpty(notificationTemplateDetail.getTitle())) {
                title = replaceContentText(notificationTemplateDetail.getTitle(), variables, true);
                log.info("title afer replace =" + title);
            }

            //Kiểm tra xem nội dung thông báo có cấu hình động không
            if (!ValidationUtils.isNullOrEmpty(notificationTemplateDetail.getContent())) {
                contentNotification = replaceContentText(contentNotification, variables, true);
                log.info("ContentNotification after replace =" + contentNotification);
            }
            Map<String, AccountModel> userAccountMap = getUserAccountMap(lstRecipient);

            for (String e : lstRecipient) {
                String recipient = e.toLowerCase();
                AccountModel account = userAccountMap.get(e);
                if (contentNotification.contains("@{{fullName}}")) {
                    String fullName = "";
                    if (account != null) {
                        fullName = StringUtil.nvl(account.getLastname(), "") + " " + StringUtil.nvl(account.getFirstname(), "");
                    } else {
                        fullName = e;
                    }
                    contentNotification = contentNotification.replaceAll("\\@\\{\\{fullName\\}\\}", fullName.trim());

                }
//                 replace dear userTitle
                if (contentNotification.contains("@{{recipientTitle}}")) {
                    String finalTitle = "";
                    if (account != null) {
                        finalTitle = StringUtil.nvl(account.getFinalTitle(), "");
                    }
                    contentNotification = contentNotification.replaceAll("\\@\\{\\{recipientTitle\\}\\}", finalTitle.trim());
                }
                UserInfoDto userInfoDto = customerService.getEmailByUser(recipient);
                String address = userInfoDto.getEmail();
                BpmNotifyUser bpmNotifyUser = getBpmNotifyUser(ticketId, recipient, title, contentNotification, notificationTemplateDetail.getType(), emailExe, address, objectNotification);
                List<Map<String, Object>> chartObj = bpmService.findCompanyCodeByUsername(recipient);
                bpmNotifyUser.setCompanyCode(chartObj.isEmpty() ? null : chartObj.get(0).get("companyCode").toString());
                bpmNotifyUsers.add(bpmNotifyUser);
            }
            bpmNotifyUserRepository.saveAll(bpmNotifyUsers);
        }

        // push noti
        notificationService.pushNotification(bpmNotifyUsers);

        return bpmNotifyUsers;
    }
}
