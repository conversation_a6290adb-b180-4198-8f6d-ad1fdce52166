package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProInstNotifyUser;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;


import java.util.List;


@Repository
public interface BpmProcInstNotifyUserRepository extends JpaRepository<BpmProInstNotifyUser, Long>, JpaSpecificationExecutor<PriorityManagement> {

    @Query("SELECT a from BpmProInstNotifyUser  a where a.bpmProcinstId = :ticketId")
    List<BpmProInstNotifyUser> getBpmProInstNotifyUsersBy(Long ticketId);

    @Query("SELECT a.recipient from BpmProInstNotifyUser  a where a.bpmProcinstId = :ticketId")
    List<String> getLstUsersByTicketId(Long ticketId);

    List<BpmProInstNotifyUser> findByBpmProcinstId(Long bpmProcinstId);

    @Modifying(clearAutomatically = true, flushAutomatically = true)
    void deleteAllByBpmProcinstId(@Param("bpmProcInstId") Long bpmProcInstId);
}
