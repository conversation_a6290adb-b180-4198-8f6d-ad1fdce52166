package vn.fis.eapprove.business.domain.assign.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import vn.fis.eapprove.business.domain.changeAssignee.entity.ChangeAssigneeHistory;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Data
@Entity
@Table(name = "assign_management")
public class AssignManagement implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "assign_name")
    private String assignName;

    @Column(name = "assign_user")
    private String assignUser;

    @Column(name = "assigned_user")
    private String assignedUser;

    @Column(name = "ticket_id")
    private Long ticketId;

    @Column(name = "start_date")
    private LocalDate startDate;

    @Column(name = "end_date")
    private LocalDate endDate;

    @Column(name = "service_range")
    private String serviceRange;

    @Column(name = "status")
    private Integer status;

    @Column(name = "effect")
    private Integer effect;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "updated_date")
    private LocalDateTime updatedDate;

    @Column(name = "updated_user")
    private String updatedUser;

    @Column(name = "description")
    private String description;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "new_ticket")
    private String newTicket;

    @Column(name = "type")
    private Integer type;

    @Column(name = "assign_title")
    private String assignTitle;

    @Column(name = "assign_duty")
    private String assignDuty;

    @Column(name = "assign_company_name")
    private String assignCompanyName;

    @Column(name = "assign_decision")
    private String assignDecision;

    @Column(name = "assigned_title")
    private String assignedTitle;

    @Column(name = "assign_storage")
    private String assignStorage;

    @Column(name = "service_id")
    private Long serviceId;

    @Column(name = "authority_conditions")
    private String authorityConditions;

    @Column(name = "request_code")
    private String requestCode;

    @Column(name = "new_request_code")
    private String newRequestCode;

    @Column(name = "history_status")
    private Integer historyStatus;

    @OneToMany(mappedBy = "assignManagement")
    @JsonIgnore
    private Set<ChangeAssigneeHistory> changeAssigneeHistories;

    @OneToMany(mappedBy = "assignManagement")
    @JsonIgnore
    private Set<PermissionDataManagement> permissionDataManagements;

    @Transient
    private List<Object> conditionList;
    @Column(name = "company_code")
    private String companyCode;
    @Column(name = "company_name")
    private String companyName;
}
