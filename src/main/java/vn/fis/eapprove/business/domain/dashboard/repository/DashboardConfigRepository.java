package vn.fis.eapprove.business.domain.dashboard.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.dashboard.entity.DashboardConfig;

import java.util.List;

@Repository
public interface DashboardConfigRepository extends JpaRepository<DashboardConfig, Long> {

    @Query("select dc from DashboardConfig dc where dc.username = :username")
    List<DashboardConfig> getDashboardConfigByUsername(String username);

    DashboardConfig getDashboardConfigById(Long id);
}
