package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "bpm_tp_sign_zone")
public class BpmTpSignZone {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "BPM_TEMPLATE_PRINT_ID")
    private Long bpmTemplatePrintId;

    @Column(name = "SIGN")
    private String sign;

    @Column(name = "TASK_DEF_KEY")
    private String taskDefKey;

    @Column(name = "PROC_INST_ID")
    private String procInstId;

    @Column(name = "ORDER_SIGN")
    private Long orderSign;

    @Column(name = "EMAIL")
    private String email;

    @Column(name = "FIRST_NAME")
    private String firstName;

    @Column(name = "LAST_NAME")
    private String lastName;

    @Column(name = "POSITION")
    private String position;

    @Column(name = "SIGN_PAGE")
    private Long page;

    @Column(name = "X")
    private Long x;

    @Column(name = "SCALE")
    private Float scale;

    @Column(name = "Y")
    private Long y;

    @Column(name = "W")
    private Long w;

    @Column(name = "H")
    private Long h;

    @Column(name = "CREATED_USER")
    private String createdUser;

    @Column(name = "CREATED_DATE")
    private Date createdDate;

    @Column(name = "UPDATED_USER")
    private String updatedUser;

    @Column(name = "UPDATED_DATE")
    private Date updatedDate;

    @Column(name = "COMMENT")
    private String comment;

    @Column(name = "SIGNED_FILE")
    private String signedFile;

    @Column(name = "SIGNED_DATE")
    private Date signedDate;

    @Column(name = "SIGN_TYPE")
    private String signType;

    @Column(name = "ORG_ASSIGNEE_TITLE")
    private String orgAssigneeTitle;

    @Column(name = "CHART_NODE_LEVEL")
    private String chartNodeLevel;
}
