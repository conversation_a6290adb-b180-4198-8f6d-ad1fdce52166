package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.*;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "bpm_notify_user")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BpmNotifyUser {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    @Column(name = "ticket_id")
    private Long ticketId;

    @Column(name = "recipient")
    private String recipient;

    @Column(name = "address")
    private String address;

    @Column(name = "title")
    private String title;

    @Column(name = "message")
    private String message;

    @Column(name = "type")
    private String type;

    @Column(name = "status")
    private String status;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "updated_user")
    private String updatedUser;

    @Transient
    private String notificationObject;

    @Column(name = "company_code")
    private String companyCode;
}
