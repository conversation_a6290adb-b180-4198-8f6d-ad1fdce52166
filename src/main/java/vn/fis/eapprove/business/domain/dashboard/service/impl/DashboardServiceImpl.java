package vn.fis.eapprove.business.domain.dashboard.service.impl;

import jakarta.persistence.Tuple;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.domain.dashboard.entity.DashboardConfig;
import vn.fis.eapprove.business.domain.dashboard.repository.DashboardConfigRepository;
import vn.fis.eapprove.business.domain.dashboard.repository.DashboardTaskRepository;
import vn.fis.eapprove.business.domain.dashboard.repository.DashboardTicketRepository;
import vn.fis.eapprove.business.domain.dashboard.service.DashboardService;
import vn.fis.eapprove.business.model.request.DashboardConfigRequest;
import vn.fis.eapprove.business.model.request.DashboardRequest;
import vn.fis.eapprove.business.model.response.DashboardConfigResponse;
import vn.fis.eapprove.business.model.response.DashboardResponse;
import vn.fis.eapprove.business.model.response.DashboardTotalResponse;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.constants.ProcInstConstants;
import vn.fis.spro.common.constants.TaskConstants;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
@Slf4j
@AllArgsConstructor
public class DashboardServiceImpl implements DashboardService {

    private final CredentialHelper credentialHelper;
    private CustomerService customerService;
    private DashboardTaskRepository dashboardTaskRepo;
    private DashboardTicketRepository dashboardTicketRepo;
    private DashboardConfigRepository dashboardConfigRepo;

    @Override
    public List<DashboardResponse> getChartBarEmployee(DashboardRequest filter) {
        List<DashboardResponse> lstResponse = new ArrayList<>();
        String username = credentialHelper.getJWTPayload().getUsername();
        Set<String> users = new HashSet<>(customerService.getUserDefault(username, null));
        filter.getDefaultUser().addAll(users);

        if (ValidationUtils.isNullOrEmpty(filter.getListChartNodeId())) {
            List<Long> chartNodes = customerService.getChartNodeIdsByUsernames(filter.getDefaultUser());
            filter.setListChartNodeId(chartNodes);
        }

        if (!ValidationUtils.isNullOrEmpty(filter.getToDate())) {
            LocalDate date = LocalDate.parse(filter.getToDate());
            String newDate = date.plusDays(1).toString();
            filter.setToDate(newDate);
        }

        List<Tuple> lstTuple = dashboardTaskRepo.getChartBarEmployee(filter.getChartId(), filter.getListChartNodeId(),
                 filter.getDefaultUser(), filter.getFromDate(), filter.getToDate(), filter.getTaskType());

        if (!ValidationUtils.isNullOrEmpty(lstTuple)) {
            lstResponse = toChartBarDashBoardResponse(lstTuple);
        }

        return lstResponse;
    }

    private List<DashboardResponse> toChartBarDashBoardResponse(List<Tuple> lstTuple) {
        List<DashboardResponse> lstResponse = new ArrayList<>();
        for (Tuple tuple : lstTuple) {
            DashboardResponse response = new DashboardResponse();
            response.setUsername(tuple.get("username") != null ? tuple.get("username").toString() : null);
            response.setStaffCode(tuple.get("staffCode") != null ? tuple.get("staffCode").toString() : null);
            response.setFullName(tuple.get("fullName") != null ? tuple.get("fullName").toString() : null);
            response.setValue(Integer.valueOf(tuple.get("approvingNotDelayed").toString()));
            response.setOtherValue(Integer.valueOf(tuple.get("approvingDelayed").toString()));
            lstResponse.add(response);
        }
        return lstResponse;
    }

    @Override
    public List<DashboardResponse> getChartPieStatus(DashboardRequest request) {
        List<DashboardResponse> lstResponse = new ArrayList<>();
        String username = credentialHelper.getJWTPayload().getUsername();
        if (!ValidationUtils.isNullOrEmpty(request.getToDate())) {
            LocalDate date = LocalDate.parse(request.getToDate());
            String newDate = date.plusDays(1).toString();
            request.setToDate(newDate);
        }

        List<Tuple> lstTuple = dashboardTicketRepo.getChartPieStatus(username, request.getFromDate(), request.getToDate());
        Integer total = dashboardTicketRepo.countDashBoardTicket(username, request.getFromDate(), request.getToDate());
        if (!ValidationUtils.isNullOrEmpty(lstTuple)) {
            lstResponse = toChartPieDashBoardResponse(lstTuple, total);
        }
        return lstResponse;
    }

    private List<DashboardResponse> toChartPieDashBoardResponse(List<Tuple> lstTuple, Integer total) {
        List<DashboardResponse> lstResponse = new ArrayList<>();
        int countProcessing = 0;
        for (Tuple tuple : lstTuple) {
            if (tuple.get("ticketStatus").toString().equals(ProcInstConstants.Status.PROCESSING.code)
                    || tuple.get("ticketStatus").toString().equals(ProcInstConstants.Status.OPENED.code)) {
                countProcessing = countProcessing + Integer.parseInt(tuple.get("count").toString());
            } else {
                DashboardResponse response = new DashboardResponse();
                response.setName(tuple.get("ticketStatus").toString());
                response.setValue(Integer.valueOf(tuple.get("count").toString()));
                response.setPercentage(String.format("%d%%", Math.round(((response.getValue() * 100.0) / total))));
                lstResponse.add(response);
            }
        }
        DashboardResponse response = new DashboardResponse();
        response.setName(ProcInstConstants.Status.PROCESSING.code);
        response.setValue(countProcessing);
        response.setPercentage(String.format("%d%%", Math.round(((response.getValue() * 100.0) / total))));
        lstResponse.add(response);
        return lstResponse;
    }

    @Override
    public List<DashboardResponse> getChartPiePriority(DashboardRequest request) {
        List<DashboardResponse> lstResponse = new ArrayList<>();
        String username = credentialHelper.getJWTPayload().getUsername();
        if (!ValidationUtils.isNullOrEmpty(request.getToDate())) {
            LocalDate date = LocalDate.parse(request.getToDate());
            String newDate = date.plusDays(1).toString();
            request.setToDate(newDate);
        }

        List<Tuple> lstTuple = dashboardTaskRepo.getChartPiePriority(username, request.getFromDate(), request.getToDate(), request.getTaskType());
        if (!ValidationUtils.isNullOrEmpty(lstTuple)) {
            Integer total = lstTuple.stream().mapToInt(e -> Integer.parseInt(e.get("count").toString())).sum();
            lstResponse = toChartPiePriorityResponse(lstTuple, total);
        }

        return lstResponse;
    }

    private List<DashboardResponse> toChartPiePriorityResponse(List<Tuple> lstTuple, Integer total) {
        List<DashboardResponse> lstResponse = new ArrayList<>();
        for (Tuple tuple : lstTuple) {
            DashboardResponse response = new DashboardResponse();
            response.setName(tuple.get("priority_name").toString());
            response.setValue(Integer.valueOf(tuple.get("count").toString()));
            response.setPercentage(String.format("%d%%", Math.round(((response.getValue() * 100.0) / total))));
            lstResponse.add(response);
        }
        return lstResponse;
    }

    @Override
    public DashboardTotalResponse getTableEmployeeWorkload(DashboardRequest request) {
        DashboardTotalResponse response = new DashboardTotalResponse();
        List<DashboardResponse> lstResponse = new ArrayList<>();
        String username = credentialHelper.getJWTPayload().getUsername();
        Set<String> users = new HashSet<>(customerService.getUserDefault(username, null));
        request.getDefaultUser().addAll(users);

        if (ValidationUtils.isNullOrEmpty(request.getListChartNodeId())) {
            List<Long> chartNodes = customerService.getChartNodeIdsByUsernames(request.getDefaultUser());
            request.setListChartNodeId(chartNodes);
        }

        if (!ValidationUtils.isNullOrEmpty(request.getToDate())) {
            LocalDate date = LocalDate.parse(request.getToDate());
            String newDate = date.plusDays(1).toString();
            request.setToDate(newDate);
        }

        List<Tuple> lstTuple = dashboardTaskRepo.getTableEmployeeWorkload(request.getChartId(), request.getListChartNodeId(),
                request.getDefaultUser(), request.getFromDate(), request.getToDate(), request.getTaskType());
        if (!ValidationUtils.isNullOrEmpty(lstTuple)) {
            lstResponse = toTableEmployeeWorkloadResponse(lstTuple);
            int total = lstResponse.stream().mapToInt(DashboardResponse::getTotal).sum();
            response.setTotal(total);
        }

        response.setData(lstResponse);
        return response;
    }

    private List<DashboardResponse> toTableEmployeeWorkloadResponse(List<Tuple> lstTuple) {
        List<DashboardResponse> lstResponse = new ArrayList<>();
        for (Tuple tuple : lstTuple) {
            DashboardResponse response = new DashboardResponse();
            response.setUsername(tuple.get("username") != null ? tuple.get("username").toString() : null);
            response.setStaffCode(tuple.get("staffCode") != null ? tuple.get("staffCode").toString() : null);
            response.setFullName(tuple.get("fullName") != null ? tuple.get("fullName").toString() : null);
            response.setValue(Integer.valueOf(tuple.get("processing").toString()));
            response.setOtherValue(Integer.valueOf(tuple.get("otherStatus").toString()));
            int total = response.getValue() + response.getOtherValue();
            double percentage = (response.getValue() * 100.0) / total;
            response.setTotal(total);
            response.setPercentage(String.format("%d%%", Math.round(percentage)));
            lstResponse.add(response);
        }
        return lstResponse;
    }

    @Override
    public DashboardTotalResponse getTableProposalStatus(DashboardRequest request) {
        DashboardTotalResponse response = new DashboardTotalResponse();
        List<DashboardResponse> lstDashboard = new ArrayList<>();

        String username = credentialHelper.getJWTPayload().getUsername();
        Set<String> users = new HashSet<>(customerService.getUserDefault(username, null));
        request.getDefaultUser().addAll(users);
        if (ValidationUtils.isNullOrEmpty(request.getListChartNodeId())) {
            List<Long> chartNodes = customerService.getChartNodeIdsByUsernames(request.getDefaultUser());
            request.setListChartNodeId(chartNodes);
        }
        if (!ValidationUtils.isNullOrEmpty(request.getToDate())) {
            LocalDate date = LocalDate.parse(request.getToDate());
            String newDate = date.plusDays(1).toString();
            request.setToDate(newDate);
        }
        List<String> lstTaskStatus = new ArrayList<>();
        if (ValidationUtils.isNullOrEmpty(request.getStatus())
                || TaskConstants.Status.ACTIVE.code.equals(request.getStatus())
                || TaskConstants.Status.PROCESSING.code.equals(request.getStatus())) {
            lstTaskStatus.add(TaskConstants.Status.ACTIVE.code);
            lstTaskStatus.add(TaskConstants.Status.PROCESSING.code);
        } else {
            lstTaskStatus.add(request.getStatus());
        }

        List<Tuple> lstTuple = dashboardTaskRepo.getTableProposalStatus(request.getChartId(), request.getListChartNodeId(), request.getDefaultUser(),
                request.getFromDate(), request.getToDate(), lstTaskStatus, request.getTaskType());
        if (!ValidationUtils.isNullOrEmpty(lstTuple)) {
            lstDashboard = toTableProposalStatusResponse(lstTuple);
            int total = lstDashboard.stream().mapToInt(DashboardResponse::getTotal).sum();
            response.setTotal(total);
        }

        response.setData(lstDashboard);
        return response;
    }

    private List<DashboardResponse> toTableProposalStatusResponse(List<Tuple> lstTuple) {
        List<DashboardResponse> lstResponse = new ArrayList<>();
        for (Tuple tuple : lstTuple) {
            DashboardResponse response = new DashboardResponse();
            response.setName(tuple.get("serviceName") != null ? tuple.get("serviceName").toString() : null);
            response.setValue(Integer.valueOf(tuple.get("processing").toString()));
            response.setOtherValue(Integer.valueOf(tuple.get("otherStatus").toString()));
            int total = response.getValue() + response.getOtherValue();
            double percentage = (response.getValue() * 100.0) / total;
            response.setTotal(total);
            response.setPercentage(String.format("%d%%", Math.round(percentage)));
            lstResponse.add(response);
        }
        return lstResponse;
    }

    @Override
    public List<DashboardConfigResponse> getDashboardConfig(String username) {
        List<DashboardConfigResponse> lstResponse = new ArrayList<>();
        List<DashboardConfig> lstConfig = dashboardConfigRepo.getDashboardConfigByUsername(username);
        if (!ValidationUtils.isNullOrEmpty(lstConfig)) {
            for (DashboardConfig config : lstConfig) {
                DashboardConfigResponse response = new DashboardConfigResponse();
                response.setId(config.getId());
                response.setUsername(config.getUsername());
                response.setName(config.getName());
                response.setType(config.getType());
                response.setFilter(toDashboardResponse(config.getFilter()));
                response.setIsShow(config.getIsShow());
                response.setIsClone(config.getIsClone());
                lstResponse.add(response);
            }
        }

        return lstResponse;
    }
    private DashboardRequest toDashboardResponse(String filterStr) {
        return ObjectUtils.toObject(filterStr, DashboardRequest.class);
    }

    @Override
    public void createDashboardConfig(List<DashboardConfigRequest> lstRequest) {
        List<DashboardConfig> lstConfig = new ArrayList<>();
        for (DashboardConfigRequest request : lstRequest) {
            DashboardConfig config;
            if (request.getId() != null) {
                config = dashboardConfigRepo.getDashboardConfigById(request.getId());
                config.setUpdatedTime(LocalDateTime.now());
            } else {
                config = new DashboardConfig();
                config.setCreatedTime(LocalDateTime.now());
            }
            config.setName(request.getName());
            config.setUsername(request.getUsername());
            config.setType(request.getType());
            config.setIsShow(request.getIsShow());
            config.setFilter(ObjectUtils.toJson(request.getFilter()));
            config.setIsClone(request.getIsClone());
            lstConfig.add(config);
        }
        dashboardConfigRepo.saveAll(lstConfig);
    }
}
