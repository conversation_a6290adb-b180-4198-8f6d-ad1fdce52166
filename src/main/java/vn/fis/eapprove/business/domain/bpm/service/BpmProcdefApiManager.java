package vn.fis.eapprove.business.domain.bpm.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefApi;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcdefApiRepository;
import vn.fis.eapprove.business.dto.ActionApiDto;
import vn.fis.eapprove.business.dto.BpmProcdefApiDto;
import java.util.List;

@Service("BpmProcdefApiManagerV1")
@Slf4j
public class BpmProcdefApiManager {

    private final BpmProcdefApiRepository bpmProcdefApiRepository;

    @Autowired
    public BpmProcdefApiManager(BpmProcdefApiRepository bpmProcdefApiRepository) {
        this.bpmProcdefApiRepository = bpmProcdefApiRepository;
    }

    public List<BpmProcdefApi> listBpmProcdefApi(Long bpmProcdefId) {
        return bpmProcdefApiRepository.getAllByBpmProcdefId(bpmProcdefId);
    }

    public Boolean delete(BpmProcdefApiDto bpmProcdefApiDto) {
        try {
            bpmProcdefApiRepository.deleteById(bpmProcdefApiDto.getId());
            log.info("Delete success");
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    /**
     * Get proc-def-api info
     *
     * <AUTHOR>
     */
    public List<ActionApiDto> getBpmProcdefApiInfo(String procDefId, String taskDefKey, Long actionId) {
        return bpmProcdefApiRepository.getBpmProcdefApiInfo(procDefId, taskDefKey, actionId);
    }

    /**
     * Check process definition exists
     *
     * <AUTHOR>
     */
    public boolean isProcDefIdExists(String procDefId) {
        return bpmProcdefApiRepository.countByProcDefId(procDefId) > 0;
    }

    public void saveAll(List<BpmProcdefApi> bpmProcdefApiList) {
        bpmProcdefApiRepository.saveAll(bpmProcdefApiList);
    }
}
