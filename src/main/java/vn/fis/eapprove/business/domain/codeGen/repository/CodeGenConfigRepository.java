package vn.fis.eapprove.business.domain.codeGen.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.codeGen.entity.CodeGenConfig;


import java.util.List;

/**
 * Author: AnhVTN
 * Date: 30/03/2023
 */
@Repository
public interface CodeGenConfigRepository extends JpaRepository<CodeGenConfig, Long>, JpaSpecificationExecutor<CodeGenConfig> {
    CodeGenConfig findCodeGenConfigByCodeAndStatus(String code,String status);
    CodeGenConfig findCodeGenConfigByIdAndStatus(Long id,String status);

    Boolean existsCodeGenConfigByCode(String code);

    @Query("SELECT CASE WHEN COUNT(c) > 0 THEN true ELSE false END FROM CodeGenConfig c where c.code " +
            "in (Select c1.code from CodeGenConfig c1 where c1.id in (:ids) and c1.status = 'deactive') " +
            "and c.id not in (:ids) and c.status = 'active'")
    Boolean checkExistActiveByIds(List<Long> ids);

}
