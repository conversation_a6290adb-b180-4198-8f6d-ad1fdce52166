package vn.fis.eapprove.business.domain.system.service.impl;

import lombok.extern.slf4j.Slf4j;

import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.system.repository.SystemGroupHistoryRepository;
import vn.fis.eapprove.business.domain.system.service.SystemGroupHistoryService;

@Service
@Slf4j
@Transactional
public class SystemGroupHistoryServiceImpl implements SystemGroupHistoryService {

    private final SystemGroupHistoryRepository systemGroupHistoryRepository;

    public SystemGroupHistoryServiceImpl(SystemGroupHistoryRepository systemGroupHistoryRepository) {
        this.systemGroupHistoryRepository = systemGroupHistoryRepository;
    }

    @Override
    public ResponseEntity<Object> findDataByTableNameAndId(String tableName, Long id)  {
        return ResponseEntity.ok(systemGroupHistoryRepository.findAllByGroupTypeAndParentOrderByIdDesc(tableName,id));
    }
}
