package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.*;

import jakarta.persistence.*;

@Entity
@Table(name = "bpm_procdef_notification_detail")
@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BpmProcdefNotificationDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "bpm_procdef_notification_id")
    private Long bpmProcdefNotificationId;

    @Column(name = "notification_template_id")
    private Long notificationTemplateId;

    @Column(name = "action_code")
    private String actionCode;

//    @ManyToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "bpm_procdef_notification_id", updatable = false, insertable = false)
//    @JsonIgnore
//    private BpmProcdefNotification procdefNotification;
}
