package vn.fis.eapprove.business.domain.groupTable.entity;

import lombok.Data;

import jakarta.persistence.*;

@Data
@Entity
@Table(name = "group_table_procTemp")

public class GroupTableProTemp {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "pro_def_id")
    private String proDefId;

    @Column(name = "form_key")
    private String formKey;
}
