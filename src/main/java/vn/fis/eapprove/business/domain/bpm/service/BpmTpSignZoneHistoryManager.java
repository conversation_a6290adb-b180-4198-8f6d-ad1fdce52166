package vn.fis.eapprove.business.domain.bpm.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTpSignZone;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTpSignZoneHistory;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTpSignZoneHistoryRepository;

import java.util.List;
import java.util.stream.Collectors;

import static vn.fis.eapprove.business.constant.Constant.FAILED;
import static vn.fis.eapprove.business.constant.Constant.SUCCESS;

@Service("BpmTpSignZoneHistoryManagerV1")
@Slf4j
@Transactional
public class BpmTpSignZoneHistoryManager {

    @Autowired
    private BpmTpSignZoneHistoryRepository bpmTpSignZoneHistoryRepository;

    public int save(List<BpmTpSignZone> req) {
        try {
            List<BpmTpSignZoneHistory> list = req.stream().map(e -> {
                BpmTpSignZoneHistory bpmTpSignZoneHistory = new BpmTpSignZoneHistory();
                bpmTpSignZoneHistory.setBpmTemplatePrintId(e.getBpmTemplatePrintId());
                bpmTpSignZoneHistory.setX(e.getX());
                bpmTpSignZoneHistory.setH(e.getH());
                bpmTpSignZoneHistory.setY(e.getY());
                bpmTpSignZoneHistory.setW(e.getW());
                bpmTpSignZoneHistory.setBpmTpSignZoneId(e.getId());
                bpmTpSignZoneHistory.setOrderSign(e.getOrderSign());
                bpmTpSignZoneHistory.setPage(e.getPage());
                bpmTpSignZoneHistory.setSign(e.getSign());
                bpmTpSignZoneHistory.setTaskDefKey(e.getTaskDefKey());
                bpmTpSignZoneHistory.setProcInstId(e.getProcInstId());
                bpmTpSignZoneHistory.setEmail(e.getEmail());
                bpmTpSignZoneHistory.setCreatedDate(e.getCreatedDate());
                return bpmTpSignZoneHistory;

            }).collect(Collectors.toList());
            bpmTpSignZoneHistoryRepository.saveAll(list);
            return SUCCESS;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return FAILED;
        }
    }
}
