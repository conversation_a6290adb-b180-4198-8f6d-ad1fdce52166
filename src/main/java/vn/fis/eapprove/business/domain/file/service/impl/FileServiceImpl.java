package vn.fis.eapprove.business.domain.file.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import vn.fis.eapprove.business.domain.file.service.FileService;
import vn.fis.eapprove.business.utils.FileUtils;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.model.response.FileUploadResponse;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.exception.FileOperationException;
import vn.fis.spro.file.manager.FileManager;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class FileServiceImpl implements FileService {

    private final CredentialHelper credentialHelper;
    private final FileManager fileManager;

    @Value("${app.s3.bucket}")
    private String bucket;

    @Override
    public List<Object> getDownloadFiles(List<String> filenames, String type) {
        List<Object> response = new ArrayList<>();
        try {

            List<byte[]> contents = new ArrayList<>();
            for (String fileName : filenames) {
                byte[] content = fileManager.getFile(bucket, fileName);
                contents.add(content);
            }

            if (!CollectionUtils.isEmpty(contents)) {
                if (type.equalsIgnoreCase(CommonConstants.TYPE_BASE64)) {
                    for (byte[] b : contents) {
                        String encodedString = Base64.getEncoder().encodeToString(b);
                        response.add(encodedString);
                    }
                } else {
                    response.addAll(contents);
                }
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return response;
    }

    @Override
    public FileUploadResponse save(MultipartFile file, String type, boolean folderByDate) {
        try {
            if (file == null || file.getOriginalFilename() == null) {
                return null;
            }

            String fileName = file.getOriginalFilename();
            String folder = getFileFolderByType(type);
            long fileSize = file.getSize();

            log.info("Starting upload file: {} ({} bytes)", fileName, fileSize);

            // check bucket exists
            if (!fileManager.isBucketExisting(bucket)) {
                fileManager.createBucket(bucket);
            }

            // upload to minIO
            String uploadedFileName = fileManager.putFile(bucket,
                    FileUtils.getUploadFileName(folder, FileUtils.lowerFileNameExtension(fileName), new Date(), folderByDate),
                    fileSize,
                    file.getInputStream());

            return FileUploadResponse.builder()
                    .type(type)
                    .fileName(uploadedFileName)
                    .size(fileSize)
                    .contentType(file.getContentType())
                    .downloadUrl(fileManager.getUrlFile(bucket, uploadedFileName))
                    .build();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return null;
    }

    public String getFileFolderByType(String type) {
        String folder = "";
        if (!ValidationUtils.isNullOrEmpty(type)) {
            folder = "import/import/" + type;
        }

        return folder;
    }

    @Override
    public String saveFileToMinIO(InputStream is, String folder, String fileName, Date date, boolean splitFolderByDate) throws FileOperationException, IOException {
        if (!ValidationUtils.isNullOrEmpty(fileName)) {
            if (is == null) {
                return null;
            }

            if (!fileManager.isBucketExisting(bucket)) {
                fileManager.createBucket(bucket);
            }

            String uploadFileName = FileUtils.getUploadFileName(folder, FileUtils.lowerFileNameExtension(fileName), date, splitFolderByDate);
            return fileManager.putFile(bucket, uploadFileName, is.available(), is);
        }

        return null;
    }
}
