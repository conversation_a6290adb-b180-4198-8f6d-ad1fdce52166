package vn.fis.eapprove.business.domain.codeGen.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.Hibernate;

import jakarta.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * Author: AnhVTN
 * Date: 30/03/2023
 */


@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@Entity
@Table(name = "code_gen_struct")
public class CodeGenStruct {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    @Column(name = "code_gen_config_id")
    private Long codeGenConfigId;
    @Column(name = "data_type", length = 50, columnDefinition = "varchar(50) null comment 'Kiểu dữ liệu'")
    private String dataType;
    @Column(name = "structor_code", length = 100, columnDefinition = "varchar(100) null comment 'Cấu trúc mã'")
    private String structorCode;
    @Column(name = "value", length = 100, columnDefinition = "varchar(100) null comment 'Giá trị'")
    private String value;
    @Column(name = "format", length = 100)
    private String format;
    @Column(name = "length")
    private Integer length;
    @Column(name = "current_value", length = 50)
    private String currentValue;
    @Column(name = "sort_order")
    private Integer sortOrder;
    @Column(name = "default_value")
    private String defaultValue;
    @Column(name = "schedule", columnDefinition = "int null comment 'Chu kỳ\n0: Không reset\n1: Ngày\n2: Tuần\n3: Tháng\n4: Quý\n5: Năm\n'")
    private Integer schedule;

    @Column(name = "reset_time")
    private Date resetTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        CodeGenStruct that = (CodeGenStruct) o;
        return id != null && Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
