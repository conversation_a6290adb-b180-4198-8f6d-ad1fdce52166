package vn.fis.eapprove.business.domain.currency.entity;

/**
 * Author: AnhVTN
 * Date: 31/03/2023
 */

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Table(name = "currency")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Currency {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    @Column(name = "name", length = 50)
    private String name;

    @Column(name = "code", length = 50)
    private String code;

    @Column(name = "another_name")
    private String anotherName;

    @Column(name = "rounding_rules")
    private Integer roundingRules;

    @Column(name = "description", length = 500)
    private String description;

    @Column(name = "create_at")
    private Date createdAt;

    @Column(name = "update_at")
    private Date updatedAt;

    @Column(name = "user_create", length = 100)
    private String userCreate;

    @Column(name = "user_updated", length = 100)
    private String userUpdated;
}
