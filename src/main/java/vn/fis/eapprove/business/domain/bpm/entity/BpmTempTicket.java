package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Data;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.*;

@Data
@Entity
@Transactional
@Table(name = "temp_ticket")
public class BpmTempTicket {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "TEMP_DATA")
    private String tempData;

    @Column(name = "PROC_INST_ID")
    private String procInstId;

    @Column(name = "TASK_ID")
    private String taskId;

    @Column(name = "STATUS")
    private Boolean status;

}
