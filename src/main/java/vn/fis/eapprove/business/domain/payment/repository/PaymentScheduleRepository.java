package vn.fis.eapprove.business.domain.payment.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.payment.entity.PaymentSchedule;


import java.util.List;

@Repository
public interface PaymentScheduleRepository extends JpaRepository<PaymentSchedule, Long>, JpaSpecificationExecutor<PaymentSchedule> {
    @Query("select count(p.ticketId) from PaymentSchedule p where p.ticketId = :ticketId order by p.createDate DESC ")
    Long countByTicketId(Long ticketId);

    List<PaymentSchedule> findByTicketId(Long id);
}
