package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmAdditionalRequest;

@Repository
public interface BpmAdditionalRequestRepository extends JpaRepository<BpmAdditionalRequest, Long> {
    BpmAdditionalRequest findTopByProcInstIdOrderByIdDesc(String procInstId);

}