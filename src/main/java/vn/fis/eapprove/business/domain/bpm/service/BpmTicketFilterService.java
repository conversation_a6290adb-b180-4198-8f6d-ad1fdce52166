package vn.fis.eapprove.business.domain.bpm.service;


import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.BpmTicketFilterRequest;
import vn.fis.eapprove.business.model.request.BpmTicketFilterSearchRequest;
import vn.fis.eapprove.business.model.response.BpmTicketFilterResponse;

import java.util.List;
import java.util.Map;

public interface BpmTicketFilterService {
    Map<String, Object> createUpdate(BpmTicketFilterRequest request) ;
    String validate(BpmTicketFilterRequest request, String username);
    boolean updateStatus(BpmTicketFilterRequest request);
    List<BpmTicketFilterResponse> searchFilter(BpmTicketFilterSearchRequest request);
    Map<String, Object> getDetailById(Long id, String username);
    void cloneById(Long id);
    void deleteById(Long id);
    PageDto searchFilterTicket(BpmTicketFilterSearchRequest request);
    List<Map<String, Object>> searchFilterOption(BpmTicketFilterSearchRequest request);
    Map<Long, Object> countSearchFilter(String username, String type);
}
