package vn.fis.eapprove.business.domain.notification.repository;

import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import vn.fis.eapprove.business.domain.notification.entity.NotificationTemplate;


import java.util.List;

public interface NotificationTemplateRepository extends JpaRepository<NotificationTemplate, Long>, JpaSpecificationExecutor<NotificationTemplate> {
    @Query("SELECT nt from NotificationTemplate nt where " +
            " (:id is null or nt.id = :id )" +
            " and (:type is null or nt.type =:type)")
    List<NotificationTemplate> getTemplateByCondition(@Param("id") Long id, @Param("type") String type);

    NotificationTemplate findNotificationTemplateById(Long id);

    List<NotificationTemplate> findNotificationTemplatesByActionCodeAndType(String actionCode, String type);

    List<NotificationTemplate> findNotificationTemplatesByActionCodeAndTypeAndNotificationObject(String actionCode, String type, String notificationObject);

    List<NotificationTemplate> findNotificationTemplatesByTitleEqualsIgnoreCaseAndType(String title, String type);

    NotificationTemplate findByTypeNoti(String type);

    @Modifying
    @Query("DELETE from NotificationTemplate nt where nt.id in (:ids)")
    void deleteByIds(@Param("ids") Long[] ids);
}
