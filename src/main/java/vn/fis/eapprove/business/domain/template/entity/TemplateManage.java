package vn.fis.eapprove.business.domain.template.entity;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Set;

@Entity
@Table(name = "template_manage")
@Data
public class TemplateManage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "template_name")
    private String templateName;

    @Column(name = "description")
    private String description;

    @Column(name = "status")
    private Integer status;

    @Column(name = "template")
    private String template;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "modified_date")
    private LocalDateTime modifiedDate;

    @Column(name = "modified_user")
    private String modifiedUser;

    @Column(name = "url_name")
    private String urlName;

    @Column(name = "special_flow")
    private Boolean specialFlow;

    @Column(name = "special_parent_id")
    private Long specialParentId;

    @Column(name = "special_company_code")
    private String specialCompanyCode;

    @Column(name = "special_parent_service_id")
    private Long specialParentServiceId;

    @OneToMany(mappedBy = "templateManage")
    @JsonIgnore
    private Set<PermissionDataManagement> permissionDataManagements;

    @OneToMany(mappedBy = "templateManage")
    @JsonIgnore
    private Set<SharedUser> sharedUsers;

    @Column(name = "company_code")
    private String companyCode;
    @Column(name = "company_name")
    private String companyName;
}
