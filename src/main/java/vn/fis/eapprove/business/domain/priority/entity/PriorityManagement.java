package vn.fis.eapprove.business.domain.priority.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Set;

@Entity
@Table(name = "priority_management")
@Data
public class PriorityManagement {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "code")
    private String code;

    @Column(name = "name")
    private String name;

    @Column(name = "status")
    private Integer status;

    @Column(name = "color")
    private String color;

    @Column(name = "description")
    private String description;

    @Column(name = "sla_value")
    private Double slaValue;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "modified_user")
    private String modifiedUser;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "modified_date")
    private LocalDateTime modifiedDate;

    @Column(name = "time_complete")
    private String alertTimeComplete;

    @Column(name = "reminder_being_time")
    private Integer reminderBeingTime;

    @Column(name = "reminder_time")
    private Integer reminderTime;

    @Column(name = "reminder_being_type")
    private String reminderBeingType;

    @Column(name = "reminder_type")
    private String reminderType;

    @Column(name = "reminder_being_value")
    private Integer reminderBeingValue;

    @Column(name = "reminder_value")
    private Integer reminderValue;

    @Column(name = "config_reminder")
    private Boolean configReminder;
    @Column(name = "company_code")
    private String companyCode;
    @Column(name = "company_name")
    private String companyName;

    @Column(name = "active_status")
    private Integer activeStatus;

    @OneToMany(mappedBy = "priorityManagement")
    @JsonIgnore
    private Set<PermissionDataManagement> permissionDataManagements;

    @OneToMany(mappedBy = "priorityManagement")
    @JsonIgnore
    private Set<SharedUser> sharedUsers;
}
