package vn.fis.eapprove.business.domain.legislative.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@Entity
@Table(name = "legislative_program")
public class LegislativeProgram {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "type", nullable = false)
    private String type;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "activity_status", nullable = false)
    private String activityStatus;

    @Column(name = "description")
    private String description;

    @Column(name = "release_year", nullable = false)
    private Long releaseYear;

    @Column(name = "responsible_agency", nullable = false)
    private String responsibleAgency;

    @Column(name = "organization_sector")
    private String organizationSector;

    @Column(name = "proc_def_id")
    private String procDefId;

    @Column(name = "process_id")
    private Long processId;

    @Column(name = "policy_development_process")
    private Boolean policyDevelopmentProcess;

    @Column(name = "process_type")
    private String processType;

    @Column(name = "has_ticket")
    private Boolean hasTicket;

    @Column(name = "proc_inst_id")
    private String procInstId;

    @Column(name = "ticket_id")
    private Long ticketId;

    @Column(name = "ticket_name")
    private String ticketTitle;

    @Column(name = "request_code")
    private String requestCode;

    @Column(name = "ticket_status")
    private String ticketStatus;

    @Column(name = "start_key")
    private String startKey;

    @Column(name = "ticket_created_time")
    private LocalDateTime ticketCreatedTime;

    @Column(name = "ticket_finish_time")
    private LocalDateTime ticketFinishTime;

    @Column(name = "process_time")
    private Long processTime;

    @Column(name = "execution_count")
    private Integer executionCount;

    @Column(name = "approval_session")
    private String approvalSession;

    @Column(name = "review_session")
    private String reviewSession;

    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "updated_user")
    private String updatedUser;

    @Column(name = "cancel_time")
    private LocalDateTime cancelTime;

    @Column(name = "cancel_user")
    private String cancelUser;

    @Column(name = "company_code")
    private String companyCode;

    @Column(name = "company_name")
    private String companyName;

    @Column(name = "chart_node_code")
    private String chartNodeCode;

    @Column(name = "estimated_time")
    private String estimatedTime;

    @OneToMany(mappedBy = "legislativeProgram")
    @JsonIgnore
    private Set<LegislativeProgramDetail> legislativeProgramDetails;
}
