package vn.fis.eapprove.business.domain.evaluation.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.evaluation.entity.EvaluationDepartment;


import java.util.List;

@Repository
public interface EvaluationDepartmentRepository extends JpaRepository<EvaluationDepartment, Long>, JpaSpecificationExecutor<EvaluationDepartment> {
    @Query("SELECT c.departmentCodes FROM EvaluationDepartment c where c.evaluationCriteriaId = :evaluationCriteriaId")
    List<String> findEvaluationDepartmentsByEvaluationCriteriaId(@Param("evaluationCriteriaId") Long evaluationCriteriaId);

    @Query("SELECT c.evaluationCriteriaId FROM EvaluationDepartment c where c.departmentCodes in(:departmentCodes)")
    List<Long> findEvaluationDepartmentsByDepartmentCodesIn(@Param("departmentCodes") List<String> departmentCodes);

    void deleteAllByEvaluationCriteriaId(Long evaluationCriteriaId);

}
