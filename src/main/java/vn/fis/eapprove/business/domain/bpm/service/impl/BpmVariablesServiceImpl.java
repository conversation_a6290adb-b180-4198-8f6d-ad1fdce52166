package vn.fis.eapprove.business.domain.bpm.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.constant.AppConstants;
import vn.fis.eapprove.business.domain.bpm.entity.BpmVariables;
import vn.fis.eapprove.business.domain.bpm.repository.BpmVariablesRepository;
import vn.fis.eapprove.business.domain.bpm.service.BpmVariablesService;
import vn.fis.eapprove.business.domain.fileCondition.service.FileService;
import vn.fis.eapprove.business.model.request.VariableValueDto;

import vn.fis.eapprove.business.utils.CastUtils;
import vn.fis.eapprove.business.utils.FileUtils;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.util.DateTimeUtils;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Author: PhucVM
 * Date: 06/03/2023
 */
@Slf4j
@Service("BpmVariablesServiceImpl")
@Transactional
public class BpmVariablesServiceImpl implements BpmVariablesService {

    private final BpmVariablesRepository bpmVariablesRepository;
    private final FileService fileService;

    @Autowired
    public BpmVariablesServiceImpl(BpmVariablesRepository bpmVariablesRepository,
                                   FileService fileService) {
        this.bpmVariablesRepository = bpmVariablesRepository;
        this.fileService = fileService;
    }

    @Override
    @Transactional(noRollbackFor = RuntimeException.class)
    public void deleteVariables(String procInstId, String taskId, Set<String> variables) {
        try {
            bpmVariablesRepository.deleteVariables(procInstId, taskId, variables);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    @Override
    @Transactional(noRollbackFor = RuntimeException.class)
    public List<BpmVariables> saveAll(List<BpmVariables> variables) {
        return bpmVariablesRepository.saveAll(variables);
    }

    @Override
    public void saveVariables(String procInstId, String taskId, Map<String, VariableValueDto> variables, boolean isDraft) {
        List<BpmVariables> variablesList = new ArrayList<>();
        try {
            if (!ValidationUtils.isNullOrEmpty(variables)) {
                variables.forEach((k, v) -> {
                    // remove các variable type = file truyền sang có value null và valueInfo null
                    if (v.getValueInfo() == null && v.getValue() == null && v.getType().toUpperCase().equalsIgnoreCase(CommonConstants.VarType.FILE)) {
                        return;
                    }
                    BpmVariables bpmVariables = new BpmVariables();
                    bpmVariables.setIsDraft(isDraft ? 1 : 0);
                    bpmVariables.setName(k);
                    bpmVariables.setTaskId(taskId);
                    bpmVariables.setProcInstId(procInstId);
                    if (!ValidationUtils.isNullOrEmpty(v.getValueInfo())) { // (phucvm3) save addtional values
                        bpmVariables.setAdditionalVal(ObjectUtils.toJson(v.getValueInfo()));
                    }
                    String value = v.getValue() != null ? v.getValue().toString() : null;
                    switch (v.getType().toUpperCase()) {
                        case CommonConstants.VarType.STRING:
                            bpmVariables.setStringVal(value);
                            bpmVariables.setType(CommonConstants.VarType.STRING);
                            break;
                        case CommonConstants.VarType.DOUBLE:
                            bpmVariables.setDoubleVal(CastUtils.stringToDouble(value));
                            bpmVariables.setType(CommonConstants.VarType.DOUBLE);
                            break;
                        case CommonConstants.VarType.LONG:
                            bpmVariables.setLongVal(CastUtils.stringToLong(value));
                            bpmVariables.setType(CommonConstants.VarType.LONG);
                            break;
                        case CommonConstants.VarType.INTEGER:
                            bpmVariables.setLongVal(CastUtils.stringToLong(value));
                            bpmVariables.setType(CommonConstants.VarType.INTEGER);
                            break;
                        case CommonConstants.VarType.JSON:
                            bpmVariables.setJsonVal(value);
                            bpmVariables.setType(CommonConstants.VarType.JSON);
                            break;
                        case CommonConstants.VarType.FILE:
                            bpmVariables.setDownloadUrl(uploadVarFile(v));
                            bpmVariables.setType(CommonConstants.VarType.FILE);
                            break;
                    }

                    variablesList.add(bpmVariables);
                });
            }

            saveAll(variablesList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * Update variable file type
     *
     * <AUTHOR>
     */
    @Override
    public String uploadVarFile(VariableValueDto varDto) {
        String downloadUrl = null;
        if (varDto != null) {
            String uploadContent = varDto.getValue() != null ? varDto.getValue().toString() : "";
            String fileName = "";
            if (varDto.getValueInfo() != null) {
                Object fileNameObj = varDto.getValueInfo().get("filename");
                fileName = fileNameObj != null ? fileNameObj.toString() : "";
            }

            Date now = new Date();
            if (!ValidationUtils.isNullOrEmpty(uploadContent)) {
                if (uploadContent.startsWith("http")) { // direct link
                    downloadUrl = uploadContent;
                } else { // upload file base64
                    // update ticket cons
                    if (fileName.matches("^" + CommonConstants.FileFolder.TICKET_FILES + "/\\d{4}/\\d{2}/\\d{2}.*")) {
                        downloadUrl = uploadContent;
                    } else if (!uploadContent.endsWith(CommonConstants.UNDEFINED_BASE64)) { // not undefined
                        downloadUrl = saveFileToMinIO(uploadContent, fileName, now);
                    }
                }
            } else if (!fileName.isEmpty()) { // update ticket only (old file name)
                if (!fileName.matches("^" + CommonConstants.FileFolder.TICKET_FILES + "/\\d{4}/\\d{2}/\\d{2}.*")) {
                    String folder = FileUtils.getUploadFolder(CommonConstants.FileFolder.TICKET_FILES, now, true) + CommonConstants.PATH_SEPARATOR;
                    downloadUrl = folder + fileName;
                } else {
                    downloadUrl = fileName;
                }
            }
        }

        return downloadUrl;
    }

    /**
     * Save file to minIO
     *
     * <AUTHOR>
     */
    @Override
    public String saveFileToMinIO(String encodedBase64, String fileName, Date date) {
        return fileService.saveFileToMinIO(encodedBase64, CommonConstants.FileFolder.TICKET_FILES, fileName, date, true);
    }

    @Override
    public String getAdditionalValue(VariableValueDto valueDto) {
        if (valueDto != null) {
            Map<String, Object> additionalValue = valueDto.getValueInfo();
            if (!ValidationUtils.isNullOrEmpty(additionalValue)) {
                String varType = valueDto.getType();
                if (!ValidationUtils.isNullOrEmpty(varType)) {
                    if (varType.equalsIgnoreCase(CommonConstants.VarType.FILE)) {
                        Object fileName = additionalValue.get("filename");
                        return fileName != null ? fileName.toString() : null;
                    } else if (additionalValue.containsKey(AppConstants.AdditionalValueKey.VALUE)) { // check additional info data type like DATE...
                        Object typeObj = additionalValue.get(AppConstants.AdditionalValueKey.TYPE);
                        if (!ValidationUtils.isNullOrEmpty(typeObj)) {
                            String type = typeObj.toString();
                            if (type.equalsIgnoreCase(AppConstants.AdditionalVarType.DATE)) {
                                return getISODateTimeText(getValueFromAdditional(additionalValue));
                            }
                        }
                    }
                }
            }
        }

        return null;
    }

    private String getISODateTimeText(Object value) {
        try {
            if (value != null) {
                long longValue = Long.parseLong(value.toString());
                LocalDateTime localDateTime = DateTimeUtils.milliToLocalDateTime(longValue).withNano(0);
                return ObjectUtils.convertObject(localDateTime, String.class);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return null;
    }

    private Object getValueFromAdditional(@NonNull Map<String, Object> additionalValue) {
        return additionalValue.get(AppConstants.AdditionalValueKey.VALUE);
    }
}
