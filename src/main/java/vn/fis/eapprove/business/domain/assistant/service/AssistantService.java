
package vn.fis.eapprove.business.domain.assistant.service;

import vn.fis.eapprove.business.domain.assistant.entity.Assistant;
import vn.fis.eapprove.business.dto.AssistantOpinionDto;
import vn.fis.eapprove.business.dto.PageDto;
import java.util.List;

/**
 * Author: AnhVTN
 * Date: 01/03/2023
 */
public interface AssistantService {

    List<Assistant> getListAssistantByTicketId(String ticketId);

    PageDto getAssistantsOpinion(AssistantOpinionDto request);

    List<String> getTicketIdByAssistanEmail(String email);

    List<String> getListAssistantTicket(List<String> usernames);
}
