package vn.fis.eapprove.business.domain.legislative.model.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class RankTicketResponse {
    private Long ticketId;
    private String ticketTitle;
    private String responseAgency;
    private LocalDateTime ticketCreatedTime;
    private LocalDateTime ticketFinishTime;
    private Long processTime;
    private String procDefId;
    private String startKey;
    private String processType;
}
