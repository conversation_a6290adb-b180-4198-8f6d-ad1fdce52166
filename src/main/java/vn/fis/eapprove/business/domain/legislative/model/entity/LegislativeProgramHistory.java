package vn.fis.eapprove.business.domain.legislative.model.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "legislative_program_history")
public class LegislativeProgramHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "legislative_program_id", nullable = false)
    private Long legislativeId;

    @Column(name = "proc_inst_id")
    private String procInstId;

    @Column(name = "detail")
    private String detail;

    @Column(name = "version")
    private Integer version;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "created_user")
    private String createdUser;
}
