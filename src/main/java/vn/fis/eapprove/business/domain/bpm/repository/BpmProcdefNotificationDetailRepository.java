package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefNotificationDetail;

import java.util.List;

@Repository
public interface BpmProcdefNotificationDetailRepository extends JpaRepository<BpmProcdefNotificationDetail, Long>, JpaSpecificationExecutor<BpmProcdefNotificationDetail> {

    void deleteAllByBpmProcdefNotificationId(Long bpmProcdefNotificationId);

    List<BpmProcdefNotificationDetail> findBpmProcdefNotificationDetailsByBpmProcdefNotificationId(Long bpmProcdefNotificationId);
}
