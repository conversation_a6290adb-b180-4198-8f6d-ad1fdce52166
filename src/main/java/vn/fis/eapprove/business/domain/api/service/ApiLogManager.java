package vn.fis.eapprove.business.domain.api.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.api.entity.ApiLog;
import vn.fis.eapprove.business.domain.api.repository.ApiLogRepository;
import vn.fis.eapprove.business.dto.ApiLogDto;
import vn.fis.eapprove.business.dto.PageSearchDto;
import vn.fis.eapprove.business.model.response.ApiLogResponse;
import vn.fis.eapprove.business.specification.ApiLogSpecification;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.business.utils.TimeUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.ArrayList;
import java.util.List;

import static vn.fis.eapprove.business.constant.Constant.LOCALFORMAT;

@Slf4j
@Service("ApiLogManagerV1")
@Transactional
public class ApiLogManager {

    @Autowired
    ResponseUtils responseUtils;

    @Autowired
    ApiLogRepository apiLogRepository;

    @Autowired
    ApiLogSpecification apiLogSpecification;

    public PageSearchDto searchViewLogApi(ApiLogDto criteria) {
        try {
            int pageNum = criteria.getPage() - 1;
            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());
            Page<ApiLog> page = apiLogRepository.findAll(
                    apiLogSpecification.filter(criteria),
                    PageRequest.of(pageNum, criteria.getLimit(),
                            sort));
            List<ApiLogResponse> apiLogResponses = new ArrayList<>();
            for (ApiLog apiLog : page.getContent()) {
                ApiLogResponse apiLogResponse = ApiLogResponse.builder()
                        .id(apiLog.getId())
                        .url(apiLog.getUrl())
                        .method(apiLog.getMethod())
                        .header(apiLog.getHeader())
                        .requestBody(apiLog.getRequestBody())
                        .requestTime(apiLog.getRequestTime() == null ? "" : TimeUtils.localDateTimeToString(apiLog.getRequestTime(), LOCALFORMAT))
                        .responseTime(apiLog.getResponseTime() == null ? "" : TimeUtils.localDateTimeToString(apiLog.getResponseTime(), LOCALFORMAT))
                        .responseStatus(apiLog.getResponseStatus())
                        .responseData(apiLog.getResponseData())
                        .bpmProcdefApiId(apiLog.getBpmProcdefApiId())
                        .bpmProcinstId(apiLog.getBpmProcinstId())
                        .apiType(apiLog.getApiType())
                        .retryTime(apiLog.getRetryTime() == null ? "" : TimeUtils.localDateTimeToString(apiLog.getRetryTime(), LOCALFORMAT))
                        .retryCount(apiLog.getRetryCount())
                        .apiName(apiLog.getApiName())
                        .taskDefKey(apiLog.getTaskDefKey())
                        .ticketName(apiLog.getTicketName())
                        .requestCode(apiLog.getRequestCode())
                        .build();
                apiLogResponses.add(apiLogResponse);

            }
            return PageSearchDto.builder().content(apiLogResponses)
                    .number(page.getNumber() + 1)
                    .numberOfElements(page.getNumberOfElements())
                    .page(page.getNumber() + 1)
                    .limit(page.getSize())
                    .totalPages(page.getTotalPages())
                    .totalElements(page.getTotalElements())
                    .sortBy(criteria.getSortBy())
                    .sortType(criteria.getSortType())
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

//    public String updateApiLog(ApiLogResquest apiLogResquest){
//        try {
//
//            ApiLog apiLog = apiLogRepository.findById(apiLogResquest.getId()).get();
//            apiLog.setUrl(apiLogResquest.getUrl());
//
//            return MESSAGE_SUCCESS;
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            return MESSAGE_FAIL;
//        }
//    }
}
