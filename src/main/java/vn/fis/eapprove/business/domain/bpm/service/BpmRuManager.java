package vn.fis.eapprove.business.domain.bpm.service;

import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.impl.bpmn.parser.BpmnParse;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTpSignZone;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcInstRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTaskRepository;
import vn.fis.eapprove.business.domain.changeAssignee.entity.ChangeAssigneeHistory;
import vn.fis.eapprove.business.domain.changeAssignee.service.ChangeAssigneeHistoryService;
import vn.fis.eapprove.business.dto.BpmTaskDto;
import vn.fis.eapprove.business.model.ActionApiContext;
import vn.fis.eapprove.business.model.NotificationUser;
import vn.fis.eapprove.business.model.request.HistoryDto;
import vn.fis.eapprove.business.model.request.RequestUpdateDto;
import vn.fis.eapprove.business.model.request.VariableValueDto;
import vn.fis.eapprove.business.model.request.WorkFlowRequest;
import vn.fis.eapprove.business.model.response.UserTitleResponse;
import vn.fis.eapprove.business.specification.BpmTaskSpecification;
import vn.fis.eapprove.business.tenant.manager.ActionApiService;
import vn.fis.eapprove.business.tenant.manager.CamundaEngineService;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.utils.CamundaUtils;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.camunda.SproFlow;
import vn.fis.spro.common.camunda.SproFlowNode;
import vn.fis.spro.common.constants.ProcInstConstants;
import vn.fis.spro.common.constants.TaskActionConstants;
import vn.fis.spro.common.constants.TaskConstants;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.manager.FileManager;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("BpmRuManagerV1")
@Transactional
@Slf4j
public class BpmRuManager {

    @Autowired
    private BpmProcInstRepository bpmProcInstRepo;
    @Autowired
    private BpmHistoryManager bpmHistoryManager;
    @Autowired
    private BpmTaskRepository bpmTaskRepository;
    @Autowired
    private BpmTaskSpecification bpmTaskSpecification;
    @Autowired
    private CamundaEngineService camundaEngineService;
    @Autowired
    private BpmTaskUserService bpmTaskUserService;
    @Autowired
    private BpmTpSignZoneManager bpmTpSignZoneManager;
    @Autowired
    private CredentialHelper credentialHelper;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private ActionApiService actionApiService;
    @Autowired
    private BpmProcInstManager bpmProcInstManager;
    @Autowired
    private ChangeAssigneeHistoryService changeAssigneeHistoryService;
    @Autowired
    private BpmTaskManager bpmTaskManager;
    @Autowired
    private Common common;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Value("${app.s3.bucket}")
    private String bucket;
    @Value("${spring.kafka.consumer.topic.notification-user}")
    private String notificationUser;

    public Document getXml(String procDefId) {
        try {
            String diagramXml = camundaEngineService.getXML(procDefId);

            DocumentBuilder db = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            InputSource is = new InputSource();
            is.setCharacterStream(new StringReader(diagramXml));

            return db.parse(is);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Map<String, Object> requestUpdate(RequestUpdateDto requestUpdateDto)  {
        List<String> checkStatus = Arrays.asList(TaskConstants.Status.COMPLETED.code, ProcInstConstants.Status.COMPLETED.code, ProcInstConstants.Status.CLOSED.code,
                ProcInstConstants.Status.CANCEL.code, ProcInstConstants.Status.RECALLING.code, ProcInstConstants.Status.RECALLED.code);
        // check task completed
        BpmTask bpmTask = bpmTaskManager.getBpmTaskByTaskId(requestUpdateDto.getTaskId());
        if (bpmTask != null && checkStatus.contains(bpmTask.getTaskStatus())) {
            throw new RuntimeException(common.getMessage("message.task.request-update.completed.not-allow", new Object[]{bpmTask.getTaskName()}));
        }

        BpmProcInst bpmProcInst = bpmProcInstManager.findById(requestUpdateDto.getTicketId());
        if (bpmProcInst != null && checkStatus.contains(bpmProcInst.getTicketStatus())) {
            throw new RuntimeException(common.getMessage("message.ticket.request-update.not-allow"));
        }

        // get start event
        BpmnModelInstance modelInstance = camundaEngineService.getModelInstance(requestUpdateDto.getProcDefId());
        if (modelInstance == null) {
            return null;
        }

        StartEvent startEvent = CamundaUtils.getStartEvent(modelInstance);
        String startKey = startEvent != null ? startEvent.getId() : null;

        String actionCode = requestUpdateDto.getIsStartEvent()
                ? TaskActionConstants.Action.REQUEST_UPDATE_TO_START.code
                : TaskActionConstants.Action.REQUEST_UPDATE.code;

        /* BEGIN handle call action api on beginning */
        Map<String, VariableValueDto> variables = actionApiService.getTicketVariables(bpmProcInst.getTicketProcInstId());

        // Add user login
        String username = credentialHelper.getJWTPayload().getUsername();
        Map<String, Object> optVariable = new HashMap<>();
        optVariable.put("accountLogin", username);
        optVariable.put("actionCode", actionCode);

        ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(
                actionCode,
                requestUpdateDto.getProcDefId(),
                startKey,
                bpmProcInst.getTicketProcInstId(),
                actionApiService.createVariablesMap(requestUpdateDto, bpmProcInst, variables, optVariable));
        /* END handle call action api on beginning */

        // check response of api call BEFORE
        Map<String, Object> resultData = new HashMap<>();
        actionApiService.setActionApiResponse(actionApiContext, resultData);

        // check allow post process of api call BEFORE
        if (!actionApiContext.isAllowPostProcess()) {
            resultData.put("isSuccess", false);
            return resultData;
        }

        List<BpmTask> listTask = bpmTaskRepository.listRuTask(requestUpdateDto.getProcInstId(), Arrays.asList(requestUpdateDto.getCurrTaskKey(), requestUpdateDto.getRuTaskKey()));
        Map<String, List<BpmTask>> groupTaskByKey = listTask.stream().collect(Collectors.groupingBy(BpmTask::getTaskDefKey));

        Set<String> listTaskKey = new HashSet<>();
        List<String> recreateTasks = new ArrayList<>();
        WorkFlowRequest wfRequest = new WorkFlowRequest();
        wfRequest.setProcDefId(requestUpdateDto.getProcDefId());
        wfRequest.setProcInstId(requestUpdateDto.getProcInstId());
        wfRequest.setFromNodeId(requestUpdateDto.getRuTaskKey());

        // get spro flows
        List<SproFlow> sproFlows = camundaEngineService.getSproFlows(wfRequest);
        SproFlowNode sproFlowNode = sproFlows.stream().flatMap(e -> e.getNodes().stream()).toList()
                .stream().filter(e -> e.getId().equalsIgnoreCase(requestUpdateDto.getRuTaskKey())).findAny().orElse(null);
        boolean isMultiInstance = sproFlowNode != null && sproFlowNode.isMultiInstance();

        List<String> flowTasks = sproFlows.stream().flatMap(
                e -> e.getNodes().stream().map(SproFlowNode::getId)
        ).collect(Collectors.toList());
        // remove request update task from cancel-activity camunda
        if (!ValidationUtils.isNullOrEmpty(flowTasks)) {
            listTaskKey.addAll(bpmTaskRepository.getAffectedKeys(requestUpdateDto.getProcInstId(), flowTasks));
            // remove request update task from cancel-activity camunda
            listTaskKey.remove(requestUpdateDto.getRuTaskKey());
        }
        recreateTasks.add(requestUpdateDto.getRuTaskKey() + (isMultiInstance ? BpmnParse.MULTI_INSTANCE_BODY_ID_SUFFIX : ""));

        // (phucvm3) save bpm-history pre
        saveHistoryPreRequestUpdate(listTaskKey, requestUpdateDto, groupTaskByKey, startKey, bpmProcInst.getCreatedUser());

        if (!requestUpdateDto.getIsStartEvent()) {
            camundaRu(requestUpdateDto.getProcInstId(), recreateTasks, listTaskKey);
            List<Map<String, Object>> listData = camundaEngineService.getCurrentRuntimeTasks(requestUpdateDto.getProcInstId());
            // re-add ruTaskKey for process all affected tasks
            listTaskKey.add(requestUpdateDto.getRuTaskKey());
            handleRuTask(listData, requestUpdateDto, groupTaskByKey, requestUpdateDto.getProcInstId(), new ArrayList<>(listTaskKey), requestUpdateDto.getTicketId(), requestUpdateDto.getUser());
        } else {
            camundaRu(requestUpdateDto.getProcInstId(), recreateTasks, listTaskKey);
            handleRuTicket(requestUpdateDto, groupTaskByKey, new ArrayList<>(listTaskKey));
        }

        //Thông báo cho user liên quan
        Map<String, VariableValueDto> variablesNotifi = new HashMap<>();
        VariableValueDto variableDto = new VariableValueDto();
        variableDto.setType("String");
        variableDto.setValue(requestUpdateDto.getRuReason());
        variablesNotifi.put("txt_LyDoTraVe", variableDto);

        NotificationUser payload = new NotificationUser();
        payload.setBpmProcdefId(null);
        payload.setNextTaskDefKey(requestUpdateDto.getRuTaskKey());
        payload.setVariables(variablesNotifi);
        payload.setTicketId(requestUpdateDto.getTicketId());
        payload.setIsGetOldVariable(true);
        payload.setLstCustomerEmails(null);
        payload.setActionCode(ProcInstConstants.Notifications.DELETED_BY_RU.code);
        payload.setEmailExe(credentialHelper.getJWTPayload().getUsername());
        kafkaTemplate.send(notificationUser, payload);
//            bpmProcdefNotificationService.addNotificationsByConfig(null, requestUpdateDto.getRuTaskKey(), variablesNotifi,
//                    requestUpdateDto.getTicketId(), true, null, ProcInstConstants.Notifications.DELETED_BY_RU.code);

        /* BEGIN handle call action api at the end */
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // Chỉ chạy sau khi transaction commit
                actionApiService.endHandleActionApi(
                        actionApiContext,
                        actionApiService.createVariablesMap(
                                requestUpdateDto,
                                bpmProcInst,
                                variables,
                                optVariable)
                );
            }
        });
        /* END handle call action api at the end */

        resultData.put("isSuccess", true);
        return resultData;
    }

    public void handleRuTicket(RequestUpdateDto requestUpdateDto, Map<String, List<BpmTask>> groupTaskByKey, List<String> affected) {
        try {
            String procInstId = requestUpdateDto.getProcInstId();
            String currentTaskKey = requestUpdateDto.getCurrTaskKey();
            Long ticketId = requestUpdateDto.getTicketId();
            String account = requestUpdateDto.getUser();
            String reason = requestUpdateDto.getRuReason();
            String attachFiles = null;
            if (!ValidationUtils.isNullOrEmpty(requestUpdateDto.getAttachFiles())) {
                attachFiles = String.join(",", requestUpdateDto.getAttachFiles());
            }

            updateTicketDeleteByRU(procInstId, LocalDateTime.now());
            BpmTaskDto criteria = new BpmTaskDto();
            criteria.setAffected(affected);
            criteria.setProcInstId(procInstId);
            criteria.setListStatus(Arrays.asList(TaskConstants.Status.ACTIVE.code, TaskConstants.Status.COMPLETED.code, TaskConstants.Status.PROCESSING.code, TaskConstants.Status.RE_CREATED_BY_RU.code));
            List<BpmTask> listTask = bpmTaskRepository.findAll(bpmTaskSpecification.filter(criteria));
            BpmTask currentTask = groupTaskByKey.get(currentTaskKey) != null ? groupTaskByKey.get(currentTaskKey).get(0) : null;
            if (!listTask.isEmpty()) {
                affected.remove(currentTaskKey);
                criteria.setListStatus(Arrays.asList(TaskConstants.Status.DELETED_BY_RU.code, TaskConstants.Status.ACTIVE.code, TaskConstants.Status.COMPLETED.code, TaskConstants.Status.PROCESSING.code, TaskConstants.Status.RE_CREATED_BY_RU.code));
                criteria.setAffected(affected);
                List<BpmTask> listTaskAfterRU = bpmTaskRepository.findAll(bpmTaskSpecification.filter(criteria));

                // (phucvm3) save bpm-history post
                saveHistoryPostRequestUpdate(listTaskAfterRU, currentTask, affected, ticketId, account, reason, attachFiles);

                for (BpmTask task : listTask) {
                    task.setTaskStatus(TaskConstants.Status.DELETED_BY_RU.code);
                }
            }
            List<BpmTask> savedBpmTask = bpmTaskRepository.saveAll(listTask);
//            for (BpmTask e : savedBpmTask) {
//                reportByChartNodeService.createReportByChartNode(e.getTaskId());
//            }

            // (phucvm3) remove task has status DELETED_BY_RU before save bpm_task_user
            savedBpmTask.removeIf(e -> e.getTaskStatus().equalsIgnoreCase(TaskConstants.Status.DELETED_BY_RU.code));
            bpmTaskUserService.saveFromBpmTasks(savedBpmTask);
            // (phucvm3) update sign zones
            resetSignZones(procInstId, new HashSet<>(affected));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public void updateTicketDeleteByRU(String procInstId, LocalDateTime time) {
        bpmProcInstRepo.deleteByRu(procInstId, time);
//        BpmProcInst bpmProcInst = bpmProcInstRepo.findBpmProcInstByTicketProcInstId(procInstId);
//        reportByGroupService.createReportByGroup(bpmProcInst.getTicketId());
    }

    public BpmProcInst save(BpmProcInst bpmProcInst) {
        return bpmProcInstRepo.save(bpmProcInst);
    }

    public void handleRuTask(List<Map<String, Object>> dataTask, RequestUpdateDto requestUpdateDto, Map<String, List<BpmTask>> groupTaskByKey, String procInstId, List<String> affected, Long ticketId, String account) {
        try {
            Map<String, Map<String, Object>> mapData = new HashMap<>();
            BpmTask ruTask = groupTaskByKey.get(requestUpdateDto.getRuTaskKey()) != null ? groupTaskByKey.get(requestUpdateDto.getRuTaskKey()).get(0) : null;
            BpmTaskDto criteria = new BpmTaskDto();
            criteria.setAffected(affected);
            criteria.setProcInstId(procInstId);
            criteria.setListStatus(Arrays.asList(TaskConstants.Status.ACTIVE.code, TaskConstants.Status.COMPLETED.code, TaskConstants.Status.PROCESSING.code, TaskConstants.Status.RE_CREATED_BY_RU.code));
            List<BpmTask> listTask = bpmTaskRepository.findAll(bpmTaskSpecification.filter(criteria));
            List<BpmTask> listAdditional = new ArrayList<>();

            // get list of change_assignee_history
            Map<String, ChangeAssigneeHistory> mapChangeAssignee = new HashMap<>();
            Map<String, String> mapTaskDefKeyToOrgAssignee = new HashMap<>();
            if (ticketId != null) {
                // 16/06: Lấy theo task RU
                List<ChangeAssigneeHistory> changeAssigneeHistories = changeAssigneeHistoryService.getLatestChangeByToAssignee(ticketId, ruTask.getTaskId());
                if (!ValidationUtils.isNullOrEmpty(changeAssigneeHistories)) {
                    mapChangeAssignee.putAll(changeAssigneeHistories.stream().collect(Collectors.toMap(e -> getChangeAssigneeMapKey(e.getTaskDefKey(), e.getOrgAssignee(), e.getToAssignee()), Function.identity())));
                    mapTaskDefKeyToOrgAssignee.putAll(changeAssigneeHistories.stream().collect(Collectors.toMap(e -> getTaskDefKeyToOrgAssigneeMapKey(e.getTaskDefKey(), e.getToAssignee()), ChangeAssigneeHistory::getOrgAssignee)));
                }
            }

            if (!listTask.isEmpty()) {
                for (Map<String, Object> data : dataTask) {
                    mapData.put(getRuTaskMapKey(data.get("taskDefKey"), data.get("assignee")), data);
                }

                for (BpmTask task : listTask) {
                    task.setTaskStatus(TaskConstants.Status.DELETED_BY_RU.code);
                    List<ChangeAssigneeHistory> changeAssigneeHistories = changeAssigneeHistoryService.getLatestChangeByToAssignee(ticketId, task.getTaskId());

                    Map<String, Object> taskObj = getTaskData(mapData, mapChangeAssignee, mapTaskDefKeyToOrgAssignee, task);
                    if (taskObj != null) {
                        BpmTask newTask = new BpmTask();
                        newTask.setTaskId(taskObj.get("id").toString());
                        newTask.setTaskExecutionId(taskObj.get("executionId").toString());
                        newTask.setTaskProcInstId(taskObj.get("procInstId").toString());
                        newTask.setTaskProcDefId(taskObj.get("procDefId").toString());
                        newTask.setTaskDefKey(taskObj.get("taskDefKey").toString());
                        newTask.setTaskCaseInstId(taskObj.get("caseInstId") != null ? taskObj.get("caseInstId").toString() : null);
                        newTask.setTaskName(taskObj.get("name").toString());
                        newTask.setSlaFinish(task.getSlaFinish());
                        newTask.setSlaResponse(task.getSlaResponse());
                        newTask.setSlaFinishTime(task.getSlaFinishTime());
                        newTask.setSlaResponseTime(task.getSlaFinishTime());
                        // Check task ủy quyền hay không
                        if (!ValidationUtils.isNullOrEmpty(changeAssigneeHistories)) {
                            ChangeAssigneeHistory changeAssigneeHistory = changeAssigneeHistories.get(0);
                            newTask.setTaskAssignee(changeAssigneeHistory.getOrgAssignee());
                        } else {
                            newTask.setTaskAssignee(task.getTaskAssignee());
                        }
                        newTask.setTaskStatus(TaskConstants.Status.RE_CREATED_BY_RU.code);
                        newTask.setTaskCreatedTime(LocalDateTime.now());

                        switch (task.getTaskType().toLowerCase()) {
                            case "approval":
                                newTask.setTaskType(TaskConstants.Type.APPROVAL.code);
                                newTask.setTaskStartedTime(LocalDateTime.parse(taskObj.get("createTime").toString()));
                                break;
                            case "execution":
                                newTask.setTaskType(TaskConstants.Type.EXECUTION.code);
                                newTask.setTaskStartedTime(null);
                                break;
                        }

                        listAdditional.add(newTask);
                    }
                }

            }

            listTask.addAll(listAdditional);
            List<BpmTask> savedBpmTask = bpmTaskRepository.saveAll(listTask);


            // (phucvm3) remove task has status DELETED_BY_RU before save bpm_task_user
            savedBpmTask.removeIf(e -> e.getTaskStatus().equalsIgnoreCase(TaskConstants.Status.DELETED_BY_RU.code));

            // save history
            saveTaskHistoryPostRequestUpdate(listAdditional, ruTask, mapData, mapChangeAssignee, mapTaskDefKeyToOrgAssignee, ticketId);
            bpmTaskUserService.saveFromBpmTasks(savedBpmTask);

            // change ticket status case ycbs
            BpmProcInst bpmprocInst = bpmProcInstManager.findById(requestUpdateDto.getTicketId());
            if (bpmprocInst.getTicketStatus().equalsIgnoreCase(ProcInstConstants.Status.ADDITIONAL_REQUEST.code)) {
                bpmprocInst.setTicketStatus(ProcInstConstants.Status.PROCESSING.code);
                bpmProcInstRepo.save(bpmprocInst);
//                reportByGroupService.createReportByGroup(bpmprocInst.getTicketId());

            }

            // (phucvm3) update sign zones
            resetSignZones(procInstId, new HashSet<>(affected));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * Get new task data with verify change assignee history
     *
     * <AUTHOR>
     */
    private Map<String, Object> getTaskData(@NonNull Map<String, Map<String, Object>> mapData,
                                            @NonNull Map<String, ChangeAssigneeHistory> mapChangeAssignee,
                                            @NonNull Map<String, String> mapTaskDefKeyToOrgAssignee,
                                            @NonNull BpmTask task) {
        Map<String, Object> taskData = mapData.get(getRuTaskMapKey(task.getTaskDefKey(), task.getTaskAssignee()));
        Map<String, Object> taskDataCandidate = mapData.get(getRuTaskMapKey(task.getTaskDefKey(), ""));

        // check task candidate
        if (taskDataCandidate != null) {
            return taskDataCandidate;
        }

        if (taskData != null) {
            return taskData;
        }

        // check task has change assignee
        if (!ValidationUtils.isNullOrEmpty(mapChangeAssignee)) {
            String taskDefKey = task.getTaskDefKey();
            String toAssignee = task.getTaskAssignee();
            String orgAssignee = mapTaskDefKeyToOrgAssignee.get(getTaskDefKeyToOrgAssigneeMapKey(taskDefKey, toAssignee));
            String mapKey = getChangeAssigneeMapKey(task.getTaskDefKey(), orgAssignee, toAssignee);
            ChangeAssigneeHistory changeAssigneeHistory = mapChangeAssignee.get(mapKey);
            if (changeAssigneeHistory != null) {
                return mapData.get(getRuTaskMapKey(task.getTaskDefKey(), changeAssigneeHistory.getOrgAssignee()));
            }
        }

        return null;
    }

    /**
     * Reset sign zones to null
     *
     * <AUTHOR>
     */
    public void resetSignZones(String procInstId, Set<String> taskDefKeys)  {
        List<BpmTpSignZone> signZones = bpmTpSignZoneManager.getRequestUpdateSignZones(procInstId, taskDefKeys);
        if (!ValidationUtils.isNullOrEmpty(signZones)) {
            List<String> deletedFiles = new ArrayList<>();
            for (BpmTpSignZone e : signZones) {
                // delete from minIO
//                if (!ValidationUtils.isNullOrEmpty(e.getSign())) {
//                    deleteSignFile(e.getSign());
//                }
//                if (!ValidationUtils.isNullOrEmpty(e.getSignedFile()) && !deletedFiles.contains(e.getSignedFile())) {
//                    deleteSignFile(e.getSignedFile());
//                    deletedFiles.add(e.getSignedFile());
//                }

                e.setSign(null);
                e.setSignedFile(null);
                e.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
                e.setUpdatedDate(new Date());
                e.setOrgAssigneeTitle(null);
            }

            bpmTpSignZoneManager.saveAll(signZones);
        }
    }

    /**
     * Reset user sign zones to null
     */
    public void resetSignZoneByTaskDefKeyAndUser(String procInstId, String taskDefKey, String username) {
        BpmTpSignZone signZone = bpmTpSignZoneManager.getSignZoneByTaskDefKeyAndUser(procInstId, taskDefKey, username);
        if (!ValidationUtils.isNullOrEmpty(signZone)) {
            // delete from minIO
//            if (!ValidationUtils.isNullOrEmpty(signZone.getSign())) {
//                deleteSignFile(signZone.getSign());
//            }
            if (!ValidationUtils.isNullOrEmpty(signZone.getSignedFile())) {
                deleteSignFile(signZone.getSignedFile());
            }

            signZone.setSign(null);
            signZone.setSignedFile(null);
            signZone.setUpdatedUser(username);
            signZone.setUpdatedDate(new Date());
            signZone.setOrgAssigneeTitle(null);

            bpmTpSignZoneManager.save(signZone);
        }
    }

    /**
     * Delete file from minIO
     *
     * <AUTHOR>
     */
    private void deleteSignFile(String fileName) {
        try {
            fileManager.deleteFile(bucket, fileName);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    private String getChangeAssigneeMapKey(String taskDefKey, String orgAssignee, String toAssignee) {
        return taskDefKey + ":" + (orgAssignee != null ? orgAssignee : "") + ":" + (toAssignee != null ? toAssignee : "");
    }

    private String getTaskDefKeyToOrgAssigneeMapKey(String taskDefKey, String toAssignee) {
        return taskDefKey + ":" + (toAssignee != null ? toAssignee : "");
    }

    /**
     * Get request-update map key
     *
     * <AUTHOR>
     */
    private String getRuTaskMapKey(Object taskDefKey, Object assignee) {
        return (taskDefKey != null ? taskDefKey.toString() : "") + ":" + (assignee != null ? assignee.toString() : "");
    }

    public void camundaRu(String procInstId, List<String> recreateTasks, Set<String> listAffected) {
        try {
            Map<String, Object> body = new HashMap<>();
            body.put("skipCustomListeners", true);
            body.put("skipIoMappings", true);
            List<Map<String, String>> listInstruction = new ArrayList<>();
            if (recreateTasks != null) {
                for (String task : recreateTasks) {
                    Map<String, String> ruObject = new HashMap<>();
                    ruObject.put("type", "startBeforeActivity");
                    ruObject.put("activityId", task);
                    listInstruction.add(ruObject);
                }
            }
            if (listAffected != null && !listAffected.isEmpty()) {
                for (String added : listAffected) {
                    Map<String, String> ruObject = new HashMap<>();
                    ruObject.put("type", "cancel");
                    ruObject.put("activityId", added);
                    listInstruction.add(ruObject);
                }
            }
            body.put("instructions", listInstruction);
            body.put("annotation", "Modified to resolve an error.");
            camundaEngineService.modifyProcessInstance(procInstId, body);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * Save bpm-history pre-process request update
     *
     * <AUTHOR>
     */
    private void saveHistoryPreRequestUpdate(Set<String> listTaskKey, RequestUpdateDto requestUpdateDto, Map<String, List<BpmTask>> groupTaskByKey, String startKey, String createdUser) {
        List<HistoryDto> historyDtoList = new ArrayList<>();

        BpmTask currentTask = groupTaskByKey.get(requestUpdateDto.getCurrTaskKey()) != null ? groupTaskByKey.get(requestUpdateDto.getCurrTaskKey()).get(0) : null;
        BpmTask ruTask = groupTaskByKey.get(requestUpdateDto.getRuTaskKey()) != null ? groupTaskByKey.get(requestUpdateDto.getRuTaskKey()).get(0) : null;

        HistoryDto newHistory = createHistoryDto(listTaskKey, requestUpdateDto, currentTask, TaskConstants.HistoryAction.REQUEST_UPDATE.code, requestUpdateDto.getUser());
        HistoryDto getRUHistory = createHistoryDto(listTaskKey, requestUpdateDto, ruTask, TaskConstants.HistoryAction.GET_REQUEST_UPDATE.code, createdUser);

        if (currentTask != null) {
            newHistory.setFromTask(currentTask.getTaskId());
            newHistory.setFromTaskKey(currentTask.getTaskDefKey());
            newHistory.setReceivedTime(currentTask.getTaskCreatedTime());

            getRUHistory.setFromTask(currentTask.getTaskId());
            getRUHistory.setFromTaskKey(currentTask.getTaskDefKey());
            getRUHistory.setReceivedTime(LocalDateTime.now());
        }

        if (requestUpdateDto.getIsStartEvent()) {
            newHistory.setToTask("startTicket");
            newHistory.setToTaskKey(startKey);

            getRUHistory.setToTask("startTicket");
            getRUHistory.setToTaskKey(startKey);
        } else {
            newHistory.setToTask(ruTask != null ? ruTask.getTaskId() : null);
            newHistory.setToTaskKey(ruTask != null ? ruTask.getTaskDefKey() : null);

            getRUHistory.setToTask(ruTask != null ? ruTask.getTaskId() : null);
            getRUHistory.setToTaskKey(ruTask != null ? ruTask.getTaskDefKey() : null);
        }

        // add attach files
        String attachFiles = null;
        String attachFilesName = null;
        String attachFilesSize = null;
        if (!ValidationUtils.isNullOrEmpty(requestUpdateDto.getAttachFiles())) {
            attachFiles = String.join(",", requestUpdateDto.getAttachFiles());
        }
        if (!ValidationUtils.isNullOrEmpty(requestUpdateDto.getAttachFilesName())) {
            attachFilesName = String.join(",", requestUpdateDto.getAttachFilesName());
        }
        if (!ValidationUtils.isNullOrEmpty(requestUpdateDto.getAttachFilesSize())) {
            attachFilesSize = String.join(",", requestUpdateDto.getAttachFilesSize());
        }

        newHistory.setAttachFiles(attachFiles);
        newHistory.setAttachFilesName(attachFilesName);
        newHistory.setAttachFilesSize(attachFilesSize);
        getRUHistory.setAttachFiles(attachFiles);
        getRUHistory.setAttachFilesName(attachFilesName);
        getRUHistory.setAttachFilesSize(attachFilesSize);

        historyDtoList.add(newHistory);
        historyDtoList.add(getRUHistory);
        bpmHistoryManager.saveAllHistory(historyDtoList);
    }

    /**
     * Save bpm-history post-process request update
     *
     * <AUTHOR>
     */
    private void saveHistoryPostRequestUpdate(List<BpmTask> listTask, BpmTask bpmTask, List<String> affected, Long ticketId, String account, String reason, String strAttachFile) {
        if (!ValidationUtils.isNullOrEmpty(listTask)) {
            String affectedStr = convertListToStringArray(affected);

            List<HistoryDto> historyDtoList = new ArrayList<>();
            listTask.forEach(task -> {
                Boolean checkTaskAffectedComplete = bpmTaskRepository.existsBpmTaskByTaskIdAndTaskStatus(task.getTaskId(), TaskConstants.Status.COMPLETED.code);
                // Chỉ lưu lịch sử các task trong list affected
                if (!Objects.equals(task.getTaskId(), bpmTask.getTaskId()) && affected.contains(task.getTaskDefKey())) {
                    HistoryDto newHistory = new HistoryDto();
                    newHistory.setTicketId(ticketId);
                    newHistory.setAffectedTask(affectedStr);
                    newHistory.setFromTask(bpmTask.getTaskId());
                    newHistory.setFromTaskKey(bpmTask.getTaskDefKey());
                    newHistory.setToTask(task.getTaskId());
                    newHistory.setToTaskKey(task.getTaskDefKey());
                    newHistory.setAction(TaskConstants.HistoryAction.AFFECTED_BY_RU.code);
                    newHistory.setProcInstId(task.getTaskProcInstId());
                    newHistory.setActionUser(account);
                    newHistory.setTaskDefKey(task.getTaskDefKey());
                    newHistory.setTaskInstId(task.getTaskId());
                    newHistory.setNote(reason);
                    newHistory.setAttachFiles(strAttachFile);
                    // add taskAssignee để map khi thực hiện lại task affected
                    newHistory.setTaskAssignee(task.getTaskAssignee());
                    newHistory.setOldTaskId(checkTaskAffectedComplete ? task.getTaskId() : null);
                    newHistory.setOldProcInstId(checkTaskAffectedComplete ? task.getTaskProcInstId() : null);
                    newHistory.setReceivedTime(task.getTaskCreatedTime());

                    // get action_user_info
                    List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(account);
                    if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
                        Map<String, Object> actionUserInfo = new HashMap<>();
                        String userTitle = lstUserTitle.stream()
                                .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                                .map(title -> {
                                    String strTitle = StringUtil.nvl(title.getTitle(), "");
                                    int concurrently = title.getConcurrently();
                                    return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                                })
                                .collect(Collectors.joining(" "));
                        actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
                        actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
                        newHistory.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
                    }
                    historyDtoList.add(newHistory);
                }
            });

            bpmHistoryManager.saveAllHistory(historyDtoList);
        }
    }

    private void saveTaskHistoryPostRequestUpdate(List<BpmTask> listTask, BpmTask ruTask,
                                                  Map<String, Map<String, Object>> mapData,
                                                  Map<String, ChangeAssigneeHistory> mapChangeAssignee,
                                                  Map<String, String> mapTaskDefKeyToOrgAssignee,
                                                  Long ticketId) {
        if (!ValidationUtils.isNullOrEmpty(listTask)) {
            List<HistoryDto> historyDtoList = new ArrayList<>();
            listTask.forEach(task -> {
                Map<String, Object> taskObj = getTaskData(mapData, mapChangeAssignee, mapTaskDefKeyToOrgAssignee, task);
                if (taskObj != null) {
                    HistoryDto newCreatedHis = new HistoryDto();
                    newCreatedHis.setTicketId(ticketId);
                    newCreatedHis.setFromTask(taskObj.get("id").toString());
                    newCreatedHis.setToTask(null);
                    newCreatedHis.setAction(TaskConstants.HistoryAction.RE_CREATED_BY_RU.code);
                    newCreatedHis.setProcInstId(task.getTaskProcInstId());
                    newCreatedHis.setActionUser(null);
                    newCreatedHis.setTaskDefKey(taskObj.get("taskDefKey").toString());
                    newCreatedHis.setTaskInstId(task.getTaskId());
                    newCreatedHis.setOldTaskId(ruTask.getTaskId());
                    historyDtoList.add(newCreatedHis);
                }
            });
            bpmHistoryManager.saveAllHistory(historyDtoList);
        }
    }

    /**
     * Create bpm-history DTO
     *
     * <AUTHOR>
     */
    public HistoryDto createHistoryDto(Set<String> listTaskKey, RequestUpdateDto requestUpdateDto, BpmTask bpmTask, String action, String actionUser) {
        String affectedStr = convertListToStringArray(new ArrayList<>(listTaskKey));

        HistoryDto dto = new HistoryDto();

        if (requestUpdateDto != null) {
            dto.setTicketId(requestUpdateDto.getTicketId());
            dto.setNote(requestUpdateDto.getRuReason());
            dto.setProcInstId(requestUpdateDto.getProcInstId());
            dto.setActionUser(actionUser);

            // get action_user_info
            List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(actionUser);
            if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
                Map<String, Object> actionUserInfo = new HashMap<>();
                String userTitle = lstUserTitle.stream()
                        .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                        .map(title -> {
                            String strTitle = StringUtil.nvl(title.getTitle(), "");
                            int concurrently = title.getConcurrently();
                            return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                        })
                        .collect(Collectors.joining(" "));
                actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
                actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
                dto.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
            }
        }

        if (bpmTask != null) {
            dto.setTaskInstId(bpmTask.getTaskId());
            dto.setTaskDefKey(bpmTask.getTaskDefKey());
        }

        dto.setAffectedTask(affectedStr);
        dto.setAction(action);

        return dto;
    }

    public List<Map<String, Object>> preSequelTask(String taskKey, String procDefId) {
        try {
            Document doc = getXml(procDefId);
            NodeList nodes = doc.getElementsByTagName("bpmn:userTask");
            NodeList nodesSequence = doc.getElementsByTagName("bpmn:sequenceFlow");
            NodeList nodesGateway = doc.getElementsByTagName("bpmn:parallelGateway");
            NodeList startNode = doc.getElementsByTagName("bpmn:startEvent");

            Set<Map<String, Object>> setMapTaskKey = new HashSet<>();
            List<String> listCheck = new ArrayList<>();

            Element startEl = (Element) startNode.item(0);
            String startKey = startEl.getAttributes().getNamedItem("id").getNodeValue();
            Map<String, Object> mapStart = new HashMap<>();
            mapStart.put("label", "Bắt Đầu");
            mapStart.put("taskDefKey", startKey);
            for (int i = 0; i < nodes.getLength(); i++) {
                Element taskEl = (Element) nodes.item(i);
                NodeList nodeIngoing = taskEl.getElementsByTagName("bpmn:incoming");
                String currentKey = taskEl.getAttributes().getNamedItem("id").getNodeValue();
                if (currentKey.equals(taskKey)) {
                    for (int j = 0; j < nodeIngoing.getLength(); j++) {
                        Element outEl = (Element) nodeIngoing.item(j);
                        String outKey = outEl.getTextContent();
                        listCheck.add(outKey);
                    }
                }
            }
            getPreSequelTask(nodes, nodesSequence, nodesGateway, listCheck, setMapTaskKey);
            List<Map<String, Object>> listResult = new ArrayList<>(setMapTaskKey);
            listResult.add(mapStart);
            return listResult;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * <AUTHOR>
     */
    private String convertListToStringArray(List<String> strings) {
        return ObjectUtils.toJson(strings);
    }

    public Boolean getPreSequelTask(NodeList nodes, NodeList nodesSequence,
                                    NodeList nodeGateways, List<String> listCheck, Set<Map<String, Object>> listTaskKey) {
        try {
            if (!listCheck.isEmpty()) {
                for (int i = 0; i < nodes.getLength(); i++) {
                    Element element = (Element) nodes.item(i);
                    String taskDefKey = element.getAttributes().getNamedItem("id").getNodeValue();
                    if (listCheck.contains(taskDefKey)) {
                        String taskName = element.getAttributes().getNamedItem("name").getNodeValue();
                        Map<String, Object> mapData = new HashMap<>();
                        listCheck.remove(taskDefKey);
                        mapData.put("label", taskName);
                        mapData.put("taskDefKey", taskDefKey);
                        listTaskKey.add(mapData);
                        NodeList nodeIn = element.getElementsByTagName("bpmn:incoming");
                        for (int j = 0; j < nodeIn.getLength(); j++) {
                            Element eleIncom = (Element) nodeIn.item(j);
                            String nextKey = eleIncom.getTextContent();
                            listCheck.add(nextKey);
                            getPreSequelTask(nodes, nodesSequence, nodeGateways, listCheck, listTaskKey);
                        }
                    }
                }
                for (int i = 0; i < nodesSequence.getLength(); i++) {
                    Element element = (Element) nodesSequence.item(i);
                    String seqKey = element.getAttributes().getNamedItem("id").getNodeValue();
                    String targetRef = element.getAttributes().getNamedItem("targetRef").getNodeValue();
                    if (listCheck.contains(targetRef) || listCheck.contains(seqKey)) {
                        String sourceRef = element.getAttributes().getNamedItem("sourceRef").getNodeValue();
                        listCheck.remove(seqKey);
                        listCheck.remove(targetRef);
                        listCheck.add(sourceRef);
                        getPreSequelTask(nodes, nodesSequence, nodeGateways, listCheck, listTaskKey);
                    }
                }
                for (int i = 0; i < nodeGateways.getLength(); i++) {
                    Element element = (Element) nodes.item(i);
                    String gateKey = element.getAttributes().getNamedItem("id").getNodeValue();

                    if (listCheck.contains(gateKey)) {
                        NodeList nodeIncom = element.getElementsByTagName("bpmn:incoming");
                        listCheck.remove(gateKey);
                        for (int k = 0; k < nodeIncom.getLength(); k++) {
                            Element eleIn = (Element) nodeIncom.item(k);
                            String inVal = eleIn.getTextContent();
                            listCheck.add(inVal);
                            getPreSequelTask(nodes, nodesSequence, nodeGateways, listCheck, listTaskKey);
                        }
                    }
                }
                return true;
            } else {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public Boolean getAffectedTask(String key, String keyEnd, NodeList nodes, NodeList nodesSequence,
                                   NodeList nodeGateways, Set<String> list, int count) {
        try {
            if (!key.equals(keyEnd)) {
                for (int i = 0; i < nodes.getLength(); i++) {
                    Element element = (Element) nodes.item(i);
                    String taskDefKey = element.getAttributes().getNamedItem("id").getNodeValue();
                    if (taskDefKey.equals(key) && !taskDefKey.equals(keyEnd)) {
                        NodeList nodeIncoming = element.getElementsByTagName("bpmn:incoming");
                        for (int j = 0; j < nodeIncoming.getLength(); j++) {
                            Element eleIncom = (Element) nodeIncoming.item(j);
                            String nextKey = eleIncom.getTextContent();
                            if (count != 0) {
                                list.add(taskDefKey);
                            }
                            getAffectedTask(nextKey, keyEnd, nodes, nodesSequence, nodeGateways, list, count++);
                        }
                    }
                }
                for (int i = 0; i < nodesSequence.getLength(); i++) {
                    Element element = (Element) nodesSequence.item(i);
                    String targetRef = element.getAttributes().getNamedItem("targetRef").getNodeValue();
                    if (targetRef.equals(key) && !targetRef.equals(keyEnd)) {
                        String sourceRef = element.getAttributes().getNamedItem("sourceRef").getNodeValue();
                        getAffectedTask(sourceRef, keyEnd, nodes, nodesSequence, nodeGateways, list, count++);
                    }
                }
                for (int i = 0; i < nodeGateways.getLength(); i++) {
                    Element element = (Element) nodes.item(i);
                    boolean checkValOut = false;
                    NodeList nodeOutgoing = element.getElementsByTagName("bpmn:outgoing");
                    NodeList nodeIncoming = element.getElementsByTagName("bpmn:incoming");
                    for (int j = 0; j < nodeOutgoing.getLength(); j++) {
                        Element eleOutGoing = (Element) nodeOutgoing.item(j);
                        String valOutGoing = eleOutGoing.getTextContent();
                        if (valOutGoing.equals(key)) {
                            checkValOut = true;
                        }
                    }
                    if (checkValOut) {
                        for (int k = 0; k < nodeIncoming.getLength(); k++) {
                            Element eleIncom = (Element) nodeIncoming.item(k);
                            String incomingVal = eleIncom.getTextContent();
                            getAffectedTask(incomingVal, keyEnd, nodes, nodesSequence, nodeGateways, list, count++);
                        }
                    }
                }
            } else {
                return true;
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public Boolean getListTask(String key, String keyEnd, NodeList startNode, NodeList endNode,
                               NodeList nodes, NodeList nodesSequence,
                               NodeList nodeGateways, Set<String> list, Set<String> listFirst,
                               List<String> listStartOut, boolean isStart) {
        try {
            boolean checkStart = false;
            if (isStart) {
                checkStart = true;
                Element startEle = (Element) startNode.item(0);
                NodeList startOutNode = startEle.getElementsByTagName("bpmn:outgoing");
                for (int i = 0; i < startOutNode.getLength(); i++) {
                    Element eleOutNode = (Element) startOutNode.item(i);
                    listStartOut.add(eleOutNode.getTextContent());
                }
            }
            if (!Objects.equals(key, keyEnd)) {
                for (int i = 0; i < nodes.getLength(); i++) {
                    Element element = (Element) nodes.item(i);
                    String taskDefKey = element.getAttributes().getNamedItem("id").getNodeValue();
                    if (taskDefKey.equals(key) || checkStart) {
                        NodeList nodeIncoming = element.getElementsByTagName("bpmn:incoming");
                        if (listStartOut.contains(taskDefKey)) {
                            listFirst.add(taskDefKey);
                            listStartOut.remove(taskDefKey);
                        }
                        for (int j = 0; j < nodeIncoming.getLength(); j++) {
                            Element eleIncom = (Element) nodeIncoming.item(j);
                            String nextKey = eleIncom.getTextContent();
                            list.add(nextKey);
                            getListTask(nextKey, keyEnd, startNode, endNode, nodes, nodesSequence, nodeGateways, list, listFirst, listStartOut, false);
                        }
                    }
                }
                for (int i = 0; i < nodesSequence.getLength(); i++) {
                    Element element = (Element) nodesSequence.item(i);
                    String sourceRef = element.getAttributes().getNamedItem("sourceRef").getNodeValue();
                    if (sourceRef.equals(key) && !sourceRef.equals(keyEnd)) {
                        String targetRef = element.getAttributes().getNamedItem("targetRef").getNodeValue();
                        getListTask(targetRef, keyEnd, startNode, endNode, nodes, nodesSequence, nodeGateways, list, listFirst, listStartOut, false);
                    }
                }
                for (int i = 0; i < nodeGateways.getLength(); i++) {
                    Element element = (Element) nodes.item(i);
                    boolean checkValOut = false;
                    boolean checkAdd = false;
                    NodeList nodeOutgoing = element.getElementsByTagName("bpmn:outgoing");
                    NodeList nodeIncoming = element.getElementsByTagName("bpmn:incoming");
                    for (int j = 0; j < nodeIncoming.getLength(); j++) {
                        Element eleIncom = (Element) nodeIncoming.item(j);
                        String incomingVal = eleIncom.getTextContent();
                        if (incomingVal.equals(key)) {
                            checkValOut = true;
                        }
                        if (listStartOut.contains(incomingVal)) {
                            checkValOut = true;
                            checkAdd = true;
                            listStartOut.remove(incomingVal);
                        }
                    }
                    if (checkValOut) {
                        for (int k = 0; k < nodeOutgoing.getLength(); k++) {
                            Element eleOutGoing = (Element) nodeOutgoing.item(k);
                            String valOutGoing = eleOutGoing.getTextContent();
                            if (checkAdd) {
                                listStartOut.add(valOutGoing);
                            }
                            getListTask(valOutGoing, keyEnd, startNode, endNode, nodes, nodesSequence, nodeGateways, list, listFirst, listStartOut, false);
                        }
                    }
                }
            } else {
                return true;
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

}
