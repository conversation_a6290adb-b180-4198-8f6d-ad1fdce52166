
package vn.fis.eapprove.business.domain.api.repository;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.api.entity.ApiManagement;


import jakarta.persistence.Tuple;
import java.util.List;
import java.util.Optional;


@Repository
public interface ApiManagementRepository extends JpaRepository<ApiManagement, Long>, JpaSpecificationExecutor<ApiManagement> {
    Optional<ApiManagement> findTopByNameLikeOrderByIdDesc(String apiActionName);

    @Query("SELECT c FROM ApiManagement c WHERE LOWER(c.name) = LOWER(:nameApiAction) AND c.createdTime is not null")
    List<ApiManagement> CheckExistName(String nameApiAction);

    @Query("SELECT c FROM ApiManagement c WHERE c.type = 'BASE_URL' AND c.status = 1 ")
    List<ApiManagement> listBaseUrl();

    @Query("SELECT c FROM ApiManagement c WHERE c.type = 'AUTHEN' AND c.status = 1 ")
    List<ApiManagement> listApiAuthen();


    @Query("SELECT c from ApiManagement c where c.baseUrlId = :apiId and c.type = 'NORMAL' and c.status = 1")
    List<ApiManagement> findBaseUrlId(Long apiId);

    @Query("select am.url from ApiManagement am where id = (select baseUrlId from ApiManagement where id = :apiId)")
    String findBaseUrlAuthenApi(Long apiId);

    @Query(value = "select distinct " +
            "    am.id,bp.name " +
            "from api_management am " +
            "         left join bpm_procdef_api bpa on bpa.api_id = am.id " +
            "         left join bpm_procdef bp on bpa.bpm_procdef_id = bp.id " +
            "         left join bpm_procdef_view_file_api bpv on  CONVERT(bpv.proc_def_id USING utf8) = bp.proc_def_id " +
            "where am.id in(:ids) and bp.name is not null ",nativeQuery = true)
    List<Tuple> countInProcDef(List<Long> ids);

    List<ApiManagement> findAllByIdInAndStatus(List<Long> ids, Integer status);

    ApiManagement getApiManagementById(Long id);
}