package vn.fis.eapprove.business.domain.assign.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.assign.entity.AssignHistory;


import java.util.List;

@Repository
public interface AssignHistoryRepository extends JpaRepository<AssignHistory, Long>, JpaSpecificationExecutor<AssignHistory> {
    Long countByAssignId(Long assignId);

    List<AssignHistory> findByAssignId(Long id);

}
