package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Data;

import jakarta.persistence.*;

@Data
@Entity
@Table(name = "bpm_template_print_config")
public class BpmTemplatePrintConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "TYPE")
    private String type;

    @Column(name = "JSON_CONFIG")
    private String jsonConfig;
}
