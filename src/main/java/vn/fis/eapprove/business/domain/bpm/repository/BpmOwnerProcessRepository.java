package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmOwnerProcess;


import java.util.List;

@Repository
public interface BpmOwnerProcessRepository extends JpaRepository<BpmOwnerProcess, Long> {

    void deleteBpmOwnerProcessByProcDefId(Long id);

    List<BpmOwnerProcess> getBpmOwnerProcessByProcDefId(Long procDefId);
    List<BpmOwnerProcess> getAllByProcDefIdIn(List<Long> procDefId);

    List<BpmOwnerProcess> findBpmOwnerProcessByVerProcDefId(String procDefId);

    @Query("SELECT b FROM BpmOwnerProcess b " +
            "JOIN BpmProcdef d ON b.procDefId = d.id " +
            "WHERE d.procDefId = :procDefId")
    List<BpmOwnerProcess> getListOwnerByProcDefId(@Param("procDefId") String procDefId);

    @Query("SELECT a FROM BpmOwnerProcess a "
            + "WHERE a.procDefId IN ("
            + "SELECT b.id FROM BpmProcdef b "
            + "JOIN ServicePackage c ON b.id = c.processId "
            + "JOIN BpmProcInst d ON d.serviceId = c.id "
            + "WHERE d.ticketId = :bpmProcInstId"
            + ")")
    List<BpmOwnerProcess> getAllByBpmProcInstId(@Param("bpmProcInstId") Long bpmProcInstId);

    @Query("select a.idUser from BpmOwnerProcess a where a.procDefId = :processId")
    List<String> getOwnerByProcDefId(Long processId);
}
