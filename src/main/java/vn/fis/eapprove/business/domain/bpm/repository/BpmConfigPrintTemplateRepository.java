package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmConfigPrintTemplate;


import java.util.List;

@Repository
public interface BpmConfigPrintTemplateRepository extends JpaRepository<BpmConfigPrintTemplate, Long>, JpaSpecificationExecutor<BpmConfigPrintTemplate> {
    BpmConfigPrintTemplate findBpmPrintTemplateById(Long id);

    BpmConfigPrintTemplate findBpmPrintTemplateByName(String id);

    @Query("SELECT bpm FROM BpmConfigPrintTemplate as bpm")
    List<BpmConfigPrintTemplate> getAll();
}
