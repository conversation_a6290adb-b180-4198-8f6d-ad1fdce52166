package vn.fis.eapprove.business.domain.submission.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Set;

@Entity
@Table(name = "submission_type")
@Data
public class SubmissionType {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "type_name")
    private String typeName;

    @Column(name = "department_create")
    private String departmentCreate;

    @Column(name = "description")
    private String description;

    @Column(name = "share_with")
    private String shareWith;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "modified_user")
    private String modifiedUser;

    @Column(name = "modified_date")
    private LocalDateTime modifiedDate;

    @Column(name = "scope_apply")
    private int scopeApply;

    @Column(name = "status")
    private Integer status;
    @Column(name = "company_code")
    private String companyCode;
    @Column(name = "company_name")
    private String companyName;

    @OneToMany(mappedBy = "submissionType")
    @JsonIgnore
    private Set<PermissionDataManagement> permissionDataManagements;

    @OneToMany(mappedBy = "submissionType")
    @JsonIgnore
    private Set<SharedUser> sharedUsers;

}
