package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "bpm_discussion")
public class BpmDiscussion {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "PARENT_ID")
    private Long parentId;

    @Column(name = "PROC_INST_ID")
    private String procInstId;

    @Column(name = "GROUP_ID")
    private Long groupId;

    @Column(name = "CONTENT")
    private String content;

    @Column(name = "CREATED_USER")
    private String createdUser;

    @Column(name = "CREATED_TIME")
    private Date createdDate;

    @Column(name = "TYPE_DISCUSSION")
    private Long typeDiscussion;

    @Column(name = "TICKET_ID")
    private Long ticketId;

    @Column(name = "IS_ADDITIONAL_REQUEST")
    private Boolean isAdditionalRequest;

    @Column(name = "IS_ADDITIONAL_REQUEST_COMPLETED")
    private Boolean isAdditionalRequestCompleted;

    @Column(name = "status_request")
    private Integer statusRequest;
}
