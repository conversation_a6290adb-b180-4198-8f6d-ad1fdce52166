package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTpSignZone;
import vn.fis.eapprove.business.dto.BpmTpSignZoneDto;
import vn.fis.eapprove.business.model.BpmTpSignZoneResponse;


import jakarta.persistence.Tuple;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
public interface BpmTpSignZoneRepository extends JpaRepository<BpmTpSignZone, Long>, JpaSpecificationExecutor<BpmTpSignZone> {

    List<BpmTpSignZone> findBpmTpSignZoneByBpmTemplatePrintId(Long id);

    List<BpmTpSignZone> findBpmTpSignZoneByBpmTemplatePrintIdAndProcInstId(Long bpmTemplatePrintId, String procInstId);

    List<BpmTpSignZone> findBpmTpSignZoneByProcInstId(String procInstId);

    BpmTpSignZone getBpmTpSignZoneById(Long id);

    @Query(value = "SELECT a FROM BpmTpSignZone a WHERE a.email LIKE '@%' AND a.procInstId = :procInstId")
    List<BpmTpSignZone> getAllSignZonesNotAssignEmail(@Param("procInstId") String procInstId);

    BpmTpSignZone findByProcInstIdAndTaskDefKeyAndEmail(@Param("procInstId") String procInstId, @Param("taskDefKey") String taskDefKey, @Param("email") String email);

    @Query(value = "SELECT a FROM BpmTpSignZone a WHERE a.procInstId = :procInstId AND a.taskDefKey = :taskDefKey AND (a.email IS NULL OR a.email = '' OR a.email like '%@candidateUsers_%')")
    List<BpmTpSignZone> getAllSignZonesWithNullEmail(@Param("procInstId") String procInstId, @Param("taskDefKey") String taskDefKey);

    List<BpmTpSignZone> getBpmTpSignZonesByProcInstIdAndTaskDefKey(@Param("procInstId") String procInstId, @Param("taskDefKey") String taskDefKey);

    @Query("SELECT a FROM BpmTpSignZone a WHERE a.procInstId = :procInstId AND a.taskDefKey IN (:taskDefKeys)")
    List<BpmTpSignZone> getRequestUpdateSignZones(@Param("procInstId") String procInstId, @Param("taskDefKeys") Set<String> taskDefKeys);

    @Query("SELECT a FROM BpmTpSignZone a WHERE a.procInstId = :procInstId AND a.taskDefKey = :taskDefKey AND a.email = :username")
    BpmTpSignZone getSignZoneByTaskDefKeyAndUser(@Param("procInstId") String procInstId, @Param("taskDefKey") String taskDefKey, @Param("username") String username);

    @Query("SELECT a as signZone, c.name as fileName "
            + "FROM BpmTpSignZone a "
            + "JOIN BpmProcInst b ON a.procInstId = b.ticketProcInstId AND (COALESCE(:ticketStatus, NULL) IS NULL OR b.ticketStatus IN (:ticketStatus)) "
            + "JOIN BpmTemplatePrint c ON a.bpmTemplatePrintId = c.id "
            + "WHERE a.signedFile IS NOT NULL AND (:procInstId IS NULL OR a.procInstId = :procInstId) "
            + "ORDER BY a.id")
    List<Tuple> getSignedFiles(@Param("procInstId") String procInstId, @Param("ticketStatus") List<String> ticketStatus);

    @Query("SELECT new vn.fis.eapprove.business.dto.BpmTpSignZoneDto("
            + "a.id, a.bpmTemplatePrintId, a.taskDefKey, a.email, a.signedFile, c.name"
            + ") "
            + "FROM BpmTpSignZone a "
            + "JOIN BpmTemplatePrint c ON a.bpmTemplatePrintId = c.id "
            + "WHERE a.procInstId = :procInstId "
            + "AND (:taskDefKey IS NULL OR a.taskDefKey = :taskDefKey)")
    List<BpmTpSignZoneDto> findAll(@Param("procInstId") String procInstId, @Param("taskDefKey") String taskDefKey);

    @Query("SELECT a FROM BpmTpSignZone a "
            + "JOIN BpmTemplatePrint c ON a.bpmTemplatePrintId = c.id "
            + "WHERE a.procInstId = :procInstId "
            + "AND (:taskDefKey IS NULL OR a.taskDefKey = :taskDefKey)")
    List<BpmTpSignZone> findOne(@Param("procInstId") String procInstId, @Param("taskDefKey") String taskDefKey);

    @Query("SELECT new vn.fis.eapprove.business.model.BpmTpSignZoneResponse(" +
            "bp.ticketId,bp.requestCode,a.sign,a.position,a.lastName,a.firstName,a.signedDate" +
            ") FROM BpmTpSignZone a join BpmProcInst bp on a.procInstId = bp.ticketProcInstId "
            + "JOIN BpmTemplatePrint c ON a.bpmTemplatePrintId = c.id "
            + "WHERE (bp.ticketId in (:ticketId) OR bp.requestCode in (:requestCode)) "
            + "AND (:taskDefKey IS NULL OR a.taskDefKey = :taskDefKey)")
    List<BpmTpSignZoneResponse> findAllByTicketIdOrRequestCode(
            @Param("ticketId") List<Long> ticketId,
            @Param("requestCode") List<String> requestCode,
            @Param("taskDefKey") String taskDefKey);

    @Query(value = "select sign as sign, position as position, first_name as firstname, last_name as lastname" +
            " from bpm_tp_sign_zone " +
            " where email = :username " +
            " and sign is not null " +
            " order by id desc limit 1", nativeQuery = true)
    Map<String, Object> getSignByUsername(@Param("username") String username);
}
