package vn.fis.eapprove.business.domain.bpm.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.simpleframework.xml.Transient;
import org.simpleframework.xml.core.Persist;

import java.time.LocalDateTime;

@Entity
@Table(name = "bpm_procdef_legislative_config")
@Data
public class BpmProcdefLegislativeStatusConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "task_def_key")
    private String taskDefKey;

    @Column(name = "task_status")
    private String taskStatus;

    @Column(name = "legislative_status")
    private String legislativeStatus;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "updated_user")
    private String updatedUser;

    @Column(name = "bpm_procdef_id")
    private Long bpmProcdefId;

    @Column(name = "proc_def_id")
    private String procDefId;

    @Column(name = "status")
    private Boolean status;
}
