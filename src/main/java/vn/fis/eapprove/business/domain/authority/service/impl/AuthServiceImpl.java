package vn.fis.eapprove.business.domain.authority.service.impl;


import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.constant.Constant;
import vn.fis.eapprove.business.domain.authority.service.AuthService;
import vn.fis.eapprove.business.dto.AccountInfoDto;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.constants.MapKeyEnum;
import vn.fis.spro.common.helper.RestHelper;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service("ImplV1")
@Slf4j
public class AuthServiceImpl implements AuthService {

    private final CredentialHelper credentialHelper;
    private final SproProperties sproProperties;
    private final RestHelper restHelper;

    @Autowired
    public AuthServiceImpl(SproProperties sproProperties,
                           CredentialHelper credentialHelper,
                           RestHelper restHelper) {
        this.credentialHelper = credentialHelper;
        this.sproProperties = sproProperties;
        this.restHelper = restHelper;
    }



    public HttpHeaders getHeaders()  {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        //headers.set("realm", credentialHelper.getRealm());
        headers.set("key_role", Constant.KEY_ROLE_PUBLIC);
        String token = credentialHelper.getJWTToken();
        if (!ValidationUtils.isNullOrEmpty(token)) {
            headers.setBearerAuth(token);
        }

        return headers;
    }

    @Override
    public List<String> getListCompanyCodeByUser(String username) {
        long startTime = System.currentTimeMillis();
        List<String> lstCompanyCode = new ArrayList<>();

        log.info("Starting get list company code, username={}", username);

        String authUrl = sproProperties.getServiceUrls().get(MapKeyEnum.AUTH.key) + "/perm/get-company-codes/" + username;
        try {
            ResponseEntity<List<String>> response = restHelper.get(authUrl, getHeaders(), new ParameterizedTypeReference<>() {
            });
            lstCompanyCode = response.getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            log.info("Finished get list company code, username={} in {} ms ", username, System.currentTimeMillis() - startTime);
        }

        return lstCompanyCode;
    }

    @Override
    public List<String> getListCompanyCodeMemberAdminByUser(String username) {
        long startTime = System.currentTimeMillis();
        List<String> lstCompanyCode = new ArrayList<>();

        log.info("Starting get list company code member admin, username={}", username);

        String authUrl = sproProperties.getServiceUrls().get(MapKeyEnum.AUTH.key) + "/perm/get-company-codes-member-admin/" + username;
        try {
            ResponseEntity<List<String>> response = restHelper.get(authUrl, getHeaders(), new ParameterizedTypeReference<>() {
            });
            lstCompanyCode = response.getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            log.info("Finished get list company code member admin, username={} in {} ms ", username, System.currentTimeMillis() - startTime);
        }

        return lstCompanyCode;
    }

    @Override
    public List<AccountInfoDto> getAccountByRoleAndUsername() {
        long startTime = System.currentTimeMillis();
        StringBuilder builder = new StringBuilder();
        builder.append(sproProperties.getServiceUrls().get(MapKeyEnum.AUTH.key)).append("/getAccountByRoleAndUsername").append("?roles=-1");
        String authUrl = builder.toString();
        try {
            ResponseEntity<List<AccountInfoDto>> response = restHelper.post(authUrl, getHeaders(),new ArrayList<>(), new ParameterizedTypeReference<>() {
            });
            return response.getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            log.info("Finished get list getAccountByRoleAndUsername, username={} in {} ms ", System.currentTimeMillis() - startTime);
        }

        return null;
    }

    @Override
    public Object getAllSystemAuthorizationGroup() {
        long startTime = System.currentTimeMillis();
        String url = sproProperties.getServiceUrls().get(MapKeyEnum.AUTH.key) + "/getAllSystemAuthorizationGroup";
        try {
            // call service
            ResponseDto<Object> responseData = restHelper.getAndGetBody(url,
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished getAllSystemAuthorizationGroup default in {} ms", System.currentTimeMillis() - startTime);
        }

        return Collections.emptyList();
    }

    @Override
    public Object getAllPermissionGroup() {
        long startTime = System.currentTimeMillis();
        String url = sproProperties.getServiceUrls().get(MapKeyEnum.AUTH.key) + "/perm/getAllPermissionGroup";
        try {
            // call service
            ResponseDto<Object> responseData = restHelper.getAndGetBody(url,
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished getAllPermissionGroup default in {} ms", System.currentTimeMillis() - startTime);
        }

        return Collections.emptyList();
    }
}
