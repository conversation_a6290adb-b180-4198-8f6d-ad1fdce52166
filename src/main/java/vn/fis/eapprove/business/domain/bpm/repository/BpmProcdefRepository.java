package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef;
import vn.fis.eapprove.business.model.response.ChildResponse;


import jakarta.persistence.Tuple;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface BpmProcdefRepository extends JpaRepository<BpmProcdef, Long>, JpaSpecificationExecutor<BpmProcdef> {

    List<BpmProcdef> getBpmProcdefByKey(String key);

    BpmProcdef getBpmProcDefById(Long id);

    @Query("SELECT c FROM BpmProcdef c WHERE c.procDefId =:procDefId AND (c.status <> 'DELETED')")
    BpmProcdef getByProcdefId(String procDefId);

    List<BpmProcdef> findBpmProcdefsByProcDefIdIn(List<String> procDefId);

    BpmProcdef findBpmProcdefByProcDefId(String procDefId);

    List<BpmProcdef> findBpmProcdefById(Long id);

    List<BpmProcdef> findBpmProcdefByNameContainingAndStatusIn(String name, List<String> listStatus);

    List<BpmProcdef> findBpmProcdefByStatusIn(List<String> listStatus);

    @Query("SELECT c FROM BpmProcdef c WHERE c.name =:name AND (c.status <> 'DELETED')")
    List<BpmProcdef> getBpmProcdefByName(String name);

    List<BpmProcdef> getBpmProcdefByNameContainingIgnoreCase(String name);

    @Query("SELECT b FROM BpmProcdef b WHERE b.id IN :lsId")
    List<BpmProcdef> getBpmProcdefListId(@Param("lsId") List<Long> id);

    @Modifying
    @Query("UPDATE BpmProcdef b SET b.status = 'DEACTIVE' WHERE b.id = :processId and b.status != 'DELETED'")
    void resetStatus(@Param("processId") Long processId);

    List<BpmProcdef> getBpmProcdefByProcDefIdIn(List<String> ids);


    Page<BpmProcdef>  getBpmProcdefByProcDefIdIn(List<String> procDefIds, Pageable pageable);

    @Modifying
    @Query("SELECT DISTINCT s.id, b.autoClose FROM BpmProcdef b JOIN ServicePackage s ON b.id = s.processId " +
            "JOIN BpmProcInst i ON i.serviceId = s.id " +
            "WHERE i.serviceId IN :servicesId")
    List<Object[]> getBpmProcdefByServiceIdAutoClose(@Param("servicesId") List<Long> servicesId);

    @Modifying
    @Query("SELECT DISTINCT s.id, b.autoCancel FROM BpmProcdef b JOIN ServicePackage s ON b.id = s.processId " +
            "JOIN BpmProcInst i ON i.serviceId = s.id " +
            "WHERE i.serviceId IN :servicesId")
    List<Object[]> getBpmProcdefByServiceIdAutoCancel(@Param("servicesId") List<Long> servicesId);

    @Query("SELECT b FROM BpmProcdef b JOIN ServicePackage s ON b.id = s.processId " +
            "JOIN BpmProcInst i ON i.serviceId = s.id " +
            "WHERE i.serviceId =:serviceId")
    BpmProcdef getBpmProcdefByService(@Param("serviceId") Long serviceId);

    Optional<BpmProcdef> findTopByNameLikeOrderByIdDesc(String name);

    @Query("SELECT u from BpmProcdef u where u.priorityId = :priorityId")
    List<BpmProcdef> getAllPriorityOnBpmProdef(Long priorityId);

    @Query("SELECT distinct u.priorityId from BpmProcdef u")
    List<Long> getAllPriorityIds();

    @Query("SELECT a FROM BpmProcdef a "
            + "JOIN ServicePackage b ON a.id = b.processId "
            + "WHERE b.id = :serviceId")
    BpmProcdef findByServiceId(@Param("serviceId") Long serviceId);

    @Query("SELECT u.resourceName from BpmProcdef u where u.status <> 'DELETED'")
    List<Object[]> getResouceName();

    @Query("SELECT DISTINCT p.resourceName, s.serviceName\n" +
            "FROM BpmProcdef p \n" +
            "JOIN ServicePackage s on s.processId = p.id \n" +
            "WHERE p.status not in :status \n"

    )
    List<Object[]> getServicesAndUser(String status
    );

    @Query("SELECT u.userCreated from BpmProcdef u where u.status <> 'DELETED'")
    List<Object[]> getCreatedUser();

    @Query("SELECT a FROM BpmProcdef a "
            + "JOIN ServicePackage b ON a.id = b.processId "
            + "WHERE b.id = :serviceId")
    BpmProcdef getByServiceId(@Param("serviceId") Long serviceId);

    @Query("SELECT sp.serviceName as serviceName,bp.procDefId as procDefId,sp.id as serviceId FROM ServicePackage sp join BpmProcdef bp on sp.processId = bp.id " +
            "Where sp.serviceType = 2 and sp.status = 'ACTIVE' and sp.serviceName IS NOT NULL")
    List<Map<String, Object>> getServicePackage();

    BpmProcdef findBpmProcdefBySpecialParentIdAndSpecialCompanyCodeAndSpecialParentServiceId(Long parentId, String companyCode, Long parentServiceId);

    BpmProcdef findByProcDefId(String procDefId);

    @Query("SELECT new vn.fis.eapprove.business.model.response.ChildResponse(t.specialParentId,t.id,t.specialCompanyCode,'',t.specialFormKey) FROM BpmProcdef t " +
            "where t.specialParentId in (:parentIds) " +
            "AND t.specialFlow = true")
    List<ChildResponse> findSpecialFlowChildDataByParentIds(List<Long> parentIds);

    @Query("select sp.id as serviceId, bp.procDefId as procDefId from BpmProcdef bp " +
            " join ServicePackage sp on sp.processId = bp.id " +
            " where sp.id in (:listServiceId)")
    List<Map<String, Object>> findByListServiceId(List<Long> listServiceId);

    @Query("select bpm from BpmProcdef bpm where bpm.id <> :id and bpm.name = :name")
    List<BpmProcdef> checkExistByName(@Param("id") Long id, @Param("name") String name);

    @Query(value = "select distinct bp.name,bpn.id as notiId,nt.id as templateId " +
            "from bpm_procdef_notification bpn join notification_template nt on bpn.notification_template_id = nt.id " +
            "join bpm_procdef bp on bp.id = bpn.bpm_procdef_id and nt.id in (:templateNotiId)",nativeQuery = true)
    List<Tuple> findAllByTemplateNotiId(List<Long> templateNotiId);

    @Query("select name from BpmProcdef where id = :id")
    String getNameById(Long id);
}