package vn.fis.eapprove.business.domain.bpm.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.springframework.transaction.annotation.Transactional;

@Data
@Entity
@Transactional
@Table(name = "bpm_template_html")
public class BpmTemplateHtml {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "html_content")
    private String htmlContent;

    @Column(name = "name")
    private String name;

    @Column(name = "header_content")
    private String headerContent;

    @Column(name = "footer_content")
    private String footerContent;

    @Column(name = "size")
    private String size;
}
