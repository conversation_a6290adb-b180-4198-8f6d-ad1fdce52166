package vn.fis.eapprove.business.domain.bpm.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "bpm_shared")
public class BpmShared {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "TASK_ID")
    private String taskId;

    @Column(name = "PROC_INST_ID")
    private String procInstId;

    @Column(name = "TYPE")
    private String type;

    @Column(name = "SHARED_USER")
    private String sharedUser;

    @Column(name = "CREATED_USER")
    private String createdUser;

    @Column(name = "CREATED_TIME")
    private Date createdDate;

    @Column(name = "IS_DELETED")
    private Boolean isDeleted;

    @Column(name = "SHARED_USER_INFO")
    private String sharedUserInfo;

    @Column(name = "CREATED_USER_INFO")
    private String createdUserInfo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PROC_INST_ID", referencedColumnName = "PROC_INST_ID", insertable = false, updatable = false)
    @JsonIgnore
    private BpmProcInst bpmProcInst;
}
