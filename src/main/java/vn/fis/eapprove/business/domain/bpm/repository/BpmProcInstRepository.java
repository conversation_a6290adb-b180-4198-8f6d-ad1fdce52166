package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.dto.TicketCompletedDto;
import vn.fis.eapprove.business.dto.TimeExpectDTO;


import jakarta.persistence.Tuple;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Repository
@Transactional
public interface BpmProcInstRepository extends JpaRepository<BpmProcInst, Long>, JpaSpecificationExecutor<BpmProcInst> {

    List<BpmProcInst> getBpmProcInstByTicketProcInstId(String id);

    BpmProcInst getBpmProcInstByTicketId(Long id);

    Boolean existsByServiceIdIn(Set<Long> serviceIds);

    Boolean existsByTicketStartUserIdAndTicketProcDefId(String startUserId, String procDefID);

    BpmProcInst findBpmProcInstByTicketProcInstId(String id);

    List<BpmProcInst> getBpmProcInstByTicketFinishTimeIsNotNullAndTicketStatus(String status);

    List<BpmProcInst> getBpmProcInstByTicketProcDefId(String procDefId);

    BpmProcInst findBpmProcInstByticketProcInstId(String id);

    BpmProcInst findBpmProcInstByTicketIdAndTicketStatusNotIn(Long id, String[] status);

    List<BpmProcInst> findBpmProcInstByticketIdIn(List<Long> ticketIds);

    List<BpmProcInst> findBpmProcInstByTicketStartUserIdIn(List<String> usernames);

    List<BpmProcInst> findBpmProcInstByTicketStartUserId(String username);

    @Modifying
    @Query("UPDATE BpmProcInst b SET b.createdUser = :newUser  WHERE b.ticketId in :ticketId")
    void updateUser(String newUser, List<Long> ticketId);

    @Modifying(clearAutomatically = true, flushAutomatically = true)
    @Query("UPDATE BpmProcInst b SET b.ticketStatus = 'DELETED' WHERE b.ticketId in :ticketId")
    void deleteDraft(@Param("ticketId") List<Long> ticketId);

    @Modifying(clearAutomatically = true, flushAutomatically = true)
    @Query("UPDATE BpmProcInst b SET b.ticketStatus = 'PROCESSING' WHERE b.ticketProcInstId = :procInstId")
    void ongoingTicket(@Param("procInstId") String procInstId);

    @Modifying(clearAutomatically = true, flushAutomatically = true)
    @Query("UPDATE BpmProcInst b SET b.ticketStatus = 'COMPLETED', b.ticketFinishTime = :time, " +
            "b.ticketEndActId = :endNodeId ,b.finish_user = :finishUser " +
            "WHERE b.ticketProcInstId = :procInstId")
    void completeTicket(@Param("procInstId") String procInstId, @Param("time") LocalDateTime time, @Param("endNodeId") String endNodeId,
                        @Param("finishUser") String finishUser);

    @Modifying(clearAutomatically = true, flushAutomatically = true)
    @Query("UPDATE BpmProcInst b SET b.ticketStatus = 'DELETED_BY_RU',b.ruTime = :time WHERE b.ticketProcInstId = :procInstId")
    void deleteByRu(@Param("procInstId") String procInstId,@Param("time") LocalDateTime time);

    @Query("SELECT new vn.fis.eapprove.business.dto.TimeExpectDTO(a.ticketProcInstId, a.ruTime, a.createdUser,c.autoCancel,a.ticketId,c.autoClose,a.ticketFinishTime,a.slaFinish) "
            + "FROM BpmProcInst a "
            + "LEFT JOIN ServicePackage b ON a.serviceId = b.id "
            + "LEFT JOIN BpmProcdef c ON b.processId = c.id "
            + "WHERE a.ticketId = :id ")
    Page<TimeExpectDTO> ticket(Long id, Pageable pageable);

    @Query("SELECT a.createdUser, d.idUser, e.sharedUser "
            + "FROM BpmProcInst a "
            + "JOIN ServicePackage b on a.serviceId = b.id "
            + "JOIN BpmProcdef c ON b.processId = c.id "
            + "LEFT JOIN BpmOwnerProcess d ON c.id = d.procDefId AND d.idUser = :user "
            + "LEFT JOIN BpmShared e ON a.ticketProcInstId = e.procInstId AND e.sharedUser = :user "
            + "WHERE a.ticketProcInstId = :procInstId")
    List<Object[]> listPermission(@Param("procInstId") String procInstId, @Param("user") String user);

    @Query("SELECT a.ticketStatus, COUNT(a.ticketId) " +
            "FROM BpmProcInst a " +
            "WHERE a.ticketStartUserId = :username " +
            "AND EXISTS (SELECT 1 FROM BpmProcdef b WHERE a.ticketProcDefId = b.procDefId) " +
            "AND EXISTS (SELECT 1 FROM ServicePackage b WHERE a.serviceId = b.id) " +
            "GROUP BY a.ticketStatus")
    List<Object[]> countTicket(@Param("username") String username);

    @Query("SELECT d.procDefId, s.serviceName " +
            "FROM BpmProcInst b " +
            "JOIN ServicePackage s on s.id = b.serviceId " +
            "JOIN BpmProcdef d on s.processId = d.id " +
            "WHERE d.procDefId in :procDefId"
    )
    List<Object[]> getListAdditional(@Param("procDefId") List<String> procDefId);

    @Query("SELECT b.ticketId, t.taskId, t.taskDefKey, t.taskAssignee,b.ticketProcInstId, t.taskType, t.taskStatus, t.taskId " +
            "FROM BpmProcInst b " +
            "JOIN BpmTask t on t.taskProcInstId = b.ticketProcInstId " +
            "WHERE b.ticketId in :ticketId"
    )
    List<Object[]> getBpmTask(List<Long> ticketId);

    @Query("SELECT DISTINCT s.serviceName " +
            "FROM BpmProcInst b " +
            "JOIN ServicePackage s on s.id = b.serviceId " +
            "JOIN BpmProcdef d on s.processId = d.id " +
            "WHERE " +
            "b.ticketStartUserId = :user")
    List<String> distinctService(@Param("user") String user);

    @Query("SELECT DISTINCT b.createdUser " +
            "FROM BpmProcInst b ")
    List<String> getAllUserCreate();

    @Query("SELECT DISTINCT b.createdUser " +
            "FROM BpmProcInst b where b.createdUser = :account")
    List<String> getAllUserByAccount(String account);

    @Query("SELECT DISTINCT b.ticketCreatedTime " +
            "FROM BpmProcInst b " +
            "JOIN BpmProcdef d on b.ticketProcDefId = d.procDefId " +
            "WHERE " +
            "b.ticketStartUserId = :user")
    List<LocalDateTime> distinctTime(@Param("user") String user);

    @Query("SELECT DISTINCT s.serviceName " +
            "FROM BpmProcInst b " +
            "JOIN ServicePackage s on s.id = b.serviceId " +
            "WHERE " +
            "b.ticketProcInstId = :procInstId")
    String serviceNameByProcinst(@Param("procInstId") String procInstId);

    List<BpmProcInst> findByLocationId(Long locationId);

    @Query("SELECT u from BpmProcInst u where u.priorityId = :priorityId")
    List<BpmProcInst> getAllPriorityOnBpmProcInst(Long priorityId);

    @Query("SELECT distinct u.priorityId from BpmProcInst u")
    List<Long> getAllPriorityIds();

    @Query("SELECT u from ServicePackage u where u.submissionType = :submissionTypeId")
    List<ServicePackage> getAllSubmissionTypeOnBpmProcInst(Long submissionTypeId);

    List<BpmProcInst> findBpmProcInstByTicketStatusIn(String[] s);

    List<BpmProcInst> findBpmProcInstByTicketStatusNotIn(String[] s);

    @Query(value = "SELECT a.ticketId AS ticketId, a.ticketTitle AS title,a.requestCode AS requestCode,"
            + "a.ticketAssign AS ticketAssign "
            + "FROM BpmProcInst a LEFT JOIN AssignManagement am on a.ticketId = am.ticketId "
            + "WHERE (COALESCE(:serviceIds, NULL) IS NULL OR a.serviceId IN (:serviceIds)) "
            + "AND (COALESCE(:status, NULL) IS NULL OR a.ticketStatus IN (:status)) "
            + "AND (a.createdUser = :userName "
            + "OR EXISTS (SELECT 1 FROM BpmShared b WHERE a.ticketProcInstId = b.procInstId AND b.sharedUser = :userName)"
            + ")"
            + "ORDER BY a.ticketTitle")
    Page<Tuple> getAllByServiceIds(@Param("serviceIds") List<Long> serviceIds, @Param("status") List<String> status, @Param("userName") String userName, Pageable pageable);

    @Query("SELECT u.ticketProcInstId from BpmProcInst u where u.serviceId = :serviceId")
    List<String> getBpmProcInstByServiceId(Long serviceId);

    @Query("SELECT u.createdUser from BpmProcInst u where u.createdUser in :account")
    Set<String> getCreateUserProcInst(List<String> account);


    @Query("SELECT distinct a.toAccount from AuthorityManagement a where a.ticketId in(select b.ticketId from BpmProcInst b where b.createdUser = :username)")
    List<String> getAccountAssigned(String username);

    @Query("select bpd from BpmProcInst bp join BpmProcdef bpd on bp.ticketProcDefId = bpd.procDefId where bp.ticketId = :id")
    BpmProcdef getAssignTemplateId(Long id);

    @Query(nativeQuery = true, value = "select * from bpm_procinst where proc_inst_id = :procInsId")
    BpmProcInst findBpmProcInstByProcInstId(@Param("procInsId") String procInsId);

    @Query(nativeQuery = true, value = "select bp.id from bpm_procinst bp where bp.status not in ('DRAFT') AND bp.created_time between STR_TO_DATE(:fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(:toDate, '%Y-%m-%d %H:%i:%s')")
    List<Long> getBpmProcInstByCreatedTime(@Param("fromDate") String fromDate, @Param("toDate") String toDate);

    Boolean existsByPriorityIdAndTicketStatusNotIn(Long priorityId,String[] status);

    @Query(value = "select distinct new vn.fis.eapprove.business.dto.TicketCompletedDto(" +
            " bp.ticketId, bp.ticketTitle, bp.requestCode, " +
            " coalesce(bp.appCode, 'EAPP'), " +
            " (select distinct bv.stringVal from BpmVariables bv where bv.procInstId = bp.ticketProcInstId and bv.name = 'start_txt_system'), " +
            " bt.actionUser, bt.taskFinishedTime, bp.serviceId, bp.ticketProcDefId, bp.ticketStartActId, bp.ticketStartUserId )" +
            " from BpmProcInst bp " +
            " left join BpmShared bs on bs.procInstId = bp.ticketProcInstId " +
            " join BpmTask bt on bt.taskProcInstId = bp.ticketProcInstId " +
            " where bp.cancelCompletedTicketId is null " +
            " and bp.ticketId not in (select sub.cancelCompletedTicketId from BpmProcInst sub where sub.cancelCompletedTicketId is not null and sub.ticketStatus not in ('COMPLETED', 'CLOSED', 'CANCEL')) " +
            " and bt.taskStatus = 'COMPLETED' and bt.actionUser is not null " +
            " and bp.ticketStatus in ('COMPLETED', 'CLOSED')" +
            " and (" +
            "   bp.createdUser = :username " +
            "   or bp.ticketStartActId = :username " +
            "   or (bs.sharedUser = :username and bs.type = 'SHARED' and bs.isDeleted is null)" +
            ") order by bp.ticketId desc, bt.taskFinishedTime asc")
    List<TicketCompletedDto> getTicketCompletedInfo(String username);

    @Query(value = "select distinct bt.actionUser from BpmTask bt " +
            " join BpmProcInst bp on bt.taskProcInstId = bp.ticketProcInstId " +
            " where bp.ticketId = :ticketId " +
            " and bt.taskStatus = 'COMPLETED' " +
            " and bt.actionUser is not null")
    List<String> getListAssigneeTicketCompleted(Long ticketId);

    @Query("select distinct bt.taskDefKey as taskDefKey, bt.taskName as taskName from BpmProcInst bp " +
            " join BpmTask bt on bt.taskProcInstId = bp.ticketProcInstId " +
            " left join ChangeAssigneeHistory ch on ch.taskId = bt.taskId " +
            " left join ServicePackage sp on sp.id = bp.serviceId " +
            " where (sp.id in (:serviceIds) or sp.specialParentId in (:serviceIds)) " +
            " and (bt.taskAssignee = :username or (bt.assignType is true and ch.orgAssignee = :username)) " +
            " order by bt.taskDefKey")
    List<Tuple> getListTaskDefKeyByServiceId(List<Long> serviceIds, String username);

    @Query(value = "select bp.id as ticketId, bp.proc_inst_id as procInstId, bz.task_def_key as taskDefKey, bz.email as assignee, bp.start_act_id as startKey" +
            " from bpm_tp_sign_zone bz " +
            "   join bpm_task bt on bt.proc_inst_id = bz.proc_inst_id " +
            "   join bpm_procinst bp on bp.proc_inst_id = bt.proc_inst_id" +
            "    where 1 = 1 " +
            "    and bt.task_def_key = bz.task_def_key" +
            "    and (bt.assignee = bz.email or bt.action_user = bz.email)" +
            "    and bt.status = 'COMPLETED'" +
            "    and bz.sign is null " +
            "    and bt.finished_time > '2025-01-20 09:39:05'" +
            "    limit 10"
            , nativeQuery = true)
    List<Tuple> checkMissingSign();

    @Query("select ticketStatus from BpmProcInst where ticketId = :ticketId")
    String getTicketStatusByTicketId(Long ticketId);

    @Query("select ticketEndActId from BpmProcInst where ticketProcInstId = :procInstId ")
    String getEndKeyByProcInstId(String procInstId);
}

