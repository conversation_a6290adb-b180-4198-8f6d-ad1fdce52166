package vn.fis.eapprove.business.domain.file.service;

import org.springframework.web.multipart.MultipartFile;
import vn.fis.spro.common.model.response.FileUploadResponse;
import vn.fis.spro.file.exception.FileOperationException;

import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;

public interface FileService {

    List<Object> getDownloadFiles(List<String> filenames, String type);

    FileUploadResponse save(MultipartFile file, String type, boolean folderByDate);

    String saveFileToMinIO(InputStream is, String folder, String fileName, Date date, boolean splitFolderByDate) throws FileOperationException, IOException;
}
