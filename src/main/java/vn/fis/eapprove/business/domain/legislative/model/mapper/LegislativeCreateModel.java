package vn.fis.eapprove.business.domain.legislative.model.mapper;

import lombok.Data;
import vn.fis.eapprove.business.model.FileModel;

import java.util.List;

@Data
public class LegislativeCreateModel {
    private Long id;
    private String name;
    private String type;
    private String description;
    private Long releaseYear;
    private String responsibleAgency;
    private Boolean hasTicket;
    private Long processId;
    private String procDefId;
    private Boolean policyDevelopmentProcess;
    private String procInstId;
    private Long ticketId;
    private String ticketTitle;
    private String ticketStatus;
    private String requestCode;
    private String approvalSession;
    private String reviewSession;
    private List<LegislativeDetailModel> details;
    private List<String> processUsers;
    private List<FileModel> files;
    private String companyCode;
    private String companyName;
    private String status;
    private String startKey;
    private String processType;
    private Integer executionCount;
    private String createdUser;
    private String estimatedTime;
    private String organizationSector;
}
