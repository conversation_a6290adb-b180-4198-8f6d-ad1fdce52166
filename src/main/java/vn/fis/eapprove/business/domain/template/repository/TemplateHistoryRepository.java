package vn.fis.eapprove.business.domain.template.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vn.fis.eapprove.business.domain.template.entity.TemplateHistory;
import vn.fis.eapprove.business.model.response.TemplateHistoryResponse;


import java.time.LocalDateTime;
import java.util.List;

public interface TemplateHistoryRepository extends JpaRepository<TemplateHistory, Long>, JpaSpecificationExecutor<TemplateHistory> {
    //    @Query("SELECT c FROM TemplateHistory c WHERE (LOWER(c.version) LIKE LOWER(CONCAT('%',:search,'%')) AND UPPER(c.version) LIKE UPPER(CONCAT('%',:search,'%')) or LOWER(c.createdUser) LIKE LOWER(CONCAT('%',:search,'%')) AND UPPER(c.createdUser) LIKE UPPER(CONCAT('%',:search,'%'))) and (c.templateId in (:templateId)) ")
//    Page<TemplateHistory> getAll(@Param("search") String search, Pageable pageable, Long templateId);
    @Query("SELECT t FROM TemplateHistory t WHERE t.templateId = :id AND t.statusHistory = true")
    List<TemplateHistory> findByTemplateId(Long id);

    @Query("SELECT t FROM TemplateHistory t WHERE t.templateId in (:id) AND t.statusHistory = true")
    List<TemplateHistory> findAllByTemplateId(List<Long> id);

    @Query("SELECT c FROM TemplateHistory c WHERE (LOWER(c.version) LIKE LOWER(CONCAT('%',:search,'%')) AND UPPER(c.version) LIKE UPPER(CONCAT('%',:search,'%')) or LOWER(c.createdUser) LIKE LOWER(CONCAT('%',:search,'%')) AND UPPER(c.createdUser) LIKE UPPER(CONCAT('%',:search,'%'))) and (c.templateId in (:templateId)) ")
    Page<TemplateHistory> getAll(String search, Pageable pageable, Long templateId);

    @Query("select count(t.id) from TemplateHistory t where t.templateId = (select t.templateId from TemplateHistory t where t.id =:id)")
    Integer findMaxVersion(Long id);

    @Query("select new vn.fis.eapprove.business.model.response.TemplateHistoryResponse(max(th.id),th.templateId,th.urlName) from TemplateHistory th  " +
            "where th.urlName = :urlName " +
            "group by th.templateId,th.urlName order by max(th.id) desc")
    List<TemplateHistoryResponse> findFirstByUrlNameAndCreatedDateBeforeOrderByIdDesc(String urlName);

    @Query("select new vn.fis.eapprove.business.model.response.TemplateHistoryResponse(max(th.id),th.templateId,th.urlName) from TemplateHistory th  " +
            "where th.urlName in (:urlNames) and th.createdDate < :ticketCreatedTime " +
            "GROUP BY th.templateId,th.urlName order by max(th.id) desc")
    List<TemplateHistoryResponse> findAllMaxVersionByUrlName(List<String> urlNames, LocalDateTime ticketCreatedTime);

    @Query(value = "SELECT t.* FROM template_history t WHERE t.template_id = :templateId ORDER BY t.id DESC LIMIT 1", nativeQuery = true)
    TemplateHistory findFirstByTemplateId(String templateId);

    @Query("SELECT t.template from TemplateHistory t where t.id = :id")
    String getTemplateById(Long id);

    @Query("select new vn.fis.eapprove.business.model.response.TemplateHistoryResponse(max(th.id),th.templateId,th.urlName) from TemplateHistory th  " +
            "where th.templateId = (select distinct COALESCE(tm.specialParentId,th.templateId) from TemplateHistory th join TemplateManage tm on th.templateId = tm.id" +
            "                        where th.templateId = :templateId) " +
            "group by th.templateId,th.urlName order by max(th.id) desc")
    List<TemplateHistoryResponse> findTemplateHistoryByChildTemplateId(Long templateId);

    @Query("select new vn.fis.eapprove.business.model.response.TemplateHistoryResponse(max(th.id),th.templateId,th.urlName,tm.id) " +
            "from TemplateHistory th join TemplateManage tm on th.templateId = tm.specialParentId " +
            "where th.templateId in (select distinct  COALESCE(tm.specialParentId,th.templateId) from TemplateHistory th join TemplateManage tm on th.templateId = tm.id" +
            "                        where th.templateId in (:templateIds)) " +
            " and th.createdDate < :ticketCreatedTime " +
            " and tm.id in (:templateIds) " +
            "group by th.templateId,th.urlName order by max(th.id) desc")
    List<TemplateHistoryResponse> findAllChildMaxVersionByUrlName(List<Long> templateIds, LocalDateTime ticketCreatedTime);

}
