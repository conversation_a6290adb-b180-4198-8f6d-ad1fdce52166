package vn.fis.eapprove.business.domain.system.entity;

import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "system_group")
public class SystemGroup implements Cloneable{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "username")
    private String username;

    @Column(name = "from_date")
    private LocalDateTime fromDate;

    @Column(name = "to_date")
    private LocalDateTime toDate;

    @Column(name = "group_type")
    private String groupType;

    @Column(name = "group_field")
    private String groupField;

    @Column(name = "group_value")
    private String groupValue;

    @Column(name = "status")
    private Integer status;

    @Column(name = "company_code")
    private String companyCode;

    @Column(name = "company_name")
    private String companyName;

    @Column(name = "description")
    private String description;

    @Column(name = "parent")
    private Long parent;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "modified_user")
    private String modifiedUser;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "modified_date")
    private LocalDateTime modifiedDate;

    @Override
    public SystemGroup clone() {
        try {
            return (SystemGroup) super.clone();
        } catch (CloneNotSupportedException e) {
            return new SystemGroup();
        }
    }

}