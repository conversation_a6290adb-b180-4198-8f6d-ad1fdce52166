package vn.fis.eapprove.business.domain.fileCondition.service.impl;

import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import vn.fis.eapprove.business.domain.fileCondition.service.FileService;
import vn.fis.eapprove.business.utils.FileUtils;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.model.response.FileUploadResponse;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.manager.FileManager;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Author: PhucVM
 * Date: 25/11/2022
 */
@Service("FileServiceImplV1")
@Slf4j
public class FileServiceImpl implements FileService {

    private final FileManager fileManager;
    private final CredentialHelper credentialHelper;
    private final SproProperties sproProperties;

    @Autowired
    public FileServiceImpl(FileManager fileManager,
                           CredentialHelper credentialHelper, SproProperties sproProperties) {
        this.fileManager = fileManager;
        this.credentialHelper = credentialHelper;
        this.sproProperties = sproProperties;
    }

    @Value("${app.s3.bucket}")
    private String bucket;

    @Override
    public String getFileFolderByType(String type) {
        String folder = "";
        if (!ValidationUtils.isNullOrEmpty(type)) {
            if (type.equalsIgnoreCase(CommonConstants.FileUploadType.TICKET)) {
                folder = CommonConstants.FileFolder.TICKET_FILES;
            } else if (type.equalsIgnoreCase(CommonConstants.FileUploadType.SIGN)) {
                folder = CommonConstants.FileFolder.SIGNATURE_FILES;
            } else if (type.equalsIgnoreCase(CommonConstants.FileUploadType.TEMPLATE)) {
                folder = CommonConstants.FileFolder.TEMPLATE_FILES;
            } else if (type.equalsIgnoreCase(CommonConstants.FileUploadType.NOTIFICATION)) {
                folder = CommonConstants.FileFolder.NOTIFICATION_ICONS;
            }
        }

        return folder;
    }

    @Override
    public String getBucket()  {
        return bucket;
    }

    @Override
    public FileUploadResponse save(MultipartFile file, String type, boolean folderByDate) {
        try {
            if (file == null || file.getOriginalFilename() == null) {
                return null;
            }

            String fileName = file.getOriginalFilename();
            String folder = getFileFolderByType(type);
            long fileSize = file.getSize();

            log.info("Starting upload file: {} ({} bytes)", fileName, fileSize);

            // check bucket exists
            if (!fileManager.isBucketExisting(getBucket())) {
                fileManager.createBucket(getBucket());
            }

            // upload to minIO
            String uploadedFileName = fileManager.putFile(getBucket(),
                    FileUtils.getUploadFileName(folder, FileUtils.lowerFileNameExtension(fileName), new Date(), folderByDate),
                    fileSize,
                    file.getInputStream());

            return FileUploadResponse.builder()
                    .type(type)
                    .fileName(uploadedFileName)
                    .size(fileSize)
                    .contentType(file.getContentType())
                    .downloadUrl(fileManager.getUrlFile(getBucket(), uploadedFileName))
                    .build();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return null;
    }
    @Override
    public List<FileUploadResponse> uploadByFileName(MultipartFile[] files, String folder,List<String> fileNames) {
        try {
            List<FileUploadResponse> responseList = new ArrayList<>();
            for (MultipartFile file : files) {
                String fileName = !fileNames.isEmpty() ? fileNames.remove(0) : "";
                if (file == null || file.getOriginalFilename() == null) {
                    continue;
                }
                long fileSize = file.getSize();

                if (!fileManager.isBucketExisting(getBucket())) fileManager.createBucket(getBucket());

                log.info("Starting upload file: {} ({} bytes)", fileName, fileSize);
                String uploadedFileName = fileManager.putFile(getBucket(), FileUtils.lowerFileNameExtension(folder+"/"+fileName), fileSize, file.getInputStream());
                if (uploadedFileName != null) {
                    FileUploadResponse rs = FileUploadResponse.builder()
                            .fileName(uploadedFileName)
                            .size(fileSize)
                            .contentType(file.getContentType())
                            .downloadUrl(sproProperties.getStorage().getUrl()
                                    .concat("/").concat(getBucket())
                                    .concat("/").concat(uploadedFileName))
                            .build();
                    responseList.add(rs);
                }

            }

            return responseList;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return null;
    }

    @Override
    public List<FileUploadResponse> saveAll(MultipartFile[] files, String type, boolean folderByDate) {
        if (files != null && files.length > 0) {
            List<FileUploadResponse> responseList = new ArrayList<>();
            for (MultipartFile file : files) {
                FileUploadResponse response = save(file, type, folderByDate);
                if (response != null) {
                    responseList.add(response);
                }
            }

            return responseList;
        }

        return null;
    }

    /**
     * Save encoded base64 file to minIO
     *
     * <AUTHOR>
     */
    @Override
    public String saveFileToMinIO(String encodedBase64, String folder, String fileName, Date date, boolean splitFolderByDate) {
        if (!ValidationUtils.isNullOrEmpty(encodedBase64) && !ValidationUtils.isNullOrEmpty(fileName)) {
            try (InputStream is = FileUtils.convertBase64ToInputStream(encodedBase64)) {
                if (is == null) {
                    return null;
                }

                String bucket = getBucket();
                if (!fileManager.isBucketExisting(bucket)) {
                    fileManager.createBucket(bucket);
                }

                String uploadFileName = FileUtils.getUploadFileName(folder, FileUtils.lowerFileNameExtension(fileName), date, splitFolderByDate);
                return fileManager.putFile(bucket, uploadFileName, is.available(), is);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }

        return null;
    }
}
