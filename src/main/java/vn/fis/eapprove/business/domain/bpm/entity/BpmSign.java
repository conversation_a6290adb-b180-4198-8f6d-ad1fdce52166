package vn.fis.eapprove.business.domain.bpm.entity;


import lombok.Data;

import jakarta.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "bpm_sign")
public class BpmSign {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "IS_DELETED")
    private boolean isDeleted = false;

    @Column(name = "IS_DEFAULT")
    private boolean isDefault = false;

    @Column(name = "Img")
    private String img;

    @Column(name = "CREATED_USER")
    private String createdUser;

    @Column(name = "CREATED_DATE")
    private Date createdDate;

    @Column(name = "UPDATED_USER")
    private String updatedUser;

    @Column(name = "UPDATED_DATE")
    private Date updatedDate;

    @Column(name = "staff_code")
    private String staffCode;


}
