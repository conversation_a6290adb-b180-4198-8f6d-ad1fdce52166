package vn.fis.eapprove.business.domain.bpm.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.Tuple;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.EndEvent;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.bpmn.instance.UserTask;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.json.JSONArray;
import org.json.JSONException;
import org.jsoup.Jsoup;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import vn.fis.eapprove.business.config.GsonAdapterConfig;
import vn.fis.eapprove.business.constant.AppConstants;
import vn.fis.eapprove.business.constant.BusinessEnum;
import vn.fis.eapprove.business.constant.LegislativeEnum;
import vn.fis.eapprove.business.domain.assign.repository.AssignRepository;
import vn.fis.eapprove.business.domain.assign.service.AssignManager;
import vn.fis.eapprove.business.domain.assistant.entity.Assistant;
import vn.fis.eapprove.business.domain.assistant.repository.AssistantRepository;
import vn.fis.eapprove.business.domain.authority.entity.AuthorityManagement;
import vn.fis.eapprove.business.domain.authority.repository.AuthorityManagementRepository;
import vn.fis.eapprove.business.domain.bpm.entity.*;
import vn.fis.eapprove.business.domain.bpm.repository.*;
import vn.fis.eapprove.business.domain.changeAssignee.entity.ChangeAssigneeHistory;
import vn.fis.eapprove.business.domain.changeAssignee.repository.ChangeAssigneeHistoryRepository;
import vn.fis.eapprove.business.domain.changeAssignee.service.ChangeAssigneeHistoryService;
import vn.fis.eapprove.business.domain.codeGen.service.CodeGenConfigService;
import vn.fis.eapprove.business.domain.dashboard.service.DashboardTicketService;
import vn.fis.eapprove.business.domain.legislative.model.entity.LegislativeProgram;
import vn.fis.eapprove.business.domain.legislative.model.entity.LegislativeProgramDetail;
import vn.fis.eapprove.business.domain.legislative.repository.LegislativeProgramDetailRepository;
import vn.fis.eapprove.business.domain.legislative.repository.LegislativeProgramRepository;
import vn.fis.eapprove.business.domain.location.service.LocationManager;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagementHistory;
import vn.fis.eapprove.business.domain.priority.repository.PriorityHistoryRepository;
import vn.fis.eapprove.business.domain.priority.service.PriorityManager;
import vn.fis.eapprove.business.domain.report.service.ReportByGroupServiceNew;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage_;
import vn.fis.eapprove.business.domain.servicePackage.repository.ServicePackageRepository;
import vn.fis.eapprove.business.domain.servicePackage.service.ServicePackageManager;
import vn.fis.eapprove.business.domain.submission.entity.SubmissionType;
import vn.fis.eapprove.business.domain.submission.service.SubmissionTypeManager;
import vn.fis.eapprove.business.domain.template.repository.TemplateHistoryRepository;
import vn.fis.eapprove.business.domain.ticket.entity.TicketAutoLog;
import vn.fis.eapprove.business.domain.ticket.service.TicketAutoLogService;
import vn.fis.eapprove.business.dto.*;
import vn.fis.eapprove.business.mapper.BpmLoadTicketMapper;
import vn.fis.eapprove.business.mapper.BpmTaskMapper;
import vn.fis.eapprove.business.model.AccountModel;
import vn.fis.eapprove.business.model.ActionApiContext;
import vn.fis.eapprove.business.model.NotificationUser;
import vn.fis.eapprove.business.model.TicketRecall;
import vn.fis.eapprove.business.model.request.*;
import vn.fis.eapprove.business.model.response.*;
import vn.fis.eapprove.business.producer.ReportProducer;
import vn.fis.eapprove.business.specification.BpmHistorySpecification;
import vn.fis.eapprove.business.specification.BpmProcInstSpecification;
import vn.fis.eapprove.business.specification.BpmShareSpecification;
import vn.fis.eapprove.business.tenant.manager.*;
import vn.fis.eapprove.business.utils.CamundaUtils;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.business.utils.FileUtils;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.business.utils.html.TemplateHtml;
import vn.fis.eapprove.business.utils.template.TemplateUtilsNew;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.camunda.SproFlow;
import vn.fis.spro.common.camunda.SproFlowNode;
import vn.fis.spro.common.constants.*;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.helper.RestHelper;
import vn.fis.spro.common.model.request.QueryRequest;
import vn.fis.spro.common.model.response.ChartInfoRoleResponse;
import vn.fis.spro.common.model.response.PagingResponse;
import vn.fis.spro.common.util.*;
import vn.fis.spro.file.manager.FileManager;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static vn.fis.eapprove.business.constant.Constant.*;
import static vn.fis.eapprove.business.utils.TimeUtils.stringToLocalDateTime;

@Slf4j
@Service("BpmProcInstManagerv1")
@Transactional(rollbackFor = {Exception.class, Throwable.class})
public class BpmProcInstManager {
    @Value("${app.superAdmin.account}")
    private String appSuperAdminAccount;
    @Autowired
    private BpmProcInstNotifyUserManager bpmProcInstNotifyManager;
    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;
    @Autowired
    private BpmProInstNotifyGroupManager bpmProInstNotifyGroupManager;
    @Autowired
    private BpmProcInstRepository bpmProcInstRepository;
    @Autowired
    private BpmProcdefManager bpmProcdefManager;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private LocationManager locationManager;
    @Autowired
    private MasterDataService masterDataService;
    @Autowired
    private BpmTaskRepository bpmTaskRepository;
    @Autowired
    private PriorityManager priorityManager;
    @Autowired
    private BpmProcInstSpecification bpmProcInstSpecification;
    @Autowired
    private BpmProcdefRepository bpmProcdefRepository;
    @Autowired
    private BpmTaskMapper bpmTaskMapper;
    @Autowired
    private BpmTaskManager bpmTaskManager;
    @Autowired
    private BpmLoadTicketMapper bpmLoadTicketMapper;
    @Autowired
    private BpmDiscussionRepository bpmDiscussionRepo;
    @Autowired
    private ResponseUtils responseUtils;
    @Autowired
    private BpmOwnerProcessManager bpmOwnerProcessManager;
    @Autowired
    private BpmHistoryRepository bpmHistoryRepository;
    @Autowired
    private BpmProcdefInheritsRepository bpmProcdefInheritsRepository;
    @Autowired
    private BpmHistorySpecification bpmHistorySpec;
    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private BpmSharedRepository bpmSharedRepository;
    @Autowired
    private TicketAutoLogService ticketAutoLogService;
    @Autowired
    private BpmShareSpecification shareSpecification;
    @Autowired
    private ServicePackageManager servicePackageManager;
    @Autowired
    private Common common;
    @Autowired
    private BpmTemplatePrintRepository bpmTemplatePrintRepository;
    @Autowired
    private BpmVariablesRepository bpmVariablesRepository;
    @Autowired
    private BpmHistoryManager bpmHistoryManager;
    @Autowired
    private BpmDiscussionFileRepository bpmDiscussionFileRepository;
    @Autowired
    private CamundaEngineService camundaEngineService;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private EntityManagerFactory entityManagerFactory;
    @Autowired
    private ServicePackageRepository servicePackageRepository;
    @Autowired
    private BpmAdditionalRequestRepository bpmAdditionalRequestRepository;
    @Autowired
    private CredentialHelper credentialHelper;
    @Autowired
    private BpmTaskUserService bpmTaskUserService;
    @Autowired
    private ActionApiService actionApiService;
    @Autowired
    private SproService sproService;
    @Autowired
    private BpmTpSignZoneManager bpmTpSignZoneManager;
    @Autowired
    private ActHiVarInstManager actHiVarInstManager;
    @Autowired
    private CodeGenConfigService codeGenConfigService;
    @Autowired
    private AssistantRepository assistantRepo;
    @Autowired
    private SproProperties sproProperties;
    @Autowired
    private BpmTemplatePrintManager bpmPrintPhaseManager;
    @Autowired
    private SubmissionTypeManager submissionTypeManager;
    @Autowired
    private RestHelper restHelper;
    @Autowired
    private BpmProcinstLinkManager bpmProcinstLinkManager;
    @Autowired
    @Lazy
    private BpmProcdefNotificationService bpmProcdefNotificationService;
    @Autowired
    private BpmProcinstRecallService bpmProcinstRecallService;
    @Autowired
    private ChangeAssigneeHistoryRepository changeAssigneeHistoryRepository;
    @Autowired
    private BpmService bpmService;
    @Value("${spro.service-urls.app-client-url}")
    private String port;
    @Autowired
    private BpmProcdefViewFileApiRepository bpmProcdefViewFileApiRepository;
    @Autowired
    private TemplateHistoryRepository templateHistoryRepository;
    @Autowired
    private ChangeAssigneeHistoryService changeAssigneeHistoryService;
    @Autowired
    private AssignManager assignManager;
    @Autowired
    private AssignRepository assignRepository;
    @Autowired
    private ReportByGroupServiceNew reportByGroupServiceNew;
    @Autowired
    private ReportProducer reportProducer;
    @Autowired
    private PriorityHistoryRepository priorityHistoryRepository;
    @Autowired
    private AuthorityManagementRepository authorityManagementRepository;
    @Autowired
    private BpmTemplatePrintConfigUserRepository bpmTemplatePrintConfigUserRepository;
    @Value("${app.s3.bucket}")
    private String bucket;
    @Value("${spro.storage.url}")
    private String storageUrl;
    @Autowired
    private BpmProcDefHistoryRepository bpmProcDefHistoryRepository;
    @Autowired
    private BpmFileConditionRepository bpmFileConditionRepository;
    @Autowired
    private TemplateHtml templateHtml;
    @Autowired
    private GotenbergManager gotenbergManager;
    @Autowired
    private DashboardTicketService dashboardTicketService;
    @Autowired
    private LegislativeProgramDetailRepository legislativeProgramDetailRepository;
    @Autowired
    private LegislativeProgramRepository legislativeProgramRepository;

    @Value("${spring.kafka.consumer.topic.insert-report-by-group}")
    private String insertReportByGroupTopic;
    @Value("${spring.kafka.consumer.topic.insert-report-by-chart-node}")
    private String topicInsertReportByChartNode;
    @Value("${spring.kafka.consumer.topic.notification-user}")
    private String notificationUser;
    @Value("${spring.kafka.consumer.topic.share-ticket}")
    private String topicShareTicket;

    public static List<Map<String, Object>> sortByRemainingTime(List<Map<String, Object>> superList, String
            sortType) {
        superList.sort((o1, o2) -> {
            if (o2.get("remainingTime") == null || o1.get("remainingTime") == null) {
                return -1;
            }
            if (sortType.equals("DESC")) {
                return ((Long) o2.get("remainingTime")).compareTo((Long) o1.get("remainingTime"));
            } else {
                return ((Long) o1.get("remainingTime")).compareTo((Long) o2.get("remainingTime"));
            }
        });

        return superList;
    }

    public byte[] getPrintFileTemplate(Long ticketId, BpmTemplatePrintRequest bpmTemplatePrintRequest, List<BpmTpSignZone> bpmTpSignZoneList) throws Exception {
        BpmProcInst currentTicket;
        Map<String, VariableValueDto> variables = bpmTemplatePrintRequest.getVariables();
        //Lấy tất cả các biến đã lưu nếu có truyền thêm ticketId
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        VariableValueDto variableValueDto;
        if (ticketId != null && !Objects.equals(ticketId, 0L)) {
            currentTicket = findById(ticketId);
            if (currentTicket != null) {//Đã có ticket thì lấy all biến đã lưu theo ProcInstId
                Map<String, VariableValueDto> variableValueDtoMap = actHiVarInstManager.getVariByTicketAndConvertToMap(currentTicket.getTicketProcInstId());
                if (variableValueDtoMap != null) {
                    if (variables == null) {
                        variables = new HashMap<>();
                    }
                    variables.putAll(variableValueDtoMap);
                }
            }

            //Lấy thông tin phiếu
            TicketDetailResponse ticketDetailResponse = searchByTicketId(ticketId);
            //Add một số thông tin mặc định của phiếu
            if (ticketDetailResponse != null) {
                //Mã phiếu
                variableValueDto = new VariableValueDto();
                variableValueDto.setType("String");
                variableValueDto.setValue(ticketDetailResponse.getId());
                variables.put("txt_maPhieu", variableValueDto);

                //Mã tờ trình
                variableValueDto = new VariableValueDto();
                variableValueDto.setType("String");
                variableValueDto.setValue(ticketDetailResponse.getRequestCode());
                variables.put("txt_maToTrinh", variableValueDto);

                //Tên phiếu
                variableValueDto = new VariableValueDto();
                variableValueDto.setType("String");
                variableValueDto.setValue(ticketDetailResponse.getTicketTitle());
                variables.put("txt_tenPhieu", variableValueDto);

                //Thời gian tạo phiếu
                variableValueDto = new VariableValueDto();
                variableValueDto.setType("String");
                variableValueDto.setValue(ticketDetailResponse.getTicketCreatedTime() != null ? dateFormat.format(ticketDetailResponse.getTicketCreatedTime()) : "");
                variables.put("dtm_thoiGianTaoPhieu", variableValueDto);

                //Loại dịch vụ
                variableValueDto = new VariableValueDto();
                variableValueDto.setType("String");
                variableValueDto.setValue(ticketDetailResponse.getProcServiceName());
                variables.put("txt_loaiDichvu", variableValueDto);

                //Thời gian hoàn thành thực tế
                variableValueDto = new VariableValueDto();
                variableValueDto.setType("String");
                variableValueDto.setValue(ticketDetailResponse.getTicketFinishTime() != null ? dateFormat.format(ticketDetailResponse.getTicketFinishTime()) : "");
                variables.put("dtm_hoanThanhThucTe", variableValueDto);

                //Thời gian phản hồi thực tế
                variableValueDto = new VariableValueDto();
                variableValueDto.setType("String");
                variableValueDto.setValue(ticketDetailResponse.getTicketStartedTime() != null ? dateFormat.format(ticketDetailResponse.getTicketStartedTime()) : "");
                variables.put("dtm_phanHoiThucTe", variableValueDto);

                //Cam kết phản hồi
                variableValueDto = new VariableValueDto();
                variableValueDto.setType("String");
                variableValueDto.setValue(ticketDetailResponse.getSlaResponseProcess());
                variables.put("txt_camKetPhanHoi", variableValueDto);

                //Cam Hoàn thành
                variableValueDto = new VariableValueDto();
                variableValueDto.setType("String");
                variableValueDto.setValue(ticketDetailResponse.getSlaFinishProcess());
                variables.put("txt_camKetHoanThanh", variableValueDto);

                //Phản hồi dự kiến
                variableValueDto = new VariableValueDto();
                variableValueDto.setType("String");
                variableValueDto.setValue(ticketDetailResponse.getSlaResponseTimeProcess() != null ? dateFormat.format(ticketDetailResponse.getSlaResponseTimeProcess()) : "");
                variables.put("dtm_phanHoiDuKien", variableValueDto);

                //Hoàn thành dự kiến
                variableValueDto = new VariableValueDto();
                variableValueDto.setType("String");
                variableValueDto.setValue(ticketDetailResponse.getSlaFinishTIme() != null ? dateFormat.format(ticketDetailResponse.getSlaFinishTIme()) : "");
                variables.put("dtm_phanHoiDuKien", variableValueDto);

                //Lấy location
                if (!ValidationUtils.isNullOrEmpty(currentTicket.getLocationId())) {
                    List<Map<String, Object>> locationManagement = masterDataService.getLoadTemplateLocation(currentTicket.getLocationId());
                    if (!ValidationUtils.isNullOrEmpty(locationManagement)) {
                        variableValueDto = new VariableValueDto();
                        variableValueDto.setType("String");
                        variableValueDto.setValue(locationManagement.get(0).get("name").toString());
                        variables.put("txt_ViTri", variableValueDto);
                    }
                }

                //Lấy danh sách phiếu liên kết
                if (!ValidationUtils.isNullOrEmpty(ticketDetailResponse.getLinkedBpmProcInstDto())) {
                    List<String> lstRelates = ticketDetailResponse.getLinkedBpmProcInstDto().stream().map(linkedBpmProcInstDto -> ValidationUtils.isNullOrEmpty(linkedBpmProcInstDto.getRequestCode()) ? linkedBpmProcInstDto.getTitle() : linkedBpmProcInstDto.getRequestCode()).collect(Collectors.toList());
                    variableValueDto = new VariableValueDto();
                    variableValueDto.setType("String");
                    variableValueDto.setValue(String.join(", ", lstRelates));
                    variables.put("txt_PhieuLienKet", variableValueDto);
                }

                //Lấy theo task
                List<BpmTask> bpmTasks = bpmTaskRepository.getBpmTaskByTaskProcInstId(currentTicket.getTicketProcInstId());
                if (bpmTasks != null && !bpmTasks.isEmpty()) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
                    for (BpmTask itemTask : bpmTasks) {
                        //Tên bước
                        VariableValueDto variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(itemTask.getTaskName());
                        variables.put(itemTask.getTaskDefKey() + "_txt_tenBuoc", variableDto);
                        //Thời gian tạo bước
                        variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(itemTask.getTaskCreatedTime() != null ? formatter.format(itemTask.getTaskCreatedTime()) : "");
                        variables.put(itemTask.getTaskDefKey() + "_dtm_thoiGianTaoBuoc", variableDto);
                        //Người thực hiện bước
                        variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(itemTask.getTaskAssignee());
                        variables.put(itemTask.getTaskDefKey() + "_txt_nguoiThucHien", variableDto);
                        //Trạng thái của bước
                        variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(itemTask.getTaskStatus());
                        variables.put(itemTask.getTaskDefKey() + "_txt_trangThai", variableDto);
                        //Thời gian hoàn thành bước
                        variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(itemTask.getTaskFinishedTime() != null ? formatter.format(itemTask.getTaskFinishedTime()) : "");
                        variables.put(itemTask.getTaskDefKey() + "_dtm_hoanThanhThucTe", variableDto);
                        //Thời gian cam kết hoàn thành
                        variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(itemTask.getSlaFinish());
                        variables.put(itemTask.getTaskDefKey() + "_txt_camKetHoanThanh", variableDto);
                        //Thời gian hoàn thành dự kiến
                        variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(itemTask.getSlaFinishTime() != null ? formatter.format(itemTask.getSlaFinishTime()) : "");
                        variables.put(itemTask.getTaskDefKey() + "_dtm_hoanThanhDuKien", variableDto);
                    }
                }
            }
        }

        // Các biến defaultTicket
        if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrintRequest.getCreateVariables())) {
            Map<String, Object> createVariables = bpmTemplatePrintRequest.getCreateVariables();
            VariableValueDto variableDto = new VariableValueDto();
            if (!ValidationUtils.isNullOrEmpty(createVariables.get("txt_tenPhieu"))) {
                variableDto.setType("String");
                variableDto.setValue(createVariables.get("txt_tenPhieu"));
                variables.put("txt_tenPhieu", variableDto);
            }
            if (!ValidationUtils.isNullOrEmpty(createVariables.get("txt_ViTri"))) {
                variableDto = new VariableValueDto();
                variableDto.setType("String");
                variableDto.setValue(createVariables.get("txt_ViTri"));
                variables.put("txt_ViTri", variableDto);
            }
        }

        //Ngày giờ hiện tại
        variableValueDto = new VariableValueDto();
        variableValueDto.setType("String");
        variableValueDto.setValue(dateFormat.format(new Date()));
        variables.put("dtm_ngayGioHienTai", variableValueDto);

        //Xuất file trả về
        BpmTemplatePrint bpmTemplatePrint;
        String templateName;
        if (!StringUtil.isEmpty(bpmTemplatePrintRequest.getUploadWordsChange())) {//Truyền trực tiếp
            templateName = bpmTemplatePrintRequest.getUploadWordsChange();
        } else {
            bpmTemplatePrint = bpmPrintPhaseManager.getBpmTemplatePrint(bpmTemplatePrintRequest.getTemplateName());
            templateName = bpmTemplatePrint.getUploadWords();
        }

        if (templateName != null && !StringUtil.isEmpty(templateName)) {//Nếu có cấu hình template
            InputStream inputStream = null;
            if ((templateName.startsWith("http"))) {//Lấy file mẫu từ hệ thống ngoài
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                ResponseEntity<Resource> responseEntity = restHelper.exchange(templateName,
                        HttpMethod.GET,
                        headers,
                        null,
                        new ParameterizedTypeReference<>() {
                        });

                if (responseEntity.getBody() != null) {
                    inputStream = responseEntity.getBody().getInputStream();
                }
            } else {//Lấy file từ hệ thống s3
                inputStream = fileManager.getFileInputStream(bucket, templateName);
            }
            if (inputStream != null) {//Nếu có file template thì thực hiện xử lý
                TemplateUtilsNew templateUtilsNew = new TemplateUtilsNew(inputStream, null);
                inputStream.close();
                templateUtilsNew.setFileManager(fileManager);
                templateUtilsNew.setBpmTpSignZoneList(bpmTpSignZoneList);
                templateUtilsNew.setBucket(bucket);

                // check theo bpm_template_print_config_user
                if (!ValidationUtils.isNullOrEmpty(bpmTpSignZoneList)) {
                    List<BpmTemplatePrintConfigUser> lstConfigUser = bpmTemplatePrintConfigUserRepository.getBpmTemplatePrintConfigUsersByUsernameIn(
                            bpmTpSignZoneList.stream().map(BpmTpSignZone::getEmail).filter(Objects::nonNull).collect(Collectors.toList())
                    );
                    if (lstConfigUser != null) {
                        templateUtilsNew.setLstConfigUser(lstConfigUser);
                    }
                }

                templateUtilsNew.handlerDataNormal(variables);
                byte[] bytesContent = templateUtilsNew.saveToBytesDocx(templateUtilsNew.getTemplate());
                if (bpmTemplatePrintRequest.getPrintType() != null && bpmTemplatePrintRequest.getPrintType().equalsIgnoreCase("docx")) {
                    // get file docx
//                    InputStream targetStream = new ByteArrayInputStream(bytesContent);
//                    fileManager.putFile(bucket, "testMtkDocx.docx", targetStream.available(), targetStream);
                    return bytesContent;
                } else {
                    String url = sproProperties.getServiceUrls().get(MapKeyEnum.PDF_SERVICE.key) + "/forms/libreoffice/convert";
                    String fileName = "default.pdf";
                    if (!ValidationUtils.isNullOrEmpty(templateName)) {
                        fileName = templateName.substring(templateName.lastIndexOf("/") + 1);
                    }

                    File createTempFile = new File("./" + fileName);
                    // Test local
//                    InputStream input = new FileInputStream(createTempFile);
//                    fileManager.putFile(bucket, "testMtkDocx.docx", input.available(), input);
                    try (FileOutputStream fos = new FileOutputStream(createTempFile)) {
                        fos.write(bytesContent);
                        fos.flush();
                    } catch (Exception e) {
                        System.out.println(e.getMessage());
                    }

                    MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
                    body.add("files", new FileSystemResource(createTempFile));
                    body.add("merge", true);

                    // execute api
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.MULTIPART_FORM_DATA);

                    try {
                        ResponseEntity<byte[]> responseEntity = restHelper.exchange(url,
                                HttpMethod.POST,
                                headers,
                                body,
                                new ParameterizedTypeReference<>() {
                                });

                        if (responseEntity != null) {
                            return responseEntity.getBody();
                        }
                    } catch (Exception e) {
                        log.error("PDF combine fail: {}", e.getMessage());
                    }
                }
            }
        }

        return null;
    }

    public byte[] getFileCombineLogo(String procInstId, String uploadWordsChange) throws Exception {
        List<BpmVariables> lstVariable = bpmVariablesRepository.getAllByVariableName(procInstId, null, List.of("start_slt_logo"));
        if (!ValidationUtils.isNullOrEmpty(lstVariable)) {
            Map<String, VariableValueDto> variables = actHiVarInstManager.convertResponseVariablesToMap(lstVariable);

            InputStream inputStream = fileManager.getFileInputStream(bucket, uploadWordsChange);
            TemplateUtilsNew templateUtilsNew = new TemplateUtilsNew(inputStream, null);
            inputStream.close();
            templateUtilsNew.setFileManager(fileManager);
            templateUtilsNew.setBpmTpSignZoneList(null);
            templateUtilsNew.setBucket(bucket);

            // combine
            templateUtilsNew.handleLogo(variables);

            return templateUtilsNew.saveToBytesDocx(templateUtilsNew.getTemplate());
        }
        return null;
    }

    public void createBpmProcinstLink(Long ticketId, List<Long> linkProcInstId) {
        if (linkProcInstId != null) {
            bpmProcinstLinkManager.deleteAllBpmProcinstLinkById(ticketId);
            if (!linkProcInstId.isEmpty()) {
                List<BpmProcinstLink> bpmProcinstLinks = new ArrayList<>();
                for (Long linkId : linkProcInstId) {
                    BpmProcinstLink procinstLink = new BpmProcinstLink();
                    procinstLink.setBpmProcinstId(ticketId);
                    procinstLink.setBpmProcinstLinkId(linkId);
                    bpmProcinstLinks.add(procinstLink);
                }
                bpmProcinstLinkManager.saveAll(bpmProcinstLinks);
            }
        }
    }

    private void handleGetDefaultCreateTicket(StartProcessInstanceDto startProcessInstanceDto, String procDefId) {
        String username;
        try {
            username = credentialHelper.getJWTPayload().getUsername();
        } catch (Exception e) {
            username = appSuperAdminAccount;
        }
        List<Map<String, Object>> chartDetail = bpmService.findChartAndChartNodeCreateTicket(startProcessInstanceDto, username, false);
        if (chartDetail != null && !chartDetail.isEmpty()) {
            if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getCompanyCode())) {
                String companyCode = chartDetail.get(0).get("companyCode").toString() + "_" + chartDetail.get(0).get("chartShortName").toString();
                startProcessInstanceDto.setCompanyCode(companyCode);
            }
            if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getChartId()))
                startProcessInstanceDto.setChartId(Long.valueOf(chartDetail.get(0).get("chartId").toString()));
            if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getChartNodeId()))
                startProcessInstanceDto.setChartNodeId(Long.valueOf(chartDetail.get(0).get("chartNodeId").toString()));
            if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getChartName()))
                startProcessInstanceDto.setChartName(chartDetail.get(0).get("chartName").toString());
            if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getChartNodeName()))
                startProcessInstanceDto.setChartNodeName(chartDetail.get(0).get("chartNodeName").toString());
            if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getChartNodeCode()))
                startProcessInstanceDto.setChartNodeCode(chartDetail.get(0).get("chartNodeCode").toString());
        }

        //Độ ưu tiên
        if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getPriorityId())) {
            BpmProcdef prodef = bpmProcdefManager.getByProcdefId(procDefId);
            if (!ValidationUtils.isNullOrEmpty(prodef))
                startProcessInstanceDto.setPriorityId(prodef.getPriorityId());
        }

        //Vị trí
        if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getLocationId())) {
            startProcessInstanceDto.setLocationId(customerService.getLocationIdByUserName(username));
        }

        //Loại tờ trình
        if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getSubmissionType())) {
            ServicePackage servicePackage = servicePackageManager.getServicePackageById(startProcessInstanceDto.getServiceId());
            if (!ValidationUtils.isNullOrEmpty(servicePackage))
                startProcessInstanceDto.setSubmissionType(servicePackage.getSubmissionType());
        }

        if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getIsAssistant())) {
            startProcessInstanceDto.setIsAssistant(false);
        }
    }

    private void handleGetDefaultCreateTicketBasicAuth(StartProcessInstanceDto startProcessInstanceDto, String procDefId, List<Map<String, Object>> chartDetail) {
        String username = startProcessInstanceDto.getAccount();
        if (chartDetail != null && !chartDetail.isEmpty()) {
            if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getCompanyCode())) {
                String companyCode = chartDetail.get(0).get("companyCode").toString() + "_" + chartDetail.get(0).get("chartShortName").toString();
                startProcessInstanceDto.setCompanyCode(companyCode);
            }
            if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getChartId()))
                startProcessInstanceDto.setChartId(Long.valueOf(chartDetail.get(0).get("chartId").toString()));
            if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getChartNodeId()))
                startProcessInstanceDto.setChartNodeId(Long.valueOf(chartDetail.get(0).get("chartNodeId").toString()));
            if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getChartName()))
                startProcessInstanceDto.setChartName(chartDetail.get(0).get("chartName").toString());
            if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getChartNodeName()))
                startProcessInstanceDto.setChartNodeName(chartDetail.get(0).get("chartNodeName").toString());
            if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getChartNodeCode()))
                startProcessInstanceDto.setChartNodeCode(chartDetail.get(0).get("chartNodeCode").toString());
        }

        //Độ ưu tiên
        if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getPriorityId())) {
            BpmProcdef prodef = bpmProcdefManager.getByProcdefId(procDefId);
            if (!ValidationUtils.isNullOrEmpty(prodef))
                startProcessInstanceDto.setPriorityId(prodef.getPriorityId());
        }

        //Vị trí
        if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getLocationId())) {
            startProcessInstanceDto.setLocationId(customerService.getLocationIdByUserName(username));
        }

        //Loại tờ trình
        if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getSubmissionType())) {
            ServicePackage servicePackage = servicePackageManager.getServicePackageById(startProcessInstanceDto.getServiceId());
            if (!ValidationUtils.isNullOrEmpty(servicePackage))
                startProcessInstanceDto.setSubmissionType(servicePackage.getSubmissionType());
        }

        if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getIsAssistant())) {
            startProcessInstanceDto.setIsAssistant(false);
        }
    }

    public ResponseEntity<?> createTicket(String procDefId, Long ticketId, StartProcessInstanceDto startProcessInstanceDto) throws Exception {
        String account;
        try {
            account = credentialHelper.getJWTPayload().getUsername();
        } catch (Exception e) {
            account = appSuperAdminAccount;
        }

        VariableValueDto requestSubject = startProcessInstanceDto.getVariables().get("requestSubject");
        //Kiểm tra vùng mặc định nếu k có tự động lưu thêm
        handleGetDefaultCreateTicket(startProcessInstanceDto, procDefId);

        BpmProcInst currentTicket = null;
        Map<String, Object> mapResponse;

        //Validate
        if (startProcessInstanceDto.getVariables() == null || startProcessInstanceDto.getVariables().isEmpty()) {
            return ResponseHelper.invalid(common.getMessage("ticket.missing-variables"));
        }
        if (startProcessInstanceDto.getServiceId() == null) {
            return ResponseHelper.invalid(common.getMessage("ticket.missing-serviceId"));
        }

        String title = requestSubject == null ? "" : StringUtil.nvl(requestSubject.getValue(), "");
        if (title == null || title.trim().isEmpty()) {
            return ResponseHelper.invalid(common.getMessage("ticket.missing-title"));
        }
        if (title.length() > 400) {
            return ResponseHelper.invalid(common.getMessage("ticket.maxlength-title"));
        }

        //get currentTicket
        if (ticketId != null && !Objects.equals(ticketId, 0L)) {
            currentTicket = findById(ticketId);
            // TH có ticketId mà không có ticket -> return
            if (currentTicket == null) {
                return ResponseHelper.invalid(common.getMessage("ticket.deleted-ticket-draft"));
            }
        }

        if (startProcessInstanceDto.getIsDraft() == null) {
            startProcessInstanceDto.setIsDraft(false);
        }

        if (startProcessInstanceDto.getLegislativeId() != null) {
            LegislativeProgram legislative = legislativeProgramRepository.findLegislativeProgramById(startProcessInstanceDto.getLegislativeId());
            if (!legislative.getProcDefId().equalsIgnoreCase(procDefId)) {
                return ResponseHelper.invalid("Quy trình không trùng khớp với chi tiết nhiệm vụ của chương trình lập pháp hàng năm");
            }
        }

        String actionCode = TaskActionConstants.Action.CREATE_TICKET.code;
        if (currentTicket != null) { //Đã có ticket
            //Kiểm tra khác user không cho update
            if (currentTicket.getCreatedUser() != null && !currentTicket.getCreatedUser().equalsIgnoreCase(account)
                    && !currentTicket.getTicketStartUserId().equalsIgnoreCase(account) // Case bàn giao công việc
            ) {
                return ResponseHelper.invalid(common.getMessage("ticket.different-user"));
            }

            //set submisstion type id to currentTicket
            currentTicket.setSubmissionTypeId(startProcessInstanceDto.getSubmissionType());
            currentTicket.setSubmissionTypeName(startProcessInstanceDto.getSubmissionTypeName());
            currentTicket.setApprovedBudgetIds(ObjectUtils.toJson(startProcessInstanceDto.getApprovedBudgetIds()));

            Map<String, Object> mapFinal = new HashMap<>();
            mapFinal.put("startKey", currentTicket.getTicketStartActId());
            mapFinal.put("procInstId", currentTicket.getTicketProcInstId());
            mapFinal.put("ticketId", ticketId);
            mapFinal.put("requestCode", currentTicket.getRequestCode());

            //Xử lý bình thường
            if (currentTicket.getTicketStatus().equals(ProcInstConstants.Status.DRAFT.code)) {//Đang là lưu nháp

                currentTicket.setTicketTitle(title);
                currentTicket.setEmailNotification(startProcessInstanceDto.getReceiveMail());

                createBpmProcinstLink(currentTicket.getTicketId(), startProcessInstanceDto.getLinkProcInstId());

                if (startProcessInstanceDto.getIsDraft().equals(Boolean.FALSE)) { //Lưu thật
                    //Vì lưu nhap chưa call vào core camuda nên khi submit form cần thực hiện lại đủ nghiệp vụ
                    deleteBpmVariables(currentTicket.getTicketProcInstId());
                    mapResponse = createNormalTicket(ticketId, title, account, procDefId, startProcessInstanceDto, TaskActionConstants.Action.DRAFT_TO_OPEN.code, currentTicket);
                    mapFinal.putAll(mapResponse);
                    mapFinal.put("url", port.substring(0, port.lastIndexOf('/')) + CONTEXT_PATH_VIEW_TICKET + "/" + currentTicket.getTicketProcDefId() + "/" + currentTicket.getTicketId() + "/" + currentTicket.getTicketStartActId());

                    try {
                        //Xử lý lưu version biểu mẫu
                        handleSaveVersionTemplateProcinst(mapResponse, procDefId);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        return ResponseHelper.invalid(common.getMessage("ticket.save-version-template-false"));
                    }
                } else { //Lưu nháp đè
                    deleteBpmVariables(currentTicket.getTicketProcInstId());
                    createDraftTicket(currentTicket, ticketId, title, account, procDefId, startProcessInstanceDto, startProcessInstanceDto.getNotifyUsers(), startProcessInstanceDto.getNotifyGroups(), startProcessInstanceDto.getChartId());
                    mapFinal.put("url", port.substring(0, port.lastIndexOf('/')) + CONTEXT_PATH + "/" + currentTicket.getServiceId() + "/" + procDefId + "?clone=" + currentTicket.getTicketProcInstId() + "??" + currentTicket.getTicketId() + "??isDraft");
                }

                return ResponseHelper.ok(mapFinal);
            } else if (startProcessInstanceDto.getAppCode() != null
                    && (startProcessInstanceDto.getAppCode().equalsIgnoreCase("CONS") || startProcessInstanceDto.getAppCode().equalsIgnoreCase("IHRP") || startProcessInstanceDto.getAppCode().equalsIgnoreCase("SAP"))
                    && (currentTicket.getTicketStatus().equals(ProcInstConstants.Status.DELETED_BY_RU.code) || currentTicket.getTicketStatus().equals(ProcInstConstants.Status.RECALLED.code))
            ) {//Trả lại bên ECON/ IHRP/ SAP gửi sang thì chỉ update biến
                //lấy full variables
                Map<String, VariableValueDto> variables = getFinalVariables(currentTicket, startProcessInstanceDto);
                // delete old variables
                deleteBpmVariables(currentTicket.getTicketProcInstId());
                bpmTaskManager.saveTaskVari(variables, null, currentTicket.getTicketProcInstId());
                // update variables camunda
                bpmTaskManager.updateCamundaVariables(currentTicket.getTicketProcInstId());
                mapFinal.put("url", port.substring(0, port.lastIndexOf('/')) + CONTEXT_PATH_VIEW_TICKET + "/" + currentTicket.getTicketProcDefId() + "/" + currentTicket.getTicketId() + "/" + currentTicket.getTicketStartActId());
                return ResponseHelper.ok(mapFinal);
            } else if (currentTicket.getTicketStatus().equals(ProcInstConstants.Status.DELETED_BY_RU.code) || currentTicket.getTicketStatus().equals(ProcInstConstants.Status.RECALLED.code)) {//Trả lại - Thu hồi
                //Với trường hợp trả lại mà đang truyền isDraft = true thì đổi lại để chạy luồng nghiệp vụ bình thường
                //Bên econ không truyền lại các biến khi thực hiện lại=> cần lấy lại từ db trường nào econ k truyền thì lấy lại
                Map<String, VariableValueDto> variables = getFinalVariables(currentTicket, startProcessInstanceDto);
                startProcessInstanceDto.setVariables(variables);
                startProcessInstanceDto.setIsDraft(false);
                actionCode = TaskActionConstants.Action.REDO.code;
            } else if (currentTicket.getTicketStatus().equals(ProcInstConstants.Status.ADDITIONAL_REQUEST.code)) {//Yêu cầu bổ sung
                Map<String, VariableValueDto> variables = getFinalVariables(currentTicket, startProcessInstanceDto);
                startProcessInstanceDto.setVariables(variables);
                startProcessInstanceDto.setTicketId(ticketId);
                updateTicketAdditionalRequest(currentTicket, startProcessInstanceDto);
            } else if (currentTicket.getTicketStatus().equals(ProcInstConstants.Status.OPENED.code)
                    || currentTicket.getTicketStatus().equals(ProcInstConstants.Status.PROCESSING.code)
                    || currentTicket.getTicketStatus().equals(ProcInstConstants.Status.COMPLETED.code)
                    || currentTicket.getTicketStatus().equals(ProcInstConstants.Status.CLOSED.code)
                    || currentTicket.getTicketStatus().equals(ProcInstConstants.Status.DELETED.code)
                    || currentTicket.getTicketStatus().equals(ProcInstConstants.Status.CANCEL.code)) {
                //Với trường hợp ticket đang chạy thì không cho update
                actionCode = TaskActionConstants.Action.UPDATE_TICKET.code;
                return ResponseHelper.invalid(common.getMessage("ticket.processing-cant-update"));
            }
        }

        //Lưu nháp thì không thực hiện nghiệp vụ vội, tự tạo id luôn không call camuda
        if (startProcessInstanceDto.getIsDraft()) {
            mapResponse = createDraftTicket(currentTicket, ticketId, title, account, procDefId, startProcessInstanceDto, startProcessInstanceDto.getNotifyUsers(), startProcessInstanceDto.getNotifyGroups(), startProcessInstanceDto.getChartId());
        } else {
            mapResponse = createNormalTicket(ticketId, title, account, procDefId, startProcessInstanceDto, actionCode, currentTicket);
        }

        return ResponseHelper.ok(mapResponse);
    }

    public void handleSaveVersionTemplateProcinst(Map<String, Object> mapResponse, String procDefId) {
        try {
            //Xử lý lưu version biểu mẫu
            Long ticketId = (Long) mapResponse.get("ticketId");
            log.info("SAVE TEMPLATE - {}", ticketId);
            if (ticketId != null) {
                BpmProcdef bpmProcdef = bpmProcdefRepository.findByProcDefId(procDefId);
                BpmProcInst bpmProcInst = bpmProcInstRepository.findById(ticketId).orElse(null);
                if (bpmProcdef != null && bpmProcInst != null) {
                    log.info("SAVE TEMPLATE 2 - {} -", ticketId);
                    BpmnModelInstance bpmnModelInstance = CamundaUtils.convertBytesToBpmnModelInstance(bpmProcdef.getBytes());

                    String startEventProcinst = bpmProcInst.getTicketStartActId();
                    Collection<StartEvent> startEvents = bpmnModelInstance.getModelElementsByType(StartEvent.class);
                    StartEvent startEvent = startEvents.stream().filter(i -> i.getId().equals(startEventProcinst)).findFirst().orElse(null);
                    log.info("SAVE TEMPLATE 3 - {} - {}", ticketId, startEvent);
                    if (startEvent != null) {
                        List<TemplateHistoryResponse> response = templateHistoryRepository.findFirstByUrlNameAndCreatedDateBeforeOrderByIdDesc(startEvent.getCamundaFormKey());
                        if (response != null)
                            bpmProcInst.setTemplateVersionId(response.get(0).getId());
                        if (bpmProcdef.getSpecialFlow() != null) {
                            List<TemplateHistoryResponse> templateHisParent = templateHistoryRepository.findTemplateHistoryByChildTemplateId(response.get(0).getTemplateId());
                            if (templateHisParent != null && !templateHisParent.isEmpty() && !templateHisParent.get(0).getId().equals(bpmProcInst.getTemplateVersionId()))
                                bpmProcInst.setParentTemplateVersionId(templateHisParent.get(0).getId());
                        }
                    }
                    log.info("SAVE TEMPLATE 4 - {} - {}", ticketId, bpmProcInst.getTemplateVersionId());
                    bpmProcInstRepository.save(bpmProcInst);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public ResponseEntity<?> createTicketBasicAuth(String procDefId, Long ticketId, StartProcessInstanceDto startProcessInstanceDto) throws Exception {
        String account;
        if (ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getAccount())) {
            return ResponseHelper.invalid(common.getMessage("ticket.missing-account"));
        } else account = startProcessInstanceDto.getAccount();
        VariableValueDto requestSubject = startProcessInstanceDto.getVariables().get("requestSubject");

        if (startProcessInstanceDto.getServiceId() == null) {
            return ResponseHelper.invalid(common.getMessage("ticket.missing-serviceId"));
        }

        List<Map<String, Object>> chartDetail = bpmService.findChartAndChartNodeCreateTicket(startProcessInstanceDto, account, true);
        if (chartDetail == null) {
            return ResponseHelper.invalid(common.getMessage("ticket.error-chart"));
        }

        // Tạo phiếu phân hệ check specialFlow
        ServicePackage servicePackage = servicePackageManager.getServicePackageById(startProcessInstanceDto.getServiceId());
        if (ValidationUtils.isNullOrEmpty(servicePackage)) {
            return ResponseHelper.invalid(common.getMessage("ticket.missing-service"));
        }
        if (servicePackage.getSpecialFlow() != null && servicePackage.getSpecialFlow() && servicePackage.getParentCompanyCode() != null) {
            List<String> listParentCompanyCode = ObjectUtils.toObject(servicePackage.getParentCompanyCode(), new TypeReference<>() {
            });
            String companyCode = chartDetail.get(0).get("companyCode").toString();
            if (!ValidationUtils.isNullOrEmpty(listParentCompanyCode) && listParentCompanyCode.contains(companyCode)) {
                ServicePackage servicePackageChild = servicePackageRepository.findServicePackageBySpecialParentIdAndSpecialCompanyCode(servicePackage.getId(), companyCode);
                BpmProcdef bpmProcdef = bpmProcdefManager.getByServiceId(servicePackageChild.getId());
                // set lại service và process theo companyCode
                procDefId = bpmProcdef.getProcDefId();
                startProcessInstanceDto.setServiceId(servicePackageChild.getId());
            }
        }

        //Kiểm tra vùng mặc định nếu k có tự động lưu thêm
        handleGetDefaultCreateTicketBasicAuth(startProcessInstanceDto, procDefId, chartDetail);

        //Fix cứng ng đệ trình vì trên FE phải sửa lại cấu trúc lưu riêng biệt null và undefided
        VariableValueDto ngDeTrinh = new VariableValueDto();
        ngDeTrinh.setType("String");
        ngDeTrinh.setValue(startProcessInstanceDto.getAccount());
        ngDeTrinh.setValueInfo(null);
        startProcessInstanceDto.getVariables().put("start_slt_nguoiDeTrinh", ngDeTrinh);
        //Fix cứng ng đệ trình vì trên FE phải sửa lại cấu trúc lưu riêng biệt null và undefided


        BpmProcInst currentTicket = null;
        Map<String, Object> mapResponse;

        //Validate
        if (startProcessInstanceDto.getVariables() == null) {
            return ResponseHelper.invalid(common.getMessage("ticket.missing-variables"));
        }

        String title = requestSubject == null ? "" : StringUtil.nvl(requestSubject.getValue(), "");
        if (title == null || title.trim().isEmpty()) {
            return ResponseHelper.invalid(common.getMessage("ticket.missing-title"));
        }
        if (title.length() > 400) {
            return ResponseHelper.invalid(common.getMessage("ticket.maxlength-title"));
        }

        //get currentTicket
        if (ticketId != null && !Objects.equals(ticketId, 0L)) {
            currentTicket = findById(ticketId);
        }

        if (startProcessInstanceDto.getIsDraft() == null) {
            startProcessInstanceDto.setIsDraft(false);
        }

        String actionCode = TaskActionConstants.Action.CREATE_TICKET.code;
        if (currentTicket != null) { //Đã có ticket
            //Kiểm tra khác user không cho update
            if (currentTicket.getCreatedUser() != null && !currentTicket.getCreatedUser().equalsIgnoreCase(account)
                    && !currentTicket.getTicketStartUserId().equalsIgnoreCase(account) // Case bàn giao công việc
            ) {
                return ResponseHelper.invalid(common.getMessage("ticket.different-user"));
            }

            //set submisstion type id to currentTicket
            currentTicket.setSubmissionTypeId(startProcessInstanceDto.getSubmissionType());
            currentTicket.setSubmissionTypeName(startProcessInstanceDto.getSubmissionTypeName());

            Map<String, Object> mapFinal = new HashMap<>();
            mapFinal.put("startKey", currentTicket.getTicketStartActId());
            mapFinal.put("procInstId", currentTicket.getTicketProcInstId());
            mapFinal.put("ticketId", ticketId);
            mapFinal.put("requestCode", currentTicket.getRequestCode());

            //Xử lý bình thường
            if (currentTicket.getTicketStatus().equals(ProcInstConstants.Status.DRAFT.code)) {//Đang là lưu nháp

                currentTicket.setTicketTitle(title);
                currentTicket.setEmailNotification(startProcessInstanceDto.getReceiveMail());

                createBpmProcinstLink(currentTicket.getTicketId(), startProcessInstanceDto.getLinkProcInstId());

                if (startProcessInstanceDto.getIsDraft().equals(Boolean.FALSE)) { //Lưu thật
                    //Vì lưu nhap chưa call vào core camuda nên khi submit form cần thực hiện lại đủ nghiệp vụ
                    deleteBpmVariables(currentTicket.getTicketProcInstId());
                    mapResponse = createNormalTicket(ticketId, title, account, procDefId, startProcessInstanceDto, TaskActionConstants.Action.DRAFT_TO_OPEN.code, currentTicket);
                    mapFinal.putAll(mapResponse);
                    mapFinal.put("url", port.substring(0, port.lastIndexOf('/')) + CONTEXT_PATH_VIEW_TICKET + "/" + currentTicket.getTicketProcDefId() + "/" + currentTicket.getTicketId() + "/" + currentTicket.getTicketStartActId());

                    try {
                        //Xử lý lưu version biểu mẫu
                        handleSaveVersionTemplateProcinst(mapResponse, procDefId);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        return ResponseHelper.invalid(common.getMessage("ticket.save-version-template-false"));
                    }
                } else { //Lưu nháp đè
                    createDraftTicket(currentTicket, ticketId, title, account, procDefId, startProcessInstanceDto, startProcessInstanceDto.getNotifyUsers(), startProcessInstanceDto.getNotifyGroups(), startProcessInstanceDto.getChartId());
                    mapFinal.put("url", port.substring(0, port.lastIndexOf('/')) + CONTEXT_PATH + "/" + currentTicket.getServiceId() + "/" + procDefId + "?clone=" + currentTicket.getTicketProcInstId() + "??" + currentTicket.getTicketId() + "??isDraft");
                }

                return ResponseHelper.ok(mapFinal);
            } else if (startProcessInstanceDto.getAppCode() != null
                    && (startProcessInstanceDto.getAppCode().equalsIgnoreCase("CONS") || startProcessInstanceDto.getAppCode().equalsIgnoreCase("IHRP") || startProcessInstanceDto.getAppCode().equalsIgnoreCase("SAP"))
                    && (currentTicket.getTicketStatus().equals(ProcInstConstants.Status.DELETED_BY_RU.code) || currentTicket.getTicketStatus().equals(ProcInstConstants.Status.RECALLED.code))
            ) {//Trả lại bên ECON/ IHRP/ SAP gửi sang thì chỉ update biến
                //lấy full variables
                Map<String, VariableValueDto> variables = getFinalVariables(currentTicket, startProcessInstanceDto);
                // delete old variables
                deleteBpmVariables(currentTicket.getTicketProcInstId());
                bpmTaskManager.saveTaskVari(variables, null, currentTicket.getTicketProcInstId());
                // update variables camunda
                bpmTaskManager.updateCamundaVariables(currentTicket.getTicketProcInstId());
                mapFinal.put("url", port.substring(0, port.lastIndexOf('/')) + CONTEXT_PATH_VIEW_TICKET + "/" + currentTicket.getTicketProcDefId() + "/" + currentTicket.getTicketId() + "/" + currentTicket.getTicketStartActId());
                return ResponseHelper.ok(mapFinal);
            } else if (currentTicket.getTicketStatus().equals(ProcInstConstants.Status.DELETED_BY_RU.code) || currentTicket.getTicketStatus().equals(ProcInstConstants.Status.RECALLED.code)) {//Trả lại - Thu hồi
                //Với trường hợp trả lại mà đang truyền isDraft = true thì đổi lại để chạy luồng nghiệp vụ bình thường
                //Bên econ không truyền lại các biến khi thực hiện lại=> cần lấy lại từ db trường nào econ k truyền thì lấy lại
                Map<String, VariableValueDto> variables = getFinalVariables(currentTicket, startProcessInstanceDto);
                startProcessInstanceDto.setVariables(variables);
                startProcessInstanceDto.setIsDraft(false);
                actionCode = TaskActionConstants.Action.REDO.code;
            } else if (currentTicket.getTicketStatus().equals(ProcInstConstants.Status.ADDITIONAL_REQUEST.code)) {//Yêu cầu bổ sung
                Map<String, VariableValueDto> variables = getFinalVariables(currentTicket, startProcessInstanceDto);
                startProcessInstanceDto.setVariables(variables);
                startProcessInstanceDto.setTicketId(ticketId);
                updateTicketAdditionalRequest(currentTicket, startProcessInstanceDto);
            } else if (currentTicket.getTicketStatus().equals(ProcInstConstants.Status.OPENED.code)
                    || currentTicket.getTicketStatus().equals(ProcInstConstants.Status.PROCESSING.code)
                    || currentTicket.getTicketStatus().equals(ProcInstConstants.Status.COMPLETED.code)
                    || currentTicket.getTicketStatus().equals(ProcInstConstants.Status.CLOSED.code)
                    || currentTicket.getTicketStatus().equals(ProcInstConstants.Status.DELETED.code)
                    || currentTicket.getTicketStatus().equals(ProcInstConstants.Status.CANCEL.code)) {
                //Với trường hợp ticket đang chạy thì không cho update
                return ResponseHelper.invalid(common.getMessage("ticket.processing-cant-update"));
            }
        }

        //Lưu nháp thì không thực hiện nghiệp vụ vội, tự tạo id luôn không call camuda
        if (startProcessInstanceDto.getIsDraft()) {
            mapResponse = createDraftTicket(currentTicket, ticketId, title, account, procDefId, startProcessInstanceDto, startProcessInstanceDto.getNotifyUsers(), startProcessInstanceDto.getNotifyGroups(), startProcessInstanceDto.getChartId());
        } else {
            mapResponse = createNormalTicket(ticketId, title, account, procDefId, startProcessInstanceDto, actionCode, currentTicket);
            try {
                //Xử lý lưu version biểu mẫu
                handleSaveVersionTemplateProcinst(mapResponse, procDefId);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return ResponseHelper.invalid(common.getMessage("ticket.save-version-template-false"));
            }

        }

        return ResponseHelper.ok(mapResponse);
    }

    private Map<String, Object> createNormalTicket(Long ticketId, String title, String account, String procDefId,
                                                   StartProcessInstanceDto startProcessInstanceDto,
                                                   String actionCode, BpmProcInst bpmProcInst) throws Exception {
        //Lấy ra trường liên kết từ phiếu để kiểm tra xem có phải tờ trình ủy quyền k
        List<Long> linkProcInstId = startProcessInstanceDto.getLinkProcInstId();

        Map<String, Object> responseData = new HashMap<>();
        Map<String, VariableValueDto> variables = startProcessInstanceDto.getVariables();

        String taskDefKey = null;
        if (!ValidationUtils.isNullOrEmpty(bpmProcInst)) {
            taskDefKey = bpmProcInst.getTicketStartActId();
        }
        setIsDraftVariable(variables, false, taskDefKey);

        // Khi tạo mới các biến có kiểu là AUTO thì tự sinh giá trị cho biến đó
        Set<String> lstProperties = variables.keySet();
        for (String key : lstProperties) {
            VariableValueDto variableItem = variables.get(key);
            if (variableItem != null && variableItem.getType() != null && variableItem.getType().equalsIgnoreCase("AUTO")) {
                log.info("Create ticket auto variable: {}", variableItem.getValue());
                variableItem.setType("String");
                String sequenceTemplate = StringUtil.nvl(variableItem.getValue(), "");
                if (sequenceTemplate != null && !sequenceTemplate.trim().isEmpty()) {
                    String requestCode = codeGenConfigService.autoGenerateCodeTicket(Long.valueOf(sequenceTemplate), null);
                    variableItem.setValue(requestCode);
                    startProcessInstanceDto.setRequestCode(requestCode);
                }
                variables.put(key, variableItem);
            }
        }
        startProcessInstanceDto.setVariables(variables);

        Map<String, Object> mapData = camundaEngineService.submitForm(procDefId, startProcessInstanceDto);
        String procInstId = mapData.get("id").toString();

        /* BEGIN handle call action api on beginning */
        // set some values for before call
        if (bpmProcInst != null) {
            bpmProcInst.setTicketEditTime(LocalDateTime.now());
            // HMTC
            bpmProcInst.setApprovedBudgetIds(ObjectUtils.toJson(startProcessInstanceDto.getApprovedBudgetIds()));
        }
        // Optional variable
        Map<String, Object> optVariable = new HashMap<>();
        optVariable.put("actionCode", actionCode);
        // Optional variable
        if (!ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getLinkProcInstId())) {
            List<BpmProcInst> lstTicketLink = bpmProcInstRepository.findBpmProcInstByticketIdIn(startProcessInstanceDto.getLinkProcInstId());
            if (!ValidationUtils.isNullOrEmpty(lstTicketLink)) {
                String requestCode = lstTicketLink.get(0).getRequestCode();
                optVariable.put("linkProcInstId", requestCode);
            }
        }
        optVariable.put("serviceIdApi", startProcessInstanceDto.getServiceId());
        optVariable.put("accountLogin", account);
        optVariable.put("legislativeId", startProcessInstanceDto.getLegislativeId());

        ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(
                actionCode,
                procDefId,
                null,
                bpmProcInst != null ? bpmProcInst.getTicketProcInstId() : procInstId,
                actionApiService.createVariablesMap(startProcessInstanceDto.getVariables(), bpmProcInst, optVariable));

        // check response of api call BEFORE
        actionApiService.setActionApiResponse(actionApiContext, responseData);

        // set back variables when call BEFORE
        setVariableCallBefore(actionApiContext, startProcessInstanceDto);

        // check allow post process of api call BEFORE
        if (!actionApiContext.isAllowPostProcess()) {
            camundaEngineService.deleteProcessInstance(procInstId);
            // Ticket draft xử lý riêng
            if (actionCode.equalsIgnoreCase(TaskActionConstants.Action.DRAFT_TO_OPEN.code)) {
                bpmTaskManager.saveTaskVari(startProcessInstanceDto.getVariables(), null, bpmProcInst.getTicketProcInstId());
                responseData.put("ticketId", null);
                finishCreateTicket(bpmProcInst.getTicketProcInstId(), null, actionApiContext, startProcessInstanceDto.getVariables(), null, optVariable);
            } else {
                finishCreateTicket(procInstId, null, actionApiContext, startProcessInstanceDto.getVariables(), null, optVariable);
            }
            return responseData;
        }
        /* END handle call action api on beginning */

        bpmTaskManager.saveTaskVari(startProcessInstanceDto.getVariables(), null, procInstId);

        responseData.putAll(saveNormalTicket(procInstId, bpmProcInst, title, account, startProcessInstanceDto));

        Long savedTicketId = (Long.valueOf(responseData.get("ticketId").toString()));

        bpmTaskManager.saveTaskForCreateTicket(procInstId,
                savedTicketId, procDefId,
                startProcessInstanceDto.getIsDraft(),
                linkProcInstId, responseData.get("procInstId").toString(), startProcessInstanceDto,
                bpmProcInst != null ? bpmProcInst.getTicketCreatedTime() : LocalDateTime.now(),
                account);

        /* BEGIN handle call action api at the end */
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // Chỉ chạy sau khi transaction commit
                finishCreateTicket(procInstId, savedTicketId, actionApiContext,
                        startProcessInstanceDto.getVariables(), bpmProcInst, optVariable);
            }
        });
        /* END handle call action api at the end */

        // create signZone
        if (!ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getPrintSignZoneRequest())) {
            startProcessInstanceDto.getPrintSignZoneRequest().setProcInstId(procInstId);
            startProcessInstanceDto.getPrintSignZoneRequest().setUsername(account);
            // save temp ticket sign zone
            try {
                bpmTpSignZoneManager.processCreateSignZone(startProcessInstanceDto.getPrintSignZoneRequest());
            } catch (Exception e) {
                log.error("Error when create sign zone: {}", e.getMessage());
            }
        }

        // manage share ticket
        ProcessShareTicketRequest request = new ProcessShareTicketRequest();
        request.setProcInstId(procInstId);
        request.setProcDefId(procDefId);
        request.setCompanyCode(startProcessInstanceDto.getCompanyCode());
        request.setCreatedUser(account);
        request.setChartNodeCode(startProcessInstanceDto.getChartNodeCode());
        request.setServiceId(startProcessInstanceDto.getServiceId().toString());
        request.setCreatedDate(LocalDate.now());
        kafkaTemplate.send(topicShareTicket, request);

        reportByGroupServiceNew.deleteTaskReturnExist(savedTicketId);
        dashboardTicketService.deleteTaskReturnExist(savedTicketId);

        return responseData;
    }

    public void createReport(String procInstIdSave, Long savedTicketId) {

        log.info("Processing import data to report ...... ");

        /* call report by chart node */
        List<BpmTask> tasks = bpmTaskRepository.getBpmTaskByTaskProcInstId(procInstIdSave);

        if (!ValidationUtils.isNullOrEmpty(tasks)) {
            for (BpmTask e : tasks) {
                reportProducer.sendKafka(e.getTaskId(), topicInsertReportByChartNode);
            }
        }

        /* call report by group */
        reportProducer.sendKafka(savedTicketId, insertReportByGroupTopic);
    }

    private Map<String, Object> createDraftTicket(BpmProcInst bpmProcInst, Long ticketId, String title, String account, String procDefId, StartProcessInstanceDto startProcessInstanceDto, List<String> users, List<String> groups, Long chartId) {
        Map<String, Object> responseData = new HashMap<>();

        /* BEGIN handle call action api on beginning */
        ActionApiContext actionApiContext;
        /* END handle call action api on beginning */

        String procInstId;
        Long savedTicketId = null;
        Map<String, VariableValueDto> variables = startProcessInstanceDto.getVariables();

        String taskDefKey = null;
        if (!ValidationUtils.isNullOrEmpty(bpmProcInst)) {
            taskDefKey = bpmProcInst.getTicketStartActId();
        }
        setIsDraftVariable(variables, true, taskDefKey);

        // Khi tạo mới các biến có kiểu là AUTO thì tự sinh giá trị cho biến đó
        Set<String> lstProperties = variables.keySet();
        for (String key : lstProperties) {
            VariableValueDto variableItem = variables.get(key);
            if (variableItem != null && variableItem.getType() != null && variableItem.getType().equalsIgnoreCase("AUTO")) {
                variableItem.setType("String");
                String sequenceTemplate = StringUtil.nvl(variableItem.getValue(), "");
                if (sequenceTemplate != null && !sequenceTemplate.trim().isEmpty()) {
//                        variableItem.setValue(sequenceDataManager.generateIDBySequenceTemplate(sequenceTemplate));
                    String requestCode = codeGenConfigService.autoGenerateCodeTicket(Long.valueOf(sequenceTemplate), null);
                    variableItem.setValue(requestCode);
                    startProcessInstanceDto.setRequestCode(requestCode);
                }
                variables.put(key, variableItem);
            }
        }
        startProcessInstanceDto.setVariables(variables);

        if (bpmProcInst != null) {
            procInstId = bpmProcInst.getTicketProcInstId();

            variables = getFinalVariables(bpmProcInst, startProcessInstanceDto);

            /* BEGIN call action api */
            // set some values for before call
            bpmProcInst.setTicketEditTime(LocalDateTime.now());

            actionApiContext = actionApiService.beginHandleActionApi(TaskActionConstants.Action.UPDATE_DRAFT_TICKET.code,
                    procDefId,
                    null,
                    procInstId,
                    actionApiService.createVariablesMap(variables, bpmProcInst));

            // check response of api call BEFORE
            actionApiService.setActionApiResponse(actionApiContext, responseData);

            // set back variables when call BEFORE
            setVariableCallBefore(actionApiContext, startProcessInstanceDto);

            // check allow post process of api call BEFORE
            if (!actionApiContext.isAllowPostProcess()) {
                return responseData;
            }
            /* END call action api */

            createBpmProcinstLink(bpmProcInst.getTicketId(), startProcessInstanceDto.getLinkProcInstId());
            responseData.putAll(saveDraftTicket(procInstId, bpmProcInst, title, account, procDefId, startProcessInstanceDto));
            deleteBpmVariables(bpmProcInst.getTicketProcInstId());
            bpmTaskManager.saveTaskVari(variables, null, bpmProcInst.getTicketProcInstId());

            if (users != null) {
                bpmProcInstNotifyManager.deleteAllByBpmProcinstId(bpmProcInst.getTicketId());

                List<BpmProInstNotifyUser> bpmListProInstNotifyUser = new ArrayList<>();
                for (String user : users) {
                    BpmProInstNotifyUser bpmProInstNotifyUser = new BpmProInstNotifyUser();
                    bpmProInstNotifyUser.setBpmProcinstId(bpmProcInst.getTicketId());
                    bpmProInstNotifyUser.setRecipient(user);
                    bpmProInstNotifyUser.setCreateUser(account);
                    bpmProInstNotifyUser.setCreatedTime(LocalDateTime.now());
                    bpmListProInstNotifyUser.add(bpmProInstNotifyUser);
                }
                bpmProcInstNotifyManager.saveAll(bpmListProInstNotifyUser);
            }

            if (groups != null) {
                bpmProInstNotifyGroupManager.deleteAllByBpmProcinstId(bpmProcInst.getTicketId());
                List<BpmProInstNotifyGroup> bpmProInstNotifyGroups = new ArrayList<>();
                for (String group : groups) {
                    BpmProInstNotifyGroup bpmProInstNotifyGroup = new BpmProInstNotifyGroup();
                    bpmProInstNotifyGroup.setBpmProcinstId(bpmProcInst.getTicketId());
                    bpmProInstNotifyGroup.setGroupId(group);
                    bpmProInstNotifyGroup.setCreateUser(account);
                    bpmProInstNotifyGroup.setCreatedTime(LocalDateTime.now());
                    bpmProInstNotifyGroup.setChartId(chartId);
                    bpmProInstNotifyGroups.add(bpmProInstNotifyGroup);
                }
                bpmProInstNotifyGroupManager.saveAll(bpmProInstNotifyGroups);
            }
        } else {
            UUID uuid = UUID.randomUUID();
            procInstId = uuid.toString();

            /* BEGIN call action api */
            actionApiContext = actionApiService.beginHandleActionApi(TaskActionConstants.Action.CREATE_DRAFT_TICKET.code,
                    procDefId,
                    null,
                    procInstId,
                    actionApiService.createVariablesMap(variables));

            // check response of api call BEFORE
            actionApiService.setActionApiResponse(actionApiContext, responseData);

            // set back variables when call BEFORE
            setVariableCallBefore(actionApiContext, startProcessInstanceDto);

            // check allow post process of api call BEFORE
            if (!actionApiContext.isAllowPostProcess()) {
                finishCreateTicket(procInstId, null, actionApiContext, variables, null, null);
                return responseData;
            }
            /* END call action api */

            bpmTaskManager.saveTaskVari(startProcessInstanceDto.getVariables(), null, procInstId, true);
            responseData.putAll(saveDraftTicket(procInstId, null, title, account, procDefId, startProcessInstanceDto));

            savedTicketId = (Long.valueOf(responseData.get("ticketId").toString()));
        }

        /* BEGIN handle call action api at the end */
        finishCreateTicket(procInstId, savedTicketId, actionApiContext, variables, null, null);
        /* END handle call action api at the end */

        //Phải tách riêng vì với luồng gửi thông báo thì xử lý khác so với lưu nháp
        if (!StringUtil.isEmpty(responseData.get("ticketId"))
                && startProcessInstanceDto.getAssistantEmail() != null
                && startProcessInstanceDto.getAssistantEmail().length > 0) {
            saveAssistant(startProcessInstanceDto.getAssistantEmail(), String.valueOf(responseData.get("ticketId")));
        }

        //Get Assistant from ChartNode
        if (!StringUtil.isEmpty(responseData.get("ticketId"))
                && startProcessInstanceDto.getChartId() != null
                && Boolean.TRUE.equals(startProcessInstanceDto.getIsAssistant())) {
//            List<String> emailAssignee = bpmTaskRepository.getAllAssigneeOfTicket(responseData.get("procInstId").toString());
            try {
                List<String> emailAssignee = getAllListUserNameUserTask(ticketId);
                getAssistantFromChartNode(String.join(",", emailAssignee), String.valueOf(responseData.get("ticketId")), String.join(",", credentialHelper.getJWTPayload().getUsername()));
            } catch (Exception e) {
                log.error("Error when get all assignee of ticket: {}", e.getMessage());
            }
        }

        return responseData;
    }

    /**
     * Handle call action api at the end
     */
    private void finishCreateTicket(String procInstId, Long savedTicketId,
                                    ActionApiContext actionApiContext, Map<String, VariableValueDto> variables, BpmProcInst bpmProcinstNew,
                                    Map<String, Object> optVariable
    ) {
        actionApiService.sendLogAfter(procInstId, savedTicketId);

        if (savedTicketId != null && !savedTicketId.equals(0L)) {
            actionApiService.endHandleActionApi(
                    actionApiContext,
                    savedTicketId,
                    procInstId,
                    actionApiService.createVariablesMap(
                            variables,
                            bpmProcinstNew != null ? bpmProcinstNew : findById(savedTicketId),
                            optVariable
                    ));
        }
    }

    private void setIsDraftVariable(Map<String, VariableValueDto> variables, boolean isDraft, String taskDefKey) {
        if (!ValidationUtils.isNullOrEmpty(variables)) {
            VariableValueDto valueDto = new VariableValueDto();
            valueDto.setType(org.springframework.util.StringUtils.capitalize(CommonConstants.VarType.STRING.toLowerCase()));
            valueDto.setValue(isDraft);
            variables.put("isDraft", valueDto);
            if (!ValidationUtils.isNullOrEmpty(taskDefKey)) {
                VariableValueDto valueTaskDefKey = new VariableValueDto();
                valueTaskDefKey.setType(org.springframework.util.StringUtils.capitalize(CommonConstants.VarType.STRING.toLowerCase()));
                valueTaskDefKey.setValue(taskDefKey);
                variables.put("taskDefKey", valueTaskDefKey);
            }
        }
    }

    private void setVariableCallBefore(@NonNull ActionApiContext actionApiContext, StartProcessInstanceDto startProcessInstanceDto) {
        Map<String, VariableValueDto> variableValueDtoMap = actionApiService.getVariableFromResult(actionApiContext.getApiExecuteBeforeActionResults());
        if (!ValidationUtils.isNullOrEmpty(variableValueDtoMap)) {
            if (startProcessInstanceDto.getVariables() == null) {
                startProcessInstanceDto.setVariables(variableValueDtoMap);
            } else {
                Map<String, VariableValueDto> newVariables = new HashMap<>(startProcessInstanceDto.getVariables());
                newVariables.putAll(variableValueDtoMap);
                startProcessInstanceDto.setVariables(newVariables);
//                startProcessInstanceDto.getVariables().putAll(variableValueDtoMap);
            }
        }
    }

    public String[] getAssistantFromChartNode(String emailAssignee, String tiketId, String createUser) {
        try {
            String[] lstAssitant = customerService.getAssistantByChartId(emailAssignee, createUser);
            Set<String> uniqueSet = new HashSet<>(Arrays.asList(lstAssitant));
            String[] uniqueArr = uniqueSet.toArray(new String[uniqueSet.size()]);
            if (uniqueArr.length > 0) {
                saveAssistant(uniqueArr, tiketId);
                return uniqueArr;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    private void saveAssistant(String[] assistants, String ticketId) {
        List<Assistant> res = new ArrayList<>();
        for (String assistant : assistants) {
            res.add(
                    Assistant.builder()
                            .ticketId(ticketId)
                            .assistantEmail(assistant)
                            .createAt(new Date())
                            .updateAt(new Date())
                            .build()
            );
        }
        assistantRepo.saveAll(res);
    }

    private Map<String, VariableValueDto> getFinalVariables(@NonNull BpmProcInst currentTicket, @NonNull StartProcessInstanceDto startProcessInstanceDto) {
        List<Map<String, VariableValueDto>> variableInstList = actHiVarInstManager.getVariByTicketConvertToVariableValueDto(currentTicket.getTicketProcInstId());

        Map<String, VariableValueDto> variables = startProcessInstanceDto.getVariables();
        Map<String, VariableValueDto> saveMultiFile = new HashMap<>();
        //Kiểm tra nếu biến đó lưu rồi thì lưu đè vào
        if (variableInstList != null && !variableInstList.isEmpty() && variables != null) {
            for (Map<String, VariableValueDto> item : variableInstList) {
                if (item != null) {
                    Set<String> lstProperties = item.keySet();
                    for (String key : lstProperties) {
                        if (key != null && !variables.containsKey(key)) {
                            //multifile thì xử lý
                            if (item.get(key).getType() != null
                                    && item.get(key).getType().equalsIgnoreCase("FILE")
                                    && key.contains("_multiFile_")) {
                                String firstFile = key.substring(0, key.indexOf("_multiFile_")) + "_multiFile_0";
                                //Không chứa file đầu tiền thì lấy lại trong db
                                if (!variables.containsKey(firstFile)) {
                                    saveMultiFile.put(key, item.get(key));
                                }
                            } else {
                                variables.put(key, item.get(key));
                            }
                        }
                    }
                }
            }
        }
        if (variables != null) {
            variables.putAll(saveMultiFile);
        }
        // remove các variable truyền sang có value null và valueInfor null
//        if (!ValidationUtils.isNullOrEmpty(variables)) {
//            Set<String> listKeys = variables.keySet();
//            for (String key : listKeys) {
//                if (variables.get(key).getValue() == null && variables.get(key).getValueInfo() == null) {
//                    variables.remove(key);
//                }
//            }
//        }
        return variables;
    }

    public Boolean updateTicketAdditionalRequest(BpmProcInst currentTicket, StartProcessInstanceDto startProcessInstanceDto) {
        /* BEGIN handle call action api on beginning */
        ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(
                TaskActionConstants.Action.ADD_INFO.code,
                currentTicket.getTicketProcDefId(),
                null,
                currentTicket.getTicketProcInstId(),
                actionApiService.createVariablesMap(
                        startProcessInstanceDto.getVariables(),
                        currentTicket,
                        actionApiService.getTicketVariables(currentTicket.getTicketProcInstId())
                ));
        /* END handle call action api on beginning */
        if (currentTicket.getTicketStatus().equalsIgnoreCase(ProcInstConstants.Status.CANCEL.code)) {
            return false;
        }
        // delete old variables
        deleteBpmVariablesAdditionalRequest(currentTicket.getTicketProcInstId());

        bpmTaskManager.saveTaskVari(startProcessInstanceDto.getVariables(), null, currentTicket.getTicketProcInstId());

        // update variables camunda
        bpmTaskManager.updateCamundaVariables(currentTicket.getTicketProcInstId());

        // update status ticket
        updateTicketAdditionalRequest(currentTicket, startProcessInstanceDto, credentialHelper.getJWTPayload().getUsername());

        currentTicket = findById(startProcessInstanceDto.getTicketId());

        /* BEGIN handle call action api at the end */
        actionApiService.endHandleActionApi(actionApiContext, actionApiService.createVariablesMap(
                startProcessInstanceDto.getVariables(),
                currentTicket,
                actionApiService.getTicketVariables(currentTicket.getTicketProcInstId())
        ));
        /* END handle call action api at the end */


        /* Call report by group */
//        reportByGroupService.createReportByGroup(currentTicket.getTicketId());
        return true;
    }

    public BpmProcInst getById(String id) {
        return bpmProcInstRepository.findBpmProcInstByTicketProcInstId(id);
    }

    public void deleteDraft(List<Long> ids) {
        bpmProcInstRepository.deleteDraft(ids);
    }

    public Boolean deleteAllDraft(List<Long> ids) {
        boolean result = false;
        List<ActionApiContext> actionApiContexts = new ArrayList<>();
        List<Long> idDraft = new ArrayList<>();
        List<Long> idNonDraft = new ArrayList<>();
        List<String> procInstIdDraft = new ArrayList<>();
        // Optional variable
        Map<String, Object> optVariable = new HashMap<>();
        String account;
        try {
            account = credentialHelper.getJWTPayload().getUsername();
        } catch (Exception e) {
            account = appSuperAdminAccount;
        }
        optVariable.put("accountLogin", account);
        if (!ValidationUtils.isNullOrEmpty(ids)) {
            List<BpmProcInst> bpmProcInsts = findAllById(ids);
            if (!ValidationUtils.isNullOrEmpty(bpmProcInsts)) {
                bpmProcInsts.forEach(e -> {
                    if (!e.getTicketStatus().equalsIgnoreCase(ProcInstConstants.Status.DRAFT.code)) {
                        idNonDraft.add(e.getTicketId());
                    } else {
                        e.setTicketStatus(TaskActionConstants.Action.DELETE_DRAFT_TICKET.code);
                        e.setTicketEditTime(LocalDateTime.now());
                        Map<String, VariableValueDto> variables = actionApiService.getTicketVariables(e.getTicketProcInstId());
                        ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(TaskActionConstants.Action.DELETE_DRAFT_TICKET.code,
                                e.getTicketProcDefId(),
                                null,
                                e.getTicketProcInstId(),
                                actionApiService.createVariablesMap(e, variables, optVariable));
                        actionApiContexts.add(actionApiContext);
                        idDraft.add(e.getTicketId());
                        procInstIdDraft.add(e.getTicketProcInstId());
                    }

                });
            }
        }

        try {
            bpmProcInstRepository.deleteAllById(idDraft);
            bpmVariablesRepository.deleteBpmVariablesByProcInstIdIn(procInstIdDraft);
            deleteDraft(idNonDraft);
            result = true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        /* BEGIN handle call action api at the end */
        actionApiContexts.forEach(actionApiContext -> actionApiService.endHandleActionApi(actionApiContext, actionApiService.createVariablesMap(actionApiContext.getBpmProcInst(), optVariable)));
        /* END handle call action api at the end */

        return result;
    }

    public List<BpmProcInst> findAllById(List<Long> ids) {
        return bpmProcInstRepository.findAllById(ids);
    }

    public void deleteBpmVariables(String procInstId) {
        bpmVariablesRepository.deleteBpmVariablesByProcInstId(procInstId);
    }

    public void deleteBpmVariablesAdditionalRequest(String procInstId) {
        bpmVariablesRepository.deleteBpmVariablesByProcInstIdAndTaskIdIsNull(procInstId);
    }

    public void updateTicketAdditionalRequest(BpmProcInst currentTicket, StartProcessInstanceDto startProcessInstanceDto, String userName) {
        currentTicket.setTicketEditTime(LocalDateTime.now());
        currentTicket.setTicketStatus(ProcInstConstants.Status.PROCESSING.code);
        bpmProcInstRepository.save(currentTicket);
//        reportByGroupService.createReportByGroup(currentTicket.getTicketId());
        reportProducer.sendKafka(currentTicket.getTicketId(), insertReportByGroupTopic);

        List<BpmTask> bpmTasks = bpmTaskRepository.getAllTasksByStatus(Collections.singletonList(currentTicket.getTicketProcInstId()), TaskConstants.TabStatus.PROCESSING);
        for (BpmTask bpmTask : bpmTasks) {
            //Thông báo cho user liên quan
            NotificationUser request = new NotificationUser();
            request.setBpmProcdefId(null);
            request.setNextTaskDefKey(bpmTask.getTaskDefKey());
            request.setVariables(new HashMap<>());
            request.setTicketId(currentTicket.getTicketId());
            request.setIsGetOldVariable(true);
            request.setLstCustomerEmails(Collections.singletonList(bpmTask.getTaskAssignee()));
            request.setActionCode(ProcInstConstants.Notifications.ADDITIONAL_REQUEST_COMPLETED.code);
            request.setEmailExe(credentialHelper.getJWTPayload().getUsername());
            kafkaTemplate.send(notificationUser, request);
//            bpmProcdefNotificationService.addNotificationsByConfig(null, bpmTask.getTaskDefKey(), new HashMap<>(),
//                    currentTicket.getTicketId(), true, Arrays.asList(bpmTask.getTaskAssignee()), ProcInstConstants.Notifications.ADDITIONAL_REQUEST_COMPLETED.code);
        }
        saveAdditionalRequestHistory(currentTicket, startProcessInstanceDto, userName);
    }

    private void saveAdditionalRequestHistory(BpmProcInst currentTicket, StartProcessInstanceDto startProcessInstanceDto, String userName) {
        // Lấy receivedTime từ action ADDITIONAL
        BpmHistory recallingHis = bpmHistoryManager.getHistoryByActionAndProcInstId(TaskConstants.HistoryAction.ADDITIONAL.code, currentTicket.getTicketProcInstId());
        LocalDateTime receivedTime = null;
        if (!ValidationUtils.isNullOrEmpty(recallingHis)) {
            receivedTime = recallingHis.getCreatedTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        }
        HistoryDto historyDto = new HistoryDto();
        historyDto.setTicketId(currentTicket.getTicketId());
        historyDto.setFromTask(null);
        historyDto.setToTask(null);
        historyDto.setAction(TaskConstants.HistoryAction.UPDATE_TICKET.code);
        historyDto.setProcInstId(currentTicket.getTicketProcInstId());
        historyDto.setActionUser(userName);
        historyDto.setReceivedTime(receivedTime);

        // get action_user_info
        List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(userName);
        if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
            Map<String, Object> actionUserInfo = new HashMap<>();
            String userTitle = lstUserTitle.stream()
                    .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                    .map(title -> {
                        String strTitle = StringUtil.nvl(title.getTitle(), "");
                        int concurrently = title.getConcurrently();
                        return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                    })
                    .collect(Collectors.joining(" "));
            actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
            actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
            historyDto.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
        }
        bpmHistoryManager.saveHistory(historyDto);
    }

    public Map<String, Object> saveDraftTicket(String procInstId, BpmProcInst bpmProcInst, String title, String
            account, String procDefId, StartProcessInstanceDto startProcessInstanceDto) {
        Map<String, Object> mapFinal = new HashMap<>();
        try {
            Long serviceId = startProcessInstanceDto.getServiceId();
            String priority = startProcessInstanceDto.getPriority();
            Long priorityId = startProcessInstanceDto.getPriorityId();
            Long locationId = startProcessInstanceDto.getLocationId();
            Boolean emailNotification = startProcessInstanceDto.getReceiveMail();
            List<String> users = startProcessInstanceDto.getNotifyUsers();
            List<String> groups = startProcessInstanceDto.getNotifyGroups();
            Long chartId = startProcessInstanceDto.getChartId();

            String startKey = null;
            String endKey = null;
            double slaFinish = 0d;
            double slaResponse = 0d;

            BpmnModelInstance modelInstance = camundaEngineService.getModelInstance(procDefId);
            if (modelInstance != null) {
                // get start event
                StartEvent startEvent = CamundaUtils.getStartEvent(modelInstance);
                startKey = startEvent != null ? startEvent.getId() : null;

                // get temporary end node id
                EndEvent endEvent = CamundaUtils.getEndEvent(modelInstance);
                endKey = endEvent != null ? endEvent.getId() : null;

                // get user tasks
                Collection<UserTask> userTasks = CamundaUtils.getUserTasks(modelInstance);
                for (UserTask userTask : userTasks) {
                    // get properties setting
                    Collection<CamundaProperty> properties = CamundaUtils.getCamundaProperties(userTask);
                    for (CamundaProperty property : properties) {
                        String name = property.getCamundaName();
                        String value = property.getCamundaValue();
                        if (name != null) {
                            if (name.equalsIgnoreCase(TaskConstants.TaskProperty.SETTING_SLA_FINISH)) {
                                value = !ValidationUtils.isNullOrEmpty(value) ? value : defaultSlaFinish;
                                slaFinish += Double.parseDouble(value);
                            } else if (name.equalsIgnoreCase(TaskConstants.TaskProperty.SETTING_SLA_RESPONSE)) {
                                value = !ValidationUtils.isNullOrEmpty(value) ? value : defaultSlaResponse;
                                slaResponse += Double.parseDouble(value);
                            }
                        }
                    }
                }
            }

            if (bpmProcInst == null) {
                bpmProcInst = new BpmProcInst();

                // save app_code information
                bpmProcInst.setAppCode(startProcessInstanceDto.getAppCode());
            }

            bpmProcInst.setTicketProcInstId(procInstId);
            bpmProcInst.setTicketProcDefId(procDefId);
            bpmProcInst.setTicketCreatedTime(LocalDateTime.now());
            bpmProcInst.setTicketStartedTime(null);
            bpmProcInst.setTicketEndTime(null);
            bpmProcInst.setTicketDuration(null);
            bpmProcInst.setCreatedUser(account);
            bpmProcInst.setTicketStartUserId(account);
            bpmProcInst.setTicketStartActId(startKey);
            bpmProcInst.setTicketEndActId(endKey);
            bpmProcInst.setTicketEditTime(LocalDateTime.now());
            bpmProcInst.setTicketStatus(ProcInstConstants.Status.DRAFT.code);
            bpmProcInst.setServiceId(serviceId);
            bpmProcInst.setTicketTitle(title);
            bpmProcInst.setSlaFinish(slaFinish);
            bpmProcInst.setSlaResponse(slaResponse);
            bpmProcInst.setTicketCanceledTime(null);
            bpmProcInst.setTicketClosedTime(null);
            bpmProcInst.setTicketRating(0D);
            bpmProcInst.setTicketDescription(null);
            bpmProcInst.setPriority(priority);
            bpmProcInst.setLocationId(locationId);
            bpmProcInst.setPriorityId(priorityId);
            bpmProcInst.setEmailNotification(emailNotification);
            bpmProcInst.setChartId(startProcessInstanceDto.getChartId());
            bpmProcInst.setCompanyCode(startProcessInstanceDto.getCompanyCode());
            bpmProcInst.setChartName(startProcessInstanceDto.getChartName());
            bpmProcInst.setChartNodeName(startProcessInstanceDto.getChartNodeName());
            bpmProcInst.setChartNodeCode(startProcessInstanceDto.getChartNodeCode());
            bpmProcInst.setChartNodeId(startProcessInstanceDto.getChartNodeId());
            bpmProcInst.setIsAssistant(startProcessInstanceDto.getIsAssistant());
            //Thêm mã yêu cầu
            if (!ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getRequestCode())) {
                bpmProcInst.setRequestCode(startProcessInstanceDto.getRequestCode());
            }

            // get priorityId from priority_management_history
            if (!ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getPriorityId())) {
                List<PriorityManagementHistory> lstVersion = priorityHistoryRepository.findByPriorityId(startProcessInstanceDto.getPriorityId());
                if (!ValidationUtils.isNullOrEmpty(lstVersion)) {
                    Long priorityHistoryId = lstVersion.stream()
                            .sorted(Comparator.comparing(PriorityManagementHistory::getVersion).reversed())
                            .map(PriorityManagementHistory::getPriorityHistoryId)
                            .findFirst().orElse(startProcessInstanceDto.getPriorityId());
                    bpmProcInst.setPriorityHistoryId(priorityHistoryId);
                }
            }
            bpmProcInst.setLegislativeId(startProcessInstanceDto.getLegislativeId());

            BpmProcInst newTicket = bpmProcInstRepository.save(bpmProcInst);
            reportProducer.sendKafka(newTicket.getTicketId(), insertReportByGroupTopic);

            createBpmProcinstLink(newTicket.getTicketId(), startProcessInstanceDto.getLinkProcInstId());

            if (users != null) {
                List<BpmProInstNotifyUser> bpmListProInstNotifyUser = new ArrayList<>();
                for (String user : users) {
                    BpmProInstNotifyUser bpmProInstNotifyUser = new BpmProInstNotifyUser();
                    bpmProInstNotifyUser.setBpmProcinstId(bpmProcInst.getTicketId());
                    bpmProInstNotifyUser.setRecipient(user);
                    bpmProInstNotifyUser.setCreateUser(account);
                    bpmProInstNotifyUser.setCreatedTime(LocalDateTime.now());
                    bpmListProInstNotifyUser.add(bpmProInstNotifyUser);
                }
                bpmProcInstNotifyManager.saveAll(bpmListProInstNotifyUser);
            }
            if (groups != null) {
                List<BpmProInstNotifyGroup> bpmProInstNotifyGroups = new ArrayList<>();
                for (String group : groups) {
                    BpmProInstNotifyGroup bpmProInstNotifyGroup = new BpmProInstNotifyGroup();
                    bpmProInstNotifyGroup.setBpmProcinstId(bpmProcInst.getTicketId());
                    bpmProInstNotifyGroup.setGroupId(group);
                    bpmProInstNotifyGroup.setCreateUser(account);
                    bpmProInstNotifyGroup.setCreatedTime(LocalDateTime.now());
                    bpmProInstNotifyGroup.setChartId(chartId);
                    bpmProInstNotifyGroups.add(bpmProInstNotifyGroup);
                }
                bpmProInstNotifyGroupManager.saveAll(bpmProInstNotifyGroups);
            }


            mapFinal.put("startKey", null);
            mapFinal.put("procInstId", procInstId);
            mapFinal.put("ticketId", newTicket.getTicketId());
            mapFinal.put("requestCode", newTicket.getRequestCode());
            mapFinal.put("url", port.substring(0, port.lastIndexOf('/')) + CONTEXT_PATH + "/" + serviceId + "/" + procDefId + "?clone=" + procInstId + "??" + newTicket.getTicketId() + "??isDraft");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return mapFinal;
    }


    /// check legislative SLA
    public Map<String, Object> saveNormalTicket(String procInstId, BpmProcInst bpmProcInst, String title, String
            account, StartProcessInstanceDto startProcessInstanceDto) {
        try {
            Boolean isDraft = startProcessInstanceDto.getIsDraft();
            Long serviceId = startProcessInstanceDto.getServiceId();

            String oldProcInstId = null;
            TicketDefaultResponse oldDefaultField = new TicketDefaultResponse();
            if (!ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getOldProcInstId())) {
                oldProcInstId = startProcessInstanceDto.getOldProcInstId();

                // save old default field history
                if (!ValidationUtils.isNullOrEmpty(bpmProcInst)) {
                    oldDefaultField.setIsAssistant(bpmProcInst.getIsAssistant());
                    oldDefaultField.setLegislativeId(bpmProcInst.getLegislativeId());
                    oldDefaultField.setReceiveMail(bpmProcInst.getEmailNotification());
                    oldDefaultField.setPriorityId(bpmProcInst.getPriorityId());

                    if (!ValidationUtils.isNullOrEmpty(bpmProcInst.getLocationId())) {
                        List<Map<String, Object>> locationManagement = masterDataService.getLoadTemplateLocation(bpmProcInst.getLocationId());
                        LocationManagementResponse locationResponse = new LocationManagementResponse();
                        if (!ValidationUtils.isNullOrEmpty(locationManagement)) {
                            locationResponse.setId(bpmProcInst.getLocationId());
                            locationResponse.setLocationName(locationManagement.get(0).get("name").toString());
                            oldDefaultField.setLocationManagement(locationResponse);
                        }
                    }
                    List<Long> listLinkId = bpmProcinstLinkManager.getProcinstLinkByBpmProcinstId(bpmProcInst.getTicketId());
                    if (!ValidationUtils.isNullOrEmpty(listLinkId)) {
                        oldDefaultField.setLinkProcInstId(listLinkId);
                    }
                    // thông báo cho user liên quan
                    List<BpmProInstNotifyUser> lstUser = bpmProcInstNotifyManager.getDetail(bpmProcInst.getTicketId());
                    if (!ValidationUtils.isNullOrEmpty(lstUser)) {
                        List<String> lstNotifyUser = lstUser.stream().map(BpmProInstNotifyUser::getRecipient).toList();
                        if (!ValidationUtils.isNullOrEmpty(lstNotifyUser)) {
                            List<NotifyUserResponse> notifyUserResponses = new ArrayList<>();
                            for (String user : lstNotifyUser) {
                                NotifyUserResponse notifyUserResponse = new NotifyUserResponse();
                                notifyUserResponse.setUserEmail(user);
                                notifyUserResponses.add(notifyUserResponse);
                            }
                            oldDefaultField.setNotifyUsers(notifyUserResponses);
                        }
                    }

                    List<BpmProInstNotifyGroup> lstGroup = bpmProInstNotifyGroupManager.getDetail(bpmProcInst.getTicketId());
                    if (!ValidationUtils.isNullOrEmpty(lstGroup)) {
                        List<String> strNotifyGroups = lstGroup.stream().map(BpmProInstNotifyGroup::getGroupId).toList();
                        List<Long> oldNotifyGroups = strNotifyGroups.stream().map(Long::valueOf).collect(Collectors.toList());
                        if (!ValidationUtils.isNullOrEmpty(oldNotifyGroups)) {
                            oldDefaultField.setNotifyGroups(oldNotifyGroups);
                        }
                    }
                }
            }
            String priority = startProcessInstanceDto.getPriority();
            Long priorityId = startProcessInstanceDto.getPriorityId();
            Long locationId = startProcessInstanceDto.getLocationId();
            List<String> notifyUser = startProcessInstanceDto.getNotifyUsers();
            List<String> notifyGroups = startProcessInstanceDto.getNotifyGroups();
            Boolean receiveMail = startProcessInstanceDto.getReceiveMail();
            Long chartId = startProcessInstanceDto.getChartId();
            Long submissionTypeId = startProcessInstanceDto.getSubmissionType();
            String submissionTypeName = startProcessInstanceDto.getSubmissionTypeName();

            Map<String, Object> mapFinal = new HashMap<>();

            Map<String, Object> mapRuExc = camundaEngineService.getHiProcInstById(procInstId);

            String aProcessDefinitionId = mapRuExc.get("procDefId").toString();

            WorkFlowRequest workFlowRequest = new WorkFlowRequest();
            workFlowRequest.setProcDefId(aProcessDefinitionId);
            workFlowRequest.setProcInstId(mapRuExc.get("procInstId").toString());
            List<SproFlow> workFlow = sproService.getWorkFlow(workFlowRequest);
            List<SproFlowNode> flowNodes = workFlow.stream().map(SproFlow::getNodes).flatMap(Collection::stream).toList();
            SproFlowNode flowNodeEnd = flowNodes.stream().filter(x -> x.getType().equals("endEvent")).findFirst().orElse(null);

            Document doc = responseUtils.getXml(aProcessDefinitionId);
            NodeList nodes = doc.getElementsByTagName("bpmn:userTask");
            NodeList endNodes = doc.getElementsByTagName("bpmn:endEvent");
            Element endNode = (Element) endNodes.item(0);
            String endKey = flowNodeEnd != null ? flowNodeEnd.getId() : endNode != null ? endNode.getAttributes().getNamedItem("id").getNodeValue() : null;
            double slaFinish = 0d;
            double slaResponse = 0d;

            // legislative sla
            List<LegislativeProgramDetail> lstDetail = null;
            if (!ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getLegislativeId())) {
                lstDetail = legislativeProgramDetailRepository.findLegislativeProgramDetailByLegislativeIdAndType(startProcessInstanceDto.getLegislativeId(), LegislativeEnum.LegislativeDetailType.PROCESS_DETAIL.code);
            }

            // sum sla start
            for (int i = 0; i < nodes.getLength(); i++) {
                Element element = (Element) nodes.item(i);
                String nodeId = element.getAttribute("id");
                NodeList name = element.getElementsByTagName("camunda:property");

                for (SproFlowNode node : flowNodes) {
                    if (node.getId().equalsIgnoreCase(nodeId)) {
                        double slaFinishClone = slaFinish;
                        for (int j = 0; j < name.getLength(); j++) {
                            Element propEle = (Element) name.item(j);
                            String propName = propEle.getAttributes().getNamedItem("name") != null ?
                                    propEle.getAttributes().getNamedItem("name").getNodeValue() : null;
                            String propVal = propEle.getAttributes().getNamedItem("value") != null ?
                                    propEle.getAttributes().getNamedItem("value").getNodeValue() : null;
                            if (propName != null && propName.equals("setting_slaFinish") && propVal != null) {
                                slaFinish = slaFinish + Double.parseDouble(propVal);
                            } else if (propName != null && propName.equals("setting_slaFinish")) {
                                slaFinish = slaFinish + Double.parseDouble(defaultSlaFinish);
                            }
                        }
                        if (!ValidationUtils.isNullOrEmpty(lstDetail)) {
                            LegislativeProgramDetail detail = lstDetail.stream()
                                    .filter(e -> e.getTaskDefKey().equals(nodeId))
                                    .findFirst().orElse(null);
                            if (detail != null) {
                                LocalDate fromDate = LocalDate.parse(detail.getFromDate());
                                LocalDate toDate = LocalDate.parse(detail.getToDate());
                                String processType = detail.getProcessType();
                                long totalDate = processType != null && processType.equals("shortcut")
                                        ? 7 // quy trình rút gọn mặc định 7days, thêm cấu hình hệ thống sau
                                        : ChronoUnit.DAYS.between(fromDate, toDate);
                                slaFinish = slaFinish + totalDate;
                            }
                        }
                        // Không cấu hình setting_slaFinish -> cộng thêm defaultSlaFinish
                        if (slaFinishClone == slaFinish) {
                            slaFinish = slaFinish + Double.parseDouble(defaultSlaFinish);
                        }
                    }
                    // sum sla end
                }
            }

            if (bpmProcInst == null) {
                bpmProcInst = new BpmProcInst();

                // save app_code information
                bpmProcInst.setAppCode(startProcessInstanceDto.getAppCode());

                // HMTC
                bpmProcInst.setApprovedBudgetIds(ObjectUtils.toJson(startProcessInstanceDto.getApprovedBudgetIds()));
            }

            bpmProcInst.setIsAssistant(startProcessInstanceDto.getIsAssistant());
            bpmProcInst.setTicketProcInstId(mapRuExc.get("procInstId").toString());
            bpmProcInst.setTicketProcDefKey(mapRuExc.get("procDefKey").toString());
            bpmProcInst.setTicketProcDefId(mapRuExc.get("procDefId").toString());
            // đã tạo phiếu thì ko update ticketCreatedTime
            bpmProcInst.setTicketCreatedTime(LocalDateTime.now());
            bpmProcInst.setTicketEditTime(LocalDateTime.now());
            bpmProcInst.setTicketStartedTime(null);
            bpmProcInst.setTicketEndTime(null);
            bpmProcInst.setTicketDuration(null);
            bpmProcInst.setCreatedUser(account);
            bpmProcInst.setTicketStartUserId(account);
            bpmProcInst.setTicketStartActId(mapRuExc.get("startActId") != null ? mapRuExc.get("startActId").toString() : null);
            bpmProcInst.setTicketEndActId(endKey);
            bpmProcInst.setTicketDeleteReason(mapRuExc.get("deleteReason") != null ? mapRuExc.get("deleteReason").toString() : null);
            bpmProcInst.setTicketStatus(ProcInstConstants.Status.OPENED.code);
            if (isDraft) {
                bpmProcInst.setTicketStatus(ProcInstConstants.Status.DRAFT.code);
            } else if (bpmProcInst.getFirstCreatedTime() == null){
                bpmProcInst.setFirstCreatedTime(LocalDateTime.now());
            }
            bpmProcInst.setServiceId(serviceId);
            bpmProcInst.setTicketTitle(title);
            bpmProcInst.setSlaFinish(slaFinish);
            bpmProcInst.setSlaResponse(slaResponse);
            bpmProcInst.setTicketCanceledTime(null);
            bpmProcInst.setTicketClosedTime(null);
            bpmProcInst.setTicketRating(0D);
            bpmProcInst.setTicketDescription(null);
            bpmProcInst.setPriority(priority);
            bpmProcInst.setLocationId(locationId);
            bpmProcInst.setPriorityId(priorityId);
            bpmProcInst.setEmailNotification(receiveMail);

            //Thêm chartId
            bpmProcInst.setChartId(chartId);
            bpmProcInst.setCompanyCode(startProcessInstanceDto.getCompanyCode());

            //Thêm tên phòng ban + tên sdtc
            bpmProcInst.setChartNodeName(startProcessInstanceDto.getChartNodeName());
            bpmProcInst.setChartName(startProcessInstanceDto.getChartName());
            bpmProcInst.setChartNodeCode(startProcessInstanceDto.getChartNodeCode());
            bpmProcInst.setChartNodeId(startProcessInstanceDto.getChartNodeId());

            //Thêm mã yêu cầu
            if (!ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getRequestCode())) {
                bpmProcInst.setRequestCode(startProcessInstanceDto.getRequestCode());
            }

            if (!ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getForceViewUrl())) {
                bpmProcInst.setForceViewUrl(startProcessInstanceDto.getForceViewUrl());
            }

            // Tờ trình hủy phiếu đã duyệt
            if (!ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getCancelTicketId())) {
                bpmProcInst.setCancelCompletedTicketId(startProcessInstanceDto.getCancelTicketId());
            }

            //add submisstionType
            bpmProcInst.setSubmissionTypeId(submissionTypeId);
            bpmProcInst.setSubmissionTypeName(submissionTypeName);

            // get priorityId from priority_management_history
            if (!ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getPriorityId())) {
                List<PriorityManagementHistory> lstVersion = priorityHistoryRepository.findByPriorityId(startProcessInstanceDto.getPriorityId());
                if (!ValidationUtils.isNullOrEmpty(lstVersion)) {
                    Long priorityHistoryId = lstVersion.stream()
                            .sorted(Comparator.comparing(PriorityManagementHistory::getVersion).reversed())
                            .map(PriorityManagementHistory::getPriorityHistoryId)
                            .findFirst().orElse(startProcessInstanceDto.getPriorityId());
                    bpmProcInst.setPriorityHistoryId(priorityHistoryId);
                }
            }
            bpmProcInst.setLegislativeId(startProcessInstanceDto.getLegislativeId());

            BpmProcInst newTicket = bpmProcInstRepository.save(bpmProcInst);
            reportProducer.sendKafka(newTicket.getTicketId(), insertReportByGroupTopic);

            createBpmProcinstLink(newTicket.getTicketId(), startProcessInstanceDto.getLinkProcInstId());

            // get and save notify user
            if (notifyUser != null) {
                bpmProcInstNotifyManager.deleteAllByBpmProcinstId(bpmProcInst.getTicketId());

                List<BpmProInstNotifyUser> bpmListProInstNotifyUser = new ArrayList<>();
                for (String user : notifyUser) {
                    BpmProInstNotifyUser bpmProInstNotifyUser = new BpmProInstNotifyUser();
                    bpmProInstNotifyUser.setBpmProcinstId(bpmProcInst.getTicketId());
                    bpmProInstNotifyUser.setRecipient(user);
                    bpmProInstNotifyUser.setCreateUser(account);
                    bpmProInstNotifyUser.setCreatedTime(LocalDateTime.now());
                    bpmListProInstNotifyUser.add(bpmProInstNotifyUser);
                }
                bpmProcInstNotifyManager.saveAll(bpmListProInstNotifyUser);
            }
            if (notifyGroups != null) {
                bpmProInstNotifyGroupManager.deleteAllByBpmProcinstId(bpmProcInst.getTicketId());
                List<BpmProInstNotifyGroup> bpmProInstNotifyGroups = new ArrayList<>();
                for (String group : notifyGroups) {
                    BpmProInstNotifyGroup bpmProInstNotifyGroup = new BpmProInstNotifyGroup();
                    bpmProInstNotifyGroup.setBpmProcinstId(bpmProcInst.getTicketId());
                    bpmProInstNotifyGroup.setGroupId(group);
                    bpmProInstNotifyGroup.setCreateUser(account);
                    bpmProInstNotifyGroup.setCreatedTime(LocalDateTime.now());
                    bpmProInstNotifyGroup.setChartId(chartId);
                    bpmProInstNotifyGroups.add(bpmProInstNotifyGroup);
                }
                bpmProInstNotifyGroupManager.saveAll(bpmProInstNotifyGroups);
            }

            ServicePackage servicePackage = servicePackageRepository.findServicePackageById(bpmProcInst.getServiceId()).get(0);
            if (servicePackage != null) {
                servicePackage.setStatus(ProcDefConstants.Status.ACTIVE.code);
                servicePackageRepository.save(servicePackage);
            }
            mapFinal.put("startKey", mapRuExc.get("startActId") != null ? mapRuExc.get("startActId").toString() : null);
            mapFinal.put("procInstId", procInstId);
            mapFinal.put("ticketId", newTicket.getTicketId());
            mapFinal.put("requestCode", newTicket.getRequestCode());

            // (phucvm3) save bpm-history
            saveCreateTicketHistory(newTicket.getTicketId(), procInstId, account, oldProcInstId, oldDefaultField);

            return mapFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * Save create task history
     *
     * <AUTHOR>
     */
    private void saveCreateTicketHistory(Long ticketId, String procInstId, String account, String oldProcInstId, TicketDefaultResponse oldDefaultField) {
        HistoryDto hisDto = new HistoryDto();
        hisDto.setTicketId(ticketId);
        hisDto.setActionUser(account);
        hisDto.setAction(TaskConstants.HistoryAction.CREATE_TICKET.code);
        hisDto.setFromTask(null);
        hisDto.setTaskInstId(null);
        hisDto.setProcInstId(procInstId);
        hisDto.setOldProcInstId(oldProcInstId);
        if (!ValidationUtils.isNullOrEmpty(oldProcInstId)) {
            // Thực hiện lại lưu receivedTime theo createdTime action GET_REQUEST_UPDATE/ RECALLED
            hisDto.setOldDefaultField(ObjectUtils.writeValueAsString(oldDefaultField));
            BpmHistory recallingHis = bpmHistoryManager.getHistoryByProcInstId(oldProcInstId);
            LocalDateTime receivedTime = null;
            if (!ValidationUtils.isNullOrEmpty(recallingHis)) {
                receivedTime = recallingHis.getCreatedTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            }
            hisDto.setReceivedTime(receivedTime);
        }
        // get action_user_info
        List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(account);
        if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
            Map<String, Object> actionUserInfo = new HashMap<>();
            String userTitle = lstUserTitle.stream()
                    .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                    .map(title -> {
                        String strTitle = StringUtil.nvl(title.getTitle(), "");
                        int concurrently = title.getConcurrently();
                        return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                    })
                    .collect(Collectors.joining(" "));
            actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
            actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
            hisDto.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
        }
        bpmHistoryManager.saveHistory(hisDto);
    }

    public List<LoadTicketDto> getTicketInfo(String procInstId) {
        try {
            List<BpmProcInst> loadTicketInfo = bpmProcInstRepository.getBpmProcInstByTicketProcInstId(procInstId);
            List<LoadTicketDto> loadTicketDtos = loadTicketInfo.stream().map(bpmLoadTicketMapper::entityToDto).collect(Collectors.toList());
            for (LoadTicketDto loadTicketDto : loadTicketDtos) {
                List<BpmTask> listTaskTicket = bpmTaskRepository.getBpmTaskByTaskProcInstId(loadTicketDto.getTicketId());
                loadTicketDto.setTicketTaskDtoList(listTaskTicket.stream().map(bpmTaskMapper::entityToDto).collect(Collectors.toList()));
            }
            return loadTicketDtos;
        } catch (Exception e) {
            return null;
        }
    }

    //todo:
    public boolean completeTicket(
            String procInstId, String account, BpmProcInst bpmProcInst, String
                    taskDefKey, String taskId,
            @NonNull Map<String, Object> camundaTask, @NonNull Map<String, Object> resultData,
            Map<String, VariableValueDto> completeTaskVari
    ) {
        try {
            Map<String, Object> optVariable = new HashMap<>();
            List<SignedFileDto> signedFileDtos = bpmTpSignZoneManager.getSignedFiles(bpmProcInst.getTicketProcInstId(), null);
            if (!ValidationUtils.isNullOrEmpty(signedFileDtos)) {
                SignedFileDto signedFileDto = signedFileDtos.get(0);
                String url = storageUrl + CommonConstants.PATH_SEPARATOR + bucket + CommonConstants.PATH_SEPARATOR + signedFileDto.getSignedFile();
                optVariable.put("fileName", signedFileDto.getFileName());
                optVariable.put("fileUrl", url);
            }
            // link ticket
            List<Long> lstLinkTicketId = bpmProcinstLinkManager.getProcinstLinkByBpmProcinstId(bpmProcInst.getTicketId());
            if (!ValidationUtils.isNullOrEmpty(lstLinkTicketId)) {
                List<BpmProcInst> lstTicketLink = bpmProcInstRepository.findBpmProcInstByticketIdIn(lstLinkTicketId);
                if (!ValidationUtils.isNullOrEmpty(lstTicketLink)) {
                    String requestCode = lstTicketLink.get(0).getRequestCode();
                    optVariable.put("linkProcInstId", requestCode);
                }
            }
            optVariable.put("accountLogin", account);
            optVariable.put("nextTaskDefKey", bpmProcInst.getTicketEndActId());
            optVariable.put("actionCode", TaskActionConstants.Action.COMPLETE.code);
            /* BEGIN handle call action api on beginning */
            Map<String, VariableValueDto> variables = actionApiService.getTicketVariables(procInstId);
            ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(
                    TaskActionConstants.Action.COMPLETE.code,
                    bpmProcInst.getTicketProcDefId(),
                    null,
                    procInstId,
                    account,
                    actionApiService.createVariablesMap(bpmProcInst, variables, camundaTask, completeTaskVari, optVariable));

            // check response of api call BEFORE
            actionApiService.setActionApiResponse(actionApiContext, resultData);

            // check allow post process of api call BEFORE
            if (!actionApiContext.isAllowPostProcess()) {
                finishCompleteTicket(procInstId, actionApiContext, variables, camundaTask, completeTaskVari, optVariable, account);
                return false;
            }
            /* END handle call action api on beginning */

//            saveCompleteTicketHistory(procInstId, account, bpmProcInst, taskDefKey, taskId);

            // get actual end node id
            String endNodeId = null;
            endNodeId = bpmProcInst.getTicketEndActId();
            String actualEndNodeId = getEndNodeId(bpmProcInst.getTicketProcDefId(), bpmProcInst.getTicketProcInstId());
            endNodeId = actualEndNodeId != null ? actualEndNodeId : endNodeId;

            //Thông báo cho user liên quan
            NotificationUser request = new NotificationUser();
            request.setBpmProcdefId(null);
            request.setNextTaskDefKey(endNodeId);
            request.setVariables(new HashMap<>());
            request.setTicketId(bpmProcInst.getTicketId());
            request.setIsGetOldVariable(true);
            request.setLstCustomerEmails(null);
            request.setActionCode(ProcInstConstants.Notifications.FINISH.code);
            request.setEmailExe(account);
            kafkaTemplate.send(notificationUser, request);
//                bpmProcdefNotificationService.addNotificationsByConfig(null, endNodeId, new HashMap<>(),
//                        bpmProcInst.getTicketId(), true, null, ProcInstConstants.Notifications.FINISH.code);

            bpmProcInstRepository.completeTicket(procInstId, LocalDateTime.now(), endNodeId,account);
            BpmProcInst procInst = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(procInstId);
            reportProducer.sendKafka(procInst.getTicketId(), insertReportByGroupTopic);

            /* BEGIN handle call action api at the end */
            log.debug("DEBUG action api AFTER: completeTicket = {}", actionApiContext);
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // Chỉ chạy sau khi transaction commit
                    finishCompleteTicket(procInstId, actionApiContext, variables, camundaTask, completeTaskVari, optVariable, account);
                }
            });
            /* END handle call action api at the end */
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return true;
    }

    private void finishCompleteTicket(
            String procInstId, ActionApiContext
                    actionApiContext, Map<String, VariableValueDto> variables, Map<String, Object> camundaTask,
            Map<String, VariableValueDto> completeTaskVari,
            Map<String, Object> optVariable,
            String actionUser
    ) {
        actionApiService.endHandleActionApiWithActionUser(
                actionApiContext,
                null,
                null,
                actionUser,
                actionApiService.createVariablesMap(
                        findBpmProcInstByTicketProcInstId(procInstId),
                        variables, camundaTask, completeTaskVari,
                        optVariable
                ));
    }

    public void saveCompleteTicketHistory(String procInstId, String account, BpmProcInst bpmProcInst, String
            taskDefKey, String taskId) {
        HistoryDto historyCompleteTicket = new HistoryDto();
        historyCompleteTicket.setActionUser(account);
        historyCompleteTicket.setTicketId(bpmProcInst != null ? bpmProcInst.getTicketId() : null);
        historyCompleteTicket.setFromTask(taskId);
        historyCompleteTicket.setAction(TaskConstants.HistoryAction.COMPLETE_TICKET.code);
        historyCompleteTicket.setTaskDefKey(taskDefKey);
        historyCompleteTicket.setFromTaskKey(taskDefKey);
        historyCompleteTicket.setTaskInstId(taskId);
        historyCompleteTicket.setProcInstId(procInstId);

        // get action_user_info
        List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(account);
        if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
            Map<String, Object> actionUserInfo = new HashMap<>();
            String userTitle = lstUserTitle.stream()
                    .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                    .map(title -> {
                        String strTitle = StringUtil.nvl(title.getTitle(), "");
                        int concurrently = title.getConcurrently();
                        return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                    })
                    .collect(Collectors.joining(" "));
            actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
            actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
            historyCompleteTicket.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
        }
        bpmHistoryManager.saveHistory(historyCompleteTicket);
    }

    public PageDto searchByListAssistantEmail(LoadAssistantTicketDto criteria) {
        try {
            if (ValidationUtils.isNullOrEmpty(criteria.getHandingOverWork())) {
                List<String> lstTicketId;
                if (!criteria.getTicketStatus().equalsIgnoreCase("SHARED")) {
                    lstTicketId = assistantRepo.getListTicketByAssistantEmail(criteria.getAssistantEmail());
                } else {
                    lstTicketId = assistantRepo.getListShareTicket(criteria.getAssistantEmail());
                }
                if (ValidationUtils.isNullOrEmpty(lstTicketId))
                    return null;
                criteria.setListTicket(lstTicketId);
            }
            if (ValidationUtils.isNullOrEmpty(criteria.getListStatus())) {
                return null;
            }
            Map<String, Object> mapResponse = bpmProcInstSpecification.getAssistantTicket(criteria);
            Long totalItems = (Long) mapResponse.get("count");
            Integer totalPage = common.getPageCount(totalItems, criteria.getLimit());
            List<MyTicketAssistantResponse> listRes = (List<MyTicketAssistantResponse>) mapResponse.get("data");

            List<Map<String, Object>> listFinal = new ArrayList<>();
            List<String> procInstId = listRes.stream().map(MyTicketAssistantResponse::getTicketProcInstId).collect(Collectors.toList());
            List<BpmTask> listTask = bpmTaskRepository.getBpmTaskByTaskStatusInAndTaskProcInstIdIn(TaskConstants.TabStatus.PROCESSING, procInstId);
            Map<String, List<BpmTask>> groupByProcId = listTask.stream().collect(Collectors.groupingBy(BpmTask::getTaskProcInstId));

            for (MyTicketAssistantResponse ticket : listRes) {
                List<BpmTask> listTaskById = groupByProcId.get(ticket.getTicketProcInstId());
                Map<String, Object> mapData = new HashMap<>();
                mapData.put("ticketId", ticket.getTicketProcInstId());
                mapData.put("id", ticket.getTicketId());
                mapData.put("endKey", ticket.getTicketEndActId());
                mapData.put("ticketStatus", ticket.getTicketStatus());

                if (criteria.getTicketStatus().equalsIgnoreCase("completed")) {
                    if (ticket.getTicketFinishTime() != null && ticket.getAutoClose() != null) {
                        Date autoClosedTime = DateUtils.addHours(ticket.getTicketFinishTime(), Math.toIntExact(ticket.getAutoClose().longValue()));
                        Date now = new Date();
                        if (now.after(autoClosedTime)) {
                            long diff = Math.abs(now.getTime() - autoClosedTime.getTime());
                            mapData.put("remainingTime", diff * (-1));
                        } else {
                            long diff = Math.abs(autoClosedTime.getTime() - now.getTime());
                            mapData.put("remainingTime", diff);
                        }
                    }
                }
                mapData.put("serviceId", ticket.getServiceId());
                mapData.put("ticketTitle", ticket.getTicketTitle());
                mapData.put("procServiceName", ticket.getServiceName());
                mapData.put("ticketStartUserId", ticket.getTicketStartUserId());
                mapData.put("ticketStartedTime", ticket.getTicketStartedTime());
                mapData.put("ticketCreatedTime", ticket.getTicketCreatedTime());
                mapData.put("ticketFinishTime", ticket.getTicketFinishTime());
                mapData.put("ticketClosedTime", ticket.getTicketClosedTime());
                mapData.put("ticketCanceledTime", ticket.getTicketCanceledTime());
                mapData.put("ticketEditTime", ticket.getTicketEditTime());
                mapData.put("ticketEndTime", ticket.getTicketEndTime());
                mapData.put("slaFinish", ticket.getSlaFinish());
                mapData.put("slaResponse", ticket.getSlaResponse());
                mapData.put("cancelReason", ticket.getCancelReason());
                mapData.put("ticketRating", ticket.getTicketRating());
                mapData.put("requestCode", ticket.getRequestCode());
                mapData.put("ticketStartActId", ticket.getTicketStartActId());
                mapData.put("submissionTypeName", ticket.getTypeName());
                mapData.put("comment", ticket.getComment());
                mapData.put("ticketProcDefId", ticket.getTicketProcDefId());
                mapData.put("companyCode", ticket.getCompanyCode());
                mapData.put("chartId", ticket.getChartId());
                mapData.put("priorityName", ticket.getPriorityName());
                mapData.put("priorityColor", ticket.getPriorityColor());
//                mapData.put("taskCreatedTime",ticket.getTaskCreatedTime());
                if (!ValidationUtils.isNullOrEmpty(ticket.getTicketProcDefId())) {
                    BpmProcdef procdef = bpmProcdefManager.getByProcdefId(ticket.getTicketProcDefId());
                    if (!ValidationUtils.isNullOrEmpty(procdef)) {
                        if (!ValidationUtils.isNullOrEmpty(procdef.getCancelTasks())) {
                            String cancelTask = procdef.getCancelTasks();
                            List<String> jsontoObject = ObjectUtils.toObject(cancelTask, new TypeReference<>() {
                            });
                            mapData.put("procDefCancelTask", jsontoObject);
                        }
                        mapData.put("procDefCancel", procdef.getCancel());
                        mapData.put("procDefAdditionalRequest", procdef.getAdditionalRequest());
                    } else {
                        mapData.put("procDefCancelTask", null);
                        mapData.put("procDefCancel", null);
                        mapData.put("procDefAdditionalRequest", null);
                    }
                }
                mapData.put("ticketPriority", ticket.getTicketPriority());

                if (listTaskById != null && !listTaskById.isEmpty()) {
                    List<TaskDetailResponse> ticketTaskDtoList = listTaskById.stream().map(x -> modelMapper.map(x, TaskDetailResponse.class)).collect(Collectors.toList());

                    mapData.put("ticketTaskDtoList", ticketTaskDtoList);
                } else {
                    mapData.put("ticketTaskDtoList", new ArrayList<>());
                }
                if (!ValidationUtils.isNullOrEmpty(ticket.getActionUser())) {
                    mapData.put("actionUser", ticket.getActionUser());
                }
                mapData.put("chartName", ticket.getChartName());
                mapData.put("chartNodeName", ticket.getChartNodeName());
                listFinal.add(mapData);

            }
            if (criteria.getSortBy().equalsIgnoreCase("remainingTime")) {
                sortByRemainingTime(listFinal, criteria.getSortType());
            }

            return PageDto.builder()
                    .content(listFinal)
                    .number(criteria.getPage())
                    .numberOfElements(listFinal.size())
                    .page(criteria.getPage())
                    .size(criteria.getLimit())
                    .totalPages(totalPage)
                    .totalElements(totalItems)
                    .build();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public List<Map<String, Object>> searchByListAssistantEmailFilter(LoadAssistantTicketDto criteria) {
        try {
            if (ValidationUtils.isNullOrEmpty(criteria.getHandingOverWork())) {
                List<String> lstTicketId;
                if (!criteria.getTicketStatus().equalsIgnoreCase("SHARED")) {
                    lstTicketId = assistantRepo.getListTicketByAssistantEmail(criteria.getAssistantEmail());
                } else {
                    lstTicketId = assistantRepo.getListShareTicket(criteria.getAssistantEmail());
                }
                criteria.setListTicket(lstTicketId);
            }
            if (ValidationUtils.isNullOrEmpty(criteria.getListStatus())) {
                return null;
            }

            return bpmProcInstSpecification.getAssistantTicketFilter(criteria);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public PageDto search(LoadTicketDto criteria, String account) {
        try {
            criteria.setUser(account);
            if (ValidationUtils.isNullOrEmpty(criteria.getListService())) {
                return null;
            }
            if (ValidationUtils.isNullOrEmpty(criteria.getListStatus())) {
                return null;
            }
            if (ValidationUtils.isNullOrEmpty(criteria.getListRating())) {
                return null;
            }
            Map<String, Object> mapResponse = bpmProcInstSpecification.getMyTicket(criteria);
            Long totalItems = (Long) mapResponse.get("count");
            Integer totalPage = common.getPageCount(totalItems, criteria.getLimit());
            List<MyTicketResponse> listRes = (List<MyTicketResponse>) mapResponse.get("data");

            List<Map<String, Object>> listFinal = new ArrayList<>();
            List<String> procInstId = listRes.stream().map(MyTicketResponse::getTicketProcInstId).collect(Collectors.toList());
            List<BpmShared> sharedList = bpmSharedRepository.findAllByProcInstIdIn(procInstId);
            List<BpmTask> listTask = bpmTaskRepository.getBpmTaskByTaskStatusInAndTaskProcInstIdIn(TaskConstants.TabStatus.PROCESSING, procInstId);
            Map<String, List<BpmTask>> groupByProcId = listTask.stream().collect(Collectors.groupingBy(BpmTask::getTaskProcInstId));
            List<BpmTaskUser> lstTaskUser = bpmTaskUserService.getAllTaskUserByTaskIdIn(listTask.stream().map(BpmTask::getTaskId).collect(Collectors.toList()));

            Map<Long, List<AccountModel>> ticketSharedMap = new HashMap<>();
            List<AccountModel> accountModels;
            if (criteria.getTicketStatus().equalsIgnoreCase("shared")) {
                List<BpmShared> bpmSharedList = bpmSharedRepository.findBpmSharedByIdIn(sharedList.stream().map(BpmShared::getId).collect(Collectors.toList()));
                accountModels = customerService.getAccountByUsernames(bpmSharedList.stream().map(BpmShared::getCreatedUser).collect(Collectors.toList()));
                if (!bpmSharedList.isEmpty()) {
                    for (BpmShared bpmShared : bpmSharedList) {
                        List<AccountModel> accountModel = accountModels.stream().filter(i -> i.getUsername() != null
                                && i.getUsername().equals(bpmShared.getCreatedUser())
                                && bpmShared.getSharedUser().equalsIgnoreCase(account)
                        ).collect(Collectors.toList());
                        if (!accountModel.isEmpty()) {
                            ticketSharedMap.put(bpmShared.getId(), accountModel);
                        }
                    }
                }
            }

            List<String> lstProcDefId = listRes.stream().map(MyTicketResponse::getTicketProcDefId).collect(Collectors.toList());
            List<BpmProcdefViewFileApi> bpmProcdefViewFileApis = bpmProcdefViewFileApiRepository.getAllByProcDefIdIn(lstProcDefId);

            for (MyTicketResponse ticket : listRes) {
                List<BpmTask> listTaskById = groupByProcId.get(ticket.getTicketProcInstId());
                Map<String, Object> mapData = new HashMap<>();
                mapData.put("ticketId", ticket.getTicketProcInstId());
                mapData.put("id", ticket.getTicketId());
                mapData.put("endKey", ticket.getTicketEndActId());
                mapData.put("startKey", ticket.getTicketStartActId());
                mapData.put("ticketStatus", ticket.getTicketStatus());
                mapData.put("serviceId", ticket.getServiceId());
                mapData.put("ticketTitle", ticket.getTicketTitle());
                mapData.put("procServiceName", ticket.getServiceName());
                mapData.put("ticketStartUserId", ticket.getTicketStartUserId());
                mapData.put("ticketStartedTime", ticket.getTicketStartedTime());
                mapData.put("ticketCreatedTime", ticket.getTicketCreatedTime());
                mapData.put("ticketFinishTime", ticket.getTicketFinishTime());
                mapData.put("ticketClosedTime", ticket.getTicketClosedTime());
                mapData.put("ticketCanceledTime", ticket.getTicketCanceledTime());
                mapData.put("ticketEditTime", ticket.getTicketEditTime());
                mapData.put("ticketEndTime", ticket.getTicketEndTime());
                mapData.put("slaFinish", ticket.getSlaFinish());
                mapData.put("slaResponse", ticket.getSlaResponse());
                mapData.put("cancelReason", ticket.getCancelReason());
                mapData.put("ticketRating", ticket.getTicketRating());
                mapData.put("submissionTypeName", ticket.getTypeName());
                mapData.put("comment", ticket.getComment());
                mapData.put("ticketProcDefId", ticket.getTicketProcDefId());
                mapData.put("requestCode", ticket.getRequestCode());
                mapData.put("companyCode", ticket.getCompanyCode());
                mapData.put("actionUser", ticket.getActionUser());
                mapData.put("chartId", ticket.getChartId());
                mapData.put("chartName", ticket.getChartName());
                mapData.put("chartNodeName", ticket.getChartNodeName());

                mapData.put("ticketPriority", ticket.getTicketPriority());

                if (listTaskById != null && !listTaskById.isEmpty()) {
                    List<TaskDetailResponse> ticketTaskDtoList = listTaskById.stream().map(x -> modelMapper.map(x, TaskDetailResponse.class)).collect(Collectors.toList());
                    for (TaskDetailResponse taskDetailResponse : ticketTaskDtoList) {
                        if (ValidationUtils.isNullOrEmpty(taskDetailResponse.getTaskAssignee()) && lstTaskUser != null) { // check đồng duyệt
                            List<String> userCandidates = lstTaskUser.stream()
                                    .filter(e -> e.getTaskId().equalsIgnoreCase(taskDetailResponse.getTaskId()))
                                    .map(BpmTaskUser::getUserName).collect(Collectors.toList());
                            if (!ValidationUtils.isNullOrEmpty(userCandidates)) {
                                taskDetailResponse.setTaskUsers(userCandidates);
                            }
                        }
                    }
                    mapData.put("ticketTaskDtoList", ticketTaskDtoList);
                } else {
                    mapData.put("ticketTaskDtoList", new ArrayList<>());
                }

                if (!ValidationUtils.isNullOrEmpty(bpmProcdefViewFileApis)) {
                    mapData.put("viewFileApi", bpmProcdefViewFileApis.stream().filter(e -> e.getProcDefId().equalsIgnoreCase(ticket.getTicketProcDefId())));
                }

                if (!ValidationUtils.isNullOrEmpty(sharedList)) {
                    BpmShared bpmShared = sharedList.stream().filter(i -> i.getProcInstId().equals(ticket.getTicketProcInstId()) && i.getSharedUser().equals(account)).findFirst().orElse(null);
                    if (!ValidationUtils.isNullOrEmpty(bpmShared)) {
                        List<AccountModel> accountModel = ticketSharedMap.get(bpmShared.getId());
                        StringBuilder str = new StringBuilder();
                        if (accountModel != null)
                            for (int i = 0; i < accountModel.size(); i++) {
                                AccountModel a = accountModel.get(0);
                                str.append(a.getUsername()).append(" - ");
                                str.append(a.getLastname()).append(" ");
                                str.append(a.getFirstname()).append(" - ");
                                str.append(a.getFinalTitle());
                                if (i < accountModel.size() - 1) {
                                    str.append(" , ");
                                }
                                mapData.put("sharedUser", str.toString());
                            }
                    } else {
                        mapData.put("sharedUser", "Hệ thống");
                    }
                }
                // id qt
                mapData.put("processId", ticket.getProcessId());

                listFinal.add(mapData);

            }
            if (criteria.getSortBy().equalsIgnoreCase("remainingTime")) {
                sortByRemainingTime(listFinal, criteria.getSortType());
            }

            return PageDto.builder()
                    .content(listFinal)
                    .number(criteria.getPage())
                    .numberOfElements(criteria.getPage())
                    .page(criteria.getPage())
                    .size(criteria.getLimit())
                    .totalPages(totalPage)
                    .totalElements(totalItems)
                    .build();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Object getTicketByServiceAndTicketStatus(GetTicketDto criteria) {
        try {
            Set<Map<String, Object>> listFinal = new HashSet<>();
            if (!ValidationUtils.isNullOrEmpty(criteria.getListTicketId()) || !ValidationUtils.isNullOrEmpty(criteria.getListCompleteTicketId())) {
                List<Object[]> listRes = bpmProcInstSpecification.getTicketByServiceAndTicketStatus(criteria);
                for (Object[] ticket : listRes) {
                    Map<String, Object> mapData = new HashMap<>();
                    mapData.put("ticketId", ticket[0]);
                    mapData.put("ticketTitle", ticket[4]);
                    mapData.put("url", port.substring(0, port.lastIndexOf('/')) + CONTEXT_PATH_VIEW_TICKET + "/" + ticket[3] + "/" + ticket[0] + "/" + ticket[5]);
                    mapData.put("ticketStatus", ticket[6]);
                    mapData.put("requestCode", ticket[7]);
                    listFinal.add(mapData);
                }
            }

            return listFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }

    }

    public List<Map<String, Object>> searchFilter(LoadTicketDto criteria, String account) {
        try {
            criteria.setUser(account);
            return bpmProcInstSpecification.getMyTicketFilter(criteria);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Map<String, Object> getFilterTicket(String email) {
        try {
            Map<String, Object> mapFinal = new HashMap<>();
            List<String> listService = servicePackageManager.getAllservicePackagesActive();
            List<LocalDateTime> listDate = bpmProcInstRepository.distinctTime(email);
            List<String> listSubmissionType = submissionTypeManager.getAllSubmissionTypeName();
            Set<String> listDateConvert = listDate.stream().map(x -> {
                Date dateConvert = Date.from(x.atZone(ZoneId.systemDefault()).toInstant());
                SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
                return formatter.format(dateConvert);
            }).collect(Collectors.toSet());
            mapFinal.put("listServices", listService);
            mapFinal.put("listDates", listDateConvert);
            mapFinal.put("listSubmissionType", listSubmissionType);
            return mapFinal;
        } catch (Exception e) {
            return null;
        }
    }

    public Map<String, Object> loadFilter(LoadFilterTicketRequest request) {
        try {
            Map<String, Object> mapFinal = new HashMap<>();
            if (request.getService()) {
                List<Map<String, Object>> listService = servicePackageManager.loadFilterService();
                mapFinal.put("services", listService);
            }
            if (request.getChart()) {
                List<Map<String, Object>> listChart = bpmService.loadChartFilter();
                mapFinal.put("charts", listChart);
            }
            if (request.getChartNode()) {
                List<Map<String, Object>> listChartNode = bpmService.loadChartNodeFilter();
                mapFinal.put("chartNode", listChartNode);
            }
            return mapFinal;
        } catch (Exception e) {
            return null;
        }
    }

    public TicketDetailResponse searchByTicketId(Long ticketId) {
        try {
            BpmProcInst procInst = findById(ticketId);
            List<BpmTask> bpmTasks = bpmTaskRepository.getBpmTaskByTaskProcInstId(procInst.getTicketProcInstId());
            String serviceName = bpmProcInstRepository.serviceNameByProcinst(procInst.getTicketProcInstId());
            Boolean checkEditPermission = bpmTaskRepository.existsByTaskProcInstIdAndTaskIsFirstAndTaskStartedTimeIsNotNull(procInst.getTicketProcInstId(), true);

            // get config procDef by serviceId (ko có lịch sử qt)
            BpmProcdef bpmProcdef = bpmProcdefRepository.findByServiceId(procInst.getServiceId());
            // get config procDefHistory by procDefId (có lịch sử qt)
            BpmProcDefHistory bpmProcDefHistory = bpmProcDefHistoryRepository.findByProcDefId(procInst.getTicketProcDefId());
            // bpmProcDefHistory null -> lấy version 0 (vẫn null -> ko có lịch sử)
            if (bpmProcDefHistory == null) {
                bpmProcDefHistory = bpmProcDefHistoryRepository.findVer0ByProcessIdAndTicketCreatedTime(bpmProcdef.getId(), procInst.getTicketCreatedTime());
            }

            List<BpmOwnerProcess> listOwnerEnt = new ArrayList<>();

            //anhvtn add get submission
            SubmissionType submissionType = submissionTypeManager.getById(procInst.getSubmissionTypeId());

            TicketDetailResponse result = modelMapper.map(procInst, TicketDetailResponse.class);

            // có lịch sử quy trình
            if (bpmProcDefHistory != null) {
                Gson g = new GsonBuilder()
                        .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                        .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                        .create();
                listOwnerEnt = g.fromJson(bpmProcDefHistory.getProcessOwner(), new TypeReference<List<BpmOwnerProcess>>() {
                }.getType());

                List<String> cancelTasks = ObjectUtils.toObject(bpmProcDefHistory.getCancelTasks(), new TypeReference<>() {
                });
                List<String> hideInfoTasks = ObjectUtils.toObject(bpmProcDefHistory.getHideInfoTasks(), new TypeReference<>() {
                });
                List<String> showInfoTasks = ObjectUtils.toObject(bpmProcDefHistory.getShowInfoTaks(), new TypeReference<>() {
                });
                List<String> changeImplementerValue = ObjectUtils.toObject(bpmProcDefHistory.getChangeImplementerValue(), new TypeReference<>() {
                });
                List<String> authorityOnTicketValue = ObjectUtils.toObject(bpmProcDefHistory.getAuthorityOnTicketValue(), new TypeReference<>() {
                });
                List<String> authorityOnTicketStep = ObjectUtils.toObject(bpmProcDefHistory.getAuthorityOnTicketStep(), new TypeReference<>() {
                });
                List<String> showInputTaskDefKeys = ObjectUtils.toObject(bpmProcDefHistory.getShowInputTaskDefKeys(), new TypeReference<>() {
                });
                List<String> hideRuTasks = ObjectUtils.toObject(bpmProcDefHistory.getHideRuTasks(), new TypeReference<>() {
                });
                List<String> hideInheritTask = ObjectUtils.toObject(bpmProcDefHistory.getHideInheritTasks(), new TypeReference<>() {
                });
                List<String> hideCommentTask = ObjectUtils.toObject(bpmProcDefHistory.getHideCommentTasks(), new TypeReference<>() {
                });
                List<String> hideDownloadTasks = ObjectUtils.toObject(bpmProcDefHistory.getHideDownloadTasks(), new TypeReference<>() {
                });
                result.setHideResult(bpmProcDefHistory.getStepByStepResultForCreate());
                result.setInformTo(bpmProcDefHistory.getInFormTo());
                result.setUpdatePermission(bpmProcDefHistory.getUpdate());
                result.setRuPermission(bpmProcDefHistory.getRequestUpdate());
                result.setNewAndClone(bpmProcDefHistory.getCreateNewAndDouble());
                result.setAutoInherits(bpmProcDefHistory.getAutoInherits());
                result.setOffNotifications(bpmProcDefHistory.getOffNotification());
                result.setCancelTasks(cancelTasks);
                result.setProcDefId(bpmProcDefHistory.getOrgProcessId());
                result.setHideInfoTasks(hideInfoTasks);
                result.setShowInfoTasks(showInfoTasks);
                result.setHideInfo(bpmProcDefHistory.getHideInfo());
                result.setShowInfo(bpmProcDefHistory.getShowInfo());
                result.setIsAssistant(bpmProcDefHistory.getIsAssistant());
                result.setIsEditAssistant(bpmProcDefHistory.getIsEditAssistant());
                result.setHideRuTasks(hideRuTasks);
                result.setHideInherit(bpmProcDefHistory.getHideInherit());
                result.setHideInheritTasks(hideInheritTask);
                result.setHideComment(bpmProcDefHistory.getHideComment());
                result.setHideCommentTasks(hideCommentTask);
                result.setHideDownload(bpmProcDefHistory.getHideDownload());
                result.setHideDownloadTasks(hideDownloadTasks);
                result.setHideShareTicket(bpmProcDefHistory.getHideShareTicket());
                result.setCancel(bpmProcDefHistory.getCancel());
                result.setAdditionalRequest(bpmProcDefHistory.getAdditionalRequest());
                result.setRecall(bpmProcDefHistory.getRecall());
                result.setChangeImplementerValue(changeImplementerValue);
                result.setAuthorityOnTicketValue(authorityOnTicketValue);
                result.setAuthorityOnTicketStep(authorityOnTicketStep);
                result.setAuthorityOnTicket(bpmProcDefHistory.getAuthorityOnTicket());
                result.setShowInputTask(bpmProcDefHistory.getShowInputTask());
                result.setShowInputTaskDefKeys(showInputTaskDefKeys);
                //Lấy cấu hình kế thừa tự động
                result.setBpmProcdefInherits(bpmProcdefInheritsRepository.getBpmProcdefInheritsInfoHistory(bpmProcDefHistory.getProcDefId()));
            }
            // ko có lịch sử quy trình
            else {
                listOwnerEnt = bpmOwnerProcessManager.getAllByBpmProcInstId(ticketId);

                List<String> cancelTasks = ObjectUtils.toObject(bpmProcdef.getCancelTasks(), new TypeReference<>() {
                });
                List<String> hideInfoTasks = ObjectUtils.toObject(bpmProcdef.getHideInfoTasks(), new TypeReference<>() {
                });
                List<String> showInfoTasks = ObjectUtils.toObject(bpmProcdef.getShowInfoTaks(), new TypeReference<>() {
                });
                List<String> changeImplementerValue = ObjectUtils.toObject(bpmProcdef.getChangeImplementerValue(), new TypeReference<>() {
                });

                List<String> authorityOnTicketValue = ObjectUtils.toObject(bpmProcdef.getAuthorityOnTicketValue(), new TypeReference<>() {
                });
                List<String> authorityOnTicketStep = ObjectUtils.toObject(bpmProcdef.getAuthorityOnTicketStep(), new TypeReference<>() {
                });
                List<String> showInputTaskDefKeys = ObjectUtils.toObject(bpmProcdef.getShowInputTaskDefKeys(), new TypeReference<>() {
                });
                List<String> hideRuTasks = ObjectUtils.toObject(bpmProcdef.getHideRuTasks(), new TypeReference<>() {
                });
                List<String> hideInheritTask = ObjectUtils.toObject(bpmProcdef.getHideInheritTasks(), new TypeReference<>() {
                });
                List<String> hideCommentTask = ObjectUtils.toObject(bpmProcdef.getHideCommentTasks(), new TypeReference<>() {
                });
                List<String> hideDownloadTasks = ObjectUtils.toObject(bpmProcdef.getHideDownloadTasks(), new TypeReference<>() {
                });
                result.setHideResult(bpmProcdef.getStepByStepResultForCreate());
                result.setInformTo(bpmProcdef.getInFormTo());
                result.setUpdatePermission(bpmProcdef.getUpdate());
                result.setRuPermission(bpmProcdef.getRequestUpdate());
                result.setNewAndClone(bpmProcdef.getCreateNewAndDouble());
                result.setAutoInherits(bpmProcdef.getAutoInherits());
                result.setOffNotifications(bpmProcdef.getOffNotification());
                result.setCancelTasks(cancelTasks);
                result.setProcDefId(bpmProcdef.getId());
                result.setHideInfoTasks(hideInfoTasks);
                result.setShowInfoTasks(showInfoTasks);
                result.setHideInfo(bpmProcdef.getHideInfo());
                result.setHideInfo(bpmProcdef.getShowInfo());
                result.setIsAssistant(bpmProcdef.getIsAssistant());
                result.setIsEditAssistant(bpmProcdef.getIsEditAssistant());
                result.setHideRuTasks(hideRuTasks);
                result.setHideInherit(bpmProcdef.getHideInherit());
                result.setHideInheritTasks(hideInheritTask);
                result.setHideComment(bpmProcdef.getHideComment());
                result.setHideCommentTasks(hideCommentTask);

                // Ẩn btn Tải file tờ trình
                result.setHideDownload(bpmProcdef.getHideDownload());
                result.setHideDownloadTasks(hideDownloadTasks);

                // Ẩn nút Chia sẻ
                result.setHideShareTicket(bpmProcdef.getHideShareTicket());

                result.setCancel(bpmProcdef.getCancel());
                result.setAdditionalRequest(bpmProcdef.getAdditionalRequest());
                result.setRecall(bpmProcdef.getRecall());
                result.setChangeImplementerValue(changeImplementerValue);
                result.setAuthorityOnTicketValue(authorityOnTicketValue);
                result.setAuthorityOnTicketStep(authorityOnTicketStep);
                result.setAuthorityOnTicket(bpmProcdef.getAuthorityOnTicket());
                result.setShowInputTask(bpmProcdef.getShowInputTask());
                result.setShowInputTaskDefKeys(showInputTaskDefKeys);
                //Lấy cấu hình kế thừa tự động -> lấy theo procDefId version mới nhất
                result.setBpmProcdefInherits(bpmProcdefInheritsRepository.getBpmProcdefInheritsInfoHistory(bpmProcdef.getProcDefId()));
            }
            if (submissionType != null) {
                result.setSubmissionTypeName(submissionType.getTypeName());
            }

            // get priorityId from priority_management_history
            if (procInst.getPriorityHistoryId() != null) {
                Optional<PriorityManagementHistory> priorityManagementHistoryOptional = priorityHistoryRepository.findById(procInst.getPriorityHistoryId());
                if (priorityManagementHistoryOptional.isPresent()) {
                    PriorityManagementHistory priorityManagementHistory = priorityManagementHistoryOptional.get();
                    result.setPriorityId(priorityManagementHistory.getPriorityId());
                    result.setPriority(priorityManagementHistory.getName());
                    result.setColor(priorityManagementHistory.getColor());
                }
            } else if (procInst.getPriorityId() != null) {
                Optional<PriorityManagement> priorityManagementOpt = priorityManager.getById(procInst.getPriorityId());
                if (priorityManagementOpt.isPresent()) {
                    PriorityManagement priorityManagement = priorityManagementOpt.get();
                    result.setPriorityId(priorityManagement.getId());
                    result.setPriority(priorityManagement.getName());
                    result.setColor(priorityManagement.getColor());
                }
            }

            //-------------------------------SLA----------------------------------//
            Double slaResponse = bpmTasks.stream().map(BpmTask::getSlaResponse).reduce(Double.MIN_VALUE, Double::sum);
//            Double slaFinish = bpmTasks.stream().map(BpmTask::getSlaFinish).reduce(Double.MIN_VALUE, Double::sum);
            // Tính slaFinish theo task - handle task song song
            Map<String, Double> maxValuesByTaskDefKey = bpmTasks.stream().collect(
                    Collectors.toMap(
                            BpmTask::getTaskDefKey,
                            BpmTask::getSlaFinish,
                            Double::max
                    ));
            double slaFinish = maxValuesByTaskDefKey.values().stream().mapToDouble(Double::doubleValue).sum();
            // Tính slaFinishTime theo task - Lấy slaFinishTime xa nhất của task
            LocalDateTime slaFinishTime = bpmTasks.stream().map(BpmTask::getSlaFinishTime)
                    .max(Comparator.naturalOrder())
                    .orElse(null);

            long slaResMin = Math.round(slaResponse * 60);
            long slaFinMin = Math.round(slaFinish * 60);

            //SLA res ticket process -- AnhVTN11 edit
            long slaResMinProcess = Math.round(procInst.getSlaResponse() * 60);
            long slaFinMinProcess = Math.round(procInst.getSlaFinish() * 60);
            //----------------------------------------------------//

            //SLA res ticket process -- AnhVTN11 edit
            result.setSlaFinishProcess(procInst.getSlaFinish());
            result.setSlaResponseProcess(procInst.getSlaResponse());
            //----------------------------------------------------//
            result.setSlaResponse(slaResponse);
            result.setSlaFinish(slaFinish);
            result.setSlaResponseTime(Date.from(procInst.getTicketCreatedTime().plusMinutes(slaResMin).atZone(ZoneId.systemDefault()).toInstant()));
            if (!ValidationUtils.isNullOrEmpty(slaFinishTime)) {
                result.setSlaFinishTIme(Date.from(slaFinishTime.atZone(ZoneId.systemDefault()).toInstant()));
            } else {
                result.setSlaFinishTIme(Date.from(procInst.getTicketCreatedTime().plusMinutes(slaFinMin).atZone(ZoneId.systemDefault()).toInstant()));
            }

            //SLA res ticket process -- AnhVTN11 edit
            result.setSlaResponseTimeProcess(Date.from(procInst.getTicketCreatedTime().plusMinutes(slaResMinProcess).atZone(ZoneId.systemDefault()).toInstant()));

            // Add workingTime default
//            LocalDateTime slaFinDate = customerService.getTimeSla(slaFinMinProcess, procInst.getTicketCreatedTime(), procInst.getTicketStartUserId());
            // btp sla
            result.setLegislativeId(procInst.getLegislativeId());
            long timeRes = procInst.getSlaFinish().longValue() + 1;
            LocalDateTime slaFinDate = procInst.getTicketCreatedTime().plusDays(timeRes).toLocalDate().atStartOfDay();
            result.setSlaFinishTimeProcess(Date.from(slaFinDate.atZone(ZoneId.systemDefault()).toInstant()));

            result.setEndKey(procInst.getTicketEndActId());
            result.setStartKey(procInst.getTicketStartActId());
            result.setTicketId(procInst.getTicketProcInstId());
            result.setId(procInst.getTicketId());
            result.setRequestCode(procInst.getRequestCode());
            //-------------------------------------------------//
            //Add chartId
            result.setChartId(procInst.getChartId());
            result.setEditPermission(!checkEditPermission);

            //HMTC
            result.setApprovedBudgetIds(procInst.getApprovedBudgetIds());

            List<BpmTask> taskInst = bpmTasks.stream().filter(bpmTask -> TaskConstants.TabStatus.PROCESSING.contains(bpmTask.getTaskStatus())).toList();
            List<TaskDetailResponse> listTask = taskInst.stream().map(task -> modelMapper.map(task, TaskDetailResponse.class)).collect(Collectors.toList());
            for (TaskDetailResponse taskDetailResponse : listTask) {
                taskDetailResponse.setTicketId(procInst.getTicketId());
                taskDetailResponse.setProcInstId(procInst.getTicketProcInstId());
                taskDetailResponse.setTicketProcDefId(procInst.getTicketProcDefId());
            }
            result.setFullName(responseUtils.getFullname(procInst.getTicketStartUserId()));

            //------------------LIST SIGN FORM-----------------------------//
            List<Tuple> listPrintPhase = bpmTemplatePrintRepository.getExecutionPrintPhaseNew(procInst.getTicketProcDefId(), Collections.singletonList(procInst.getTicketStartActId()));
            String finalTaskDefKey = procInst.getTicketStartActId();
            List<Map<String, Object>> listPrintPhaseRes = listPrintPhase.stream().map(x -> {
                Map<String, Object> mapPrint = new HashMap<>();
                mapPrint.put("id", x.get("id"));
                mapPrint.put("printType", x.get("printType"));
                mapPrint.put("pdfContent", x.get("pdfContent"));
                mapPrint.put("content", x.get("content"));
                mapPrint.put("taskDefKey", finalTaskDefKey);
                mapPrint.put("templateName", x.get("name"));
                mapPrint.put("status", x.get("status"));
                mapPrint.put("htmlId", x.get("htmlId"));
                return mapPrint;
            }).collect(Collectors.toList());

            result.setListSignForm(listPrintPhaseRes);
            result.setTicketTaskDtoList(listTask);
            result.setProcServiceName(serviceName);
            result.setListOwner(listOwnerEnt.stream().map(BpmOwnerProcess::getIdUser).collect(Collectors.toList()));

            // get history
            HistoryDto criteria = new HistoryDto();
            criteria.setTicketId(procInst.getTicketId());
            criteria.setAction(TaskConstants.HistoryAction.REQUEST_UPDATE.code);
            Sort sortHistory = responseUtils.getSort("createdTime", "DESC");
            List<BpmHistory> listHistory = bpmHistoryRepository.findAll(bpmHistorySpec.filter(criteria), sortHistory);

            List<Map<String, Object>> listRuData = new ArrayList<>();
            if (!listHistory.isEmpty()) {
                for (BpmHistory histo : listHistory) {
                    Map<String, Object> mapRu = new HashMap<>();
                    mapRu.put("currTaskKey", histo.getFromTaskKey());
                    mapRu.put("ruTaskKey", histo.getToTaskKey());
                    mapRu.put("affectedTask", ObjectUtils.toObject(histo.getAffectedTask(), List.class));
                    mapRu.put("actionUser", histo.getActionUser());
                    mapRu.put("ruReason", histo.getNote());
                    mapRu.put("time", histo.getCreatedTime());
                    mapRu.put("fullName", responseUtils.getFullname(histo.getActionUser()));
                    if (!ValidationUtils.isNullOrEmpty(histo.getAttachFiles())) {
                        mapRu.put("attachFiles", Arrays.asList(histo.getAttachFiles().split("\\s*,\\s*")));
                    }
                    mapRu.put("ruProcInstId", histo.getProcInstId());
                    listRuData.add(mapRu);
                }
            }
            result.setRuData(listRuData);

            // ticket additional request
            if (procInst.getTicketStatus().equals(ProcInstConstants.Status.ADDITIONAL_REQUEST.code)) {
                BpmAdditionalRequest bpmAdditionalRequest = bpmAdditionalRequestRepository.findTopByProcInstIdOrderByIdDesc(procInst.getTicketProcInstId());
                if (bpmAdditionalRequest != null) {
                    result.setContentRequest(bpmAdditionalRequest.getContentRequest());
                }
            }

            result.setAppCode(procInst.getAppCode());

            // (phucvm3) add bpm_task_user
            result.setTaskUsers(bpmTaskUserService.getTaskDefKeyToUserMap(procInst.getTicketProcInstId()));

            // (phucvm3) add signed files
            result.setSignedFiles(bpmTpSignZoneManager.getSignedFiles(procInst.getTicketProcInstId(), null));

            // (phucvm3) get end node id
            result.setEndNodeId(getEndNodeId(procInst.getTicketProcDefId(), procInst.getTicketProcInstId()));

            // (phucvm3) get linked proc-inst info; sửa cho phép liên kiết nhều phiếu
            List<Long> procInstLinks = bpmProcinstLinkManager.getProcinstLinkByBpmProcinstId(procInst.getTicketId());
            if (procInstLinks != null && !procInstLinks.isEmpty()) {
                List<LinkedBpmProcInstDto> linkedBpmProcInstDtos = new ArrayList<>();
                for (Long item : procInstLinks) {
                    BpmProcInst linkedBpmProcInst = findById(item);
                    if (linkedBpmProcInst != null) {
                        linkedBpmProcInstDtos.add(new LinkedBpmProcInstDto(linkedBpmProcInst.getTicketProcDefId(),
                                linkedBpmProcInst.getTicketId(), linkedBpmProcInst.getTicketTitle(), linkedBpmProcInst.getTicketStartActId(), linkedBpmProcInst.getRequestCode()));
                    }
                }
                result.setLinkedBpmProcInstDto(linkedBpmProcInstDtos);
            }

            // get ticket recall information
            result.setTicketRecall(getTicketRecall(procInst));

            // set company code
            result.setCompanyCode(procInst.getCompanyCode());

            // Set chart node id
            result.setChartNodeId(procInst.getChartNodeId());

            result.setViewFileApi(bpmProcdefViewFileApiRepository.getAllByProcDefId(procInst.getTicketProcDefId()));

            result.setForceViewUrl(procInst.getForceViewUrl());

            List<BpmProInstNotifyUser> lstNotifyUser = bpmProcInstNotifyManager.getDetail(procInst.getTicketId());
            List<String> notifyUsers = new ArrayList<>();
            if (!ValidationUtils.isNullOrEmpty(lstNotifyUser)) {
                notifyUsers = lstNotifyUser.stream().map(BpmProInstNotifyUser::getRecipient).collect(Collectors.toList());
            }
            result.setNotifyUsers(notifyUsers);

            // get cancel draft data
            List<Map<String, Object>> cancelDraft = new ArrayList<>();
            List<BpmVariables> cancelDraftVari = bpmVariablesRepository.getListVariablesDraftByProcInstId(procInst.getTicketProcInstId());
            if (!ValidationUtils.isNullOrEmpty(cancelDraftVari)) {
                for (BpmVariables varDraft : cancelDraftVari) {
                    Map<String, Object> variableDraft = new HashMap<>();
                    variableDraft.put("name", varDraft.getName());
                    variableDraft.put("type", varDraft.getType());
                    variableDraft.put("isDraft", varDraft.getIsDraft());
                    variableDraft.put("additionalVal", varDraft.getAdditionalVal());
                    switch (varDraft.getType().toLowerCase()) {
                        case "string":
                            variableDraft.put("value", varDraft.getStringVal());
                            break;
                        case "double":
                            variableDraft.put("value", varDraft.getDoubleVal());
                            break;
                        case "long":
                        case "integer":
                            variableDraft.put("value", varDraft.getLongVal());
                            break;
                        case "json":
                            variableDraft.put("value", varDraft.getJsonVal());
                            break;
                        case "file":
                            variableDraft.put("value", varDraft.getDownloadUrl());
                            break;
                    }
                    cancelDraft.add(variableDraft);
                }
                result.setCancelDraftVariables(cancelDraft);
            }
            List<String> lstOrgAssignee = changeAssigneeHistoryRepository.getAllOrgAssigneeByTicketId(ticketId);
            if (!ValidationUtils.isNullOrEmpty(lstOrgAssignee)) {
                result.setLstOrgAssignee(lstOrgAssignee);
            }

            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    private TicketDetailResponse getPrintInfo(Long ticketId) {
        BpmProcInst procInst = findById(ticketId);
        String serviceName = bpmProcInstRepository.serviceNameByProcinst(procInst.getTicketProcInstId());

        TicketDetailResponse result = modelMapper.map(procInst, TicketDetailResponse.class);
        result.setProcServiceName(serviceName);

        return result;
    }

    private TicketRecall getTicketRecall(@NonNull BpmProcInst bpmProcInst) {
        BpmProcinstRecall bpmProcinstRecall = bpmProcinstRecallService.findByProcInstId(bpmProcInst.getTicketProcInstId());
        if (bpmProcinstRecall != null) {
            TicketRecall ticketRecall = new TicketRecall();
            ticketRecall.setRecallTime(DateTimeUtils.localDateTimeToDate(bpmProcinstRecall.getRecallTime()));
            ticketRecall.setRecallUser(bpmProcinstRecall.getRecallUser());
            ticketRecall.setReason(bpmProcinstRecall.getReason());
            if (!ValidationUtils.isNullOrEmpty(bpmProcinstRecall.getAttachFile())) {
                ticketRecall.setAttachFiles(Arrays.asList(bpmProcinstRecall.getAttachFile().split("\\s*,\\s*")));
            }
            if (!ValidationUtils.isNullOrEmpty(bpmProcinstRecall.getAttachFileName())) {
                ticketRecall.setAttachFilesName(Arrays.asList(bpmProcinstRecall.getAttachFileName().split("\\s*,\\s*")));
            }
            if (!ValidationUtils.isNullOrEmpty(bpmProcinstRecall.getAttachFileName())) {
                ticketRecall.setAttachFilesSize(Arrays.asList(bpmProcinstRecall.getAttachFileSize().split("\\s*,\\s*")));
            }

            return ticketRecall;
        }

        return null;
    }

    /**
     * Get end node ID
     *
     * <AUTHOR>
     */
    @Nullable
    public String getEndNodeId(String procDefId, String procInstId) {
        try {
            WorkFlowRequest wfRequest = new WorkFlowRequest();
            wfRequest.setProcDefId(procDefId);
            wfRequest.setProcInstId(procInstId);

            // get spro flows
            List<SproFlow> sproFlows = camundaEngineService.getSproFlows(wfRequest);
            if (!ValidationUtils.isNullOrEmpty(sproFlows)) {
                // get last item
                SproFlow lastSproFlow = sproFlows.get(sproFlows.size() - 1);
                if (!ValidationUtils.isNullOrEmpty(lastSproFlow.getNodes()) && lastSproFlow.getNodes().size() == 1) {
                    SproFlowNode node = lastSproFlow.getNodes().get(0);
                    if (node != null && !ValidationUtils.isNullOrEmpty(node.getType()) && node.getType().equalsIgnoreCase(AppConstants.BpmnNodeType.END_EVENT)) {
                        return node.getId();
                    }
                }
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return null;
    }

    public Map<String, Long> countTicket(String account, Boolean ownerProcess, String search) {
        try {
            Long countOngoing = bpmProcInstSpecification.countOngoing(account, ownerProcess, search);
            Long countCompleted = bpmProcInstSpecification.countCompleted(account, ownerProcess, search);
            Long countDraft = bpmProcInstSpecification.countDraft(account, ownerProcess, search);
            Long countCancel = bpmProcInstSpecification.countCancel(account, ownerProcess, search);
            Long countShared = bpmProcInstSpecification.countShared(account, ownerProcess, search);
            Long countShare = bpmProcInstSpecification.countShare(account, search);
            Long countRecalled = bpmProcInstSpecification.countRecalled(account, ownerProcess, search);

            Map<String, Long> mapFinal = new HashMap<>();

            mapFinal.put("ONGOING", countOngoing);
            mapFinal.put("COMPLETED", countCompleted);
            mapFinal.put("DRAFT", countDraft);
            mapFinal.put("CANCEL", countCancel);
            mapFinal.put("SHARED", countShared);
            mapFinal.put("SHARE", countShare);
            mapFinal.put("RECALLED", countRecalled);
            return mapFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }


    public BpmProcInst closeTicket(TicketRequest request, String taskDefKey) {
        try {
            BpmProcInst bpmProcInst = findById(Long.parseLong(request.getProcInstId()));
            if (bpmProcInst == null) {
                return null;
            }
            // check ticket status
            if (!bpmProcInst.getTicketStatus().equalsIgnoreCase(ProcInstConstants.Status.COMPLETED.code)) {
                throw new RuntimeException(common.getMessage("message.task.close.incompleted.not-allow"));
            }
            /* BEGIN handle call action api on beginning */
            Map<String, VariableValueDto> variables = actionApiService.getTicketVariables(bpmProcInst.getTicketProcInstId());
            ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(TaskActionConstants.Action.RATING.code,
                    bpmProcInst.getTicketProcDefId(),
                    taskDefKey,
                    bpmProcInst.getTicketProcInstId(),
                    actionApiService.createVariablesMap(bpmProcInst, request, variables));
            /* END handle call action api on beginning */

            String userName = request.getActionUser();
            bpmProcInst.setTicketClosedTime(LocalDateTime.now());
            bpmProcInst.setTicketStatus(ProcInstConstants.Status.CLOSED.code);
            bpmProcInst.setTicketRating(request.getRating());
            bpmProcInst.setComment(request.getComment());

            bpmProcInstRepository.save(bpmProcInst);
            // call report by group
            reportProducer.sendKafka(bpmProcInst.getTicketId(), insertReportByGroupTopic);

            //-----------------Save History------------------//
            HistoryDto hisDto = new HistoryDto();
            hisDto.setActionUser(userName);
            hisDto.setAction(TaskConstants.HistoryAction.RATING.code);
            hisDto.setNote(request.getComment());
            hisDto.setTicketId(bpmProcInst.getTicketId());
            hisDto.setProcInstId(bpmProcInst.getTicketProcInstId());
            hisDto.setReceivedTime(bpmProcInst.getTicketFinishTime());

            // get action_user_info
            List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(userName);
            if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
                Map<String, Object> actionUserInfo = new HashMap<>();
                String userTitle = lstUserTitle.stream()
                        .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                        .map(title -> {
                            String strTitle = StringUtil.nvl(title.getTitle(), "");
                            int concurrently = title.getConcurrently();
                            return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                        })
                        .collect(Collectors.joining(" "));
                actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
                actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
                hisDto.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
            }
            bpmHistoryManager.saveHistory(hisDto);

            bpmProcInst = findById(Long.parseLong(request.getProcInstId()));
            if (bpmProcInst != null) {
                //Thông báo cho user liên quan
                String actionCode = request.getIsAutoRequest() ? ProcInstConstants.Notifications.AUTO_CLOSED.code : ProcInstConstants.Notifications.CLOSED.code;
                NotificationUser payload = new NotificationUser();
                payload.setBpmProcdefId(null);
                payload.setNextTaskDefKey(ProcInstConstants.Notifications.CLOSED.code);
                payload.setVariables(new HashMap<>());
                payload.setTicketId(bpmProcInst.getTicketId());
                payload.setIsGetOldVariable(true);
                payload.setLstCustomerEmails(Collections.singletonList(bpmProcInst.getCreatedUser()));
                payload.setActionCode(actionCode);
                payload.setEmailExe(credentialHelper.getJWTPayload().getUsername());
                kafkaTemplate.send(notificationUser, payload);
//                bpmProcdefNotificationService.addNotificationsByConfig(null, ProcInstConstants.Notifications.CLOSED.code, new HashMap<>(),
//                        bpmProcInst.getTicketId(), true, Arrays.asList(bpmProcInst.getCreatedUser()), actionCode);

            }

            /* BEGIN handle call action api at the end */
            actionApiService.endHandleActionApi(actionApiContext, actionApiService.createVariablesMap(bpmProcInst, request, variables));
            /* END handle call action api at the end */

            return bpmProcInst;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Map<String, Object> cancelTicket(Long ticketId, String id, String account, String reason, String
            taskDefKey, List<String> attachFiles, List<String> attachFilesName, List<String> attachFilesSize) {
        return cancelTicket(ticketId, id, account, reason, taskDefKey, false, attachFiles, attachFilesName, attachFilesSize);
    }

    public Map<String, Object> cancelTicket(Long ticketId, String id, String account, String reason, String taskDefKey,
                                            boolean isAutoRequest, List<String> attachFiles, List<String> attachFilesName,
                                            List<String> attachFilesSize) {
        try {
            BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(id);

            /* BEGIN handle call action api on beginning */
            // Add user login
            Map<String, Object> optVariable = new HashMap<>();
            optVariable.put("accountLogin", credentialHelper.getJWTPayload().getUsername());

            ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(
                    TaskActionConstants.Action.CANCEL_TICKET.code,
                    bpmProcInst.getTicketProcDefId(),
                    taskDefKey,
                    id,
                    actionApiService.createVariablesMap(bpmProcInst, actionApiService.getTicketVariables(id), optVariable));
            /* END handle call action api on beginning */

            // check response of api call BEFORE
            Map<String, Object> resultData = new HashMap<>();
            actionApiService.setActionApiResponse(actionApiContext, resultData);

            // check allow post process of api call BEFORE
            if (!actionApiContext.isAllowPostProcess()) {
                resultData.put("isSuccess", false);
                return resultData;
            }

            camundaEngineService.deleteProcessInstance(id);

            //Save history
            String strAttachFiles = null;
            String strAttachFilesName = null;
            String strAttachFilesSize = null;
            if (!ValidationUtils.isNullOrEmpty(attachFiles)) {
                strAttachFiles = String.join(",", attachFiles);
                strAttachFilesName = String.join(",", attachFilesName);
                strAttachFilesSize = String.join(",", attachFilesSize);
            }
            HistoryDto hisDto = new HistoryDto();
            hisDto.setActionUser(account);
            hisDto.setAction(TaskConstants.HistoryAction.CANCEL_TICKET.code);
            hisDto.setNote(reason);
            hisDto.setProcInstId(bpmProcInst.getTicketProcInstId());
            hisDto.setTicketId(ticketId);
            hisDto.setAttachFiles(strAttachFiles);
            hisDto.setAttachFilesName(strAttachFilesName);
            hisDto.setAttachFilesSize(strAttachFilesSize);

            // get action_user_info
            List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(account);
            if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
                Map<String, Object> actionUserInfo = new HashMap<>();
                String userTitle = lstUserTitle.stream()
                        .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                        .map(title -> {
                            String strTitle = StringUtil.nvl(title.getTitle(), "");
                            int concurrently = title.getConcurrently();
                            return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                        })
                        .collect(Collectors.joining(" "));
                actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
                actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
                hisDto.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
            }
            bpmHistoryManager.saveHistory(hisDto);

            bpmProcInst.setTicketClosedTime(LocalDateTime.now());
            bpmProcInst.setTicketCanceledTime(LocalDateTime.now());
            bpmProcInst.setTicketStatus(ProcInstConstants.Status.CANCEL.code);
            bpmProcInst.setCancelReason(reason);
            bpmProcInst.setCancelUser(account);
            bpmProcInstRepository.save(bpmProcInst);

            //Lấy task active
            List<BpmTask> bpmTasks = bpmTaskRepository.getAllTasksByStatus(Collections.singletonList(bpmProcInst.getTicketProcInstId()),
                    Arrays.asList("ACTIVE", "PROCESSING", "DELETED_BY_RU"));

            Map<String, VariableValueDto> variables = new HashMap<>();
            VariableValueDto variableDto = new VariableValueDto();
            variableDto.setType("String");
            variableDto.setValue(reason);
            variables.put("txt_LyDoHuy", variableDto);

            if (bpmTasks != null && !bpmTasks.isEmpty()) {
                List<String> taskDefKeys = bpmTasks.stream().map(BpmTask::getTaskDefKey).distinct().toList();
                for (String nextTaskDefKey : taskDefKeys) {
                    //Thông báo cho user liên quan
                    String actionCode = isAutoRequest ? ProcInstConstants.Notifications.AUTO_CANCEL.code : ProcInstConstants.Notifications.CANCEL.code;
                    NotificationUser payload = new NotificationUser();
                    payload.setBpmProcdefId(null);
                    payload.setNextTaskDefKey(nextTaskDefKey);
                    payload.setVariables(variables);
                    payload.setTicketId(bpmProcInst.getTicketId());
                    payload.setIsGetOldVariable(true);
                    payload.setLstCustomerEmails(null);
                    payload.setActionCode(actionCode);
                    payload.setEmailExe(credentialHelper.getJWTPayload().getUsername());
                    kafkaTemplate.send(notificationUser, payload);
//                    bpmProcdefNotificationService.addNotificationsByConfig(null, nextTaskDefKey, variables,
//                            bpmProcInst.getTicketId(), true, null, actionCode);
                }
            } else {
                // Trường hợp không có task active -> gửi cho người tạo
                String startKey = bpmProcInst.getTicketStartActId();
                String actionCode = isAutoRequest ? ProcInstConstants.Notifications.AUTO_CANCEL.code : ProcInstConstants.Notifications.CANCEL.code;

                NotificationUser payload = new NotificationUser();
                payload.setBpmProcdefId(null);
                payload.setNextTaskDefKey(startKey);
                payload.setVariables(variables);
                payload.setTicketId(bpmProcInst.getTicketId());
                payload.setIsGetOldVariable(true);
                payload.setLstCustomerEmails(null);
                payload.setActionCode(actionCode);
                payload.setEmailExe(credentialHelper.getJWTPayload().getUsername());
                kafkaTemplate.send(notificationUser, payload);
//                bpmProcdefNotificationService.addNotificationsByConfig(null, startKey, variables,
//                        bpmProcInst.getTicketId(), true, null, actionCode);
            }

            if (!ValidationUtils.isNullOrEmpty(bpmTasks)) {
                for (BpmTask e : bpmTasks) {
                    e.setTaskStatus(TaskConstants.Status.CANCEL.code);
                }
            }

            /* BEGIN handle call action api at the end */
            log.debug("DEBUG action api AFTER: cancelTicket = {}", actionApiContext);
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // Chỉ chạy sau khi transaction commit
                    actionApiService.endHandleActionApi(
                            actionApiContext,
                            actionApiService.createVariablesMap(
                                    bpmProcInst,
                                    actionApiService.getTicketVariables(id),
                                    optVariable
                            ));
                }
            });
            /* END handle call action api at the end */

            resultData.put("isSuccess", true);
            return resultData;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public BpmDiscussion updateDiscussion(MultipartFile[] files, String content, String
            discusId, List<Long> idFilesDelete, Long typeDiscussion, Boolean isAddtionalRequest, Boolean isCreateUserAdditionalRequest) {
        try {
            BpmDiscussion bpmDiscussion = bpmDiscussionRepo.findBpmDiscussionById(Long.parseLong(discusId));
            List<BpmDiscussionFile> discusFiles = new ArrayList<>();
            if (files != null) {
                if (!ValidationUtils.isNullOrEmpty(files)) {
                    for (MultipartFile file : files) {
                        fileManager.putFile(bucket, file.getOriginalFilename(), file.getSize(), file.getInputStream());
                        String url = fileManager.getUrlFile(bucket, file.getOriginalFilename());
                        BpmDiscussionFile bpmDiscussionFile1 = new BpmDiscussionFile();
                        bpmDiscussionFile1.setDiscussionId(bpmDiscussion.getId());
                        bpmDiscussionFile1.setFileName(file.getOriginalFilename());
                        bpmDiscussionFile1.setDownloadUrl(url);
                        discusFiles.add(bpmDiscussionFile1);
                    }
                    bpmDiscussionFileRepository.saveAll(discusFiles);
                }
            }

            if (!ValidationUtils.isNullOrEmpty(idFilesDelete)) {
                bpmDiscussionFileRepository.deleteAllById(idFilesDelete);
            }
            bpmDiscussion.setContent(content);
            bpmDiscussion.setTypeDiscussion(typeDiscussion);

            if (isAddtionalRequest) {
                bpmDiscussion.setIsAdditionalRequestCompleted(!ValidationUtils.isNullOrEmpty(isCreateUserAdditionalRequest) && isCreateUserAdditionalRequest);
            }

            bpmDiscussionRepo.save(bpmDiscussion);

            return bpmDiscussion;
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public Boolean discussion(MultipartFile[] files, String content, String procInstId, String groupId, String
            discusId, List<Long> idFilesDelete, Long typeDiscussion, Long ticketId, Boolean isAddtionalRequest, Boolean isCreateUserAdditionalRequest) {


        BpmProcInst bpmProcInst = bpmProcInstRepository.findById(ticketId).orElse(null);
        List<String> ignoreStatus = Arrays.asList(TaskConstants.Status.DELETED_BY_RU.code,
                TaskConstants.Status.RE_CREATED_BY_RU.code,
                TaskConstants.Status.COMPLETED.code,
                TaskConstants.Status.CANCEL.code);
        if (isAddtionalRequest && (bpmProcInst == null || ignoreStatus.contains(bpmProcInst.getTicketStatus()))) {
            throw new RuntimeException(common.getMessage("message.task.completed.not-allow"));
        }

        List<String> typeSend = new ArrayList<>();
        typeSend.add("WEB");
        typeSend.add("MAIL");
        try {
            BpmDiscussion discuss = new BpmDiscussion();
            BpmDiscussion parent = null;
            if (discusId != null && !discusId.isEmpty()) {
                discuss = updateDiscussion(files, content, discusId, idFilesDelete, typeDiscussion, isAddtionalRequest, isCreateUserAdditionalRequest);
            } else {
                discuss.setContent(content);
                discuss.setProcInstId(procInstId);
                discuss.setCreatedDate(new Date());
                discuss.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                discuss.setTypeDiscussion(typeDiscussion);
                discuss.setTicketId(ticketId);
                discuss.setIsAdditionalRequest(isAddtionalRequest);
                if (StringUtils.isNotEmpty(groupId)) {
                    Long idGroup = Long.parseLong(groupId);
                    discuss.setGroupId(idGroup);
                    if (typeDiscussion == 1) //Thông báo cho người tạo khi bình luận công khai
                        parent = bpmDiscussionRepo.findById(idGroup).orElse(null);
                }

                if (isAddtionalRequest) {
                    discuss.setIsAdditionalRequestCompleted(!ValidationUtils.isNullOrEmpty(isCreateUserAdditionalRequest) && isCreateUserAdditionalRequest);
                }

                BpmDiscussion discus = bpmDiscussionRepo.save(discuss);

                List<BpmDiscussionFile> discusFiles = new ArrayList<>();
                if (!ValidationUtils.isNullOrEmpty(files)) {
                    for (MultipartFile file : files) {
                        String orginFileName = FileUtils.convertFileName(FileUtils.lowerFileNameExtension(Objects.requireNonNull(file.getOriginalFilename())));
                        BpmDiscussionFile bpmDiscussionFile = new BpmDiscussionFile();
                        fileManager.putFile(bucket, orginFileName, file.getSize(), file.getInputStream());
                        bpmDiscussionFile.setDiscussionId(discus.getId());
                        bpmDiscussionFile.setFileName(orginFileName);
                        bpmDiscussionFile.setDownloadUrl(orginFileName);
                        bpmDiscussionFile.setFileSize(String.valueOf(file.getSize()));
                        discusFiles.add(bpmDiscussionFile);
                    }
                    bpmDiscussionFileRepository.saveAll(discusFiles);
                }
            }
            if (bpmProcInst != null) {
                //Lấy user tag trong bình luận
                List<String> lstCustomerUsers = new ArrayList<>();
                List<String> lstCreatedUser = new ArrayList<>();
                String contentText = content == null ? "" : content;
                org.jsoup.nodes.Document doc = Jsoup.parse(contentText);
                List<org.jsoup.nodes.Element> elements = doc.getElementsByClass("wysiwyg-mention");
                for (org.jsoup.nodes.Element element : elements) {
                    String user = element.attr("data-value");
                    String userFinal = Common.getFirstValue(user);
                    if (!ValidationUtils.isNullOrEmpty(userFinal)) {
                        lstCustomerUsers.add(userFinal);
                    }
                }
                if (!discuss.getCreatedUser().equals(bpmProcInst.getCreatedUser())) {
                    lstCreatedUser.add(bpmProcInst.getCreatedUser());
                }
                if (isAddtionalRequest) {

                    /* BEGIN handle call action api on beginning */
                    Map<String, VariableValueDto> variables = actionApiService.getTicketVariables(bpmProcInst.getTicketProcInstId());
                    BpmAdditionalRequestDto dto = new BpmAdditionalRequestDto();
                    dto.setContentRequest(content);
                    Map<String, Object> optVariable = new HashMap<>();
                    optVariable.put("accountLogin", credentialHelper.getJWTPayload().getUsername());
                    ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(
                            TaskActionConstants.Action.ADDITIONAL_REQUEST.code,
                            bpmProcInst.getTicketProcDefId(),
                            null,
                            bpmProcInst.getTicketProcInstId(),
                            actionApiService.createVariablesMap(dto, bpmProcInst, variables, optVariable));
                    /* END handle call action api on beginning */

                    //Update lại status nếu hop le
                    if (ValidationUtils.isNullOrEmpty(isCreateUserAdditionalRequest) || !isCreateUserAdditionalRequest) {
                        bpmProcInst.setTicketStatus(ProcInstConstants.Status.ADDITIONAL_REQUEST.code);
                        //Thông báo cho user liên quan
                        Map<String, VariableValueDto> variablesNotifi = new HashMap<>();
                        VariableValueDto variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(content);
                        variablesNotifi.put("txt_LyDoYeuCauBoSung", variableDto);

                        NotificationUser payload = new NotificationUser();
                        payload.setBpmProcdefId(null);
                        payload.setNextTaskDefKey(bpmProcInst.getTicketStartActId());
                        payload.setVariables(variablesNotifi);
                        payload.setTicketId(bpmProcInst.getTicketId());
                        payload.setIsGetOldVariable(true);
                        payload.setLstCustomerEmails(null);
                        payload.setActionCode(ProcInstConstants.Notifications.ADDITIONAL_REQUEST.code);
                        payload.setEmailExe(credentialHelper.getJWTPayload().getUsername());
                        kafkaTemplate.send(notificationUser, payload);
//                        bpmProcdefNotificationService.addNotificationsByConfig(null, bpmProcInst.getTicketStartActId(), variablesNotifi,
//                                bpmProcInst.getTicketId(), true, null, ProcInstConstants.Notifications.ADDITIONAL_REQUEST.code);

                    } else {
                        bpmProcInst.setTicketStatus(ProcInstConstants.Status.PROCESSING.code);

                        List<BpmTask> bpmTasks = bpmTaskRepository.getAllTasksByStatus(Collections.singletonList(bpmProcInst.getTicketProcInstId()), TaskConstants.TabStatus.PROCESSING);
                        for (BpmTask bpmTask : bpmTasks) {
                            List<String> lstAssignee = new ArrayList<>();
                            lstAssignee.add(bpmTask.getTaskAssignee());
                            if (bpmTask.getAssignType() != null && bpmTask.getAssignType()) {
                                List<ChangeAssigneeHistory> changeAssigneeHistories = changeAssigneeHistoryService.getLatestChangeByToAssignee(bpmProcInst.getTicketId(), bpmTask.getTaskId());
                                if (!ValidationUtils.isNullOrEmpty(changeAssigneeHistories)) {
                                    String orgAssignee = changeAssigneeHistories.get(0).getOrgAssignee();
                                    lstAssignee.add(orgAssignee);
                                }
                            }
                            //Thông báo cho user liên quan
                            NotificationUser payload = new NotificationUser();
                            payload.setBpmProcdefId(null);
                            payload.setNextTaskDefKey(bpmTask.getTaskDefKey());
                            payload.setVariables(new HashMap<>());
                            payload.setTicketId(bpmProcInst.getTicketId());
                            payload.setIsGetOldVariable(true);
                            payload.setLstCustomerEmails(lstAssignee);
                            payload.setActionCode(ProcInstConstants.Notifications.ADDITIONAL_REQUEST_COMPLETED.code);
                            payload.setEmailExe(credentialHelper.getJWTPayload().getUsername());
                            kafkaTemplate.send(notificationUser, payload);
//                            bpmProcdefNotificationService.addNotificationsByConfig(null, bpmTask.getTaskDefKey(), new HashMap<>(),
//                                    bpmProcInst.getTicketId(), true, Arrays.asList(bpmTask.getTaskAssignee()), ProcInstConstants.Notifications.ADDITIONAL_REQUEST_COMPLETED.code);
                        }
                    }

                    /* BEGIN handle call action api at the end */
                    actionApiService.endHandleActionApi(actionApiContext, actionApiService.createVariablesMap(dto, bpmProcInst, variables));
                    /* END handle call action api at the end */
                } else {
                    // nếu là phản hồi bình luận thì thông báo cho người chủ bình luận
                    if (parent != null && !discuss.getCreatedUser().equals(parent.getCreatedUser())) {
                        List<String> lstParentUser = new ArrayList<>();
                        lstParentUser.add(parent.getCreatedUser());
                        bpmProcdefNotificationService.addNotification(bpmProcInst.getTicketId(), ProcInstConstants.NotificationsObject.TAG_USER.code, lstParentUser, ProcInstConstants.Notifications.DISCUSSION.code, new HashMap<>(), typeSend, bpmProcInst.getTicketStartActId());
                    }
                    //Thông báo cho user được tag
                    if (!lstCustomerUsers.isEmpty()) {
                        bpmProcdefNotificationService.addNotification(bpmProcInst.getTicketId(), ProcInstConstants.NotificationsObject.TAG_USER.code, lstCustomerUsers, ProcInstConstants.Notifications.DISCUSSION.code, new HashMap<>(), typeSend, bpmProcInst.getTicketStartActId());
                    }
                    //Thông báo cho user tạo phiếu
                    if (typeDiscussion == 1) {
                        bpmProcdefNotificationService.addNotification(bpmProcInst.getTicketId(), ProcInstConstants.NotificationsObject.CREATE_USER_NOTIFICATION.code, lstCreatedUser, ProcInstConstants.Notifications.DISCUSSION.code, new HashMap<>(), typeSend, bpmProcInst.getTicketStartActId());
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    // tách api additional request
    public Boolean discussionAdditionalRequest(MultipartFile[] files, String content, String procInstId, String groupId, String
            discusId, List<Long> idFilesDelete, Long typeDiscussion, Long ticketId, Boolean isAddtionalRequest, Boolean isCreateUserAdditionalRequest) {


        BpmProcInst bpmProcInst = bpmProcInstRepository.findById(ticketId).orElse(null);
        List<String> ignoreStatus = Arrays.asList(TaskConstants.Status.DELETED_BY_RU.code,
                TaskConstants.Status.RE_CREATED_BY_RU.code,
                TaskConstants.Status.COMPLETED.code,
                TaskConstants.Status.CANCEL.code);
        if (isAddtionalRequest && (bpmProcInst == null || ignoreStatus.contains(bpmProcInst.getTicketStatus()))) {
            throw new RuntimeException(common.getMessage("message.task.completed.not-allow"));
        }

        try {
            BpmDiscussion discuss = new BpmDiscussion();
            BpmDiscussion parent = null;
            if (discusId != null && !discusId.isEmpty()) {
                discuss = updateDiscussion(files, content, discusId, idFilesDelete, typeDiscussion, isAddtionalRequest, isCreateUserAdditionalRequest);
            } else {
                discuss.setContent(content);
                discuss.setProcInstId(procInstId);
                discuss.setCreatedDate(new Date());
                discuss.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                discuss.setTypeDiscussion(typeDiscussion);
                discuss.setTicketId(ticketId);
                discuss.setIsAdditionalRequest(isAddtionalRequest);
                if (StringUtils.isNotEmpty(groupId)) {
                    Long idGroup = Long.parseLong(groupId);
                    discuss.setGroupId(idGroup);
                    // bổ sung thông tin update status_request
                    parent = bpmDiscussionRepo.getBpmDiscussionById(idGroup);
                    if (parent != null) {
                        parent.setStatusRequest(BusinessEnum.AdditionStatusRequest.UPDATED.code);
                    }
                }
                discuss.setIsAdditionalRequestCompleted(isCreateUserAdditionalRequest);
                if (isCreateUserAdditionalRequest) {
                    discuss.setStatusRequest(BusinessEnum.AdditionStatusRequest.UPDATED.code);
                } else {
                    discuss.setStatusRequest(BusinessEnum.AdditionStatusRequest.NEEDS_UPDATE.code);
                }

                BpmDiscussion discus = bpmDiscussionRepo.save(discuss);

                List<BpmDiscussionFile> discusFiles = new ArrayList<>();
                if (!ValidationUtils.isNullOrEmpty(files)) {
                    for (MultipartFile file : files) {
                        String orginFileName = FileUtils.convertFileName(FileUtils.lowerFileNameExtension(file.getOriginalFilename()));
                        BpmDiscussionFile bpmDiscussionFile = new BpmDiscussionFile();
                        fileManager.putFile(bucket, orginFileName, file.getSize(), file.getInputStream());
                        bpmDiscussionFile.setDiscussionId(discus.getId());
                        bpmDiscussionFile.setFileName(orginFileName);
                        bpmDiscussionFile.setDownloadUrl(orginFileName);
                        bpmDiscussionFile.setFileSize(String.valueOf(file.getSize()));
                        discusFiles.add(bpmDiscussionFile);
                    }
                    bpmDiscussionFileRepository.saveAll(discusFiles);
                }
            }
            if (bpmProcInst != null) {
                //Lấy user tag trong bình luận
//                List<String> lstCustomerUsers = new ArrayList<>();
//                List<String> lstCreatedUser = new ArrayList<>();
//                String contentText = content == null ? "" : content;
//                org.jsoup.nodes.Document doc = Jsoup.parse(contentText);
//                List<org.jsoup.nodes.Element> elements = doc.getElementsByClass("wysiwyg-mention");
//                for (org.jsoup.nodes.Element element : elements) {
//                    String user = element.attr("data-value");
//                    String userFinal = Common.getFirstValue(user);
//                    if (!ValidationUtils.isNullOrEmpty(userFinal)) {
//                        lstCustomerUsers.add(userFinal);
//                    }
//                }
//                if (!discuss.getCreatedUser().equals(bpmProcInst.getCreatedUser())) {
//                    lstCreatedUser.add(bpmProcInst.getCreatedUser());
//                }

                if (isAddtionalRequest) {
                    /* BEGIN handle call action api on beginning */
                    Map<String, VariableValueDto> variables = actionApiService.getTicketVariables(bpmProcInst.getTicketProcInstId());
                    BpmAdditionalRequestDto dto = new BpmAdditionalRequestDto();
                    dto.setContentRequest(content);
                    Map<String, Object> optVariable = new HashMap<>();
                    optVariable.put("accountLogin", credentialHelper.getJWTPayload().getUsername());
                    ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(
                            TaskActionConstants.Action.ADDITIONAL_REQUEST.code,
                            bpmProcInst.getTicketProcDefId(),
                            null,
                            bpmProcInst.getTicketProcInstId(),
                            actionApiService.createVariablesMap(dto, bpmProcInst, variables, optVariable));
                    /* END handle call action api on beginning */

                    // yêu cầu bổ sung
                    if (ValidationUtils.isNullOrEmpty(isCreateUserAdditionalRequest) || !isCreateUserAdditionalRequest) {
                        bpmProcInst.setTicketStatus(ProcInstConstants.Status.ADDITIONAL_REQUEST.code);
                        //Thông báo cho user liên quan
                        Map<String, VariableValueDto> variablesNotifi = new HashMap<>();
                        VariableValueDto variableDto = new VariableValueDto();
                        variableDto.setType("String");
                        variableDto.setValue(content);
                        variablesNotifi.put("txt_LyDoYeuCauBoSung", variableDto);

                        NotificationUser payload = new NotificationUser();
                        payload.setBpmProcdefId(null);
                        payload.setNextTaskDefKey(bpmProcInst.getTicketStartActId());
                        payload.setVariables(variablesNotifi);
                        payload.setTicketId(bpmProcInst.getTicketId());
                        payload.setIsGetOldVariable(true);
                        payload.setLstCustomerEmails(null);
                        payload.setActionCode(ProcInstConstants.Notifications.ADDITIONAL_REQUEST.code);
                        payload.setEmailExe(credentialHelper.getJWTPayload().getUsername());
                        kafkaTemplate.send(TopicConstants.TOPIC_NOTIFICATION_USER, payload);
//                        bpmProcdefNotificationService.addNotificationsByConfig(null, bpmProcInst.getTicketStartActId(), variablesNotifi,
//                                bpmProcInst.getTicketId(), true, null, ProcInstConstants.Notifications.ADDITIONAL_REQUEST.code);

                    } else { // bổ sung thông tin
                        Long countRequestRemain = bpmDiscussionRepo.countAdditionalRequestRemain(bpmProcInst.getTicketId());
                        // không còn additional request status_request = 0 mới update status ticket
                        if (countRequestRemain == 0) {
                            bpmProcInst.setTicketStatus(ProcInstConstants.Status.PROCESSING.code);
                        }

                        List<BpmTask> bpmTasks = bpmTaskRepository.getAllTasksByStatus(Collections.singletonList(bpmProcInst.getTicketProcInstId()), TaskConstants.TabStatus.PROCESSING);
                        for (BpmTask bpmTask : bpmTasks) {
                            List<String> lstAssignee = new ArrayList<>();
                            lstAssignee.add(bpmTask.getTaskAssignee());
                            if (bpmTask.getAssignType() != null && bpmTask.getAssignType()) {
                                List<ChangeAssigneeHistory> changeAssigneeHistories = changeAssigneeHistoryService.getLatestChangeByToAssignee(bpmProcInst.getTicketId(), bpmTask.getTaskId());
                                if (!ValidationUtils.isNullOrEmpty(changeAssigneeHistories)) {
                                    String orgAssignee = changeAssigneeHistories.get(0).getOrgAssignee();
                                    lstAssignee.add(orgAssignee);
                                }
                            }
                            //Thông báo cho user liên quan
                            NotificationUser payload = new NotificationUser();
                            payload.setBpmProcdefId(null);
                            payload.setNextTaskDefKey(bpmTask.getTaskDefKey());
                            payload.setVariables(new HashMap<>());
                            payload.setTicketId(bpmProcInst.getTicketId());
                            payload.setIsGetOldVariable(true);
                            payload.setLstCustomerEmails(lstAssignee);
                            payload.setActionCode(ProcInstConstants.Notifications.ADDITIONAL_REQUEST_COMPLETED.code);
                            payload.setEmailExe(credentialHelper.getJWTPayload().getUsername());
                            kafkaTemplate.send(TopicConstants.TOPIC_NOTIFICATION_USER, payload);
//                            bpmProcdefNotificationService.addNotificationsByConfig(null, bpmTask.getTaskDefKey(), new HashMap<>(),
//                                    bpmProcInst.getTicketId(), true, Arrays.asList(bpmTask.getTaskAssignee()), ProcInstConstants.Notifications.ADDITIONAL_REQUEST_COMPLETED.code);
                        }
                    }

                    /* BEGIN handle call action api at the end */
                    actionApiService.endHandleActionApi(actionApiContext, actionApiService.createVariablesMap(dto, bpmProcInst, variables));
                    /* END handle call action api at the end */
                }
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public PageDto getDiscussion(DiscussionRequest req) {
        try {
            int pageNum = req.getPage() - 1;
            Page<BpmDiscussion> pageDiscussion = bpmDiscussionRepo.findByTicketId(req.getTicketId(), req.getSearch().trim(), req.getIsAdditionalRequest(), PageRequest.of(pageNum, req.getLimit()));
            List<BpmDiscussion> listDiscussion = pageDiscussion.getContent();
            List<DiscussionResponse> discussionResponses = new ArrayList<>();
            List<Long> lists = new ArrayList<>();
            if (!listDiscussion.isEmpty()) {
                for (BpmDiscussion data : listDiscussion) {
                    List<FileResponse> fileResponse = new ArrayList<>();
                    DiscussionResponse discussionResponse = new DiscussionResponse();
                    Long getGroupId = data.getGroupId();
                    Long id = data.getId();
                    if (lists.contains(id)) {
                        continue;
                    }
                    if (!ValidationUtils.isNullOrEmpty(data.getGroupId())) {
                        data = bpmDiscussionRepo.findBpmDiscussionById(getGroupId);
                        lists.add(getGroupId);
                    } else {
                        List<BpmDiscussion> replyComments = bpmDiscussionRepo.getAllByGroupIdAndTicketIdAndIsAdditionalRequestAndGroupIdIsNullOrderByCreatedDateAsc(id, data.getTicketId(), data.getIsAdditionalRequest());
                        if (!ValidationUtils.isNullOrEmpty(replyComments)) {
                            discussionResponse.setId(data.getId());
                            discussionResponse.setProcInstId(data.getProcInstId());
                            discussionResponse.setTicketId(data.getTicketId());
                            discussionResponse.setContent(data.getContent());
                            discussionResponse.setTypeDiscussion(data.getTypeDiscussion());
                            discussionResponse.setCreatedUser(data.getCreatedUser());
                            discussionResponse.setCreatedDate(data.getCreatedDate());
                            discussionResponse.setStatusRequest(data.getStatusRequest());
                            List<BpmDiscussionFile> bpmDiscussionFileList = bpmDiscussionFileRepository.getBpmDiscussionFileByDiscussionId(data.getId());
                            if (!bpmDiscussionFileList.isEmpty()) {
                                List<BpmDiscussionFile> downloadUrls = bpmDiscussionFileList.stream().filter(bpmDiscussionFile -> bpmDiscussionFile.getDiscussionId().equals(id)).toList();
                                for (BpmDiscussionFile bpmDiscussionFile : downloadUrls) {
                                    FileResponse fileResponse1 = FileResponse.builder()
                                            .id(bpmDiscussionFile.getId())
                                            .downloadUrls(bpmDiscussionFile.getDownloadUrl())
                                            .fileName(bpmDiscussionFile.getFileName())
                                            .build();
                                    fileResponse.add(fileResponse1);
                                }
                                discussionResponse.setFileResponse(fileResponse);

                            }
                            discussionResponses.add(discussionResponse);
                        }
                    }
                    List<BpmDiscussionFile> bpmDiscussionFileList = bpmDiscussionFileRepository.getBpmDiscussionFileByDiscussionId(data.getId());
                    discussionResponse.setId(data.getId());
                    discussionResponse.setProcInstId(data.getProcInstId());
                    discussionResponse.setTicketId(data.getTicketId());
                    discussionResponse.setContent(data.getContent());
                    discussionResponse.setTypeDiscussion(data.getTypeDiscussion());
                    discussionResponse.setCreatedUser(data.getCreatedUser());
                    discussionResponse.setCreatedDate(data.getCreatedDate());
                    discussionResponse.setStatusRequest(data.getStatusRequest());

                    if (!bpmDiscussionFileList.isEmpty()) {
                        List<BpmDiscussionFile> downloadUrls = bpmDiscussionFileList.stream().filter(bpmDiscussionFile -> bpmDiscussionFile.getDiscussionId().equals(discussionResponse.getId())).toList();
                        for (BpmDiscussionFile bpmDiscussionFile : downloadUrls) {
                            FileResponse fileResponse1 = FileResponse.builder()
                                    .id(bpmDiscussionFile.getId())
                                    .downloadUrls(bpmDiscussionFile.getDownloadUrl())
                                    .fileName(bpmDiscussionFile.getFileName())
                                    .fileSize(bpmDiscussionFile.getFileSize())
                                    .build();
                            fileResponse.add(fileResponse1);
                        }
                        discussionResponse.setFileResponse(fileResponse);

                    }
                    ///reply comment
                    List<BpmDiscussion> replyComments;
                    if (ValidationUtils.isNullOrEmpty(getGroupId)) {
                        replyComments = bpmDiscussionRepo.getAllByGroupIdAndTicketIdAndIsAdditionalRequestAndGroupIdNotNullOrderByCreatedDateAsc(id, data.getTicketId(), data.getIsAdditionalRequest());
                    } else {
                        replyComments = bpmDiscussionRepo.getAllByGroupIdAndTicketIdAndIsAdditionalRequestAndGroupIdNotNullOrderByCreatedDateAsc(getGroupId, data.getTicketId(), data.getIsAdditionalRequest());
                    }
                    List<DiscussionResponse> discussionReplyResponses = new ArrayList<>();
                    for (BpmDiscussion replyComment : replyComments) {
                        List<FileResponse> fileReplyResponse = new ArrayList<>();
                        List<BpmDiscussionFile> bpmDiscussionReplyFileList = bpmDiscussionFileRepository.getBpmDiscussionFileByDiscussionId(replyComment.getId());
                        DiscussionResponse discussionReplyResponse = new DiscussionResponse();
                        discussionReplyResponse.setId(replyComment.getId());
                        discussionReplyResponse.setProcInstId(replyComment.getProcInstId());
                        discussionReplyResponse.setTicketId(replyComment.getTicketId());
                        discussionReplyResponse.setTypeDiscussion(replyComment.getTypeDiscussion());
                        discussionReplyResponse.setContent(replyComment.getContent());
                        discussionReplyResponse.setCreatedUser(replyComment.getCreatedUser());
                        discussionReplyResponse.setCreatedDate(replyComment.getCreatedDate());
                        discussionReplyResponse.setGroupId(replyComment.getGroupId());
                        discussionReplyResponse.setStatusRequest(data.getStatusRequest());
                        if (!bpmDiscussionReplyFileList.isEmpty()) {
                            List<BpmDiscussionFile> downloadUrls = bpmDiscussionReplyFileList.stream().filter(bpmDiscussionFile -> bpmDiscussionFile.getDiscussionId().equals(replyComment.getId())).toList();
                            if (bpmDiscussionReplyFileList.equals(bpmDiscussionFileList)) {
                                continue;
                            }
                            for (BpmDiscussionFile bpmDiscussionFile : downloadUrls) {
                                FileResponse fileResponse2 = FileResponse.builder()
                                        .id(bpmDiscussionFile.getId())
                                        .downloadUrls(bpmDiscussionFile.getDownloadUrl())
                                        .fileName(bpmDiscussionFile.getFileName())
                                        .fileSize(bpmDiscussionFile.getFileSize())
                                        .build();
                                fileReplyResponse.add(fileResponse2);
                            }
                            discussionReplyResponse.setFileResponse(fileReplyResponse);
                        }
                        discussionReplyResponses.add(discussionReplyResponse);

                    }
                    discussionResponse.setDiscussionReplys(discussionReplyResponses);
                    discussionResponses.add(discussionResponse);

                }
            }

            Set<DiscussionResponse> discussionResponseSet = new HashSet<>(discussionResponses);
            discussionResponses = new ArrayList<>(discussionResponseSet);
            discussionResponses.sort((o1, o2) -> o2.getCreatedDate().compareTo(o1.getCreatedDate()));
            return PageDto.builder()
                    .content(discussionResponses)
                    .number(pageNum)
                    .numberOfElements(pageDiscussion.getNumberOfElements())
                    .page(pageNum)
                    .size(pageDiscussion.getSize())
                    .totalPages(pageDiscussion.getTotalPages())
                    .totalElements(pageDiscussion.getTotalElements())
                    .build();
        } catch (Exception e) {
            return null;
        }
    }

    public DiscussionResponse getLastComment(String procInstId) {
        try {
            Sort sort = responseUtils.getSort("createdDate", "DESC");
            Page<BpmDiscussion> lastCmt = bpmDiscussionRepo.findByProcInstId(procInstId, PageRequest.of(0, 1, sort));
            if (!lastCmt.getContent().isEmpty()) {
                return modelMapper.map(lastCmt.getContent().get(0), DiscussionResponse.class);
            }
            return new DiscussionResponse();
        } catch (Exception e) {
            return null;
        }
    }

    public Long CountCmtByTicket(String procInstId) {
        try {
            return bpmDiscussionRepo.countByProcInstId(procInstId);
        } catch (Exception e) {
            return null;
        }
    }

    public Boolean deleteDiscussion(Long discusId) {
        try {
            BpmDiscussion bpmDiscussion = bpmDiscussionRepo.findBpmDiscussionById(discusId);
            if (bpmDiscussion.getIsAdditionalRequest()) {
                return false;
            }
            if (ValidationUtils.isNullOrEmpty(bpmDiscussion.getGroupId())) {
                bpmDiscussionRepo.deleteById(discusId);
                bpmDiscussionRepo.deleteAllByGroupId(bpmDiscussion.getId());
            } else {
                bpmDiscussionRepo.deleteById(discusId);
            }


            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public List<LoadTicketDto> getByProcDefId(String procDefId) {
        return bpmProcInstRepository.getBpmProcInstByTicketProcDefId(procDefId)
                .stream().map(bpmLoadTicketMapper::entityToDto).collect(Collectors.toList());
    }

    public Boolean createShared(Map<String, Object> data, String actionUser) {
        try {
            String procInstId = data.get("procInstId").toString();
            List<String> listUser = (List<String>) data.get("listUser");
            String type = data.get("type").toString().toUpperCase();
            List<BpmShared> listShare = new ArrayList<>();
            List<String> listExist = bpmSharedRepository.listExist(procInstId, listUser);
            List<String> allUser = new ArrayList<>(listUser);
            allUser.add(actionUser);
            List<AccountModel> accountModels = customerService.getAccountByUsernames(allUser);
            for (String user : listUser) {
                if (listExist.contains(user)) {
                    continue;
                }
                BpmShared share = new BpmShared();
                share.setSharedUser(user);
                share.setCreatedDate(new Date());
                share.setType(type);
                share.setProcInstId(procInstId);
                share.setCreatedUser(actionUser);
                if (!ValidationUtils.isNullOrEmpty(accountModels)) {
                    String sharedInfo = accountModels.stream()
                            .filter(e -> e.getUsername().equalsIgnoreCase(user))
                            .map(e -> e.getUsername() + "-" + e.getLastname() + " " + e.getFirstname() + "-" + e.getFinalTitle())
                            .findFirst().orElse(null);
                    String createdInfo = accountModels.stream()
                            .filter(e -> e.getUsername().equalsIgnoreCase(actionUser))
                            .map(e -> e.getUsername() + "-" + e.getLastname() + " " + e.getFirstname() + "-" + e.getFinalTitle())
                            .findFirst().orElse("Hệ thống");

                    share.setSharedUserInfo(sharedInfo);
                    share.setCreatedUserInfo(createdInfo);
                }
                listShare.add(share);
            }

            bpmSharedRepository.saveAll(listShare);
            BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(procInstId);
            if (bpmProcInst != null && !type.equalsIgnoreCase("FOLLOWED")) { // theo dõi ko gửi mail + noti
                List<String> lstCustomerEmails = listShare.stream().map(BpmShared::getSharedUser).collect(Collectors.toList());
                //Thông báo cho user liên quan
                NotificationUser payload = new NotificationUser();
                payload.setBpmProcdefId(null);
                payload.setNextTaskDefKey(ProcInstConstants.Notifications.SHARE.code);
                payload.setVariables(new HashMap<>());
                payload.setTicketId(bpmProcInst.getTicketId());
                payload.setIsGetOldVariable(true);
                payload.setLstCustomerEmails(lstCustomerEmails);
                payload.setActionCode(ProcInstConstants.Notifications.SHARE.code);
                payload.setEmailExe(credentialHelper.getJWTPayload().getUsername());
                kafkaTemplate.send(notificationUser, payload);
//                bpmProcdefNotificationService.addNotificationsByConfig(null, ProcInstConstants.Notifications.SHARE.code, new HashMap<>(),
//                        bpmProcInst.getTicketId(), true, lstCustomerEmails, ProcInstConstants.Notifications.SHARE.code);
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public PageDto loadShared(ShareRequest request, String email) {
        try {
            int pageNum = request.getPage() - 1;
            Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());
            if (request.getCheckFollow()) {
                request.setUser(email);
            }
            Page<BpmShared> pageShare = bpmSharedRepository.findAll(shareSpecification.filterShare(request), PageRequest.of(pageNum, request.getLimit(), sort));
            List<ShareResponse> listResult = pageShare.get().map(x -> modelMapper.map(x, ShareResponse.class)).collect(Collectors.toList());
            return PageDto.builder()
                    .content(listResult)
                    .number(pageNum)
                    .numberOfElements(pageShare.getNumberOfElements())
                    .page(pageNum)
                    .size(pageShare.getSize())
                    .totalPages(pageShare.getTotalPages())
                    .totalElements(pageShare.getTotalElements())
                    .build();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Boolean deleteShared(Long id) {
        try {
            BpmShared bpmShared = bpmSharedRepository.findById(id).orElse(null);
            List<BpmShared> lstBpmShared = new ArrayList<>();
            String actionUser = credentialHelper.getJWTPayload().getUsername();
            if (bpmShared != null) {
                BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(bpmShared.getProcInstId());
                if (bpmProcInst != null) {
                    List<String> lstCustomerEmails = Arrays.asList(bpmProcInst.getCreatedUser(), bpmShared.getSharedUser());
                    //Thông báo cho user liên quan
                    NotificationUser payload = new NotificationUser();
                    payload.setBpmProcdefId(null);
                    payload.setNextTaskDefKey(ProcInstConstants.Notifications.REMOVE_SHARE.code);
                    payload.setVariables(new HashMap<>());
                    payload.setTicketId(bpmProcInst.getTicketId());
                    payload.setIsGetOldVariable(true);
                    payload.setLstCustomerEmails(lstCustomerEmails);
                    payload.setActionCode(ProcInstConstants.Notifications.REMOVE_SHARE.code);
                    payload.setEmailExe(actionUser);
                    kafkaTemplate.send(notificationUser, payload);
//                    bpmProcdefNotificationService.addNotificationsByConfig(null, ProcInstConstants.Notifications.REMOVE_SHARE.code, new HashMap<>(),
//                            bpmProcInst.getTicketId(), true, lstCustomerEmails, ProcInstConstants.Notifications.REMOVE_SHARE.code);

                }
                BpmShared bpmDeleted = new BpmShared();
                bpmDeleted.setSharedUser(bpmShared.getSharedUser());
                bpmDeleted.setCreatedDate(new Date());
                bpmDeleted.setType("DELETED");
                bpmDeleted.setProcInstId(bpmShared.getProcInstId());
                bpmDeleted.setCreatedUser(actionUser);
                bpmDeleted.setSharedUserInfo(bpmShared.getSharedUserInfo());
                List<AccountModel> accountModels = customerService.getAccountByUsernames(Arrays.asList(actionUser, bpmShared.getSharedUser()));
                if (!ValidationUtils.isNullOrEmpty(accountModels)) {
                    String createdInfo = accountModels.stream()
                            .filter(e -> e.getUsername().equalsIgnoreCase(actionUser))
                            .map(e -> e.getUsername() + "-" + e.getLastname() + " " + e.getFirstname() + "-" + e.getFinalTitle())
                            .toList().get(0);
                    bpmDeleted.setCreatedUserInfo(createdInfo);
                }
                bpmShared.setIsDeleted(true);

                lstBpmShared.add(bpmShared);
                lstBpmShared.add(bpmDeleted);
            }

            bpmSharedRepository.saveAll(lstBpmShared);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public Boolean cancelMonitor(String procInstId, String user) {
        try {
            bpmSharedRepository.deleteAllByProcInstIdAndSharedUser(procInstId, user);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public List<String> getListRolePermission(String procInstId) {
        try {
            String email = credentialHelper.getJWTPayload().getEmail();
            List<Object[]> listPermission = bpmProcInstRepository.listPermission(procInstId, email);
            List<String> listFinal = new ArrayList<>();
            if (!listPermission.isEmpty()) {
                Object[] data = listPermission.get(0);
                if (data[0] != null) {
                    listFinal.add(ProcInstConstants.Permission.CREATOR);
                }
                if (data[1] != null) {
                    listFinal.add(ProcInstConstants.Permission.PROCESS_OWNER);
                }
                if (data[2] != null) {
                    listFinal.add(ProcInstConstants.Permission.SHARED);
                }
            }

            // (phucvm3) check task assignee
            List<String> taskUsers = bpmTaskUserService.getAllUserName(procInstId, null);
            if (!ValidationUtils.isNullOrEmpty(taskUsers)) {
                if (taskUsers.stream().anyMatch(e -> e.equalsIgnoreCase(email))) {
                    listFinal.add(ProcInstConstants.Permission.ASSIGNEE);
                }
            }

            return listFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public boolean checkUser(String account) {
        try {
            List<BpmTaskUser> bpmTaskUserList = bpmTaskUserService.getAllByUserName(account);
            List<String> taskIds = bpmTaskUserList.stream().map(BpmTaskUser::getTaskId).collect(Collectors.toList());
            List<BpmTask> listTask = bpmTaskRepository.getListTaskByListTaskId(taskIds, TaskConstants.TabStatus.PROCESSING, account);
            List<BpmProcInst> lstTicket = bpmProcInstRepository.findBpmProcInstByTicketStartUserId(account);
            if (!ValidationUtils.isNullOrEmpty(listTask) || !ValidationUtils.isNullOrEmpty(lstTicket)) {
                return false;
            }
        } catch (Exception e) {
            log.error("Error checkUser: {}", e.getMessage());
        }

        return true;
    }

    public List<Map<String, Object>> getTicketLink(Long ticketId) {
        List<Long> ticketLinkIds = bpmProcinstLinkManager.getProcinstLinkByBpmProcinstId(ticketId);

        List<BpmProcInst> bpmProcInsts = bpmProcInstRepository.findBpmProcInstByticketIdIn(ticketLinkIds);

        return responseLinkTicket(bpmProcInsts);
    }

    private List<Map<String, Object>> responseLinkTicket(List<BpmProcInst> bpmProcInsts) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(bpmProcInsts)) {
            for (BpmProcInst bpmProcInst : bpmProcInsts) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", bpmProcInst.getTicketId());
                map.put("title", bpmProcInst.getTicketTitle());
                map.put("procDefId", bpmProcInst.getTicketProcDefId());
                map.put("procInstId", bpmProcInst.getTicketProcInstId());
                map.put("requestCode", bpmProcInst.getRequestCode());
                ServicePackage servicePackage = bpmProcInst.getServicePackage();
                map.put("serviceId", servicePackage.getId());
                map.put("serviceName", servicePackage.getServiceName());
                result.add(map);
            }
        }
        return result;
    }

    public List<Map<String, Object>> getTicket(Long ticketId) {
        List<Long> ticketIds = bpmProcinstLinkManager.getProcinstLinkByBpmProcinstLinkId(ticketId);

        List<BpmProcInst> bpmProcInsts = bpmProcInstRepository.findBpmProcInstByticketIdIn(ticketIds);

        return responseLinkTicket(bpmProcInsts);
    }

    /**
     * Query data for my ticket, ticket approval & ticket execution screen
     *
     * <AUTHOR>
     */
    public PagingResponse<?> query(QueryRequest<TicketFilter> request) {
        if (request == null) {
            log.info("Query process-instance ==> the request object is null");
            return null;
        }
        TicketFilter sample = request.getSample();
        EntityManager em = getEntityManager();
        try {

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<TicketDto> query = cb.createQuery(TicketDto.class);

            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef);
            Join<BpmProcInst, BpmTask> bpmTask = bpmProcInst.join(BpmProcInst_.bpmTasks, JoinType.LEFT);

            // get predicate by filter
            Predicate predicate = bpmProcInstSpecification.queryFilter(sample, cb, query, bpmProcInst, servicePackage, bpmTask);

            // select
            query.multiselect(
                    bpmProcInst.get(BpmProcInst_.ticketId),
                    bpmProcInst.get(BpmProcInst_.ticketTitle),
                    bpmProcdef.get(BpmProcdef_.procDefId),
                    bpmProcInst.get(BpmProcInst_.ticketProcInstId),
                    servicePackage.get(ServicePackage_.id),
                    servicePackage.get(ServicePackage_.serviceName),
                    bpmProcInst.get(BpmProcInst_.ticketStatus),
                    bpmProcInst.get(BpmProcInst_.ticketCreatedTime),
                    bpmProcInst.get(BpmProcInst_.ticketStartedTime),
                    bpmProcInst.get(BpmProcInst_.ticketEndTime),
                    bpmProcInst.get(BpmProcInst_.ticketCanceledTime),
                    bpmProcInst.get(BpmProcInst_.ticketClosedTime),
                    bpmProcInst.get(BpmProcInst_.ticketFinishTime),
                    bpmProcInst.get(BpmProcInst_.ticketEditTime),
                    bpmProcInst.get(BpmProcInst_.comment),
                    bpmProcInst.get(BpmProcInst_.cancelReason),
                    bpmProcdef.get(BpmProcdef_.autoClose)
            ).distinct(true);

            // where
            if (predicate != null) {
                query.where(predicate);
            }

            // sort
            List<String> noSortFields = new ArrayList<>(List.of("remainingTime"));
            if (!ValidationUtils.isNullOrEmpty(request.getOrders())) {
                // init columns mapping
                Map<String, String> bpmProcInstAllowSortColumns = Stream.of(new String[][]{
                        {"ticketId", BpmProcInst_.TICKET_ID}, {"ticketTitle", BpmProcInst_.TICKET_TITLE},
                        {"ticketStatus", BpmProcInst_.TICKET_STATUS}, {"createdTime", BpmProcInst_.TICKET_CREATED_TIME}
                }).collect(Collectors.toMap(data -> data[0], data -> data[1]));

                Map<String, String> servicePackageAllowSortColumns = Stream.of(new String[][]{
                        {"serviceName", ServicePackage_.SERVICE_NAME}
                }).collect(Collectors.toMap(data -> data[0], data -> data[1]));

                // build query order
                List<Order> orders = new ArrayList<>();
                request.getOrders().forEach(order -> {
                    String property = order.getProperty();
                    if (!noSortFields.contains(property)) {
                        From<?, ?> from = null;
                        if (bpmProcInstAllowSortColumns.containsKey(property)) {
                            from = bpmProcInst;
                            order.setProperty(bpmProcInstAllowSortColumns.get(order.getProperty()));
                        } else if (servicePackageAllowSortColumns.containsKey(property)) {
                            from = servicePackage;
                            order.setProperty(servicePackageAllowSortColumns.get(order.getProperty()));
                        }

                        if (from != null) {
                            orders.add(SortUtils.getQueryOrder(cb, from, order));
                        }
                    }
                });

                if (!ValidationUtils.isNullOrEmpty(orders)) {
                    query.orderBy(orders);
                }
            }

            // query and pagination
            TypedQuery<TicketDto> typedQuery = em.createQuery(query);
            if (request.getPageInfo() != null) {
                int firstResult = QueryUtils.getFirstResult(request.getPageInfo().getPageNumber(), request.getPageInfo().getPageSize());
                int maxResult = request.getPageInfo().getPageSize();
                typedQuery.setFirstResult(firstResult).setMaxResults(maxResult);
            }
            List<TicketDto> bpmProcInstList = typedQuery.getResultList();
            if (bpmProcInstList != null) {
                // get addition data
                getAdditionDataForTicket(bpmProcInstList);
            }

            int pageSize = QueryUtils.getPageSize(request.getPageInfo(), bpmProcInstList);

            // count query
            CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
            Root<BpmProcInst> bpmProcInstCount = countQuery.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackageCount = bpmProcInstCount.join(BpmProcInst_.servicePackage);
            Join<BpmProcInst, BpmTask> bpmTaskCount = bpmProcInstCount.join(BpmProcInst_.bpmTasks, JoinType.LEFT);

            Predicate predicateCount = bpmProcInstSpecification.queryFilter(sample, cb, countQuery, bpmProcInstCount, servicePackageCount, bpmTaskCount);
            countQuery.select(cb.countDistinct(bpmProcInstCount)).where(predicateCount);
            Long total = em.createQuery(countQuery).getSingleResult();

            return PagingResponse.<TicketDto>builder()
                    .content(bpmProcInstList)
                    .pageNumber(QueryUtils.getPageNumber(request.getPageInfo()))
                    .pageSize(pageSize)
                    .numberOfElements(QueryUtils.getNumberOfElements(bpmProcInstList))
                    .totalElements(total)
                    .totalPages(QueryUtils.getTotalPage(total, pageSize))
                    .build();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            if (em != null)
                em.close();
        }

        return null;
    }

    /**
     * Get additional data
     *
     * <AUTHOR>
     */
    private void getAdditionDataForTicket(List<TicketDto> bpmProcInstList) {
        List<String> bpmProcInstIds = bpmProcInstList.stream().map(TicketDto::getProcInstId).collect(Collectors.toList());
        List<BpmTask> bpmTasks = bpmTaskManager.getAllProcessingTasks(bpmProcInstIds);

        // get name of active tasks
        if (!ValidationUtils.isNullOrEmpty(bpmTasks)) {
            Map<String, List<BpmTask>> procInstIdToTasksMap = bpmTasks.stream().collect(Collectors.groupingBy(BpmTask::getTaskProcInstId));
            Map<String, String> procInstIdToTaskNamesMap = procInstIdToTasksMap.entrySet()
                    .stream()
                    .collect(Collectors.toMap(Map.Entry::getKey,
                            taskMap -> taskMap.getValue().stream().map(BpmTask::getTaskName).collect(Collectors.joining(", "))
                    ));

            bpmProcInstList.forEach(e -> {
                String procInstId = e.getProcInstId();
                e.setTaskNames(procInstIdToTaskNamesMap.get(procInstId));
            });
        }
    }

    /**
     * Count ticket of assistant
     *
     * <AUTHOR>
     */
    public List<TicketCountDto> assistantTicketCount(TicketAssistantFilter sample) {
        List<TicketCountDto> result = new ArrayList<>();
        if (sample == null) {
            log.info("Count process-instance ==> the request object is null");
            return result;
        }
        EntityManager em = getEntityManager();
        try {
            //load all ticket assistant
            List<String> lstTicket = assistantRepo.getListTicketByAssistantEmail(sample.getAssistantEmail());
            sample.setLstTicketId(lstTicket);


            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<Tuple> query = cb.createQuery(Tuple.class);

            /* 1. Count all bpm_procinst status */
            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage);

            // get predicate by filter
            Predicate predicate = bpmProcInstSpecification.queryFilterAssistant(sample, cb, query, bpmProcInst, servicePackage);

            // select
            query.multiselect(
                    bpmProcInst.get(BpmProcInst_.ticketStatus),
                    cb.countDistinct(bpmProcInst.get(BpmProcInst_.ticketId))
            );

            // where
            if (predicate != null) {
                query.where(predicate);
            }

            // group by
            query.groupBy(bpmProcInst.get(BpmProcInst_.ticketStatus));

            // get result
            Map<String, TicketCountDto> statusMap = initTicketCountByStatusMap();
            List<Tuple> resultList = em.createQuery(query).getResultList();
            if (!ValidationUtils.isNullOrEmpty(resultList)) {
                resultList.forEach(e -> {
                    String status = (String) e.get(0);
                    Long total = (Long) e.get(1);

                    String tab;
                    if (ProcInstConstants.TabStatus.PROCESSING.contains(status)) {
                        tab = ProcInstConstants.Tab.PROCESSING;
                    } else if (ProcInstConstants.TabStatus.COMPLETE.contains(status)) {
                        tab = ProcInstConstants.Tab.COMPLETE;
                    } else if (status.equalsIgnoreCase(ProcInstConstants.Status.CANCEL.code)) {
                        tab = ProcInstConstants.Tab.CANCEL;
                    } else if (status.equalsIgnoreCase(ProcInstConstants.Status.DRAFT.code)) {
                        tab = ProcInstConstants.Tab.DRAFT;
                    } else {
                        tab = "";
                    }

                    TicketCountDto ticketCountDto = statusMap.get(tab);
                    if (ticketCountDto != null) {
                        ticketCountDto.setTotal(ticketCountDto.getTotal() + total);
                    }
                });
            }

            /* 2. Count shared status */
            CriteriaQuery<Long> queryShared = cb.createQuery(Long.class);
            sample.setTab(ProcInstConstants.Tab.SHARED);

            //Count list FOLLOWED from BPM_SHARED
            List<String> lstTicketShare = assistantRepo.getListShareTicket(sample.getAssistantEmail());
            sample.setLstTicketId(lstTicketShare != null ? lstTicketShare : new ArrayList<>());
            bpmProcInst = queryShared.from(BpmProcInst.class);
            servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage);

            predicate = bpmProcInstSpecification.queryFilterAssistant(sample, cb, queryShared, bpmProcInst, servicePackage);

            // select
            queryShared.select(cb.countDistinct(bpmProcInst.get(BpmProcInst_.ticketId)));

            if (predicate != null) {
                queryShared.where(predicate);
            }

            // get result
            Long totalShared = em.createQuery(queryShared).getSingleResult();
            TicketCountDto ticketCountDto = statusMap.get(ProcInstConstants.Tab.SHARED);
            if (ticketCountDto != null) {
                ticketCountDto.setTotal(ticketCountDto.getTotal() + totalShared);
            }

            result.addAll(statusMap.values());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            if (em != null)
                em.close();
        }

        return result;
    }

    /**
     * Count ticket
     *
     * <AUTHOR>
     */
    public List<TicketCountDto> count(TicketFilter sample) {
        List<TicketCountDto> result = new ArrayList<>();
        if (sample == null) {
            log.info("Count process-instance ==> the request object is null");
            return result;
        }
        EntityManager em = getEntityManager();
        try {

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<Tuple> query = cb.createQuery(Tuple.class);

            /* 1. Count all bpm_procinst status */
            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage);
            Join<BpmProcInst, BpmTask> bpmTask = bpmProcInst.join(BpmProcInst_.bpmTasks, JoinType.LEFT);

            // get predicate by filter
            Predicate predicate = bpmProcInstSpecification.queryFilter(sample, cb, query, bpmProcInst, servicePackage, bpmTask);

            // select
            query.multiselect(
                    bpmProcInst.get(BpmProcInst_.ticketStatus),
                    cb.countDistinct(bpmProcInst.get(BpmProcInst_.ticketId))
            );

            // where
            if (predicate != null) {
                query.where(predicate);
            }

            // group by
            query.groupBy(bpmProcInst.get(BpmProcInst_.ticketStatus));

            // get result
            Map<String, TicketCountDto> statusMap = initTicketCountByStatusMap();
            List<Tuple> resultList = em.createQuery(query).getResultList();
            if (!ValidationUtils.isNullOrEmpty(resultList)) {
                resultList.forEach(e -> {
                    String status = (String) e.get(0);
                    Long total = (Long) e.get(1);

                    String tab;
                    if (ProcInstConstants.TabStatus.PROCESSING.contains(status)) {
                        tab = ProcInstConstants.Tab.PROCESSING;
                    } else if (ProcInstConstants.TabStatus.COMPLETE.contains(status)) {
                        tab = ProcInstConstants.Tab.COMPLETE;
                    } else if (status.equalsIgnoreCase(ProcInstConstants.Status.CANCEL.code)) {
                        tab = ProcInstConstants.Tab.CANCEL;
                    } else if (status.equalsIgnoreCase(ProcInstConstants.Status.DRAFT.code)) {
                        tab = ProcInstConstants.Tab.DRAFT;
                    } else {
                        tab = "";
                    }

                    TicketCountDto ticketCountDto = statusMap.get(tab);
                    if (ticketCountDto != null) {
                        ticketCountDto.setTotal(ticketCountDto.getTotal() + total);
                    }
                });
            }

            /* 2. Count shared status */
            CriteriaQuery<Long> queryShared = cb.createQuery(Long.class);
            sample.setTab(ProcInstConstants.Tab.SHARED);

            bpmProcInst = queryShared.from(BpmProcInst.class);
            servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage);
            bpmTask = bpmProcInst.join(BpmProcInst_.bpmTasks, JoinType.LEFT);
            predicate = bpmProcInstSpecification.queryFilter(sample, cb, queryShared, bpmProcInst, servicePackage, bpmTask);

            // select
            queryShared.select(cb.countDistinct(bpmProcInst.get(BpmProcInst_.ticketId)));

            if (predicate != null) {
                queryShared.where(predicate);
            }

            // get result
            Long totalShared = em.createQuery(queryShared).getSingleResult();
            TicketCountDto ticketCountDto = statusMap.get(ProcInstConstants.Tab.SHARED);
            if (ticketCountDto != null) {
                ticketCountDto.setTotal(ticketCountDto.getTotal() + totalShared);
            }

            result.addAll(statusMap.values());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            if (em != null)
                em.close();
        }

        return result;
    }

    /**
     * Init tab's ticket count data map
     *
     * <AUTHOR>
     */
    private Map<String, TicketCountDto> initTicketCountByStatusMap() {
        return Stream.of(
                new AbstractMap.SimpleEntry<>(ProcInstConstants.Tab.PROCESSING, new TicketCountDto(ProcInstConstants.Tab.PROCESSING, 0L)),
                new AbstractMap.SimpleEntry<>(ProcInstConstants.Tab.COMPLETE, new TicketCountDto(ProcInstConstants.Tab.COMPLETE, 0L)),
                new AbstractMap.SimpleEntry<>(ProcInstConstants.Tab.CANCEL, new TicketCountDto(ProcInstConstants.Tab.CANCEL, 0L)),
                new AbstractMap.SimpleEntry<>(ProcInstConstants.Tab.DRAFT, new TicketCountDto(ProcInstConstants.Tab.DRAFT, 0L)),
                new AbstractMap.SimpleEntry<>(ProcInstConstants.Tab.SHARED, new TicketCountDto(ProcInstConstants.Tab.SHARED, 0L))
        ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private EntityManager getEntityManager() {
        return entityManagerFactory.createEntityManager();
    }

    /**
     * Get bpm-proc-inst by proc_inst_id
     *
     * <AUTHOR>
     */
    public BpmProcInst findBpmProcInstByTicketProcInstId(String id) {
        return bpmProcInstRepository.findBpmProcInstByTicketProcInstId(id);
    }

    public TicketDefaultResponse getDefaultByProcInstId(Long ticketId) {
        try {
            BpmProcInst bpmProcInst = findById(ticketId);
            List<NotifyUserResponse> notifyUserResponseList = new ArrayList<>();
            TicketDefaultResponse ticketDefaultResponse = new TicketDefaultResponse();
            if (bpmProcInst != null) {
                List<BpmProInstNotifyUser> proInstNotifyUser = bpmProcInstNotifyManager.getDetail(ticketId);
                List<BpmProInstNotifyGroup> proInstNotifyGroup = bpmProInstNotifyGroupManager.getDetail(ticketId);
                List<String> listNotifyUserEmail = new ArrayList<>();
                List<Long> listNotifyGroupId = new ArrayList<>();
                if (!ValidationUtils.isNullOrEmpty(proInstNotifyUser)) {
                    for (BpmProInstNotifyUser bpmProInstNotifyUser : proInstNotifyUser) {
                        String notifyUserEmail;
                        if (bpmProInstNotifyUser.getRecipient() != null) {
                            notifyUserEmail = bpmProInstNotifyUser.getRecipient();
                            listNotifyUserEmail.add(notifyUserEmail);
                        }
                    }
                    List<ChartInfoRoleResponse> userInfoList = customerService.getUserByEmail(listNotifyUserEmail);
                    if (!ValidationUtils.isNullOrEmpty(userInfoList)) {
                        for (ChartInfoRoleResponse userInfoResponse : userInfoList) {
                            NotifyUserResponse userResponse = new NotifyUserResponse();
                            userResponse.setId(userInfoResponse.getId());
                            userResponse.setNotifyUserName(responseUtils.getFullname(userInfoResponse.getUsername()));
                            userResponse.setUserEmail(userInfoResponse.getUsername());
                            notifyUserResponseList.add(userResponse);
                        }
                    }
                    if (!ValidationUtils.isNullOrEmpty(notifyUserResponseList)) {
                        ticketDefaultResponse.setNotifyUsers(notifyUserResponseList);
                    }
                }
                if (!ValidationUtils.isNullOrEmpty(proInstNotifyGroup)) {
                    for (BpmProInstNotifyGroup bpmProInstNotifyGroup : proInstNotifyGroup) {
                        long groupId;
                        if (bpmProInstNotifyGroup.getGroupId() != null) {
                            groupId = Long.parseLong(bpmProInstNotifyGroup.getGroupId());
                            listNotifyGroupId.add(groupId);
                        }
                    }
                    if (!ValidationUtils.isNullOrEmpty(listNotifyGroupId)) {
                        ticketDefaultResponse.setNotifyGroups(listNotifyGroupId);
                    }
                }

                if (!ValidationUtils.isNullOrEmpty(bpmProcInst.getLocationId())) {
                    List<Map<String, Object>> locationManagement = masterDataService.getLoadTemplateLocation(bpmProcInst.getLocationId());
                    LocationManagementResponse locationResponse = new LocationManagementResponse();
                    if (!ValidationUtils.isNullOrEmpty(locationManagement)) {
                        locationResponse.setId(bpmProcInst.getLocationId());
                        locationResponse.setLocationName(locationManagement.get(0).get("name").toString());
                        ticketDefaultResponse.setLocationManagement(locationResponse);
                    }
                }
                ticketDefaultResponse.setLinkProcInstId(bpmProcinstLinkManager.getProcinstLinkByBpmProcinstId(bpmProcInst.getTicketId()));

                if (!ValidationUtils.isNullOrEmpty(bpmProcInst.getPriorityId())) {
                    ticketDefaultResponse.setPriorityId(bpmProcInst.getPriorityId());
                }
                if (!ValidationUtils.isNullOrEmpty(bpmProcInst.getEmailNotification())) {
                    ticketDefaultResponse.setReceiveMail(bpmProcInst.getEmailNotification());
                }
                if (!ValidationUtils.isNullOrEmpty(bpmProcInst.getChartNodeName())) {
                    ticketDefaultResponse.setChartNodeName(bpmProcInst.getChartNodeName());
                }
                if (!ValidationUtils.isNullOrEmpty(bpmProcInst.getCompanyCode())) {
                    ticketDefaultResponse.setCompanyCode(bpmProcInst.getCompanyCode());
                }
                if (!ValidationUtils.isNullOrEmpty(bpmProcInst.getChartId())) {
                    ticketDefaultResponse.setChartId(bpmProcInst.getChartId());
                }

                if (!ValidationUtils.isNullOrEmpty(bpmProcInst.getChartNodeId())) {
                    ticketDefaultResponse.setChartNodeId(bpmProcInst.getChartNodeId());
                }

                if (!ValidationUtils.isNullOrEmpty(bpmProcInst.getChartNodeCode())) {
                    ticketDefaultResponse.setChartNodeCode(bpmProcInst.getChartNodeCode());
                }

                if (!ValidationUtils.isNullOrEmpty(bpmProcInst.getIsAssistant())) {
                    ticketDefaultResponse.setIsAssistant(bpmProcInst.getIsAssistant());
                }
                if (!ValidationUtils.isNullOrEmpty(bpmProcInst.getLegislativeId())) {
                    ticketDefaultResponse.setLegislativeId(bpmProcInst.getLegislativeId());
                }

            }
            return ticketDefaultResponse;

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public LocationManagementResponse getLocationByEmail(String account) {
        try {
            Long id = customerService.getLocationIdByUserName(account);
            LocationManagementDto locationManagement = locationManager.locationInfo(id);
            LocationManagementResponse locationResponse = new LocationManagementResponse();
            if (!ValidationUtils.isNullOrEmpty(locationManagement)) {
                locationResponse.setId(locationManagement.getId());
                locationResponse.setLocationName(locationManagement.getLocationName());
            }
            return locationResponse;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public List<String> getAllUserCreate() {
        return bpmProcInstRepository.getAllUserCreate();
    }

    public BpmProcInst findById(Long id) {
        return bpmProcInstRepository.getBpmProcInstByTicketId(id);
    }

    /**
     * Find tickets by list of service-id
     */
    public PagingResponse<Map<String, Object>> getTicketByServiceIds(List<Long> serviceIds, Integer
            pageNumber, Integer pageSize, Boolean isGetChildSpecialFlow) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (serviceIds != null && serviceIds.isEmpty()) {
            serviceIds = null; // set null to skip condition and get all
        }

        Pageable pageable = null;
        if (pageNumber != null && pageSize != null) {
            pageable = PageRequest.of(pageNumber, pageSize);
        }
        // get login account
        String userName = credentialHelper.getJWTPayload().getUsername();
        if (isGetChildSpecialFlow) {
            List<ChildResponse> childs = servicePackageRepository.findSpecialFlowChildDataByParentIds(serviceIds);
            if (!childs.isEmpty())
                serviceIds.addAll(childs.stream().map(ChildResponse::getId).toList());
        }
        Page<Tuple> queryResult = bpmProcInstRepository.getAllByServiceIds(serviceIds, ProcInstConstants.TabStatus.COMPLETE, userName, pageable);
        List<Tuple> tupleList = new ArrayList<>();

        if (queryResult != null && !queryResult.isEmpty()) {
            tupleList.addAll(queryResult.getContent());
        }

        if (!ValidationUtils.isNullOrEmpty(tupleList)) {
            tupleList.forEach(e -> {
                Map<String, Object> itemMap = new HashMap<>();
                itemMap.put("ticketId", QueryUtils.getValueFromTuple(e, "ticketId", Long.class));
                itemMap.put("title", QueryUtils.getValueFromTuple(e, "title", String.class));
                itemMap.put("requestCode", QueryUtils.getValueFromTuple(e, "requestCode", String.class));
                itemMap.put("ticketAssign", QueryUtils.getValueFromTuple(e, "ticketAssign", Long.class));
                result.add(itemMap);
            });

            return PagingResponse.<Map<String, Object>>builder()
                    .content(result)
                    .pageNumber(queryResult.getNumber())
                    .pageSize(queryResult.getSize())
                    .numberOfElements(queryResult.getNumberOfElements())
                    .totalElements(queryResult.getTotalElements())
                    .totalPages(queryResult.getTotalPages())
                    .build();
        }

        return null;
    }

    /**
     * Save one
     */
    public BpmProcInst save(BpmProcInst bpmProcInst) {
        bpmProcInstRepository.save(bpmProcInst);
        return bpmProcInst;
    }

    public void autoCancel(AutoCancelRequest autoCancelRequest) {
        try {
            List<ListTimeExpectRequest> listTimeExpectRequests = new ArrayList<>();
            List<TicketAutoLog> listTiketAuto = new ArrayList<>();
            Pageable pageable = PageRequest.of(0, 100);
            autoCancelRequest.getIds().forEach(id -> {
                Page<TimeExpectDTO> page = bpmProcInstRepository.ticket(id, pageable);
                if (ValidationUtils.isNullOrEmpty(page)) {
                    TicketAutoLog ticketAutoLog = new TicketAutoLog();
                    ticketAutoLog.setType(autoCancelRequest.getStatus());
                    ticketAutoLog.setProcessTime(LocalDateTime.now());
                    ticketAutoLog.setState(AutoTicketConstant.State.SUCCESS.code);
                    listTiketAuto.add(ticketAutoLog);
                }
                page.getContent().forEach(data -> {
                    try {
                        ListTimeExpectRequest listTimeExpectRequest = new ListTimeExpectRequest();
                        listTimeExpectRequest.setTicketId(data.getTicketId());
                        listTimeExpectRequest.setProcInstId(data.getTicketProcInstId());
                        listTimeExpectRequest.setUser(data.getCreatedUser());
                        if (autoCancelRequest.getStatus().equalsIgnoreCase(ProcInstConstants.Status.CANCEL.code)) {
                            listTimeExpectRequest.setDate(data.getRuTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                            if (ValidationUtils.isNullOrEmpty(data.getAutoCancel())) {
                                return;
                            }
                            listTimeExpectRequest.setTime(data.getAutoCancel() * 60);
                        } else if (autoCancelRequest.getStatus().equalsIgnoreCase(ProcInstConstants.Status.CLOSED.code)) {
                            if (ValidationUtils.isNullOrEmpty(data.getAutoClose())) {
                                return;
                            }
                            listTimeExpectRequest.setTime(data.getAutoClose() * 60);
                            if (ValidationUtils.isNullOrEmpty(data.getTicketFinishTime())) {
                                return;
                            }
                            listTimeExpectRequest.setDate(data.getTicketFinishTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                        }
                        listTimeExpectRequests.add(listTimeExpectRequest);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        TicketAutoLog ticketAutoLog = new TicketAutoLog();
                        ticketAutoLog.setTicketId(id);
                        ticketAutoLog.setType(autoCancelRequest.getStatus());
                        ticketAutoLog.setProcessTime(LocalDateTime.now());
                        ticketAutoLog.setState(AutoTicketConstant.State.FAIL.code);
                        ticketAutoLog.setLog(e.getMessage());
                        listTiketAuto.add(ticketAutoLog);
                    }

                });
            });
            List<Map<String, Object>> mapList = customerService.listTimeToFeedback(listTimeExpectRequests);
            mapList.forEach(stringObjectMap -> {
                try {
                    TicketAutoLog ticketAutoLog = new TicketAutoLog();
                    LocalDateTime timeExpect = stringToLocalDateTime(stringObjectMap.get("timeRes").toString(), FORMAT_DATE_TIME_2);
                    if (autoCancelRequest.getStatus().equalsIgnoreCase(ProcInstConstants.Status.CANCEL.code)) {
                        if (LocalDateTime.now().isAfter(timeExpect)) {
                            cancelTicket(Long.parseLong(stringObjectMap.get("ticketId").toString()),
                                    stringObjectMap.get("procInstId").toString(), "Hệ thống",
                                    "Hệ thống tự động hủy do quá thời gian cập nhật Phiếu yêu cầu", null, new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
                        }
                    } else if (autoCancelRequest.getStatus().equalsIgnoreCase(ProcInstConstants.Status.CLOSED.code)) {
                        if (!ValidationUtils.isNullOrEmpty(timeExpect)) {
                            TicketRequest request = new TicketRequest();
                            request.setProcInstId(stringObjectMap.get("ticketId").toString());
                            request.setRating(5.0);
                            request.setComment(AutoTicketConstant.Type.CLOSED.code);
                            request.setIsAutoRequest(true);
                            request.setActionUser("Hệ thống");

                            if (!ValidationUtils.isNullOrEmpty(timeExpect)) {
                                if (LocalDateTime.now().isAfter(timeExpect)) {
                                    closeTicket(request, null);
                                }
                            }
                        }
                    }
                    ticketAutoLog.setTicketId(Long.parseLong(stringObjectMap.get("ticketId").toString()));
                    ticketAutoLog.setType(autoCancelRequest.getStatus());
                    ticketAutoLog.setProcessTime(LocalDateTime.now());
                    ticketAutoLog.setState(AutoTicketConstant.State.SUCCESS.code);
                    listTiketAuto.add(ticketAutoLog);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    TicketAutoLog ticketAutoLog = new TicketAutoLog();
                    ticketAutoLog.setTicketId(Long.parseLong(stringObjectMap.get("ticketId").toString()));
                    ticketAutoLog.setType(autoCancelRequest.getStatus());
                    ticketAutoLog.setProcessTime(LocalDateTime.now());
                    ticketAutoLog.setState(AutoTicketConstant.State.FAIL.code);
                    ticketAutoLog.setLog(e.getMessage());
                    listTiketAuto.add(ticketAutoLog);
                }

            });
            ticketAutoLogService.saveAll(listTiketAuto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public List<Long> getListTicketId(String status) {
        try {
            if (status.equalsIgnoreCase(ProcInstConstants.Status.CANCEL.code)) {
                String[] statusCancel = new String[]{ProcInstConstants.Status.DELETED_BY_RU.code};
                List<BpmProcInst> listBpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketStatusIn(statusCancel);
                return listBpmProcInst.stream().map(BpmProcInst::getTicketId).collect(Collectors.toList());
            } else {
                String[] statusClose = new String[]{ProcInstConstants.Status.COMPLETED.code};
                List<BpmProcInst> listBpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketStatusIn(statusClose);
                return listBpmProcInst.stream().map(BpmProcInst::getTicketId).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public void autoCloseTicket() {
        try {
            List<ListTimeExpectRequest> listTimeExpectRequests = new ArrayList<>();
            String[] status = new String[]{ProcInstConstants.Status.COMPLETED.code};
            List<BpmProcInst> listBpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketStatusIn(status);
            if (!ValidationUtils.isNullOrEmpty(listBpmProcInst)) {
                for (BpmProcInst bpmProcInst : listBpmProcInst) {
                    ListTimeExpectRequest listTimeExpectRequest = new ListTimeExpectRequest();
                    ServicePackage servicePackage = servicePackageRepository.getServicePackageById(bpmProcInst.getServiceId());
                    BpmProcdef procdef = bpmProcdefRepository.getBpmProcDefById(servicePackage.getProcessId());
                    listTimeExpectRequest.setTicketId(bpmProcInst.getTicketId());
                    listTimeExpectRequest.setProcInstId(bpmProcInst.getTicketProcInstId());
                    listTimeExpectRequest.setDate(bpmProcInst.getTicketCreatedTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                    listTimeExpectRequest.setUser(bpmProcInst.getCreatedUser());
                    listTimeExpectRequest.setTime(procdef.getAutoClose() * 60);
                    listTimeExpectRequests.add(listTimeExpectRequest);
                }
                List<TimeExpectResponse> listTime = customerService.listTimeExpect(listTimeExpectRequests);
                if (!ValidationUtils.isNullOrEmpty(listTime)) {
                    for (TimeExpectResponse time : listTime) {
                        TicketRequest request = new TicketRequest();
                        request.setProcInstId(String.valueOf(time.getTicketId()));
                        request.setRating(5.0);
                        request.setComment(AutoTicketConstant.Type.CLOSED.code);
                        request.setIsAutoRequest(true);
                        request.setActionUser("Hệ thống");

                        if (!ValidationUtils.isNullOrEmpty(time.getTimeExpect())) {
                            if (LocalDateTime.now().isAfter(time.getTimeExpect())) {
                                closeTicket(request, null);
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * Recall ticket
     *
     * @param ticketId Equivalent bpm_procinst.id
     */
    public BpmProcInst recall(Long ticketId, TicketRecallRequest request) {
        // find bpm_procinst by ticketId
        BpmProcInst bpmProcInst = findById(ticketId);
        if (bpmProcInst == null) {
            throw new RuntimeException(common.getMessage("message.ticket.not-exists"));
        }

        String currentStatus = bpmProcInst.getTicketStatus();
        List<String> checkStatus = Arrays.asList(ProcInstConstants.Status.ADDITIONAL_REQUEST.code, ProcInstConstants.Status.OPENED.code, ProcInstConstants.Status.PROCESSING.code);
        if (!checkStatus.contains(currentStatus.toUpperCase())) {
            throw new RuntimeException(common.getMessage("message.ticket.status-change"));
        }

        String username = credentialHelper.getJWTPayload().getUsername();

        /* BEGIN handle call action api on beginning */
//        ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(
//                TaskActionConstants.Action.RECALLING.code,
//                bpmProcInst.getTicketProcDefId(),
//                bpmProcInst.getTicketStartActId(),
//                bpmProcInst.getTicketProcInstId(),
//                actionApiService.createVariablesMap(bpmProcInst, actionApiService.getTicketVariables(bpmProcInst.getTicketProcInstId())));

        //--- Begin: Insert bpm_procinst_recall ---//
        String attachFiles = null;
        String attachFilesName = null;
        String attachFilesSize = null;
        if (request != null) {
            String reason = request.getReason();
            if (!ValidationUtils.isNullOrEmpty(request.getAttachFiles())) {
                attachFiles = String.join(",", request.getAttachFiles());
                attachFilesName = String.join(",", request.getAttachFilesName());
                attachFilesSize = String.join(",", request.getAttachFilesSize());
            }

            BpmProcinstRecall bpmProcinstRecall = new BpmProcinstRecall();
            bpmProcinstRecall.setBpmProcinstId(ticketId);
            bpmProcinstRecall.setProcInstId(bpmProcInst.getTicketProcInstId());
            bpmProcinstRecall.setRecallUser(username);
            bpmProcinstRecall.setRecallTime(LocalDateTime.now());
            bpmProcinstRecall.setReason(reason);
            bpmProcinstRecall.setAttachFile(attachFiles);
            bpmProcinstRecall.setAttachFileName(attachFilesName);
            bpmProcinstRecall.setAttachFileSize(attachFilesSize);
            bpmProcinstRecallService.save(bpmProcinstRecall);
        }
        //--- End: Insert bpm_procinst_recall ---//

        //--- Begin: Update status ---//
        bpmProcInst.setTicketStatus(ProcInstConstants.Status.RECALLING.code);
        bpmProcInst.setTicketEditTime(LocalDateTime.now());
        save(bpmProcInst);
//        reportByGroupService.createReportByGroup(bpmProcInst.getTicketId());

        //--- End: Update status ---//

        List<BpmTask> bpmTasks = bpmTaskRepository.getAllTasksByStatus(Collections.singletonList(bpmProcInst.getTicketProcInstId()), TaskConstants.TabStatus.PROCESSING);
        if (bpmTasks != null && !bpmTasks.isEmpty()) {
            List<String> taskDefKeys = bpmTasks.stream().map(BpmTask::getTaskDefKey).distinct().toList();
            for (String taskDefKey : taskDefKeys) {
                Map<String, VariableValueDto> variablesNotifi = new HashMap<>();
                VariableValueDto variableDto = new VariableValueDto();
                variableDto.setType("String");
                variableDto.setValue(request.getReason());
                variablesNotifi.put("txt_LyDoThuHoi", variableDto);
                // Lấy assignee theo bpm_task_user
                List<String> lstUserTasks = bpmTaskUserService.getAllUserName(bpmProcInst.getTicketProcInstId(), taskDefKey);
                // Check nếu có ủy quyền thì add orgAssignee
                List<String> lstOrgAssignee = changeAssigneeHistoryRepository.getListOrgAssigneeByProcInstIdAndTaskDefKeyAndToAssigneeIn(bpmProcInst.getTicketProcInstId(), taskDefKey, lstUserTasks);

                List<String> lstCustomerEmails = Stream.concat(lstUserTasks.stream(), lstOrgAssignee.stream()).collect(Collectors.toList());

                try {
                    //Thông báo cho user liên quan
                    NotificationUser payload = new NotificationUser();
                    payload.setBpmProcdefId(null);
                    payload.setNextTaskDefKey(taskDefKey);
                    payload.setVariables(variablesNotifi);
                    payload.setTicketId(bpmProcInst.getTicketId());
                    payload.setIsGetOldVariable(true);
                    payload.setLstCustomerEmails(lstCustomerEmails);
                    payload.setActionCode(ProcInstConstants.Notifications.RECALLING.code);
                    payload.setEmailExe(credentialHelper.getJWTPayload().getUsername());
                    kafkaTemplate.send(notificationUser, payload);
//                    bpmProcdefNotificationService.addNotificationsByConfig(null, taskDefKey, variablesNotifi,
//                            bpmProcInst.getTicketId(), true, lstCustomerEmails, ProcInstConstants.Notifications.RECALLING.code);

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }


        // save history
        List<BpmHistory> historyList = new ArrayList<>();
        // Add task nhận yêu cầu thu hồi
        if (!ValidationUtils.isNullOrEmpty(bpmTasks)) {
            List<String> toTaskKeys = bpmTasks.stream().map(BpmTask::getTaskDefKey).distinct().toList();
            if (!ValidationUtils.isNullOrEmpty(toTaskKeys)) {
                String finalAttachFiles = attachFiles;
                String finalAttachFilesName = attachFilesName;
                String finalAttachFilesSize = attachFilesSize;
                toTaskKeys.forEach(toTaskKey -> {
                    BpmHistory history = new BpmHistory();
                    history.setTicketId(ticketId);
                    history.setProcInstId(bpmProcInst.getTicketProcInstId());
                    history.setFromTaskKey(bpmProcInst.getTicketStartActId());
                    history.setFromTask(bpmProcInst.getTicketStartActId());
                    history.setToTaskKey(toTaskKey);
                    history.setAction(TaskConstants.HistoryAction.RECALLING.code);
                    history.setActionUser(username);
                    history.setCreatedTime(new Date());
                    history.setNote(request.getReason());
                    history.setAttachFiles(finalAttachFiles);
                    history.setAttachFilesName(finalAttachFilesName);
                    history.setAttachFilesSize(finalAttachFilesSize);

                    // get action_user_info
                    List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(username);
                    if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
                        Map<String, Object> actionUserInfo = new HashMap<>();
                        String userTitle = lstUserTitle.stream()
                                .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                                .map(title -> {
                                    String strTitle = StringUtil.nvl(title.getTitle(), "");
                                    int concurrently = title.getConcurrently();
                                    return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                                })
                                .collect(Collectors.joining(" "));
                        actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
                        actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
                        history.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
                    }

                    historyList.add(history);
                });
            }
        }

        bpmHistoryManager.saveAll(historyList);

        /* BEGIN handle call action api at the end */
//        actionApiService.endHandleActionApi(
//                actionApiContext,
//                actionApiService.createVariablesMap(
//                        bpmProcInst,
//                        actionApiService.getTicketVariables(bpmProcInst.getTicketProcInstId())
//                ));
        return bpmProcInst;
    }

    public List<HistoryFileResponse> getAllFileByTicketId(Long ticketId) {
        BpmProcInst bpmProcInst = bpmProcInstRepository.findById(ticketId).orElse(null);
        List<BpmVariables> bpmVariables = bpmVariablesRepository.findAllByProcInstIdAndTypeOrderByIdDesc(bpmProcInst != null ? bpmProcInst.getTicketProcInstId() : null, "FILE");
        List<BpmHistory> bpmHistories = bpmHistoryRepository.findAllByTicketIdAndAttachFilesNotNullAndActionNotInOrderByIdDesc(ticketId, Arrays.asList("AFFECTED_BY_RU", "GET_REQUEST_UPDATE"));
        List<Map<String, Object>> bpmDiscussionFiles = bpmDiscussionFileRepository.findHistoryFileByTicketId(ticketId);
        Set<HistoryFileResponse> listResponse = new HashSet<>();
        String startAction = bpmProcInst != null ? bpmProcInst.getTicketStartActId() : null;
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
        for (BpmVariables variables : bpmVariables) {
            HistoryFileResponse response = new HistoryFileResponse();
            if (variables.getAdditionalVal() != null) {
                Map<String, Object> additionalVal = ObjectUtils.toObject(variables.getAdditionalVal(), HashMap.class);
                if (additionalVal.get("data") instanceof HashMap) {
                    Map<String, String> fileRes = (Map<String, String>) additionalVal.get("data");
                    response.setUploadTime(fileRes.get("uploadTime"));
                    response.setCreatedUser(fileRes.get("createUser"));
                    response.setIdHistory(fileRes.get("idHistory"));
                    response.setFileType(fileRes.get("fileType"));
                    response.setDescription(fileRes.get("description"));
                    response.setFilename(fileRes.get("filename"));
                    response.setFileSize(String.valueOf(fileRes.get("fileSize")));
                    response.setDisplayName(fileRes.get("displayName") != null ? fileRes.get("displayName") : fileRes.get("filename"));
                    String stepName = fileRes.get("name");
                    Boolean isCreateUserUpLoad = bpmProcInst.getTicketStartUserId().equals(fileRes.get("createUser"));
                    Boolean isFileStartStep = startAction != null && stepName != null && stepName.startsWith(startAction);
                    response.setIsStartStep(isCreateUserUpLoad || isFileStartStep);
                }
            }
            response.setDownloadUrl(variables.getDownloadUrl());
//            response.setTaskDefKey(variables.getName());
            listResponse.add(response);
        }

        for (BpmHistory bpmHistory : bpmHistories) {
            List<String> fileList = List.of(bpmHistory.getAttachFiles().split(","));
            List<String> fileListName = List.of(bpmHistory.getAttachFilesName().split(","));
            List<String> fileListSize = bpmHistory.getAttachFilesSize() != null ? List.of(bpmHistory.getAttachFilesSize().split(",")) : new ArrayList<>();
            for (int idx = 0; idx < fileList.size(); idx++) {
                String i = fileList.get(idx);
                String size = !fileListSize.isEmpty() && idx < fileListSize.size() ? fileListSize.get(idx) : "";
                HistoryFileResponse response = new HistoryFileResponse();
                response.setDescription(bpmHistory.getNote());
                response.setFilename(i);
                response.setDisplayName(!fileListName.isEmpty() && idx < fileListName.size() ? fileListName.get(idx) : i);
                response.setFileType("history");
                response.setCreatedUser(bpmHistory.getActionUser());
                response.setUploadTime(bpmHistory.getCreatedTime() != null ? dateTimeFormatter.format(((Timestamp) bpmHistory.getCreatedTime()).toLocalDateTime()) : null);
                response.setDownloadUrl(i);
                response.setFileSize(size);
                response.setIsStartStep(false);
                listResponse.add(response);
            }
        }

        for (Map<String, Object> discussion : bpmDiscussionFiles) {
            HistoryFileResponse response = new HistoryFileResponse();
            response.setDescription("Yêu cầu bổ sung");
            response.setFilename(discussion.get("fileName").toString());
            response.setDisplayName(discussion.get("displayName") != null ? discussion.get("displayName").toString() : discussion.get("fileName").toString());
            response.setFileType("history");
            response.setCreatedUser(discussion.get("createdUser").toString());
            response.setUploadTime(discussion.get("createdDate") != null ? dateTimeFormatter.format(((Timestamp) discussion.get("createdDate")).toLocalDateTime()) : null);
            response.setDownloadUrl(discussion.get("fileName").toString());
            boolean isCreateUserUpLoad = bpmProcInst.getTicketStartUserId().equals(response.getCreatedUser());
            response.setIsStartStep(isCreateUserUpLoad);
            response.setFileSize(String.valueOf(discussion.get("fileSize")));
            listResponse.add(response);
        }

        return listResponse.stream().distinct().collect(Collectors.toList());
    }

    public List<TaskInfoResponse> getAllUserTaskInfo(Long ticketId) {
        List<TaskInfoResponse> lstResponse;

        BpmProcInst bpmProcInst = bpmProcInstRepository.getBpmProcInstByTicketId(ticketId);
        List<BpmTask> lstTask = bpmTaskRepository.getBpmTaskByTaskProcInstId(bpmProcInst.getTicketProcInstId());

        // List data bàn giao
        List<AuthorityManagement> lstAuthority = authorityManagementRepository.findByTicketIdAndTypeOrderByCreateAtDesc(ticketId, 3);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");

        // get flow info with assignee from camunda
        WorkFlowRequest workFlowRequest = new WorkFlowRequest();
        workFlowRequest.setProcDefId(bpmProcInst.getTicketProcDefId());
        workFlowRequest.setProcInstId(bpmProcInst.getTicketProcInstId());
        List<SproFlow> workFlow = sproService.getWorkFlow(workFlowRequest);

        // sort list task
        List<SproFlowNode> nodes = workFlow.stream()
                .flatMap(e -> e.getNodes().stream().filter(node -> node.getType().equals("userTask") || node.getType().equalsIgnoreCase("startEvent")))
                .peek(e -> {
                    if (e.getType().equalsIgnoreCase("startEvent")) {
                        e.setAssignee(bpmProcInst.getTicketStartUserId());
                    }
                })
                .sorted((o1, o2) -> {
                    if (o1.getPosition() == o2.getPosition() && !o1.getType().equalsIgnoreCase("startEvent")) {
                        return o1.getId().compareTo(o2.getId());
                    }
                    return o1.getPosition();
                })
                .toList();

        // list assignee camunda
        List<String> lstAssignee = nodes.stream()
                .filter(json -> json.getAssignee() != null)
                .map(json -> json.getAssignee().replaceAll("\\[\"|\"\\]|\\\\|\"", ""))
                .map(json -> Arrays.stream(json.split(",")).map(String::trim))
                .flatMap(Stream::distinct)
                .distinct()
                .collect(Collectors.toList());

        // map list task info
        List<TaskInfoDto> lstTaskInfoDto = lstTask.stream().map(task -> {
            TaskInfoDto taskInfoDto = new TaskInfoDto();
            taskInfoDto.setTaskDefKey(task.getTaskDefKey());
            taskInfoDto.setTaskStatus(task.getTaskStatus());
            taskInfoDto.setTaskFinishedTime(task.getTaskFinishedTime());
            taskInfoDto.setTaskId(task.getId());
            taskInfoDto.setActionUser(task.getActionUser());
            taskInfoDto.setTaskCreatedTime(task.getTaskCreatedTime());

            // check task UQ - bỏ qua đồng duyệt
            if (task.getAssignType() == null || !task.getAssignType() || task.getTaskAssignee() == null) {
                taskInfoDto.setAssignTask(false);
                taskInfoDto.setOrgAssignee(task.getTaskAssignee());
            } else {
                // Task có UQ
                taskInfoDto.setAssignTask(true);
                List<ChangeAssigneeHistory> changeAssigneeHistories = changeAssigneeHistoryService.getLatestChangeByToAssignee(bpmProcInst.getTicketId(), task.getTaskId());
                if (!ValidationUtils.isNullOrEmpty(changeAssigneeHistories)) {
                    ChangeAssigneeHistory changeAssigneeHistory = changeAssigneeHistories.get(0);
                    Integer typeAssignee = changeAssigneeHistory.getType();
                    taskInfoDto.setOrgAssignee(changeAssigneeHistory.getOrgAssignee());
                    taskInfoDto.setToAssignee(changeAssigneeHistory.getToAssignee());

                    if (typeAssignee == 1) {
                        int effect = changeAssigneeHistory.getAssignManagement().getEffect();

                        // check expire assignment - UQ tờ trình
                        if (effect != 1 && !changeAssigneeHistory.getOrgAssignee().equalsIgnoreCase(task.getTaskAssignee())
                                && assignManager.isAssignmentExpired(changeAssigneeHistory.getOrgAssignee(), task.getTaskAssignee(), bpmProcInst.getServiceId())) {
                            taskInfoDto.setAssignTask(false);
                            taskInfoDto.setOrgAssignee(changeAssigneeHistory.getOrgAssignee());
                        }
                    }

                }
            }

            return taskInfoDto;
        }).toList();

        // list user được ủy quyền
        List<String> lstToAssignee = lstTaskInfoDto.stream().map(TaskInfoDto::getToAssignee).filter(Objects::nonNull).toList();

        if (!ValidationUtils.isNullOrEmpty(lstToAssignee)) {
            lstAssignee.addAll(lstToAssignee);
        }

        if (!ValidationUtils.isNullOrEmpty(lstAuthority)) {
            lstAssignee.addAll(lstAuthority.stream().map(AuthorityManagement::getToAccount).toList());
        }

        // info user
        List<AccountModel> lstUserInfo = customerService.getUserTaskInfoByUserNames(lstAssignee.stream().distinct().collect(Collectors.toList()));

        lstResponse = nodes.stream().map(node -> {
            TaskInfoResponse response = new TaskInfoResponse();
            response.setTaskName(node.getName());
            response.setTaskDefKey(node.getId());
            response.setTaskType(getTaskType(node));
            response.setFormKey(node.getFormKey() != null ? node.getFormKey() : "");
            List<String> lstAssigneeTask = new ArrayList<>();
            if (node.getAssignee() != null) {
                lstAssigneeTask = Arrays.stream(node.getAssignee().replaceAll("\\[\"|\"\\]|\\\\|\"", "").split(",")).map(String::trim).collect(Collectors.toList());
            }

            // add thêm user được UQ (nếu có)
            List<String> lstToAssingeeTask = lstTaskInfoDto.stream()
                    .filter(taskInfoDto -> taskInfoDto.getTaskDefKey().equalsIgnoreCase(node.getId()) && taskInfoDto.getToAssignee() != null)
                    .map(TaskInfoDto::getToAssignee)
                    .distinct()
                    .toList();
            if (!ValidationUtils.isNullOrEmpty(lstToAssingeeTask)) {
                lstAssigneeTask.addAll(lstToAssingeeTask);
            }

            List<AssigneeInfo> lstAssigneeInfo = new ArrayList<>();

            // check task candidate completed
            if (node.isCandidateTask()) {
                BpmTask candidateTask = lstTask.stream()
                        .filter(task -> task.getTaskDefKey().equalsIgnoreCase(node.getId()) && task.getTaskStatus().equalsIgnoreCase(TaskConstants.Status.COMPLETED.code))
                        .findFirst().orElse(null);
                if (candidateTask != null) {
                    AccountModel userInfo = lstUserInfo.stream()
                            .filter(e -> e.getUsername().equalsIgnoreCase(candidateTask.getTaskAssignee()))
                            .findFirst().orElse(null);
                    AssigneeInfo assigneeCandidate = new AssigneeInfo();
                    assigneeCandidate.setUsername(candidateTask.getTaskAssignee());
                    assigneeCandidate.setTaskStatus(candidateTask.getTaskStatus());
                    assigneeCandidate.setCompletedTime(formatter.format(candidateTask.getTaskFinishedTime()));
                    assigneeCandidate.setCreatedTime(candidateTask.getTaskCreatedTime());
                    assigneeCandidate.setFinishTime(candidateTask.getTaskFinishedTime());
                    if (userInfo != null) {
                        assigneeCandidate.setFullName(userInfo.getLastname() + " " + userInfo.getFirstname());
                        assigneeCandidate.setTitle(userInfo.getFinalTitle());
                        assigneeCandidate.setCompanyCode(userInfo.getCompanyCode());
                        assigneeCandidate.setChartShortName(userInfo.getChartShortName());
                        assigneeCandidate.setChartName(userInfo.getChartName());
                    }

                    lstAssigneeInfo.add(assigneeCandidate);
                    response.setLstAssigneeInfo(lstAssigneeInfo);

                    return response;
                }
            }

            lstAssigneeInfo = lstAssigneeTask.stream().distinct().map(assignee -> {
                String fullname = null;
                AssigneeInfo assigneeInfo = new AssigneeInfo();
                String authorAssignee = null;

                // case bàn giao công việc
                if (!ValidationUtils.isNullOrEmpty(lstAuthority)) {
                    AuthorityManagement authorTask = lstAuthority.stream()
                            .filter(e -> e.getTaskDefKey().equals(node.getId()) && e.getFromAccount().equals(assignee))
                            .findFirst().orElse(null);
                    if (authorTask != null) {
                        authorAssignee = authorTask.getToAccount();
                    }
                }

                assigneeInfo.setUsername(authorAssignee != null ? authorAssignee : assignee);

                AccountModel userInfo = lstUserInfo.stream()
                        .filter(e -> e.getUsername().equalsIgnoreCase(assigneeInfo.getUsername()))
                        .findFirst().orElse(null);
                if (userInfo != null) {
                    assigneeInfo.setTitle(userInfo.getFinalTitle());
                    assigneeInfo.setCompanyCode(userInfo.getCompanyCode());
                    assigneeInfo.setChartShortName(userInfo.getChartShortName());
                    assigneeInfo.setChartName(userInfo.getChartName());
                    fullname = userInfo.getLastname() + " " + userInfo.getFirstname();
                }

                TaskInfoDto taskInfo = lstTaskInfoDto.stream()
                        .filter(task -> task.getTaskDefKey().equalsIgnoreCase(node.getId())
                                && (node.isCandidateTask()
                                || (task.getOrgAssignee() != null && task.getOrgAssignee().equalsIgnoreCase(assigneeInfo.getUsername()))
                                || (task.getToAssignee() != null && task.getToAssignee().equalsIgnoreCase(assigneeInfo.getUsername()))))
                        .findFirst().orElse(null);

                if (taskInfo != null) {
                    assigneeInfo.setTaskStatus(taskInfo.getTaskStatus());
                    assigneeInfo.setCompletedTime(taskInfo.getTaskFinishedTime() != null ? formatter.format(taskInfo.getTaskFinishedTime()) : null);
                    assigneeInfo.setCreatedTime(taskInfo.getTaskCreatedTime());
                    assigneeInfo.setFinishTime(taskInfo.getTaskFinishedTime());
                    assigneeInfo.setTaskId(taskInfo.getTaskId());

                    if (fullname != null && taskInfo.getAssignTask()) {
                        if (taskInfo.getOrgAssignee().equalsIgnoreCase(assignee)) {
                            fullname = fullname + " - UQ";
                        } else if (taskInfo.getToAssignee().equalsIgnoreCase(assignee)) {
                            fullname = fullname + " - TUQ";
                        }
                    }
                }

                assigneeInfo.setFullName(fullname);

                // add thêm ngày tạo phiếu node start
                if (response.getTaskType() != null && response.getTaskType().equalsIgnoreCase("startEvent")) {
                    assigneeInfo.setCompletedTime(formatter.format(bpmProcInst.getTicketCreatedTime()));
                }

                return assigneeInfo;
            }).sorted(Comparator.comparing(AssigneeInfo::getTaskId, Comparator.nullsLast(Long::compareTo))).collect(Collectors.toList());
            response.setLstAssigneeInfo(lstAssigneeInfo);

            return response;
        }).collect(Collectors.toList());

        return lstResponse;
    }

    public Map<String, Object> getAllAssigneeForDrawFlow(Long ticketId) {
        BpmProcInst bpmProcInst = bpmProcInstRepository.getBpmProcInstByTicketId(ticketId);
        List<BpmTask> lstTask = bpmTaskRepository.getBpmTaskByTaskProcInstId(bpmProcInst.getTicketProcInstId());
        // get flow info with assignee from camunda
        WorkFlowRequest workFlowRequest = new WorkFlowRequest();
        workFlowRequest.setProcDefId(bpmProcInst.getTicketProcDefId());
        workFlowRequest.setProcInstId(bpmProcInst.getTicketProcInstId());
        List<SproFlow> workFlow = sproService.getWorkFlow(workFlowRequest);

        Map<String, Object> response = new HashMap<>();

        List<SproFlowNode> nodes = workFlow.stream()
                .flatMap(e -> e.getNodes().stream().filter(node -> node.getType().equals("userTask")))
                .toList();

        // map list task info
        List<TaskInfoDto> lstTaskInfoDto = lstTask.stream().map(task -> {
            TaskInfoDto taskInfoDto = new TaskInfoDto();
            taskInfoDto.setTaskDefKey(task.getTaskDefKey());
            taskInfoDto.setTaskStatus(task.getTaskStatus());
            taskInfoDto.setTaskFinishedTime(task.getTaskFinishedTime());
            taskInfoDto.setTaskId(task.getId());
            taskInfoDto.setActionUser(task.getActionUser());

            // check task UQ - bỏ qua đồng duyệt
            if (task.getAssignType() == null || !task.getAssignType() || task.getTaskAssignee() == null) {
                taskInfoDto.setAssignTask(false);
                taskInfoDto.setOrgAssignee(task.getTaskAssignee());
            } else {
                // Task có UQ
                taskInfoDto.setAssignTask(true);
                List<ChangeAssigneeHistory> changeAssigneeHistories = changeAssigneeHistoryService.getLatestChangeByToAssignee(bpmProcInst.getTicketId(), task.getTaskId());
                if (!ValidationUtils.isNullOrEmpty(changeAssigneeHistories)) {
                    ChangeAssigneeHistory changeAssigneeHistory = changeAssigneeHistories.get(0);
                    Integer typeAssignee = changeAssigneeHistory.getType();
                    taskInfoDto.setOrgAssignee(changeAssigneeHistory.getOrgAssignee());
                    taskInfoDto.setToAssignee(changeAssigneeHistory.getToAssignee());

                    if (typeAssignee == 1) {
                        int effect = changeAssigneeHistory.getAssignManagement().getEffect();

                        // check expire assignment - UQ tờ trình
                        if (effect != 1 && !changeAssigneeHistory.getOrgAssignee().equalsIgnoreCase(task.getTaskAssignee())
                                && assignManager.isAssignmentExpired(changeAssigneeHistory.getOrgAssignee(), task.getTaskAssignee(), bpmProcInst.getServiceId())) {
                            taskInfoDto.setAssignTask(false);
                            taskInfoDto.setOrgAssignee(changeAssigneeHistory.getOrgAssignee());
                        }
                    }

                }
            }

            return taskInfoDto;
        }).toList();

        for (SproFlowNode node : nodes) {
            if (!ValidationUtils.isNullOrEmpty(node.getAssignee())) {
                List<String> lstAssignee = List.of(node.getAssignee().replaceAll("\\[\"|\"\\]|\\\\|\"", "").split(","));
                if (!lstTaskInfoDto.isEmpty()) {
                    lstAssignee = lstAssignee.stream()
                            .map(assignee -> {
                                TaskInfoDto taskInfo = lstTaskInfoDto.stream()
                                        .filter(task -> task.getTaskDefKey().equalsIgnoreCase(node.getId())
                                                && task.getOrgAssignee() != null && task.getOrgAssignee().equalsIgnoreCase(assignee.trim()))
                                        .findFirst().orElse(null);
                                if (taskInfo != null && taskInfo.getToAssignee() != null) {
                                    return taskInfo.getToAssignee();
                                } else {
                                    return assignee.trim();
                                }
                            })
                            .collect(Collectors.toList());
                }
                response.put(node.getId(), lstAssignee);
            }
        }

        return response;
    }

    public List<String> getAllListUserNameUserTask(Long ticketId) {
        BpmProcInst bpmProcInst = bpmProcInstRepository.getBpmProcInstByTicketId(ticketId);
        List<String> result = new ArrayList<>();
        // get flow info with assignee from camunda
        WorkFlowRequest workFlowRequest = new WorkFlowRequest();
        workFlowRequest.setProcDefId(bpmProcInst.getTicketProcDefId());
        workFlowRequest.setProcInstId(bpmProcInst.getTicketProcInstId());
        List<SproFlow> workFlow = sproService.getWorkFlow(workFlowRequest);
        if (!ValidationUtils.isNullOrEmpty(workFlow) && !workFlow.isEmpty()) {
            workFlow.forEach(i -> {
                i.getNodes().forEach(j -> {
                    if (j != null && j.getAssignee() != null) {
                        try {
                            JSONArray jsonArray = new JSONArray(j.getAssignee());
                            for (int z = 0; z < jsonArray.length(); z++) {
                                result.add(jsonArray.getString(z));
                            }
                        } catch (JSONException e) {
                            result.add(j.getAssignee());
                        }

                    }
                });
            });
        }


        return result;
    }


    private String getTaskType(SproFlowNode node) {
        if (node.getType().equalsIgnoreCase("startEvent")) {
            return node.getType();
        }
        String type = "normalTask";
        if (node.isCandidateTask()) {
            type = "candidateTask";
        }
        if (node.isMultiInstance()) {
            type = node.isSequential() ? "sequentialTask" : "multiInstanceTask";
        }
        return type;
    }

    public Map<String, Integer> getBpmProcinstStatisticByUser() {
        String username;
        try {
            username = credentialHelper.getJWTPayload().getUsername();
        } catch (Exception e) {
            username = appSuperAdminAccount;
        }
        String type = String.valueOf(TaskConstants.Type.EXECUTION);
        Map<String, Integer> map = new HashMap<String, Integer>();
        Integer bpmTask = bpmTaskRepository.countByTaskAssigneeAndTaskType(username, type);
        Integer assistant = assistantRepo.countByAssistantEmail(username);
        Integer assign = assignRepository.countByUsername(username);
        map.put(MenuConstant.Key.BPM_TASK.key, bpmTask);
        map.put(MenuConstant.Key.ASSISTANT.key, assistant);
        map.put(MenuConstant.Key.ASSIGN.key, assign);

        return map;
    }

    public BpmProcInst findBpmProcInstById(Long ticketId) {
        return bpmProcInstRepository.findById(ticketId).orElseThrow(() -> new RuntimeException(common.getMessage("message.ticket.not-exists")));
    }

    public List<TicketCompletedResponse> getListTicketCompletedInfo(String username) {
        List<TicketCompletedDto> lstData = bpmProcInstRepository.getTicketCompletedInfo(username);
        List<TicketCompletedResponse> lstResponse = new ArrayList<>();
        if (!ValidationUtils.isNullOrEmpty(lstData)) {
            Map<Long, List<String>> map = lstData.stream()
                    .collect(Collectors.groupingBy(
                            TicketCompletedDto::getTicketId,
                            Collectors.mapping(TicketCompletedDto::getActionUser, Collectors.toList())
                    ));

            lstResponse = lstData.stream().map(data -> {
                TicketCompletedResponse response = new TicketCompletedResponse();
                response.setTicketId(data.getTicketId());
                response.setTicketTitle(data.getTicketTitle());
                response.setRequestCode(data.getRequestCode());
                response.setAppCode(data.getAppCode());
                response.setSystemCode(data.getSystemCode());
                response.setListAssignee(map.get(data.getTicketId()).stream().distinct().collect(Collectors.toList()));
                response.setServiceId(data.getServiceId());
                response.setUrl(port.substring(0, port.lastIndexOf('/')) + CONTEXT_PATH_VIEW_TICKET + "/" + data.getProcDefId() + "/" + data.getTicketId() + "/" + data.getStartKey());
                response.setCreatedUser(data.getCreatedUser());
                return response;
            }).distinct().collect(Collectors.toList());
        }

        return lstResponse;
    }

    public List<String> getListAssigneeTicketCompleted(Long ticketId) {
        List<String> lstResponse = new ArrayList<>();
        List<String> lstActionUser = bpmProcInstRepository.getListAssigneeTicketCompleted(ticketId);
        if (!ValidationUtils.isNullOrEmpty(lstActionUser)) {
            List<String> lstUsernameActive = customerService.getListUsernameActive(lstActionUser);
            if (!ValidationUtils.isNullOrEmpty(lstUsernameActive)) {
                lstResponse = lstActionUser.stream().filter(lstUsernameActive::contains).collect(Collectors.toList());
            }
        }
        return lstResponse;
    }

    public Map<String, Object> cancelByTicket(CancelByTicketRequest request) {
        Map<String, Object> response = new HashMap<>();
        StringBuilder reason = new StringBuilder();
        reason.append("Hủy phiếu sau khi duyệt - Được hủy bởi tờ trình ");
        reason.append("<a href=\"");
        reason.append(port.substring(0, port.lastIndexOf('/'))).append(CONTEXT_PATH_VIEW_TICKET).append("/").append(request.getTicketProcDefId()).append("/").append(request.getCancelTicketId()).append("/").append(request.getTicketStartActId());
        reason.append("\" target=\"_blank\">");
        reason.append(StringUtil.nvl(request.getRequestCode(), request.getCancelTicketId().toString()));
        reason.append("</a>");
//        String reason = "Hủy phiếu sau khi duyệt";

        BpmProcInst bpmProcInst = bpmProcInstRepository.getBpmProcInstByTicketId(request.getTicketId());

        HistoryDto hisDto = new HistoryDto();
        hisDto.setActionUser(request.getActionUser());
        hisDto.setAction(TaskConstants.HistoryAction.CANCEL_TICKET.code);
        hisDto.setNote(String.valueOf(reason));
        hisDto.setProcInstId(bpmProcInst.getTicketProcInstId());
        hisDto.setTicketId(request.getTicketId());

        // get action_user_info
        List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(request.getActionUser());
        if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
            Map<String, Object> actionUserInfo = new HashMap<>();
            String userTitle = lstUserTitle.stream()
                    .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                    .map(title -> {
                        String strTitle = StringUtil.nvl(title.getTitle(), "");
                        int concurrently = title.getConcurrently();
                        return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                    })
                    .collect(Collectors.joining(" "));
            actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
            actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
            hisDto.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
        }
        bpmHistoryManager.saveHistory(hisDto);

        bpmProcInst.setTicketClosedTime(LocalDateTime.now());
        bpmProcInst.setTicketCanceledTime(LocalDateTime.now());
        bpmProcInst.setTicketStatus(ProcInstConstants.Status.CANCEL.code);
        bpmProcInst.setCancelReason(String.valueOf(reason));
        bpmProcInst.setCancelUser(request.getActionUser());
        bpmProcInstRepository.save(bpmProcInst);

        response.put("ticketId", bpmProcInst.getTicketId());
        response.put("reason", reason);
        response.put("actionUser", request.getActionUser());

        return response;
    }

    public List<Map<String, Object>> getListTaskDefKeyByServiceId(List<Long> listServiceId, String username) {
        List<Map<String, Object>> lstResponse = new ArrayList<>();

        List<Tuple> lstData = bpmProcInstRepository.getListTaskDefKeyByServiceId(listServiceId, username);
        for (Tuple data : lstData) {
            Map<String, Object> response = new HashMap<>();
            response.put("taskDefKey", data.get("taskDefKey"));
            response.put("taskName", data.get("taskName"));
            lstResponse.add(response);
        }

        return lstResponse;
    }

    @Scheduled(cron = "0 0/15 * * * ?")
    public void addMissingSign() {
        List<Tuple> lstMissingSign = bpmProcInstRepository.checkMissingSign();
        log.info("addMissingSign: {}", lstMissingSign);
        if (!ValidationUtils.isNullOrEmpty(lstMissingSign)) {
            for (Tuple data : lstMissingSign) {
                String taskDefKey = (String) data.get("taskDefKey");
                String assignee = (String) data.get("assignee");
                String actionUser = (String) data.get("actionUser");
                String procInstId = (String) data.get("procInstId");
                String startKey = (String) data.get("startKey");

                BpmHistory history = bpmHistoryRepository.getBpmHistoryMissingSign(procInstId, taskDefKey, actionUser);
                if (history == null) {
                    continue;
                }
                BpmTpSignZone signZone = bpmTpSignZoneManager.getSignZoneByTaskDefKeyAndUser(procInstId, taskDefKey, assignee);
                if (signZone == null) {
                    continue;
                }

                List<BpmTpSignZone> signZoneStart = bpmTpSignZoneManager.findOne(procInstId, startKey);
                Map<String, Object> signObject = bpmTpSignZoneManager.getSignByUsername(actionUser);
                String sign = signObject.get("sign").toString();
                String position = signObject.get("position").toString();
                String firstname = signObject.get("firstname").toString();
                String lastname = signObject.get("lastname").toString();

                signZone.setFirstName(firstname);
                signZone.setLastName(lastname);
                signZone.setSign(sign);
                signZone.setPosition(position);
                signZone.setSignedFile(signZoneStart.get(0).getSignedFile());
                signZone.setSignedDate(history.getCreatedTime());
                signZone.setComment(history.getNote());
                bpmTpSignZoneManager.save(signZone);

                // combine sign file
                bpmPrintPhaseManager.combineSignedFile(signZone.getBpmTemplatePrintId(), signZone.getProcInstId());
            }
        }
    }

    public boolean checkUserInTicket(List<String> users) {
        List<Long> listTaskId = bpmTaskRepository.getListTaskByListUser(users);
        List<BpmProcInst> lstTicket = bpmProcInstRepository.findBpmProcInstByTicketStartUserIdIn(users);
        return ValidationUtils.isNullOrEmpty(listTaskId) && ValidationUtils.isNullOrEmpty(lstTicket);
    }

    public byte[] getHtmlFileTemplate(Long ticketId, BpmTemplatePrintRequest bpmTemplatePrintRequest, List<BpmTpSignZone> bpmTpSignZoneList) throws Exception {
        BpmProcInst currentTicket;
        Map<String, VariableValueDto> variables = bpmTemplatePrintRequest.getVariables();
        //Lấy tất cả các biến đã lưu nếu có truyền thêm ticketId
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        VariableValueDto variableValueDto;
        if (ticketId != null && !Objects.equals(ticketId, 0L)) {
            currentTicket = findById(ticketId);
            if (currentTicket != null) {//Đã có ticket thì lấy all biến đã lưu theo ProcInstId
                Map<String, VariableValueDto> variableValueDtoMap = actHiVarInstManager.getVariByTicketAndConvertToMap(currentTicket.getTicketProcInstId());
                if (variableValueDtoMap != null) {
                    if (variables == null) {
                        variables = new HashMap<>();
                    }
                    variables.putAll(variableValueDtoMap);
                }
            }

            //Lấy thông tin phiếu
            TicketDetailResponse ticketDetailResponse = getPrintInfo(ticketId);
            //Add một số thông tin mặc định của phiếu
            //Mã phiếu
            variableValueDto = new VariableValueDto();
            variableValueDto.setType("String");
            variableValueDto.setValue(ticketDetailResponse.getId());
            variables.put("txt_maPhieu", variableValueDto);

            //Mã tờ trình
            variableValueDto = new VariableValueDto();
            variableValueDto.setType("String");
            variableValueDto.setValue(ticketDetailResponse.getRequestCode());
            variables.put("txt_maToTrinh", variableValueDto);

            //Tên phiếu
            variableValueDto = new VariableValueDto();
            variableValueDto.setType("String");
            variableValueDto.setValue(ticketDetailResponse.getTicketTitle());
            variables.put("txt_tenPhieu", variableValueDto);

            //Thời gian tạo phiếu
            variableValueDto = new VariableValueDto();
            variableValueDto.setType("String");
            variableValueDto.setValue(ticketDetailResponse.getTicketCreatedTime() != null ? dateFormat.format(ticketDetailResponse.getTicketCreatedTime()) : "");
            variables.put("dtm_thoiGianTaoPhieu", variableValueDto);

            //Loại dịch vụ
            variableValueDto = new VariableValueDto();
            variableValueDto.setType("String");
            variableValueDto.setValue(ticketDetailResponse.getProcServiceName());
            variables.put("txt_loaiDichvu", variableValueDto);

            //Thời gian hoàn thành thực tế
            variableValueDto = new VariableValueDto();
            variableValueDto.setType("String");
            variableValueDto.setValue(ticketDetailResponse.getTicketFinishTime() != null ? dateFormat.format(ticketDetailResponse.getTicketFinishTime()) : "");
            variables.put("dtm_hoanThanhThucTe", variableValueDto);

            //Thời gian phản hồi thực tế
            variableValueDto = new VariableValueDto();
            variableValueDto.setType("String");
            variableValueDto.setValue(ticketDetailResponse.getTicketStartedTime() != null ? dateFormat.format(ticketDetailResponse.getTicketStartedTime()) : "");
            variables.put("dtm_phanHoiThucTe", variableValueDto);

            //Cam kết phản hồi
            variableValueDto = new VariableValueDto();
            variableValueDto.setType("String");
            variableValueDto.setValue(ticketDetailResponse.getSlaResponseProcess());
            variables.put("txt_camKetPhanHoi", variableValueDto);

            //Cam kết Hoàn thành
            variableValueDto = new VariableValueDto();
            variableValueDto.setType("String");
            variableValueDto.setValue(ticketDetailResponse.getSlaFinishProcess());
            variables.put("txt_camKetHoanThanh", variableValueDto);

            //Phản hồi dự kiến
            variableValueDto = new VariableValueDto();
            variableValueDto.setType("String");
            variableValueDto.setValue(ticketDetailResponse.getSlaResponseTimeProcess() != null ? dateFormat.format(ticketDetailResponse.getSlaResponseTimeProcess()) : "");
            variables.put("dtm_phanHoiDuKien", variableValueDto);

            //Hoàn thành dự kiến
            variableValueDto = new VariableValueDto();
            variableValueDto.setType("String");
            variableValueDto.setValue(ticketDetailResponse.getSlaFinishTIme() != null ? dateFormat.format(ticketDetailResponse.getSlaFinishTIme()) : "");
            variables.put("dtm_phanHoiDuKien", variableValueDto);

            //Lấy location
            if (!ValidationUtils.isNullOrEmpty(currentTicket.getLocationId())) {
                List<Map<String, Object>> locationManagement = masterDataService.getLoadTemplateLocation(currentTicket.getLocationId());
                if (!ValidationUtils.isNullOrEmpty(locationManagement)) {
                    variableValueDto = new VariableValueDto();
                    variableValueDto.setType("String");
                    variableValueDto.setValue(locationManagement.get(0).get("name").toString());
                    variables.put("txt_ViTri", variableValueDto);
                }
            }

            //Lấy danh sách phiếu liên kết
            if (!ValidationUtils.isNullOrEmpty(ticketDetailResponse.getLinkedBpmProcInstDto())) {
                List<String> lstRelates = ticketDetailResponse.getLinkedBpmProcInstDto().stream().map(linkedBpmProcInstDto -> ValidationUtils.isNullOrEmpty(linkedBpmProcInstDto.getRequestCode()) ? linkedBpmProcInstDto.getTitle() : linkedBpmProcInstDto.getRequestCode()).collect(Collectors.toList());
                variableValueDto = new VariableValueDto();
                variableValueDto.setType("String");
                variableValueDto.setValue(String.join(", ", lstRelates));
                variables.put("txt_PhieuLienKet", variableValueDto);
            }

            //Lấy theo task
            List<BpmTask> bpmTasks = bpmTaskRepository.getBpmTaskByTaskProcInstId(currentTicket.getTicketProcInstId());
            if (bpmTasks != null && !bpmTasks.isEmpty()) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
                for (BpmTask itemTask : bpmTasks) {
                    //Tên bước
                    VariableValueDto variableDto = new VariableValueDto();
                    variableDto.setType("String");
                    variableDto.setValue(itemTask.getTaskName());
                    variables.put(itemTask.getTaskDefKey() + "_txt_tenBuoc", variableDto);
                    //Thời gian tạo bước
                    variableDto = new VariableValueDto();
                    variableDto.setType("String");
                    variableDto.setValue(itemTask.getTaskCreatedTime() != null ? formatter.format(itemTask.getTaskCreatedTime()) : "");
                    variables.put(itemTask.getTaskDefKey() + "_dtm_thoiGianTaoBuoc", variableDto);
                    //Người thực hiện bước
                    variableDto = new VariableValueDto();
                    variableDto.setType("String");
                    variableDto.setValue(itemTask.getTaskAssignee());
                    variables.put(itemTask.getTaskDefKey() + "_txt_nguoiThucHien", variableDto);
                    //Trạng thái của bước
                    variableDto = new VariableValueDto();
                    variableDto.setType("String");
                    variableDto.setValue(itemTask.getTaskStatus());
                    variables.put(itemTask.getTaskDefKey() + "_txt_trangThai", variableDto);
                    //Thời gian hoàn thành bước
                    variableDto = new VariableValueDto();
                    variableDto.setType("String");
                    variableDto.setValue(itemTask.getTaskFinishedTime() != null ? formatter.format(itemTask.getTaskFinishedTime()) : "");
                    variables.put(itemTask.getTaskDefKey() + "_dtm_hoanThanhThucTe", variableDto);
                    //Thời gian cam kết hoàn thành
                    variableDto = new VariableValueDto();
                    variableDto.setType("String");
                    variableDto.setValue(itemTask.getSlaFinish());
                    variables.put(itemTask.getTaskDefKey() + "_txt_camKetHoanThanh", variableDto);
                    //Thời gian hoàn thành dự kiến
                    variableDto = new VariableValueDto();
                    variableDto.setType("String");
                    variableDto.setValue(itemTask.getSlaFinishTime() != null ? formatter.format(itemTask.getSlaFinishTime()) : "");
                    variables.put(itemTask.getTaskDefKey() + "_dtm_hoanThanhDuKien", variableDto);
                }
            }
        }

        // Các biến defaultTicket
        if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrintRequest.getCreateVariables())) {
            Map<String, Object> createVariables = bpmTemplatePrintRequest.getCreateVariables();
            VariableValueDto variableDto = new VariableValueDto();
            if (!ValidationUtils.isNullOrEmpty(createVariables.get("txt_tenPhieu"))) {
                variableDto.setType("String");
                variableDto.setValue(createVariables.get("txt_tenPhieu"));
                variables.put("txt_tenPhieu", variableDto);
            }
            if (!ValidationUtils.isNullOrEmpty(createVariables.get("txt_ViTri"))) {
                variableDto = new VariableValueDto();
                variableDto.setType("String");
                variableDto.setValue(createVariables.get("txt_ViTri"));
                variables.put("txt_ViTri", variableDto);
            }
        }

        //Ngày giờ hiện tại
        variableValueDto = new VariableValueDto();
        variableValueDto.setType("String");
        variableValueDto.setValue(dateFormat.format(new Date()));
        variables.put("dtm_ngayGioHienTai", variableValueDto);

        //Xuất file trả về
        String htmlContent = bpmTemplatePrintRequest.getHtmlContent();
        String headerContent = bpmTemplatePrintRequest.getHeaderContent();
        String footerContent = bpmTemplatePrintRequest.getFooterContent();

        if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrintRequest.getFileConditionId())) { // tạo phiếu
            Tuple tuple = bpmFileConditionRepository.getTemplateHtmlChangeById(bpmTemplatePrintRequest.getFileConditionId());
            htmlContent = tuple.get("templateHtml").toString();
            headerContent = !ValidationUtils.isNullOrEmpty(tuple.get("templateHeader")) ? tuple.get("templateHeader").toString() : "";
            footerContent = !ValidationUtils.isNullOrEmpty(tuple.get("templateFooter")) ? tuple.get("templateFooter").toString() : "";
        }

        String htmlReplace = templateHtml.handleData(variables, htmlContent, bpmTpSignZoneList);
        return gotenbergManager.htmlStringToPdf(htmlReplace, headerContent, footerContent);
    }
}
