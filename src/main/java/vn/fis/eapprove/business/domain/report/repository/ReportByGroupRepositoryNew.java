package vn.fis.eapprove.business.domain.report.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.report.entity.ReportByGroupNew;


@Repository
public interface ReportByGroupRepositoryNew extends JpaRepository<ReportByGroupNew, Long> {

    @Query(nativeQuery = true, value = "select * from report_by_group_new where ticket_id = :ticketId and version_time = :versionTime")
    ReportByGroupNew findByTicketIdAndVersionTime(@Param("ticketId") Long ticketId,
                                                  @Param("versionTime") String versionTime);

}
