package vn.fis.eapprove.business.domain.assistant.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;

import jakarta.persistence.*;
import java.util.Date;

/**
 * Author: AnhVTN
 * Date: 01/03/2023
 */
@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Entity
@Builder
@AllArgsConstructor
@Table(name = "assistant")
public class Assistant {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;
    @Column(name = "ticket_id")
    private String ticketId;
    @Column(name = "assistant_email")
    private String assistantEmail;
    @Column(name = "create_at")
    private Date createAt;
    @Column(name = "update_at")
    private Date updateAt;

    @Transient
    private String formatedCreateAt;
    @Transient
    private String formatedUpdateAt;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ticket_id", referencedColumnName = "ID", insertable = false, updatable = false)
    @JsonIgnore
    private BpmProcInst bpmProcInst;

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
