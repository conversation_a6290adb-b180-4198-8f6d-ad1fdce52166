package vn.fis.eapprove.business.domain.manageApi.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.manageApi.entity.ManageAPI;
import vn.fis.eapprove.business.domain.manageApi.service.ManageAPIManager;


@Repository
public interface ManagerAPIRepository extends JpaRepository<ManageAPI, Long>, JpaSpecificationExecutor<ManageAPIManager> {
    @Query("SELECT c FROM ManageAPI c WHERE c.nameAPI LIKE CONCAT('%',:search,'%')")
    Page<ManageAPI> getAll(@Param("search") String search, Pageable pageable);
}
