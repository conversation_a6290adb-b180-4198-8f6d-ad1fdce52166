package vn.fis.eapprove.business.domain.template.entity;


import lombok.Data;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Table(name = "template_history")
@Data
public class TemplateHistory  implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "template_id")
    private Long templateId;

    @Column(name = "version")
    private String version;

    @Column(name = "status_history")
    private Boolean statusHistory;

    @Column(name = "content_edit")
    private String contentEdit;

    @Column(name = "url_name")
    private String urlName;

    @Column(name = "template_name")
    private String templateName;

    @Column(name = "template")
    private String template;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "description")
    private String description;

    @Column(name = "apply_for")
    private String applyFor;

    @Column(name = "share_with")
    private String shareWith;

    public TemplateHistory copyWithoutId(TemplateHistory oldEntity,Integer newVersion) {
        TemplateHistory newEntity = new TemplateHistory();
        newEntity.setTemplateId(oldEntity.getTemplateId());
        newEntity.setVersion("V"+newVersion);
        newEntity.setStatusHistory(oldEntity.getStatusHistory());
        newEntity.setContentEdit(oldEntity.getContentEdit());
        newEntity.setUrlName(oldEntity.getUrlName());
        newEntity.setTemplateName(oldEntity.getTemplateName());
        newEntity.setTemplate(oldEntity.getTemplate());
        newEntity.setCreatedDate(LocalDateTime.now());
        newEntity.setCreatedUser(null);
        newEntity.setDescription(oldEntity.getDescription());
        newEntity.setShareWith(oldEntity.getShareWith());
        newEntity.setApplyFor(oldEntity.getApplyFor());
        return newEntity;
    }

}
