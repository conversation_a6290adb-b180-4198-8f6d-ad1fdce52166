package vn.fis.eapprove.business.domain.system.entity;

import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "system_group_history")
public class SystemGroupHistory implements Cloneable{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "group_type")
    private String groupType;

    @Column(name = "description")
    private String description;

    @Column(name = "parent")
    private Long parent;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "version")
    private Long version;

}