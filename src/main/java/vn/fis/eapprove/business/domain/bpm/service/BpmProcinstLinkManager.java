package vn.fis.eapprove.business.domain.bpm.service;



import vn.fis.eapprove.business.domain.bpm.entity.BpmProcinstLink;

import java.util.List;

public interface BpmProcinstLinkManager {
    void deleteAllBpmProcinstLinkById(Long procInstId);

    void saveAll(List<BpmProcinstLink> bpmProcinstLinkList);

    void save(BpmProcinstLink procinstLink);

    List<BpmProcinstLink> findBpmProcinstLinkByBpmProcinstId(Long bpmProcinstId);

    List<Long> getProcinstLinkByBpmProcinstId(Long bpmProcinstId);

    List<Long> getProcinstLinkByBpmProcinstLinkId(Long bpmProcinstLinkId);
}
