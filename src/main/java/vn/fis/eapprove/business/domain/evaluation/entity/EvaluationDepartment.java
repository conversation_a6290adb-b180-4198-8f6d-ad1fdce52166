package vn.fis.eapprove.business.domain.evaluation.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;

@Entity
@Table(name = "evaluation_department")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class EvaluationDepartment {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "evaluation_criteria_id")
    private Long evaluationCriteriaId;

    @Column(name = "department_codes")
    private String departmentCodes;

    @Column(name = "company_code")
    private String companyCode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "evaluation_criteria_id", updatable = false, insertable = false)
    @JsonIgnore
    private EvaluationCriteria evaluationCriteria;
}
