package vn.fis.eapprove.business.domain.bpm.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import vn.fis.eapprove.business.domain.assistant.entity.Assistant;
import vn.fis.eapprove.business.domain.authority.entity.AuthorityManagement;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.submission.entity.SubmissionType;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@Entity
@Table(name = "bpm_procinst")
@NamedEntityGraph(
        name = "proc-inst-query-graph",
        attributeNodes = {
                @NamedAttributeNode("servicePackage"),
                @NamedAttributeNode(value = "bpmTasks", subgraph = "bpmTasks-subgraph"),
        },
        subgraphs = {
                @NamedSubgraph(
                        name = "bpmTasks-subgraph",
                        attributeNodes = {
                                @NamedAttributeNode("bpmProcInst")
                        }
                )
        }
)
public class BpmProcInst implements Serializable {

    private static final long serialVersionUID = 894188785036651347L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long ticketId;

    @Column(name = "SERVICE_ID")
    private Long serviceId;

    @Column(name = "PROC_INST_ID")
    private String ticketProcInstId;

    @Column(name = "TITLE")
    private String ticketTitle;

    @Column(name = "PROC_DEF_KEY")
    private String ticketProcDefKey;

    @Column(name = "PROC_DEF_ID")
    private String ticketProcDefId;

    @Column(name = "PRIORITY")
    private String priority;

    @Column(name = "CREATED_USER")
    private String createdUser;

    @Column(name = "PRIORITY_ID")
    private Long priorityId;

    @Column(name = "PRIORITY_HISTORY_ID")
    private Long priorityHistoryId;

    @Column(name = "CREATED_TIME")
    private LocalDateTime ticketCreatedTime;

    @Column(name = "STARTED_TIME")
    private LocalDateTime ticketStartedTime;

    @Column(name = "END_TIME")
    private LocalDateTime ticketEndTime;

    @Column(name = "DURATION")
    private Long ticketDuration;

    @Column(name = "START_USER_ID")
    private String ticketStartUserId;

    @Column(name = "START_ACT_ID")
    private String ticketStartActId;

    @Column(name = "END_ACT_ID")
    private String ticketEndActId;

    @Column(name = "DELETE_REASON")
    private String ticketDeleteReason;

    @Column(name = "TENANT_ID")
    private String ticketTenantId;

    @Column(name = "STATUS")
    private String ticketStatus;

    @Column(name = "IS_AUTO")
    private Boolean isAuto;

    @Column(name = "SLA_FINISH")
    private Double slaFinish;

    @Column(name = "SLA_RESPONSE")
    private Double slaResponse;

    @Column(name = "CANCELED_TIME")
    private LocalDateTime ticketCanceledTime;

    @Column(name = "CLOSED_TIME")
    private LocalDateTime ticketClosedTime;

    @Column(name = "FINISH_TIME")
    private LocalDateTime ticketFinishTime;

    @Column(name = "EDIT_TIME")
    private LocalDateTime ticketEditTime;

    @Column(name = "RATING")
    private Double ticketRating;

    @Column(name = "COMMENT")
    private String comment;

    @Column(name = "CANCEL_REASON")
    private String cancelReason;

    @Column(name = "DESCRIPTION")
    private String ticketDescription;

    @Column(name = "location_id")
    private Long locationId;

    @Column(name = "submission_type_id")
    private Long submissionTypeId;

    @Column(name = "link_procinst_id")
    private Long linkProcinstId;

    @Column(name = "email_notification")
    private Boolean emailNotification;

    @Column(name = "app_code")
    private String appCode;

    @Column(name = "request_code")
    private String requestCode;

    @Column(name = "is_assistant")
    private Boolean isAssistant;

    @Column(name = "chart_id")
    private Long chartId;

    @Column(name = "ru_time")
    private LocalDateTime ruTime;

    @Column(name = "company_code")
    private String companyCode;

    @Column(name = "chart_name")
    private String chartName;

    @Column(name = "chart_node_name")
    private String chartNodeName;

    @Column(name = "submission_type_name")
    private String submissionTypeName;

    @Column(name = "chart_node_id")
    private Long chartNodeId;

    @Column(name = "chart_node_code")
    private String chartNodeCode;

    @Column(name = "template_version_id")
    private Long templateVersionId;

    @Column(name = "parent_template_version_id")
    private Long parentTemplateVersionId;

    @Column(name = "force_view_url")
    private String forceViewUrl;

    // id tờ trình Hủy phiếu đã duyệt
    @Column(name = "cancel_completed_ticket")
    private Long cancelCompletedTicketId;

    @Column(name = "cancel_user")
    private String cancelUser;

    @Column(name = "ticket_assign")
    private Boolean ticketAssign;

    @Column(name = "approved_budget_ids")
    private String approvedBudgetIds;

    @Column(name = "legislative_id")
    private Long legislativeId;

    @Column(name = "first_created_time")
    private LocalDateTime firstCreatedTime;

    @Column(name = "finish_user")
    private String finish_user;

    @Column(name = "response_agency")
    private String responseAgency;

    @Column(name = "legislative_type")
    private String legislativeType;

    // JOIN
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SERVICE_ID", updatable = false, insertable = false)
    @JsonIgnore
    private ServicePackage servicePackage;

    @OneToMany(mappedBy = "bpmProcInst")
    @JsonIgnore
    private Set<BpmTask> bpmTasks;

    @OneToMany(mappedBy = "bpmProcInst")
    @JsonIgnore
    private Set<BpmShared> bpmShareds;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PRIORITY_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    @JsonIgnore
    private PriorityManagement priorityManagement;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "submission_type_id", referencedColumnName = "ID", insertable = false, updatable = false)
    @JsonIgnore
    private SubmissionType submissionType;

    @OneToMany(mappedBy = "bpmProcInst")
    @JsonIgnore
    private Set<BpmHistory> bpmHistories;

    @OneToMany(mappedBy = "bpmProcInst")
    @JsonIgnore
    private Set<AuthorityManagement> authorityManagements;

    @OneToMany(mappedBy = "bpmProcInst")
    @JsonIgnore
    private Set<Assistant> assistants;

}