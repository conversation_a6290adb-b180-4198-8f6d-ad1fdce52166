package vn.fis.eapprove.business.domain.priority.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;


import java.util.List;


@Repository
public interface PriorityManagementRepository extends JpaRepository<PriorityManagement, Long>, JpaSpecificationExecutor<PriorityManagement> {

    @Query("SELECT u FROM PriorityManagement u " +
            "WHERE LOWER(u.name) LIKE LOWER(CONCAT('%',:search,'%'))")
    List<PriorityManagement> getListPriority(@Param("search") String search);

    @Query("SELECT c FROM PriorityManagement c WHERE c.name = :namePriority AND c.createdDate is not null")
    List<PriorityManagement> CheckExistName(String namePriority);

    @Query("SELECT c.slaValue FROM PriorityManagement c WHERE c.id = :id AND c.createdDate is not null")
    Double getValueById(Long id);

    @Query("UPDATE PriorityManagement p SET p.activeStatus = :activeStatus WHERE p.id in :lstId")
    @Modifying
    void updateStatusById(List<Long> lstId,Integer activeStatus);

    PriorityManagement getPriorityManagementById(Long id);
    @Query("SELECT CASE WHEN COUNT(bp.name) > 0 THEN true ELSE false END FROM PriorityManagement pm JOIN BpmProcdef bp on bp.priorityId = pm.id Where pm.id in :id")
    Boolean checkPriorityExistInProcdef(List<Long> id);

    @Query("SELECT CASE WHEN COUNT(bp.name) > 0 THEN true ELSE false END FROM PriorityManagement pm JOIN BpmProcdef bp on bp.priorityId = pm.id Where pm.id in :ids")
    Boolean checkPriorityExistInProcdefById(List<Long> ids);

    List<PriorityManagement> findAllByActiveStatus(Integer status);
}
