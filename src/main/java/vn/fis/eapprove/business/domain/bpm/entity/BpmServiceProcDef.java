package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;

@Data
@Entity
@Table(name = "bpm_owner_process")
@AllArgsConstructor
@NoArgsConstructor
public class BpmServiceProcDef {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "SERVICE_ID")
    private Long serviceId;

    @Column(name = "PROCDEF_ID")
    private Long procDefId;
}
