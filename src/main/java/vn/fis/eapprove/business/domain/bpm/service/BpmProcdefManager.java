package vn.fis.eapprove.business.domain.bpm.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import jakarta.persistence.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.*;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.camunda.bpm.model.xml.instance.ModelElementInstance;
import org.json.JSONArray;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import vn.fis.eapprove.business.config.GsonAdapterConfig;
import vn.fis.eapprove.business.domain.api.entity.ApiManagement;
import vn.fis.eapprove.business.domain.api.repository.ApiManagementRepository;
import vn.fis.eapprove.business.domain.authority.service.AuthService;
import vn.fis.eapprove.business.domain.bpm.entity.*;
import vn.fis.eapprove.business.domain.bpm.repository.*;
import vn.fis.eapprove.business.domain.groupTable.entity.GroupTableProTemp;
import vn.fis.eapprove.business.domain.groupTable.repository.GroupProcTempRepository;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.repository.ServicePackageRepository;
import vn.fis.eapprove.business.domain.servicePackage.service.ServicePackageManager;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.repository.ShareUserRepository;
import vn.fis.eapprove.business.domain.sharedUser.service.ShareUserManager;
import vn.fis.eapprove.business.domain.system.repository.SystemGroupRepository;
import vn.fis.eapprove.business.domain.template.entity.TemplateManage;
import vn.fis.eapprove.business.domain.template.repository.TemplateRepository;
import vn.fis.eapprove.business.dto.*;
import vn.fis.eapprove.business.exception.ErrorMessage;
import vn.fis.eapprove.business.mapper.BpmProcDefDetailMapper;
import vn.fis.eapprove.business.mapper.BpmProcdefMapper;
import vn.fis.eapprove.business.mapper.BpmProdefTestMapper;
import vn.fis.eapprove.business.model.request.BpmTaskPrintRequest;
import vn.fis.eapprove.business.model.response.*;
import vn.fis.eapprove.business.specification.BpmProcdefSpecification;
import vn.fis.eapprove.business.specification.ServicePackageSpecification;
import vn.fis.eapprove.business.tenant.manager.CamundaEngineService;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.utils.CamundaUtils;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.constants.*;
import vn.fis.spro.common.exception.AppException;
import vn.fis.spro.common.model.request.QueryRequest;
import vn.fis.spro.common.model.response.ChartInfoRoleResponse;
import vn.fis.spro.common.model.response.PagingResponse;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.SortUtils;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.manager.FileManager;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static vn.fis.eapprove.business.constant.Constant.PROCESS;

/**
 * Generated by Speed Generator
 *
 * <AUTHOR> href="mailto:<EMAIL>">Kien Nguyen</a>
 */
@Slf4j
@Service("BpmProcdefManagerV1")
@Transactional
public class BpmProcdefManager {

    @Autowired
    CredentialHelper credentialHelper;
    @Autowired
    CustomerService customerService;
    /**
     * @param json
     * @param prodefId
     * @return
     */
    TypeMap<ApprovalTaskDto, BpmPrintVariableDto> propertyMapper = null;
    @Autowired
    private ShareUserRepository shareUserRepository;
    @Autowired
    private SproProperties sproProperties;
    @Autowired
    private BpmProcdefRepository bpmProcdefRepository;
    @Autowired
    private BpmProcDefHistoryRepository bpmProcDefHistoryRepository;
    @Autowired
    private BpmProcdefMapper bpmProcdefMapper;
    @Autowired
    private BpmProcdefSpecification bpmProcdefSpecification;
    @Autowired
    private BpmOwnerProcessManager bpmOwnerProcessManager;
    @Autowired
    private BpmProcDefDetailMapper bpmProcDefDetailMapper;
    @Autowired
    private ServicePackageRepository servicePackageRepo;
    @Autowired
    private ServicePackageSpecification servicePackageSpec;
    @Autowired
    private ServicePackageManager servicePackageManager;
    @Autowired
    private ResponseUtils responseUtils;
    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private BpmTpTaskRepository bpmPrintPhaseTaskRepository;
    @Autowired
    private BpmTemplatePrintRepository bpmTemplatePrintRepository;
    @Autowired
    private GroupProcTempRepository groupProcTempRepository;
    @Autowired
    private TemplateRepository templateRepository;
    @Autowired
    private Common common;
    @Autowired
    private CamundaEngineService camundaEngineService;
    private RestTemplate restTemplate = new RestTemplate();
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private BpmProcdefApiRepository bpmProcdefApiRepository;
    @Autowired
    private BpmProcdefInheritsRepository bpmProcdefInheritsRepository;
    @Autowired
    private BpmProcdefNotificationRepository bpmProcdefNotificationRepository;
    @Autowired
    private BpmProcdefNotificationDetailRepository bpmProcdefNotificationDetailRepository;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private BpmProcdefApiManager bpmProcdefApiManager;
    @Autowired
    private BpmProdefTestMapper bpmProdefTestMapper;
    @Autowired
    private ApiManagementRepository apiManagementRepository;
    @Autowired
    private ShareUserManager shareUserManager;
    @Autowired
    private PermissionDataManagementRepository permissionDataManagementRepository;
    @Autowired
    private AuthService authService;
    @Autowired
    private BpmProcdefViewFileApiRepository bpmProcdefViewFileApiRepository;
    @Autowired
    private BpmProcdefLegislativeConfigRepository bpmProcdefLegislativeConfigRepository;

    @Autowired
    private BpmProcdefLegislativeTicketConfigRepository bpmProcdefLegislativeTicketConfigRepository;

    @Autowired
    private SystemGroupRepository systemGroupRepository;
    @Autowired
    private BpmTpTaskRepository bpmTpTaskRepository;
    @Value("${app.s3.bucket}")
    private String bucket;
    @Autowired
    private BpmOwnerProcessRepository bpmOwnerProcessRepository;

    public BpmProcdefDto get(Long id) {
        return bpmProcdefRepository.findById(id).map(bpmProcdefMapper::entityToDto).orElse(null);
    }

    // GET ALL PROC DEF
    public List<BpmProcdefDto> listAllBpmProcdef() {
        try {
            List<BpmProcdefDto> bpmProcdefDtoList = bpmProcdefRepository.findAll().stream().map(e -> modelMapper.map(e, BpmProcdefDto.class)).collect(Collectors.toList());
            return bpmProcdefDtoList;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<BpmProcdefDto> listAllBpmProcdefNew(String name) {
        try {
            List<String> listStatus = Arrays.asList("DEACTIVE", "ACTIVE");
            List<BpmProcdefDto> bpmProcdefDtoList = bpmProcdefRepository.findBpmProcdefByNameContainingAndStatusIn(name, listStatus).stream()
                    // filter bỏ qt con special flow
                    .filter(bpmProcdef -> bpmProcdef.getSpecialFlow() == null)
                    .map(e -> modelMapper.map(e, BpmProcdefDto.class)).collect(Collectors.toList());
            return bpmProcdefDtoList;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public PageDto listAllBpmProcdefByTemplateId(BpmProcdefDto criteria, Long templateId) {
        try {
            Integer pageNum = criteria.getPage() - 1;
            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());
            TemplateManage templateManage = templateRepository.findById(templateId).get();
            String formKey = templateManage.getUrlName();
            List<GroupTableProTemp> groupTableProTemps = groupProcTempRepository.getGroupTableProTempsByFormKey(formKey);
            List<String> procDefIds = new ArrayList<>();
            for (GroupTableProTemp groupTableProTemp : groupTableProTemps) {
                String procDefId = null;
                procDefId = groupTableProTemp.getProDefId();
                procDefIds.add(procDefId);
            }
            Page<BpmProcdef> bpmProcdefs = bpmProcdefRepository.getBpmProcdefByProcDefIdIn(procDefIds, PageRequest.of(pageNum, criteria.getLimit(), sort));
            List<BpmProcdefDto> data = bpmProcdefs.getContent().stream().map(e -> modelMapper.map(e, BpmProcdefDto.class)).collect(Collectors.toList());
            return PageDto.builder()
                    .content(data)
                    .number(bpmProcdefs.getNumber() + 1)
                    .numberOfElements(bpmProcdefs.getNumberOfElements())
                    .page(bpmProcdefs.getNumber() + 1)
                    .size(bpmProcdefs.getSize())
                    .totalElements(bpmProcdefs.getTotalElements())
                    .totalPages(bpmProcdefs.getTotalPages())
                    .build();

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public Map<String, Object> checkProcessType(String procDefId) {
        return checkProcessType(procDefId, null, null);
    }

    public Map<String, Object> checkProcessType(String procDefId, String taskDefKey, Long serviceId) {
        try {
            BpmnModelInstance modelInstance = camundaEngineService.getModelInstance(procDefId);
            if (modelInstance == null) {
                return null;
            }

            if (taskDefKey == null || "null".equalsIgnoreCase(taskDefKey)) {
                // get start-event
                StartEvent startEvent = CamundaUtils.getStartEvent(modelInstance);
                taskDefKey = startEvent != null ? startEvent.getId() : "";

            }

            BpmProcdef procdef;
            if (serviceId != null && !serviceId.equals(0L)) {
                // (phucvm3) get procDef by service-id
                procdef = getByServiceId(serviceId);
            } else {
                procdef = bpmProcdefRepository.findBpmProcdefByProcDefId(procDefId);
            }

            if (procdef != null) {
                Map<String, Object> mapFinal = new HashMap<>();
                List<Tuple> listPrintPhase = bpmTemplatePrintRepository.getExecutionPrintPhaseNew(procDefId, Collections.singletonList(taskDefKey));
                String finalTaskDefKey = taskDefKey;
                List<Map<String, Object>> listPrintPhaseRes = listPrintPhase.stream().map(x -> {
                    Map<String, Object> mapPrint = new HashMap<>();
                    mapPrint.put("id", x.get("id"));
                    mapPrint.put("printType", x.get("printType"));
                    mapPrint.put("pdfContent", x.get("pdfContent"));
                    mapPrint.put("content", x.get("content"));
                    mapPrint.put("taskDefKey", finalTaskDefKey);
                    mapPrint.put("templateName", x.get("name"));
                    mapPrint.put("status", x.get("status"));
                    mapPrint.put("cf_id", x.get("cf_id"));
                    mapPrint.put("conditionText", x.get("conditionText"));
                    mapPrint.put("uploadWords", x.get("uploadWords"));
                    mapPrint.put("uploadWordsChange", x.get("uploadWordsChange"));
                    mapPrint.put("htmlId", x.get("htmlId"));
                    return mapPrint;
                }).collect(Collectors.toList());

                mapFinal.put("processType", procdef.getProcessType());

                if (procdef.getProcessType() != null && procdef.getProcessType() == 1) {
                    List<Map<String, Object>> listUserTaskData = new ArrayList<>();
                    Collection<UserTask> userTasks = CamundaUtils.getUserTasks(modelInstance);
                    userTasks.forEach(userTask -> {
                        Map<String, Object> dataMap = new HashMap<>();
                        dataMap.put("taskDefKey", userTask.getId());
                        dataMap.put("taskName", userTask.getName());
                        if (userTask.getLoopCharacteristics() != null) {
                            String variable = CamundaUtils.getVariableNameFromExpression(((MultiInstanceLoopCharacteristics) userTask.getLoopCharacteristics()).getCamundaCollection());
                            dataMap.put("zoomeField", variable);
                        }

                        listUserTaskData.add(dataMap);
                    });
                    mapFinal.put("listUserTask", listUserTaskData);
                }
                mapFinal.put("listSignForm", listPrintPhaseRes);
                mapFinal.put("informTo", procdef.getInFormTo());

                return mapFinal;
            }

            return null;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * Get process definition instance by service-id
     *
     * <AUTHOR>
     */
    public BpmProcdef getByServiceId(Long serviceId) {
        return bpmProcdefRepository.getByServiceId(serviceId);
    }

    // GET PROC DEF BY ID
    public BpmProcdef getById(Long id) {
        BpmProcdef bpmProcdef = bpmProcdefRepository.getBpmProcDefById(id);
        return bpmProcdef;
    }

    public BpmProcdef getByProcdefId(String procDefId) {
        BpmProcdef bpmProcdef = bpmProcdefRepository.getByProcdefId(procDefId);
        return bpmProcdef;

    }

    // GET PROC DEF BY NAME
    public List<BpmProcdefDto> getBpmProcdefByName(String name) {
        List<BpmProcdefDto> bpmProcdefDtoList = bpmProcdefRepository.getBpmProcdefByName(name).stream()
                .map(bpmProcdefMapper::entityToDto).collect(Collectors.toList());

        return bpmProcdefDtoList;
    }

    // GET PROC DEF BY NAME
    public List<BpmProcdefDto> listBpmProcdefLikeName(String name) {
        List<BpmProcdefDto> bpmProcdefDtoList = bpmProcdefRepository.getBpmProcdefByNameContainingIgnoreCase(name)
                .stream()
                .map(e -> modelMapper.map(e, BpmProcdefDto.class)).collect(Collectors.toList());
        return bpmProcdefDtoList;
    }

    // GET APPROVAL TASK BY PROCESS DEF ID
    public List<ApprovalTaskDto> getApprovalTask(String procDefId) {
        List<ApprovalTaskDto> dataList = new LinkedList<>();
        try {
            BpmnModelInstance modelInstance = camundaEngineService.getModelInstance(procDefId);
            if (modelInstance == null) {
                return dataList;
            }

            // get start event
            String startKey = "";
            Collection<StartEvent> startEvents = modelInstance.getModelElementsByType(StartEvent.class);
            for (StartEvent event : startEvents) {
                startKey = event.getId();
                String startName = event.getName();
                String elementType = event.getElementType().getTypeName();
                List<FlowNode> listNodeAfter = getFlowNodes(event.getOutgoing(), true);
                List<String> listAfter = listNodeAfter.stream().map(FlowNode::getId).collect(Collectors.toList());
                Set<String> setAfter = new HashSet<String>(listAfter);
                List<String> listWithoutDuplicateElementsAfter = new ArrayList<String>(setAfter);
                List<FlowNode> listNodeBefore = getFlowNodes(event.getIncoming(), false);
                List<String> listBefore = listNodeBefore.stream().map(FlowNode::getId).collect(Collectors.toList());
                Set<String> setBefore = new HashSet<String>(listBefore);
                List<String> listWithoutDuplicateElementsBefore = new ArrayList<String>(setBefore);
                List<BpmTpTask> bpmPrintStartPhase = bpmPrintPhaseTaskRepository.findBpmTpTaskByTaskDefKeyAndProcDefId(startKey, procDefId);
                List<BpmTpTask> bpmTpTaskList = bpmPrintPhaseTaskRepository.findUsed(startKey, procDefId, PROCESS);
                ApprovalTaskDto approvalTaskDto = ApprovalTaskDto.builder()
                        .taskKey(startKey)
                        .taskName(startName)
                        .formKey(event.getCamundaFormKey())
                        .taskType(TaskConstants.Type.EXECUTION.code)
                        .printId(bpmPrintStartPhase.isEmpty() ? null : bpmPrintStartPhase.get(0).getId())
                        .isUsed(CollectionUtils.isNotEmpty(bpmTpTaskList))
                        .elementType(elementType)
                        .listTaskAfter(listWithoutDuplicateElementsAfter)
                        .listTaskBefore(listWithoutDuplicateElementsBefore)
                        .build();
                dataList.add(approvalTaskDto);
            }
            // get user tasks
            Collection<UserTask> userTasks = modelInstance.getModelElementsByType(UserTask.class);
            for (UserTask userTask : userTasks) {
                String taskKey = userTask.getId();
                String taskName = userTask.getName();
                String formKey = userTask.getCamundaFormKey();
                String assignee = userTask.getCamundaAssignee();
                String candidateUsers = userTask.getCamundaCandidateUsers();
                String elementType = userTask.getElementType().getTypeName();
                List<FlowNode> listNodeAfter = getFlowNodes(userTask.getOutgoing(), true);
                List<String> listAfter = listNodeAfter.stream().map(FlowNode::getId).collect(Collectors.toList());
                Set<String> setAfter = new HashSet<String>(listAfter);
                List<String> listWithoutDuplicateElementsAfter = new ArrayList<String>(setAfter);
                List<FlowNode> listNodeBefore = getFlowNodes(userTask.getIncoming(), false);
                List<String> listBefore = listNodeBefore.stream().map(FlowNode::getId).collect(Collectors.toList());
                Set<String> setBefore = new HashSet<String>(listBefore);
                List<String> listWithoutDuplicateElementsBefore = new ArrayList<String>(setBefore);
                listWithoutDuplicateElementsBefore.add(0, startKey);
                boolean isMultiInstance = false;
                String collection = null;
                if (userTask.getLoopCharacteristics() != null && userTask.getLoopCharacteristics() instanceof MultiInstanceLoopCharacteristics) {
                    isMultiInstance = true;
                    collection = ((MultiInstanceLoopCharacteristics) userTask.getLoopCharacteristics()).getCamundaCollection();
                }

                String taskType = TaskConstants.Type.APPROVAL.code;
                if (userTask.getExtensionElements() != null) {
                    Collection<ModelElementInstance> instances = userTask.getExtensionElements().getElements();
                    for (ModelElementInstance instance : instances) {
                        if (instance instanceof CamundaProperties) {
                            for (CamundaProperty prop : ((CamundaProperties) instance).getCamundaProperties()) {
                                if (prop.getCamundaName() != null && prop.getCamundaName().equalsIgnoreCase("setting_taskType") && prop.getCamundaValue() != null) {
                                    taskType = prop.getCamundaValue().toUpperCase();
                                    break;
                                }
                            }
                        }
                    }
                }

                Long printId = null;
                List<BpmTpTask> bpmPrintPhaseTask = bpmPrintPhaseTaskRepository.findBpmTpTaskByTaskDefKeyAndProcDefId(taskKey, procDefId);
                List<BpmTpTask> bpmTpTaskList = bpmPrintPhaseTaskRepository.findUsed(taskKey, procDefId, PROCESS);
                if (bpmPrintPhaseTask.size() > 0) {
                    if (bpmPrintPhaseTask.get(0) != null) {
                        printId = bpmPrintPhaseTask.get(0).getId();
                    }
                }

                ApprovalTaskDto approvalTaskDto = ApprovalTaskDto.builder()
                        .taskKey(taskKey)
                        .taskName(taskName)
                        .formKey(formKey)
                        .taskType(taskType)
                        .printId(printId)
                        .assignee(assignee)
                        .candidateUsers(candidateUsers)
                        .isMultiInstance(isMultiInstance)
                        .isUsed(CollectionUtils.isNotEmpty(bpmTpTaskList))
                        .collection(collection)
                        .elementType(elementType)
                        .listTaskBefore(listWithoutDuplicateElementsBefore)
                        .listTaskAfter(listWithoutDuplicateElementsAfter)
                        .build();


                dataList.add(approvalTaskDto);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return dataList;
    }

    public List<FlowNode> getFlowNodes(Collection<SequenceFlow> sequenceFlows, Boolean iss) {
        List<FlowNode> flowNodes = new ArrayList<>();
        if (sequenceFlows == null || sequenceFlows.isEmpty()) {
            return flowNodes;
        }
        sequenceFlows.forEach(sequenceFlow -> {
            FlowNode node = null;
            if (iss) {
                node = sequenceFlow.getTarget();
            } else {
                node = sequenceFlow.getSource();
            }
            if (node instanceof Task) {
                flowNodes.add(node);
            }
            if (iss) {
                flowNodes.addAll(getFlowNodes(node.getOutgoing(), iss));
            } else {
                flowNodes.addAll(getFlowNodes(node.getIncoming(), iss));

            }
        });

        return flowNodes;
    }

    public Map<String, Object> loadVariable(String json, String prodefId) {

        if (propertyMapper == null) {
            propertyMapper = modelMapper.createTypeMap(ApprovalTaskDto.class, BpmPrintVariableDto.class);
            propertyMapper.addMapping(ApprovalTaskDto::getTaskKey, BpmPrintVariableDto::setValue);
            propertyMapper.addMapping(ApprovalTaskDto::getTaskName, BpmPrintVariableDto::setLabel);
            propertyMapper.addMapping(ApprovalTaskDto::getTaskKey, BpmPrintVariableDto::setTaskDefKey);
            propertyMapper.addMapping(ApprovalTaskDto::getListVariable, BpmPrintVariableDto::setListVariable);
        }
        List<BpmTaskPrintRequest> taskKey = new ArrayList<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            taskKey = mapper.readValue(json, new TypeReference<List<BpmTaskPrintRequest>>() {
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<ApprovalTaskDto> lstApprovals = getListBefore(taskKey, prodefId);
        /*List variable parse for col 3 {value:"",label:""}*/
        List<String> variableColThree = new ArrayList();
        if (!lstApprovals.isEmpty()) {
            Set<String> formKey = new HashSet<>();
            taskKey.stream().forEach(item -> {
                if (!ValidationUtils.isNullOrEmpty(item.getFormKey())) {
                    formKey.add(item.getFormKey());
                }
            });
            if (!ValidationUtils.isNullOrEmpty(formKey)) {
                formKey.stream().forEach(item -> {
                    List<String> val = getVariable1(prodefId, item);
                    variableColThree.addAll(val);
                });
            }
            return new HashMap() {{
                put("variableColTwo", lstApprovals.stream().map(approval -> modelMapper.map(approval, BpmPrintVariableDto.class)));
                put("variableColThree", variableColThree);
            }};
        } else {
            List<ApprovalTaskDto> approvalTaskDtoList = getApprovalTask(prodefId);
            List<String> val = approvalTaskDtoList.isEmpty() ? new ArrayList<>() : getVariable1(prodefId, approvalTaskDtoList.get(0).getFormKey());
            variableColThree.addAll(val);
            return new HashMap() {{
                put("variableColTwo", lstApprovals.stream().map(approval -> modelMapper.map(approval, BpmPrintVariableDto.class)));
                put("variableColThree", variableColThree);
            }};
        }

    }

    public List<String> getVariable1(String procDefId, String formKeyChoose) {
        try {
            List<String> listFinal = new ArrayList<>();
            BpmnModelInstance modelInstance = camundaEngineService.getModelInstance(procDefId);
            if (modelInstance == null) {
                return listFinal;
            }
            Collection<UserTask> userTasks = modelInstance.getModelElementsByType(UserTask.class);
            for (UserTask userTask : userTasks) {
                String formkey = userTask.getCamundaFormKey();
                if (!ValidationUtils.isNullOrEmpty(formkey)) {
                    if (formkey.equalsIgnoreCase(formKeyChoose)) {
                        if (!ValidationUtils.isNullOrEmpty(userTask.getCamundaAssignee())) {
                            listFinal.add(userTask.getCamundaAssignee());
                        }
                        if (!ValidationUtils.isNullOrEmpty(userTask.getCamundaCandidateUsers())) {
                            listFinal.add(userTask.getCamundaCandidateUsers());
                        }
                    }
                }
            }
            List<String> stringList = new ArrayList<>();
            TemplateManage templateManages = templateRepository.findByUrlName(formKeyChoose);
            List<Map<String, Object>> formData = new ArrayList<>();
            if (templateManages != null) {
                // special flow thì lấy thêm template cha
                if (templateManages.getSpecialFlow() != null && templateManages.getSpecialFlow() && templateManages.getSpecialParentId() != null) {
                    TemplateManage templateParent = templateRepository.findById(templateManages.getSpecialParentId()).get();
                    if (!ValidationUtils.isNullOrEmpty(templateParent.getTemplate())) {
                        Map<String, List<Map<String, Object>>> resultParent =
                                new ObjectMapper().readValue(templateParent.getTemplate(), HashMap.class);
                        formData.addAll(resultParent.get("form"));
                    }
                }
                if (!ValidationUtils.isNullOrEmpty(templateManages.getTemplate())) {
                    Map<String, List<Map<String, Object>>> result =
                            new ObjectMapper().readValue(templateManages.getTemplate(), HashMap.class);
                    formData.addAll(result.get("form"));
                }
                formData.stream().forEach(fd -> {
                    stringList.add("@" + fd.get("name").toString());
                    if (fd.get("type").toString().equalsIgnoreCase("currency")) {
                        stringList.add("@" + fd.get("name").toString() + "_currency");
                        stringList.add("@" + fd.get("name").toString() + "_currencyText");
                    }

                    //Bổ sung 1 số trường lưu label của select,radio,checkbox
                    List<String> addListText = Arrays.asList("select", "radio", "checkbox");
                    if (addListText.contains(fd.get("type").toString())) {
                        stringList.add("@" + fd.get("name").toString() + "_text");
                    }

                    if (!ValidationUtils.isNullOrEmpty(fd.get("specialField")) && fd.get("specialField") instanceof ArrayList) {
                        List<String> specialFieldList = (List<String>) fd.get("specialField");
                        //Xử lý field dùng chung
                        if (!specialFieldList.isEmpty() && specialFieldList.contains("share")) {
                            stringList.add("@__shareField_" + fd.get("name"));
                        }
                    }

                    List<Map<String, Object>> columnData = (List<Map<String, Object>>) fd.get("columns");
                    if (columnData != null) {
                        columnData.stream().forEach(cd -> {
                            if (!cd.get("name").toString().isEmpty()) {
                                if (fd.get("type").equals("matrix")) {
                                    stringList.add("@" + fd.get("name") + "." + cd.get("name").toString());
                                    if (!ValidationUtils.isNullOrEmpty(fd.get("specialField")) && fd.get("specialField") instanceof ArrayList) {
                                        List<String> specialFieldList = (List<String>) fd.get("specialField");
                                        //Xử lý field dùng chung
                                        if (!specialFieldList.isEmpty() && specialFieldList.contains("share")) {
                                            stringList.add("@__shareField_" + fd.get("name") + "." + cd.get("name").toString());
                                        }
                                    }
                                } else {
                                    stringList.add("@" + cd.get("name").toString());
                                }

                            }
                        });
                    }
                });

                for (String a : listFinal) {
                    String s = a.replace("#{", "").replace("}", "");
                    stringList.add("@" + s);
                }
                Set<String> set = new HashSet<String>(stringList);
                List<String> listWithoutDuplicateElements = new ArrayList<String>(set);
                return listWithoutDuplicateElements;
            }
            return new ArrayList<>();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    public List<ApprovalTaskDto> getListBefore(List<BpmTaskPrintRequest> taskKey, String prodefId) {
        try {
            List<ApprovalTaskDto> approvalTaskDtoListFinal = new ArrayList<>();

            List<ApprovalTaskDto> approvalTaskDtoList = getApprovalTask(prodefId);

            if (!ValidationUtils.isNullOrEmpty(approvalTaskDtoList)) {
                approvalTaskDtoListFinal = approvalTaskDtoList.stream()
                        .filter(lst -> taskKey.stream().map(BpmTaskPrintRequest::getFormKey).collect(Collectors.toList()).contains(lst.getFormKey()))
                        .map(map -> {
                            List<String> lstVari = getVariable1(prodefId, map.getFormKey());
                            if (!ValidationUtils.isNullOrEmpty(lstVari)) {
                                map.setListVariable(lstVari);
                            }
                            return map;
                        })
                        .collect(Collectors.toList());
            }

//            for (BpmTaskPrintRequest task : taskKey) {
//
//                List<String> listBefore = new ArrayList<>();
//                for (ApprovalTaskDto taskDto : approvalTaskDtoList) {
//                    if (taskDto.getTaskKey().equalsIgnoreCase(task.getTaskKey())) {
//                        listBefore = taskDto.getListTaskBefore();
//                    }
//                }
//                listBefore.add(task.getTaskKey());
//                for (ApprovalTaskDto taskDto1 : approvalTaskDtoList) {
//                    if (listBefore.contains(taskDto1.getTaskKey())) {
//                        if (!approvalTaskDtoListFinal.contains(taskDto1)) {
//                            if (!ValidationUtils.isNullOrEmpty(task.getFormKey())) {
//                                taskDto1.setListVariable(getVariable1(prodefId, task.getFormKey()));
//                            }
//                            approvalTaskDtoListFinal.add(taskDto1);
//                        }
//                    }
//
//                }
//
//            }

            return approvalTaskDtoListFinal;
        } catch (Exception e) {
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }


    public List<BpmProcDefDetailDTO> getDetail(Long procDefId, String token) {
        try {
            List<BpmProcDefDetailDTO> bpmProcDefDetailDTOS = bpmProcdefRepository.findBpmProcdefById(procDefId).stream().
                    map(bpmProcDefDetailMapper::entityToDto).collect(Collectors.toList());
            BpmProcdef bpmProcdef = bpmProcdefRepository.getById(procDefId);
            for (BpmProcDefDetailDTO bpmProcDefDetailDTO : bpmProcDefDetailDTOS) {
                List<BpmOwnerProcess> bpmOwnerProcessList = bpmOwnerProcessManager.bpmOwnerProcessByProcDefId(Long.valueOf(bpmProcDefDetailDTO.getId()));
                List<String> account = bpmOwnerProcessList.stream().map(BpmOwnerProcess::getIdUser).collect(Collectors.toList());
                List<BpmOwnerDto> ownerDtos = new ArrayList<>();

                // get user information from customer-service: /chart/getUserByEmail
                List<ChartInfoRoleResponse> chartInfoRoleResponses = responseUtils.getUserByEmail(account);
                if (!ValidationUtils.isNullOrEmpty(chartInfoRoleResponses)) {
                    chartInfoRoleResponses.forEach(e -> {
                        BpmOwnerDto bpmOwnerDto = new BpmOwnerDto(e.getId(), e.getUsername());
                        ownerDtos.add(bpmOwnerDto);
                    });
                }
                bpmProcDefDetailDTO.setOwners(ownerDtos);

                List<ServicePackage> servicePackageList = servicePackageManager.listByProcessId(Long.valueOf(bpmProcDefDetailDTO.getId()));
                List<ServiceNamePopUpResponse> serviceNamePopUp = new ArrayList<>();
                if (servicePackageList.size() > 0) {
                    for (ServicePackage servicePackage : servicePackageList) {
                        serviceNamePopUp.add(new ServiceNamePopUpResponse(String.valueOf(servicePackage.getId()), servicePackage.getServiceName(), servicePackage.getSubmissionType()));
                    }
                    bpmProcDefDetailDTO.setServiceNamePopUp(serviceNamePopUp);
                }

                bpmProcDefDetailDTO.setBytes(bpmProcdef.getBytes());

                // call camunda engine-rest get XML
                bpmProcDefDetailDTO.setFileXml(camundaEngineService.getXML(bpmProcDefDetailDTO.getProcDefId()));

            }
            return bpmProcDefDetailDTOS;
        } catch (Exception e) {
            return null;
        }
    }

    public List<ProDefDetailDto> getAllDetail(Long procDefId) {
        try {

            List<ProDefDetailDto> bpmProcDefDetailDTOS = bpmProcdefRepository.findBpmProcdefById(procDefId).stream().
                    map(bpmProdefTestMapper::entityToDto).collect(Collectors.toList());
            BpmProcdef bpmProcdef = bpmProcdefRepository.getById(procDefId);
            for (ProDefDetailDto bpmProcDefDetailDTO : bpmProcDefDetailDTOS) {

                List<BpmOwnerProcess> bpmOwnerProcessList = bpmOwnerProcessManager.bpmOwnerProcessByProcDefId(Long.valueOf(bpmProcDefDetailDTO.getId()));
                List<String> username = bpmOwnerProcessList.stream().map(BpmOwnerProcess::getIdUser).collect(Collectors.toList());
                List<BpmOwnerDto> ownerDtos = new ArrayList<>();

                // get user information from customer-service: /chart/getUserByEmail
                List<ChartInfoRoleResponse> chartInfoRoleResponses = responseUtils.getUserByEmail(username);
                if (!ValidationUtils.isNullOrEmpty(chartInfoRoleResponses)) {
                    chartInfoRoleResponses.forEach(e -> {
                        BpmOwnerDto bpmOwnerDto = new BpmOwnerDto(e.getId(), e.getUsername());
                        ownerDtos.add(bpmOwnerDto);
                    });
                }
                bpmProcDefDetailDTO.setOwners(ownerDtos);
                if (bpmProcdef != null) {
                    List<BpmProcdefInherits> inherits = bpmProcdefInheritsRepository.getBpmProcdefInheritsInfoHistory(bpmProcdef.getProcDefId());
                    List<BpmProcdefNotification> notifications = bpmProcdefNotificationRepository.findBpmProcdefNotificationsByProcDefIdAndStatus(bpmProcdef.getProcDefId(), "1");
                    // get by version
                    bpmProcDefDetailDTO.setBpmProcdefInherits(inherits);
                    bpmProcDefDetailDTO.setBpmProcdefNotifications(notifications);
                }
                // show list service mapping with process
                List<ServicePackage> servicePackageList = servicePackageManager.listByProcessId(Long.valueOf(bpmProcDefDetailDTO.getId()));
                List<ServiceNamePopUpResponse> serviceNamePopUp = new ArrayList<>();
                if (servicePackageList.size() > 0) {
                    for (ServicePackage servicePackage : servicePackageList) {
                        serviceNamePopUp.add(new ServiceNamePopUpResponse(String.valueOf(servicePackage.getId()), servicePackage.getServiceName(), servicePackage.getSubmissionType()));
                    }
                    bpmProcDefDetailDTO.setServiceNamePopUp(serviceNamePopUp);
                }
                //show apimanagement
                List<BpmProcdefApi> bpmProcdefApis = bpmProcdefApiRepository.findBpmProcdefApisByProcDefId(bpmProcdef.getProcDefId());
                List<BpmProcdefApiDto> bpmProcDefDetailDTOList = new ArrayList<>();
                for (BpmProcdefApi bpmProcdefApi : bpmProcdefApis) {
                    ApiManagement apiManagement = apiManagementRepository.getById(bpmProcdefApi.getApiId());
                    BpmProcdefApiDto apiDto = new BpmProcdefApiDto(bpmProcdefApi.getId(), bpmProcdefApi.getProcDefId(), bpmProcdefApi.getTaskDefKey(), bpmProcdefApi.getActionId(), bpmProcdefApi.getApiId(), bpmProcdefApi.getHeader(), bpmProcdefApi.getBody(), bpmProcdefApi.getStatus(), bpmProcdefApi.getCreatedUser(), bpmProcdefApi.getCreatedTime(), bpmProcdefApi.getUpdatedUser(), bpmProcdefApi.getUpdatedTime(),
                            apiManagement.getUrl(), apiManagement.getMethod(),
                            bpmProcdefApi.getCallOrder(), bpmProcdefApi.getSuccessCondition(),
                            bpmProcdefApi.getResponse(), bpmProcdefApi.getContinueOnError(),
                            bpmProcdefApi.getCallCondition(), false, bpmProcdefApi.getDescription());
                    bpmProcDefDetailDTOList.add(apiDto);
                }
                bpmProcDefDetailDTO.setBpmProcdefApiDtos(bpmProcDefDetailDTOList);

                bpmProcDefDetailDTO.setBytes(bpmProcdef.getBytes());
                bpmProcDefDetailDTO.setIsAutoCancel(bpmProcdef.getIsAutoCancel());
                bpmProcDefDetailDTO.setOffNotification(bpmProcdef.getOffNotification());
                bpmProcDefDetailDTO.setShowInputTask(bpmProcdef.getShowInputTask());
                bpmProcDefDetailDTO.setHideInherit(bpmProcdef.getHideInherit());
                bpmProcDefDetailDTO.setHideComment(bpmProcdef.getHideComment());
                bpmProcDefDetailDTO.setHideDownload(bpmProcdef.getHideDownload());
                bpmProcDefDetailDTO.setHideShareTicket(bpmProcdef.getHideShareTicket());
                bpmProcDefDetailDTO.setDisableApprovedTicket(bpmProcdef.getDisableApprovedTicket());
                bpmProcDefDetailDTO.setWarningApprovedTicket(bpmProcdef.getWarningApprovedTicket());
                bpmProcDefDetailDTO.setLegislativeRequirement(bpmProcdef.getLegislativeRequirement());

                // auto complete
                bpmProcDefDetailDTO.setAutoCompleteTask(bpmProcdef.getAutoCompleteTask());

                //API VIEW FILE
                List<BpmProcdefViewFileApi> bpmProcdefViewFileApis = bpmProcdefViewFileApiRepository.getAllByBpmProcdefIdAndProcDefId(Long.valueOf(bpmProcDefDetailDTO.getId()), bpmProcDefDetailDTO.getProcDefId());
                bpmProcDefDetailDTO.setBpmProcdefViewFileApis(bpmProcdefViewFileApis);

                //Cấu hình trạng thái nhiệm vụ theo bước
                List<BpmProcdefLegislativeStatusConfig> bpmProcdefLegislativeStatusConfigs = bpmProcdefLegislativeConfigRepository.getAllByBpmProcdefIdAndProcDefId(Long.valueOf(bpmProcDefDetailDTO.getId()), bpmProcDefDetailDTO.getProcDefId());
                bpmProcDefDetailDTO.setConfigLegislativeByStatus(bpmProcdefLegislativeStatusConfigs);

                List<BpmProcdefLegislativeTicketConfig> bpmProcdefLegislativeTicketConfigs = bpmProcdefLegislativeTicketConfigRepository.getAllByBpmProcdefIdAndProcDefId(Long.valueOf(bpmProcDefDetailDTO.getId()), bpmProcDefDetailDTO.getProcDefId());
                bpmProcDefDetailDTO.setConfigLegislativeTicket(bpmProcdefLegislativeTicketConfigs);

                // call camunda engine-rest get XML
                bpmProcDefDetailDTO.setFileXml(camundaEngineService.getXML(bpmProcDefDetailDTO.getProcDefId()));
                //convert and show  list cancel Task
                List<String> cancelTasks = ObjectUtils.toObject(bpmProcdef.getCancelTasks(), new TypeReference<>() {
                });
                List<String> hideInfoTasks = ObjectUtils.toObject(bpmProcdef.getHideInfoTasks(), new TypeReference<>() {
                });
                List<String> showInfoTasks = ObjectUtils.toObject(bpmProcdef.getShowInfoTaks(), new TypeReference<>() {
                });
                List<String> changeImplementValue = ObjectUtils.toObject(bpmProcdef.getChangeImplementerValue(), new TypeReference<>() {
                });
                List<String> showInputTaskDefKeys = ObjectUtils.toObject(bpmProcdef.getShowInputTaskDefKeys(), new TypeReference<>() {
                });
                List<String> hideRuTasks = ObjectUtils.toObject(bpmProcdef.getHideRuTasks(), new TypeReference<>() {
                });
                List<String> hideInheritTasks = ObjectUtils.toObject(bpmProcdef.getHideInheritTasks(), new TypeReference<>() {
                });
                List<String> hideCommentTasks = ObjectUtils.toObject(bpmProcdef.getHideCommentTasks(), new TypeReference<>() {
                });
                List<String> hideDownloadTasks = ObjectUtils.toObject(bpmProcdef.getHideDownloadTasks(), new TypeReference<>() {
                });
                String warningApprovedTicketTasks = bpmProcdef.getWarningApprovedTicketTasks();
                String disabledApprovedTicketTasks = bpmProcdef.getDisabledApprovedTicketTasks();

                if (!ValidationUtils.isNullOrEmpty(warningApprovedTicketTasks)) {
                    bpmProcDefDetailDTO.setListWarningApprovedTicketTasks(warningApprovedTicketTasks);
                }
                if (!ValidationUtils.isNullOrEmpty(disabledApprovedTicketTasks)) {
                    bpmProcDefDetailDTO.setListDisabledApprovedTicketTasks(disabledApprovedTicketTasks);
                }

                if (!ValidationUtils.isNullOrEmpty(cancelTasks)) {
                    bpmProcDefDetailDTO.setListCancelTasks(cancelTasks);
                }
                if (!ValidationUtils.isNullOrEmpty(changeImplementValue)) {
                    bpmProcDefDetailDTO.setListChangeImplementerValue(changeImplementValue);
                }
                if (!ValidationUtils.isNullOrEmpty(hideInfoTasks)) {
                    bpmProcDefDetailDTO.setListHideInfoTasks(hideInfoTasks);
                }
                if (!ValidationUtils.isNullOrEmpty(showInfoTasks)) {
                    bpmProcDefDetailDTO.setListShowInfoTasks(showInfoTasks);
                }
                if (!ValidationUtils.isNullOrEmpty(showInputTaskDefKeys)) {
                    bpmProcDefDetailDTO.setLstShowInputTaskDefKeys(showInputTaskDefKeys);
                }
                if (!ValidationUtils.isNullOrEmpty(hideRuTasks)) {
                    bpmProcDefDetailDTO.setListHideRuTasks(hideRuTasks);
                }
                if (!ValidationUtils.isNullOrEmpty(hideInheritTasks)) {
                    bpmProcDefDetailDTO.setListHideInheritTasks(hideInheritTasks);
                }

                if (!ValidationUtils.isNullOrEmpty(hideCommentTasks)) {
                    bpmProcDefDetailDTO.setListHideCommentTasks(hideCommentTasks);
                }

                // Ẩn download file trình ký
                if (!ValidationUtils.isNullOrEmpty(hideDownloadTasks)) {
                    bpmProcDefDetailDTO.setListHideDownloadTasks(hideDownloadTasks);
                }

                //convert and show listRelatedHideTicketValue
                List<Integer> listHideTicketValue = ObjectUtils.toObject(bpmProcdef.getHideRelatedTicketValue(), new TypeReference<>() {
                });
                if (!ValidationUtils.isNullOrEmpty(listHideTicketValue)) {
                    bpmProcDefDetailDTO.setListHideRelatedTicketValue(listHideTicketValue);
                }

                //convert and show listAuthorityOnTicket
                List<Integer> listAuthorityOnTicket = ObjectUtils.toObject(bpmProcdef.getAuthorityOnTicketValue(), new TypeReference<>() {
                });
                if (!ValidationUtils.isNullOrEmpty(listAuthorityOnTicket)) {
                    bpmProcDefDetailDTO.setListAuthorityOnTicketValue(listAuthorityOnTicket);
                }

                List<String> listAuthorityOnTicketStep = ObjectUtils.toObject(bpmProcdef.getAuthorityOnTicketStep(), new TypeReference<>() {
                });
                if (!ValidationUtils.isNullOrEmpty(listAuthorityOnTicketStep)) {
                    bpmProcDefDetailDTO.setListAuthorityOnTicketStep(listAuthorityOnTicketStep);
                }
                // show list share user
                List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceIdAndReferenceType(bpmProcdef.getId(), ShareUserTypeEnum.PROCESS.type);
                List<String> listEmail = new ArrayList<>();
                if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                    listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(bpmProcdef.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
                }
                bpmProcDefDetailDTO.setShareWith(listEmail);

                // get list applyFor
                List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(procDefId, PermissionDataConstants.Type.BPM_PROCDEF.code);
                List<String> companyCodes = permissionDataManagements.stream().map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
                if (!ValidationUtils.isNullOrEmpty(companyCodes)) {
                    bpmProcDefDetailDTO.setApplyFor(companyCodes);
                }

                // Special clone form key
                bpmProcDefDetailDTO.setSpecialFlow(bpmProcdef.getSpecialFlow());
                bpmProcDefDetailDTO.setSpecialParentId(bpmProcdef.getSpecialParentId());
                bpmProcDefDetailDTO.setSpecialCompanyCode(bpmProcdef.getSpecialCompanyCode());
                List<String> lstFormKey = ObjectUtils.toObject(bpmProcdef.getSpecialFormKey(), new TypeReference<>() {
                });
                bpmProcDefDetailDTO.setListSpecialFormKey(lstFormKey);
            }
            return bpmProcDefDetailDTOS;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    //UPDATE PROC DEF
    public BpmProcdef updateProcessInBPM(CreateBPMRequest createBPMRequest) {
        try {
            List<Map<String, Object>> mapResponseGet = camundaEngineService.getProcessDefinitionByDeploymentId(createBPMRequest.getDeploymentId());
            Map<String, Object> stringObjectMap = !mapResponseGet.isEmpty() ? mapResponseGet.get(0) : null;
            String procDefId = stringObjectMap != null ? stringObjectMap.get("id").toString() : null;
            String key = stringObjectMap != null ? stringObjectMap.get("key").toString() : null;
            String resource = stringObjectMap != null ? stringObjectMap.get("resource").toString() : createBPMRequest.getFileName();
            String deployment = stringObjectMap != null ? stringObjectMap.get("deploymentId").toString() : createBPMRequest.getDeploymentId();

            String cancelTasks = new Gson().toJson(createBPMRequest.getCancelTasks());
            String hideInfoTasks = new Gson().toJson(createBPMRequest.getHideInfoTasks());
            String showInfoTasks = new Gson().toJson(createBPMRequest.getShowInfoTasks());
            String hideTicketValue = new Gson().toJson(createBPMRequest.getHideRelatedTicketValue());
            String changeImplementerValue = new Gson().toJson(createBPMRequest.getChangeImplementerValue());
            String authorityOnTicketStep = new Gson().toJson(createBPMRequest.getAuthorityOnTicketStep());
            String authorityOnTicketValue = new Gson().toJson(createBPMRequest.getAuthorityOnTicketValue());
            String showInputTaskDefKeys = new Gson().toJson(createBPMRequest.getShowInputTaskDefKeys());
            String hideRuTasks = new Gson().toJson(createBPMRequest.getHideRuTasks());
            String hideInheritTasks = new Gson().toJson(createBPMRequest.getHideInheritTasks());
            String hideCommentTasks = new Gson().toJson(createBPMRequest.getHideCommentTasks());
            String hideDownloadTasks = new Gson().toJson(createBPMRequest.getHideDownloadTasks());
            String warningApprovedTicketTasks = createBPMRequest.getWarningApprovedTicketTasks();
            String disabledApprovedTicketTasks = createBPMRequest.getDisabledApprovedTicketTasks();

            byte[] bytes = null;
            if (createBPMRequest.getFileName() != null) {
                //lấy file từ minio
                bytes = fileManager.getFile(bucket, createBPMRequest.getFileName());
            }

            //Get user login
            BpmProcdef bpmProcdef = bpmProcdefRepository.findById(createBPMRequest.getId()).get();
            if (procDefId == null) {
                procDefId = bpmProcdef.getProcDefId();
            }

            String contentEdit = handleContentUpdateBPM(createBPMRequest, bpmProcdef, bytes);

            String oldProdefId = bpmProcdef.getProcDefId();

            //Update template
            List<GroupTableProTemp> bpmGroupTableProTemps = new ArrayList<>();
            List<GroupTableProTemp> listOld = groupProcTempRepository.findByProDefId(bpmProcdef.getProcDefId());
            if (createBPMRequest.getTemplateId() != null) {
                if (listOld != null) {
                    listOld.stream().forEach(i -> groupProcTempRepository.deleteByProDefId(i.getProDefId()));
                }
                for (String formKey : createBPMRequest.getTemplateId()) {
                    GroupTableProTemp groupTableProTemp = new GroupTableProTemp();
                    groupTableProTemp.setProDefId(bpmProcdef.getProcDefId());
                    groupTableProTemp.setFormKey(formKey);
                    bpmGroupTableProTemps.add(groupTableProTemp);
                }
                updateTemplateStatus(createBPMRequest.getTemplateId());
                groupProcTempRepository.saveAll(bpmGroupTableProTemps);
            }

            //Update BpmProcDef

            Integer type = ValidationUtils.isNullOrEmpty(createBPMRequest.getProcessType()) ? 0 : createBPMRequest.getProcessType();
            bpmProcdef.setProcessType(type);
            bpmProcdef.setProcDefId(procDefId);
            bpmProcdef.setName(createBPMRequest.getName());
            bpmProcdef.setDescription(createBPMRequest.getDescription());
            bpmProcdef.setKey(key);
            bpmProcdef.setResourceName(resource);
            bpmProcdef.setBytes(bytes);
            bpmProcdef.setDeploymentId(deployment);
            bpmProcdef.setPrioritized(createBPMRequest.getPrioritized());
            bpmProcdef.setUserUpdate(credentialHelper.getJWTPayload().getUsername());
            bpmProcdef.setUpdatedDate(LocalDateTime.now());
            bpmProcdef.setAutoClose(createBPMRequest.getAutoClose());
            bpmProcdef.setAutoCancel(createBPMRequest.getAutoCancel());
            bpmProcdef.setLocation(createBPMRequest.getLocation());
            bpmProcdef.setInFormTo(createBPMRequest.getInFormTo());
            bpmProcdef.setStepByStepResultForCreate(createBPMRequest.getStepByStepResultForCreate());
            bpmProcdef.setUpdate(createBPMRequest.getUpdate());
            bpmProcdef.setRequestUpdate(createBPMRequest.getRequestUpdate());
            bpmProcdef.setHideRuTasks(hideRuTasks);
            bpmProcdef.setCreateNewAndDouble(createBPMRequest.getCreateNewAndDouble());
            bpmProcdef.setPriorityId(createBPMRequest.getPriorityId());
            bpmProcdef.setCancel(createBPMRequest.getCancel());
            bpmProcdef.setCancelTasks(cancelTasks);
            bpmProcdef.setChangeImplementerValue(changeImplementerValue);

            bpmProcdef.setShowInfo(createBPMRequest.getShowInfo());
            bpmProcdef.setHideInfo(createBPMRequest.getHideInfo());
            bpmProcdef.setIsAssistant(createBPMRequest.getIsAssistant());
            bpmProcdef.setIsEditAssistant(createBPMRequest.getIsEditAssistant());
            bpmProcdef.setHideInfoTasks(hideInfoTasks);
            bpmProcdef.setShowInfoTaks(showInfoTasks);
            bpmProcdef.setIsAutoCancel(createBPMRequest.getIsAutoCancel());

            // auto complete
            bpmProcdef.setAutoCompleteTask(createBPMRequest.getAutoCompleteTask());

            bpmProcdef.setAdditionalRequest(createBPMRequest.getAdditionalRequest());
            bpmProcdef.setHideRelatedTicket(createBPMRequest.getHideRelatedTicket());
            bpmProcdef.setHideRelatedTicketValue(hideTicketValue);
            bpmProcdef.setAuthorityOnTicket(createBPMRequest.getAuthorityOnTicket());
            bpmProcdef.setAuthorityOnTicketValue(authorityOnTicketValue);
            bpmProcdef.setAuthorityOnTicketStep(authorityOnTicketStep);
            bpmProcdef.setRecall(createBPMRequest.getRecall());
            bpmProcdef.setShowInputTask(createBPMRequest.getShowInputTask());
            bpmProcdef.setShowInputTaskDefKeys(showInputTaskDefKeys);

            //Nút ẩn kế thừa
            bpmProcdef.setHideInherit(createBPMRequest.getHideInherit());
            bpmProcdef.setHideInheritTasks(hideInheritTasks);

            bpmProcdef.setHideComment(createBPMRequest.getHideComment());
            bpmProcdef.setHideCommentTasks(hideCommentTasks);

            // Ẩn download file trình ký
            bpmProcdef.setHideDownload(createBPMRequest.getHideDownload());
            bpmProcdef.setHideDownloadTasks(hideDownloadTasks);

            // Ẩn nút Chia sẻ
            bpmProcdef.setHideShareTicket(createBPMRequest.getHideShareTicket());

            //HMTC
            bpmProcdef.setDisableApprovedTicket(createBPMRequest.getDisableApprovedTicket());
            bpmProcdef.setWarningApprovedTicket(createBPMRequest.getWarningApprovedTicket());
            bpmProcdef.setDisabledApprovedTicketTasks(disabledApprovedTicketTasks);
            bpmProcdef.setWarningApprovedTicketTasks(warningApprovedTicketTasks);

            //Legislative
            bpmProcdef.setLegislativeRequirement(createBPMRequest.getLegislativeRequirement());

            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getAutoInherits())) {
                bpmProcdef.setAutoInherits(createBPMRequest.getAutoInherits());
            }
            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getOffNotification())) {
                bpmProcdef.setOffNotification(createBPMRequest.getOffNotification());
            }
            bpmProcdef = bpmProcdefRepository.save(bpmProcdef);

            // save procDef history
            BpmProcDefHistory bpmProcdefHistory = new BpmProcDefHistory();
            bpmProcdefHistory.setProcessType(createBPMRequest.getProcessType());
            bpmProcdefHistory.setProcDefId(procDefId);
            bpmProcdefHistory.setName(createBPMRequest.getName());
            bpmProcdefHistory.setDescription(createBPMRequest.getDescription());
            bpmProcdefHistory.setKey(key);
            bpmProcdefHistory.setDeploymentId(deployment);
            bpmProcdefHistory.setResourceName(resource);
            bpmProcdefHistory.setBytes(bytes);
            bpmProcdefHistory.setPrioritized(createBPMRequest.getPrioritized());
            bpmProcdefHistory.setUserCreated(credentialHelper.getJWTPayload().getUsername());
            bpmProcdefHistory.setCreatedDate(LocalDateTime.now());
            bpmProcdefHistory.setAutoClose(createBPMRequest.getAutoClose());
            bpmProcdefHistory.setAutoCancel(createBPMRequest.getAutoCancel());
            bpmProcdefHistory.setStatus(bpmProcdef.getStatus());
            bpmProcdefHistory.setLocation(createBPMRequest.getLocation());
            bpmProcdefHistory.setInFormTo(createBPMRequest.getInFormTo());
            bpmProcdefHistory.setStepByStepResultForCreate(createBPMRequest.getStepByStepResultForCreate());
            bpmProcdefHistory.setUpdate(createBPMRequest.getUpdate());
            bpmProcdefHistory.setRequestUpdate(createBPMRequest.getRequestUpdate());
            bpmProcdefHistory.setHideRuTasks(hideRuTasks);
            bpmProcdefHistory.setCreateNewAndDouble(createBPMRequest.getCreateNewAndDouble());
            bpmProcdefHistory.setPriorityId(createBPMRequest.getPriorityId());
            bpmProcdefHistory.setCancel(createBPMRequest.getCancel());
            bpmProcdefHistory.setCancelTasks(cancelTasks);
            bpmProcdefHistory.setChangeImplementerValue(changeImplementerValue);
            bpmProcdefHistory.setShowInfo(createBPMRequest.getShowInfo());
            bpmProcdefHistory.setHideInfo(createBPMRequest.getHideInfo());
            bpmProcdefHistory.setIsAssistant(createBPMRequest.getIsAssistant());
            bpmProcdefHistory.setIsEditAssistant(createBPMRequest.getIsEditAssistant());
            bpmProcdefHistory.setHideInfoTasks(hideInfoTasks);
            bpmProcdefHistory.setShowInfoTaks(showInfoTasks);
            bpmProcdefHistory.setIsAutoCancel(createBPMRequest.getIsAutoCancel());
            bpmProcdefHistory.setAdditionalRequest(createBPMRequest.getAdditionalRequest());
            bpmProcdefHistory.setHideRelatedTicket(createBPMRequest.getHideRelatedTicket());
            bpmProcdefHistory.setHideRelatedTicketValue(hideTicketValue);
            bpmProcdefHistory.setAuthorityOnTicket(createBPMRequest.getAuthorityOnTicket());
            bpmProcdefHistory.setAuthorityOnTicketValue(authorityOnTicketValue);
            bpmProcdefHistory.setAuthorityOnTicketStep(authorityOnTicketStep);
            bpmProcdefHistory.setRecall(createBPMRequest.getRecall());
            bpmProcdefHistory.setShowInputTask(createBPMRequest.getShowInputTask());
            bpmProcdefHistory.setShowInputTaskDefKeys(showInputTaskDefKeys);
            bpmProcdefHistory.setHideInherit(createBPMRequest.getHideInherit());
            bpmProcdefHistory.setHideInheritTasks(hideInheritTasks);
            bpmProcdefHistory.setHideComment(createBPMRequest.getHideComment());
            bpmProcdefHistory.setHideCommentTasks(hideCommentTasks);
            bpmProcdefHistory.setHideDownload(createBPMRequest.getHideDownload());
            bpmProcdefHistory.setHideDownloadTasks(hideDownloadTasks);
            bpmProcdefHistory.setHideShareTicket(createBPMRequest.getHideShareTicket());
            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getAutoInherits())) {
                bpmProcdefHistory.setAutoInherits(createBPMRequest.getAutoInherits());
            }
            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getOffNotification())) {
                bpmProcdefHistory.setOffNotification(createBPMRequest.getOffNotification());
            }
            bpmProcdefHistory.setCompanyCode(bpmProcdef.getCompanyCode());
            bpmProcdefHistory.setCompanyName(bpmProcdef.getCompanyName());
            bpmProcdefHistory.setAutoCompleteTask(bpmProcdef.getAutoCompleteTask());
            bpmProcdefHistory.setDisableApprovedTicket(createBPMRequest.getDisableApprovedTicket());
            bpmProcdefHistory.setWarningApprovedTicket(createBPMRequest.getWarningApprovedTicket());
            bpmProcdefHistory.setDisabledApprovedTicketTasks(disabledApprovedTicketTasks);
            bpmProcdefHistory.setWarningApprovedTicketTasks(warningApprovedTicketTasks);
            bpmProcdefHistory.setLegislativeRequirement(createBPMRequest.getLegislativeRequirement());


            bpmProcdefHistory.setOrgProcessId(bpmProcdef.getId());
            bpmProcdefHistory.setContentEdit(contentEdit);

            Integer version = Math.toIntExact(bpmProcDefHistoryRepository.countByOrgProcessId(bpmProcdef.getId()));

            bpmProcdefHistory.setVersion("V" + version);

            List<BpmProcDefHistory> listOldHistory = bpmProcDefHistoryRepository.findByOrgProcessId(bpmProcdef.getId());
            for (BpmProcDefHistory history : listOldHistory) {
                history.setStatusHistory(false);
            }
            bpmProcdefHistory.setStatusHistory(true);

            bpmProcDefHistoryRepository.saveAll(listOldHistory);

            // Update procDefId mtk
            List<BpmTemplatePrint> lstTemplatePrint = bpmTemplatePrintRepository.getByProcDefId(oldProdefId);
            if (!ValidationUtils.isNullOrEmpty(lstTemplatePrint)) {
                for (BpmTemplatePrint bpmTemplatePrint : lstTemplatePrint) {
                    bpmTemplatePrint.setProcDefId(bpmProcdef.getProcDefId());

                    // Update procDefId bpmTpTask
                    List<BpmTpTask> lstBpmTpTask = bpmTpTaskRepository.getByTemplatePrintIdAndProcDefId(bpmTemplatePrint.getId(), oldProdefId);
                    if (!ValidationUtils.isNullOrEmpty(lstBpmTpTask)) {
                        for (BpmTpTask bpmTask : lstBpmTpTask) {
                            bpmTask.setProcDefId(bpmProcdef.getProcDefId());
                        }
                        bpmTpTaskRepository.saveAll(lstBpmTpTask);
                    }
                }
                bpmTemplatePrintRepository.saveAll(lstTemplatePrint);
            }

            //Update grouptableProctemp
            List<GroupTableProTemp> groupTableProTempList = groupProcTempRepository.findByProDefId(oldProdefId);
            for (GroupTableProTemp groupTableProTemp : groupTableProTempList) {
                groupTableProTemp.setProDefId(procDefId);
            }
            groupProcTempRepository.saveAll(groupTableProTempList);

            Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();
            Long id = bpmProcdef.getId();
            bpmOwnerProcessManager.deleteBpmOwnerProcessByProcDefId(id);
            List<BpmOwnerProcess> bpmOwnerProcessList = new ArrayList<>();
            for (String owner : createBPMRequest.getOwners()) {
                BpmOwnerProcess bpmOwnerProcess = new BpmOwnerProcess();
                bpmOwnerProcess.setProcDefId(bpmProcdef.getId());
                bpmOwnerProcess.setIdUser(owner);
                bpmOwnerProcessList.add(bpmOwnerProcess);
            }
            bpmOwnerProcessManager.saveAll(bpmOwnerProcessList);
            bpmProcdefHistory.setProcessOwner(g.toJson(bpmOwnerProcessList));

            List<BpmProcdefApi> bpmProcdefApis = new ArrayList<>();

            for (BpmProcdefApiDto bpmProcdefApiDto : createBPMRequest.getBpmProcdefApiList()) {
                // ko xóa mới tạo bản ghi version mới
                if (bpmProcdefApiDto.getIsDelete() == null || !bpmProcdefApiDto.getIsDelete()) {
                    BpmProcdefApi bpmProcdefApi = new BpmProcdefApi();
                    bpmProcdefApi.setProcDefId(procDefId);
                    bpmProcdefApi.setTaskDefKey(bpmProcdefApiDto.getTaskDefKey());
                    bpmProcdefApi.setActionId(bpmProcdefApiDto.getActionId());
                    bpmProcdefApi.setApiId(bpmProcdefApiDto.getApiId());
                    bpmProcdefApi.setHeader(bpmProcdefApiDto.getHeader());
                    bpmProcdefApi.setBody(bpmProcdefApiDto.getBody());
                    bpmProcdefApi.setBpmProcdefId(bpmProcdef.getId());
                    bpmProcdefApi.setStatus(1L);
                    bpmProcdefApi.setCallOrder(bpmProcdefApiDto.getCallOrder());
                    bpmProcdefApi.setSuccessCondition(bpmProcdefApiDto.getSuccessCondition());
                    bpmProcdefApi.setResponse(bpmProcdefApiDto.getResponse());
                    bpmProcdefApi.setContinueOnError(bpmProcdefApiDto.getContinueOnError());
                    bpmProcdefApi.setCallCondition(bpmProcdefApiDto.getCallCondition());
                    bpmProcdefApi.setDescription(bpmProcdefApiDto.getDescription());
                    bpmProcdefApis.add(bpmProcdefApi);
                }
            }

            bpmProcdefApiRepository.saveAll(bpmProcdefApis);

            //Lưu cấu hình kế thừa -> tạo mới version theo procDefId
            List<BpmProcdefInherits> bpmProcdefInherits = new ArrayList<>();
            BpmProcdefInherits procdefInherits;
            for (BpmProcdefInheritsDTO bpmProcdefInheritsDTO : createBPMRequest.getBpmProcdefInherits()) {
                procdefInherits = new BpmProcdefInherits();
                procdefInherits.setProcDefId(procDefId);
                procdefInherits.setTaskDefKey(bpmProcdefInheritsDTO.getTaskDefKey());
                procdefInherits.setTaskDefKey(bpmProcdefInheritsDTO.getTaskDefKey());
                procdefInherits.setTaskDefKeyInherits(bpmProcdefInheritsDTO.getTaskDefKeyInherits());
                procdefInherits.setFieldInherits(bpmProcdefInheritsDTO.getFieldInherits());
                procdefInherits.setCreatedTime(LocalDateTime.now());
                procdefInherits.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                procdefInherits.setBpmProcdefId(bpmProcdef.getId());
                procdefInherits.setStatus("1");
                bpmProcdefInherits.add(procdefInherits);
            }
            bpmProcdefInheritsRepository.saveAll(bpmProcdefInherits);


            //Lưu cấu hình thông báo -> tạo mới version theo procDefId
            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getBpmProcdefNotifications())) {
                List<BpmProcdefNotification> bpmProcdefNotifications = new ArrayList<>();
                BpmProcdefNotification procdefNotification = null;
                for (BpmProcdefNotificationDTO bpmProcdefNotificationDTO : createBPMRequest.getBpmProcdefNotifications()) {
                    List<BpmProcdefNotificationDetail> bpmProcdefNotificationDetails = new ArrayList<>();
                    procdefNotification = new BpmProcdefNotification();
                    procdefNotification.setProcDefId(procDefId);
                    procdefNotification.setActionCode(bpmProcdefNotificationDTO.getActionCode());
                    procdefNotification.setNotificationObject(bpmProcdefNotificationDTO.getNotificationObject());
                    procdefNotification.setNotificationTemplateId(bpmProcdefNotificationDTO.getNotificationTemplateId());
                    procdefNotification.setTaskDefKey(bpmProcdefNotificationDTO.getTaskDefKey());
                    procdefNotification.setTaskDefKeyNotification(bpmProcdefNotificationDTO.getTaskDefKeyNotification());
                    procdefNotification.setFieldNotification(bpmProcdefNotificationDTO.getFieldNotification());
                    procdefNotification.setCreatedTime(LocalDateTime.now());
                    procdefNotification.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    procdefNotification.setBpmProcdefId(bpmProcdef.getId());
                    procdefNotification.setStatus("1");
                    procdefNotification.setAddMoreConfig(bpmProcdefNotificationDTO.getAddMoreConfig());
                    procdefNotification.setOffNotification(bpmProcdefNotificationDTO.getOffNotification());
                    bpmProcdefNotifications.add(procdefNotification);
                }
                //save all bản ghi
                bpmProcdefNotificationRepository.saveAll(bpmProcdefNotifications);
            }


            //Lưu cấu view file từ API -> bỏ xóa cấu hình
            List<BpmProcdefViewFileApi> bpmProcdefViewFileApis = new ArrayList<>();
            if (createBPMRequest.getViewFileApi() != null) {
                BpmProcdefViewFileApi bpmProcdefViewFileApi;
                for (BpmProcdefViewFileApi dto : createBPMRequest.getViewFileApi()) {
                    if (dto.getStatus().equals(Boolean.TRUE)) {
                        bpmProcdefViewFileApi = new BpmProcdefViewFileApi();
                        if (dto.getBpmProcdefId() != null && dto.getProcDefId().equals(procDefId)) { // Quy trình update thì lưu thêm 1 bản ghi mới
                            bpmProcdefViewFileApi.setId(dto.getId());
                        }
                        bpmProcdefViewFileApi.setBaseUrl(dto.getBaseUrl());
                        bpmProcdefViewFileApi.setBody(dto.getBody());
                        bpmProcdefViewFileApi.setButtonName(dto.getButtonName());
                        bpmProcdefViewFileApi.setHeader(dto.getHeader());
                        bpmProcdefViewFileApi.setResponse(dto.getResponse());
                        bpmProcdefViewFileApi.setMethod(dto.getMethod());
                        bpmProcdefViewFileApi.setTaskDefKey(dto.getTaskDefKey());
                        bpmProcdefViewFileApi.setUrl(dto.getUrl());
                        bpmProcdefViewFileApi.setStatus(dto.getStatus());
                        bpmProcdefViewFileApi.setProcDefId(procDefId);
                        bpmProcdefViewFileApi.setDisplayCondition(dto.getDisplayCondition());
                        bpmProcdefViewFileApi.setShowButtonInCreateTicket(dto.getShowButtonInCreateTicket());

                        bpmProcdefViewFileApi.setBpmProcdefId(bpmProcdef.getId());
                        if (dto.getId() != null) {
                            bpmProcdefViewFileApi.setCreatedTime(LocalDateTime.now());
                            bpmProcdefViewFileApi.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                        }
                        bpmProcdefViewFileApis.add(bpmProcdefViewFileApi);
                    }
                }
            }
            if (!ValidationUtils.isNullOrEmpty(bpmProcdefViewFileApis)) {
                bpmProcdefViewFileApiRepository.saveAll(bpmProcdefViewFileApis);
            }

            //Cấu hình trạng thái nhiệm vụ theo bước
            List<BpmProcdefLegislativeStatusConfig> bpmProcdefLegislativeStatusConfigs = new ArrayList<>();
            if (createBPMRequest.getConfigLegislativeByStatus() != null) {
                List<Long> deleteList = new ArrayList<>();
                for (BpmProcdefLegislativeStatusConfig dto : createBPMRequest.getConfigLegislativeByStatus()) {
                    BpmProcdefLegislativeStatusConfig bpmProcdefLegislativeStatusConfig = new BpmProcdefLegislativeStatusConfig();
                    if (dto.getStatus().equals(Boolean.TRUE)) {
                        bpmProcdefLegislativeStatusConfig.setBpmProcdefId(bpmProcdef.getId());
                        bpmProcdefLegislativeStatusConfig.setStatus(Boolean.TRUE);
                        bpmProcdefLegislativeStatusConfig.setTaskDefKey(dto.getTaskDefKey());
                        bpmProcdefLegislativeStatusConfig.setLegislativeStatus(dto.getLegislativeStatus());
                        bpmProcdefLegislativeStatusConfig.setTaskStatus(dto.getTaskStatus());
                        bpmProcdefLegislativeStatusConfig.setProcDefId(procDefId);
                        if (dto.getId() != null) {
                            bpmProcdefLegislativeStatusConfig.setCreatedTime(LocalDateTime.now());
                            bpmProcdefLegislativeStatusConfig.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                        } else {
                            bpmProcdefLegislativeStatusConfig.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
                            bpmProcdefLegislativeStatusConfig.setUpdatedTime(LocalDateTime.now());
                        }
                        bpmProcdefLegislativeStatusConfigs.add(bpmProcdefLegislativeStatusConfig);
                    } else deleteList.add(dto.getId());
                }
                bpmProcdefLegislativeConfigRepository.deleteAllById(deleteList);
            }
            if (!ValidationUtils.isNullOrEmpty(bpmProcdefLegislativeStatusConfigs)) {
                bpmProcdefLegislativeConfigRepository.saveAll(bpmProcdefLegislativeStatusConfigs);
            }

            //
            List<BpmProcdefLegislativeTicketConfig> bpmProcdefLegislativeTicketConfigs = new ArrayList<>();
            if (createBPMRequest.getConfigLegislativeByStatus() != null) {
                List<Long> deleteList = new ArrayList<>();
                for (BpmProcdefLegislativeTicketConfig dto : createBPMRequest.getConfigLegislativeTicket()) {
                    BpmProcdefLegislativeTicketConfig bpmProcdefLegislativeTicketConfig = new BpmProcdefLegislativeTicketConfig();
                    if (dto.getStatus().equals(Boolean.TRUE)) {
                        bpmProcdefLegislativeTicketConfig.setBpmProcdefId(bpmProcdef.getId());
                        bpmProcdefLegislativeTicketConfig.setStatus(Boolean.TRUE);
                        bpmProcdefLegislativeTicketConfig.setTicketField(dto.getTicketField());
                        bpmProcdefLegislativeTicketConfig.setLegislativeField(dto.getLegislativeField());

                        bpmProcdefLegislativeTicketConfig.setProcDefId(procDefId);
                        if (dto.getId() != null) {
                            bpmProcdefLegislativeTicketConfig.setCreatedTime(LocalDateTime.now());
                            bpmProcdefLegislativeTicketConfig.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                        } else {
                            bpmProcdefLegislativeTicketConfig.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
                            bpmProcdefLegislativeTicketConfig.setUpdatedTime(LocalDateTime.now());
                        }
                        bpmProcdefLegislativeTicketConfigs.add(bpmProcdefLegislativeTicketConfig);
                    } else deleteList.add(dto.getId());
                }
                bpmProcdefLegislativeTicketConfigRepository.deleteAllById(deleteList);
            }
            if (!ValidationUtils.isNullOrEmpty(bpmProcdefLegislativeTicketConfigs)) {
                bpmProcdefLegislativeTicketConfigRepository.saveAll(bpmProcdefLegislativeTicketConfigs);
            }

            // share user
            shareUserRepository.deleteAllByReferenceIdAndReferenceType(bpmProcdef.getId(), ShareUserTypeEnum.PROCESS.type);
            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getShareWith())) {
                List<SharedUser> sharedUsers = new ArrayList<>();
                for (String shareWith : createBPMRequest.getShareWith()) {
                    SharedUser sharedUser = new SharedUser();
                    sharedUser.setReferenceId(bpmProcdef.getId());
                    sharedUser.setReferenceType(ShareUserTypeEnum.PROCESS.type);
                    sharedUser.setEmail(shareWith);
                    sharedUsers.add(sharedUser);
                }
                shareUserRepository.saveAll(sharedUsers);
                bpmProcdefHistory.setShareWith(g.toJson(sharedUsers));
            }

            // Lưu phân quyền dữ liệu
            // Xóa data cũ
            List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(id, PermissionDataConstants.Type.BPM_PROCDEF.code);
            if (!ValidationUtils.isNullOrEmpty(oldData)) {
                permissionDataManagementRepository.deleteAll(oldData);
            }
            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : createBPMRequest.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(id);
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.BPM_PROCDEF.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                permissionDataManagementRepository.saveAll(permissionDataManagements);
                bpmProcdefHistory.setApplyFor(g.toJson(permissionDataManagements));
            }

            bpmProcDefHistoryRepository.save(bpmProcdefHistory);

            return bpmProcdef;
        } catch (Exception e) {
            log.error(e.getMessage(), e);

            return null;
        }
    }

    private String handleContentUpdateBPM(CreateBPMRequest request, BpmProcdef bpmProcdef, byte[] bytes) {
        StringBuilder builder = new StringBuilder();

        String cancelTasks = new Gson().toJson(request.getCancelTasks());
        String hideInfoTasks = new Gson().toJson(request.getHideInfoTasks());
        String showInfoTasks = new Gson().toJson(request.getShowInfoTasks());
        String hideTicketValue = new Gson().toJson(request.getHideRelatedTicketValue());
        String changeImplementerValue = new Gson().toJson(request.getChangeImplementerValue());
        String authorityOnTicketStep = new Gson().toJson(request.getAuthorityOnTicketStep());
        String authorityOnTicketValue = new Gson().toJson(request.getAuthorityOnTicketValue());
        String showInputTaskDefKeys = new Gson().toJson(request.getShowInputTaskDefKeys());
        String hideRuTasks = new Gson().toJson(request.getHideRuTasks());
        String hideInheritTasks = new Gson().toJson(request.getHideInheritTasks());
        String hideCommentTasks = new Gson().toJson(request.getHideCommentTasks());
        String hideDownloadTasks = new Gson().toJson(request.getHideDownloadTasks());

        builder.append("Nâng phiên bản lịch sử");
        builder.append(System.lineSeparator());

        if (!Arrays.equals(bytes, bpmProcdef.getBytes())) {
            builder.append("Thay đổi Luồng nghiệp vụ");
            builder.append(System.lineSeparator());
        }

        if (!request.getName().equalsIgnoreCase(bpmProcdef.getName())) {
            builder.append("Thay đổi Tên quy trình");
            builder.append(System.lineSeparator());
        }

        if (request.getDescription() != null && !request.getDescription().equalsIgnoreCase(bpmProcdef.getDescription())) {
            builder.append("Thay đổi Mô tả");
            builder.append(System.lineSeparator());
        }

        if (request.getPriorityId() != null && !request.getPriorityId().equals(bpmProcdef.getPriorityId())) {
            builder.append("Thay đổi Độ ưu tiên");
            builder.append(System.lineSeparator());
        }

        List<String> ownerProcess = bpmOwnerProcessRepository.getOwnerByProcDefId(bpmProcdef.getId());
        if (!request.getOwners().equals(ownerProcess)) {
            builder.append("Thay đổi Chủ sở hữu quy trình");
            builder.append(System.lineSeparator());
        }

        List<String> sharedUser = shareUserRepository.getShareUserByReferenceId(ShareUserTypeEnum.PROCESS.type, bpmProcdef.getId());
        if (!request.getShareWith().equals(sharedUser)) {
            builder.append("Thay đổi Chia sẻ với người dùng");
            builder.append(System.lineSeparator());
        }

        List<String> applyFors = permissionDataManagementRepository.getApplyForByTypeId(PermissionDataConstants.Type.BPM_PROCDEF.code, bpmProcdef.getId());
        if (!request.getApplyFor().equals(applyFors)) {
            builder.append("Thay đổi Cho phép IT ở các công ty chỉnh sửa");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getAutoCancel(), bpmProcdef.getAutoCancel())
                || !Objects.equals(request.getIsAutoCancel(), bpmProcdef.getIsAutoCancel())
        ) {
            builder.append("Thay đổi Tự động hủy");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getAutoClose(), bpmProcdef.getAutoClose())) {
            builder.append("Thay đổi Tự động đóng");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getStepByStepResultForCreate(), bpmProcdef.getStepByStepResultForCreate())) {
            builder.append("Thay đổi Ẩn kết quả từng bước đối với người tạo yêu cầu");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getInFormTo(), bpmProcdef.getInFormTo())) {
            builder.append("Thay đổi Ẩn trường Thông báo cho");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getHideRelatedTicket(), bpmProcdef.getHideRelatedTicket())
                || !Objects.equals(hideTicketValue, bpmProcdef.getHideRelatedTicketValue())
        ) {
            builder.append("Thay đổi Tờ trình liên quan");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(changeImplementerValue, bpmProcdef.getChangeImplementerValue())) {
            builder.append("Thay đổi Ủy quyền");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getCancel(), bpmProcdef.getCancel())
                || !Objects.equals(cancelTasks, bpmProcdef.getCancelTasks())
        ) {
            builder.append("Thay đổi Vô hiệu hóa Hủy phiếu yêu cầu");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getAdditionalRequest(), bpmProcdef.getAdditionalRequest())) {
            builder.append("Thay đổi Vô hiệu hóa Yêu cầu bổ sung");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getRequestUpdate(), bpmProcdef.getRequestUpdate())
                || !Objects.equals(hideRuTasks, bpmProcdef.getHideRuTasks())
        ) {
            builder.append("Thay đổi Vô hiệu hóa Trả về");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getRecall(), bpmProcdef.getRecall())) {
            builder.append("Thay đổi Vô hiệu hóa Thu hồi");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getCreateNewAndDouble(), bpmProcdef.getCreateNewAndDouble())) {
            builder.append("Thay đổi Vô hiệu hóa Tạo yêu cầu mới và Nhân bản");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getAutoInherits(), bpmProcdef.getAutoInherits())) {
            builder.append("Thay đổi Vô hiệu hóa tự động kế thừa");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getOffNotification(), bpmProcdef.getOffNotification())) {
            builder.append("Thay đổi Tắt tất cả thông báo");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getShowInfo(), bpmProcdef.getShowInfo())
                || !Objects.equals(showInfoTasks, bpmProcdef.getShowInfoTaks())
        ) {
            builder.append("Thay đổi Hiển thị thông tin đầu vào/ đầu ra");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getHideInfo(), bpmProcdef.getHideInfo())
                || !Objects.equals(hideInfoTasks, bpmProcdef.getHideInfoTasks())
        ) {
            builder.append("Thay đổi Ẩn thông tin đầu vào");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getShowInputTask(), bpmProcdef.getShowInputTask())
                || !Objects.equals(showInputTaskDefKeys, bpmProcdef.getShowInputTaskDefKeys())
        ) {
            builder.append("Thay đổi Chọn bước được xem nội dung từ các bước song song tại thời điểm bất kỳ");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getHideInherit(), bpmProcdef.getHideInherit())
                || !Objects.equals(hideInheritTasks, bpmProcdef.getHideInheritTasks())
        ) {
            builder.append("Thay đổi Ẩn nút kế thừa");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getHideComment(), bpmProcdef.getHideComment())
                || !Objects.equals(hideCommentTasks, bpmProcdef.getHideCommentTasks())
        ) {
            builder.append("Thay đổi Ẩn textbox Nội dung phê duyệt");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getHideDownload(), bpmProcdef.getHideDownload())
                || !Objects.equals(hideDownloadTasks, bpmProcdef.getHideDownloadTasks())
        ) {
            builder.append("Thay đổi Ẩn nút Tải file Tờ trình");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getHideShareTicket(), bpmProcdef.getHideShareTicket())) {
            builder.append("Thay đổi Ẩn nút Chia sẻ");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getIsAssistant(), bpmProcdef.getIsAssistant())
                || !Objects.equals(request.getIsEditAssistant(), bpmProcdef.getIsEditAssistant())
        ) {
            builder.append("Thay đổi Cấu hình trợ lý");
            builder.append(System.lineSeparator());
        }

        if (request.getIsChangeActionApi() != null && request.getIsChangeActionApi()) {
            builder.append("Thay đổi Hành động");
            builder.append(System.lineSeparator());
        }

        if (request.getIsChangeInherits() != null && request.getIsChangeInherits()) {
            builder.append("Thay đổi Tự động kế thừa");
            builder.append(System.lineSeparator());
        }

        if (request.getIsChangeNotification() != null && request.getIsChangeNotification()) {
            builder.append("Thay đổi Cấu hình thông báo");
            builder.append(System.lineSeparator());
        }

        if (request.getIsChangeViewFileApi() != null && request.getIsChangeViewFileApi()) {
            builder.append("Thay đổi Thêm mới và cấu hình button");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getWarningApprovedTicket(), bpmProcdef.getWarningApprovedTicket())
                || !Objects.equals(request.getWarningApprovedTicketTasks(), bpmProcdef.getWarningApprovedTicketTasks())
        ) {
            builder.append("Thay đổi Cấu hình tham số API cảnh báo");
            builder.append(System.lineSeparator());
        }

        if (!Objects.equals(request.getDisableApprovedTicket(), bpmProcdef.getDisableApprovedTicket())
                || !Objects.equals(request.getDisabledApprovedTicketTasks(), bpmProcdef.getDisabledApprovedTicketTasks())
        ) {
            builder.append("Thay đổi Chặn phê duyệt theo Hạn mức tài chính");
            builder.append(System.lineSeparator());
        }

        return builder.toString();
    }

    public BpmProcdef createCopy(BpmProcdef bpmProcdef, String deploymentId) {
        try {
            List<Map<String, Object>> mapResponseGet = camundaEngineService.getProcessDefinitionByDeploymentId(deploymentId);
            Map<String, Object> stringObjectMap = !mapResponseGet.isEmpty() ? mapResponseGet.get(0) : null;
            String procDefId = stringObjectMap != null ? stringObjectMap.get("id").toString() : bpmProcdef.getProcDefId();
            String key = stringObjectMap != null ? stringObjectMap.get("key").toString() : bpmProcdef.getKey();
            String resource = stringObjectMap != null ? stringObjectMap.get("resource").toString() : bpmProcdef.getResourceName();
            String deployment = stringObjectMap != null ? stringObjectMap.get("deploymentId").toString() : deploymentId;

            //Get user login
//            String email = credentialHelper.getJWTPayload().getEmail();

            BpmProcdef bpmProcdefCopy = new BpmProcdef();
            bpmProcdefCopy.setProcDefId(procDefId);
            String name = null;
            BpmProcdef foundBpmProcDef = bpmProcdefRepository.findTopByNameLikeOrderByIdDesc(bpmProcdef.getName() + " - copy" + "%").orElse(null);
            if (foundBpmProcDef != null) {
                name = foundBpmProcDef.getName();
            }
            if (name == null) {
                bpmProcdefCopy.setName(bpmProcdef.getName() + " - copy");
            } else {
                String numRegex = null;
                try {
                    Pattern pattern = Pattern.compile("^(.*)\\((\\d+)\\)$");
                    Matcher matcher = pattern.matcher(name);

                    if (matcher.find()) {
                        numRegex = matcher.group(2);
                    } else {
                        numRegex = "1";
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                if (numRegex != null) {
                    int num = Integer.parseInt(numRegex);
                    bpmProcdefCopy.setName(bpmProcdef.getName() + " - copy(" + (num + 1) + ")");
                }
            }
            bpmProcdefCopy.setProcessType(bpmProcdef.getProcessType());
            bpmProcdefCopy.setDescription(bpmProcdef.getDescription());
            bpmProcdefCopy.setKey(key);
            bpmProcdefCopy.setResourceName(resource);
            bpmProcdefCopy.setDeploymentId(deployment);
            bpmProcdefCopy.setBytes(bpmProcdef.getBytes());
            bpmProcdefCopy.setPrioritized(bpmProcdef.getPrioritized());
            bpmProcdefCopy.setUserCreated(credentialHelper.getJWTPayload().getUsername());
            bpmProcdefCopy.setCreatedDate(LocalDateTime.now());
            bpmProcdefCopy.setAutoClose(bpmProcdef.getAutoClose());
            bpmProcdefCopy.setAutoCancel(bpmProcdef.getAutoCancel());
            bpmProcdefCopy.setStatus(ProcDefConstants.Status.DEACTIVE.code);
            bpmProcdefCopy.setLocation(bpmProcdef.getLocation());
            bpmProcdefCopy.setInFormTo(bpmProcdef.getInFormTo());
            bpmProcdefCopy.setStepByStepResultForCreate(bpmProcdef.getStepByStepResultForCreate());
            bpmProcdefCopy.setUpdate(bpmProcdef.getUpdate());
            bpmProcdefCopy.setRequestUpdate(bpmProcdef.getRequestUpdate());
            bpmProcdefCopy.setHideRuTasks(bpmProcdef.getHideRuTasks());
            bpmProcdefCopy.setCreateNewAndDouble(bpmProcdef.getCreateNewAndDouble());
            bpmProcdefCopy.setPriorityId(bpmProcdef.getPriorityId());
            bpmProcdefCopy.setCancel(bpmProcdef.getCancel());
            bpmProcdefCopy.setCancelTasks(bpmProcdef.getCancelTasks());


            bpmProcdefCopy.setShowInfo(bpmProcdef.getShowInfo());
            bpmProcdefCopy.setHideInfo(bpmProcdef.getHideInfo());
            bpmProcdefCopy.setIsAssistant(bpmProcdef.getIsAssistant());
            bpmProcdefCopy.setIsEditAssistant(bpmProcdef.getIsEditAssistant());
            bpmProcdefCopy.setHideInfoTasks(bpmProcdef.getHideInfoTasks());
            bpmProcdefCopy.setShowInfoTaks(bpmProcdef.getShowInfoTaks());
            bpmProcdefCopy.setIsAutoCancel(bpmProcdef.getIsAutoCancel());

            // auto complete
            bpmProcdefCopy.setAutoCompleteTask(bpmProcdef.getAutoCompleteTask());

            bpmProcdefCopy.setChangeImplementerValue(bpmProcdef.getChangeImplementerValue());

            bpmProcdefCopy.setAdditionalRequest(bpmProcdef.getAdditionalRequest());
            bpmProcdefCopy.setHideRelatedTicket(bpmProcdef.getHideRelatedTicket());
            bpmProcdefCopy.setHideRelatedTicketValue(bpmProcdef.getHideRelatedTicketValue());
            bpmProcdefCopy.setRecall(bpmProcdef.getRecall());
            bpmProcdefCopy.setShowInputTask(bpmProcdef.getShowInputTask());
            bpmProcdefCopy.setShowInputTaskDefKeys(bpmProcdef.getShowInputTaskDefKeys());

            bpmProcdefCopy.setHideInherit(bpmProcdef.getHideInherit());
            bpmProcdefCopy.setHideInheritTasks(bpmProcdef.getHideInheritTasks());
            bpmProcdefCopy.setHideComment(bpmProcdef.getHideComment());
            bpmProcdefCopy.setHideCommentTasks(bpmProcdef.getHideCommentTasks());
            bpmProcdefCopy.setHideDownload(bpmProcdef.getHideDownload());
            bpmProcdefCopy.setHideDownloadTasks(bpmProcdef.getHideDownloadTasks());
            bpmProcdefCopy.setHideShareTicket(bpmProcdef.getHideShareTicket());


            if (bpmProcdef.getAutoInherits() != null) {
                bpmProcdefCopy.setAutoInherits(bpmProcdef.getAutoInherits());
            }
            if (bpmProcdef.getOffNotification() != null) {
                bpmProcdefCopy.setOffNotification(bpmProcdef.getOffNotification());
            }
            if (bpmProcdefCopy.getName().length() > 100) {
                throw new AppException("Tên quy trình vượt quá độ dài");
            }

            BpmProcdef bpmProcdefs = bpmProcdefRepository.save(bpmProcdefCopy);
            //Lưu cấu hình thông báo

            List<BpmProcdefNotification> notifications = bpmProcdefNotificationRepository.findBpmProcdefNotificationsByBpmProcdefId(bpmProcdef.getId());
            if (!ValidationUtils.isNullOrEmpty(notifications)) {
                List<BpmProcdefNotification> bpmProcdefNotifications = new ArrayList<>();
                BpmProcdefNotification procdefNotification = null;
                for (BpmProcdefNotification notification : notifications) {
                    procdefNotification = bpmProcdefNotificationRepository.findById(notification.getId()).get();
                    if (procdefNotification != null) {
                        procdefNotification.setProcDefId(bpmProcdefs.getProcDefId());
                        procdefNotification.setActionCode(notification.getActionCode());
                        procdefNotification.setNotificationObject(notification.getNotificationObject());
                        procdefNotification.setNotificationTemplateId(notification.getNotificationTemplateId());
                        procdefNotification.setTaskDefKey(notification.getTaskDefKey());
                        procdefNotification.setTaskDefKeyNotification(notification.getTaskDefKeyNotification());
                        procdefNotification.setFieldNotification(notification.getFieldNotification());
                        procdefNotification.setCreatedTime(LocalDateTime.now());
                        procdefNotification.setUpdatedTime(LocalDateTime.now());
                        procdefNotification.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
                        procdefNotification.setBpmProcdefId(bpmProcdefs.getId());
                        procdefNotification.setStatus(notification.getStatus());
                        procdefNotification.setAddMoreConfig(notification.getAddMoreConfig());
                        procdefNotification.setOffNotification(notification.getOffNotification());
                        bpmProcdefNotifications.add(procdefNotification);
                    }
                }
                //save all bản ghi
                bpmProcdefNotificationRepository.saveAll(bpmProcdefNotifications);
            }


            // Shared user
            List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceIdAndReferenceType(bpmProcdef.getId(), ShareUserTypeEnum.PROCESS.type);
            if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                List<SharedUser> sharedUsersCopy = new ArrayList<>();
                for (SharedUser sharedUser : sharedUsers) {
                    SharedUser sharedUserCopy = new SharedUser();
                    sharedUserCopy.setReferenceId(bpmProcdefs.getId());
                    sharedUserCopy.setReferenceType(ShareUserTypeEnum.PROCESS.type);
                    sharedUserCopy.setEmail(sharedUser.getEmail());

                    sharedUsersCopy.add(sharedUserCopy);
                }
                shareUserRepository.saveAll(sharedUsersCopy);
            }

            //CREATE bpmOwnerProcessManager
            List<BpmOwnerProcess> bpmOwnerProcessByProcDefId = bpmOwnerProcessManager.bpmOwnerProcessByProcDefId(bpmProcdef.getId());
            List<String> owners = new ArrayList<>();
            for (BpmOwnerProcess bpmOwnerProcess : bpmOwnerProcessByProcDefId) {
                owners.add(bpmOwnerProcess.getIdUser());
            }
            List<BpmOwnerProcess> bpmOwnerProcessList = new ArrayList<>();
            for (String owner : owners) {
                BpmOwnerProcess bpmOwnerProcess = new BpmOwnerProcess();
                bpmOwnerProcess.setProcDefId(bpmProcdefCopy.getId());
                bpmOwnerProcess.setIdUser(owner);
                bpmOwnerProcessList.add(bpmOwnerProcess);
            }
            bpmOwnerProcessManager.saveAll(bpmOwnerProcessList);

            List<BpmProcdefApi> bpmProcdefApiList = bpmProcdefApiManager.listBpmProcdefApi(bpmProcdef.getId());
            if (!ValidationUtils.isNullOrEmpty(bpmProcdefApiList)) {
                List<BpmProcdefApi> bpmProcdefApis = new ArrayList<>();
                for (BpmProcdefApi bpmProcdefApiCopy : bpmProcdefApiList) {

                    BpmProcdefApi bpmProcdefApi = new BpmProcdefApi();
                    bpmProcdefApi.setBpmProcdefId(bpmProcdefs.getId());
                    bpmProcdefApi.setTaskDefKey(bpmProcdefApiCopy.getTaskDefKey());
                    bpmProcdefApi.setActionId(bpmProcdefApiCopy.getActionId());
                    bpmProcdefApi.setApiId(bpmProcdefApiCopy.getApiId());
                    bpmProcdefApi.setHeader(bpmProcdefApiCopy.getHeader());
                    bpmProcdefApi.setBody(bpmProcdefApiCopy.getBody());
                    bpmProcdefApi.setUpdatedTime(LocalDateTime.now());
                    bpmProcdefApi.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
                    bpmProcdefApi.setProcDefId(procDefId);
                    bpmProcdefApi.setStatus(bpmProcdefApiCopy.getStatus());
                    bpmProcdefApi.setCallOrder(bpmProcdefApiCopy.getCallOrder());
                    bpmProcdefApi.setSuccessCondition(bpmProcdefApiCopy.getSuccessCondition());
                    bpmProcdefApi.setResponse(bpmProcdefApiCopy.getResponse());
                    bpmProcdefApi.setContinueOnError(bpmProcdefApiCopy.getContinueOnError());
                    bpmProcdefApi.setCallCondition(bpmProcdefApiCopy.getCallCondition());

                    bpmProcdefApis.add(bpmProcdefApi);
                }
                bpmProcdefApiManager.saveAll(bpmProcdefApis);
            }

            List<BpmProcdefInherits> bpmProcdefInherits = bpmProcdefInheritsRepository.getAllByBpmProcdefId(bpmProcdef.getId());
            if (!ValidationUtils.isNullOrEmpty(bpmProcdefInherits)) {
                List<BpmProcdefInherits> bpmProcdefInheritsCopy = new ArrayList<>();
                for (BpmProcdefInherits listBpmProcdefInherits : bpmProcdefInherits) {
                    BpmProcdefInherits listCopy = new BpmProcdefInherits();
                    listCopy.setBpmProcdefId(bpmProcdefs.getId());
                    listCopy.setProcDefId(listBpmProcdefInherits.getProcDefId());
                    listCopy.setTaskDefKey(listBpmProcdefInherits.getTaskDefKey());
                    listCopy.setTaskDefKeyInherits(listBpmProcdefInherits.getTaskDefKeyInherits());
                    listCopy.setFieldInherits(listBpmProcdefInherits.getFieldInherits());
                    listCopy.setStatus(listBpmProcdefInherits.getStatus());
                    listCopy.setCreatedTime(LocalDateTime.now());
                    listCopy.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    bpmProcdefInheritsCopy.add(listCopy);
                }
                bpmProcdefInheritsRepository.saveAll(bpmProcdefInheritsCopy);
            }
            //dữ liệu phân quyền
            List<PermissionDataManagement> listPer = permissionDataManagementRepository.getPermissionDataManagementByTypeId(bpmProcdef.getId());
            if (!ValidationUtils.isNullOrEmpty(listPer)) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (PermissionDataManagement data : listPer) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(bpmProcdefs.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.BPM_PROCDEF.code);
                    permissionDataManagement.setCompanyCode(data.getCompanyCode());
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                permissionDataManagementRepository.saveAll(permissionDataManagements);
            }

            // Cấu hình trạng thái nhiệm vụ theo bước
            List<BpmProcdefLegislativeStatusConfig> bpmProcdefLegislativeStatusConfigs = bpmProcdefLegislativeConfigRepository.getAllByBpmProcdefId(bpmProcdef.getId());
            if (!ValidationUtils.isNullOrEmpty(bpmProcdefLegislativeStatusConfigs)) {
                List<BpmProcdefLegislativeStatusConfig> clones = new ArrayList<>();
                for (BpmProcdefLegislativeStatusConfig data : bpmProcdefLegislativeStatusConfigs) {
                    BpmProcdefLegislativeStatusConfig clone = new BpmProcdefLegislativeStatusConfig();
                    clone.setTaskStatus(data.getTaskStatus());
                    clone.setTaskDefKey(data.getTaskDefKey());
                    clone.setLegislativeStatus(data.getLegislativeStatus());
                    clone.setStatus(Boolean.TRUE);
                    clones.add(clone);
                }
                bpmProcdefLegislativeConfigRepository.saveAll(clones);
            }

            // Cấu hình trạng paramticket map legislative
            List<BpmProcdefLegislativeTicketConfig> bpmProcdefLegislativeTicketConfigs = bpmProcdefLegislativeTicketConfigRepository.getAllByBpmProcdefId(bpmProcdef.getId());
            if (!ValidationUtils.isNullOrEmpty(bpmProcdefLegislativeTicketConfigs)) {
                List<BpmProcdefLegislativeTicketConfig> clones = new ArrayList<>();
                for (BpmProcdefLegislativeTicketConfig data : bpmProcdefLegislativeTicketConfigs) {
                    BpmProcdefLegislativeTicketConfig clone = new BpmProcdefLegislativeTicketConfig();
                    clone.setTicketField(data.getTicketField());
                    clone.setLegislativeField(data.getLegislativeField());
                    clone.setStatus(Boolean.TRUE);
                    clones.add(clone);
                }
                bpmProcdefLegislativeTicketConfigRepository.saveAll(clones);
            }


            return bpmProcdefCopy;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    // DELETE PROC DEF
    public void delete(BpmProcdef bpmProcdef) {
        try {
            bpmProcdef.setStatus(ProcDefConstants.Status.DELETED.code);
            bpmProcdefRepository.save(bpmProcdef);
            groupProcTempRepository.deleteByProDefId(bpmProcdef.getProcDefId());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // CREATE PROC DEF
    public BpmProcdef saveProcessInBPM(CreateBPMRequest createBPMRequest) {
        try {
            List<Map<String, Object>> mapResponseGet = camundaEngineService.getProcessDefinitionByDeploymentId(createBPMRequest.getDeploymentId());
            Map<String, Object> stringObjectMap = !mapResponseGet.isEmpty() ? mapResponseGet.get(0) : null;
            String procDefId = stringObjectMap != null ? stringObjectMap.get("id").toString() : null;
            String key = stringObjectMap != null ? stringObjectMap.get("key").toString() : null;
            String resource = stringObjectMap != null ? stringObjectMap.get("resource").toString() : createBPMRequest.getFileName();
            String deployment = stringObjectMap != null ? stringObjectMap.get("deploymentId").toString() : createBPMRequest.getDeploymentId();

            String cancelTasks = new Gson().toJson(createBPMRequest.getCancelTasks());
            String showInfoTasks = new Gson().toJson(createBPMRequest.getShowInfoTasks());
            String hideInfoTasks = new Gson().toJson(createBPMRequest.getHideInfoTasks());
            String hideTicketValue = new Gson().toJson(createBPMRequest.getHideRelatedTicketValue());
            String changeImplementerValue = new Gson().toJson(createBPMRequest.getChangeImplementerValue());
            String authorityOnTicketValue = new Gson().toJson(createBPMRequest.getHideRelatedTicketValue());
            String authorityOnTicketStep = new Gson().toJson(createBPMRequest.getAuthorityOnTicketStep());
            String showInputTaskDefKeys = new Gson().toJson(createBPMRequest.getShowInputTaskDefKeys());
            String hideRuTasks = new Gson().toJson(createBPMRequest.getHideRuTasks());
            String hideInheritTasks = new Gson().toJson(createBPMRequest.getHideInheritTasks());
            String hideCommentTasks = new Gson().toJson(createBPMRequest.getHideCommentTasks());
            String hideDownloadTasks = new Gson().toJson(createBPMRequest.getHideDownloadTasks());
            String warningApprovedTicketTasks = createBPMRequest.getWarningApprovedTicketTasks();
            String disabledApprovedTicketTasks = createBPMRequest.getDisabledApprovedTicketTasks();

            // save procDef
            String email = credentialHelper.getJWTPayload().getEmail();
            BpmProcdef bpmProcdef = new BpmProcdef();
            bpmProcdef.setProcessType(createBPMRequest.getProcessType());
            bpmProcdef.setProcDefId(procDefId);
            bpmProcdef.setName(createBPMRequest.getName());
            bpmProcdef.setDescription(createBPMRequest.getDescription());
            bpmProcdef.setKey(key);
            bpmProcdef.setDeploymentId(deployment);
            if (createBPMRequest.getFileName() != null) {
                //lấy file từ minio
                byte[] bytes = fileManager.getFile(bucket, createBPMRequest.getFileName());
                bpmProcdef.setResourceName(resource);
                bpmProcdef.setBytes(bytes);
            }
            bpmProcdef.setPrioritized(createBPMRequest.getPrioritized());
            bpmProcdef.setUserCreated(credentialHelper.getJWTPayload().getUsername());
            bpmProcdef.setCreatedDate(LocalDateTime.now());
            bpmProcdef.setAutoClose(createBPMRequest.getAutoClose());
            bpmProcdef.setAutoCancel(createBPMRequest.getAutoCancel());
            bpmProcdef.setStatus(ProcDefConstants.Status.DEACTIVE.code);
            bpmProcdef.setLocation(createBPMRequest.getLocation());
            bpmProcdef.setInFormTo(createBPMRequest.getInFormTo());
            bpmProcdef.setStepByStepResultForCreate(createBPMRequest.getStepByStepResultForCreate());
            bpmProcdef.setUpdate(createBPMRequest.getUpdate());
            bpmProcdef.setRequestUpdate(createBPMRequest.getRequestUpdate());
            bpmProcdef.setHideRuTasks(hideRuTasks);
            bpmProcdef.setCreateNewAndDouble(createBPMRequest.getCreateNewAndDouble());
            bpmProcdef.setPriorityId(createBPMRequest.getPriorityId());
            bpmProcdef.setCancel(createBPMRequest.getCancel());
            bpmProcdef.setCancelTasks(cancelTasks);
            bpmProcdef.setChangeImplementerValue(changeImplementerValue);
            bpmProcdef.setDisableApprovedTicket(createBPMRequest.getDisableApprovedTicket());
            bpmProcdef.setWarningApprovedTicket(createBPMRequest.getWarningApprovedTicket());
            bpmProcdef.setDisabledApprovedTicketTasks(disabledApprovedTicketTasks);
            bpmProcdef.setWarningApprovedTicketTasks(warningApprovedTicketTasks);
            bpmProcdef.setLegislativeRequirement(createBPMRequest.getLegislativeRequirement());

            bpmProcdef.setShowInfo(createBPMRequest.getShowInfo());
            bpmProcdef.setHideInfo(createBPMRequest.getHideInfo());
            bpmProcdef.setIsAssistant(createBPMRequest.getIsAssistant());
            bpmProcdef.setIsEditAssistant(createBPMRequest.getIsEditAssistant());
            bpmProcdef.setHideInfoTasks(hideInfoTasks);
            bpmProcdef.setShowInfoTaks(showInfoTasks);
            bpmProcdef.setIsAutoCancel(createBPMRequest.getIsAutoCancel());

            // auto complete
            bpmProcdef.setAutoCompleteTask(createBPMRequest.getAutoCompleteTask());

            bpmProcdef.setAdditionalRequest(createBPMRequest.getAdditionalRequest());
            bpmProcdef.setHideRelatedTicket(createBPMRequest.getHideRelatedTicket());
            bpmProcdef.setHideRelatedTicketValue(hideTicketValue);
            bpmProcdef.setAuthorityOnTicket(createBPMRequest.getAuthorityOnTicket());
            bpmProcdef.setAuthorityOnTicketValue(authorityOnTicketValue);
            bpmProcdef.setAuthorityOnTicketStep(authorityOnTicketStep);
            bpmProcdef.setRecall(createBPMRequest.getRecall());
            bpmProcdef.setShowInputTask(createBPMRequest.getShowInputTask());
            bpmProcdef.setShowInputTaskDefKeys(showInputTaskDefKeys);

            bpmProcdef.setHideInherit(createBPMRequest.getHideInherit());
            bpmProcdef.setHideInheritTasks(hideInheritTasks);

            bpmProcdef.setHideComment(createBPMRequest.getHideComment());
            bpmProcdef.setHideCommentTasks(hideCommentTasks);

            bpmProcdef.setHideDownload(createBPMRequest.getHideDownload());
            bpmProcdef.setHideDownloadTasks(hideDownloadTasks);

            bpmProcdef.setHideShareTicket(createBPMRequest.getHideShareTicket());

            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getAutoInherits())) {
                bpmProcdef.setAutoInherits(createBPMRequest.getAutoInherits());
            }
            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getOffNotification())) {
                bpmProcdef.setOffNotification(createBPMRequest.getOffNotification());
            }
            List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
            if (!ValidationUtils.isNullOrEmpty(listCompanyCodeAndName))
                for (NameAndCodeCompanyResponse response : listCompanyCodeAndName) {
                    bpmProcdef.setCompanyCode(response.getCompanyCode());
                    bpmProcdef.setCompanyName(response.getCompanyName());
                }
            BpmProcdef bpmProcdef1 = bpmProcdefRepository.save(bpmProcdef);

            // save procDef history
            BpmProcDefHistory bpmProcdefHistory = new BpmProcDefHistory();
            bpmProcdefHistory.setProcessType(createBPMRequest.getProcessType());
            bpmProcdefHistory.setProcDefId(procDefId);
            bpmProcdefHistory.setName(createBPMRequest.getName());
            bpmProcdefHistory.setDescription(createBPMRequest.getDescription());
            bpmProcdefHistory.setKey(key);
            bpmProcdefHistory.setDeploymentId(deployment);
            if (createBPMRequest.getFileName() != null) {
                //lấy file từ minio
                byte[] bytes = fileManager.getFile(bucket, createBPMRequest.getFileName());
                bpmProcdefHistory.setResourceName(resource);
                bpmProcdefHistory.setBytes(bytes);
            }
            bpmProcdefHistory.setPrioritized(createBPMRequest.getPrioritized());
            bpmProcdefHistory.setUserCreated(credentialHelper.getJWTPayload().getUsername());
            bpmProcdefHistory.setCreatedDate(LocalDateTime.now());
            bpmProcdefHistory.setAutoClose(createBPMRequest.getAutoClose());
            bpmProcdefHistory.setAutoCancel(createBPMRequest.getAutoCancel());
            bpmProcdefHistory.setStatus(ProcDefConstants.Status.DEACTIVE.code);
            bpmProcdefHistory.setLocation(createBPMRequest.getLocation());
            bpmProcdefHistory.setInFormTo(createBPMRequest.getInFormTo());
            bpmProcdefHistory.setStepByStepResultForCreate(createBPMRequest.getStepByStepResultForCreate());
            bpmProcdefHistory.setUpdate(createBPMRequest.getUpdate());
            bpmProcdefHistory.setRequestUpdate(createBPMRequest.getRequestUpdate());
            bpmProcdefHistory.setHideRuTasks(hideRuTasks);
            bpmProcdefHistory.setCreateNewAndDouble(createBPMRequest.getCreateNewAndDouble());
            bpmProcdefHistory.setPriorityId(createBPMRequest.getPriorityId());
            bpmProcdefHistory.setCancel(createBPMRequest.getCancel());
            bpmProcdefHistory.setCancelTasks(cancelTasks);
            bpmProcdefHistory.setChangeImplementerValue(changeImplementerValue);
            bpmProcdefHistory.setShowInfo(createBPMRequest.getShowInfo());
            bpmProcdefHistory.setHideInfo(createBPMRequest.getHideInfo());
            bpmProcdefHistory.setIsAssistant(createBPMRequest.getIsAssistant());
            bpmProcdefHistory.setIsEditAssistant(createBPMRequest.getIsEditAssistant());
            bpmProcdefHistory.setHideInfoTasks(hideInfoTasks);
            bpmProcdefHistory.setShowInfoTaks(showInfoTasks);
            bpmProcdefHistory.setIsAutoCancel(createBPMRequest.getIsAutoCancel());
            bpmProcdefHistory.setAdditionalRequest(createBPMRequest.getAdditionalRequest());
            bpmProcdefHistory.setHideRelatedTicket(createBPMRequest.getHideRelatedTicket());
            bpmProcdefHistory.setHideRelatedTicketValue(hideTicketValue);
            bpmProcdefHistory.setAuthorityOnTicket(createBPMRequest.getAuthorityOnTicket());
            bpmProcdefHistory.setAuthorityOnTicketValue(authorityOnTicketValue);
            bpmProcdefHistory.setAuthorityOnTicketStep(authorityOnTicketStep);
            bpmProcdefHistory.setRecall(createBPMRequest.getRecall());
            bpmProcdefHistory.setShowInputTask(createBPMRequest.getShowInputTask());
            bpmProcdefHistory.setShowInputTaskDefKeys(showInputTaskDefKeys);
            bpmProcdefHistory.setHideInherit(createBPMRequest.getHideInherit());
            bpmProcdefHistory.setHideInheritTasks(hideInheritTasks);
            bpmProcdefHistory.setHideComment(createBPMRequest.getHideComment());
            bpmProcdefHistory.setHideCommentTasks(hideCommentTasks);
            bpmProcdefHistory.setHideDownload(createBPMRequest.getHideDownload());
            bpmProcdefHistory.setHideDownloadTasks(hideDownloadTasks);
            bpmProcdefHistory.setHideShareTicket(createBPMRequest.getHideShareTicket());
            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getAutoInherits())) {
                bpmProcdefHistory.setAutoInherits(createBPMRequest.getAutoInherits());
            }
            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getOffNotification())) {
                bpmProcdefHistory.setOffNotification(createBPMRequest.getOffNotification());
            }
            if (!ValidationUtils.isNullOrEmpty(listCompanyCodeAndName)) {
                for (NameAndCodeCompanyResponse response : listCompanyCodeAndName) {
                    bpmProcdefHistory.setCompanyCode(response.getCompanyCode());
                    bpmProcdefHistory.setCompanyName(response.getCompanyName());
                }
            }
            bpmProcdefHistory.setDisableApprovedTicket(createBPMRequest.getDisableApprovedTicket());
            bpmProcdefHistory.setWarningApprovedTicket(createBPMRequest.getWarningApprovedTicket());
            bpmProcdefHistory.setDisabledApprovedTicketTasks(disabledApprovedTicketTasks);
            bpmProcdefHistory.setWarningApprovedTicketTasks(warningApprovedTicketTasks);
            bpmProcdefHistory.setLegislativeRequirement(createBPMRequest.getLegislativeRequirement());

            bpmProcdefHistory.setAutoCompleteTask(createBPMRequest.getAutoCompleteTask());
            bpmProcdefHistory.setOrgProcessId(bpmProcdef1.getId());
            bpmProcdefHistory.setContentEdit("Thêm mới thành công quy trình");
            bpmProcdefHistory.setVersion("V0");

            List<GroupTableProTemp> bpmGroupTableProTemps = new ArrayList<>();
            for (String formKey : createBPMRequest.getTemplateId()) {
                GroupTableProTemp groupTableProTemp = new GroupTableProTemp();
                groupTableProTemp.setProDefId(bpmProcdef1.getProcDefId());
                groupTableProTemp.setFormKey(formKey);
                bpmGroupTableProTemps.add(groupTableProTemp);
            }
            groupProcTempRepository.saveAll(bpmGroupTableProTemps);
            //Cập nhập status biểu mẫu => 1
            updateTemplateStatus(createBPMRequest.getTemplateId());

            Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();
            List<BpmOwnerProcess> bpmOwnerProcessList = new ArrayList<>();
            for (String owner : createBPMRequest.getOwners()) {
                BpmOwnerProcess bpmOwnerProcess = new BpmOwnerProcess();
                bpmOwnerProcess.setProcDefId(bpmProcdef.getId());
                bpmOwnerProcess.setIdUser(owner);
                bpmOwnerProcessList.add(bpmOwnerProcess);
            }
            bpmOwnerProcessManager.saveAll(bpmOwnerProcessList);
            bpmProcdefHistory.setProcessOwner(g.toJson(bpmOwnerProcessList));

            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getBpmProcdefApiList())) {
                List<BpmProcdefApi> bpmProcdefApis = new ArrayList<>();
                for (BpmProcdefApiDto bpmProcdefApiDto : createBPMRequest.getBpmProcdefApiList()) {
                    BpmProcdefApi bpmProcdefApi = new BpmProcdefApi();
                    bpmProcdefApi.setProcDefId(procDefId);
                    bpmProcdefApi.setTaskDefKey(bpmProcdefApiDto.getTaskDefKey());
                    bpmProcdefApi.setActionId(bpmProcdefApiDto.getActionId());
                    bpmProcdefApi.setApiId(bpmProcdefApiDto.getApiId());
                    bpmProcdefApi.setHeader(bpmProcdefApiDto.getHeader());
                    bpmProcdefApi.setBody(bpmProcdefApiDto.getBody());
                    bpmProcdefApi.setCreatedTime(LocalDateTime.now());
                    bpmProcdefApi.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    bpmProcdefApi.setBpmProcdefId(bpmProcdef.getId());
                    bpmProcdefApi.setStatus(1L);
                    bpmProcdefApi.setCallOrder(bpmProcdefApiDto.getCallOrder());
                    bpmProcdefApi.setSuccessCondition(bpmProcdefApiDto.getSuccessCondition());
                    bpmProcdefApi.setResponse(bpmProcdefApiDto.getResponse());
                    bpmProcdefApi.setContinueOnError(bpmProcdefApiDto.getContinueOnError());
                    bpmProcdefApi.setCallCondition(bpmProcdefApiDto.getCallCondition());
                    bpmProcdefApis.add(bpmProcdefApi);
                }
                bpmProcdefApiRepository.saveAll(bpmProcdefApis);
            }

            //Lưu cấu hình kế thừa
            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getBpmProcdefInherits())) {
                List<BpmProcdefInherits> bpmProcdefInherits = new ArrayList<>();
                for (BpmProcdefInheritsDTO bpmProcdefInheritsDTO : createBPMRequest.getBpmProcdefInherits()) {
                    BpmProcdefInherits procdefInherits = new BpmProcdefInherits();
                    procdefInherits.setProcDefId(procDefId);
                    procdefInherits.setTaskDefKey(bpmProcdefInheritsDTO.getTaskDefKey());
                    procdefInherits.setTaskDefKeyInherits(bpmProcdefInheritsDTO.getTaskDefKeyInherits());
                    procdefInherits.setFieldInherits(bpmProcdefInheritsDTO.getFieldInherits());
                    procdefInherits.setCreatedTime(LocalDateTime.now());
                    procdefInherits.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    procdefInherits.setBpmProcdefId(bpmProcdef.getId());
                    procdefInherits.setStatus("1");
                    bpmProcdefInherits.add(procdefInherits);
                }

                bpmProcdefInheritsRepository.saveAll(bpmProcdefInherits);
            }

            //Lưu cấu hình thông báo
            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getBpmProcdefNotifications())) {
                List<BpmProcdefNotification> bpmProcdefNotifications = new ArrayList<>();
                BpmProcdefNotification procdefNotification = null;
                for (BpmProcdefNotificationDTO bpmProcdefNotificationDTO : createBPMRequest.getBpmProcdefNotifications()) {
                    procdefNotification = new BpmProcdefNotification();
                    procdefNotification.setProcDefId(procDefId);
                    procdefNotification.setActionCode(bpmProcdefNotificationDTO.getActionCode());
                    procdefNotification.setNotificationObject(bpmProcdefNotificationDTO.getNotificationObject());
                    procdefNotification.setNotificationTemplateId(bpmProcdefNotificationDTO.getNotificationTemplateId());
                    procdefNotification.setTaskDefKey(bpmProcdefNotificationDTO.getTaskDefKey());
                    procdefNotification.setTaskDefKeyNotification(bpmProcdefNotificationDTO.getTaskDefKeyNotification());
                    procdefNotification.setFieldNotification(bpmProcdefNotificationDTO.getFieldNotification());
                    procdefNotification.setCreatedTime(LocalDateTime.now());
                    procdefNotification.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    procdefNotification.setBpmProcdefId(bpmProcdef.getId());
                    procdefNotification.setStatus("1");
                    procdefNotification.setAddMoreConfig(bpmProcdefNotificationDTO.getAddMoreConfig());
                    procdefNotification.setOffNotification(bpmProcdefNotificationDTO.getOffNotification());
                    bpmProcdefNotifications.add(procdefNotification);
                }
                bpmProcdefNotificationRepository.saveAll(bpmProcdefNotifications);
            }

            // Lưu người được chia sẻ
            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getShareWith())) {
                List<SharedUser> sharedUsers = new ArrayList<>();
                for (String shareWith : createBPMRequest.getShareWith()) {
                    SharedUser sharedUser = new SharedUser();
                    sharedUser.setReferenceId(bpmProcdef1.getId());
                    sharedUser.setReferenceType(ShareUserTypeEnum.PROCESS.type);
                    sharedUser.setEmail(shareWith);
                    sharedUsers.add(sharedUser);
                }
                shareUserRepository.saveAll(sharedUsers);
                bpmProcdefHistory.setShareWith(g.toJson(sharedUsers));
            }

            // Lưu phân quyền dữ liệu
            if (!ValidationUtils.isNullOrEmpty(createBPMRequest.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : createBPMRequest.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(bpmProcdef1.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.BPM_PROCDEF.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                permissionDataManagementRepository.saveAll(permissionDataManagements);
                bpmProcdefHistory.setApplyFor(g.toJson(permissionDataManagements));
            }

            bpmProcDefHistoryRepository.save(bpmProcdefHistory);

            //Lưu cấu view file từ API
            List<BpmProcdefViewFileApi> bpmProcdefViewFileApis = new ArrayList<>();
            if (createBPMRequest.getViewFileApi() != null) {
                List<Long> deleteListViewFileApi = new ArrayList<>();
                BpmProcdefViewFileApi bpmProcdefViewFileApi;
                for (BpmProcdefViewFileApi dto : createBPMRequest.getViewFileApi()) {
                    if (dto.getStatus().equals(Boolean.TRUE)) {
                        bpmProcdefViewFileApi = new BpmProcdefViewFileApi();
                        if (dto.getBpmProcdefId() != null && dto.getBpmProcdefId().equals(procDefId)) { // Quy trình update thì lưu thêm 1 bản ghi mới
                            bpmProcdefViewFileApi.setId(dto.getId());
                        }
                        bpmProcdefViewFileApi.setBaseUrl(dto.getBaseUrl());
                        bpmProcdefViewFileApi.setBody(dto.getBody());
                        bpmProcdefViewFileApi.setButtonName(dto.getButtonName());
                        bpmProcdefViewFileApi.setHeader(dto.getHeader());
                        bpmProcdefViewFileApi.setResponse(dto.getResponse());
                        bpmProcdefViewFileApi.setMethod(dto.getMethod());
                        bpmProcdefViewFileApi.setTaskDefKey(dto.getTaskDefKey());
                        bpmProcdefViewFileApi.setUrl(dto.getUrl());
                        bpmProcdefViewFileApi.setStatus(dto.getStatus());
                        bpmProcdefViewFileApi.setProcDefId(procDefId);
                        bpmProcdefViewFileApi.setDisplayCondition(dto.getDisplayCondition());
                        bpmProcdefViewFileApi.setShowButtonInCreateTicket(dto.getShowButtonInCreateTicket());

                        bpmProcdefViewFileApi.setBpmProcdefId(bpmProcdef.getId());
                        if (dto.getId() != null) {
                            bpmProcdefViewFileApi.setCreatedTime(LocalDateTime.now());
                            bpmProcdefViewFileApi.setCreatedUser(credentialHelper.getJWTPayload().getUsername());

                        } else {
                            bpmProcdefViewFileApi.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
                            bpmProcdefViewFileApi.setUpdatedTime(LocalDateTime.now());
                        }
                        bpmProcdefViewFileApis.add(bpmProcdefViewFileApi);
                    } else deleteListViewFileApi.add(dto.getId());
                }
                bpmProcdefViewFileApiRepository.deleteAllById(deleteListViewFileApi);
            }
            if (!ValidationUtils.isNullOrEmpty(bpmProcdefViewFileApis)) {
                bpmProcdefViewFileApiRepository.saveAll(bpmProcdefViewFileApis);
            }

            //Lưu cấu view file từ API
            List<BpmProcdefLegislativeStatusConfig> bpmProcdefLegislativeStatusConfigs = new ArrayList<>();
            if (createBPMRequest.getConfigLegislativeByStatus() != null) {
                List<Long> deleteList = new ArrayList<>();
                for (BpmProcdefLegislativeStatusConfig dto : createBPMRequest.getConfigLegislativeByStatus()) {
                    BpmProcdefLegislativeStatusConfig bpmProcdefLegislativeStatusConfig = new BpmProcdefLegislativeStatusConfig();
                    if (dto.getStatus().equals(Boolean.TRUE)) {
                        bpmProcdefLegislativeStatusConfig.setBpmProcdefId(bpmProcdef.getId());
                        bpmProcdefLegislativeStatusConfig.setProcDefId(procDefId);
                        bpmProcdefLegislativeStatusConfig.setStatus(Boolean.TRUE);
                        bpmProcdefLegislativeStatusConfig.setTaskDefKey(dto.getTaskDefKey());
                        bpmProcdefLegislativeStatusConfig.setLegislativeStatus(dto.getLegislativeStatus());
                        bpmProcdefLegislativeStatusConfig.setTaskStatus(dto.getTaskStatus());
                        if (dto.getId() != null) {
                            bpmProcdefLegislativeStatusConfig.setCreatedTime(LocalDateTime.now());
                            bpmProcdefLegislativeStatusConfig.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                        } else {
                            bpmProcdefLegislativeStatusConfig.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
                            bpmProcdefLegislativeStatusConfig.setUpdatedTime(LocalDateTime.now());
                        }
                        bpmProcdefLegislativeStatusConfigs.add(bpmProcdefLegislativeStatusConfig);
                    } else deleteList.add(dto.getId());
                }
                bpmProcdefLegislativeConfigRepository.deleteAllById(deleteList);
            }
            if (!ValidationUtils.isNullOrEmpty(bpmProcdefLegislativeStatusConfigs)) {
                bpmProcdefLegislativeConfigRepository.saveAll(bpmProcdefLegislativeStatusConfigs);
            }

            List<BpmProcdefLegislativeTicketConfig> bpmProcdefLegislativeTicketConfigs = new ArrayList<>();
            if (createBPMRequest.getConfigLegislativeTicket() != null) {
                List<Long> deleteList = new ArrayList<>();
                for (BpmProcdefLegislativeTicketConfig dto : createBPMRequest.getConfigLegislativeTicket()) {
                    BpmProcdefLegislativeTicketConfig bpmProcdefLegislativeTicketConfig = new BpmProcdefLegislativeTicketConfig();
                    if (dto.getStatus().equals(Boolean.TRUE)) {
                        bpmProcdefLegislativeTicketConfig.setBpmProcdefId(bpmProcdef.getId());
                        bpmProcdefLegislativeTicketConfig.setProcDefId(procDefId);
                        bpmProcdefLegislativeTicketConfig.setLegislativeField(dto.getLegislativeField());
                        bpmProcdefLegislativeTicketConfig.setTicketField(dto.getTicketField());
                        if (dto.getId() != null) {
                            bpmProcdefLegislativeTicketConfig.setCreatedTime(LocalDateTime.now());
                            bpmProcdefLegislativeTicketConfig.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                        } else {
                            bpmProcdefLegislativeTicketConfig.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
                            bpmProcdefLegislativeTicketConfig.setUpdatedTime(LocalDateTime.now());
                        }
                        bpmProcdefLegislativeTicketConfigs.add(bpmProcdefLegislativeTicketConfig);
                    } else deleteList.add(dto.getId());
                }
                bpmProcdefLegislativeTicketConfigRepository.deleteAllById(deleteList);
            }
            if (!ValidationUtils.isNullOrEmpty(bpmProcdefLegislativeTicketConfigs)) {
                bpmProcdefLegislativeTicketConfigRepository.saveAll(bpmProcdefLegislativeTicketConfigs);
            }

            return bpmProcdef;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public void updateTemplateStatus(List<String> urlNames) {
        try {
            log.info("Start updateTemplateStatus()");
            List<TemplateManage> templates = templateRepository.findAllByUrlNameIn(urlNames);
            if (templates != null) {
                templates = templates.stream().map(templateManage -> {
                    templateManage.setStatus(1);
                    return templateManage;
                }).collect(Collectors.toList());
                templateRepository.saveAll(templates);
            }
            log.info("End updateTemplateStatus()");
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    //GET PROC DEF BY DEF KEY
    public List<BpmProcdef> getProcDefByKey(String deploymentId) {
        try {
            List<Map<String, Object>> dataList = camundaEngineService.getProcessDefinitionByDeploymentId(deploymentId);
            Map<String, Object> dataMap = null;
            if (!dataList.isEmpty()) {
                dataMap = dataList.get(0);
            }
            String key = dataMap != null ? dataMap.get("key").toString() : null;

            return bpmProcdefRepository.getBpmProcdefByKey(key);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * @param criteria
     * @return BpmProcdefForCombo
     * @Description get list bpm for dto
     */
    public PageDto getBpmProcdefForCombo(BpmProcdefDto criteria) {
        int pageNum = criteria.getPage() - 1;
        Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());
        String username = credentialHelper.getJWTPayload().getUsername();
        List<String> companyCode = new ArrayList<>();
        companyCode.add("-1");
        criteria.setListCompanyCode(companyCode);
        criteria.setLstGroupPermissionId(new ArrayList<>());

        Page<BpmProcdef> page = bpmProcdefRepository.findAll(bpmProcdefSpecification.filter(criteria, username), PageRequest.of(pageNum, criteria.getLimit(), sort));

        // filter procDefId exist
        List<String> lstExistProcDef = bpmTemplatePrintRepository.getProcDefIdByPrintType(0, criteria.getProcDefId());
        List<BpmProcdefForCombo> bpmProcdefDto = page.getContent().stream()
                .map(e -> modelMapper.map(e, BpmProcdefForCombo.class))
                .filter(e -> !lstExistProcDef.contains(e.getProcDefId()))
                .collect(Collectors.toList());

        return PageDto.builder()
                .content(bpmProcdefDto)
                .number(criteria.getPage())
                .numberOfElements(criteria.getLimit())
                .page(criteria.getPage())
                .size(criteria.getLimit())
                .totalPages(page.getTotalPages())
                .totalElements(page.getTotalElements())
                .build();
    }

    //LIST AND SEARCH FILTER PROC DEF
    //lấy ra các mẫu quy trình có sẵn
    public PageDto search(BpmProcdefDto criteria) {
        try {
            int pageNum = criteria.getPage() - 1;
            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());
            if (!ValidationUtils.isNullOrEmpty(criteria.getListTemplateName())) {
                List<GroupTableProTemp> groupTableProTemps = groupProcTempRepository.getGroupTableProTempsByFormKeyIn(criteria.getListTemplateName());
                List<String> procDefIds = new ArrayList<>();
                for (GroupTableProTemp groupTableProTemp : groupTableProTemps) {
                    String procDefId = null;
                    procDefId = groupTableProTemp.getProDefId();
                    procDefIds.add(procDefId);
                }
                criteria.setListProcDefId(procDefIds);
            }

            // Lấy list companyCode được phép tác động
            String username = credentialHelper.getJWTPayload().getUsername();
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
            criteria.setListCompanyCode(lstCompanyCode);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.BPM_PROC_DEF.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                criteria.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            Page<BpmProcdef> page = bpmProcdefRepository.findAll(bpmProcdefSpecification.filter(criteria, username), PageRequest.of(pageNum, criteria.getLimit(), sort));
            List<BpmProcdefDto> bpmProcdefDtoList = page.getContent().stream().map(e -> modelMapper.map(e, BpmProcdefDto.class)).collect(Collectors.toList());

            List<Long> lsProcIds = bpmProcdefDtoList.stream().map(BpmProcdefDto::getId).collect(Collectors.toList());
            List<ServicePackage> servicePackages = servicePackageRepo.findAll(servicePackageSpec.getServicePackageByProcIds(lsProcIds));
            Map<Long, List<ServiceNamePopUpResponse>> mapService = new HashMap<>();
            for (ServicePackage service : servicePackages) {
                ServiceNamePopUpResponse serviceRes = new ServiceNamePopUpResponse();
                serviceRes.setId(service.getId().toString());
                serviceRes.setServiceName(service.getServiceName());
                if (mapService.containsKey(service.getProcessId())) {
                    List<ServiceNamePopUpResponse> listExist = mapService.get(service.getProcessId());
                    listExist.add(serviceRes);
                    mapService.put(service.getProcessId(), listExist);
                } else {
                    List<ServiceNamePopUpResponse> listExist = new ArrayList<>();
                    listExist.add(serviceRes);
                    mapService.put(service.getProcessId(), listExist);
                }
            }

            List<Long> procDefIds = bpmProcdefDtoList.stream().map(BpmProcdefDto::getId).collect(Collectors.toList());
            List<BpmOwnerProcess> allBpmOwner = bpmOwnerProcessManager.getAllByProcDefIdIn(procDefIds);

            List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceIdInAndReferenceType(procDefIds, ShareUserTypeEnum.PROCESS.type);

            for (BpmProcdefDto bpmProcdefDto : bpmProcdefDtoList) {

                if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                    List<String> listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(bpmProcdefDto.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
                    bpmProcdefDto.setShareUser(listEmail);
                }
//                List<BpmOwnerProcess> bpmOwnerProcessList = bpmOwnerProcessManager.bpmOwnerProcessByProcDefId(bpmProcdefDto.getId());
                List<BpmOwnerProcess> bpmOwnerProcessList = allBpmOwner.stream().filter(i -> i.getProcDefId().equals(bpmProcdefDto.getId())).collect(Collectors.toList());

                List<BpmOwnerDto> ownerDtos = new ArrayList<>();

                List<String> usernames = bpmOwnerProcessList.stream().map(BpmOwnerProcess::getIdUser).collect(Collectors.toList());
//                List<ChartInfoRoleResponse> chartInfoRoleResponses = responseUtils.getUserByEmail(emails);
//                if (!ValidationUtils.isNullOrEmpty(chartInfoRoleResponses)) {
                for (String name : usernames) {
//                        Long id = chartInfoRoleResponse.getId();
//                        String name = chartInfoRoleResponse.getUsername();
                    BpmOwnerDto bpmOwnerDto = new BpmOwnerDto(null, name);
                    ownerDtos.add(bpmOwnerDto);
                }
//                }
                bpmProcdefDto.setOwners(ownerDtos);

                if (bpmProcdefDto.getStatus().equalsIgnoreCase("deactive")) {
                    bpmProcdefDto.setStatus(ProcDefConstants.Status.DEACTIVE.code);
                } else {
                    bpmProcdefDto.setStatus(ProcDefConstants.Status.ACTIVE.code);
                }
                bpmProcdefDto.setServiceNamePopUp(mapService.get(bpmProcdefDto.getId()));

                //view share

//                SharedUser sharedUser = shareUserRepository.findAllByReferenceIdAndReferenceType(bpmProcdefDto.getId(), ShareUserTypeEnum.PROCESS.type);
//                if (!ValidationUtils.isNullOrEmpty(sharedUser)) {
//                    ObjectMapper mapper = new ObjectMapper();
//                    List<String> listEmail = mapper.readValue(sharedUser.getEmail(), new TypeReference<List<String>>() {
//                    });
//                    bpmProcdefDto.setShareUser(listEmail);
//                }

            }

            //Get companyCode+name in special child when get Parent (specFlow = false) + template + templateName
            List<Long> parentIds = bpmProcdefDtoList.stream().map(BpmProcdefDto::getId).collect(Collectors.toList());
            List<ChildResponse> child = bpmProcdefRepository.findSpecialFlowChildDataByParentIds(parentIds);

            Set<String> companyCodes = child.stream().map(ChildResponse::getSpecialCompanyCode).collect(Collectors.toSet());
            List<NameAndCodeCompanyResponse> allCompanyCodeAndName = customerService.findAllCompanyCodeAndNameByCompanyCode(new ArrayList<>(companyCodes));

            for (ChildResponse item : child) {
                String specialName = allCompanyCodeAndName.stream().filter(i -> i.getCompanyCode().equals(item.getSpecialCompanyCode())).findFirst().orElse(new NameAndCodeCompanyResponse()).getCompanyName();
                item.setSpecialCompanyName(specialName);
            }

            String regex = "\\-[\\d\\w]+\\-[\\d\\w]+$";
            Set<String> allFormKey = new HashSet<>();

            for (BpmProcdefDto item : bpmProcdefDtoList) {
                List<ChildTemplateResponse> templateParent = new ArrayList<>();
                if (criteria.getSpecialFlow()) {
                    JSONArray jsonArray = new JSONArray(item.getSpecialFormKey());
                    for (int i = 0; i < jsonArray.length(); i++) {
                        String companyCode = jsonArray.get(i).toString();
                        allFormKey.add(companyCode);
                        templateParent.add(new ChildTemplateResponse(companyCode, ""));
                    }
                } else {
                    List<ChildResponse> childRes = child.stream().filter(i -> i.getParentId().equals(item.getId())).collect(Collectors.toList());

                    for (ChildResponse childR : childRes) {
                        JSONArray jsonArray = new JSONArray(childR.getSpecialFormKey());
                        List<ChildTemplateResponse> templateChild = new ArrayList<>();
                        for (int i = 0; i < jsonArray.length(); i++) {
                            String companyCode = jsonArray.get(i).toString();
                            allFormKey.add(companyCode);
                            templateChild.add(new ChildTemplateResponse(companyCode, ""));
                            if (companyCode != null) {
                                String newCompanyCode = companyCode.replaceAll(regex, "");
                                allFormKey.add(newCompanyCode);
                                templateParent.add(new ChildTemplateResponse(newCompanyCode, ""));
                            }
                        }
                        childR.setTemplates(templateChild.stream().distinct().collect(Collectors.toList()));
                    }
                    item.setChild(childRes.stream().distinct().collect(Collectors.toList()));

                }
                item.setTemplates(templateParent);
            }
            List<TemplateManage> allTemplate = templateRepository.findAllByUrlNameIn(new ArrayList<>(allFormKey));

            List<Long> procDefStringIds = bpmProcdefDtoList.stream().map(BpmProcdefDto::getId).collect(Collectors.toList());
            List<TemplatePrintApplyResponse> printApply = bpmTemplatePrintRepository.getTemplatePrintApplyProcdef(procDefStringIds);
            for (BpmProcdefDto item : bpmProcdefDtoList) {
                List<String> prints = printApply.stream().filter(i -> i.getProcDefId().equals(item.getProcDefId())).map(TemplatePrintApplyResponse::getName).collect(Collectors.toList());
                item.setPrintApply(prints);
                for (ChildTemplateResponse templateParent : item.getTemplates()) {
                    templateParent.setTemplateName(allTemplate.stream().filter(i -> i.getUrlName().equals(templateParent.getSpecialFormKey())).findFirst().orElse(new TemplateManage()).getTemplateName());
                }

                if (!ValidationUtils.isNullOrEmpty(item.getChild()))
                    for (ChildResponse c : item.getChild()) {
                        for (ChildTemplateResponse templateParent : c.getTemplates()) {
                            templateParent.setTemplateName(allTemplate.stream().filter(i -> i.getUrlName().equals(templateParent.getSpecialFormKey())).findFirst().orElse(new TemplateManage()).getTemplateName());
                        }
                    }
            }


            return PageDto.builder().content(bpmProcdefDtoList)
                    .number(criteria.getPage())
                    .numberOfElements(criteria.getLimit())
                    .page(criteria.getPage())
                    .size(criteria.getLimit())
                    .totalPages(page.getTotalPages())
                    .totalElements(page.getTotalElements())
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<BpmProcdefDto> searchForLegislative(BpmProcdefDto criteria) {
        try {
            int pageNum = criteria.getPage() - 1;
            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());
            if (!ValidationUtils.isNullOrEmpty(criteria.getListTemplateName())) {
                List<GroupTableProTemp> groupTableProTemps = groupProcTempRepository.getGroupTableProTempsByFormKeyIn(criteria.getListTemplateName());
                List<String> procDefIds = new ArrayList<>();
                for (GroupTableProTemp groupTableProTemp : groupTableProTemps) {
                    String procDefId = null;
                    procDefId = groupTableProTemp.getProDefId();
                    procDefIds.add(procDefId);
                }
                criteria.setListProcDefId(procDefIds);
            }

            // Lấy list companyCode được phép tác động
            String username = credentialHelper.getJWTPayload().getUsername();
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
            criteria.setListCompanyCode(lstCompanyCode);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.BPM_PROC_DEF.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                criteria.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            Page<BpmProcdef> page = bpmProcdefRepository.findAll(bpmProcdefSpecification.filter(criteria, username), PageRequest.of(pageNum, criteria.getLimit(), sort));
            List<BpmProcdefDto> bpmProcdefDtoList = page.getContent().stream().map(e -> modelMapper.map(e, BpmProcdefDto.class)).collect(Collectors.toList());

            return bpmProcdefDtoList;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    //Clone dùng cho filter
    public List<BpmProcdefDto> searchFilter(BpmProcdefDto criteria) {
        try {
            if (!ValidationUtils.isNullOrEmpty(criteria.getListTemplateName())) {
                List<GroupTableProTemp> groupTableProTemps = groupProcTempRepository.getGroupTableProTempsByFormKeyIn(criteria.getListTemplateName());
                List<String> procDefIds = new ArrayList<>();
                for (GroupTableProTemp groupTableProTemp : groupTableProTemps) {
                    String procDefId = null;
                    procDefId = groupTableProTemp.getProDefId();
                    procDefIds.add(procDefId);
                }
                criteria.setListProcDefId(procDefIds);
            }

            // Lấy list companyCode được phép tác động
            String username = credentialHelper.getJWTPayload().getUsername();
//            String username = "employee";
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

            criteria.setListCompanyCode(lstCompanyCode);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.BPM_PROC_DEF.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                criteria.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            List<BpmProcdef> page = bpmProcdefRepository.findAll(bpmProcdefSpecification.filter(criteria, username));
            List<BpmProcdefDto> bpmProcdefDtoList = page.stream().map(e -> modelMapper.map(e, BpmProcdefDto.class)).collect(Collectors.toList());

            List<Long> lsProcIds = bpmProcdefDtoList.stream().map(BpmProcdefDto::getId).collect(Collectors.toList());
            List<ServicePackage> servicePackages = servicePackageRepo.findAll(servicePackageSpec.getServicePackageByProcIds(lsProcIds));
            Map<Long, List<ServiceNamePopUpResponse>> mapService = new HashMap<>();
            for (ServicePackage service : servicePackages) {
                ServiceNamePopUpResponse serviceRes = new ServiceNamePopUpResponse();
                serviceRes.setId(service.getId().toString());
                serviceRes.setServiceName(service.getServiceName());
                if (mapService.containsKey(service.getProcessId())) {
                    List<ServiceNamePopUpResponse> listExist = mapService.get(service.getProcessId());
                    listExist.add(serviceRes);
                    mapService.put(service.getProcessId(), listExist);
                } else {
                    List<ServiceNamePopUpResponse> listExist = new ArrayList<>();
                    listExist.add(serviceRes);
                    mapService.put(service.getProcessId(), listExist);
                }
            }

            for (BpmProcdefDto bpmProcdefDto : bpmProcdefDtoList) {

                if (bpmProcdefDto.getStatus().equalsIgnoreCase("deactive")) {
                    bpmProcdefDto.setStatus(ProcDefConstants.Status.DEACTIVE.code);
                } else {
                    bpmProcdefDto.setStatus(ProcDefConstants.Status.ACTIVE.code);
                }
                bpmProcdefDto.setServiceNamePopUp(mapService.get(bpmProcdefDto.getId()));

            }

            return bpmProcdefDtoList;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<String> shareWithUser() {
        try {
            String token = credentialHelper.getJWTToken();
//            String realm = credentialH/elper.getRealm();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("key_role", "public");
//            //headers.set("realm", realm);
            headers.set("Authorization", "Bearer " + token);
            HttpEntity<String> requestEntity = new HttpEntity<String>(headers);
            ResponseEntity<List<String>> responseCustomer = restTemplate.exchange(sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key) + "/customerInfo/getAllCustomer", HttpMethod.GET, requestEntity, new ParameterizedTypeReference<List<String>>() {
            });

            List<String> listCus = responseCustomer.getBody();
            if (CollectionUtils.isEmpty(listCus)) {
                throw new ErrorMessage("");
            }
            return listCus;
        } catch (Exception e) {
            throw new ErrorMessage("");
        }
    }

    public PagingResponse<BpmProcDefMinDto> findAll(QueryRequest<BpmProcdefDto> request) {
        if (request == null) {
            throw new RuntimeException("Query object must not be null");
        }

        Page<BpmProcdef> page = null;
        List<BpmProcdef> bpmProcdefs = null;
        String username = credentialHelper.getJWTPayload().getUsername();
        if (request.getPageInfo() != null) {
            page = bpmProcdefRepository.findAll(bpmProcdefSpecification.filter(request.getSample(), username), PageRequest.of(request.getPageInfo().getPageNumber(), request.getPageInfo().getPageSize(), SortUtils.buildSort(request.getOrders())));
        } else {
            bpmProcdefs = bpmProcdefRepository.findAll(bpmProcdefSpecification.filter(request.getSample(), username), SortUtils.buildSort(request.getOrders()));
        }

        bpmProcdefs = (bpmProcdefs == null) ? page.getContent() : bpmProcdefs;
        List<BpmProcDefMinDto> bpmProcdefDtos = bpmProcdefs.stream().map(e -> modelMapper.map(e, BpmProcDefMinDto.class)).collect(Collectors.toList());

        return PagingResponse.<BpmProcDefMinDto>builder()
                .content(bpmProcdefDtos)
                .totalPages(page != null ? page.getTotalPages() : 1)
                .totalElements(page != null ? page.getTotalElements() : bpmProcdefDtos.size())
                .numberOfElements(page != null ? page.getNumberOfElements() : bpmProcdefDtos.size())
                .pageNumber(page != null ? page.getNumber() : 0)
                .pageSize(page != null ? page.getSize() : bpmProcdefDtos.size())
                .build();
    }

    /**
     * Find process definition by service-id
     *
     * <AUTHOR>
     */
    @Transactional(noRollbackFor = RuntimeException.class)
    public BpmProcdef findByServiceId(Long serviceId) {
        try {
            return bpmProcdefRepository.findByServiceId(serviceId);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return null;


    }

    public GetAllNameDto getAllFilterName() {
        try {
            Set<Map<String, Object>> setResourceName = new HashSet<>();
            Set<Map<String, Object>> setServiceName = new HashSet<>();
            Set<Map<String, Object>> setTemplateName = new HashSet<>();

            List<Object[]> listObjectResource = bpmProcdefRepository.getResouceName();
            List<Object[]> listObjectService = servicePackageRepo.getServiceNameAndId();
            List<Object[]> listObjectTemplate = templateRepository.getTemplateNameAndId();

            for (Object[] resouce : listObjectResource) {
                Map<String, Object> resouceName = new LinkedHashMap<>();
                if (!ValidationUtils.isNullOrEmpty(resouce)) {
                    resouceName.put("resourceName", resouce[0].toString());
                    setResourceName.add(resouceName);
                }

            }
            for (Object[] service : listObjectService) {
                Map<String, Object> serviceName = new LinkedHashMap<>();
                if (!ValidationUtils.isNullOrEmpty(service)) {
                    serviceName.put("id", service[0]);
                    serviceName.put("serviceName", service[1].toString());
                    setServiceName.add(serviceName);
                }

            }
            for (Object[] template : listObjectTemplate) {
                Map<String, Object> templateName = new LinkedHashMap<>();
                if (!ValidationUtils.isNullOrEmpty(template)) {
                    templateName.put("id", template[0].toString());
                    templateName.put("templateName", template[1].toString());
                    templateName.put("urlName", template[2].toString());
                    setTemplateName.add(templateName);
                }
            }
            GetAllNameDto getAllNameDto = new GetAllNameDto();
            getAllNameDto.setListObjectResource(setResourceName);
            getAllNameDto.setListObjectService(setServiceName);
            getAllNameDto.setListObjectTemplate(setTemplateName);

            return getAllNameDto;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    public void handleControlDto(Map<String, Object> fd, List<TemplateFormControlDto> controls, Boolean getAllNoFilter) {
        TemplateFormControlDto control = new TemplateFormControlDto();
        control.setType(fd.get("type") != null ? fd.get("type").toString() : null);
        //Cấu hình trong biểu mẫu
        boolean isAuthCondition = fd.get("isConditionAuth") != null && (boolean) fd.get("isConditionAuth");
        if ((getAllNoFilter || isAuthCondition) && !this.isExcludedControl(control.getType())) {
            control.setId(fd.get("id") != null ? fd.get("id").toString() : null);
            control.setName(fd.get("name") != null ? fd.get("name").toString() : null);
            control.setLabel(fd.get("label") != null ? fd.get("label").toString() : null);
            control.setDisplay(fd.get("display") != null && Boolean.parseBoolean(fd.get("display").toString()));
            control.setReadonly(fd.get("readonly") != null && Boolean.parseBoolean(fd.get("readonly").toString()));
            control.setFieldSortOrder(fd.get("fieldSortOrder") != null ? Integer.parseInt(fd.get("fieldSortOrder").toString()) : 0);
            controls.add(control);
        }
    }

    public List<TemplateFormControlDto> getTemplateFormControls(String procDefId, Boolean getAllNoFilter) {
        List<TemplateFormControlDto> controls = new ArrayList<>();


        try {
            BpmProcdef bpmProcdef = this.bpmProcdefRepository.findBpmProcdefByProcDefId(procDefId);
            if (null != bpmProcdef) {
                DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
                DocumentBuilder builder = factory.newDocumentBuilder();
                InputSource is = new InputSource(new StringReader(new String(bpmProcdef.getBytes(), StandardCharsets.UTF_8)));
                Document doc = builder.parse(is);
                doc.getDocumentElement().normalize();

                NodeList list = doc.getElementsByTagName("bpmn:startEvent");
                String urlName = null;
                for (int temp = 0; temp < list.getLength(); temp++) {
                    Node node = list.item(temp);
                    if (node.getNodeType() == Node.ELEMENT_NODE) {
                        Element element = (Element) node;
                        String id = element.getAttribute("id");
                        if (id.equals("start")) {
                            urlName = element.getAttribute("camunda:formKey");
                            break;
                        }
                    }
                }

                if (urlName != null) {
                    TemplateManage templateManage = this.templateRepository.findByUrlName(urlName);
                    List<Map<String, Object>> formData = new ArrayList<>();
                    if (null != templateManage) {
                        // special flow thì lấy thêm template cha
                        if (templateManage.getSpecialFlow() != null && templateManage.getSpecialFlow() && templateManage.getSpecialParentId() != null) {
                            TemplateManage templateParent = templateRepository.findById(templateManage.getSpecialParentId()).get();
                            if (!ValidationUtils.isNullOrEmpty(templateParent.getTemplate())) {
                                Map<String, List<Map<String, Object>>> resultParent =
                                        new ObjectMapper().readValue(templateParent.getTemplate(), HashMap.class);
                                formData.addAll(resultParent.get("form"));
                            }
                        }
                        if (!ValidationUtils.isNullOrEmpty(templateManage.getTemplate())) {
                            Map<String, List<Map<String, Object>>> result = new ObjectMapper().readValue(templateManage.getTemplate(), HashMap.class);
                            formData.addAll(result.get("form"));
                        }

                        formData.forEach(fd -> {
                            String type = fd.get("type").toString();
                            if (type.matches("table|matrix") && fd.get("columns") != null) {
                                if (getAllNoFilter) {
                                    List<Map<String, Object>> listChild = (List<Map<String, Object>>) fd.get("columns");
                                    if (listChild != null) {
                                        listChild = listChild.subList(listChild.size() / 2, listChild.size());
                                        listChild.forEach(i -> handleControlDto(i, controls, getAllNoFilter));
                                    }
                                }
                            } else if (!type.matches("label|splitter")) {
                                handleControlDto(fd, controls, getAllNoFilter);
                            }
                        });
                    }
                }
            }

            return controls;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    private boolean isExcludedControl(String controlType) {
        boolean isExcluded = false;
        String[] EXCLUDED_TYPES = {"label", "splitter", "tab", "divider", "file", ""};
        for (String element : EXCLUDED_TYPES) {
            if (Objects.equals(element, controlType)) {
                isExcluded = true;
                break;
            }
        }
        return isExcluded;
    }

    public List<Map<String, Object>> getServicePackage() {
        return bpmProcdefRepository.getServicePackage();
    }

    // Clone luồng đặc biệt
    public BpmProcdef createCopySpecial(BpmProcdef bpmProcdef, String deploymentId, String companyCode, List<String> lstFormKey, Long parentServiceId) {
        try {
            List<Map<String, Object>> mapResponseGet = camundaEngineService.getProcessDefinitionByDeploymentId(deploymentId);
            Map<String, Object> stringObjectMap = !mapResponseGet.isEmpty() ? mapResponseGet.get(0) : null;
            String procDefId = stringObjectMap != null ? stringObjectMap.get("id").toString() : bpmProcdef.getProcDefId();
            String key = stringObjectMap != null ? stringObjectMap.get("key").toString() : bpmProcdef.getKey();
            String resource = stringObjectMap != null ? stringObjectMap.get("resource").toString() : bpmProcdef.getResourceName();
            String deployment = stringObjectMap != null ? stringObjectMap.get("deploymentId").toString() : deploymentId;
            String specialFormKey = new Gson().toJson(lstFormKey);

            //Get user login
            String username = credentialHelper.getJWTPayload().getUsername();

            // check qt con da duoc tao -> update (giữ nguyên info file camunda đã cấu hình từ trước)
            BpmProcdef bpmProcdefCopy = bpmProcdefRepository.findBpmProcdefBySpecialParentIdAndSpecialCompanyCodeAndSpecialParentServiceId(bpmProcdef.getId(), companyCode, parentServiceId);
            if (ValidationUtils.isNullOrEmpty(bpmProcdefCopy)) {
                bpmProcdefCopy = new BpmProcdef();

                // tạo bản ghi mới set info file camunda
                bpmProcdefCopy.setResourceName(resource);
                bpmProcdefCopy.setDeploymentId(deployment);
                bpmProcdefCopy.setBytes(bpmProcdef.getBytes());
                bpmProcdefCopy.setProcDefId(procDefId);

                bpmProcdefCopy.setName(bpmProcdef.getName() + "-" + parentServiceId.toString() + "-" + companyCode);
                bpmProcdefCopy.setProcessType(bpmProcdef.getProcessType());
                bpmProcdefCopy.setDescription(bpmProcdef.getDescription());
                bpmProcdefCopy.setKey(key);
                bpmProcdefCopy.setPrioritized(bpmProcdef.getPrioritized());
                bpmProcdefCopy.setUserCreated(username);
                bpmProcdefCopy.setCreatedDate(LocalDateTime.now());
                bpmProcdefCopy.setAutoClose(bpmProcdef.getAutoClose());
                bpmProcdefCopy.setAutoCancel(bpmProcdef.getAutoCancel());
                bpmProcdefCopy.setStatus(ProcDefConstants.Status.ACTIVE.code);
                bpmProcdefCopy.setLocation(bpmProcdef.getLocation());
                bpmProcdefCopy.setInFormTo(bpmProcdef.getInFormTo());
                bpmProcdefCopy.setStepByStepResultForCreate(bpmProcdef.getStepByStepResultForCreate());
                bpmProcdefCopy.setUpdate(bpmProcdef.getUpdate());
                bpmProcdefCopy.setRequestUpdate(bpmProcdef.getRequestUpdate());
                bpmProcdefCopy.setHideRuTasks(bpmProcdef.getHideRuTasks());
                bpmProcdefCopy.setCreateNewAndDouble(bpmProcdef.getCreateNewAndDouble());
                bpmProcdefCopy.setPriorityId(bpmProcdef.getPriorityId());
                bpmProcdefCopy.setCancel(bpmProcdef.getCancel());
                bpmProcdefCopy.setCancelTasks(bpmProcdef.getCancelTasks());

                bpmProcdefCopy.setShowInfo(bpmProcdef.getShowInfo());
                bpmProcdefCopy.setHideInfo(bpmProcdef.getHideInfo());
                bpmProcdefCopy.setIsAssistant(bpmProcdef.getIsAssistant());
                bpmProcdefCopy.setIsEditAssistant(bpmProcdef.getIsEditAssistant());
                bpmProcdefCopy.setHideInfoTasks(bpmProcdef.getHideInfoTasks());
                bpmProcdefCopy.setShowInfoTaks(bpmProcdef.getShowInfoTaks());
                bpmProcdefCopy.setIsAutoCancel(bpmProcdef.getIsAutoCancel());

                // auto complete
                bpmProcdefCopy.setAutoCompleteTask(bpmProcdef.getAutoCompleteTask());

                bpmProcdefCopy.setAdditionalRequest(bpmProcdef.getAdditionalRequest());
                bpmProcdefCopy.setHideRelatedTicket(bpmProcdef.getHideRelatedTicket());
                bpmProcdefCopy.setHideRelatedTicketValue(bpmProcdef.getHideRelatedTicketValue());
                bpmProcdefCopy.setRecall(bpmProcdef.getRecall());
                bpmProcdefCopy.setShowInputTask(bpmProcdef.getShowInputTask());
                bpmProcdefCopy.setShowInputTaskDefKeys(bpmProcdef.getShowInputTaskDefKeys());

                bpmProcdefCopy.setHideInherit(bpmProcdef.getHideInherit());
                bpmProcdefCopy.setHideInheritTasks(bpmProcdef.getHideInheritTasks());

                bpmProcdefCopy.setHideComment(bpmProcdef.getHideComment());
                bpmProcdefCopy.setHideCommentTasks(bpmProcdef.getHideCommentTasks());

                bpmProcdefCopy.setHideDownload(bpmProcdef.getHideDownload());
                bpmProcdefCopy.setHideDownloadTasks(bpmProcdef.getHideDownloadTasks());

                bpmProcdefCopy.setHideShareTicket(bpmProcdef.getHideShareTicket());
                bpmProcdefCopy.setChangeImplementerValue(bpmProcdef.getChangeImplementerValue());

                if (!ValidationUtils.isNullOrEmpty(bpmProcdef.getAutoInherits())) {
                    bpmProcdefCopy.setAutoInherits(bpmProcdef.getAutoInherits());
                }
                if (!ValidationUtils.isNullOrEmpty(bpmProcdef.getOffNotification())) {
                    bpmProcdefCopy.setOffNotification(bpmProcdef.getOffNotification());
                }

                if (bpmProcdef.getAutoInherits() != null) {
                    bpmProcdefCopy.setAutoInherits(bpmProcdef.getAutoInherits());
                }
                if (bpmProcdef.getOffNotification() != null) {
                    bpmProcdefCopy.setOffNotification(bpmProcdef.getOffNotification());
                }

                bpmProcdefCopy.setSpecialFlow(true);
                bpmProcdefCopy.setSpecialParentId(bpmProcdef.getId());
                bpmProcdefCopy.setSpecialCompanyCode(companyCode);
                bpmProcdefCopy.setSpecialFormKey(specialFormKey);
                bpmProcdefCopy.setSpecialParentServiceId(parentServiceId);

                List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
                if (!ValidationUtils.isNullOrEmpty(listCompanyCodeAndName)) {
                    NameAndCodeCompanyResponse response = listCompanyCodeAndName.get(0);
                    bpmProcdefCopy.setCompanyCode(response.getCompanyCode());
                    bpmProcdefCopy.setCompanyName(response.getCompanyName());
                }
                bpmProcdefCopy.setDisableApprovedTicket(bpmProcdef.getDisableApprovedTicket());
                bpmProcdefCopy.setWarningApprovedTicket(bpmProcdef.getWarningApprovedTicket());
                bpmProcdefCopy.setDisabledApprovedTicketTasks(bpmProcdef.getDisabledApprovedTicketTasks());
                bpmProcdefCopy.setWarningApprovedTicketTasks(bpmProcdef.getWarningApprovedTicketTasks());
                bpmProcdefCopy.setLegislativeRequirement(bpmProcdef.getLegislativeRequirement());
                bpmProcdefCopy = bpmProcdefRepository.save(bpmProcdefCopy);

                // save procDef history
                BpmProcDefHistory bpmProcdefHistory = new BpmProcDefHistory();
                bpmProcdefHistory.setProcessType(bpmProcdef.getProcessType());
                bpmProcdefHistory.setProcDefId(procDefId);
                bpmProcdefHistory.setName(bpmProcdef.getName());
                bpmProcdefHistory.setDescription(bpmProcdef.getDescription());
                bpmProcdefHistory.setKey(key);
                bpmProcdefHistory.setDeploymentId(deployment);
                bpmProcdefHistory.setResourceName(resource);
                bpmProcdefHistory.setBytes(bpmProcdef.getBytes());
                bpmProcdefHistory.setPrioritized(bpmProcdef.getPrioritized());
                bpmProcdefHistory.setUserCreated(credentialHelper.getJWTPayload().getUsername());
                bpmProcdefHistory.setCreatedDate(LocalDateTime.now());
                bpmProcdefHistory.setAutoClose(bpmProcdef.getAutoClose());
                bpmProcdefHistory.setAutoCancel(bpmProcdef.getAutoCancel());
                bpmProcdefHistory.setStatus(ProcDefConstants.Status.DEACTIVE.code);
                bpmProcdefHistory.setLocation(bpmProcdef.getLocation());
                bpmProcdefHistory.setInFormTo(bpmProcdef.getInFormTo());
                bpmProcdefHistory.setStepByStepResultForCreate(bpmProcdef.getStepByStepResultForCreate());
                bpmProcdefHistory.setUpdate(bpmProcdef.getUpdate());
                bpmProcdefHistory.setRequestUpdate(bpmProcdef.getRequestUpdate());
                bpmProcdefHistory.setHideRuTasks(bpmProcdef.getHideRuTasks());
                bpmProcdefHistory.setCreateNewAndDouble(bpmProcdef.getCreateNewAndDouble());
                bpmProcdefHistory.setPriorityId(bpmProcdef.getPriorityId());
                bpmProcdefHistory.setCancel(bpmProcdef.getCancel());
                bpmProcdefHistory.setCancelTasks(bpmProcdef.getCancelTasks());
                bpmProcdefHistory.setChangeImplementerValue(bpmProcdef.getChangeImplementerValue());

                bpmProcdefHistory.setShowInfo(bpmProcdef.getShowInfo());
                bpmProcdefHistory.setHideInfo(bpmProcdef.getHideInfo());
                bpmProcdefHistory.setIsAssistant(bpmProcdef.getIsAssistant());
                bpmProcdefHistory.setIsEditAssistant(bpmProcdef.getIsEditAssistant());
                bpmProcdefHistory.setHideInfoTasks(bpmProcdef.getHideInfoTasks());
                bpmProcdefHistory.setShowInfoTaks(bpmProcdef.getShowInfoTaks());
                bpmProcdefHistory.setIsAutoCancel(bpmProcdef.getIsAutoCancel());
                bpmProcdefHistory.setAdditionalRequest(bpmProcdef.getAdditionalRequest());
                bpmProcdefHistory.setHideRelatedTicket(bpmProcdef.getHideRelatedTicket());
                bpmProcdefHistory.setHideRelatedTicketValue(bpmProcdef.getHideRelatedTicketValue());
                bpmProcdefHistory.setAuthorityOnTicket(bpmProcdef.getAuthorityOnTicket());
                bpmProcdefHistory.setAuthorityOnTicketValue(bpmProcdef.getAuthorityOnTicketValue());
                bpmProcdefHistory.setAuthorityOnTicketStep(bpmProcdef.getAuthorityOnTicketStep());
                bpmProcdefHistory.setRecall(bpmProcdef.getRecall());
                bpmProcdefHistory.setShowInputTask(bpmProcdef.getShowInputTask());
                bpmProcdefHistory.setShowInputTaskDefKeys(bpmProcdef.getShowInputTaskDefKeys());
                bpmProcdefHistory.setHideInherit(bpmProcdef.getHideInherit());
                bpmProcdefHistory.setHideInheritTasks(bpmProcdef.getHideInheritTasks());
                bpmProcdefHistory.setHideComment(bpmProcdef.getHideComment());
                bpmProcdefHistory.setHideCommentTasks(bpmProcdef.getHideCommentTasks());
                bpmProcdefHistory.setHideDownload(bpmProcdef.getHideDownload());
                bpmProcdefHistory.setHideDownloadTasks(bpmProcdef.getHideDownloadTasks());
                bpmProcdefHistory.setHideShareTicket(bpmProcdef.getHideShareTicket());
                bpmProcdefHistory.setAutoInherits(bpmProcdef.getAutoInherits());
                bpmProcdefHistory.setOffNotification(bpmProcdef.getOffNotification());

                bpmProcdefHistory.setSpecialFlow(true);
                bpmProcdefHistory.setSpecialParentId(bpmProcdef.getId());
                bpmProcdefHistory.setSpecialCompanyCode(companyCode);
                bpmProcdefHistory.setSpecialFormKey(specialFormKey);
                bpmProcdefHistory.setSpecialParentServiceId(parentServiceId);

                if (!ValidationUtils.isNullOrEmpty(listCompanyCodeAndName)) {
                    for (NameAndCodeCompanyResponse response : listCompanyCodeAndName) {
                        bpmProcdefHistory.setCompanyCode(response.getCompanyCode());
                        bpmProcdefHistory.setCompanyName(response.getCompanyName());
                    }
                }
                bpmProcdefHistory.setAutoCompleteTask(bpmProcdef.getAutoCompleteTask());
                bpmProcdefHistory.setOrgProcessId(bpmProcdefCopy.getId());
                bpmProcdefHistory.setContentEdit("Thêm mới thành công quy trình");
                bpmProcdefHistory.setVersion("V0");

                Gson g = new GsonBuilder()
                        .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                        .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                        .create();
                // Shared user
                List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceIdAndReferenceType(bpmProcdef.getId(), ShareUserTypeEnum.PROCESS.type);
                if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                    List<SharedUser> sharedUsersCopy = new ArrayList<>();
                    for (SharedUser sharedUser : sharedUsers) {
                        SharedUser sharedUserCopy = new SharedUser();
                        sharedUserCopy.setReferenceId(bpmProcdefCopy.getId());
                        sharedUserCopy.setReferenceType(ShareUserTypeEnum.PROCESS.type);
                        sharedUserCopy.setEmail(sharedUser.getEmail());
                        sharedUsersCopy.add(sharedUserCopy);
                    }
                    shareUserRepository.saveAll(sharedUsersCopy);
                    bpmProcdefHistory.setShareWith(g.toJson(sharedUsersCopy));
                }

                //CREATE bpmOwnerProcessManager
                List<BpmOwnerProcess> bpmOwnerProcessByProcDefId = bpmOwnerProcessManager.bpmOwnerProcessByProcDefId(bpmProcdef.getId());
                List<String> owners = new ArrayList<>();
                for (BpmOwnerProcess bpmOwnerProcess : bpmOwnerProcessByProcDefId) {
                    owners.add(bpmOwnerProcess.getIdUser());
                }
                List<BpmOwnerProcess> bpmOwnerProcessList = new ArrayList<>();
                for (String owner : owners) {
                    BpmOwnerProcess bpmOwnerProcess = new BpmOwnerProcess();
                    bpmOwnerProcess.setProcDefId(bpmProcdefCopy.getId());
                    bpmOwnerProcess.setIdUser(owner);
                    bpmOwnerProcessList.add(bpmOwnerProcess);
                }
                bpmOwnerProcessManager.saveAll(bpmOwnerProcessList);
                bpmProcdefHistory.setProcessOwner(g.toJson(bpmOwnerProcessList));

                List<BpmProcdefApi> bpmProcdefApiList = bpmProcdefApiManager.listBpmProcdefApi(bpmProcdef.getId());
                if (!ValidationUtils.isNullOrEmpty(bpmProcdefApiList)) {
                    // Xóa cấu hình cũ nếu có
                    List<BpmProcdefApi> bpmProcdefApiCloneList = bpmProcdefApiManager.listBpmProcdefApi(bpmProcdefCopy.getId());
                    if (!ValidationUtils.isNullOrEmpty(bpmProcdefApiCloneList)) {
                        bpmProcdefApiRepository.deleteAll(bpmProcdefApiCloneList);
                    }

                    List<BpmProcdefApi> bpmProcdefApis = new ArrayList<>();
                    for (BpmProcdefApi bpmProcdefApiCopy : bpmProcdefApiList) {

                        BpmProcdefApi bpmProcdefApi = new BpmProcdefApi();
                        bpmProcdefApi.setBpmProcdefId(bpmProcdefCopy.getId());
                        bpmProcdefApi.setTaskDefKey(bpmProcdefApiCopy.getTaskDefKey());
                        bpmProcdefApi.setActionId(bpmProcdefApiCopy.getActionId());
                        bpmProcdefApi.setApiId(bpmProcdefApiCopy.getApiId());
                        bpmProcdefApi.setHeader(bpmProcdefApiCopy.getHeader());
                        bpmProcdefApi.setBody(bpmProcdefApiCopy.getBody());
                        bpmProcdefApi.setUpdatedTime(LocalDateTime.now());
                        bpmProcdefApi.setUpdatedUser(username);
                        bpmProcdefApi.setProcDefId(bpmProcdefCopy.getProcDefId());
                        bpmProcdefApi.setStatus(bpmProcdefApiCopy.getStatus());
                        bpmProcdefApi.setCallOrder(bpmProcdefApiCopy.getCallOrder());
                        bpmProcdefApi.setSuccessCondition(bpmProcdefApiCopy.getSuccessCondition());
                        bpmProcdefApi.setResponse(bpmProcdefApiCopy.getResponse());
                        bpmProcdefApi.setContinueOnError(bpmProcdefApiCopy.getContinueOnError());
                        bpmProcdefApi.setCallCondition(bpmProcdefApiCopy.getCallCondition());

                        bpmProcdefApis.add(bpmProcdefApi);
                    }
                    bpmProcdefApiManager.saveAll(bpmProcdefApis);
                }

                List<BpmProcdefInherits> bpmProcdefInherits = bpmProcdefInheritsRepository.getAllByBpmProcdefId(bpmProcdef.getId());
                if (!ValidationUtils.isNullOrEmpty(bpmProcdefInherits)) {
                    // Xóa cấu hình cũ nếu có
                    List<BpmProcdefInherits> bpmProcdefCloneInherits = bpmProcdefInheritsRepository.getAllByBpmProcdefId(bpmProcdefCopy.getId());
                    if (!ValidationUtils.isNullOrEmpty(bpmProcdefCloneInherits)) {
                        bpmProcdefInheritsRepository.deleteAll(bpmProcdefCloneInherits);
                    }

                    List<BpmProcdefInherits> bpmProcdefInheritsCopy = new ArrayList<>();
                    for (BpmProcdefInherits listBpmProcdefInherits : bpmProcdefInherits) {
                        BpmProcdefInherits listCopy = new BpmProcdefInherits();
                        listCopy.setBpmProcdefId(bpmProcdefCopy.getId());
                        listCopy.setProcDefId(bpmProcdefCopy.getProcDefId());
                        listCopy.setTaskDefKey(listBpmProcdefInherits.getTaskDefKey());
                        listCopy.setTaskDefKeyInherits(listBpmProcdefInherits.getTaskDefKeyInherits());
                        listCopy.setFieldInherits(listBpmProcdefInherits.getFieldInherits());
                        listCopy.setStatus(listBpmProcdefInherits.getStatus());
                        listCopy.setCreatedTime(LocalDateTime.now());
                        listCopy.setCreatedUser(username);
                        bpmProcdefInheritsCopy.add(listCopy);
                    }
                    bpmProcdefInheritsRepository.saveAll(bpmProcdefInheritsCopy);
                }

                // Lưu phân quyền dữ liệu
                // Xóa data cũ
                List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(bpmProcdefCopy.getId(), PermissionDataConstants.Type.BPM_PROCDEF.code);
                if (!ValidationUtils.isNullOrEmpty(oldData)) {
                    permissionDataManagementRepository.deleteAll(oldData);
                }
                PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                permissionDataManagement.setTypeId(bpmProcdefCopy.getId());
                permissionDataManagement.setTypeName(PermissionDataConstants.Type.BPM_PROCDEF.code);
                permissionDataManagement.setCompanyCode(companyCode);
                permissionDataManagement.setCreatedUser(username);
                permissionDataManagement.setCreatedTime(LocalDateTime.now());
                permissionDataManagementRepository.save(permissionDataManagement);

                bpmProcdefHistory.setApplyFor(g.toJson(Collections.singletonList(permissionDataManagement)));

                bpmProcDefHistoryRepository.save(bpmProcdefHistory);

                // Lưu cấu hình thông báo
                List<BpmProcdefNotification> notifications = bpmProcdefNotificationRepository.findBpmProcdefNotificationsByBpmProcdefId(bpmProcdef.getId());
                if (!ValidationUtils.isNullOrEmpty(notifications)) {
                    List<BpmProcdefNotification> bpmProcdefNotifications = new ArrayList<>();
                    for (BpmProcdefNotification notification : notifications) {
                        BpmProcdefNotification cloneNoti = new BpmProcdefNotification();
                        cloneNoti.setProcDefId(bpmProcdefCopy.getProcDefId());
                        cloneNoti.setActionCode(notification.getActionCode());
                        cloneNoti.setNotificationObject(notification.getNotificationObject());
                        cloneNoti.setNotificationTemplateId(notification.getNotificationTemplateId());
                        cloneNoti.setTaskDefKey(notification.getTaskDefKey());
                        cloneNoti.setTaskDefKeyNotification(notification.getTaskDefKeyNotification());
                        cloneNoti.setFieldNotification(notification.getFieldNotification());
                        cloneNoti.setCreatedTime(LocalDateTime.now());
                        cloneNoti.setUpdatedTime(LocalDateTime.now());
                        cloneNoti.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
                        cloneNoti.setBpmProcdefId(bpmProcdefCopy.getId());
                        cloneNoti.setStatus(notification.getStatus());
                        cloneNoti.setAddMoreConfig(notification.getAddMoreConfig());
                        cloneNoti.setOffNotification(notification.getOffNotification());
                        bpmProcdefNotifications.add(cloneNoti);
                    }
                    bpmProcdefNotificationRepository.saveAll(bpmProcdefNotifications);
                }

                // Lưu view file api
                List<BpmProcdefViewFileApi> bpmProcdefViewFileApis = bpmProcdefViewFileApiRepository.getAllByBpmProcdefIdAndProcDefId(bpmProcdef.getId(), bpmProcdef.getProcDefId());
                if (!ValidationUtils.isNullOrEmpty(bpmProcdefViewFileApis)) {
                    List<BpmProcdefViewFileApi> cloneViewFileApis = new ArrayList<>();
                    for (BpmProcdefViewFileApi bpmProcdefViewFileApi : bpmProcdefViewFileApis) {
                        BpmProcdefViewFileApi cloneFileApi = new BpmProcdefViewFileApi();
                        cloneFileApi.setUrl(bpmProcdefViewFileApi.getUrl());
                        cloneFileApi.setBody(bpmProcdefViewFileApi.getBody());
                        cloneFileApi.setHeader(bpmProcdefViewFileApi.getHeader());
                        cloneFileApi.setBpmProcdefId(bpmProcdefCopy.getId());
                        cloneFileApi.setProcDefId(bpmProcdefCopy.getProcDefId());
                        cloneFileApi.setMethod(bpmProcdefViewFileApi.getMethod());
                        cloneFileApi.setTaskDefKey(bpmProcdefViewFileApi.getTaskDefKey());
                        cloneFileApi.setStatus(bpmProcdefViewFileApi.getStatus());
                        cloneFileApi.setCreatedTime(LocalDateTime.now());
                        cloneFileApi.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                        cloneFileApi.setButtonName(bpmProcdefViewFileApi.getButtonName());
                        cloneFileApi.setResponse(bpmProcdefViewFileApi.getResponse());
                        cloneFileApi.setBaseUrl(bpmProcdefViewFileApi.getBaseUrl());
                        cloneFileApi.setDisplayCondition(bpmProcdefViewFileApi.getDisplayCondition());
                        cloneFileApi.setShowButtonInCreateTicket(bpmProcdefViewFileApi.getShowButtonInCreateTicket());
                        cloneViewFileApis.add(cloneFileApi);
                    }
                    bpmProcdefViewFileApiRepository.saveAll(cloneViewFileApis);
                }

                // Cấu hình trạng thái nhiệm vụ theo bước
                List<BpmProcdefLegislativeStatusConfig> bpmProcdefLegislativeStatusConfigs = bpmProcdefLegislativeConfigRepository.getAllByBpmProcdefIdAndProcDefId(bpmProcdef.getId(), bpmProcdef.getProcDefId());
                if (!ValidationUtils.isNullOrEmpty(bpmProcdefLegislativeStatusConfigs)) {
                    List<BpmProcdefLegislativeStatusConfig> clones = new ArrayList<>();
                    for (BpmProcdefLegislativeStatusConfig data : bpmProcdefLegislativeStatusConfigs) {
                        BpmProcdefLegislativeStatusConfig clone = new BpmProcdefLegislativeStatusConfig();
                        clone.setTaskStatus(data.getTaskStatus());
                        clone.setTaskDefKey(data.getTaskDefKey());
                        clone.setLegislativeStatus(data.getLegislativeStatus());
                        clone.setStatus(Boolean.TRUE);
                        clone.setProcDefId(data.getProcDefId());
                        clones.add(clone);
                    }
                    bpmProcdefLegislativeConfigRepository.saveAll(clones);
                }
            }

            return bpmProcdefCopy;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public List<BpmProcdef> findAll() {
        return bpmProcdefRepository.findAll();
    }

    // Phân quyền theo nhóm
    public Object getAllSystemGroup() {
        List<Map<String, Object>> lstResponse = new ArrayList<>();
        BpmProcdefDto criteria = new BpmProcdefDto();
        criteria.setPage(1);
        criteria.setLimit(999999);
        criteria.setSortBy("id");
        criteria.setSortType("DESC");
        int pageNum = criteria.getPage() - 1;
        Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());

        // Lấy list companyCode được phép tác động
        String username = credentialHelper.getJWTPayload().getUsername();
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
        criteria.setListCompanyCode(lstCompanyCode);

        // Phân quyền theo nhóm
        List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.BPM_PROC_DEF.tableName, username);
        // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
        if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
            criteria.setLstGroupPermissionId(lstGroupPermissionId);
            // get list company code role admin member
            List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
            criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
        }

        Page<BpmProcdef> page = bpmProcdefRepository.findAll(bpmProcdefSpecification.filter(criteria, username), PageRequest.of(pageNum, criteria.getLimit(), sort));
        List<BpmProcdefDto> bpmProcdefDtoList = page.getContent().stream().map(e -> modelMapper.map(e, BpmProcdefDto.class)).collect(Collectors.toList());

        for (BpmProcdefDto bpmProcdefDto : bpmProcdefDtoList) {
            Map<String, Object> response = new HashMap<>();
            response.put("id", bpmProcdefDto.getId());
            response.put("name", bpmProcdefDto.getName());
            response.put("companyCode", bpmProcdefDto.getCompanyCode());
            response.put("companyName", bpmProcdefDto.getCompanyName());

            lstResponse.add(response);
        }

        return lstResponse;
    }
}
