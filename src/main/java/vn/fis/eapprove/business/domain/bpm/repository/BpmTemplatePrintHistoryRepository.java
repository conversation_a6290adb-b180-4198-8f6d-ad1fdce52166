package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrintHistory;


import java.util.List;

@Repository
public interface BpmTemplatePrintHistoryRepository extends JpaRepository<BpmTemplatePrintHistory, Long>, JpaSpecificationExecutor<BpmTemplatePrintHistory> {

    Long countByBpmTemplatePrintId(Long bpmTemplatePrintId);

    List<BpmTemplatePrintHistory> findByBpmTemplatePrintId(Long bpmTemplatePrintId);

    BpmTemplatePrintHistory getBpmTemplatePrintHistoryById(Long id);
}
