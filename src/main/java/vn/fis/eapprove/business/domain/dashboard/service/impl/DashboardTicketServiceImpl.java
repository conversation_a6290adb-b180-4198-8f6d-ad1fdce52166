package vn.fis.eapprove.business.domain.dashboard.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcInstRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTaskRepository;
import vn.fis.eapprove.business.domain.changeAssignee.repository.ChangeAssigneeHistoryRepository;
import vn.fis.eapprove.business.domain.dashboard.entity.DashboardTicket;
import vn.fis.eapprove.business.domain.dashboard.repository.DashboardTaskRepository;
import vn.fis.eapprove.business.domain.dashboard.repository.DashboardTicketRepository;
import vn.fis.eapprove.business.domain.dashboard.service.DashboardTicketService;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;
import vn.fis.eapprove.business.domain.priority.repository.PriorityManagementRepository;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.repository.ServicePackageRepository;
import vn.fis.eapprove.business.dto.AssigneeInfoDto;
import vn.fis.eapprove.business.dto.DashboardTicketDto;
import vn.fis.eapprove.business.exception.report.BusinessException;
import vn.fis.eapprove.business.exception.report.ReportCode;
import vn.fis.eapprove.business.mapper.DashboardTicketMapper;
import vn.fis.eapprove.business.model.response.UserInfoByUsername;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDateTime;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class DashboardTicketServiceImpl implements DashboardTicketService {

    private final DashboardTicketRepository dashboardTicketRepository;
    private final BpmProcInstRepository bpmProcInstRepository;
    private final BpmTaskRepository bpmTaskRepository;
    private final ChangeAssigneeHistoryRepository changeAssigneeHistoryRepository;
    private final CustomerService customerService;
    private final PriorityManagementRepository priorityManagementRepository;
    private final ServicePackageRepository servicePackageRepository;
    private final DashboardTicketMapper mapper;
    private final DashboardTaskRepository dashboardTaskRepository;

    @Override
    public void createDashboardTicket(Long ticketId) {
        try {
            if (!isTicketExist(ticketId)) {
                log.info("Create dashboard ticket = {}", ticketId);
                insert(ticketId);
            } else {
                // update ghi đè lên bản ghi đã tồn tại
                log.info("Update dashboard ticket = {}", ticketId);
                update(ticketId);
            }
            log.info("Create dashboard ticket success = {}", ticketId);
        } catch (Exception e) {
            log.error("Error when create dashboard ticket = {}", ticketId, e);
        }
    }

    private void insert(Long ticketId){
        DashboardTicketDto dashboardTicketDto = mapToDashboardDto(ticketId);
        if (dashboardTicketDto == null) {
            return;
        }
        DashboardTicket dashboardTicket = mapper.to(dashboardTicketDto);
        dashboardTicketRepository.save(dashboardTicket);
    }

    private void update(Long ticketId){
        DashboardTicketDto dashboardTicketDto = mapToDashboardDto(ticketId);
        if (dashboardTicketDto == null) {
            return;
        }
        DashboardTicket existed = dashboardTicketRepository.getDashboardTicketByTicketId(ticketId);
        DashboardTicket update = mapper.merge(existed, dashboardTicketDto);
        dashboardTicketRepository.save(update);
    }

    @Override
    public void deleteTaskReturnExist(Long ticketId) {
        try {
            BpmProcInst bpmProcInst = bpmProcInstRepository.getBpmProcInstByTicketId(ticketId);
            if (bpmProcInst == null) {
                return;
            }
            List<String> status = new ArrayList<>();
            status.add("DELETED_BY_RU");
            status.add("AGREE_TO_RECALL");
            List<BpmTask> bpmTasks = bpmTaskRepository.findBpmTaskReturnByProcInstIdAndStatus(bpmProcInst.getTicketProcInstId(), status);
            if (bpmTasks.isEmpty()) {
                return;
            }
            List<String> taskIds = new ArrayList<>();
            for (BpmTask e : bpmTasks) {
                taskIds.add(e.getTaskId());
            }
            dashboardTaskRepository.deleteByTaskId(taskIds);
        } catch (Exception e) {
            log.error("Error when delete task return exist: ", e);
        }
    }

    private boolean isTicketExist(Long ticketId){
        DashboardTicket existed = dashboardTicketRepository.getDashboardTicketByTicketId(ticketId);
        return existed != null;
    }

    public DashboardTicketDto mapToDashboardDto(Long ticketId) {

        DashboardTicketDto dashboardTicketDto = new DashboardTicketDto();

        BpmProcInst bpmProcInst = bpmProcInstRepository.findById(ticketId).orElseThrow(() -> new BusinessException(ReportCode.TICKET_ID_NOT_FOUND));

        if (bpmProcInst.getTicketStatus().equals("DRAFT")) {
            return null;
        }
        List<BpmTask> bpmTasks = bpmTaskRepository.findBpmTaskByProcInstId(bpmProcInst.getTicketProcInstId());
        Set<LocalDateTime> taskStartTimes = new HashSet<>();
        if (!ValidationUtils.isNullOrEmpty(bpmTasks)) {
            for (BpmTask e : bpmTasks) {
                taskStartTimes.add(e.getTaskCreatedTime());
            }
        }
        LocalDateTime maxTaskStartTime = taskStartTimes.stream()
                .max(LocalDateTime::compareTo)
                .orElse(null);
        Set<String> assignees = new HashSet<>();
        Set<String> taskTypes = new HashSet<>();
        if (!ValidationUtils.isNullOrEmpty(bpmTasks)) {
            for (BpmTask e : bpmTasks) {
                if (Boolean.TRUE.equals(e.getAssignType())) {
                    String orgAssignee = changeAssigneeHistoryRepository.getAssigneeOrgByTaskId(e.getTaskId());
                    assignees.add(orgAssignee);
                    taskTypes.add(e.getTaskType());
                } else {
                    assignees.add(e.getTaskAssignee());
                    taskTypes.add(e.getTaskType());
                }
            }
        }
        AssigneeInfoDto assigneeInfoDto = customerService.getAssigneeInfoByListUser(assignees);
        if (maxTaskStartTime != null) {
            dashboardTicketDto.setActionCurrentTime(maxTaskStartTime);
        } else {
            dashboardTicketDto.setActionCurrentTime(bpmProcInst.getTicketCreatedTime());
        }
        dashboardTicketDto.setAssignee(assignees.toString());
        dashboardTicketDto.setTaskType(taskTypes.toString());
        if (assigneeInfoDto != null) {
            dashboardTicketDto.setAssigneeChartId(assigneeInfoDto.getChartIds().toString());
            dashboardTicketDto.setAssigneeChartNodeId(assigneeInfoDto.getChartNodeIds().toString());
            dashboardTicketDto.setAssigneeStatus(assigneeInfoDto.getStatus().toString());
        }

        Optional<ServicePackage> servicePackage = servicePackageRepository.findById(bpmProcInst.getServiceId());
        if (servicePackage.isPresent()) {
            dashboardTicketDto.setServiceName(servicePackage.get().getServiceName());
            dashboardTicketDto.setSubmissionType(servicePackage.get().getSubmissionType());
            dashboardTicketDto.setMasterParentId(servicePackage.get().getMasterParentId());
        } else {
            return null;
        }
        if (bpmProcInst.getChartNodeId() != null) {
            dashboardTicketDto.setChartNodeId(bpmProcInst.getChartNodeId());
        }
        dashboardTicketDto.setServiceId(bpmProcInst.getServiceId());
        dashboardTicketDto.setTicketId(bpmProcInst.getTicketId());
        dashboardTicketDto.setProcDefId(bpmProcInst.getTicketProcDefId());
        dashboardTicketDto.setTitle(bpmProcInst.getTicketTitle());
        dashboardTicketDto.setProcInstStatus(bpmProcInst.getTicketStatus());
        dashboardTicketDto.setProcInstId(bpmProcInst.getTicketProcInstId());
        dashboardTicketDto.setRequestCode(bpmProcInst.getRequestCode());
        dashboardTicketDto.setSlaFinish(bpmProcInst.getSlaFinish());
        dashboardTicketDto.setStartedTime(bpmProcInst.getTicketStartedTime());
        dashboardTicketDto.setFinishedTime(bpmProcInst.getTicketFinishTime());
        dashboardTicketDto.setCreatedUser(bpmProcInst.getCreatedUser().toLowerCase());
        dashboardTicketDto.setLocationId(bpmProcInst.getLocationId());
        dashboardTicketDto.setCreatedTime(bpmProcInst.getTicketCreatedTime());
        dashboardTicketDto.setChartNodeName(bpmProcInst.getChartNodeName());
        dashboardTicketDto.setChartNodeCode(bpmProcInst.getChartNodeCode());
        dashboardTicketDto.setChartId(bpmProcInst.getChartId());
        dashboardTicketDto.setPriorityId(bpmProcInst.getPriorityId());

        if (bpmProcInst.getPriorityId() != null) {
            Optional<PriorityManagement> priorityManagement = priorityManagementRepository.findById(bpmProcInst.getPriorityId());
            if (priorityManagement.isPresent()) {
                dashboardTicketDto.setPriorityName(priorityManagement.get().getName());
            } else {
                dashboardTicketDto.setPriorityName("DEFAULT");
            }
        }
        UserInfoByUsername createUser = customerService.getUserInfoByUsername(bpmProcInst.getCreatedUser().toLowerCase());
        if (createUser != null) {
            dashboardTicketDto.setCreatedUserFullName(createUser.getFullName());
            dashboardTicketDto.setChartShortName(createUser.getChartShortName());
            dashboardTicketDto.setUserTitleName(createUser.getTitleName());
            dashboardTicketDto.setStaffCode(createUser.getStaffCode());
            dashboardTicketDto.setDirectManager(createUser.getDirectManager());
            dashboardTicketDto.setUserStatus(createUser.getStatus());
            dashboardTicketDto.setManagerLevel(createUser.getManagerLevel());
            dashboardTicketDto.setEmail(createUser.getEmail());
        }
//        long timeRes = bpmProcInst.getSlaFinish().longValue() * 60;
//        LocalDateTime slaFinishTime = customerService.getTimeSla(timeRes, bpmProcInst.getTicketCreatedTime(), bpmProcInst.getCreatedUser());
        // btp sla
        long timeRes = bpmProcInst.getSlaFinish().longValue() + 1;
        LocalDateTime slaFinishTime = bpmProcInst.getTicketCreatedTime().plusDays(timeRes).toLocalDate().atStartOfDay();
        if (!ValidationUtils.isNullOrEmpty(slaFinishTime)) {
            dashboardTicketDto.setSlaFinishTime(slaFinishTime);
        }
        return dashboardTicketDto;
    }
}
