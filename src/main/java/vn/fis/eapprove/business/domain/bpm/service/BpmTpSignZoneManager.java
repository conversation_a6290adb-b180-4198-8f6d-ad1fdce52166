package vn.fis.eapprove.business.domain.bpm.service;

import jakarta.persistence.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.bpm.entity.*;
import vn.fis.eapprove.business.domain.bpm.repository.*;
import vn.fis.eapprove.business.domain.changeAssignee.entity.ChangeAssigneeHistory;
import vn.fis.eapprove.business.domain.changeAssignee.repository.ChangeAssigneeHistoryRepository;
import vn.fis.eapprove.business.domain.changeAssignee.service.ChangeAssigneeHistoryService;
import vn.fis.eapprove.business.domain.fileCondition.service.FileService;
import vn.fis.eapprove.business.dto.BpmTpSignZoneDto;
import vn.fis.eapprove.business.dto.SignedFileDto;
import vn.fis.eapprove.business.model.AccountModel;
import vn.fis.eapprove.business.model.BpmTpSignZoneResponse;
import vn.fis.eapprove.business.model.request.*;
import vn.fis.eapprove.business.model.response.BpmPrintSignZoneResponse;
import vn.fis.eapprove.business.model.response.BpmSignResponse;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.tenant.manager.GotenbergManager;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.business.utils.FileUtils;
import vn.fis.eapprove.business.utils.PDFUtils;
import vn.fis.eapprove.business.utils.html.TemplateHtml;
import vn.fis.eapprove.business.utils.template.HtmlToPdfConverter;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.ProcInstConstants;
import vn.fis.spro.common.constants.TaskConstants;
import vn.fis.spro.common.helper.RestHelper;
import vn.fis.spro.common.model.response.UserInfoResponse;
import vn.fis.spro.common.util.DateTimeUtils;
import vn.fis.spro.common.util.QueryUtils;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.manager.FileManager;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import static vn.fis.eapprove.business.constant.Constant.FAILED;
import static vn.fis.eapprove.business.constant.Constant.SUCCESS;

@Service("BpmTpSignZoneManagerV1")
@Slf4j
@Transactional
public class BpmTpSignZoneManager {

    @Autowired
    private BpmTpSignZoneRepository bpmTpSignZoneRepository;

    @Autowired
    private BpmTemplatePrintRepository bpmTemplatePrintRepository;

    @Autowired
    private BpmTpTaskRepository bpmTpTaskRepository;

    @Autowired
    private BpmTaskRepository bpmTaskRepository;

    @Autowired
    private ChangeAssigneeHistoryRepository changeAssigneeHistoryRepository;

    @Autowired
    private BpmTpSignZoneHistoryManager bpmTpSignZoneHistoryManager;

    @Autowired
    private BpmTempTicketRepository bpmTempTicketRepository;

    @Autowired
    private CredentialHelper credentialHelper;

    @Autowired
    private FileManager fileManager;

    @Autowired
    private SproProperties sproProperties;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private FileService fileService;

    @Autowired
    private Common common;

    @Autowired
    private ChangeAssigneeHistoryService changeAssigneeHistoryService;

    @Autowired
    @Lazy
    private BpmTemplatePrintManager bpmTemplatePrintManager;

    @Autowired
    @Lazy
    private BpmProcInstManager bpmProcInstManager;

    @Autowired
    private BpmProcInstRepository bpmProcInstRepository;

    @Autowired
    private BpmTemplatePrintConfigUserRepository bpmTemplatePrintConfigUserRepository;

    @Value("${app.s3.bucket}")
    private String bucket;

    @Autowired
    private RestHelper restHelper;

    @Autowired
    private BpmFileConditionRepository bpmFileConditionRepository;
    @Autowired
    private TemplateHtml templateHtml;
    @Autowired
    private GotenbergManager gotenbergManager;

    public int save(BpmPrintSignZoneRequest req) {
        try {
            String createdUser = credentialHelper.getJWTPayload().getUsername();
            BpmTemplatePrint bpmPrintPhase = bpmTemplatePrintRepository.getById(req.getBpmPrintPhaseId());
            if (bpmPrintPhase == null) {
                return FAILED;
            }

            if (req.getPdfContent() != null) {
                bpmPrintPhase.setPdfContent(uploadBase64ToMinIO(bpmPrintPhase.getPdfContent(), null, true));
                bpmTemplatePrintRepository.save(bpmPrintPhase);
            }

            List<BpmTpSignZone> bpmSignZoneRequests = new ArrayList<>();
            for (BpmSignZoneRequest bpmSignZoneRequest : req.getZones()) {
                BpmTpSignZone bpmTpSignZone = new BpmTpSignZone();
                bpmTpSignZone.setBpmTemplatePrintId(bpmPrintPhase.getId());
                bpmTpSignZone.setOrderSign(bpmSignZoneRequest.getOrderSign());
                bpmTpSignZone.setEmail(bpmSignZoneRequest.getEmail());
                bpmTpSignZone.setX(bpmSignZoneRequest.getX());
                bpmTpSignZone.setY(bpmSignZoneRequest.getY());
                bpmTpSignZone.setH(bpmSignZoneRequest.getH());
                bpmTpSignZone.setPage(bpmSignZoneRequest.getPage());
                bpmTpSignZone.setW(bpmSignZoneRequest.getW());
                bpmTpSignZone.setProcInstId(req.getProcInstId());
                bpmTpSignZone.setFirstName(bpmSignZoneRequest.getFirstName());
                bpmTpSignZone.setLastName(bpmSignZoneRequest.getLastName());
                bpmTpSignZone.setPosition(bpmSignZoneRequest.getPosition());
                bpmTpSignZone.setTaskDefKey(bpmSignZoneRequest.getTaskDefKey());
                bpmTpSignZone.setCreatedUser(createdUser);
                bpmTpSignZone.setScale(bpmSignZoneRequest.getScale());
                bpmTpSignZone.setCreatedDate(new Date());
                bpmSignZoneRequests.add(bpmTpSignZone);
            }
            bpmTpSignZoneRepository.saveAll(bpmSignZoneRequests);

            return SUCCESS;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return FAILED;
        }
    }

    public boolean checkOrder(Long bpmPrintPhaseId, Long order) {
        try {
            boolean has = false;
            List<BpmTpSignZone> bpmPrintSignZones = bpmTpSignZoneRepository.findBpmTpSignZoneByBpmTemplatePrintId(bpmPrintPhaseId);
            for (BpmTpSignZone bpmTpSignZone : bpmPrintSignZones) {
                if (Objects.equals(order, bpmTpSignZone.getOrderSign())) {
                    has = true;
                    break;
                }
            }
            return has;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public List<BpmTpSignZone> getAllSignZones(Long templatePrintId, String procInstId) {
        return bpmTpSignZoneRepository.findBpmTpSignZoneByBpmTemplatePrintIdAndProcInstId(templatePrintId, procInstId);
    }

    public List<BpmPrintSignZoneResponse> getPrintSignZoneListByBpmPrintPhaseAndProcInst(BpmTemplatePrint bpmPrintPhase, String procInstId) {
        try {
            if (bpmPrintPhase == null) {
                return null;
            }

            List<BpmTpSignZone> signZones = getAllSignZones(bpmPrintPhase.getId(), procInstId);

            // combine signature
//            combineSignToPdf(bpmPrintPhase, signZones);

            return signZones.stream().map(e -> {
                BpmPrintSignZoneResponse bpmPrintSignZoneResponse = new BpmPrintSignZoneResponse();

                bpmPrintSignZoneResponse.setId(e.getId());
                bpmPrintSignZoneResponse.setOrderSign(e.getOrderSign());
                bpmPrintSignZoneResponse.setCreatedDate(e.getCreatedDate());
                bpmPrintSignZoneResponse.setCreatedUser(e.getCreatedUser());
                bpmPrintSignZoneResponse.setH(e.getH());
                bpmPrintSignZoneResponse.setW(e.getW());
                bpmPrintSignZoneResponse.setX(e.getX());
                bpmPrintSignZoneResponse.setY(e.getY());
                bpmPrintSignZoneResponse.setPage(e.getPage());
                bpmPrintSignZoneResponse.setProcInstId(e.getProcInstId());
                bpmPrintSignZoneResponse.setTaskDefKey(e.getTaskDefKey());
                bpmPrintSignZoneResponse.setUpdatedDate(e.getUpdatedDate());
                bpmPrintSignZoneResponse.setUpdatedUser(e.getUpdatedUser());
                bpmPrintSignZoneResponse.setEmail(e.getEmail());
                bpmPrintSignZoneResponse.setFirstName(e.getFirstName());
                bpmPrintSignZoneResponse.setLastName(e.getLastName());
                bpmPrintSignZoneResponse.setPosition(e.getPosition());
                bpmPrintSignZoneResponse.setScale(e.getScale());
                bpmPrintSignZoneResponse.setBpmSignResponse(new BpmSignResponse(e.getSign()));
                bpmPrintSignZoneResponse.setComment(e.getComment());
                bpmPrintSignZoneResponse.setSignedDate(e.getSignedDate());
                bpmPrintSignZoneResponse.setSignedFile(e.getSignedFile());
                bpmPrintSignZoneResponse.setSignType(e.getSignType());
                bpmPrintSignZoneResponse.setChartNodeLevel(e.getChartNodeLevel());

                return bpmPrintSignZoneResponse;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public List<BpmPrintSignZoneResponse> combineSignedFile(BpmTemplatePrint bpmPrintPhase, String procInstId) {
        try {
            if (bpmPrintPhase == null) {
                return null;
            }

            List<BpmTpSignZone> signZones = getAllSignZones(bpmPrintPhase.getId(), procInstId);

            // combine signature
            combineSignToPdf(bpmPrintPhase, signZones);

            return signZones.stream().map(e -> {
                BpmPrintSignZoneResponse bpmPrintSignZoneResponse = new BpmPrintSignZoneResponse();

                bpmPrintSignZoneResponse.setId(e.getId());
                bpmPrintSignZoneResponse.setOrderSign(e.getOrderSign());
                bpmPrintSignZoneResponse.setCreatedDate(e.getCreatedDate());
                bpmPrintSignZoneResponse.setCreatedUser(e.getCreatedUser());
                bpmPrintSignZoneResponse.setH(e.getH());
                bpmPrintSignZoneResponse.setW(e.getW());
                bpmPrintSignZoneResponse.setX(e.getX());
                bpmPrintSignZoneResponse.setY(e.getY());
                bpmPrintSignZoneResponse.setPage(e.getPage());
                bpmPrintSignZoneResponse.setProcInstId(e.getProcInstId());
                bpmPrintSignZoneResponse.setTaskDefKey(e.getTaskDefKey());
                bpmPrintSignZoneResponse.setUpdatedDate(e.getUpdatedDate());
                bpmPrintSignZoneResponse.setUpdatedUser(e.getUpdatedUser());
                bpmPrintSignZoneResponse.setEmail(e.getEmail());
                bpmPrintSignZoneResponse.setFirstName(e.getFirstName());
                bpmPrintSignZoneResponse.setLastName(e.getLastName());
                bpmPrintSignZoneResponse.setPosition(e.getPosition());
                bpmPrintSignZoneResponse.setScale(e.getScale());
                bpmPrintSignZoneResponse.setBpmSignResponse(new BpmSignResponse(e.getSign()));
                bpmPrintSignZoneResponse.setComment(e.getComment());
                bpmPrintSignZoneResponse.setSignedDate(e.getSignedDate());
                bpmPrintSignZoneResponse.setSignedFile(e.getSignedFile());
                bpmPrintSignZoneResponse.setSignType(e.getSignType());
                bpmPrintSignZoneResponse.setChartNodeLevel(e.getChartNodeLevel());

                return bpmPrintSignZoneResponse;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * Combine list of sign-zones to template document
     *
     * <AUTHOR>
     */
    private void combineSignToPdf(BpmTemplatePrint bpmPrintPhase, List<BpmTpSignZone> signZones) throws IOException {
        if (bpmPrintPhase == null || ValidationUtils.isNullOrEmpty(signZones)) {
            return;
        }
        long startTime = System.currentTimeMillis();

        PDDocument doc = null;
        try {
            byte[] bytesContent = null;
            BpmTpSignZone bpmTpSignZone = signZones.stream().filter(itemSign -> !ValidationUtils.isNullOrEmpty(itemSign.getSignedFile())).findAny().orElse(null);

            if (!ValidationUtils.isNullOrEmpty(bpmPrintPhase.getContent())) { // ký html
                BpmProcInst bpmProcInst = bpmProcInstManager.findBpmProcInstByTicketProcInstId(bpmTpSignZone.getProcInstId());
                BpmTemplatePrintRequest bpmTemplatePrintRequest = new BpmTemplatePrintRequest();
                bpmTemplatePrintRequest.setHtmlContent(bpmPrintPhase.getContent());
                bpmTemplatePrintRequest.setHeaderContent(bpmPrintPhase.getHeaderContent());
                bpmTemplatePrintRequest.setFooterContent(bpmPrintPhase.getFooterContent());
                bpmTemplatePrintRequest.setPrintType("pdf");
                bytesContent = bpmProcInstManager.getHtmlFileTemplate(bpmProcInst.getTicketId(), bpmTemplatePrintRequest, signZones);

            } else if (bpmPrintPhase.getUploadWordsChange() != null && !bpmPrintPhase.getUploadWordsChange().trim().isEmpty()) {//Sinh file từ file khác
                if (bpmPrintPhase.getUploadWordsChange().toLowerCase().endsWith("docx")) {//Mẫu trình kí docx
                    BpmTemplatePrintRequest bpmTemplatePrintRequest = new BpmTemplatePrintRequest();
                    bpmTemplatePrintRequest.setUploadWordsChange(bpmPrintPhase.getUploadWordsChange());
                    bpmTemplatePrintRequest.setPrintType("pdf");
                    if (!signZones.isEmpty()) {
                        if (bpmTpSignZone != null) {
                            BpmProcInst bpmProcInst = bpmProcInstManager.findBpmProcInstByTicketProcInstId(bpmTpSignZone.getProcInstId());
                            if (bpmProcInst != null) {
                                bytesContent = bpmProcInstManager.getPrintFileTemplate(bpmProcInst.getTicketId(), bpmTemplatePrintRequest, signZones);

                            }
                        } else {
                            log.info("############# Không tìm thấy file kí ##############");
                        }
                    }
                } else if (bpmPrintPhase.getUploadWordsChange().toLowerCase().endsWith("pdf")) {//Kí tự động pdf
                    HtmlToPdfConverter htmlToPdfConverter = new HtmlToPdfConverter();
                    // check theo bpm_template_print_config_user
                    List<BpmTemplatePrintConfigUser> lstConfigUser = new ArrayList<>();
                    if (!ValidationUtils.isNullOrEmpty(signZones)) {
                        lstConfigUser = bpmTemplatePrintConfigUserRepository.getBpmTemplatePrintConfigUsersByUsernameIn(
                                signZones.stream().map(BpmTpSignZone::getEmail).filter(Objects::nonNull).collect(Collectors.toList())
                        );
                    }
                    bytesContent = htmlToPdfConverter.autoBindSignature(bpmPrintPhase, signZones, fileManager, bucket, lstConfigUser);
                }
            }
            InputStream inputStream;

            if (bpmTpSignZone != null) {
                // add requestCode to each page of pdf
                BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByticketProcInstId(signZones.get(0).getProcInstId());
                if (bytesContent != null) {
                    if (!ValidationUtils.isNullOrEmpty(bpmProcInst)) {
                        String requestCode = bpmProcInst.getRequestCode();
                        doc = PDDocument.load(new ByteArrayInputStream(bytesContent));
                        PDFont fontTimes = PDFUtils.getFontTimes(doc);
                        float fontTimesSize = 10f;
                        int pages = doc.getNumberOfPages();
                        for (int i = 0; i < pages; i++) {
                            PDPage page = doc.getPage(i);
                            PDRectangle mediaBox = page.getMediaBox();
                            float pageHeight = mediaBox.getUpperRightY();
                            float pageWidth = mediaBox.getUpperRightX();
                            PDPageContentStream contentStream = new PDPageContentStream(doc, page, PDPageContentStream.AppendMode.APPEND, false, true);
                            // save normal state
                            contentStream.saveGraphicsState();
                            /* BEGIN: write text */
                            contentStream.setFont(fontTimes, fontTimesSize);
                            // add requestCode
                            if (!ValidationUtils.isNullOrEmpty(requestCode)) {
                                contentStream.beginText();
                                contentStream.newLineAtOffset(pageWidth * 0.8f, pageHeight * 0.02f);
                                contentStream.showText(requestCode);
                                contentStream.endText();
                            }
                            contentStream.close();
                        }
                        ByteArrayOutputStream bos = new ByteArrayOutputStream();
                        doc.save(bos);
                        inputStream = new ByteArrayInputStream(bos.toByteArray());
                        bos.close();
                    } else {
                        inputStream = new ByteArrayInputStream(bytesContent);
                    }
                } else {
                    inputStream = new ByteArrayInputStream(new byte[0]);
                }

                inputStream.close();
                // save file s3
                fileManager.putFile(bucket, bpmTpSignZone.getSignedFile(), inputStream.available(), inputStream);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (doc != null) {
                doc.close();
            }
            log.info("combineSignToPdf: {} in {} ms", signZones.get(0).getProcInstId() , System.currentTimeMillis() - startTime);
        }
    }

    /**
     * <AUTHOR>
     */
    private String getMessage(String prop) {
        return common.getMessage(prop);
    }

    private String getTemplateName(BpmTemplatePrint bpmTemplatePrint) {
        if (bpmTemplatePrint != null) {
            String templateName = (!ValidationUtils.isNullOrEmpty(bpmTemplatePrint.getName()) ? bpmTemplatePrint.getName() : (bpmTemplatePrint.getId() != null ? String.valueOf(bpmTemplatePrint.getId()) : ""));
            if (templateName.contains(".")) {
                return templateName.substring(0, templateName.lastIndexOf(".")).replaceAll("\\W", "_");
            }
        }

        return "";
    }

    public int assignSign(BpmCreateSignRequest bpmCreateSignRequest, String userName) {
        try {
            BpmTemplatePrint bpmTemplatePrint = bpmTemplatePrintManager.findById(bpmCreateSignRequest.getTemplatePrintId());
            String procInstId = bpmCreateSignRequest.getProcInstId();
            Long ticketId = bpmCreateSignRequest.getTicketId();
            String taskId = bpmCreateSignRequest.getTaskId();

            BpmTask bpmTask = bpmTaskRepository.getBpmTaskByTaskId(taskId);
            BpmProcInst bpmProcInst = bpmProcInstManager.findById(ticketId);

            // check task allow process
            List<String> checkStatus = Arrays.asList(ProcInstConstants.Status.RECALLING.code, ProcInstConstants.Status.CANCEL.code);
            if (bpmTask.getTaskStatus().equalsIgnoreCase(TaskConstants.Status.DELETED_BY_RU.code) || checkStatus.contains(bpmProcInst.getTicketStatus())) {
                throw new RuntimeException(common.getMessage("message.task.completed.not-allow"));
            }

            String templateName = getTemplateName(bpmTemplatePrint);
            ChangeAssigneeHistory changeAssigneeHistory = null;
            List<ChangeAssigneeHistory> changeAssigneeHistories = changeAssigneeHistoryService.getLatestChangeByToAssignee(bpmCreateSignRequest.getTicketId(), bpmCreateSignRequest.getTaskId());
            if (!ValidationUtils.isNullOrEmpty(changeAssigneeHistories)) {
                changeAssigneeHistory = changeAssigneeHistories.get(0);
            }
            Date now = new Date();
            List<BpmTpSignZone> bpmTpSignZones = new ArrayList<>();
            UserInfoResponse userInfoResponse = customerService.getUserInfo(userName);
            BpmTpSignZone startSignZone = bpmTpSignZoneRepository.getSignZoneByTaskDefKeyAndUser(bpmProcInst.getTicketProcInstId(), bpmProcInst.getTicketStartActId(), bpmProcInst.getTicketStartUserId());
            String signedFileName = !ValidationUtils.isNullOrEmpty(startSignZone.getSignedFile())
                    ? startSignZone.getSignedFile()
                    : getSignedFileName(bpmProcInst.getTicketProcInstId(), templateName, now);

            for (BpmSignRequest bpmSignRequest : bpmCreateSignRequest.getBpmSignRequest()) {
                BpmTpSignZone bpmTpSignZone = bpmTpSignZoneRepository.getBpmTpSignZoneById(bpmSignRequest.getTpSignZoneId());
                if (bpmTpSignZone != null) {
                    if (ValidationUtils.isNullOrEmpty(procInstId)) {
                        procInstId = bpmTpSignZone.getProcInstId();
                    }

                    if (bpmSignRequest.getSign() != null) {
                        if (bpmSignRequest.getIsSign()) {
                            bpmTpSignZone.setSign(bpmSignRequest.getSign());
                            //Check user login hiện tại với mặc định ban đầu ký
                            if (!userName.equalsIgnoreCase(bpmTpSignZone.getEmail())) {

                                bpmTpSignZone.setEmail(userName);
                                bpmTpSignZone.setFirstName(userInfoResponse.getFirstname());
                                bpmTpSignZone.setLastName(userInfoResponse.getLastname());
                            }
                            if (!ValidationUtils.isNullOrEmpty(changeAssigneeHistory)) {
                                // được uỷ quyền ký
                                if (userName.equalsIgnoreCase(bpmTpSignZone.getEmail()) && !userName.equalsIgnoreCase(changeAssigneeHistory.getOrgAssignee())) {
                                    bpmTpSignZone.setOrgAssigneeTitle(changeAssigneeHistory.getOrgAssigneeTitle());
                                }
                            }
                            bpmTpSignZone.setEmail(userName);
                            bpmTpSignZone.setComment(bpmSignRequest.getComment());
                            bpmTpSignZone.setSignedDate(DateTimeUtils.localDateTimeToDate(bpmTask.getTaskFinishedTime()));
                            bpmTpSignZone.setPosition(bpmSignRequest.getTitle());
                            bpmTpSignZone.setSignedFile(signedFileName);
                            // add chartNodeLevel check signAction
                            if (!ValidationUtils.isNullOrEmpty(bpmSignRequest.getChartNodeLevel())) {
                                bpmTpSignZone.setChartNodeLevel(bpmSignRequest.getChartNodeLevel());
                            }
                            bpmTpSignZones.add(bpmTpSignZone);
                        }
                    }
                }
            }
            bpmTpSignZoneRepository.saveAll(bpmTpSignZones);

            // BEGIN: generate sign file
            if (bpmTemplatePrint != null) {
                List<BpmTpSignZone> signZones = getAllSignZones(bpmTemplatePrint.getId(), procInstId);
                combineSignToPdf(bpmTemplatePrint, signZones);
            }
            // END: generate sign file

            return SUCCESS;
        } catch (Exception e) {
            log.error("assign sign fail: {}", e.getMessage());
            return FAILED;
        }
    }

    /**
     * Create signed file name
     *
     * <AUTHOR>
     */
    public String getSignedFileName(String procInstId, String templateName, Date date) {
        String folder = FileUtils.getUploadFolder(CommonConstants.FileFolder.SIGNED_FILES, date, true);
        return folder
                + CommonConstants.PATH_SEPARATOR
                + templateName
                + "_"
                + procInstId
                + "."
                + CommonConstants.FileExtension.PDF;
    }

    public int saveTypeTemplatePrint(BpmPrintSignZoneRequest bpmPrintSignZoneRequest) {
        try {
            if (bpmPrintSignZoneRequest.getPdfContent() == null) {
                log.info("================================Không có file kí");
                return FAILED;
            }

            // delete temp ticket when create ticket success
            if (bpmPrintSignZoneRequest.getTicketTempId() != null) {
                bpmTempTicketRepository.deleteById(bpmPrintSignZoneRequest.getTicketTempId());
            }

            BpmTemplatePrint bpmTemplatePrint = new BpmTemplatePrint();
            bpmTemplatePrint.setName(bpmPrintSignZoneRequest.getName());
            bpmTemplatePrint.setCreatedDate(new Date());
            bpmTemplatePrint.setProcDefId(bpmPrintSignZoneRequest.getProcDefId());
            bpmTemplatePrint.setCreatedUser(bpmPrintSignZoneRequest.getUsername() != null ? bpmPrintSignZoneRequest.getUsername() : credentialHelper.getJWTPayload().getUsername());
            bpmTemplatePrint.setPrintType(1);

            bpmTemplatePrint.setPdfContent(uploadBase64ToMinIO(bpmPrintSignZoneRequest.getPdfContent(), null, true));
            String uploadWordsChange = bpmPrintSignZoneRequest.getUploadWordsChange();//Trường này chi mẫu trình kí mới set vào và sử dung khi muốn thay thế file template mặc định

            // ký html
            if (!ValidationUtils.isNullOrEmpty(bpmPrintSignZoneRequest.getFileConditionId())) {
                Tuple tuple = bpmFileConditionRepository.getTemplateHtmlChangeById(bpmPrintSignZoneRequest.getFileConditionId());
                String templateHtml = tuple.get("templateHtml").toString();
                String templateHeader = tuple.get("templateHeader").toString();
                String templateFooter = tuple.get("templateFooter").toString();
                bpmTemplatePrint.setContent(templateHtml);
                bpmTemplatePrint.setHeaderContent(templateHeader);
                bpmTemplatePrint.setFooterContent(templateFooter);
            } else {
                if (uploadWordsChange != null && uploadWordsChange.startsWith("http")) {//File từ hệ thống ngoài thì get file lưu lại về hệ thống mình
                    InputStream in = new ByteArrayInputStream(new byte[0]);
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.MULTIPART_FORM_DATA);

                    ResponseEntity<Resource> responseEntity = restHelper.exchange(uploadWordsChange,
                            HttpMethod.GET,
                            headers,
                            null,
                            new ParameterizedTypeReference<>() {
                            });

                    if (responseEntity.getBody() != null) {
                        in = responseEntity.getBody().getInputStream();
                    }

                    String folder = FileUtils.getUploadFolder(CommonConstants.FileFolder.SIGNED_FILES, new Date(), true);
                    uploadWordsChange = folder + CommonConstants.PATH_SEPARATOR
                            + CommonConstants.FileFolder.TEMPLATE_FILES
                            + "_"
                            + bpmPrintSignZoneRequest.getProcInstId()
                            + "."
                            + (uploadWordsChange.endsWith("docx") ? CommonConstants.FileExtension.DOCX : CommonConstants.FileExtension.PDF);
                    fileManager.putFile(bucket, uploadWordsChange, in.available(), in);

                    in.close();
                }

                // combine logo mtk
                if (uploadWordsChange != null && uploadWordsChange.toLowerCase().endsWith("docx")) {
                    byte[] logoDocxFile = bpmProcInstManager.getFileCombineLogo(bpmPrintSignZoneRequest.getProcInstId(), uploadWordsChange);
                    if (logoDocxFile != null) {
                        InputStream in = new ByteArrayInputStream(logoDocxFile);
                        String folder = FileUtils.getUploadFolder(CommonConstants.FileFolder.SIGNED_FILES, new Date(), true);
                        uploadWordsChange = folder + CommonConstants.PATH_SEPARATOR
                                + CommonConstants.FileFolder.TEMPLATE_FILES
                                + "_"
                                + bpmPrintSignZoneRequest.getProcInstId()
                                + "."
                                + CommonConstants.FileExtension.DOCX;
                        fileManager.putFile(bucket, uploadWordsChange, in.available(), in);
                        in.close();
                    }
                }
            }

            bpmTemplatePrint.setUploadWordsChange(uploadWordsChange);
            BpmTemplatePrint newBpmPrintPhase = bpmTemplatePrintRepository.save(bpmTemplatePrint);

            List<BpmTpTask> bpmPrintPhaseTasks = new ArrayList<>();
            for (BpmTaskPrintRequest taskKey : bpmPrintSignZoneRequest.getTaskKey()) {
                BpmTpTask bpmTpTask = new BpmTpTask();
                bpmTpTask.setName(taskKey.getTaskName());
                bpmTpTask.setProcDefId(bpmPrintSignZoneRequest.getProcDefId());
                bpmTpTask.setTaskDefKey(taskKey.getTaskKey());
                bpmTpTask.setTaskDefKey(taskKey.getTaskKey());
                bpmTpTask.setBpmTemplatePrintId(newBpmPrintPhase.getId());
                bpmTpTask.setTaskType(taskKey.getTaskType());
                bpmPrintPhaseTasks.add(bpmTpTask);
            }
            bpmTpTaskRepository.saveAll(bpmPrintPhaseTasks);

            Date now = new Date();
            String templateName = getTemplateName(newBpmPrintPhase);
            List<BpmTpSignZone> bpmTpSignZones = new ArrayList<>();
            for (BpmSignZoneRequest bpmSignZoneRequest : bpmPrintSignZoneRequest.getZones()) {
                BpmTpSignZone bpmTpSignZone = new BpmTpSignZone();

                // Update sign zone case ủy quyền tự động
                ChangeAssigneeHistory changeAssigneeHistory;
                String assignedEmail = null;
                String assignedFirstName = null;
                String assignedLastName = null;
                BpmTask bpmTask = bpmTaskRepository.findBpmTaskByTaskProcInstIdAndTaskDefKeyAndAssignType(bpmPrintSignZoneRequest.getProcInstId(), bpmSignZoneRequest.getTaskDefKey(), true);
                if (!ValidationUtils.isNullOrEmpty(bpmTask)) {
                    changeAssigneeHistory = changeAssigneeHistoryRepository.findChangeAssigneeHistoryByProcInstIdAndTaskIdAndType(bpmTask.getTaskProcInstId(), bpmTask.getTaskId(), 1);
                    if (!ValidationUtils.isNullOrEmpty(changeAssigneeHistory) && changeAssigneeHistory.getOrgAssignee().equalsIgnoreCase(bpmSignZoneRequest.getEmail())) {
                        assignedEmail = changeAssigneeHistory.getToAssignee();
                        UserInfoResponse userInfo = customerService.getUserInfo(assignedEmail);
                        if (userInfo != null) {
                            assignedFirstName = userInfo.getFirstname();
                            assignedLastName = userInfo.getLastname();
                        }

                        bpmTpSignZone.setOrgAssigneeTitle(changeAssigneeHistory.getOrgAssigneeTitle());
                    }
                }

                bpmTpSignZone.setBpmTemplatePrintId(newBpmPrintPhase.getId());
                bpmTpSignZone.setOrderSign(bpmSignZoneRequest.getOrderSign());
                bpmTpSignZone.setEmail(assignedEmail != null ? assignedEmail : bpmSignZoneRequest.getEmail());
                bpmTpSignZone.setX(bpmSignZoneRequest.getX());
                bpmTpSignZone.setY(bpmSignZoneRequest.getY());
                bpmTpSignZone.setH(bpmSignZoneRequest.getH());
                bpmTpSignZone.setPage(bpmSignZoneRequest.getPage());
                bpmTpSignZone.setW(bpmSignZoneRequest.getW());
                bpmTpSignZone.setProcInstId(bpmPrintSignZoneRequest.getProcInstId());
                bpmTpSignZone.setTaskDefKey(bpmSignZoneRequest.getTaskDefKey());
                bpmTpSignZone.setCreatedUser(bpmPrintSignZoneRequest.getUsername() != null ? bpmPrintSignZoneRequest.getUsername() : credentialHelper.getJWTPayload().getUsername());
                bpmTpSignZone.setFirstName(assignedFirstName != null ? assignedFirstName : bpmSignZoneRequest.getFirstName());
                bpmTpSignZone.setLastName(assignedLastName != null ? assignedLastName : bpmSignZoneRequest.getLastName());
                bpmTpSignZone.setPosition(bpmSignZoneRequest.getPosition());
                bpmTpSignZone.setCreatedDate(now);
                bpmTpSignZone.setScale(bpmSignZoneRequest.getScale());
                bpmTpSignZone.setSignType(bpmSignZoneRequest.getSignType());
                //Trường hợp bước start cũng muốn kí luôn
                if (bpmSignZoneRequest.getSign() != null && !bpmSignZoneRequest.getSign().isEmpty()) {
                    bpmTpSignZone.setSign(bpmSignZoneRequest.getSign());
                    bpmTpSignZone.setSignedDate(now);
                    bpmTpSignZone.setSignedFile(getSignedFileName(bpmTpSignZone.getProcInstId(), templateName, now));
                }
                // add chartNodeLevel check signAction
                if (!ValidationUtils.isNullOrEmpty(bpmSignZoneRequest.getChartNodeLevel())) {
                    bpmTpSignZone.setChartNodeLevel(bpmSignZoneRequest.getChartNodeLevel());
                }

                bpmTpSignZones.add(bpmTpSignZone);
            }
            log.info("================================Lưu signezones");
            bpmTpSignZoneRepository.saveAll(bpmTpSignZones);

            log.info("================================Bắt đầu kí này");
            combineSignToPdf(newBpmPrintPhase, bpmTpSignZones);
            log.info("================================Kết thúc kí này");

            //Update status print
            if (!ValidationUtils.isNullOrEmpty(bpmPrintSignZoneRequest.getBpmPrintPhaseId())) {
                BpmTemplatePrint oldTemplate = bpmTemplatePrintRepository.findById(bpmPrintSignZoneRequest.getBpmPrintPhaseId()).orElse(null);
                if (oldTemplate != null) {
                    oldTemplate.setStatus(true);
                    bpmTemplatePrintRepository.save(oldTemplate);
                }
            }

            return SUCCESS;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return FAILED;
        }
    }

    public int update(BpmPrintSignZoneRequest req) {
        try {
            List<BpmTpSignZone> bpmTpSignZones = new ArrayList<>();
            for (BpmSignZoneRequest bpmSignZoneRequest : req.getZones()) {
                BpmTpSignZone bpmTpSignZone = bpmTpSignZoneRepository.getBpmTpSignZoneById(bpmSignZoneRequest.getId());
                if (bpmTpSignZone == null) {
                    return FAILED;
                }

                bpmTpSignZone.setOrderSign(bpmSignZoneRequest.getOrderSign());
                bpmTpSignZone.setX(bpmSignZoneRequest.getX());
                bpmTpSignZone.setY(bpmSignZoneRequest.getY());
                bpmTpSignZone.setH(bpmSignZoneRequest.getH());
                bpmTpSignZone.setPage(bpmSignZoneRequest.getPage());
                bpmTpSignZone.setW(bpmSignZoneRequest.getW());
                bpmTpSignZone.setFirstName(bpmSignZoneRequest.getFirstName());
                bpmTpSignZone.setLastName(bpmSignZoneRequest.getLastName());
                bpmTpSignZone.setPosition(bpmSignZoneRequest.getPosition());
                bpmTpSignZone.setProcInstId(req.getProcInstId());
                bpmTpSignZone.setTaskDefKey(bpmSignZoneRequest.getTaskDefKey());
                bpmTpSignZone.setUpdatedUser(credentialHelper.getJWTPayload().getEmail());
                bpmTpSignZone.setUpdatedDate(new Date());

                if (credentialHelper.getJWTPayload().getEmail().equalsIgnoreCase(bpmTpSignZone.getEmail())
                        && bpmSignZoneRequest.getSign() != null
                        && bpmSignZoneRequest.getSign().contains(".")) {
                    bpmTpSignZone.setSign(bpmSignZoneRequest.getSign());
                } else if (credentialHelper.getJWTPayload().getEmail().equalsIgnoreCase(bpmTpSignZone.getEmail())) {
                    bpmTpSignZone.setSign(uploadBase64ToMinIO(bpmSignZoneRequest.getSign(), bpmSignZoneRequest.getFileType(), false));
                }

                bpmTpSignZones.add(bpmTpSignZone);
            }

            bpmTpSignZoneRepository.saveAll(bpmTpSignZones);
            bpmTpSignZoneHistoryManager.save(bpmTpSignZones);

            return SUCCESS;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return FAILED;
        }
    }

    public BpmTempTicket saveTempTicket(BpmTempTicket bpmTempTicket) {
        return bpmTempTicketRepository.save(bpmTempTicket);
    }

    public BpmTempTicket getTempTicketById(Long id) {
        return bpmTempTicketRepository.getBpmTempTicketById(id);
    }

    /**
     * Upload encoded print file to minIO
     *
     * <AUTHOR>
     */
    public String uploadBase64ToMinIO(String encodedBase64, String signZoneFileType, boolean isTemplateFile) {
        if (!ValidationUtils.isNullOrEmpty(encodedBase64)) {
            // set default file extension PNG
            if (ValidationUtils.isNullOrEmpty(signZoneFileType)) {
                signZoneFileType = CommonConstants.FileExtension.PNG;
            }

            UUID id = UUID.randomUUID();
            String fileName = isTemplateFile
                    ? (CommonConstants.DefaultFileName.SIGN_DOCUMENT + id + "." + CommonConstants.FileExtension.PDF)
                    : (CommonConstants.DefaultFileName.SIGNATURE + id) + "." + signZoneFileType;
            String folder = isTemplateFile ? CommonConstants.FileFolder.TEMPLATE_FILES : CommonConstants.FileFolder.SIGNATURE_FILES;
            return fileService.saveFileToMinIO(encodedBase64, folder, fileName, new Date(), true);
        }

        return null;
    }

    /**
     * List all sign zones has email is null
     *
     * <AUTHOR>
     */
    @Transactional(noRollbackFor = RuntimeException.class)
    public List<BpmTpSignZone> getAllSignZonesWithNullEmail(String procInstId, String taskDefKey) {
        try {
            return bpmTpSignZoneRepository.getAllSignZonesWithNullEmail(procInstId, taskDefKey);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return new ArrayList<>();
    }

    /**
     * List all sign zones has email is null
     * => CHANGE: list all sign zones by procInstId and taskDefKey
     *
     * <AUTHOR>
     */
    @Transactional(noRollbackFor = RuntimeException.class)
    public List<BpmTpSignZone> getAllSignZonesByProcInstIdAndTaskDefKey(String procInstId, String taskDefKey) {
        try {
            return bpmTpSignZoneRepository.getBpmTpSignZonesByProcInstIdAndTaskDefKey(procInstId, taskDefKey);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return new ArrayList<>();
    }

    /**
     * Update user email to sign zones
     *
     * <AUTHOR>
     */
    @Transactional(noRollbackFor = RuntimeException.class)
    public void updateSignZones(String procInstId, String taskDefKey, String email, String ticketCreatedUser) {
        // get user info
        List<AccountModel> userInfos = customerService.getAccountByUsernames(Arrays.asList(email, ticketCreatedUser));
        AccountModel userInfo = userInfos.stream()
                .filter(e -> e.getUsername().equalsIgnoreCase(email))
                .findFirst().orElse(null);

        // Change -> list all sign zones by procInstId and taskDefKey
//        List<BpmTpSignZone> signZones = getAllSignZonesWithNullEmail(procInstId, taskDefKey);
        List<BpmTpSignZone> signZones = getAllSignZonesByProcInstIdAndTaskDefKey(procInstId, taskDefKey);

        signZones.forEach(zone -> {
            zone.setEmail(email);
            zone.setUpdatedDate(new Date());
            zone.setUpdatedUser(email);

            if (userInfo != null) {
                zone.setFirstName(userInfo.getFirstname());
                zone.setLastName(userInfo.getLastname());
                zone.setPosition(userInfo.getFinalTitle());
            }
        });

        saveAll(signZones);
    }

    /**
     * Save all entities
     *
     * <AUTHOR>
     */
    public List<BpmTpSignZone> saveAll(List<BpmTpSignZone> bpmTpSignZones) {
        if (ValidationUtils.isNullOrEmpty(bpmTpSignZones)) {
            return bpmTpSignZones;
        }

        return bpmTpSignZoneRepository.saveAll(bpmTpSignZones);
    }

    /**
     * Get request-update sign zones
     *
     * <AUTHOR>
     */
    public List<BpmTpSignZone> getRequestUpdateSignZones(String procInstId, Set<String> taskDefKeys) {
        return bpmTpSignZoneRepository.getRequestUpdateSignZones(procInstId, taskDefKeys);
    }

    /**
     * Get signed files by ticket-id and status
     *
     * <AUTHOR>
     */
    @Transactional(noRollbackFor = RuntimeException.class)
    public List<SignedFileDto> getSignedFiles(String procInstId, List<String> ticketStatus) {
        try {
            List<Tuple> signZones = bpmTpSignZoneRepository.getSignedFiles(procInstId, ticketStatus);
            Map<String, Integer> signedMap = new HashMap<>();
            List<SignedFileDto> signedFiles = new ArrayList<>();
            int pos = 0;
            if (!ValidationUtils.isNullOrEmpty(signZones)) {
                for (Tuple e : signZones) {
                    BpmTpSignZone signZone = QueryUtils.getValueFromTuple(e, "signZone", BpmTpSignZone.class);
                    String fileName = QueryUtils.getValueFromTuple(e, "fileName", String.class);
                    if (signZone != null) {
                        String mapKey = getSignedFileMapKey(signZone);
                        SignedFileDto signedFile = new SignedFileDto(signZone.getTaskDefKey(), signZone.getSignedFile(), fileName, signZone.getBpmTemplatePrintId());
                        if (!signedMap.containsKey(mapKey)) {
                            signedMap.put(mapKey, pos);
                            signedFiles.add(signedFile);
                            pos++;
                        } else {
                            int replacePos = signedMap.get(mapKey);
                            signedFiles.set(replacePos, signedFile);
                        }
                    }
                }
            }

            return signedFiles;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return new ArrayList<>();
    }

    private String getSignedFileMapKey(BpmTpSignZone bpmTpSignZone) {
        if (bpmTpSignZone != null) {
            return (!ValidationUtils.isNullOrEmpty(bpmTpSignZone.getProcInstId()) ? bpmTpSignZone.getProcInstId() : "") + "::" + (!ValidationUtils.isNullOrEmpty(bpmTpSignZone.getTaskDefKey()) ? bpmTpSignZone.getTaskDefKey() : "");
        }

        return "";
    }

    public List<BpmTpSignZoneDto> findAll(String procInstId, String taskDefKey) {
        try {
            return bpmTpSignZoneRepository.findAll(procInstId, taskDefKey);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return new ArrayList<>();
    }

    public List<BpmTpSignZone> findOne(String procInstId, String taskDefKey) {
        try {
            return bpmTpSignZoneRepository.findOne(procInstId, taskDefKey);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return new ArrayList<>();
    }

    public List<BpmTpSignZoneResponse> findAllByTicketIdOrRequestCode(List<Long> ticketId, List<String> requestCode, String taskDefKey) {
        try {
            return bpmTpSignZoneRepository.findAllByTicketIdOrRequestCode(ticketId, requestCode, taskDefKey);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return new ArrayList<>();
    }

    public BpmTpSignZone getSignZoneByTaskDefKeyAndUser(String procInstId, String taskDefKey, String username) {
        return bpmTpSignZoneRepository.getSignZoneByTaskDefKeyAndUser(procInstId, taskDefKey, username);
    }

    public BpmTpSignZone save(BpmTpSignZone bpmTpSignZone) {
        return bpmTpSignZoneRepository.save(bpmTpSignZone);
    }

    public Map<String, Object> getSignByUsername(String username) {
        return bpmTpSignZoneRepository.getSignByUsername(username);
    }

    public void processCreateSignZone(BpmPrintSignZoneRequest printSignZoneRequest) {
        // save temp ticket sign zone
        BpmTempTicket tempTicket = new BpmTempTicket();
        tempTicket.setProcInstId(printSignZoneRequest.getProcInstId());
        tempTicket.setTempData(StringUtil.objectToJson(printSignZoneRequest));

        // combine sign
        int result = saveTypeTemplatePrint(printSignZoneRequest);

        tempTicket.setStatus(result != FAILED);
        bpmTempTicketRepository.save(tempTicket);
    }
}
