package vn.fis.eapprove.business.domain.report.service.impl;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcInstRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTaskRepository;
import vn.fis.eapprove.business.domain.changeAssignee.repository.ChangeAssigneeHistoryRepository;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;
import vn.fis.eapprove.business.domain.priority.repository.PriorityManagementRepository;
import vn.fis.eapprove.business.domain.report.entity.ReportByGroup;
import vn.fis.eapprove.business.domain.report.repository.ReportByChartNodeRepo;
import vn.fis.eapprove.business.domain.report.repository.ReportByGroupRepository;
import vn.fis.eapprove.business.domain.report.service.ReportByGroupService;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.repository.ServicePackageRepository;
import vn.fis.eapprove.business.dto.AssigneeInfoDto;
import vn.fis.eapprove.business.dto.ReportByGroupDto;
import vn.fis.eapprove.business.mapper.IReportByGroupMapper;
import vn.fis.eapprove.business.model.response.UserInfoByUsername;
import vn.fis.eapprove.business.tenant.manager.impl.CustomerServiceImpl;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDateTime;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReportByGroupServiceImpl implements ReportByGroupService {

    private final ReportByGroupRepository reportByGroupRepository;
    private final ReportByChartNodeRepo reportByChartNodeRepo;
    private final IReportByGroupMapper mapper;
    private final BpmProcInstRepository bpmProcInstRepository;
    private final ServicePackageRepository servicePackageRepository;
    private final PriorityManagementRepository priorityManagementRepository;
    private final CustomerServiceImpl customerService;
    private final ChangeAssigneeHistoryRepository changeAssigneeHistoryRepository;
    private final BpmTaskRepository bpmTaskRepository;

    @Override
    public void createReportByGroup(Long procInstId) {
        try {
            System.out.println("TicketId: >>>>>" + procInstId + "<<<<" + "----------start--------create------report by group--------------------------------");

            if (!this.isTicketExist(procInstId)) {
                this.insert(procInstId);
                System.out.println("TicketId: " + procInstId + "------------------insert------ok--------------------------------");

            } else {
                this.update(procInstId);
            }
            System.out.println("TicketId: " + procInstId + "------------------update------ok--------------------------------");
        } catch (Exception e) {
            System.out.println("TicketId: " + procInstId + "------------------error--------------------------------");
            e.printStackTrace();
        }
    }

    @Override
    public ReportByGroup insert(Long bpmProcInstId) {
        ReportByGroupDto reportByGroupDto = this.mapToReportByGroup(bpmProcInstId);
        if (reportByGroupDto == null) {
            return null;
        }
        ReportByGroup reportByGroup = mapper.to(reportByGroupDto);
        return reportByGroupRepository.save(reportByGroup);
    }

    @Override
    public ReportByGroup update(Long bpmProcInstId) {
        ReportByGroupDto reportByGroupDto = this.mapToReportByGroup(bpmProcInstId);
        if (reportByGroupDto == null) {
            return null;
        }
        ReportByGroup existed = this.findByTicketId(reportByGroupDto.getTicketId());
        ReportByGroup update = mapper.merge(existed, reportByGroupDto);
        return reportByGroupRepository.save(update);
    }

    @Override
    public ReportByGroup findByTicketId(Long procInstId) {
        return reportByGroupRepository.findByTicketId(procInstId);
    }

    @Override
    public boolean isTicketExist(Long procInstId) {
        ReportByGroup existed = reportByGroupRepository.findByTicketId(procInstId);
        return existed != null;
    }

    public List<Long> getTicketIds(String fromDate, String toDate) {
        return bpmProcInstRepository.getBpmProcInstByCreatedTime(fromDate, toDate);
    }

    @Override
    public void syncReportByGroup(String fromDate, String toDate) {
        List<Long> ticketIds = this.getTicketIds(fromDate, toDate);
        for (Long e : ticketIds) {
            this.createReportByGroup(e);
        }
    }

    @Override
    public void deleteTaskReturnExist(Long ticketId) {
        try {
            BpmProcInst bpmProcInst = bpmProcInstRepository.getBpmProcInstByTicketId(ticketId);
            if (bpmProcInst == null) {
                return;
            }
            List<String> status = new ArrayList<>();
            status.add("DELETED_BY_RU");
            status.add("AGREE_TO_RECALL");
            List<BpmTask> bpmTasks = bpmTaskRepository.findBpmTaskReturnByProcInstIdAndStatus(bpmProcInst.getTicketProcInstId(), status);
            if (bpmTasks.isEmpty()) {
                return;
            }
            List<String> taskIds = new ArrayList<>();
            for (BpmTask e : bpmTasks) {
                taskIds.add(e.getTaskId());
            }
            reportByChartNodeRepo.deleteByTaskId(taskIds);
        } catch (Exception e) {
            log.error("Error when delete task return exist: ", e);
        }
    }

    public ReportByGroupDto mapToReportByGroup(Long bpmProcInstId) {
        ReportByGroupDto reportByGroupDto = new ReportByGroupDto();
        BpmProcInst bpmProcInst = bpmProcInstRepository.findById(bpmProcInstId).orElseThrow(() -> new RuntimeException("BpmProcInst not found"));
        if (bpmProcInst.getTicketStatus().equals("DRAFT")) {
            return null;
        }
        List<BpmTask> bpmTasks = bpmTaskRepository.findBpmTaskByProcInstId(bpmProcInst.getTicketProcInstId());
        Set<LocalDateTime> taskStartTimes = new HashSet<>();
        if (!ValidationUtils.isNullOrEmpty(bpmTasks)) {
            for (BpmTask e : bpmTasks) {
                taskStartTimes.add(e.getTaskCreatedTime());
            }
        }
        LocalDateTime maxTaskStartTime = taskStartTimes.stream()
                .max(LocalDateTime::compareTo)
                .orElse(null);
        Set<String> assignees = new HashSet<>();
        Set<String> taskTypes = new HashSet<>();
        if (!ValidationUtils.isNullOrEmpty(bpmTasks)) {
            for (BpmTask e : bpmTasks) {
                if (Boolean.TRUE.equals(e.getAssignType())) {
                    String orgAssignee = changeAssigneeHistoryRepository.getAssigneeOrgByTaskId(e.getTaskId());
                    assignees.add(orgAssignee);
                    taskTypes.add(e.getTaskType());
                } else {
                    assignees.add(e.getTaskAssignee());
                    taskTypes.add(e.getTaskType());
                }
            }
        }
        AssigneeInfoDto assigneeInfoDto = customerService.getAssigneeInfoByListUser(assignees);
        if (maxTaskStartTime != null) {
            reportByGroupDto.setActionCurrentTime(maxTaskStartTime);
        } else {
            reportByGroupDto.setActionCurrentTime(bpmProcInst.getTicketCreatedTime());
        }
        reportByGroupDto.setAssignee(assignees.toString());
        reportByGroupDto.setTaskType(taskTypes.toString());
        if (assigneeInfoDto != null) {
            reportByGroupDto.setAssigneeChartId(assigneeInfoDto.getChartIds().toString());
            reportByGroupDto.setAssigneeChartNodeId(assigneeInfoDto.getChartNodeIds().toString());
            reportByGroupDto.setAssigneeStatus(assigneeInfoDto.getStatus().toString());
        }

        Optional<ServicePackage> servicePackage = servicePackageRepository.findById(bpmProcInst.getServiceId());
        if (servicePackage.isPresent()) {
            reportByGroupDto.setServiceName(servicePackage.get().getServiceName());
            reportByGroupDto.setSubmissionType(servicePackage.get().getSubmissionType());
            reportByGroupDto.setMasterParentId(servicePackage.get().getMasterParentId());
        } else {
            return null;
        }
        if (bpmProcInst.getChartNodeId() != null) {
            reportByGroupDto.setChartNodeId(bpmProcInst.getChartNodeId());
        }
        reportByGroupDto.setServiceId(bpmProcInst.getServiceId());
        reportByGroupDto.setTicketId(bpmProcInst.getTicketId());
        reportByGroupDto.setProcDefId(bpmProcInst.getTicketProcDefId());
        reportByGroupDto.setTitle(bpmProcInst.getTicketTitle());
        reportByGroupDto.setProcInstStatus(bpmProcInst.getTicketStatus());
        reportByGroupDto.setProcInstId(bpmProcInst.getTicketProcInstId());
        reportByGroupDto.setRequestCode(bpmProcInst.getRequestCode());
        reportByGroupDto.setSlaFinish(bpmProcInst.getSlaFinish());
        reportByGroupDto.setStartedTime(bpmProcInst.getTicketStartedTime());
        reportByGroupDto.setFinishedTime(bpmProcInst.getTicketFinishTime());
        reportByGroupDto.setCreatedUser(bpmProcInst.getCreatedUser().toLowerCase());
        reportByGroupDto.setLocationId(bpmProcInst.getLocationId());
        reportByGroupDto.setCreatedTime(bpmProcInst.getTicketCreatedTime());
        reportByGroupDto.setChartNodeName(bpmProcInst.getChartNodeName());
        reportByGroupDto.setChartNodeCode(bpmProcInst.getChartNodeCode());
        reportByGroupDto.setChartId(bpmProcInst.getChartId());
        reportByGroupDto.setPriorityId(bpmProcInst.getPriorityId());

        if (bpmProcInst.getPriorityId() != null) {
            Optional<PriorityManagement> priorityManagement = priorityManagementRepository.findById(bpmProcInst.getPriorityId());
            if (priorityManagement.isPresent()) {
                reportByGroupDto.setPriorityName(priorityManagement.get().getName());
            } else {
                reportByGroupDto.setPriorityName("DEFAULT");
            }
        }
        UserInfoByUsername createUser = customerService.getUserInfoByUsername(bpmProcInst.getCreatedUser().toLowerCase());
        if (createUser != null) {
            reportByGroupDto.setCreatedUserFullName(createUser.getFullName());
            reportByGroupDto.setChartShortName(createUser.getChartShortName());
            reportByGroupDto.setUserTitleName(createUser.getTitleName());
            reportByGroupDto.setStaffCode(createUser.getStaffCode());
            reportByGroupDto.setDirectManager(createUser.getDirectManager());
            reportByGroupDto.setUserStatus(createUser.getStatus());
            reportByGroupDto.setManagerLevel(createUser.getManagerLevel());
            reportByGroupDto.setEmail(createUser.getEmail());
        }
//        long timeRes = bpmProcInst.getSlaFinish().longValue() * 60;
//        LocalDateTime slaFinishTime = customerService.getTimeSla(timeRes, bpmProcInst.getTicketCreatedTime(), bpmProcInst.getCreatedUser());

        // btp sla
        long timeRes = bpmProcInst.getSlaFinish().longValue() + 1;
        LocalDateTime slaFinishTime = bpmProcInst.getTicketCreatedTime().plusDays(timeRes).toLocalDate().atStartOfDay();

        if (!ValidationUtils.isNullOrEmpty(slaFinishTime)) {
            reportByGroupDto.setSlaFinishTime(slaFinishTime);
        }
        return reportByGroupDto;
    }

}