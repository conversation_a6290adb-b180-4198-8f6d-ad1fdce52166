package vn.fis.eapprove.business.domain.groupTable.service;


import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.domain.groupTable.entity.GroupTableProTemp;
import vn.fis.eapprove.business.domain.groupTable.repository.GroupProcTempRepository;
import vn.fis.eapprove.business.model.response.GroupProcTempResponse;


import java.util.List;
import java.util.stream.Collectors;

@Service("GroupProcTempManagerV1")
public class GroupProcTempManager {
    @Autowired
    GroupProcTempRepository groupProcTempRepository;
    @Autowired
    ModelMapper modelMapper;

    public List<GroupProcTempResponse> getAll() {
        try {
            List<GroupTableProTemp> listRes = groupProcTempRepository.findAll();
            List<GroupProcTempResponse> listRp = listRes.stream().map(x -> modelMapper.map(x, GroupProcTempResponse.class)).collect(Collectors.toList());
            return listRp;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}


