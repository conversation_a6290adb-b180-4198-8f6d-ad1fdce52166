package vn.fis.eapprove.business.domain.bpm.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "bpm_owner_process")
@AllArgsConstructor
@NoArgsConstructor
public class BpmOwnerProcess {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "PROC_DEF_ID")
    private Long procDefId;

    @Column(name = "ID_USER")
    private String idUser;

    @Column(name = "VER_PROC_DEF_ID")
    private String verProcDefId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PROC_DEF_ID", referencedColumnName = "[ID]", insertable = false, updatable = false)
    @JsonIgnore
    private BpmProcdef bpmProcdef;
}
