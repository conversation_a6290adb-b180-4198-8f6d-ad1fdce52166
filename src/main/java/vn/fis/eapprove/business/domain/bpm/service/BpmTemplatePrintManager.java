package vn.fis.eapprove.business.domain.bpm.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import jakarta.persistence.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.hwpf.usermodel.Paragraph;
import org.apache.poi.hwpf.usermodel.Range;
import org.apache.poi.hwpf.usermodel.Section;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.*;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;
import vn.fis.eapprove.business.config.GsonAdapterConfig;
import vn.fis.eapprove.business.constant.TypeFile;
import vn.fis.eapprove.business.domain.authority.service.AuthService;
import vn.fis.eapprove.business.domain.bpm.entity.*;
import vn.fis.eapprove.business.domain.bpm.repository.*;
import vn.fis.eapprove.business.domain.fileCondition.entity.FileCondition;
import vn.fis.eapprove.business.domain.fileCondition.service.FileService;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.repository.ServicePackageRepository;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.repository.ShareUserRepository;
import vn.fis.eapprove.business.domain.system.repository.SystemGroupRepository;
import vn.fis.eapprove.business.dto.*;
import vn.fis.eapprove.business.exception.ErrorMessage;
import vn.fis.eapprove.business.model.request.BpmPrintPhaseRequest;
import vn.fis.eapprove.business.model.request.BpmTaskPrintRequest;
import vn.fis.eapprove.business.model.response.*;
import vn.fis.eapprove.business.specification.BpmPrintPhaseSpecification;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.tenant.manager.GotenbergManager;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.constants.*;
import vn.fis.spro.common.helper.RestHelper;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.manager.FileManager;

import java.io.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static vn.fis.eapprove.business.constant.Constant.*;

;

@Service("BpmTemplatePrintManagerV1")
@Slf4j
@Transactional
public class BpmTemplatePrintManager {
    @Autowired
    ModelMapper modelMapper;
    @Autowired
    ShareUserRepository shareUserRepository;
    @Autowired
    CustomerService customerService;
    @Autowired
    private BpmTemplatePrintRepository bpmTemplatePrintRepository;
    @Autowired
    private ServicePackageRepository servicePackageRepository;
    @Autowired
    private BpmTpTaskRepository bpmTpTaskRepository;
    @Autowired
    private BpmPrintPhaseSpecification bpmPrintPhaseSpecification;
    @Autowired
    private BpmTpSignZoneManager bpmPrintSignZoneManager;
    @Autowired
    private BpmTaskManager bpmTaskManager;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private ResponseUtils responseUtils;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private FileService fileService;
    @Autowired
    private CredentialHelper credentialHelper;
    @Autowired
    private BpmFileConditionRepository bpmFileConditionRepository;
    @Autowired
    private BpmTemplatePrintConfigRepository bpmTemplatePrintConfigRepository;
    @Autowired
    private SproProperties sproProperties;
    @Autowired
    private PermissionDataManagementRepository permissionDataManagementRepository;
    @Autowired
    private AuthService authService;
    @Autowired
    private RestHelper restHelper;
    @Autowired
    private SystemGroupRepository systemGroupRepository;
    @Autowired
    private Common common;
    @Value("${app.s3.bucket}")
    private String bucket;
    @Value("${spro.storage.url}")
    private String storageUrl;
    @Autowired
    private BpmTemplatePrintHistoryRepository bpmTemplatePrintHistoryRepository;
    @Autowired
    private BpmTemplateHtmlRepository bpmTemplateHtmlRepository;
    @Autowired
    private BpmProcdefRepository bpmProcdefRepository;
    @Autowired
    private GotenbergManager gotenbergManager;

    public int save(BpmPrintPhaseRequest req) {
        try {
            if (checkExistName(req.getName())) {
                return NAME_EXISTED;
            }
//            String createdUser = credentialHelper.getJWTPayload().getEmail().substring(0, credentialHelper.getJWTPayload().getEmail().indexOf("@"));
            BpmTemplatePrint bpmTemplatePrint = new BpmTemplatePrint();
            bpmTemplatePrint.setContent(req.getForm());
            bpmTemplatePrint.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            bpmTemplatePrint.setCreatedDate(new Date());
            bpmTemplatePrint.setName(req.getName());
            bpmTemplatePrint.setDescr(req.getDescription());
            bpmTemplatePrint.setProcDefId(req.getProcDefId());
            bpmTemplatePrint.setPrintType(req.getType());
            List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
            for (NameAndCodeCompanyResponse response : listCompanyCodeAndName) {
                bpmTemplatePrint.setCompanyCode(response.getCompanyCode());
                bpmTemplatePrint.setCompanyName(response.getCompanyName());
            }
            BpmTemplatePrint newPrintPhase = bpmTemplatePrintRepository.save(bpmTemplatePrint);
            List<BpmTpTask> bpmPrintPhaseTasks = new ArrayList<>();
            for (BpmTaskPrintRequest taskKey : req.getTaskKey()) {
                BpmTpTask bpmTpTask = new BpmTpTask();
                bpmTpTask.setName(taskKey.getTaskName());
                bpmTpTask.setProcDefId(req.getProcDefId());
                bpmTpTask.setFormKey(taskKey.getFormKey());
                bpmTpTask.setTaskDefKey(taskKey.getTaskKey());
                bpmTpTask.setBpmTemplatePrintId(newPrintPhase.getId());
                bpmTpTask.setTaskType(taskKey.getTaskType());
                bpmPrintPhaseTasks.add(bpmTpTask);
            }
            bpmTpTaskRepository.saveAll(bpmPrintPhaseTasks);
            BpmTemplatePrint newBpmPrintPhase = bpmTemplatePrintRepository.save(newPrintPhase);
            bpmTemplatePrintRepository.save(newBpmPrintPhase);

            return SUCCESS;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return FAILED;
        }
    }

    public String create1(CreateBpmTemplatePrintRequest bpmTemplatePrintRequest) {
        try {
            BpmTemplatePrint bpmTemplatePrint = new BpmTemplatePrint();
            bpmTemplatePrint.setProcessName(bpmTemplatePrintRequest.getProcessName());
            bpmTemplatePrint.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            bpmTemplatePrint.setName(bpmTemplatePrintRequest.getName());
            bpmTemplatePrint.setDescr(bpmTemplatePrintRequest.getDescription());
            bpmTemplatePrint.setProcDefId(bpmTemplatePrintRequest.getProcDefId());
            bpmTemplatePrint.setProcessId(bpmTemplatePrintRequest.getProcessId());
            bpmTemplatePrint.setPrintType(0);

            bpmTemplatePrint.setCreatedDate(new Date());
            bpmTemplatePrint.setHistoryChange(bpmTemplatePrintRequest.getJsonHistory());
            bpmTemplatePrint.setConfigType(bpmTemplatePrintRequest.getConfigType());
            List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
            for (NameAndCodeCompanyResponse response : listCompanyCodeAndName) {
                bpmTemplatePrint.setCompanyCode(response.getCompanyCode());
                bpmTemplatePrint.setCompanyName(response.getCompanyName());
            }
            BpmTemplatePrint newPrintPhase = bpmTemplatePrintRepository.save(bpmTemplatePrint);

            // save history
            BpmTemplatePrintHistory history = modelMapper.map(newPrintPhase, BpmTemplatePrintHistory.class);
            history.setId(null);
            history.setBpmTemplatePrintId(newPrintPhase.getId());
            history.setVersion("V0");
            history.setStatusHistory(true);
            history.setCreatedDate(LocalDateTime.now());
            history.setContentEdit("Thêm mới thành công mẫu trình ký");
            history.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            history.setConfigType(bpmTemplatePrintRequest.getConfigType());

            Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();
            // Lưu phân quyền dữ liệu
            if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrintRequest.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : bpmTemplatePrintRequest.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(newPrintPhase.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.BPM_TEMPLATE_PRINT.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }
                permissionDataManagementRepository.saveAll(permissionDataManagements);
                history.setApplyFor(g.toJson(permissionDataManagements));
            }

            if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrintRequest.getShareWith())) {
                List<SharedUser> sharedUsers = new ArrayList<>();
                for (String shareWith : bpmTemplatePrintRequest.getShareWith()) {
                    SharedUser sharedUser = new SharedUser();
                    sharedUser.setReferenceId(newPrintPhase.getId());
                    sharedUser.setReferenceType(ShareUserTypeEnum.TEMPLATEPRINT.type);
                    sharedUser.setEmail(shareWith);
                    sharedUsers.add(sharedUser);
                }
                shareUserRepository.saveAll(sharedUsers);
                history.setShareWith(g.toJson(sharedUsers));
            }

            // ưu tiên check cấu hình html trước
            if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrintRequest.getHtmlConditionRequests())) {
                List<FileCondition> listFileConditions = new ArrayList<>();
                for (FileConditionRequest htmlRequest : bpmTemplatePrintRequest.getHtmlConditionRequests()) {
                    FileCondition fileCondition = new FileCondition();
                    fileCondition.setHtmlId(htmlRequest.getHtmlId());
                    BpmTemplateHtml bpmTemplateHtml = bpmTemplateHtmlRepository.getBpmTemplateHtmlById(htmlRequest.getHtmlId());
                    if (bpmTemplateHtml != null) {
                        String templateHtml = bpmTemplateHtml.getHtmlContent();
                        String templateHeader = bpmTemplateHtml.getHeaderContent();
                        String templateFooter = bpmTemplateHtml.getFooterContent();
                        if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrintRequest.getStringMap())) {
                            Set<String> lstKeySet = bpmTemplatePrintRequest.getStringMap().keySet();
                            for (String key : lstKeySet) {
                                templateHtml = templateHtml.replace(key, bpmTemplatePrintRequest.getStringMap().get(key));
                            }
                        }
                        fileCondition.setTemplateHtmlChange(templateHtml);
                        fileCondition.setTemplateHtmlHeader(templateHeader);
                        fileCondition.setTemplateHtmlFooter(templateFooter);
                        if (!ValidationUtils.isNullOrEmpty(htmlRequest.getCondition())) {
                            fileCondition.setConditionText(htmlRequest.getCondition());
                        }
                        fileCondition.setBpmTemplatePrintId(newPrintPhase.getId());
                        if (!ValidationUtils.isNullOrEmpty(htmlRequest.getBpmTaskPrintRequest())) {
                            fileCondition.setName(htmlRequest.getBpmTaskPrintRequest().getTaskName());
                            fileCondition.setTaskType(htmlRequest.getBpmTaskPrintRequest().getTaskType());
                            fileCondition.setTaskDefKey(htmlRequest.getBpmTaskPrintRequest().getTaskKey());
                            fileCondition.setFormKey(htmlRequest.getBpmTaskPrintRequest().getFormKey());
                        } else {
                            fileCondition.setName(bpmTemplatePrintRequest.getTaskKey().get(0).getTaskName());
                            fileCondition.setTaskType(bpmTemplatePrintRequest.getTaskKey().get(0).getTaskType());
                            fileCondition.setTaskDefKey(bpmTemplatePrintRequest.getTaskKey().get(0).getTaskKey());
                            fileCondition.setFormKey(bpmTemplatePrintRequest.getTaskKey().get(0).getFormKey());

                        }
                        listFileConditions.add(fileCondition);
                    }
                    bpmFileConditionRepository.saveAll(listFileConditions);
                    history.setConditionFile(g.toJson(listFileConditions));
                }
            } else
                if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrintRequest.getFileConditionRequests())) { // cấu hình docx
                List<FileCondition> listFileConditions = new ArrayList<>();
                for (FileConditionRequest fileConditionRequest : bpmTemplatePrintRequest.getFileConditionRequests()) {
                    FileCondition fileCondition = new FileCondition();
                    fileCondition.setUploadWords(fileConditionRequest.getFileName());
                    InputStream inputStream = fileManager.getFileInputStream(bucket, fileConditionRequest.getFileName());
                    File createTempFile = null;
                    if (fileConditionRequest.getFileName().endsWith(".docx")) {
                        createTempFile = File.createTempFile(fileConditionRequest.getFileName().replace(".docx", "") + "_change", ".docx");
                        XWPFDocument doc = new XWPFDocument(inputStream);
                        OutputStream out = new FileOutputStream(createTempFile);
                        for (String key : bpmTemplatePrintRequest.getStringMap().keySet()) {
                            if (bpmTemplatePrintRequest.getStringMap().get(key) != null && !bpmTemplatePrintRequest.getStringMap().get(key).isEmpty()) {
                                replaceTextDocx(doc, key, bpmTemplatePrintRequest.getStringMap().get(key));
                            }
                        }
                        doc.write(out);
                    } else {
                        createTempFile = File.createTempFile(fileConditionRequest.getFileName().replace(".doc", "") + "_change", ".doc");
                        HWPFDocument doc = new HWPFDocument(inputStream);
                        OutputStream out = new FileOutputStream(createTempFile);
                        for (String key : bpmTemplatePrintRequest.getStringMap().keySet()) {
                            if (bpmTemplatePrintRequest.getStringMap().get(key) != null && !bpmTemplatePrintRequest.getStringMap().get(key).isEmpty()) {
                                if (key.equalsIgnoreCase(bpmTemplatePrintRequest.getStringMap().get(key))) {
                                    continue;
                                }
                                replaceTextDoc(doc, key, bpmTemplatePrintRequest.getStringMap().get(key));
                            }
                        }
                        doc.write(out);
                    }
                    FileInputStream input = new FileInputStream(createTempFile);
                    fileManager.putFile(bucket, createTempFile.getName(), createTempFile.length(), input);

                    fileCondition.setUploadWordsChange(createTempFile.getName());
                    if (!ValidationUtils.isNullOrEmpty(fileConditionRequest.getCondition())) {
                        fileCondition.setConditionText(fileConditionRequest.getCondition());
                    }
                    fileCondition.setBpmTemplatePrintId(newPrintPhase.getId());
                    if (!ValidationUtils.isNullOrEmpty(fileConditionRequest.getBpmTaskPrintRequest())) {
                        fileCondition.setName(fileConditionRequest.getBpmTaskPrintRequest().getTaskName());
                        fileCondition.setTaskType(fileConditionRequest.getBpmTaskPrintRequest().getTaskType());
                        fileCondition.setTaskDefKey(fileConditionRequest.getBpmTaskPrintRequest().getTaskKey());
                        fileCondition.setFormKey(fileConditionRequest.getBpmTaskPrintRequest().getFormKey());
                    } else {
                        fileCondition.setName(bpmTemplatePrintRequest.getTaskKey().get(0).getTaskName());
                        fileCondition.setTaskType(bpmTemplatePrintRequest.getTaskKey().get(0).getTaskType());
                        fileCondition.setTaskDefKey(bpmTemplatePrintRequest.getTaskKey().get(0).getTaskKey());
                        fileCondition.setFormKey(bpmTemplatePrintRequest.getTaskKey().get(0).getFormKey());

                    }
                    listFileConditions.add(fileCondition);
                    createTempFile.deleteOnExit();
                }
                bpmFileConditionRepository.saveAll(listFileConditions);
                history.setConditionFile(g.toJson(listFileConditions));
            }

            List<BpmTpTask> lstBpmTpTasks = new ArrayList<>();
            for (int i = 0; i < bpmTemplatePrintRequest.getTaskKey().size(); i++) {
                BpmTpTask bpmTpTask = new BpmTpTask();
                if (i == 0) {
                    bpmTpTask.setName(bpmTemplatePrintRequest.getTaskKey().get(i).getTaskName());
                    bpmTpTask.setProcDefId(bpmTemplatePrintRequest.getProcDefId());
                    bpmTpTask.setFormKey(bpmTemplatePrintRequest.getTaskKey().get(i).getFormKey());
                    bpmTpTask.setStatus("START");
                    bpmTpTask.setTaskDefKey(bpmTemplatePrintRequest.getTaskKey().get(i).getTaskKey());
                    bpmTpTask.setBpmTemplatePrintId(newPrintPhase.getId());
                    bpmTpTask.setTaskType(bpmTemplatePrintRequest.getTaskKey().get(i).getTaskType());
                } else {
                    bpmTpTask.setName(bpmTemplatePrintRequest.getTaskKey().get(i).getTaskName());
                    bpmTpTask.setProcDefId(bpmTemplatePrintRequest.getProcDefId());
                    bpmTpTask.setFormKey(bpmTemplatePrintRequest.getTaskKey().get(i).getFormKey());
                    bpmTpTask.setStatus("PROCESS");
                    bpmTpTask.setTaskDefKey(bpmTemplatePrintRequest.getTaskKey().get(i).getTaskKey());
                    bpmTpTask.setBpmTemplatePrintId(newPrintPhase.getId());
                    bpmTpTask.setTaskType(bpmTemplatePrintRequest.getTaskKey().get(i).getTaskType());
                }
                lstBpmTpTasks.add(bpmTpTask);
            }
            bpmTpTaskRepository.saveAll(lstBpmTpTasks);
            history.setTpTask(g.toJson(lstBpmTpTasks));

            bpmTemplatePrintHistoryRepository.save(history);

            return messageSource.getMessage("message.template.create.saveSuccess", null, Locale.getDefault());

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    public String updateBpm(CreateBpmTemplatePrintRequest bpmTemplatePrintRequest) {
        try {
            BpmTemplatePrint bpmTemplatePrint = bpmTemplatePrintRepository.findById(bpmTemplatePrintRequest.getId()).get();

            // handle history content edit
            String editContent = handleEditContent(bpmTemplatePrintRequest, bpmTemplatePrint);

            bpmTemplatePrint.setProcessName(bpmTemplatePrintRequest.getProcessName());
            bpmTemplatePrint.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
            bpmTemplatePrint.setName(bpmTemplatePrintRequest.getName());
            if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrintRequest.getDescription())) {
                bpmTemplatePrint.setDescr(bpmTemplatePrintRequest.getDescription());
            }
            bpmTemplatePrint.setProcDefId(bpmTemplatePrintRequest.getProcDefId());
            bpmTemplatePrint.setProcessId(bpmTemplatePrintRequest.getProcessId());
            bpmTemplatePrint.setPrintType(0);
            bpmTemplatePrint.setUpdatedDate(new Date());
            bpmTemplatePrint.setHistoryChange(bpmTemplatePrintRequest.getJsonHistory());
            bpmTemplatePrint.setConfigType(bpmTemplatePrintRequest.getConfigType());
            BpmTemplatePrint newPrintPhase = bpmTemplatePrintRepository.save(bpmTemplatePrint);

            Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();
            // save history
            BpmTemplatePrintHistory history = modelMapper.map(newPrintPhase, BpmTemplatePrintHistory.class);
            history.setId(null);
            history.setBpmTemplatePrintId(newPrintPhase.getId());
            int version = Math.toIntExact(bpmTemplatePrintHistoryRepository.countByBpmTemplatePrintId(bpmTemplatePrint.getId()));
            history.setVersion("V" + version);
            history.setStatusHistory(true);
            history.setContentEdit(editContent);
            history.setCreatedDate(LocalDateTime.now());
            history.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            history.setConfigType(bpmTemplatePrintRequest.getConfigType());

            List<BpmTemplatePrintHistory> listOldHistory = bpmTemplatePrintHistoryRepository.findByBpmTemplatePrintId(bpmTemplatePrint.getId());
            for (BpmTemplatePrintHistory oldHistory : listOldHistory) {
                oldHistory.setStatusHistory(false);
            }
            bpmTemplatePrintHistoryRepository.saveAll(listOldHistory);

            // Lưu phân quyền dữ liệu
            // Xóa data cũ
            List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(newPrintPhase.getId(), PermissionDataConstants.Type.BPM_TEMPLATE_PRINT.code);
            if (!ValidationUtils.isNullOrEmpty(oldData)) {
                permissionDataManagementRepository.deleteAll(oldData);
            }
            if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrintRequest.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : bpmTemplatePrintRequest.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(newPrintPhase.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.BPM_TEMPLATE_PRINT.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }
                permissionDataManagementRepository.saveAll(permissionDataManagements);
                history.setApplyFor(g.toJson(permissionDataManagements));
            }

            // share user
            shareUserRepository.deleteAllByReferenceIdAndReferenceType(newPrintPhase.getId(), ShareUserTypeEnum.TEMPLATEPRINT.type);
            if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrintRequest.getShareWith())) {
                List<SharedUser> sharedUsers = new ArrayList<>();
                for (String shareWith : bpmTemplatePrintRequest.getShareWith()) {
                    SharedUser sharedUser = new SharedUser();
                    sharedUser.setReferenceId(newPrintPhase.getId());
                    sharedUser.setReferenceType(ShareUserTypeEnum.TEMPLATEPRINT.type);
                    sharedUser.setEmail(shareWith);
                    sharedUsers.add(sharedUser);
                }
                shareUserRepository.saveAll(sharedUsers);
                history.setShareWith(g.toJson(sharedUsers));
            }

            bpmFileConditionRepository.deleteAllByBpmTemplatePrintId(newPrintPhase.getId());

            // ưu tiên check cấu hình html trước
            if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrintRequest.getHtmlConditionRequests())) {
                List<FileCondition> listFileConditions = new ArrayList<>();
                for (FileConditionRequest htmlRequest : bpmTemplatePrintRequest.getHtmlConditionRequests()) {
                    FileCondition fileCondition = new FileCondition();
                    fileCondition.setHtmlId(htmlRequest.getHtmlId());
                    BpmTemplateHtml bpmTemplateHtml = bpmTemplateHtmlRepository.getBpmTemplateHtmlById(htmlRequest.getHtmlId());
                    if (bpmTemplateHtml != null) {
                        String templateHtml = bpmTemplateHtml.getHtmlContent();
                        String templateHeader = bpmTemplateHtml.getHeaderContent();
                        String templateFooter = bpmTemplateHtml.getFooterContent();
                        if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrintRequest.getStringMap())) {
                            Set<String> lstKeySet = bpmTemplatePrintRequest.getStringMap().keySet();
                            for (String key : lstKeySet) {
                                templateHtml = templateHtml.replace(key, bpmTemplatePrintRequest.getStringMap().get(key));
                            }
                        }
                        fileCondition.setTemplateHtmlChange(templateHtml);
                        fileCondition.setTemplateHtmlHeader(templateHeader);
                        fileCondition.setTemplateHtmlFooter(templateFooter);
                        if (!ValidationUtils.isNullOrEmpty(htmlRequest.getCondition())) {
                            fileCondition.setConditionText(htmlRequest.getCondition());
                        }
                        fileCondition.setBpmTemplatePrintId(newPrintPhase.getId());
                        if (!ValidationUtils.isNullOrEmpty(htmlRequest.getBpmTaskPrintRequest())) {
                            fileCondition.setName(htmlRequest.getBpmTaskPrintRequest().getTaskName());
                            fileCondition.setTaskType(htmlRequest.getBpmTaskPrintRequest().getTaskType());
                            fileCondition.setTaskDefKey(htmlRequest.getBpmTaskPrintRequest().getTaskKey());
                            fileCondition.setFormKey(htmlRequest.getBpmTaskPrintRequest().getFormKey());
                        } else {
                            fileCondition.setName(bpmTemplatePrintRequest.getTaskKey().get(0).getTaskName());
                            fileCondition.setTaskType(bpmTemplatePrintRequest.getTaskKey().get(0).getTaskType());
                            fileCondition.setTaskDefKey(bpmTemplatePrintRequest.getTaskKey().get(0).getTaskKey());
                            fileCondition.setFormKey(bpmTemplatePrintRequest.getTaskKey().get(0).getFormKey());

                        }
                        listFileConditions.add(fileCondition);
                    }
                    bpmFileConditionRepository.saveAll(listFileConditions);
                    history.setConditionFile(g.toJson(listFileConditions));
                }
            } else
            if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrintRequest.getFileConditionRequests())) { // cấu hình docx
                List<FileCondition> lstFileConditions = new ArrayList<>();
                for (FileConditionRequest fileConditionRequest : bpmTemplatePrintRequest.getFileConditionRequests()) {
                    FileCondition fileCondition = new FileCondition();
                    fileCondition.setUploadWords(fileConditionRequest.getFileName());
                    InputStream inputStream = fileManager.getFileInputStream(bucket, fileConditionRequest.getFileName());
                    File createTempFile = null;
                    if (fileConditionRequest.getFileName().endsWith(".docx")) {
                        createTempFile = File.createTempFile(fileConditionRequest.getFileName().replace(".docx", "") + "_change", ".docx");
                        XWPFDocument doc = new XWPFDocument(inputStream);
                        OutputStream out = new FileOutputStream(createTempFile);
                        for (String key : bpmTemplatePrintRequest.getStringMap().keySet()) {
                            if (bpmTemplatePrintRequest.getStringMap().get(key) != null && !bpmTemplatePrintRequest.getStringMap().get(key).isEmpty()) {
                                replaceTextDocx(doc, key, bpmTemplatePrintRequest.getStringMap().get(key));
                            }
                        }
                        doc.write(out);
                    } else {
                        createTempFile = File.createTempFile(fileConditionRequest.getFileName().replace(".doc", "") + "_change", ".doc");
                        HWPFDocument doc = new HWPFDocument(inputStream);
                        OutputStream out = new FileOutputStream(createTempFile);
                        for (String key : bpmTemplatePrintRequest.getStringMap().keySet()) {
                            if (bpmTemplatePrintRequest.getStringMap().get(key) != null && !bpmTemplatePrintRequest.getStringMap().get(key).isEmpty()) {
                                if (key.equalsIgnoreCase(bpmTemplatePrintRequest.getStringMap().get(key))) {
                                    continue;
                                }
                                replaceTextDoc(doc, key, bpmTemplatePrintRequest.getStringMap().get(key));
                            }
                        }
                        doc.write(out);
                    }
                    FileInputStream input = new FileInputStream(createTempFile);
                    fileManager.putFile(bucket, createTempFile.getName(), createTempFile.length(), input);

                    fileCondition.setUploadWordsChange(createTempFile.getName());
                    fileCondition.setUploadWords(fileConditionRequest.getFileName());
                    if (!ValidationUtils.isNullOrEmpty(fileConditionRequest.getCondition())) {
                        fileCondition.setConditionText(fileConditionRequest.getCondition());
                    }
                    fileCondition.setBpmTemplatePrintId(newPrintPhase.getId());
                    if (!ValidationUtils.isNullOrEmpty(fileConditionRequest.getBpmTaskPrintRequest())) {
                        fileCondition.setName(fileConditionRequest.getBpmTaskPrintRequest().getTaskName());
                        fileCondition.setTaskType(fileConditionRequest.getBpmTaskPrintRequest().getTaskType());
                        fileCondition.setTaskDefKey(fileConditionRequest.getBpmTaskPrintRequest().getTaskKey());
                        fileCondition.setFormKey(fileConditionRequest.getBpmTaskPrintRequest().getFormKey());
                    } else {
                        fileCondition.setName(bpmTemplatePrintRequest.getTaskKey().get(0).getTaskName());
                        fileCondition.setTaskType(bpmTemplatePrintRequest.getTaskKey().get(0).getTaskType());
                        fileCondition.setTaskDefKey(bpmTemplatePrintRequest.getTaskKey().get(0).getTaskKey());
                        fileCondition.setFormKey(bpmTemplatePrintRequest.getTaskKey().get(0).getFormKey());
                    }
                    lstFileConditions.add(fileCondition);
                    createTempFile.deleteOnExit();
                }
                bpmFileConditionRepository.saveAll(lstFileConditions);
                history.setConditionFile(g.toJson(lstFileConditions));
            }

            bpmTpTaskRepository.deleteAllByBpmTemplatePrintId(newPrintPhase.getId());
            List<BpmTpTask> lstBpmTpTasks = new ArrayList<>();
            for (int i = 0; i < bpmTemplatePrintRequest.getTaskKey().size(); i++) {
                BpmTpTask bpmTpTask = new BpmTpTask();
                if (i == 0) {
                    bpmTpTask.setName(bpmTemplatePrintRequest.getTaskKey().get(i).getTaskName());
                    bpmTpTask.setProcDefId(bpmTemplatePrintRequest.getProcDefId());
                    bpmTpTask.setFormKey(bpmTemplatePrintRequest.getTaskKey().get(i).getFormKey());
                    bpmTpTask.setStatus("START");
                    bpmTpTask.setTaskDefKey(bpmTemplatePrintRequest.getTaskKey().get(i).getTaskKey());
                    bpmTpTask.setBpmTemplatePrintId(newPrintPhase.getId());
                    bpmTpTask.setTaskType(bpmTemplatePrintRequest.getTaskKey().get(i).getTaskType());
                } else {
                    bpmTpTask.setName(bpmTemplatePrintRequest.getTaskKey().get(i).getTaskName());
                    bpmTpTask.setProcDefId(bpmTemplatePrintRequest.getProcDefId());
                    bpmTpTask.setFormKey(bpmTemplatePrintRequest.getTaskKey().get(i).getFormKey());
                    bpmTpTask.setStatus("PROCESS");
                    bpmTpTask.setTaskDefKey(bpmTemplatePrintRequest.getTaskKey().get(i).getTaskKey());
                    bpmTpTask.setBpmTemplatePrintId(newPrintPhase.getId());
                    bpmTpTask.setTaskType(bpmTemplatePrintRequest.getTaskKey().get(i).getTaskType());
                }
                lstBpmTpTasks.add(bpmTpTask);
            }
            bpmTpTaskRepository.saveAll(lstBpmTpTasks);
            history.setTpTask(g.toJson(lstBpmTpTasks));

            bpmTemplatePrintHistoryRepository.save(history);

            return messageSource.getMessage("message.template.create.saveSuccess", null, Locale.getDefault());
        } catch (Exception e) {
            log.error("Edit mtk failed: ", e);
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    private String handleEditContent(CreateBpmTemplatePrintRequest request, BpmTemplatePrint bpmTemplatePrint) {
        StringBuilder builder = new StringBuilder();
        DataTemplatePrint data = showInformation(bpmTemplatePrint.getId());

        builder.append("Nâng phiên bản lịch sử");
        builder.append(System.lineSeparator());

        if (!request.getApplyFor().equals(data.getApplyFor())) {
            builder.append("Thay đổi Cho phép IT ở các công ty chỉnh sửa");
            builder.append(System.lineSeparator());
        }
        if (!request.getName().equalsIgnoreCase(data.getName())) {
            builder.append("Thay đổi Tên mẫu trình ký");
            builder.append(System.lineSeparator());
        }
        List<String> lstFileRequest = request.getFileConditionRequests().stream().map(FileConditionRequest::getFileName).toList();
        List<String> lstFileResponse = data.getFileConditionList().stream().map(FileConditionDto::getUploadWords).toList();
        if (!lstFileRequest.equals(lstFileResponse)) {
            builder.append("Thay đổi Chọn tệp trình ký");
            builder.append(System.lineSeparator());
        }
        if (!request.getDescription().equalsIgnoreCase(data.getDescr())) {
            builder.append("Thay đổi Mô tả");
            builder.append(System.lineSeparator());
        }
        if (!request.getShareWith().equals(data.getShareWith())) {
            builder.append("Thay đổi Chia sẻ với người dùng");
            builder.append(System.lineSeparator());
        }
        if (!request.getProcDefId().equalsIgnoreCase(data.getProcDefId())) {
            builder.append("Thay đổi Quy trình");
            builder.append(System.lineSeparator());
        }
        List<String> lstTaskRequest = request.getTaskKey().stream().map(BpmTaskPrintRequest::getTaskKey).toList();
        List<String> lstTaskResponse = data.getBpmTpTaskList().stream().map(BpmTpTask::getTaskDefKey).toList();
        if (!lstTaskRequest.equals(lstTaskResponse)) {
            builder.append("Thay đổi Chọn bước ký duyệt");
            builder.append(System.lineSeparator());
        }
        List<String> lstConditionRequest = request.getFileConditionRequests().stream().map(FileConditionRequest::getCondition).toList();
        List<String> lstConditionResponse = data.getFileConditionList().stream().map(FileConditionDto::getConditionText).toList();
        if (!lstConditionRequest.equals(lstConditionResponse)) {
            builder.append("Thay đổi Chọn điều kiện");
            builder.append(System.lineSeparator());
        }
        if (!request.getJsonHistory().equalsIgnoreCase(bpmTemplatePrint.getHistoryChange())) {
            builder.append("Thay đổi Danh sách biến thay thế");
            builder.append(System.lineSeparator());
        }

        return builder.toString();
    }

    public ContentDto getFormFile1(MultipartFile[] multipartFiles) {
        try {
            ZipSecureFile.setMinInflateRatio(0);
            Set<String> stringList = new HashSet<>();
            List<String> fileName = new ArrayList<>();
            for (MultipartFile file : multipartFiles) {
                if (file.isEmpty()) {
                    throw new ErrorMessage(messageSource.getMessage("error.templatePrint.valid.fileUploadNotEmpty", null, Locale.getDefault()));
                }
                List<TypeFile> list = TypeFile.stream().filter(s -> file.getOriginalFilename().endsWith(s.getTypeOfFile())).toList();
                if (list.isEmpty()) {
                    throw new ErrorMessage(messageSource.getMessage("error.templatePrint.valid.fileCheck", null, Locale.getDefault()));
                }
                String fileNameChange = upload(file);
                String[] words = null;
                String content = null;
                if (file.getOriginalFilename().endsWith(".docx")) {
                    XWPFDocument document = new XWPFDocument(OPCPackage.open(file.getInputStream()));
                    XWPFWordExtractor wordExtractor = new XWPFWordExtractor(document);
                    content = wordExtractor.getText();
                    words = wordExtractor.getText().split("@");
                    wordExtractor.close();
                } else {
                    HWPFDocument document = new HWPFDocument(file.getInputStream());
                    WordExtractor wordExtractor = new WordExtractor(document);
                    content = wordExtractor.getText();
                    words = wordExtractor.getText().split("@");
                }

                List<String> test = new ArrayList<>(Arrays.asList(words));
                if (!test.get(0).contains("@")) {
                    test.remove(0);
                }
                if (!content.endsWith("@")) {
                    test.remove(test.size() - 1);
                }
                List<String> listCase = test.stream().filter(s -> !s.contains(" ") && !s.isBlank()).toList();
                for (String a : listCase) {
                    if (!a.contains("\n") && !a.contains("\t")) {
                        stringList.add("@" + a + "@");
                    }
                }
                fileName.add(fileNameChange);
            }
            HashSet<String> sortedHashSet = stringList.stream()
                    .sorted()
                    .collect(Collectors.toCollection(LinkedHashSet::new));
            return ContentDto.builder().value(sortedHashSet).fileName(fileName).build();
        } catch (Exception e) {
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()));
        }
    }

    public String upload(MultipartFile file) {
        try {
            if (!fileManager.isBucketExisting(bucket)) {
                fileManager.createBucket(bucket);
            }
            return fileManager.putFile(bucket, file.getOriginalFilename(), file.getSize(), file.getInputStream());
        } catch (Exception e) {
            log.error("Happened error when upload file: ");
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    public BpmTemplatePrint getBpmTemplatePrint(String name) {
        return bpmTemplatePrintRepository.getByName1(name);
    }

    public DataTemplatePrint showInformation(Long id) {
        try {
            BpmTemplatePrint bpmTemplatePrint = bpmTemplatePrintRepository.getById(id);
            List<String> listEmail = new ArrayList<>();
            ObjectMapper mapper = new ObjectMapper();

            // get share user
            List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceIdAndReferenceType(bpmTemplatePrint.getId(), ShareUserTypeEnum.TEMPLATEPRINT.type);
            if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(bpmTemplatePrint.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
            }

            String des = "";
            if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrint.getDescr())) {
                des = bpmTemplatePrint.getDescr();
            }
            List<HistoryChangeDto> taskKey = mapper.readValue(bpmTemplatePrint.getHistoryChange(), new TypeReference<List<HistoryChangeDto>>() {
            });
            List<BpmTpTask> bpmTpTaskList = bpmTpTaskRepository.findBpmTpTaskByBpmTemplatePrintId(bpmTemplatePrint.getId());
            List<FileCondition> fileConditions = bpmFileConditionRepository.findByBpmTemplatePrintId(bpmTemplatePrint.getId());

            List<FileConditionDto> fileConditionDtos = fileConditions.stream()
                    .map(fileCondition -> modelMapper.map(fileCondition, FileConditionDto.class))
                    .collect(Collectors.toList());
            for (FileConditionDto condition : fileConditionDtos) {
                if (!ValidationUtils.isNullOrEmpty(condition.getUploadWords())) {
                    InputStream inputStream = fileManager.getFileInputStream(bucket, condition.getUploadWords());
                    byte[] sourceBytes = IOUtils.toByteArray(inputStream);
                    String encodedString = Base64.getEncoder().encodeToString(sourceBytes);
                    condition.setBase64(encodedString);
                }

                if (condition.getConditionText() == null) {
                    condition.setConditionText("");
                }
            }
            List<Long> htmlConfig = fileConditions.stream().filter(fileCondition -> fileCondition.getHtmlId() != null).map(FileCondition::getHtmlId).collect(Collectors.toList());

            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(bpmTemplatePrint.getId(), PermissionDataConstants.Type.BPM_TEMPLATE_PRINT.code);
            List<String> companyCodes = permissionDataManagements.stream().map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());

            return DataTemplatePrint.builder()
                    .id(bpmTemplatePrint.getId())
                    .name(bpmTemplatePrint.getName())
                    .processName(bpmTemplatePrint.getProcessName())
                    .descr(des)
                    .procDefId(bpmTemplatePrint.getProcDefId())
                    .shareWith(listEmail)
                    .bpmTpTaskList(bpmTpTaskList)
                    .historyChangeDtoList(taskKey)
                    .fileConditionList(fileConditionDtos)
                    .applyFor(companyCodes)
                    .htmlConfig(htmlConfig)
                    .configType(bpmTemplatePrint.getConfigType())
                    .build();
        } catch (Exception e) {

            log.error("Happened error when upload file: ", e);
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }

    }

    public Set<LoadUseDto> processUsed() {
        List<BpmTemplatePrint> bpmTemplatePrint = bpmTemplatePrintRepository.listProcessUsed();
        if (CollectionUtils.isEmpty(bpmTemplatePrint)) {
            throw new ErrorMessage(messageSource.getMessage("error.templatePrint.valid.dataValid", null, Locale.getDefault()));
        }
        Set<LoadUseDto> loadUseDtos = new HashSet<>();
        for (BpmTemplatePrint bpm : bpmTemplatePrint) {
            LoadUseDto loadUseDto = new LoadUseDto();
            loadUseDto.setName(bpm.getProcessName());
            loadUseDto.setProdefId(bpm.getProcDefId());
            loadUseDtos.add(loadUseDto);
        }
        return loadUseDtos;
    }

    public byte[] convertFileGotenberg(File file) throws Exception {
        String url = sproProperties.getServiceUrls().get(MapKeyEnum.PDF_SERVICE.key) + "/forms/libreoffice/convert";

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(credentialHelper.getJWTToken());
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("files", new FileSystemResource(file));
        body.add("merge", true);
//        body.add("pdfa", "PDF/A-1a");

        ResponseEntity<byte[]> responseEntity = restHelper.exchange(url,
                HttpMethod.POST,
                headers,
                body,
                new ParameterizedTypeReference<>() {
                });

        if (responseEntity != null) {
            return responseEntity.getBody();
        }

        return null;
    }

    public List<ReviewDto> review(PreviewRequest previewRequest) {
        try {
            List<ReviewDto> reviewDtoList = new ArrayList<>();
            for (String fileName : previewRequest.getFileName()) {
                ReviewDto reviewDto = new ReviewDto();
                InputStream inputStream = fileManager.getFileInputStream(bucket, fileName);
                File createTempFile = null;
                if (fileName.endsWith(".docx")) {
                    createTempFile = File.createTempFile(fileName.replace(".docx", "") + "_change", ".docx");
                    XWPFDocument doc = new XWPFDocument(inputStream);
                    OutputStream out = new FileOutputStream(createTempFile);
                    for (String key : previewRequest.getMap().keySet()) {
                        if (previewRequest.getMap().get(key) != null && !previewRequest.getMap().get(key).isEmpty()) {
                            replaceTextDocx(doc, key, previewRequest.getMap().get(key));
                        }
                    }
                    doc.write(out);
//                    Test local
//                    InputStream input = new FileInputStream(createTempFile);
//                    fileManager.putFile(bucket, "testMtkDocx.docx", input.available(), input);
                    byte[] response = convertFileGotenberg(createTempFile);
                    String base64 = Base64.getEncoder().encodeToString(response);
                    reviewDto.setFileName(fileName);
                    reviewDto.setBase64(base64);
                    reviewDtoList.add(reviewDto);

                } else {
                    createTempFile = File.createTempFile(fileName.replace(".doc", "") + "_change", ".doc");
                    HWPFDocument doc = new HWPFDocument(inputStream);
                    OutputStream out = new FileOutputStream(createTempFile);
                    for (String key : previewRequest.getMap().keySet()) {
                        if (previewRequest.getMap().get(key) != null && !previewRequest.getMap().get(key).isEmpty()) {
                            if (key.equalsIgnoreCase(previewRequest.getMap().get(key))) {
                                continue;
                            }
                            replaceTextDoc(doc, key, previewRequest.getMap().get(key));
                        }
                    }
                    doc.write(out);
//                    InputStream input = new FileInputStream(createTempFile);
//                    FileReq fileReq = FileReq.builder().filename(createTempFile.getName()).content(input.readAllBytes()).build();
//                    FileRes response = restTemplate.postForObject(sproProperties.getServiceUrls().get(MapKeyEnum.PDF_SERVICE.key) + "/office-to-pdf", fileReq, FileRes.class);
//                    String base64 = Base64.getEncoder().encodeToString(response.getContent());
                    byte[] response = convertFileGotenberg(createTempFile);
                    String base64 = Base64.getEncoder().encodeToString(response);
                    reviewDto.setFileName(fileName);
                    reviewDto.setBase64(base64);
                    reviewDtoList.add(reviewDto);
                }
            }
            return reviewDtoList;

        } catch (Exception e) {
            log.error("Happened error when upload file: ", e);
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    public void replaceTextDocxInRuns(XWPFParagraph p, List<XWPFRun> runs, String key, String textReplace) {
        int num = 0;
        if (p.getText().indexOf(key) != -1 && runs != null) {
            for (XWPFRun r : runs) {
                String t = r.getText(0);
                if (t != null && t.contains(key)) { // Trường hợp 1 run chứa key
                    t = t.replace(key, textReplace);
                    r.setText(t, 0);
                } else if (t != null && t.indexOf("@") != -1) { // Trường hợp bị tách thành nhiều run
                    for (int iR = 0; iR < t.length(); iR++) {
                        if (t.charAt(iR) == '@') {
                            int start = iR, end = start, startRun = num, endRun = startRun;
                            String endString = "@";
                            //Lấy index và run tương ứng để replace không bị mất style
                            for (int i = 1; i < key.length(); i++) {
                                if (iR + i < t.length() && t.charAt(iR + i) == key.charAt(i)) {
                                    end = iR + i;
                                    endRun = num;
                                    endString += key.charAt(i);
                                } else if (endString.length() < key.length()) {
                                    boolean accecpt = true;
                                    int indexKeyCheck = i;
                                    for (int j = num + 1; j < runs.size(); j++) {
                                        int runIndex = 0;
                                        String runsText = runs.get(j).getText(0);
                                        if (runsText != null) {
                                            while (runIndex < runsText.length()) {
                                                if (indexKeyCheck < key.length() && key.charAt(indexKeyCheck) == runsText.charAt(runIndex)) {
                                                    end = runIndex;
                                                    endRun = j;
                                                    endString += runsText.charAt(runIndex);
                                                } else accecpt = false;
                                                indexKeyCheck++;
                                                runIndex++;
                                                if (!accecpt) break;
                                            }
                                        }
                                        if (!accecpt) break;
                                    }
                                    if (endString.equals(key)) break;

                                }
                            }
                            //Xử lý chọn run lấy style phù hợp
                            if (endString.equals(key)) {
                                int runAccecpt = endRun;
                                if (endRun - startRun > 1) {
                                    // TH bị tách > 2 run thì lấy thằng run ở giữa
                                    runAccecpt = startRun + 1;
                                }
                                for (int i = startRun; i <= endRun; i++) {
                                    //Replace những thằng hợp lệ về ""
                                    String newReplace = "";
                                    if (i == startRun)
                                        newReplace = runs.get(i).getText(0).substring(0, start);
                                    else if (i == endRun) {
                                        newReplace = runs.get(i).getText(0).substring(end + 1);
                                    }
                                    runs.get(i).setText(newReplace, 0);
                                }
                                //Set biến replace vào run tương ứng
                                runs.get(runAccecpt).setText(textReplace, 0);
                                // remove các run có text = ""
                                for (int i = 0; i < runs.size(); i++) {
                                    if (runs.get(i).getText(0) != null && runs.get(i).getText(0).isEmpty()) {
                                        p.removeRun(i);
                                    }
                                }
                                return;
                            }
                        }
                    }
                }
                num++;
            }
        }
    }

    public void replaceTextDocx(XWPFDocument docx, String key, String textReplace) {

        for (XWPFParagraph p : docx.getParagraphs()) {
            String line = p.getText();
            if (line.contains(key)) {
                replaceTextDocxInRuns(p, p.getRuns(), key, textReplace);
            }

//            List<XWPFRun> runs = p.getRuns();
//            String line = p.getText();
//            StringBuilder text = new StringBuilder();
//            int num = 0;
//            if (runs != null) {
//                Boolean check = false;
//                for (XWPFRun r : runs) {
//
//                    num++;
//                    if (line != null && line.contains(key)) {
//                        check = true;
////                        line = line.replaceAll(key, textReplace);
//                        r.setText("", 0);
//
//                    } else {
//                        text.append(r.getText(r.getTextPosition()));
//                    }
//                    if (runs.size() == num && check) {
//                        line = line.replace(text, "").replace(key, textReplace);
//                        r.setText(line, 0);
//                    }
//                }
//            }
        }
        for (XWPFTable tbl : docx.getTables()) {
            for (XWPFTableRow row : tbl.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFTable tbl1 : cell.getTables()) {
                        for (XWPFTableRow row1 : tbl1.getRows()) {
                            for (XWPFTableCell cell1 : row1.getTableCells()) {
                                for (XWPFParagraph p : cell1.getParagraphs()) {
                                    replaceTextDocxInRuns(p, p.getRuns(), key, textReplace);
//                                    List<XWPFRun> runs = p.getRuns();
//                                    String line = p.getText();
//                                    StringBuilder text = new StringBuilder();
//                                    int num = 0;
//                                    if (runs != null) {
//                                        Boolean check = false;
//                                        for (XWPFRun r : runs) {
//
//                                            num++;
//                                            if (line != null && line.contains(key)) {
//                                                check = true;
//                                                r.setText("", 0);
//                                            } else {
//                                                text.append(r.getText(r.getTextPosition()));
//                                            }
//                                            if (runs.size() == num && check) {
//                                                line = line.replace(text, "").replace(key, textReplace);
//                                                r.setText(line, 0);
//                                            }
//                                        }
//                                    }
                                }
                            }
                        }
                    }
                    for (XWPFParagraph p : cell.getParagraphs()) {
                        replaceTextDocxInRuns(p, p.getRuns(), key, textReplace);
//                        List<XWPFRun> runs = p.getRuns();
//                        String line = p.getText();
//                        StringBuilder text = new StringBuilder();
//                        int num = 0;
//                        if (runs != null) {
//                            Boolean check = false;
//                            for (XWPFRun r : runs) {
//
//                                num++;
//                                if (line != null && line.contains(key)) {
//                                    check = true;
//                                    r.setText("", 0);
//                                } else {
//                                    text.append(r.getText(r.getTextPosition()));
//                                }
//                                if (runs.size() == num && check) {
//                                    line = line.replace(text, "").replace(key, textReplace);
//                                    r.setText(line, 0);
//                                }
//                            }
//                        }
                    }
                }
            }
        }

    }

    private HWPFDocument replaceTextDoc(HWPFDocument doc, String findText, String replaceText) {
        Range range = doc.getRange();
        for (int numSec = 0; numSec < range.numSections(); ++numSec) {
            Section sec = range.getSection(numSec);
            for (int numPara = 0; numPara < sec.numParagraphs(); numPara++) {
                Paragraph para = sec.getParagraph(numPara);
                para.replaceText(findText, replaceText);
            }
        }
        return doc;
    }

    public List<ReviewDto> getObject(Long id) {
        try {
            BpmTemplatePrint bpmTemplatePrint = bpmTemplatePrintRepository.getById(id);
            List<FileCondition> fileConditions = bpmFileConditionRepository.findByBpmTemplatePrintId(bpmTemplatePrint.getId());
            List<ReviewDto> reviewDtoList = new ArrayList<>();
            for (FileCondition fileCondition : fileConditions) {
                ReviewDto reviewDto = new ReviewDto();
                byte[] inputStream = fileManager.getFile(bucket, fileCondition.getUploadWordsChange());
                File createTempFile = new File("./" + fileCondition.getUploadWordsChange());
                reviewDto.setFileName(fileCondition.getUploadWordsChange());
                try (FileOutputStream fos = new FileOutputStream(createTempFile)) {
                    fos.write(inputStream);
                    fos.flush();
                } catch (Exception e) {
                    System.out.println(e.getMessage());
                }
                byte[] response = convertFileGotenberg(createTempFile);
                String base64 = Base64.getEncoder().encodeToString(response);
                reviewDto.setBase64(base64);
                reviewDtoList.add(reviewDto);
            }
            return reviewDtoList;
        } catch (Exception e) {
            log.error("Happened error when get list objects from minio: ", e);
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    public List<ReviewDto> showHtml(Long id) {
        List<FileCondition> fileConditions = bpmFileConditionRepository.findByBpmTemplatePrintId(id);
        List<ReviewDto> reviewDtoList = new ArrayList<>();
        for (FileCondition fileCondition : fileConditions) {
            ReviewDto reviewDto = new ReviewDto();
            reviewDto.setHtmlString(fileCondition.getTemplateHtmlChange());
            String bodyStr = fileCondition.getTemplateHtmlChange();
            String headerStr = fileCondition.getTemplateHtmlHeader();
            String footerStr = fileCondition.getTemplateHtmlFooter();
            byte [] response = gotenbergManager.htmlStringToPdf(bodyStr, headerStr, footerStr);
            String base64 = Base64.getEncoder().encodeToString(response);
            reviewDto.setBase64(base64);
            reviewDtoList.add(reviewDto);
        }
        return reviewDtoList;
    }


    public Boolean checkName(String name, Long id) {
        if (id != null) {
            BpmTemplatePrint bpmTemplatePrint = bpmTemplatePrintRepository.findById(id).get();
            if (!name.equalsIgnoreCase(bpmTemplatePrint.getName())) {
                return !checkExistName(name);
            }
        } else {
            return !checkExistName(name);
        }
        return true;
    }

    public boolean checkExistName(String name) {
        try {
            List<BpmTemplatePrint> printPhaseList = bpmTemplatePrintRepository.getByNameExist(name);
            return !printPhaseList.isEmpty();
        } catch (Exception e) {
            return true;
        }
    }

    //update mẫu trình ký
    public int update(BpmPrintPhaseRequest req) {
        try {
            BpmTemplatePrint bpmTemplatePrint = bpmTemplatePrintRepository.getById(req.getId());
            bpmTemplatePrint.setContent(req.getForm());
            bpmTemplatePrint.setName(req.getName());
            bpmTemplatePrint.setDescr(req.getDescription());

            String fileName = "print_" + req.getId() + ".pdf";
            Date now = new Date();
            String uploadedFileName = fileService.saveFileToMinIO(req.getBase64(), CommonConstants.FileFolder.TICKET_FILES, fileName, now, true);

            bpmTemplatePrint.setPdfContent(uploadedFileName);
            bpmTemplatePrint.setUpdatedDate(now);
            bpmTemplatePrint.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
            bpmTemplatePrintRepository.save(bpmTemplatePrint);
            return SUCCESS;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return FAILED;
        }
    }

    public BpmTemplatePrintDto getById(Long id) {
        BpmTemplatePrintDto bpmTemplatePrintDto = new BpmTemplatePrintDto();

        BpmTemplatePrint bpmTemplatePrint = bpmTemplatePrintRepository.getById(id);
        List<BpmTpTask> tpTasks = bpmTpTaskRepository.findBpmTpTaskByBpmTemplatePrintId(id);

        bpmTemplatePrintDto.setId(bpmTemplatePrint.getId());
        bpmTemplatePrintDto.setContent(bpmTemplatePrint.getContent());
        bpmTemplatePrintDto.setName(bpmTemplatePrint.getName());
        bpmTemplatePrintDto.setDescr(bpmTemplatePrint.getDescr());
        bpmTemplatePrintDto.setPrintType(bpmTemplatePrint.getPrintType());
        bpmTemplatePrintDto.setProcDefId(bpmTemplatePrint.getProcDefId());
        bpmTemplatePrintDto.setPdfContent(bpmTemplatePrint.getPdfContent());
        bpmTemplatePrintDto.setCreatedUser(bpmTemplatePrint.getCreatedUser());
        bpmTemplatePrintDto.setDeleted(bpmTemplatePrint.isDeleted());
        bpmTemplatePrintDto.setCreatedDate(bpmTemplatePrint.getCreatedDate());
        bpmTemplatePrintDto.setUpdatedDate(bpmTemplatePrint.getUpdatedDate());
        bpmTemplatePrintDto.setUpdatedUser(bpmTemplatePrint.getUpdatedUser());
        bpmTemplatePrintDto.setBpmTask(tpTasks);
        return bpmTemplatePrintDto;
    }

    public BpmTemplatePrint findById(Long id) {
        return bpmTemplatePrintRepository.getById(id);
    }

    public BpmPrintSignResponse getBpmPrintSignById(Long id, String procInstId, String username) {
        try {
            BpmPrintSignResponse bpmPrintSignResponse = new BpmPrintSignResponse();
            List<BpmTpTask> bpmPrintPhaseTasks = bpmTpTaskRepository
                    .findBpmTpTaskByBpmTemplatePrintId(id);
            List<BpmPrintTaskReponse> bpmPrintTaskReponses = new ArrayList<>();

            for (BpmTpTask bpmTpTask : bpmPrintPhaseTasks) {
                BpmPrintTaskReponse bpmPrintTaskReponse = new BpmPrintTaskReponse();
                bpmPrintTaskReponse.setId(bpmTpTask.getId());
                bpmPrintTaskReponse.setName(bpmTpTask.getName());
                bpmPrintTaskReponse.setTaskDefKey(bpmTpTask.getTaskDefKey());

                List<String> taskDefKeys = new ArrayList<>();
                taskDefKeys.add(bpmTpTask.getTaskDefKey());
                List<Map<String, Object>> listUser = bpmTaskManager.getAssigneeByTicket(procInstId, taskDefKeys);
                List<BpmSignUserReponse> bpmSignUserReponses = listUser.stream().map(e -> {
                    BpmSignUserReponse bpmSignUserReponse = new BpmSignUserReponse();
                    bpmSignUserReponse.setId(null);
                    bpmSignUserReponse.setName(e.get("assignee").toString());
                    return bpmSignUserReponse;
                }).collect(Collectors.toList());

                bpmPrintTaskReponse.setUsers(bpmSignUserReponses);

                if (bpmTpTask.getTaskType() == 1) {
                    bpmPrintTaskReponses.add(bpmPrintTaskReponse);
                }
            }
            BpmTemplatePrint bpmTemplatePrint = findById(id);
            if (bpmTemplatePrint == null) {
                return null;
            }

            List<BpmPrintSignZoneResponse> bpmPrintSignZoneResponses = bpmPrintSignZoneManager.getPrintSignZoneListByBpmPrintPhaseAndProcInst(bpmTemplatePrint, procInstId);

            bpmPrintSignResponse.setHtml(bpmTemplatePrint.getContent());
            bpmPrintSignResponse.setId(bpmTemplatePrint.getId());
            bpmPrintSignResponse.setName(bpmTemplatePrint.getName());
            bpmPrintSignResponse.setPrintType(bpmTemplatePrint.getPrintType());
            bpmPrintSignResponse.setPdfContent(bpmTemplatePrint.getPdfContent());
            bpmPrintSignResponse.setCreatedUser(bpmTemplatePrint.getCreatedUser());
            bpmPrintSignResponse.setCreatedDate(bpmTemplatePrint.getCreatedDate());
            bpmPrintSignResponse.setUpdatedDate(bpmTemplatePrint.getUpdatedDate());
            bpmPrintSignResponse.setUpdatedUser(bpmTemplatePrint.getUpdatedUser());
            bpmPrintSignResponse.setProcDefId(bpmTemplatePrint.getProcDefId());
            bpmPrintSignResponse.setTasks(bpmPrintTaskReponses);
            bpmPrintSignResponse.setSigns(bpmPrintSignZoneResponses);
            bpmPrintSignResponse.setSignedFile(getSignedFile(bpmPrintSignZoneResponses));
            bpmPrintSignResponse.setStorageUrl(storageUrl + CommonConstants.PATH_SEPARATOR + bucket + CommonConstants.PATH_SEPARATOR);

            return bpmPrintSignResponse;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Map<String, Object> combineSignedFile(Long id, String procInstId) {
        BpmTemplatePrint bpmTemplatePrint = findById(id);
        List<BpmPrintSignZoneResponse> bpmPrintSignZoneResponses = bpmPrintSignZoneManager.combineSignedFile(bpmTemplatePrint, procInstId);
        Map<String, Object> response = new HashMap<>();
        response.put("signedFile", getSignedFile(bpmPrintSignZoneResponses));
        response.put("storageUrl", storageUrl + CommonConstants.PATH_SEPARATOR + bucket + CommonConstants.PATH_SEPARATOR);
        return response;
    }

    private String getSignedFile(List<BpmPrintSignZoneResponse> bpmPrintSignZoneResponses) {
        if (!ValidationUtils.isNullOrEmpty(bpmPrintSignZoneResponses)) {
            return bpmPrintSignZoneResponses.stream().filter(e -> !ValidationUtils.isNullOrEmpty(e.getSignedFile())).findAny().map(BpmPrintSignZoneResponse::getSignedFile).orElse(null);
        }

        return null;
    }

    public ResponseEntity<?> deleteById(List<Long> listId) {
        List<BpmTemplatePrint> bpmTemplatePrint = bpmTemplatePrintRepository.findAllById(listId);
        String msg = "Thành công";
        if (bpmTemplatePrint.isEmpty()) {
            msg = common.getMessage("message.bpmTemplatePrint.notFound");
        }
        if (bpmTemplatePrint.stream().filter(i -> i.getStatus()!= null && i.getStatus().equals(Boolean.TRUE)).findAny().isEmpty()) {
            bpmTemplatePrint.forEach(i -> i.setDeleted(true));
            bpmTemplatePrintRepository.saveAll(bpmTemplatePrint);
        } else {
            msg = common.getMessage("message.bpmTemplatePrint.deleteExistTicket");
        }
        return ResponseEntity.ok(ResponseDto.builder().code(msg.equals("Thành công") ? ResponseCodeEnum.SUCCESS.code : ResponseCodeEnum.FAIL.code).message(msg).build());
    }


    //search mẫu trình ký
    public PageDto searchFilter(SearchFilter filter) {
        try {
            int pageNum = filter.getPage() - 1;
            Sort sort = responseUtils.getSort(filter.getSortBy(), filter.getSortType());

            // Add list company code user
            String username = credentialHelper.getJWTPayload().getUsername();
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

            filter.setLstCompanyCodes(lstCompanyCode);

            Page<BpmTemplatePrint> bpmTemplatePrints = bpmTemplatePrintRepository.findAll(bpmPrintPhaseSpecification.filterSearch(filter, username)
                    , PageRequest.of(pageNum, filter.getSize(),
                            sort));
            if (CollectionUtils.isEmpty(bpmTemplatePrints.getContent())) {
                return null;
            }
            List<BpmPrintPhaseResponse> bpmPrintPhaseResponses = new ArrayList<>();
            for (BpmTemplatePrint bpmTemplatePrint : bpmTemplatePrints.getContent()) {
                BpmPrintPhaseResponse bpmPrintPhaseResponse = new BpmPrintPhaseResponse();
                List<BpmTpTask> bpmPrintPhaseTasks = bpmTpTaskRepository
                        .findBpmTpTaskByBpmTemplatePrintId(bpmTemplatePrint.getId());
                String firstStep = "";
                for (BpmTpTask bpmTpTask : bpmPrintPhaseTasks) {
                    if (ValidationUtils.isNullOrEmpty(bpmTpTask.getStatus())) {
                        continue;
                    }
                    if (bpmTpTask.getStatus().equalsIgnoreCase("START")) {
                        firstStep = bpmTpTask.getName();
                    }
                }
                List<String> listEmail = new ArrayList<>();
                List<ServicePackage> servicePackageList = null;
                if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrint.getProcessId())) {
                    servicePackageList = servicePackageRepository.findByProcessIdAndStatusAndDeletedIsFalse(bpmTemplatePrint.getProcessId(), "ACTIVE");
                }
                bpmPrintPhaseResponse.setIsDelete(ValidationUtils.isNullOrEmpty(servicePackageList));
                bpmPrintPhaseResponse.setProcDefId(bpmTemplatePrint.getProcDefId());
                bpmPrintPhaseResponse.setId(bpmTemplatePrint.getId());
                bpmPrintPhaseResponse.setShareWith(listEmail);
                bpmPrintPhaseResponse.setName(bpmTemplatePrint.getName());
                bpmPrintPhaseResponse.setContent(bpmTemplatePrint.getContent());
                bpmPrintPhaseResponse.setCreatedDate(bpmTemplatePrint.getCreatedDate());
                bpmPrintPhaseResponse.setCreatedUser(bpmTemplatePrint.getCreatedUser());
                bpmPrintPhaseResponse.setUpdatedDate(bpmTemplatePrint.getUpdatedDate());
                bpmPrintPhaseResponse.setUpdatedUser(bpmTemplatePrint.getUpdatedUser());
                bpmPrintPhaseResponse.setPdfContent(bpmTemplatePrint.getPdfContent());
                bpmPrintPhaseResponse.setProcDefName(bpmTemplatePrint.getProcessName());
                bpmPrintPhaseResponse.setBpmPrintPhaseTasks(bpmPrintPhaseTasks);
                bpmPrintPhaseResponse.setFirstStep(firstStep);
                bpmPrintPhaseResponse.setUpdatedDate(bpmTemplatePrint.getUpdatedDate());
                bpmPrintPhaseResponse.setUpdatedUser(bpmTemplatePrint.getUpdatedUser());
                bpmPrintPhaseResponses.add(bpmPrintPhaseResponse);
            }
            return PageDto.builder()
                    .content(bpmPrintPhaseResponses)
                    .number(filter.getPage())
                    .numberOfElements(bpmTemplatePrints.getNumberOfElements())
                    .page(filter.getPage())
                    .size(bpmTemplatePrints.getSize())
                    .totalPages(bpmTemplatePrints.getTotalPages())
                    .totalElements(bpmTemplatePrints.getTotalElements())
                    .build();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    public PageDto search(SearchPrintDto criteria) {
        try {
            int pageNum = criteria.getPage() - 1;
            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());

            String username = credentialHelper.getJWTPayload().getUsername();
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.BPM_TEMPLATE_PRINT.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                criteria.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            Page<BpmTemplatePrint> page = bpmTemplatePrintRepository.findAll(
                    bpmPrintPhaseSpecification.filter(criteria, lstCompanyCode, username),
                    PageRequest.of(pageNum, criteria.getSize(), sort)
            );
            List<BpmTemplatePrint> a = page.getContent();
            List<BpmPrintPhaseResponse> bpmPrintPhaseResponses = new ArrayList<>();
            for (BpmTemplatePrint bpmTemplatePrint : page.getContent()) {
                BpmPrintPhaseResponse bpmPrintPhaseResponse = new BpmPrintPhaseResponse();
                List<BpmTpTask> bpmPrintPhaseTasks = bpmTpTaskRepository
                        .findBpmTpTaskByBpmTemplatePrintId(bpmTemplatePrint.getId());
                String firstStep = "";
                for (BpmTpTask bpmTpTask : bpmPrintPhaseTasks) {
                    if (ValidationUtils.isNullOrEmpty(bpmTpTask.getStatus())) {
                        continue;
                    }
                    if (bpmTpTask.getStatus().equalsIgnoreCase("START")) {
                        firstStep = bpmTpTask.getName();
                    }
                }
                List<String> listEmail = new ArrayList<>();
                List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceIdAndReferenceType(bpmTemplatePrint.getId(), ShareUserTypeEnum.TEMPLATEPRINT.type);
                if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                    listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(bpmTemplatePrint.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
                }

                List<ServicePackage> servicePackageList = null;
                if (!ValidationUtils.isNullOrEmpty(bpmTemplatePrint.getProcessId())) {
                    servicePackageList = servicePackageRepository.findByProcessIdAndStatusAndDeletedIsFalse(bpmTemplatePrint.getProcessId(), "ACTIVE");
                }
                bpmPrintPhaseResponse.setIsDelete(ValidationUtils.isNullOrEmpty(servicePackageList));
                bpmPrintPhaseResponse.setProcDefId(bpmTemplatePrint.getProcDefId());
                bpmPrintPhaseResponse.setId(bpmTemplatePrint.getId());
                bpmPrintPhaseResponse.setShareWith(listEmail);
                bpmPrintPhaseResponse.setName(bpmTemplatePrint.getName());
                bpmPrintPhaseResponse.setContent(bpmTemplatePrint.getContent());
                bpmPrintPhaseResponse.setCreatedDate(bpmTemplatePrint.getCreatedDate());
                bpmPrintPhaseResponse.setCreatedUser(bpmTemplatePrint.getCreatedUser());
                bpmPrintPhaseResponse.setUpdatedDate(bpmTemplatePrint.getUpdatedDate());
                bpmPrintPhaseResponse.setUpdatedUser(bpmTemplatePrint.getUpdatedUser());
                bpmPrintPhaseResponse.setPdfContent(bpmTemplatePrint.getPdfContent());
                bpmPrintPhaseResponse.setProcDefName(bpmTemplatePrint.getProcessName());
                bpmPrintPhaseResponse.setBpmPrintPhaseTasks(bpmPrintPhaseTasks);
                bpmPrintPhaseResponse.setFirstStep(firstStep);
                bpmPrintPhaseResponse.setUpdatedDate(bpmTemplatePrint.getUpdatedDate());
                bpmPrintPhaseResponse.setUpdatedUser(bpmTemplatePrint.getUpdatedUser());
                bpmPrintPhaseResponse.setCompanyCode(bpmTemplatePrint.getCompanyCode());
                bpmPrintPhaseResponse.setCompanyName(bpmTemplatePrint.getCompanyName());
                bpmPrintPhaseResponse.setSpecialFlow(bpmTemplatePrint.getSpecialFlow());
                bpmPrintPhaseResponse.setSpecialCompanyCode(bpmTemplatePrint.getSpecialCompanyCode());
                bpmPrintPhaseResponse.setSpecialParentServiceId(bpmTemplatePrint.getSpecialParentId());
                bpmPrintPhaseResponse.setSpecialParentId(bpmTemplatePrint.getSpecialParentServiceId());
                bpmPrintPhaseResponse.setStatus(bpmTemplatePrint.getStatus());
                bpmPrintPhaseResponse.setConfigType(bpmTemplatePrint.getConfigType());
                bpmPrintPhaseResponses.add(bpmPrintPhaseResponse);
            }

            List<Long> parentIds = bpmPrintPhaseResponses.stream().map(BpmPrintPhaseResponse::getId).collect(Collectors.toList());
            List<ChildResponse> child = bpmTemplatePrintRepository.findSpecialFlowChildDataByParentIds(parentIds);

            Set<String> companyCodes = child.stream().map(ChildResponse::getSpecialCompanyCode).collect(Collectors.toSet());
            List<NameAndCodeCompanyResponse> allCompanyCodeAndName = customerService.findAllCompanyCodeAndNameByCompanyCode(new ArrayList<>(companyCodes));
            if (allCompanyCodeAndName != null) {
                child.forEach(i -> i.setSpecialCompanyName(allCompanyCodeAndName.stream()
                        .filter(j -> j.getCompanyCode().equals(i.getSpecialCompanyCode()))
                        .findFirst()
                        .orElse(new NameAndCodeCompanyResponse())
                        .getCompanyName()));
            }

            bpmPrintPhaseResponses.forEach(i -> {
                i.setChild(child.stream().filter(c -> c.getParentId().equals(i.getId())).distinct().collect(Collectors.toList()));
            });
            return PageDto.builder()
                    .content(bpmPrintPhaseResponses)
                    .number(criteria.getPage())
                    .numberOfElements(page.getNumberOfElements())
                    .page(criteria.getPage())
                    .size(page.getSize())
                    .totalPages(page.getTotalPages())
                    .totalElements(page.getTotalElements())
                    .build();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    public List<BpmTemplatePrintConfigDto> getConfig() {
        try {
            List<BpmTemplatePrintConfig> bpmTemplatePrintConfig = bpmTemplatePrintConfigRepository.findAll();
            return bpmTemplatePrintConfig.stream()
                    .map(bpmTemplatePrintConfig1 -> modelMapper.map(bpmTemplatePrintConfig1, BpmTemplatePrintConfigDto.class)).collect(Collectors.toList());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }

    }

    public List<BpmPrintPhaseResponse> searchFilter(SearchPrintDto criteria) {
        try {
            String username = credentialHelper.getJWTPayload().getUsername();
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.BPM_TEMPLATE_PRINT.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                criteria.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            List<BpmTemplatePrint> allData = bpmTemplatePrintRepository.findAll(bpmPrintPhaseSpecification.filter(criteria, lstCompanyCode, username));
            List<BpmPrintPhaseResponse> bpmPrintPhaseResponses = new ArrayList<>();
            List<BpmTpTask> bpmPrintPhaseTasks = bpmTpTaskRepository.findAllByBpmTemplatePrintIdIn(allData.stream().map(BpmTemplatePrint::getId).collect(Collectors.toList()));
            for (BpmTemplatePrint bpmTemplatePrint : allData) {
                BpmPrintPhaseResponse bpmPrintPhaseResponse = new BpmPrintPhaseResponse();
                List<String> signStep = new ArrayList<>();
                List<BpmTpTask> filterList = bpmPrintPhaseTasks.stream().filter(i -> i.getBpmTemplatePrintId().equals(bpmTemplatePrint.getId())).toList();
                for (BpmTpTask bpmTpTask : filterList) {
                    if (bpmTpTask.getStatus().equalsIgnoreCase("START")) {
                        bpmPrintPhaseResponse.setFirstStep(bpmTpTask.getName());
                    } else signStep.add(bpmTpTask.getName());
                }
                List<String> listEmail = new ArrayList<>();
                List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceIdAndReferenceType(bpmTemplatePrint.getId(), ShareUserTypeEnum.TEMPLATEPRINT.type);
                if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                    listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(bpmTemplatePrint.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
                }

                bpmPrintPhaseResponse.setProcDefId(bpmTemplatePrint.getProcDefId());
                bpmPrintPhaseResponse.setId(bpmTemplatePrint.getId());
                bpmPrintPhaseResponse.setShareWith(listEmail);
                bpmPrintPhaseResponse.setName(bpmTemplatePrint.getName());
                bpmPrintPhaseResponse.setContent(bpmTemplatePrint.getContent());
                bpmPrintPhaseResponse.setProcDefName(bpmTemplatePrint.getProcessName());
                bpmPrintPhaseResponse.setCreatedUser(bpmTemplatePrint.getCreatedUser());

                bpmPrintPhaseResponse.setCompanyCode(bpmTemplatePrint.getCompanyCode());
                bpmPrintPhaseResponse.setCompanyName(bpmTemplatePrint.getCompanyName());
                bpmPrintPhaseResponse.setSignStep(signStep);

                bpmPrintPhaseResponse.setUpdatedUser(bpmTemplatePrint.getUpdatedUser());
                bpmPrintPhaseResponses.add(bpmPrintPhaseResponse);
            }
            return bpmPrintPhaseResponses;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    public void cloneTemplatePrintSpecial(List<BpmTemplatePrint> lstParent, BpmProcdef bpmProcdef, Long parentServiceId, String companyCode) {
        try {
            String username = credentialHelper.getJWTPayload().getUsername();
            for (BpmTemplatePrint parent : lstParent) {

                BpmTemplatePrint bpmTemplatePrint = bpmTemplatePrintRepository.findBpmTemplatePrintBySpecialParentIdAndSpecialCompanyCodeAndSpecialParentServiceId(parent.getId(), companyCode, parentServiceId);
                if (ValidationUtils.isNullOrEmpty(bpmTemplatePrint)) {
                    bpmTemplatePrint = new BpmTemplatePrint();
                    bpmTemplatePrint.setCreatedDate(new Date());
                    bpmTemplatePrint.setCreatedUser(username);
                    bpmTemplatePrint.setProcessName(bpmProcdef.getName());
                    bpmTemplatePrint.setName(parent.getName() + "-" + parentServiceId.toString() + "-" + companyCode);
                    bpmTemplatePrint.setDescr(parent.getDescr());
                    bpmTemplatePrint.setProcDefId(bpmProcdef.getProcDefId());
                    bpmTemplatePrint.setProcessId(bpmProcdef.getId());
                    bpmTemplatePrint.setPrintType(0);
                    bpmTemplatePrint.setPdfContent(parent.getPdfContent());
                    bpmTemplatePrint.setHistoryChange(parent.getHistoryChange());
                    List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(username);
                    for (NameAndCodeCompanyResponse response : listCompanyCodeAndName) {
                        bpmTemplatePrint.setCompanyCode(response.getCompanyCode());
                        bpmTemplatePrint.setCompanyName(response.getCompanyName());
                    }

                    bpmTemplatePrint.setSpecialFlow(true);
                    bpmTemplatePrint.setSpecialParentId(parent.getId());
                    bpmTemplatePrint.setSpecialCompanyCode(companyCode);
                    bpmTemplatePrint.setSpecialParentServiceId(parentServiceId);

                    BpmTemplatePrint newPrintPhase = bpmTemplatePrintRepository.save(bpmTemplatePrint);

                    // save history
                    BpmTemplatePrintHistory history = modelMapper.map(newPrintPhase, BpmTemplatePrintHistory.class);
                    history.setId(null);
                    history.setBpmTemplatePrintId(newPrintPhase.getId());
                    history.setVersion("V0");
                    history.setStatusHistory(true);
                    history.setCreatedDate(LocalDateTime.now());
                    history.setContentEdit("Thêm mới thành công mẫu trình ký");

                    Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();
                    // Start Lưu phân quyền dữ liệu
                    // Xóa data cũ
                    List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(newPrintPhase.getId(), PermissionDataConstants.Type.BPM_TEMPLATE_PRINT.code);
                    if (!ValidationUtils.isNullOrEmpty(oldData)) {
                        permissionDataManagementRepository.deleteAll(oldData);
                    }
                    // Phân quyền theo parent
                    List<PermissionDataManagement> dataParent = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(parent.getId(), PermissionDataConstants.Type.BPM_TEMPLATE_PRINT.code);
                    if (!ValidationUtils.isNullOrEmpty(dataParent)) {
                        List<PermissionDataManagement> lstPermission = new ArrayList<>();
                        for (PermissionDataManagement data : dataParent) {
                            PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                            permissionDataManagement.setTypeId(newPrintPhase.getId());
                            permissionDataManagement.setTypeName(PermissionDataConstants.Type.BPM_TEMPLATE_PRINT.code);
                            permissionDataManagement.setCompanyCode(data.getCompanyCode());
                            permissionDataManagement.setCreatedUser(username);
                            permissionDataManagement.setCreatedTime(LocalDateTime.now());
                            lstPermission.add(permissionDataManagement);
                        }
                        permissionDataManagementRepository.saveAll(lstPermission);
                        history.setApplyFor(g.toJson(lstPermission));
                    }
                    // End Lưu phân quyền dữ liệu

                    // Start save condition file
                    bpmFileConditionRepository.deleteAllByBpmTemplatePrintId(newPrintPhase.getId());
                    List<FileCondition> lstFileConditionParent = bpmFileConditionRepository.findByBpmTemplatePrintId(parent.getId());
                    List<FileCondition> lstFileConditionChild = new ArrayList<>();
                    if (!ValidationUtils.isNullOrEmpty(lstFileConditionParent)) {
                        for (FileCondition fileCondition : lstFileConditionParent) {
                            FileCondition fileConditionChild = new FileCondition();
                            fileConditionChild.setBpmTemplatePrintId(newPrintPhase.getId());
                            fileConditionChild.setUploadWords(fileCondition.getUploadWords());
                            fileConditionChild.setUploadWordsChange(fileCondition.getUploadWordsChange());
                            fileConditionChild.setTaskType(fileCondition.getTaskType());
                            fileConditionChild.setName(fileCondition.getName());
                            fileConditionChild.setTaskDefKey(fileCondition.getTaskDefKey());
                            fileConditionChild.setFormKey(fileCondition.getFormKey());
                            fileConditionChild.setConditionText(fileCondition.getConditionText());
                            lstFileConditionChild.add(fileConditionChild);
                        }
                        bpmFileConditionRepository.saveAll(lstFileConditionChild);
                        history.setConditionFile(g.toJson(lstFileConditionChild));
                    }
                    // End save condition file

                    // Save bpmTpTask
                    bpmTpTaskRepository.deleteAllByBpmTemplatePrintId(newPrintPhase.getId());
                    List<BpmTpTask> lstBpmTpTaskParent = bpmTpTaskRepository.findBpmTpTaskByBpmTemplatePrintId(parent.getId());
                    List<BpmTpTask> lstBpmTpTaskChild = new ArrayList<>();
                    if (!ValidationUtils.isNullOrEmpty(lstBpmTpTaskParent)) {
                        for (BpmTpTask bpmTpTask : lstBpmTpTaskParent) {
                            BpmTpTask bpmTpTaskChild = new BpmTpTask();
                            bpmTpTaskChild.setBpmTemplatePrintId(newPrintPhase.getId());
                            bpmTpTaskChild.setTaskType(bpmTpTask.getTaskType());
                            bpmTpTaskChild.setName(bpmTpTask.getName());
                            bpmTpTaskChild.setProcDefId(bpmProcdef.getProcDefId());
                            bpmTpTaskChild.setTaskDefKey(bpmTpTask.getTaskDefKey());
                            bpmTpTaskChild.setStatus(bpmTpTask.getStatus());
                            bpmTpTaskChild.setFormKey(bpmTpTask.getFormKey());
                            lstBpmTpTaskChild.add(bpmTpTaskChild);
                        }
                        bpmTpTaskRepository.saveAll(lstBpmTpTaskChild);
                        history.setTpTask(g.toJson(lstBpmTpTaskChild));
                    }
                    // End save bpmTpTask

                    // save history
                    bpmTemplatePrintHistoryRepository.save(history);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ErrorMessage(messageSource.getMessage(e.getMessage(), null, Locale.getDefault()), e);
        }
    }

    // Phân quyền theo nhóm
    public Object getAllSystemGroup()  {
        try {
            List<Map<String, Object>> lstResponse = new ArrayList<>();
            SearchPrintDto criteria = new SearchPrintDto();
            criteria.setPage(1);
            criteria.setLimit(999999);
            criteria.setSortBy("id");
            criteria.setSortType("DESC");

            int pageNum = criteria.getPage() - 1;
            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());

            String username = credentialHelper.getJWTPayload().getUsername();
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.BPM_TEMPLATE_PRINT.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                criteria.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            Page<BpmTemplatePrint> page = bpmTemplatePrintRepository.findAll(
                    bpmPrintPhaseSpecification.filter(criteria, lstCompanyCode, username),
                    PageRequest.of(pageNum, criteria.getLimit(), sort));

            List<BpmTemplatePrint> bpmTemplatePrints = page.getContent();
            for (BpmTemplatePrint bpmTemplatePrint : bpmTemplatePrints) {
                Map<String, Object> response = new HashMap<>();
                response.put("id", bpmTemplatePrint.getId());
                response.put("name", bpmTemplatePrint.getName());
                response.put("companyCode", bpmTemplatePrint.getCompanyCode());
                response.put("companyName", bpmTemplatePrint.getCompanyName());

                lstResponse.add(response);
            }
            return lstResponse;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public ContentDto getConfigHtml(List<Long> templateHtmlIds) {
        Set<String> stringSet = new HashSet<>();
        List<String> fileName = new ArrayList<>();
        List<BpmTemplateHtml> templateHtmls = bpmTemplateHtmlRepository.getBpmTemplateHtmlsByIdIn(templateHtmlIds);
        for (BpmTemplateHtml templateHtml : templateHtmls) {
            String htmlContent = templateHtml.getHtmlContent();
            if (htmlContent != null) {
                String[] lines = htmlContent.split("\\r?\\n");

                // Regex để khớp với các giá trị trong "@...@"
                Pattern pattern = Pattern.compile("@[^@]+@");

                for (String line : lines) {
                    Matcher matcher = pattern.matcher(line);
                    // Lặp để tìm tất cả các khớp trong dòng
                    while (matcher.find()) {
                        stringSet.add(matcher.group());
                    }
                }
            }
        }
        HashSet<String> sortedHashSet = stringSet.stream()
                .sorted()
                .collect(Collectors.toCollection(LinkedHashSet::new));
        return ContentDto.builder().value(sortedHashSet).fileName(fileName).build();
    }

    public List<ReviewDto> reviewHtml(PreviewRequest previewRequest) {
        List<ReviewDto> reviewDtoList = new ArrayList<>();
        List<BpmTemplateHtml> templateHtmls = bpmTemplateHtmlRepository.getBpmTemplateHtmlsByIdIn(previewRequest.getTemplateHtmlIds());
        for (BpmTemplateHtml templateHtml : templateHtmls) {
            ReviewDto reviewDto = new ReviewDto();
            String htmlContent = templateHtml.getHtmlContent();
            Set<String> lstKeySet = previewRequest.getMap().keySet();
            for (String key : lstKeySet) {
                htmlContent = htmlContent.replace(key, previewRequest.getMap().get(key));
            }
            reviewDto.setHtmlString(htmlContent);
            byte [] pdfContent = gotenbergManager.htmlStringToPdf(htmlContent, templateHtml.getHeaderContent(), templateHtml.getFooterContent());
            String base64 = Base64.getEncoder().encodeToString(pdfContent);
            reviewDto.setBase64(base64);
            reviewDtoList.add(reviewDto);
        }
        return reviewDtoList;
    }

    public List<BpmTemplatePrintResponse> getAllTemplateHtml() {
        List<Tuple> lstTuple = bpmTemplateHtmlRepository.getTupleBpmTemplateHtml();
        List<BpmTemplatePrintResponse> lstResponse = new ArrayList<>();
        for (Tuple tuple : lstTuple) {
            BpmTemplatePrintResponse response = new BpmTemplatePrintResponse();
            response.setId(tuple.get("id", Long.class));
            response.setName(tuple.get("name", String.class));
            lstResponse.add(response);
        }

        return lstResponse;
    }

    public int createPrintHtml(BpmPrintPhaseRequest request) {
        Long checkDup = bpmTemplateHtmlRepository.checkNameDuplicate(request.getName(), request.getHtmlId());
        if (checkDup > 0) {
            return NAME_EXISTED;
        }
        BpmTemplateHtml bpmTemplateHtml = new BpmTemplateHtml();
        if (!ValidationUtils.isNullOrEmpty(request.getHtmlId())) {
            bpmTemplateHtml = bpmTemplateHtmlRepository.getBpmTemplateHtmlById(request.getHtmlId());
        }
        bpmTemplateHtml.setName(request.getName());
        bpmTemplateHtml.setHtmlContent(request.getHtmlTemplate());
        bpmTemplateHtml.setHeaderContent(request.getHeaderTemplate());
        bpmTemplateHtml.setFooterContent(request.getFooterTemplate());
        bpmTemplateHtml.setSize(request.getSize());
        bpmTemplateHtmlRepository.save(bpmTemplateHtml);
        return SUCCESS;
    }

    public BpmTemplateHtml getDetailTemplateHtml(Long id) {
        return bpmTemplateHtmlRepository.getBpmTemplateHtmlById(id);
    }
}
