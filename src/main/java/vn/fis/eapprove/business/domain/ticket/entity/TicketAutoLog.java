package vn.fis.eapprove.business.domain.ticket.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "ticket_auto_log")
@Data
public class TicketAutoLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "ticket_id")
    private Long ticketId;

    @Column(name = "type")
    private String type;

    @Column(name = "process_time")
    private LocalDateTime processTime;

    @Column(name = "state")
    private String state;
    @Column(name = "log")
    private String log;

}
