package vn.fis.eapprove.business.domain.submission.service;

import vn.fis.eapprove.security.CredentialHelper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import vn.fis.eapprove.business.domain.authority.service.AuthService;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcInstRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcdefRepository;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.repository.ServicePackageRepository;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.repository.ShareUserRepository;
import vn.fis.eapprove.business.domain.sharedUser.service.ShareUserManager;
import vn.fis.eapprove.business.domain.submission.entity.SubmissionType;
import vn.fis.eapprove.business.domain.submission.repository.SubmissionTypeRepository;
import vn.fis.eapprove.business.domain.system.repository.SystemGroupRepository;
import vn.fis.eapprove.business.dto.ChartNodeDto;
import vn.fis.eapprove.business.dto.PageSearchDto;
import vn.fis.eapprove.business.dto.SearchSubmissionDto;
import vn.fis.eapprove.business.dto.SubmissionTypeDto;
import vn.fis.eapprove.business.exception.ErrorMessage;
import vn.fis.eapprove.business.model.request.SubmissionTypeRequest;
import vn.fis.eapprove.business.model.response.NameAndCodeCompanyResponse;
import vn.fis.eapprove.business.model.response.ServiceApplyResponse;
import vn.fis.eapprove.business.model.response.SubmissionTypeResponse;
import vn.fis.eapprove.business.specification.SubmissionTypeSpecification;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.business.utils.TimeUtils;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.MapKeyEnum;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ShareUserTypeEnum;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static vn.fis.eapprove.business.constant.Constant.*;

@Slf4j
@Service
@Transactional
public class SubmissionTypeManager {
    @Autowired
    CredentialHelper credentialHelper;
    @Autowired
    SproProperties sproProperties;
    @Autowired
    RestTemplate restTemplate;
    @Autowired
    SubmissionTypeRepository submissionTypeRepository;
    @Autowired
    ResponseUtils responseUtils;
    @Autowired
    BpmProcInstRepository bpmProcInstRepository;
    @Autowired
    BpmProcdefRepository bpmProcdefRepository;
    @Autowired
    SubmissionTypeSpecification submissionTypeSpecification;
    @Autowired
    CustomerService customerService;
    @Autowired
    ModelMapper modelMapper;
    @Autowired
    ShareUserManager shareUserManager;
    @Autowired
    ShareUserRepository shareUserRepository;
    @Autowired
    MessageSource messageSource;
    @Autowired
    private PermissionDataManagementRepository permissionDataManagementRepository;
    @Autowired
    private AuthService authService;
    @Autowired
    private SystemGroupRepository systemGroupRepository;
    @Autowired
    private ServicePackageRepository servicePackageRepository;

    public PageSearchDto searchSubmissionType(SearchSubmissionDto criteria) {
        try {
            int pageNum;
            if (criteria.getPage() > 1 && !ValidationUtils.isNullOrEmpty(criteria.getSearch())) {
                pageNum = 0;
            } else {
                pageNum = criteria.getPage() - 1;
            }
            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());

            String username = credentialHelper.getJWTPayload().getUsername();
            // Lấy list companyCode cấu hình QL vai trò người dùng
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.SUBMISSION_TYPE.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                criteria.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            Page<SubmissionType> page = submissionTypeRepository.findAll(
                    submissionTypeSpecification.filter(criteria, lstCompanyCode, username),
                    PageRequest.of(pageNum, criteria.getLimit(),
                            sort));

            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.SUBMISSION_TYPE.code);

            // Lấy list share user
            List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceType(ShareUserTypeEnum.SUBMISSION.type);
            List<SubmissionType> resultList  = page.getContent();
            List<Long> subIds = resultList.stream().map(SubmissionType::getId).collect(Collectors.toList());
            List<ServicePackage> bpmProcInsts = servicePackageRepository.getAllBySubmissionTypeIn(subIds);

            List<ServiceApplyResponse> serviceApplyResponse = submissionTypeRepository.getAllServiceApply(subIds);
            List<SubmissionTypeResponse> submissionTypeResponses = new ArrayList<>();

            for (SubmissionType submissionType : resultList) {
                List<Long> listSub = bpmProcInsts.stream().map(ServicePackage::getSubmissionType).collect(Collectors.toList());
                List<String> serviceApply = serviceApplyResponse.stream().filter(i-> Objects.equals(i.getId(), submissionType.getId())).map(ServiceApplyResponse::getName).collect(Collectors.toList());
                int status = listSub.contains(submissionType.getId()) ?1 : 0;

                List<String> listEmail = new ArrayList<>();
                if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                    listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(submissionType.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
                }

                // Lấy list companyCodes applyFor
                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(submissionType.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());

                SubmissionTypeResponse submissionTypeResponse = SubmissionTypeResponse.builder()
                        .id(submissionType.getId())
                        .companyCode(submissionType.getCompanyCode())
                        .typeName(submissionType.getTypeName())
                        .scopeApply(submissionType.getScopeApply())
                        .departmentCreate(Stream.of(submissionType.getDepartmentCreate().split(",", -1))
                                .collect(Collectors.toList()))
                        .shareWith(listEmail)
                        .status(status)
                        .description(submissionType.getDescription())
                        .createdUser(submissionType.getCreatedUser())
                        .createdDate(TimeUtils.localDateTimeToString(submissionType.getCreatedDate(), LOCALFORMAT))
                        .modifiedUser(submissionType.getModifiedUser())
                        .modifiedDate(submissionType.getModifiedDate() == null ? "" : TimeUtils.localDateTimeToString(submissionType.getModifiedDate(), LOCALFORMAT))
                        .applyFor(companyCodes)
                        .companyName(submissionType.getCompanyName())
                        .serviceApply(serviceApply)
                        .build();
                submissionTypeResponses.add(submissionTypeResponse);

            }
            return PageSearchDto.builder().content(submissionTypeResponses)
                    .number(page.getNumber() + 1)
                    .numberOfElements(page.getNumberOfElements())
                    .page(page.getNumber() + 1)
                    .limit(page.getSize())
                    .totalPages(page.getTotalPages())
                    .totalElements(page.getTotalElements())
                    .sortBy(criteria.getSortBy())
                    .sortType(criteria.getSortType())
                    .build();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }

    }

    public List<SubmissionTypeResponse> searchSubmissionTypeFilter(SearchSubmissionDto criteria) {
        try {
//            Integer pageNum;
//            if (criteria.getPage() > 1 && !ValidationUtils.isNullOrEmpty(criteria.getSearch())) {
//                pageNum = 0;
//            } else {
//                pageNum = criteria.getPage() - 1;
//            }
//            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());

            String username = credentialHelper.getJWTPayload().getUsername();
            // Lấy list companyCode cấu hình QL vai trò người dùng
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.SUBMISSION_TYPE.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                criteria.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            List<SubmissionType> page = submissionTypeRepository.findAll(
                    submissionTypeSpecification.filter(criteria, lstCompanyCode, username)
//                    , PageRequest.of(pageNum, criteria.getLimit(), sort)
            );

            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.SUBMISSION_TYPE.code);

            // Lấy list share user
            List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceType(ShareUserTypeEnum.SUBMISSION.type);

            List<SubmissionTypeResponse> submissionTypeResponses = new ArrayList<>();
            for (SubmissionType submissionType : page) {
                List<ServicePackage> bpmProcInsts = bpmProcInstRepository.getAllSubmissionTypeOnBpmProcInst(submissionType.getId());
                List<Long> listSub = bpmProcInsts.stream().map(ServicePackage::getSubmissionType).collect(Collectors.toList());
                int status = 0;
                if (listSub.contains(submissionType.getId())) {
                    status = 1;
                }

                List<String> listEmail = new ArrayList<>();
                if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                    listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(submissionType.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
                }

                // Lấy list companyCodes applyFor
                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(submissionType.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());

                SubmissionTypeResponse submissionTypeResponse = SubmissionTypeResponse.builder()
                        .id(submissionType.getId())
                        .companyCode(submissionType.getCompanyCode())
                        .companyName(submissionType.getCompanyName())
                        .typeName(submissionType.getTypeName())
                        .scopeApply(submissionType.getScopeApply())
                        .departmentCreate(Stream.of(submissionType.getDepartmentCreate().split(",", -1))
                                .collect(Collectors.toList()))
                        .shareWith(listEmail)
                        .status(status)
                        .description(submissionType.getDescription())
                        .createdUser(submissionType.getCreatedUser())
                        .createdDate(TimeUtils.localDateTimeToString(submissionType.getCreatedDate(), LOCALFORMAT))
                        .modifiedUser(submissionType.getModifiedUser())
                        .modifiedDate(submissionType.getModifiedDate() == null ? "" : TimeUtils.localDateTimeToString(submissionType.getModifiedDate(), LOCALFORMAT))
                        .applyFor(companyCodes)
                        .build();
                submissionTypeResponses.add(submissionTypeResponse);

            }
            return submissionTypeResponses;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }

    }

    public String createSubmissionType(SubmissionTypeRequest submissionTypeRequest) {
        try {
            String departmentCreate = submissionTypeRequest.getDepartmentCreate().stream().collect(Collectors.joining(","));
//            String shareWith = submissionTypeRequest.getShareWith().stream().collect(Collectors.joining(","));
            SubmissionType submissionType = new SubmissionType();
            submissionType.setTypeName(submissionTypeRequest.getTypeName());
            submissionType.setDepartmentCreate(departmentCreate);
            submissionType.setStatus((submissionTypeRequest.getStatus() != null) ? submissionTypeRequest.getStatus() : 0);
            submissionType.setDescription(submissionTypeRequest.getDescription());
//            submissionType.setShareWith(shareWith);
//            submissionType.setCompanyCode(getCode(credentialHelper.getJWTPayload().getEmail()));
            submissionType.setScopeApply(submissionTypeRequest.getScopeApply());
            submissionType.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            submissionType.setCreatedDate(LocalDateTime.now());
            List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
            for(NameAndCodeCompanyResponse response : listCompanyCodeAndName){
                submissionType.setCompanyCode(response.getCompanyCode());
                submissionType.setCompanyName(response.getCompanyName());
            }
            submissionTypeRepository.save(submissionType);

            // Lưu phân quyền dữ liệu
            if (!ValidationUtils.isNullOrEmpty(submissionTypeRequest.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : submissionTypeRequest.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(submissionType.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.SUBMISSION_TYPE.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                permissionDataManagementRepository.saveAll(permissionDataManagements);
            }

            if (!ValidationUtils.isNullOrEmpty(submissionTypeRequest.getShareWith())) {
                List<SharedUser> sharedUsers = new ArrayList<>();
                for (String shareWith : submissionTypeRequest.getShareWith()) {
                    SharedUser sharedUser = new SharedUser();
                    sharedUser.setReferenceId(submissionType.getId());
                    sharedUser.setReferenceType(ShareUserTypeEnum.SUBMISSION.type);
                    sharedUser.setEmail(shareWith);
                    sharedUsers.add(sharedUser);
                }
                shareUserRepository.saveAll(sharedUsers);
            }

            return MESSAGE_SUCCESS;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return MESSAGE_FAIL;
        }
    }

    public SubmissionType cloneSubmissionType(Long id) {
        try {
            SubmissionType submissionType = submissionTypeRepository.findById(id).get();
            String departmentCreate = submissionType.getDepartmentCreate();
            String shareWith = submissionType.getShareWith();
            SubmissionType submissionTypeCopy = new SubmissionType();
            String name = null;
            SubmissionType submissionType2 = submissionTypeRepository.findTopByTypeNameLikeOrderByIdDesc(submissionType.getTypeName() + " - copy" + "%").orElse(null);
            if (submissionType2 != null) {
                name = submissionType2.getTypeName();
            }
            if (name == null) {
                submissionTypeCopy.setTypeName(submissionType.getTypeName() + " - copy");
            } else {
                String numRegex = null;
                try {
                    Pattern pattern = Pattern.compile("^(.*)\\((\\d+)\\)$");
                    Matcher matcher = pattern.matcher(name);

                    if (matcher.find()) {
                        numRegex = matcher.group(2);
                    } else {
                        numRegex = "1";
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                if (numRegex != null) {
                    int num = Integer.parseInt(numRegex);
                    submissionTypeCopy.setTypeName(submissionType.getTypeName() + " - copy(" + (num + 1) + ")");
                }
            }
            submissionTypeCopy.setDepartmentCreate(departmentCreate);
            submissionTypeCopy.setStatus(0);
            submissionTypeCopy.setDescription(submissionType.getDescription());
            submissionTypeCopy.setShareWith(shareWith);
//            submissionTypeCopy.setCompanyCode(getCode(credentialHelper.getJWTPayload().getEmail()));

            submissionTypeCopy.setScopeApply(submissionType.getScopeApply());
            submissionTypeCopy.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            submissionTypeCopy.setCreatedDate(LocalDateTime.now());

            List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
            for(NameAndCodeCompanyResponse response : listCompanyCodeAndName){
                submissionTypeCopy.setCompanyCode(response.getCompanyCode());
                submissionTypeCopy.setCompanyName(response.getCompanyName());
            }

            submissionTypeCopy = submissionTypeRepository.save(submissionTypeCopy);

            // Lưu phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
            List<PermissionDataManagement> oldList = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(id,PermissionDataConstants.Type.SUBMISSION_TYPE.code);
            for (PermissionDataManagement data : oldList) {
                PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                permissionDataManagement.setTypeId(submissionTypeCopy.getId());
                permissionDataManagement.setTypeName(PermissionDataConstants.Type.SUBMISSION_TYPE.code);
                permissionDataManagement.setCompanyCode(data.getCompanyCode());
                permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                permissionDataManagement.setCreatedTime(LocalDateTime.now());
                permissionDataManagements.add(permissionDataManagement);
            }
            permissionDataManagementRepository.saveAll(permissionDataManagements);
            return submissionTypeCopy;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public SubmissionType getById(Long id) {
        SubmissionType submissionType = submissionTypeRepository.getSubmissionTypeById(id);
        return submissionType;
    }

    public String updateSubmissionType(SubmissionTypeRequest submissionTypeRequest) {
        try {

            String departmentCreate = submissionTypeRequest.getDepartmentCreate().stream().collect(Collectors.joining(","));
            String shareUsers = new Gson().toJson(submissionTypeRequest.getShareWith());
            SubmissionType submissionType = submissionTypeRepository.findById(submissionTypeRequest.getId()).get();
            submissionType.setTypeName(submissionTypeRequest.getTypeName());
            submissionType.setDepartmentCreate(departmentCreate);
            submissionType.setDescription(submissionTypeRequest.getDescription());
//            submissionType.setCompanyCode(getCode(credentialHelper.getJWTPayload().getEmail()));
            submissionType.setScopeApply(submissionTypeRequest.getScopeApply());
            submissionType.setModifiedUser(credentialHelper.getJWTPayload().getUsername());
            submissionType.setModifiedDate(LocalDateTime.now());

//            List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
//            for(NameAndCodeCompanyResponse response : listCompanyCodeAndName){
//                submissionType.setCompanyCode(response.getCompanyCode());
//                submissionType.setCompanyName(response.getCompanyName());
//            }

            // Lưu phân quyền dữ liệu
            // Xóa data cũ
            List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(submissionTypeRequest.getId(), PermissionDataConstants.Type.SUBMISSION_TYPE.code);
            if (!ValidationUtils.isNullOrEmpty(oldData)) {
                permissionDataManagementRepository.deleteAll(oldData);
            }
            if (!ValidationUtils.isNullOrEmpty(submissionTypeRequest.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : submissionTypeRequest.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(submissionTypeRequest.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.SUBMISSION_TYPE.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                permissionDataManagementRepository.saveAll(permissionDataManagements);
            }

            submissionTypeRepository.save(submissionType);

            // share user
            shareUserRepository.deleteAllByReferenceIdAndReferenceType(submissionTypeRequest.getId(), ShareUserTypeEnum.SUBMISSION.type);
            if (!ValidationUtils.isNullOrEmpty(submissionTypeRequest.getShareWith())) {
                List<SharedUser> sharedUsers = new ArrayList<>();
                for (String shareWith : submissionTypeRequest.getShareWith()) {
                    SharedUser sharedUser = new SharedUser();
                    sharedUser.setReferenceId(submissionType.getId());
                    sharedUser.setReferenceType(ShareUserTypeEnum.SUBMISSION.type);
                    sharedUser.setEmail(shareWith);
                    sharedUsers.add(sharedUser);
                }
                shareUserRepository.saveAll(sharedUsers);
            }

            return MESSAGE_SUCCESS;

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return MESSAGE_FAIL;
        }
    }

    public String deleteSubmissionType(List<Long> listId) {
        try {
            for (Long idSubmissionType : listId) {
                List<ServicePackage> servicePackages = bpmProcInstRepository.getAllSubmissionTypeOnBpmProcInst(idSubmissionType);
                // Check nếu có trong service package thì ko xóa
                if (!ValidationUtils.isNullOrEmpty(servicePackages)) {
                    return MESSAGE_FAIL;
                } else {
                    submissionTypeRepository.deleteById(idSubmissionType);
                }
            }
            return MESSAGE_SUCCESS;
        } catch (Exception e) {
            log.error("Delete failed");
            return null;
        }
    }


    public String getCode(String email) {
        try {
            String token = credentialHelper.getJWTToken();
//            //String realm = credentialHelper.getRealm();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("key_role", "public");
//            //headers.set("realm", realm);
            headers.set("Authorization", "Bearer " + token);
            HttpEntity<?> requestEntity = new HttpEntity<>(headers);
            String url = UriComponentsBuilder.fromHttpUrl(sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key) + "/userInfo/getCodePriorityByChartId").queryParam("email", email).encode().toUriString();
            ResponseEntity<ResponseDto> responseCustomer = restTemplate.exchange(url,
                    HttpMethod.GET, requestEntity, new ParameterizedTypeReference<>() {
                    });
            ResponseDto responseDto = responseCustomer.getBody();
            String chartDtoList = String.valueOf(responseDto.getData());
            return chartDtoList;
        } catch (Exception e) {
            log.error("Error getCode {}", e.getMessage());
            return null;
        }
    }

    public boolean CheckExistName(String nameSubmissionType) {
        try {
            List<SubmissionType> submissionTypes = submissionTypeRepository.CheckExistName(nameSubmissionType);
            if (submissionTypes.size() > 0) {
                return true;
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public Boolean checkNameExits(String nameSubmissionType, Long id) {
        if (id != null) {
            SubmissionType submissionType = submissionTypeRepository.findById(id).get();
            if (!nameSubmissionType.equalsIgnoreCase(submissionType.getTypeName())) {
                if (CheckExistName(nameSubmissionType)) {
                    return true;
                }
            }

        } else {
            if (CheckExistName(nameSubmissionType)) {
                return true;
            }
        }
        return false;
    }

    public List<String> getAlluserInfo() {
        try {
            List<String> response = customerService.listAllUserInfo();
            return response;
        } catch (Exception e) {
            throw new ErrorMessage("");
        }
    }

    public List<SubmissionTypeDto> getAllSubmissionType() {
        try {
            List<SubmissionTypeDto> submissionType = new ArrayList<>();
            List<SubmissionType> submissionTypes = submissionTypeRepository.findAll();
            if (!CollectionUtils.isEmpty(submissionTypes)) {
                submissionType = submissionTypes.stream().map(listSub -> modelMapper.map(listSub, SubmissionTypeDto.class)).collect(Collectors.toList());
            }
            return submissionType;

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<ChartNodeDto> getChartNodeByChartId(Long chartId) {
        try {
            List<ChartNodeDto> response = customerService.getChartNodeByChartId(chartId);
            return response;
        } catch (Exception e) {
            throw new ErrorMessage("");
        }
    }

    public List<String> getAllSubmissionTypeName() {
        return submissionTypeRepository.getAllSubmissionTypeName();
    }

    public Object getAllSystemGroup()  {
        List<Map<String, Object>> lstResponse = new ArrayList<>();
        SearchSubmissionDto criteria = new SearchSubmissionDto();
        criteria.setPage(1);
        criteria.setLimit(999999);
        criteria.setSortBy("id");
        criteria.setSortType("DESC");

        int pageNum = criteria.getPage() - 1;
        Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());

        String username = credentialHelper.getJWTPayload().getUsername();
        // Lấy list companyCode cấu hình QL vai trò người dùng
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
        // Phân quyền theo nhóm
        List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.SUBMISSION_TYPE.tableName, username);
        // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
        if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
            criteria.setLstGroupPermissionId(lstGroupPermissionId);
            // get list company code role admin member
            List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
            criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
        }

        Page<SubmissionType> page = submissionTypeRepository.findAll(
                submissionTypeSpecification.filter(criteria, lstCompanyCode, username),
                PageRequest.of(pageNum, criteria.getLimit(), sort));
        List<SubmissionType> lstSubmissionType = page.getContent();
        for (SubmissionType submissionType : lstSubmissionType) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", submissionType.getId());
            map.put("name", submissionType.getTypeName());
            map.put("companyCode", submissionType.getCompanyCode());
            map.put("companyName", submissionType.getCompanyName());
            lstResponse.add(map);
        }

        return lstResponse;
    }
}
