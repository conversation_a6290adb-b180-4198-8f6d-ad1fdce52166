package vn.fis.eapprove.business.domain.notification.service;


import vn.fis.eapprove.business.domain.notification.entity.NotificationTemplate;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.NotificationTemplateSearchRequest;
import vn.fis.eapprove.business.model.response.NotificationTemplateResponse;


import java.util.List;

/**
 * Author: AnhVTN
 * Date: 03/01/2023
 */
public interface NotificationTemplateService {
    NotificationTemplate getTemplateByCondition(Long id, String type);

    NotificationTemplate findNotificationTemplateById(Long id);

    public List<NotificationTemplate> findNotificationTemplatesByActionCodeAndType(String actionCode, String type);

    public List<NotificationTemplate> findNotificationTemplatesByActionCodeAndTypeAndNotificationObject(String actionCode, String type, String notificationObject);

    public List<NotificationTemplate> findNotificationTemplatesByTitleAndType(String title, String type);

    PageDto getTemplates(NotificationTemplateSearchRequest request) ;

    List<NotificationTemplateResponse> getTemplatesFilter(NotificationTemplateSearchRequest request) ;

    NotificationTemplate save(NotificationTemplate request);

    void deleteByIds(Long[] ids);

    List<NotificationTemplate> getAllTemplates();

    public Object getAllSystemGroup() ;

}
