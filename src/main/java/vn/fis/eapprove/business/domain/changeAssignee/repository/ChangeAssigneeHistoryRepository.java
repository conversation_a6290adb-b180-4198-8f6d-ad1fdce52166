package vn.fis.eapprove.business.domain.changeAssignee.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.changeAssignee.entity.ChangeAssigneeHistory;


import java.util.List;
import java.util.Optional;

/**
 * Author: PhucVM
 * Date: 22/04/2023
 */
@Repository
public interface ChangeAssigneeHistoryRepository extends JpaRepository<ChangeAssigneeHistory, Long>, JpaSpecificationExecutor<ChangeAssigneeHistory> {

    Optional<ChangeAssigneeHistory> findTopByBpmProcinstIdAndTaskIdOrderByChangeTimeAscIdAsc(@Param("bpmProcinstId") Long bpmProcinstId, @Param("taskId") String taskId);

    ChangeAssigneeHistory findChangeAssigneeHistoryByProcInstIdAndTaskIdAndType(String procInstId, String taskId, Integer type);

    List<ChangeAssigneeHistory> findChangeAssigneeHistoriesByAssignTicketId(Long assignTicketId);

    @Query("SELECT a FROM ChangeAssigneeHistory a "
            + "LEFT JOIN ChangeAssigneeHistory b "
            + "ON a.bpmProcinstId = b.bpmProcinstId "
            + "AND a.taskId = b.taskId "
            + "AND a.changeTime < b.changeTime "
            + "WHERE b.changeTime IS NULL "
            + "AND (:bpmProcinstId IS NULL OR a.bpmProcinstId = :bpmProcinstId) "
            + "AND (:procInstId IS NULL OR a.procInstId = :procInstId) "
            + "AND (:bpmTaskId IS NULL OR a.bpmTaskId = :bpmTaskId) "
            + "AND (:taskId IS NULL OR a.taskId = :taskId)"
            + "AND (:fromAssignee IS NULL OR a.fromAssignee = :fromAssignee) "
            + "AND (:toAssignee IS NULL OR a.toAssignee = :toAssignee)")
    List<ChangeAssigneeHistory> getLatestChangeAssignees(@Param("bpmProcinstId") Long bpmProcinstId,
                                                         @Param("procInstId") String procInstId,
                                                         @Param("bpmTaskId") Long bpmTaskId,
                                                         @Param("taskId") String taskId,
                                                         @Param("fromAssignee") String fromAssignee,
                                                         @Param("toAssignee") String toAssignee);

    @Query("SELECT distinct a.orgAssignee from ChangeAssigneeHistory a " +
            "where a.procInstId = :procInstId " +
            "and a.toAssignee in (:lstToAccounts) " +
            "and a.taskDefKey = :taskDefKey")
    List<String> getListOrgAssigneeByProcInstIdAndTaskDefKeyAndToAssigneeIn(@Param("procInstId") String procInstId, @Param("taskDefKey") String taskDefKey, @Param("lstToAccounts") List<String> lstToAccounts);

    @Query(nativeQuery = true, value = "select cah.org_assignee from change_assignee_history cah where cah.task_id = :taskId ORDER BY cah.change_time ASC\n" +
            "LIMIT 1")
    String getAssigneeOrgByTaskId(@Param("taskId") String taskId);

    List<ChangeAssigneeHistory> findAllByBpmProcinstIdAndTaskIdAndOrgAssignee(@Param("bpmProcinstId") Long bpmProcinstId, @Param("taskId") String taskId, @Param("orgAssignee") String orgAssignee);

    @Query("select distinct ca.orgAssignee from ChangeAssigneeHistory ca where ca.bpmProcinstId = :ticketId")
    List<String> getAllOrgAssigneeByTicketId(Long ticketId);
}
