package vn.fis.eapprove.business.domain.sharedUser.service;

import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.repository.ShareUserRepository;
import vn.fis.eapprove.business.exception.ErrorMessage;

import vn.fis.spro.common.model.request.SharedUserRequest;
import vn.fis.spro.common.model.response.SharedUserResponse;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;


@Service
@Slf4j
@Transactional
public class ShareUserManager {


    @Autowired
    ShareUserRepository shareUserRepository;

    @Autowired
    MessageSource messageSource;

    @Autowired
    ModelMapper modelMapper;

    public void saveAll(SharedUser sharedUser) {

        shareUserRepository.save(sharedUser);
    }

    public List<SharedUser> getAll() {
        try {
            List<SharedUser> sharedUsers = shareUserRepository.findAll().stream()
                    .map(e -> modelMapper.map(e, SharedUser.class)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sharedUsers)) {
                throw new ErrorMessage(messageSource.getMessage("message.location.nameOK", null, Locale.getDefault()));
            }
            return sharedUsers;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public void createSharedUsers(SharedUserRequest request) {

        if (!ValidationUtils.isNullOrEmpty(request.getReferenceId())) {
            List<SharedUser> oldData = shareUserRepository.getSharedUsersByReferenceIdAndReferenceType(request.getReferenceId(), request.getReferenceType());
            if (!ValidationUtils.isNullOrEmpty(oldData)) {
                shareUserRepository.deleteAll(oldData);
            }
        }

        if (!ValidationUtils.isNullOrEmpty(request.getShareWith())) {
            List<SharedUser> sharedUsers = new ArrayList<>();
            for (String shareWith : request.getShareWith()) {
                SharedUser sharedUser = new SharedUser();
                sharedUser.setReferenceId(request.getReferenceId());
                sharedUser.setReferenceType(request.getReferenceType());
                sharedUser.setEmail(shareWith);

                sharedUsers.add(sharedUser);
            }
            shareUserRepository.saveAll(sharedUsers);
        }
    }

    public List<SharedUserResponse> getListSharedUser(SharedUserRequest data) {
        List<SharedUser> lstData = shareUserRepository.getSharedUsersByReferenceTypeAndEmailIn(data.getReferenceType(), data.getShareWith());

        List<SharedUserResponse> lstResponse = new ArrayList<>();
        if (!ValidationUtils.isNullOrEmpty(lstData)) {
            lstResponse = lstData.stream()
                    .collect(Collectors.groupingBy(
                            SharedUser::getReferenceId,
                            Collectors.mapping(SharedUser::getEmail, Collectors.toList())
                    ))
                    .entrySet()
                    .stream()
                    .map(entry -> {
                        SharedUserResponse result = new SharedUserResponse();
                        result.setReferenceId(entry.getKey());
                        result.setShareWith(entry.getValue());
                        return result;
                    })
                    .collect(Collectors.toList());
        }
        return lstResponse;
    }

}
