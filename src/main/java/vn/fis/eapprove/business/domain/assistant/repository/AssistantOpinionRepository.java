package vn.fis.eapprove.business.domain.assistant.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.assistant.entity.AssistantOpinion;


import java.util.List;

/**
 * Author: AnhVTN
 * Date: 01/03/2023
 */

@Repository
public interface AssistantOpinionRepository extends JpaRepository<AssistantOpinion, Long>, JpaSpecificationExecutor<AssistantOpinion> {
    List<AssistantOpinion> getAssistantOpinionByAssistantEmail(String assistantEmail);
}
