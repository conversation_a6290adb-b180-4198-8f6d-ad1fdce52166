package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTempTicket;


@Repository
public interface BpmTempTicketRepository extends JpaRepository<BpmTempTicket, Long>, JpaSpecificationExecutor<BpmTempTicket> {
    BpmTempTicket getBpmTempTicketById(Long id);
}
