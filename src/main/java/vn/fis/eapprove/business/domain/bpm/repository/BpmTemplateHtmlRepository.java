package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplateHtml;

import jakarta.persistence.Tuple;
import java.util.List;

@Repository
public interface BpmTemplateHtmlRepository extends JpaRepository<BpmTemplateHtml, Long>, JpaSpecificationExecutor<BpmTemplateHtml> {
    List<BpmTemplateHtml> getBpmTemplateHtmlsByIdIn(List<Long> ids);

    @Query(value = "select id, name from bpm_template_html", nativeQuery = true)
    List<Tuple> getTupleBpmTemplateHtml();

    BpmTemplateHtml getBpmTemplateHtmlById(Long id);

    @Query("select count(a.id) from BpmTemplateHtml a where a.name = :name and (:id is null or a.id <> :id)")
    Long checkNameDuplicate(String name, Long id);
}
