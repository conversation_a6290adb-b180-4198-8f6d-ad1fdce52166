package vn.fis.eapprove.business.domain.permission.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;


import java.util.List;

@Repository
public interface PermissionDataManagementRepository extends JpaRepository<PermissionDataManagement, Long> {

    List<PermissionDataManagement> getPermissionDataManagementsByTypeIdAndTypeName(Long typeId, String typeName);

    List<PermissionDataManagement> getPermissionDataManagementsByTypeName(String typeName);

    @Query("SELECT pd0 FROM PermissionDataManagement pd0 where pd0.typeName = :typeName AND pd0.typeId in (" +
            "SELECT pd.typeId from PermissionDataManagement pd " +
            "WHERE pd.typeName = :typeName " +
            "AND (pd.companyCode in (:companyCodes) or pd.companyCode = '-1')) ")
    List<PermissionDataManagement> findTypeIdByTypeNameAndCompanyCodes(@Param("typeName") String typeName, @Param("companyCodes") List<String> companyCodes);

    List<PermissionDataManagement> getPermissionDataManagementByTypeId(Long id);

    @Query("select a.companyCode from PermissionDataManagement a where a.typeName = :typeName and a.typeId = :typeId")
    List<String> getApplyForByTypeId(String typeName, Long typeId);

    List<PermissionDataManagement> getPermissionDataManagementsByTypeIdAndTypeNameAndAddition(Long typeId, String typeName, String addition);
}
