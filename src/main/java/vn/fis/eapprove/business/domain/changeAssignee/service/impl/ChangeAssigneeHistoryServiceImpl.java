
package vn.fis.eapprove.business.domain.changeAssignee.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.changeAssignee.entity.ChangeAssigneeHistory;
import vn.fis.eapprove.business.domain.changeAssignee.repository.ChangeAssigneeHistoryRepository;
import vn.fis.eapprove.business.domain.changeAssignee.service.ChangeAssigneeHistoryService;


import java.time.LocalDateTime;
import java.util.List;

/**
 * Author: PhucVM
 * Date: 22/04/2023
 */
@Slf4j
@Service("ChangeAssigneeHistoryServiceImplV1")
@Transactional
public class ChangeAssigneeHistoryServiceImpl implements ChangeAssigneeHistoryService {

    private final ChangeAssigneeHistoryRepository changeAssigneeHistoryRepository;

    @Autowired
    public ChangeAssigneeHistoryServiceImpl(ChangeAssigneeHistoryRepository changeAssigneeHistoryRepository) {
        this.changeAssigneeHistoryRepository = changeAssigneeHistoryRepository;
    }

    @Override
    public ChangeAssigneeHistory save(ChangeAssigneeHistory entity) {
        return changeAssigneeHistoryRepository.save(entity);
    }

    @Override
    public List<ChangeAssigneeHistory> saveAll(List<ChangeAssigneeHistory> entities) {
        return changeAssigneeHistoryRepository.saveAll(entities);
    }

    @Override
    public ChangeAssigneeHistory save(@NonNull BpmProcInst bpmProcInst, @NonNull BpmTask bpmTask, String fromAssignee, String toAssignee, String orgAssigneeTitle, Integer assignType, Long assignTicketId) {
        // get first history record (if have) to get original assignee
        ChangeAssigneeHistory firstHistory = getFirstChangeAssignee(bpmProcInst.getTicketId(), bpmTask.getTaskId());

        ChangeAssigneeHistory entity = new ChangeAssigneeHistory();
        entity.setBpmProcinstId(bpmProcInst.getTicketId());
        entity.setProcInstId(bpmProcInst.getTicketProcInstId());
        entity.setBpmTaskId(bpmTask.getId());
        entity.setTaskId(bpmTask.getTaskId());
        entity.setTaskDefKey(bpmTask.getTaskDefKey());
        entity.setChangeTime(LocalDateTime.now());
        entity.setOrgAssignee(firstHistory != null ? firstHistory.getOrgAssignee() : fromAssignee);
        entity.setFromAssignee(fromAssignee);
        entity.setToAssignee(toAssignee);
        entity.setOrgAssigneeTitle(orgAssigneeTitle);
        entity.setType(assignType);
        entity.setAssignTicketId(assignTicketId);

        return save(entity);
    }

    @Override
    public List<ChangeAssigneeHistory> getLatestChangeAssignees(Long bpmProcinstId, String procInstId, Long bpmTaskId, String taskId, String fromAssignee, String toAssignee) {
        return changeAssigneeHistoryRepository.getLatestChangeAssignees(bpmProcinstId, procInstId, bpmTaskId, taskId, fromAssignee, toAssignee);
    }

    @Override
    public List<ChangeAssigneeHistory> getLatestChangeByToAssignee(Long bpmProcinstId) {
        return getLatestChangeAssignees(bpmProcinstId, null, null, null, null, null);
    }

    @Override
    public List<ChangeAssigneeHistory> getLatestChangeByToAssignee(Long bpmProcinstId, String taskId) {
        return getLatestChangeAssignees(bpmProcinstId, null, null, taskId, null, null);
    }

    @Override
    public ChangeAssigneeHistory getFirstChangeAssignee(Long bpmProcinstId, String taskId) {
        return changeAssigneeHistoryRepository.findTopByBpmProcinstIdAndTaskIdOrderByChangeTimeAscIdAsc(bpmProcinstId, taskId).orElse(null);
    }

    @Override
    public List<ChangeAssigneeHistory> findAllByBpmProcinstIdAndTaskIdAndOrgAssignee(Long bpmProcinstId, String taskId, String orgAssignee) {
        return changeAssigneeHistoryRepository.findAllByBpmProcinstIdAndTaskIdAndOrgAssignee(bpmProcinstId, taskId, orgAssignee);
    }

    @Override
    public void deleteAll(List<ChangeAssigneeHistory> entities) {
        changeAssigneeHistoryRepository.deleteAll(entities);
    }
}
