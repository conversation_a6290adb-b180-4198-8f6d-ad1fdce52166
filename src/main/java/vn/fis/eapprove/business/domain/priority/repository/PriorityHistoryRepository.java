package vn.fis.eapprove.business.domain.priority.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.web.bind.annotation.RestController;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagementHistory;


import java.util.List;

@RestController
public interface PriorityHistoryRepository extends JpaRepository<PriorityManagementHistory, Long>, JpaSpecificationExecutor<PriorityManagementHistory> {
    List<PriorityManagementHistory> findByPriorityId(Long id);

    @Query("SELECT c.slaValue FROM PriorityManagementHistory c WHERE c.priorityHistoryId = :id")
    Double getValueById(Long id);
 }
