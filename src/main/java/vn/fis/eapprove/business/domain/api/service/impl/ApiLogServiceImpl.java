package vn.fis.eapprove.business.domain.api.service.impl;

import jakarta.persistence.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.api.entity.ApiLog;
import vn.fis.eapprove.business.domain.api.repository.ApiLogRepository;
import vn.fis.eapprove.business.domain.api.service.ApiLogService;
import vn.fis.eapprove.business.dto.AdditionApiInfoDto;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.helper.RedisHelper;
import vn.fis.spro.common.util.DateTimeUtils;
import vn.fis.spro.common.util.HttpUtils;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.io.IOException;
import java.util.List;

/**
 * Author: PhucVM
 * Date: 29/11/2022
 */
@Service("ApiLogServiceImplV1")
@Slf4j
@Transactional
public class ApiLogServiceImpl implements ApiLogService {

    private final ApiLogRepository apiLogRepository;
    private final RedisHelper redisHelper;

    @Value("${job.apiLogTask.enable}")
    private boolean apiLogTaskEnable;

    @Autowired
    public ApiLogServiceImpl(ApiLogRepository apiLogRepository,
                             RedisHelper redisHelper) {
        this.apiLogRepository = apiLogRepository;
        this.redisHelper = redisHelper;
    }

    @Override
    public ApiLog save(ApiLog apiLog) {
        return apiLogRepository.save(apiLog);
    }

    @Override
    public List<ApiLog> saveAll(List<ApiLog> apiLogs) {
        return apiLogRepository.saveAll(apiLogs);
    }

    @Override
    public void logging(HttpRequest request, ClientHttpResponse response, String requestBody, long startTime, long endTime) throws IOException {
        ApiLog apiLog = ApiLog.builder()
                .url(request.getURI().toString())
                .method(request.getMethod() != null ? request.getMethod().toString() : null)
                .header(HttpUtils.handleNullableJson(ObjectUtils.toJson(request.getHeaders())))
                .requestBody(HttpUtils.handleInvalidJson(HttpUtils.handleNullableJson(requestBody)))
                .requestTime(DateTimeUtils.milliToLocalDateTime(startTime))
                .responseTime(DateTimeUtils.milliToLocalDateTime(endTime))
                .responseStatus(String.valueOf(response.getStatusCode().value()))
                .responseData(HttpUtils.handleInvalidJson(HttpUtils.handleNullableJson(HttpUtils.getResponseBody(response))))
                .apiType(HttpUtils.getApiType(request.getHeaders()))
                .build();

        setAdditionalInfo(apiLog, request.getHeaders());

        if (!apiLogTaskEnable) return;

        // apiType = TASK_ACTION => save log
        if (!HttpUtils.getApiType(request.getHeaders()).equalsIgnoreCase(CommonConstants.ApiType.TASK_ACTION)) {
            return;
        }

        if (apiLog.isLogAfter() && !ValidationUtils.isNullOrEmpty(apiLog.getProcInstId())) { // save to map and wait for created bpmProcInstId
            redisHelper.enqueue(apiLog.getProcInstId(), apiLog);
        } else {
            redisHelper.enqueue(CommonConstants.QueueName.API_LOG_QUEUE, apiLog);
        }
    }

    @Override
    public void setAdditionalInfo(ApiLog apiLog, HttpHeaders httpHeaders) {
        if (apiLog == null) {
            return;
        }

        String data = HttpUtils.getHeaderValue(httpHeaders, CommonConstants.ApiHeader.ADDITIONAL_INFO);
        if (!ValidationUtils.isNullOrEmpty(data)) {
            AdditionApiInfoDto info = ObjectUtils.toObject(data, AdditionApiInfoDto.class);
            if (info != null) {
                apiLog.setBpmProcdefApiId(info.getBpmProcdefApiId());
                apiLog.setBpmProcinstId(info.getBpmProcinstId());
                apiLog.setLogAfter(info.isLogAfter());
                apiLog.setProcInstId(info.getProcInstId());

                if (!ValidationUtils.isNullOrEmpty(info.getBpmProcdefApiId())) {
                    Tuple apiInfo = apiLogRepository.getApiInfoByBpmProcDefId(info.getBpmProcdefApiId());
                    if (apiInfo != null) {
                        apiLog.setApiName(apiInfo.get("apiName", String.class));
                        apiLog.setTaskDefKey(apiInfo.get("taskDefKey", String.class));
                    }
                }
                if (!ValidationUtils.isNullOrEmpty(info.getBpmProcinstId())) {
                    Tuple ticketInfo = apiLogRepository.getTicketInfoByTicketId(info.getBpmProcinstId());
                    if (ticketInfo != null) {
                        apiLog.setTicketName(ticketInfo.get("ticketName", String.class));
                        apiLog.setRequestCode(ticketInfo.get("requestCode") != null ? ticketInfo.get("requestCode", String.class) : "");
                    }
                }
            }
        }
    }
}
