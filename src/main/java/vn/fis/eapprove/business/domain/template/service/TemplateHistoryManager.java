package vn.fis.eapprove.business.domain.template.service;

import vn.fis.eapprove.security.CredentialHelper;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import vn.fis.eapprove.business.config.GsonAdapterConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.repository.ShareUserRepository;
import vn.fis.eapprove.business.domain.template.entity.TemplateHistory;
import vn.fis.eapprove.business.domain.template.entity.TemplateManage;
import vn.fis.eapprove.business.domain.template.repository.TemplateHistoryRepository;
import vn.fis.eapprove.business.domain.template.repository.TemplateRepository;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.FilterTemplateHistoryRequest;
import vn.fis.eapprove.business.model.response.TemplateHistoryResponse;
import vn.fis.eapprove.business.specification.TemplateHistorySpecification;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ShareUserTypeEnum;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class TemplateHistoryManager {
    @Autowired
    TemplateHistoryRepository templateHistoryRepository;

    @Autowired
    ResponseUtils responseUtils;

    @Autowired
    TemplateManager templateManager;

    @Autowired
    TemplateRepository templateRepository;

    @Autowired
    CredentialHelper credentialHelper;
    @Autowired
    private TemplateHistorySpecification templateHistorySpecification;
    @Autowired
    private Common common;

    @Autowired
    private ShareUserRepository shareUserRepository;
    @Autowired
    private PermissionDataManagementRepository permissionDataManagementRepository;

    public PageDto getAll(FilterTemplateHistoryRequest filterTemplateHistoryRequest) {

        Map<String, Object> mapData = templateHistorySpecification.getAllHistory(filterTemplateHistoryRequest);
        List<TemplateHistoryResponse> result = (List<TemplateHistoryResponse>) mapData.get("data");

        Long totalItems = (Long) mapData.get("count");
        Integer totalPage = common.getPageCount(totalItems, filterTemplateHistoryRequest.getLimit());


        return PageDto.builder()
                .content(result)
                .number(filterTemplateHistoryRequest.getPage())
                .numberOfElements(filterTemplateHistoryRequest.getPage())
                .page(filterTemplateHistoryRequest.getPage())
                .size(filterTemplateHistoryRequest.getLimit())
                .totalPages(totalPage)
                .totalElements(totalItems)
                .build();
    }


    public ResponseEntity<?> restore(Long id) {
        //ID của version
        try {
            TemplateHistory old = templateHistoryRepository.findById(id).orElse(null);
            if (old != null) {
                Boolean check = templateManager.checkExistTemplateNameAndUrl(old.getTemplateId(), old.getUrlName(), old.getTemplateName());
                if (check) {
                    return responseUtils.getResponseEntity(null, -1, "Tồn tại tên biểu mẫu/mã sử dụng đã tồn tại trong hệ thống", HttpStatus.OK);
                }

                Integer newVersion = templateHistoryRepository.findMaxVersion(id);
                TemplateHistory templateHistory = new TemplateHistory().copyWithoutId(old, newVersion);
                Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();

                TemplateManage templateManage = templateRepository.findById(templateHistory.getTemplateId()).orElse(null);
                if (templateManage != null) {
                    templateManage.setTemplate(templateHistory.getTemplate());
                    templateManage.setTemplateName(templateHistory.getTemplateName());
                    templateManage.setUrlName(templateHistory.getUrlName());
                    templateManage.setDescription(templateHistory.getContentEdit());
                    templateManage.setStatus(1);
                    templateManage.setModifiedUser(null);
                    templateManage.setModifiedDate(null);
                    templateManage.setCreatedDate(LocalDateTime.now());
                    templateManage.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    templateManage.setDescription(templateHistory.getDescription());
                    templateRepository.save(templateManage);

                    // Clone permission data
                    List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(templateManage.getId(), PermissionDataConstants.Type.TEMPLATE_MANAGE.code);
                    if (!ValidationUtils.isNullOrEmpty(oldData)) {
                        permissionDataManagementRepository.deleteAllById(oldData.stream().map(PermissionDataManagement::getId).collect(Collectors.toList()));
                    }
                    List<PermissionDataManagement> newData = g.fromJson(templateHistory.getApplyFor(), new TypeToken<List<PermissionDataManagement>>() {
                    }.getType());
                    if (!ValidationUtils.isNullOrEmpty(newData)) {
                        List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                        for (PermissionDataManagement data : newData) {
                            PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                            permissionDataManagement.setTypeId(templateManage.getId());
                            permissionDataManagement.setTypeName(PermissionDataConstants.Type.TEMPLATE_MANAGE.code);
                            permissionDataManagement.setCompanyCode(data.getCompanyCode());
                            permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                            permissionDataManagement.setCreatedTime(LocalDateTime.now());
                            permissionDataManagements.add(permissionDataManagement);
                        }

                        permissionDataManagementRepository.saveAll(permissionDataManagements);
                    }

                    // clone share user
                    List<SharedUser> sharedUserListOld = shareUserRepository.getSharedUsersByReferenceIdAndReferenceType(templateManage.getId(), ShareUserTypeEnum.TEMPLATE.type);
                    if (!ValidationUtils.isNullOrEmpty(sharedUserListOld)) {
                        shareUserRepository.deleteAllById(sharedUserListOld.stream().map(SharedUser::getId).collect(Collectors.toList()));
                    }
                    List<SharedUser> sharedUserListNew = g.fromJson(templateHistory.getShareWith(), new TypeToken<List<SharedUser>>() {
                    }.getType());
                    if (!ValidationUtils.isNullOrEmpty(sharedUserListNew)) {
                        List<SharedUser> sharedUsers = new ArrayList<>();
                        for (SharedUser shareWith : sharedUserListNew) {
                            SharedUser sharedUser = new SharedUser();
                            sharedUser.setReferenceId(templateManage.getId());
                            sharedUser.setReferenceType(ShareUserTypeEnum.TEMPLATE.type);
                            sharedUser.setEmail(shareWith.getEmail());
                            sharedUsers.add(sharedUser);
                        }

                        shareUserRepository.saveAll(sharedUsers);
                    }


                    List<TemplateHistory> list = templateHistoryRepository.findByTemplateId(templateManage.getId());
                    for (TemplateHistory history : list) {
                        history.setStatusHistory(false);
                    }
                    templateHistory.setId(null);
                    templateHistory.setStatusHistory(true);
                    templateHistory.setCreatedUser(credentialHelper.getJWTPayload().getUsername());

                    templateHistoryRepository.save(templateHistory);
                }
            }
            return responseUtils.getResponseEntity(true, 1, "Cập nhật biểu mẫu thành công", HttpStatus.OK);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        }
    }

    public String getTemplateById(Long id) {
        return templateHistoryRepository.getTemplateById(id);
    }
}
