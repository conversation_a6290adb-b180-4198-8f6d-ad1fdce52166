package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcinstLink;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcinstLinkId;


import java.util.List;

/**
 * Author: PhucVM
 * Date: 28/03/2023
 */
@Repository
public interface BpmProcinstLinkRepository extends JpaRepository<BpmProcinstLink, BpmProcinstLinkId>, JpaSpecificationExecutor<BpmProcinstLink> {
    @Modifying(clearAutomatically = true, flushAutomatically = true)
    @Query("DELETE FROM BpmProcinstLink "
            + "WHERE bpmProcinstId = :procInstId ")
    void deleteAllBpmProcinstLinkById(@Param("procInstId") Long procInstId);

    List<BpmProcinstLink> findBpmProcinstLinkByBpmProcinstId(Long bpmProcinstId);

    @Query("SELECT a.bpmProcinstLinkId from BpmProcinstLink  a where a.bpmProcinstId = :bpmProcinstId")
    List<Long> getProcinstLinkByBpmProcinstId(@Param("bpmProcinstId") Long bpmProcinstId);

    @Query("SELECT link.bpmProcinstId FROM BpmProcinstLink link WHERE link.bpmProcinstLinkId = :bpmProcinstLinkId")
    List<Long> getProcinstLinkByBpmProcinstLinkId(@Param("bpmProcinstLinkId") Long bpmProcinstLinkId);
}
