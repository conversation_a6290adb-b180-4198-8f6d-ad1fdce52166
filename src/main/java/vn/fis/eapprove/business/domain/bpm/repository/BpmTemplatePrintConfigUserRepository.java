package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrintConfigUser;


import java.util.List;

public interface BpmTemplatePrintConfigUserRepository extends JpaRepository<BpmTemplatePrintConfigUser, Long>, JpaSpecificationExecutor<BpmTemplatePrintConfigUser> {
    List<BpmTemplatePrintConfigUser> getBpmTemplatePrintConfigUsersByUsernameIn(List<String> usernames);
}
