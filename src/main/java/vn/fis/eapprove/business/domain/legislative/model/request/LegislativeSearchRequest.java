package vn.fis.eapprove.business.domain.legislative.model.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.dto.BaseDto;
import vn.fis.spro.common.model.request.DateFilterDto;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LegislativeSearchRequest extends BaseDto {
    private String type;
    private String activityStatus;
    private String username;

    // filter
    private List<String> listName;
    private List<String> listResponsibleAgency;
    private List<String> listReleaseYear;
    private List<String> listFileName;
    private List<String> listApprovalSession;
    private List<String> listReviewSession;
    private List<String> listStatus;
    private List<String> listRequestCode;
    private List<Integer> listExecutionCount;
    private List<String> listDescription;
    private List<String> listCompanyName;
    private List<String> listCreatedUser;
    private List<String> listUpdatedUser;
    private List<DateFilterDto> listDateFilter;
    private List<String> listChartNodeCode;
}
