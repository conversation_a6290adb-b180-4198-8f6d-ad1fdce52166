package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmServiceProcDef;


import java.util.List;

@Repository
public interface BpmServiceProcDefRepository extends JpaRepository<BpmServiceProcDef, Long> {

    List<BpmServiceProcDef> getBpmServiceProcDefsByServiceIdIn(List<Long> serviceId);

    List<BpmServiceProcDef> getBpmServiceProcDefByProcDefId(Long procDefId);
}
