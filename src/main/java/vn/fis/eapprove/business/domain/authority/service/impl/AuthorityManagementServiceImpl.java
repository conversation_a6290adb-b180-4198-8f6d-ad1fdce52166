package vn.fis.eapprove.business.domain.authority.service.impl;

import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.assign.entity.AssignManagement;
import vn.fis.eapprove.business.domain.assign.repository.AssignRepository;
import vn.fis.eapprove.business.domain.authority.entity.AuthorityManagement;
import vn.fis.eapprove.business.domain.authority.repository.AuthorityManagementRepository;
import vn.fis.eapprove.business.domain.authority.service.AuthorityManagementService;


import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @project business-process-service
 * @created 3/13/2023 - 4:58 PM
 */

@Slf4j
@Service("")
@Transactional
public class AuthorityManagementServiceImpl implements AuthorityManagementService {

    private final AuthorityManagementRepository repo;
    private final AssignRepository assignRepository;
    private final CredentialHelper credentialHelper;

    @Autowired
    public AuthorityManagementServiceImpl(AuthorityManagementRepository repo, AssignRepository assignRepository, CredentialHelper credentialHelper) {
        this.repo = repo;
        this.assignRepository = assignRepository;
        this.credentialHelper = credentialHelper;
    }


    @Override
    public AuthorityManagement saveAuthority(String ticketId, AssignManagement assignManagement, String taskId, String taskDefkey) {
        return repo.save(
                AuthorityManagement
                        .builder()
                        .ticketId(Long.parseLong(ticketId))
                        .type(assignManagement.getEffect())
                        .fromAccount(assignManagement.getAssignUser())
                        .toAccount(assignManagement.getAssignedUser())
                        .taskDefKey(taskDefkey)
                        .taskId(taskId)
                        .userCreate("System")
                        .userUpdate("System")
                        .reason(assignManagement.getAssignName())
                        .createAt(new Date())
                        .updateAt(new Date())
                        .build()
        );
    }

    @Override
    public List<AuthorityManagement> findAuthorityByEmail(String fromAccount, String toAccount) {
        return repo.findAuthorityManagementByFromAccountAndToAccount(fromAccount, toAccount);
    }

    @Override
    public void deleteAuthority(Long id) {
        repo.deleteById(id);
    }

    @Override
    public AuthorityManagement getByTicketId(Long ticketId, Integer type) {
        return repo.getByTicketId(ticketId, type);
    }

    @Override
    public void updateAuthorityById(Long id, String fromAccount, String toAccount, Integer type)  {
        credentialHelper.getJWTPayload().getUsername();
        repo.updateAuthority(id, fromAccount, toAccount, credentialHelper.getJWTPayload().getUsername(), new Date(), type);
    }

    @Override
    public AuthorityManagement saveAuthorityByTask(String ticketId, String fromAccount, String toAccount, String taskId, String taskDefkey)  {
        return repo.save(
                AuthorityManagement
                        .builder()
                        .ticketId(Long.parseLong(ticketId))
                        .type(3)
                        .fromAccount(fromAccount)
                        .toAccount(toAccount)
                        .taskDefKey(taskDefkey)
                        .taskId(taskId)
                        .userCreate(credentialHelper.getJWTPayload().getUsername())
                        .userUpdate(credentialHelper.getJWTPayload().getUsername())
                        .createAt(new Date())
                        .updateAt(new Date())
                        .build()
        );
    }

    @Override
    public AuthorityManagement saveAuthorityByTicket(String ticketId, String fromAccount, String toAccount)  {
        return repo.save(
                AuthorityManagement
                        .builder()
                        .ticketId(Long.parseLong(ticketId))
                        .type(2)
                        .fromAccount(fromAccount)
                        .toAccount(toAccount)
                        .userCreate(credentialHelper.getJWTPayload().getUsername())
                        .userUpdate(credentialHelper.getJWTPayload().getUsername())
                        .createAt(new Date())
                        .updateAt(new Date())
                        .build()
        );
    }


    @Override
    public AuthorityManagement saveAuthorityByAssistance(String ticketId, String fromAccount, String toAccount)  {
        return repo.save(
                AuthorityManagement
                        .builder()
                        .ticketId(Long.parseLong(ticketId))
                        .type(4)
                        .fromAccount(fromAccount)
                        .toAccount(toAccount)
                        .userCreate(credentialHelper.getJWTPayload().getUsername())
                        .userUpdate(credentialHelper.getJWTPayload().getUsername())
                        .createAt(new Date())
                        .updateAt(new Date())
                        .build()
        );
    }
}
