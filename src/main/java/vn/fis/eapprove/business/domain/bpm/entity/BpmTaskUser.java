package vn.fis.eapprove.business.domain.bpm.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;

/**
 * Author: PhucVM
 * Date: 11/11/2022
 */
@Getter
@Setter
@Entity
@Table(name = "bpm_task_user")
public class BpmTaskUser {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "BPM_TASK_ID")
    private Long bpmTaskId;

    @Column(name = "TASK_ID")
    private String taskId;

    @Column(name = "TASK_DEF_KEY")
    private String taskDefKey;

    @Column(name = "TASK_NAME")
    private String taskName;

    @Column(name = "PROC_INST_ID")
    private String procInstId;

    @Column(name = "USER_NAME")
    private String userName;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BPM_TASK_ID", insertable = false, updatable = false)
    @JsonIgnore
    private BpmTask bpmTask;
}
