package vn.fis.eapprove.business.domain.changeAssignee.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import vn.fis.eapprove.business.domain.assign.entity.AssignManagement;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Author: PhucVM
 * Date: 22/04/2023
 */
@Getter
@Setter
@Entity
@Table(name = "change_assignee_history")
public class ChangeAssigneeHistory implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "BPM_PROCINST_ID")
    private Long bpmProcinstId;

    @Column(name = "PROC_INST_ID")
    private String procInstId;

    @Column(name = "BPM_TASK_ID")
    private Long bpmTaskId;

    @Column(name = "TASK_ID")
    private String taskId;

    @Column(name = "TASK_DEF_KEY")
    private String taskDefKey;

    @Column(name = "CHANGE_TIME")
    private LocalDateTime changeTime;

    @Column(name = "ORG_ASSIGNEE")
    private String orgAssignee;

    @Column(name = "FROM_ASSIGNEE")
    private String fromAssignee;

    @Column(name = "TO_ASSIGNEE")
    private String toAssignee;

    @Column(name = "ORG_ASSIGNEE_TITLE")
    private String orgAssigneeTitle;

    @Column(name = "TYPE")
    private Integer type;

    @Column(name = "ASSIGN_TICKET_ID")
    private Long assignTicketId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({
            @JoinColumn(name = "TASK_ID", referencedColumnName = "TASK_ID", insertable = false, updatable = false),
            @JoinColumn(name = "TO_ASSIGNEE", referencedColumnName = "ASSIGNEE", insertable = false, updatable = false)
    })
    @JsonIgnore
    private BpmTask bpmTask;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ASSIGN_TICKET_ID", referencedColumnName = "TICKET_ID", insertable = false, updatable = false)
    @JsonIgnore
    private AssignManagement assignManagement;

}
