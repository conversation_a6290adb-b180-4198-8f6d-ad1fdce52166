package vn.fis.eapprove.business.domain.manageApi.service;

import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.domain.manageApi.entity.ManageAPI;
import vn.fis.eapprove.business.domain.manageApi.repository.ManagerAPIRepository;
import vn.fis.eapprove.business.model.request.BasePageRequest;
import vn.fis.eapprove.business.model.request.ManageApiRequest;
import vn.fis.eapprove.business.model.response.ManageApiResponse;

import vn.fis.eapprove.business.utils.ResponseUtils;

import java.util.Date;

@Service("ManageAPIManagerV1")
public class ManageAPIManager {
    @Autowired
    ResponseUtils responseUtils;

    @Autowired
    ModelMapper modelMapper;

    @Autowired
    ManagerAPIRepository managerAPIRepository;

    public Boolean deleteManageAPI(ManageApiRequest manageApiRequest) {
        try {
            managerAPIRepository.deleteById(manageApiRequest.getId());
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    public Boolean createManageAPI(ManageApiRequest manageApiRequest, String email) {
        try {
            ManageAPI mdt = new ManageAPI();


//           String typeDisplay = new Gson().toJson(masterDataTemplateRequest.getTypeDisplay());
            //List<Map<String,String>> Vacationa = new Gson().fromJson(Test, List.class);

            mdt.setNameAPI(manageApiRequest.getNameAPI());
            mdt.setUrl(manageApiRequest.getUrl());
            mdt.setTicketName(manageApiRequest.getTicketPhase());
            mdt.setDescription(manageApiRequest.getDescription() != null ? manageApiRequest.getDescription() : "");
            mdt.setTicketPhase(manageApiRequest.getTicketPhase());
            mdt.setStatus(manageApiRequest.getStatus());
            mdt.setTicketId(manageApiRequest.getTicketId());


            mdt.setCreatedUser(email);
            mdt.setCreatedDate(new Date());

            managerAPIRepository.save(mdt);

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public ManageAPI updateManageAPI(ManageApiRequest manageApiRequest, String email) {

        try {
            ManageAPI mdf = managerAPIRepository.findById(manageApiRequest.getId()).get();
//            String createdTemplate = new Gson().toJson(templateRequest.getTemplate());

            if (manageApiRequest.getId() != null) {
                mdf.setId(manageApiRequest.getId());
            }

            if (manageApiRequest.getDescription() != null) {
                mdf.setDescription(manageApiRequest.getDescription());
            }
            if (manageApiRequest.getNameAPI() != null) {
                mdf.setNameAPI(manageApiRequest.getNameAPI());
            }
            if (manageApiRequest.getUrl() != null) {
                mdf.setUrl(manageApiRequest.getUrl());
            }
            if (manageApiRequest.getTicketPhase() != null) {
                mdf.setTicketPhase(manageApiRequest.getTicketPhase());
            }
            if (manageApiRequest.getTicketId() != null) {
                mdf.setTicketId(manageApiRequest.getTicketId());
            }
            if (manageApiRequest.getTicketName() != null) {
                mdf.setTicketName(manageApiRequest.getTicketName());
            }
            if (manageApiRequest.getTicketName() != null) {
                mdf.setTicketName(manageApiRequest.getTicketName());
            }
            if (manageApiRequest.getStatus() != null) {
                mdf.setStatus(manageApiRequest.getStatus());
            }


            mdf.setModifiedDate(new Date());
            mdf.setModifiedUser(email);


            managerAPIRepository.save(mdf);

            return mdf;
        } catch (Exception e) {
            return null;
        }
    }

    public Page<ManageApiResponse> getAll(BasePageRequest basePageRequest) {
        try {
            basePageRequest.setPage(basePageRequest.getPage() - 1);
            Sort sort = responseUtils.getSort(basePageRequest.getSortBy(), basePageRequest.getSortType());
            Page<ManageAPI> data = managerAPIRepository.getAll(basePageRequest.getSearch(), PageRequest.of(basePageRequest.getPage(), basePageRequest.getLimit(), sort));
            Page<ManageApiResponse> manageApiResponses = data.map(manageAPI -> {
//            List<Map<String,String>>  createdTemplate = new Gson().fromJson(templateManage.getTemplate(), List.class);
                ManageApiResponse manageApiResponse = new ManageApiResponse();

                manageApiResponse.setId(manageAPI.getId());
                manageApiResponse.setNameAPI(manageAPI.getNameAPI());
                manageApiResponse.setUrl(manageAPI.getUrl());
                manageApiResponse.setTicketPhase(manageAPI.getTicketPhase());
                manageApiResponse.setDescription(manageAPI.getTicketPhase());
                manageApiResponse.setTicketId(manageAPI.getTicketId());
                manageApiResponse.setTicketName(manageAPI.getTicketName());
                manageApiResponse.setStatus(manageAPI.getStatus());
                manageApiResponse.setModifiedDate(manageAPI.getModifiedDate());
                manageApiResponse.setModifiedUser(manageAPI.getModifiedUser());
                manageApiResponse.setCreatedUser(manageAPI.getCreatedUser());
                manageApiResponse.setCreatedDate(manageAPI.getCreatedDate());

                return manageApiResponse;
            });
            return manageApiResponses;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}


