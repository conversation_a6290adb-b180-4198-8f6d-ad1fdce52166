package vn.fis.eapprove.business.domain.permission.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.submission.entity.SubmissionType;
import vn.fis.eapprove.business.domain.template.entity.TemplateManage;
import vn.fis.eapprove.business.domain.api.entity.ApiManagement;
import vn.fis.eapprove.business.domain.assign.entity.AssignManagement;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrint;
import vn.fis.eapprove.business.domain.codeGen.entity.CodeGenConfig;
import vn.fis.eapprove.business.domain.evaluation.entity.EvaluationCriteria;
import vn.fis.eapprove.business.domain.location.entity.LocationManagement;
import vn.fis.eapprove.business.domain.manageApi.entity.ManageShareTicket;
import vn.fis.eapprove.business.domain.notification.entity.NotificationTemplate;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * Author: KienPN
 * Date: 07/09/2023
 */
@Getter
@Setter
@Entity
@Table(name = "permission_data_management")
public class PermissionDataManagement {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "TYPE_ID")
    private Long typeId;

    @Column(name = "TYPE_NAME")
    private String typeName;

    @Column(name = "COMPANY_CODE")
    private String companyCode;

    @Column(name = "CREATED_USER")
    private String createdUser;

    @Column(name = "CREATED_TIME")
    private LocalDateTime createdTime;

    @Column(name = "ADDITION")
    private String addition;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TYPE_ID", insertable = false, updatable = false)
    @JsonIgnore
    private TemplateManage templateManage;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TYPE_ID", insertable = false, updatable = false)
    @JsonIgnore
    private BpmProcdef bpmProcdef;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TYPE_ID", insertable = false, updatable = false)
    @JsonIgnore
    private BpmTemplatePrint bpmTemplatePrint;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TYPE_ID", insertable = false, updatable = false)
    @JsonIgnore
    private ServicePackage servicePackage;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TYPE_ID", insertable = false, updatable = false)
    @JsonIgnore
    private LocationManagement locationManagement;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TYPE_ID", insertable = false, updatable = false)
    @JsonIgnore
    private PriorityManagement priorityManagement;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TYPE_ID", insertable = false, updatable = false)
    @JsonIgnore
    private NotificationTemplate notificationTemplate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TYPE_ID", insertable = false, updatable = false)
    @JsonIgnore
    private ApiManagement apiManagement;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TYPE_ID", insertable = false, updatable = false)
    @JsonIgnore
    private CodeGenConfig codeGenConfig;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TYPE_ID", insertable = false, updatable = false)
    @JsonIgnore
    private SubmissionType submissionType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TYPE_ID", insertable = false, updatable = false)
    @JsonIgnore
    private EvaluationCriteria evaluationCriteria;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TYPE_ID", insertable = false, updatable = false)
    @JsonIgnore
    private AssignManagement assignManagement;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TYPE_ID", insertable = false, updatable = false)
    @JsonIgnore
    private ManageShareTicket manageShareTicket;

}
