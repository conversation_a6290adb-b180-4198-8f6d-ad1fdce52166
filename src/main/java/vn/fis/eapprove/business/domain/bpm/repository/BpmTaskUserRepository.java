package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTaskUser;


import java.util.List;
import java.util.Set;

/**
 * Author: PhucVM
 * Date: 23/11/2022
 */
@Repository
public interface BpmTaskUserRepository extends JpaRepository<BpmTaskUser, Long> {

    @Modifying
    @Query("UPDATE BpmTaskUser SET userName = :newUserName "
            + "WHERE procInstId = :procInstId AND taskId = :taskId AND userName = :oldUserName")
    void updateNewUserName(@Param("procInstId") String procInstId,
                           @Param("taskId") String taskId,
                           @Param("oldUserName") String oldUserName,
                           @Param("newUserName") String newUserName);

    @Modifying
    @Query("DELETE FROM BpmTaskUser WHERE taskId IN (:taskIds)")
    void deleteAllByTaskIds(@Param("taskIds") List<String> taskIds);

    @Modifying
    @Query("DELETE FROM BpmTaskUser WHERE procInstId = :procInstId AND taskDefKey IN (:taskDefKeys)")
    void deleteAllByTaskDefKeys(@Param("procInstId") String procInstId, @Param("taskDefKeys") Set<String> taskDefKeys);

    List<BpmTaskUser> getBpmTaskUserByTaskIdIn(List<String> listId);

    List<BpmTaskUser> getBpmTaskUsersByUserName(String userName);

    @Query("SELECT userName FROM BpmTaskUser WHERE procInstId = :procInstId AND (:taskDefKey IS NULL OR taskDefKey = :taskDefKey)")
    List<String> getAllUserName(@Param("procInstId") String procInstId,
                                @Param("taskDefKey") String taskDefKey);

    List<BpmTaskUser> getAllByProcInstId(@Param("procInstId") String procInstId);
}
