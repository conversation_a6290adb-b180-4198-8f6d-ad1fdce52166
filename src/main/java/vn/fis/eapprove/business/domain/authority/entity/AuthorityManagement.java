package vn.fis.eapprove.business.domain.authority.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @project business-process-service
 * @created 3/13/2023 - 4:44 PM
 */
@Getter
@Setter
@ToString
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "authority_management")
public class AuthorityManagement implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    @Column(name = "ticket_id")
    private Long ticketId;
    @Column(name = "from_account")
    private String fromAccount;
    @Column(name = "to_account")
    private String toAccount;
    @Column(name = "reason")
    private String reason;
    @Column(name = "task_id")
    private String taskId;
    @Column(name = "task_def_key")
    private String taskDefKey;
    @Column(name = "type")
    private Integer type;
    @Column(name = "user_create")
    private String userCreate;
    @Column(name = "user_update")
    private String userUpdate;
    @Column(name = "create_at")
    private Date createAt;
    @Column(name = "update_at")
    private Date updateAt;

    @Column(name = "origin_user")
    private String originUser;
    @Transient
    private String formatedCreateAt;
    @Transient
    private String formatedUpdateAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ticket_id", referencedColumnName = "ID", insertable = false, updatable = false)
    @JsonIgnore
    private BpmProcInst bpmProcInst;

//    @OneToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "task_id", referencedColumnName = "task_id", insertable = false, updatable = false)
//    @JsonIgnore
//    private BpmTask bpmTask;
}
