package vn.fis.eapprove.business.domain.notification.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.bpm.entity.BpmNotifyUser;
import vn.fis.eapprove.business.domain.notification.entity.Notification;
import vn.fis.eapprove.business.domain.notification.repository.NotificationRepository;
import vn.fis.eapprove.business.domain.notification.service.NotificationService;
import vn.fis.eapprove.business.model.request.NotificationRequest;


import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: AnhVTN
 * Date: 03/01/2023
 */
@Service
@Slf4j
@Transactional
public class NotificationServiceImpl implements NotificationService {

    @Value("${push-noti.url-noti-proxy}")
    private String url;

    private final NotificationRepository notificationRepository;

    @Autowired
    public NotificationServiceImpl(NotificationRepository notificationRepository) {
        this.notificationRepository = notificationRepository;
    }

    @Override
    public void saveBatch(List<Notification> notifications) {
        this.notificationRepository.saveAll(notifications);
    }

    @Override
    public void pushNotification(List<BpmNotifyUser> bpmNotifyUsers) {
        List<NotificationRequest> notificationRequests = from(bpmNotifyUsers);
        try {
            ObjectMapper mapper = new ObjectMapper();
            String inputJson = mapper.writeValueAsString(notificationRequests);
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(inputJson))
                    .build();
            HttpClient client = HttpClient.newHttpClient();
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            int statusCode = response.statusCode();
            if (statusCode == 200) {
                log.info("Notification sent successfully.");
            } else {
                log.info("Notification sending failed. Status code: {}", statusCode);
            }
        } catch (Exception e) {
            log.error("Notification sending error.", e);
        }
    }

    private List<NotificationRequest> from(List<BpmNotifyUser> bpmNotifyUsers) {
        List<NotificationRequest> notificationRequests = new ArrayList<>();
        for (BpmNotifyUser bpmNotifyUser : bpmNotifyUsers) {
            String message = bpmNotifyUser.getMessage();
            Map<String, Object> payload = new HashMap<>();
            payload.put("title", bpmNotifyUser.getTitle());
            payload.put("body", message);
            Map<Object, Object> extraInfo = new HashMap<>();
            extraInfo.put("isPersistence", true);
            extraInfo.put("isWeb", true);

            // web socket
            NotificationRequest notificationRequest = new NotificationRequest();
            notificationRequest.setReceiver(bpmNotifyUser.getRecipient());
            notificationRequest.setType("socket");
            notificationRequest.setPayload(payload);
            notificationRequest.setExtraInfo(extraInfo);
            notificationRequest.setTitle(bpmNotifyUser.getTitle());
            notificationRequests.add(notificationRequest);

            // mobile
            NotificationRequest mobileRequest = new NotificationRequest();
            mobileRequest.setReceiver(bpmNotifyUser.getRecipient());
            mobileRequest.setType("mobile");
            mobileRequest.setPayload(payload);
            mobileRequest.setExtraInfo(extraInfo);
            mobileRequest.setTitle(bpmNotifyUser.getTitle());
            notificationRequests.add(mobileRequest);
        }
        return notificationRequests;
    }
}
