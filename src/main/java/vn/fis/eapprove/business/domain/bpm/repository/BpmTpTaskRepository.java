package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTpTask;


import java.util.List;

@Repository
public interface BpmTpTaskRepository extends JpaRepository<BpmTpTask, Long>, JpaSpecificationExecutor<BpmTpTask> {
    List<BpmTpTask> findBpmTpTaskByTaskDefKeyAndProcDefId(String taskDefKey, String procDefId);

    List<BpmTpTask> findByTaskDefKeyAndProcDefIdAndStatus(String taskDefKey, String procDefId, String status);

    List<BpmTpTask> findBpmTpTaskByBpmTemplatePrintId(Long id);

    List<BpmTpTask> findAllByBpmTemplatePrintIdIn(List<Long> ids);

    @Query("SELECT b FROM BpmTpTask b \n" +
            "JOIN BpmTemplatePrint d ON b.bpmTemplatePrintId = d.id\n" +
            "WHERE d.isDeleted = false and b.taskDefKey = :taskDefKey and b.procDefId =:procDefId and b.status = :status")
    List<BpmTpTask> findUsed(String taskDefKey, String procDefId, String status);

    void deleteByBpmTemplatePrintId(Long id);

    void deleteAllByBpmTemplatePrintId(Long id);

    List<BpmTpTask> findByBpmTemplatePrintId(Long id);

    @Query("SELECT bt from BpmTpTask bt " +
            "join BpmTemplatePrint d ON bt.bpmTemplatePrintId = d.id " +
            "where bt.bpmTemplatePrintId = :id and bt.procDefId = :procDefId and d.printType = 0")
    List<BpmTpTask> getByTemplatePrintIdAndProcDefId(Long id, String procDefId);
}
