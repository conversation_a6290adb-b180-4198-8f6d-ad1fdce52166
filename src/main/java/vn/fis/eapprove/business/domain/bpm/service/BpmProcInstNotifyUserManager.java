package vn.fis.eapprove.business.domain.bpm.service;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProInstNotifyUser;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcInstNotifyUserRepository;


import java.util.List;

@Service("BpmProcInstNotifyUserManagerV1")
@Slf4j
@Transactional
public class BpmProcInstNotifyUserManager {


    @Autowired
    private BpmProcInstNotifyUserRepository notifyUserRepository;

    public void saveAll(List<BpmProInstNotifyUser> bpmProInstNotifyUsers) {
        notifyUserRepository.saveAll(bpmProInstNotifyUsers);
    }

    public List<BpmProInstNotifyUser> getDetail(Long ticketId) {
        return notifyUserRepository.getBpmProInstNotifyUsersBy(ticketId);
    }

    public void deleteAllByBpmProcinstId(Long ticketId) {
        notifyUserRepository.deleteAllByBpmProcinstId(ticketId);
    }

    public List<BpmProInstNotifyUser> findByBpmProcinstId(Long bpmProcinstId) {
        return notifyUserRepository.findByBpmProcinstId(bpmProcinstId);
    }

    public List<BpmProInstNotifyUser> getBpmProInstNotifyUsersBy(Long ticketId) {
        return notifyUserRepository.getBpmProInstNotifyUsersBy(ticketId);
    }

}

