package vn.fis.eapprove.business.domain.bpm.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import vn.fis.eapprove.security.CredentialHelper;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.fis.eapprove.business.constant.Constant;
import vn.fis.eapprove.business.domain.api.entity.ApiLog;
import vn.fis.eapprove.business.domain.api.entity.ApiManagement;
import vn.fis.eapprove.business.domain.api.repository.ApiManagementRepository;
import vn.fis.eapprove.business.domain.api.service.ApiLogService;
import vn.fis.eapprove.business.domain.bpm.service.BpmService;
import vn.fis.eapprove.business.dto.AdditionApiInfoDto;
import vn.fis.eapprove.business.interceptor.RestTemplateHeaderInterceptor;
import vn.fis.eapprove.business.model.request.StartProcessInstanceDto;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.MapKeyEnum;
import vn.fis.spro.common.helper.RedisHelper;
import vn.fis.spro.common.helper.RestHelper;
import vn.fis.spro.common.util.HttpUtils;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: AnhVTN
 * Date: 15/03/2023
 */

@Service("BpmServiceImplV1")
@Slf4j
public class BpmServiceImpl implements BpmService {
    private final RestHelper restHelper;
    private final CredentialHelper credentialHelper;
    private final SproProperties sproProperties;
    private final ApiManagementRepository apiManagementRepository;
    private String customerHost;

    @Value("${app.superAdmin.username}")
    private String usernameBasicAuth;
    @Value("${app.superAdmin.password}")
    private String password;
    @Value("${app.superAdmin.realm}")
    private String realm;

    @Value("${app.superAdmin.account}")
    private String appSuperAdminAccount;

    private final RedisHelper redisHelper;

    public BpmServiceImpl(RestHelper restHelper, CredentialHelper credentialHelper, SproProperties sproProperties, ApiManagementRepository apiManagementRepository, RedisHelper redisHelper) {
        this.restHelper = restHelper;
        this.credentialHelper = credentialHelper;
        this.sproProperties = sproProperties;
        this.apiManagementRepository = apiManagementRepository;
        this.redisHelper = redisHelper;
    }

    @PostConstruct
    public void init()  {
        customerHost = sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key);
        if (customerHost != null && customerHost.endsWith(CommonConstants.PATH_SEPARATOR)) {
            customerHost = customerHost.substring(0, customerHost.length() - 1);
        }


    }

    private String getFullUrlCustomer(String uri) {
        return customerHost + uri;
    }

    private long getEndTimeInMs(long startTime) {
        return System.currentTimeMillis() - startTime;
    }

    @Override
    public HttpHeaders getHeaders()  {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("key_role", Constant.KEY_ROLE_PUBLIC);

        try {
            //headers.set("realm", credentialHelper.getRealm());
            String token = credentialHelper.getJWTToken();
            if (!ValidationUtils.isNullOrEmpty(token)) {
                headers.setBearerAuth(token);
            }
        } catch (Exception e) {
            //headers.set("realm", realm);
            headers.setBasicAuth(usernameBasicAuth, password);
        }

        return headers;
    }

    public Map<String, Object> findSystemConfigByCodeAndName(String code, String name) {
        long startTime = System.currentTimeMillis();
        log.info("Starting findSystemConfigByCodeAndName ");

        String uri = "/systemConfig/findByCodeAndName?code=" + code + "&name=" + name;
        log.info("findSystemConfigByCodeAndName URL", uri);
        try {
            Map<String, Object> params = new HashMap<>();

            Map<String, Object> responseData = restHelper.getAndGetBody(
                    getFullUrlCustomer(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    }
            );
            log.info("findSystemConfigByCodeAndName responseData", responseData);
            if (responseData != null) {
                return responseData;
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {

            log.info("FINISH findSystemConfigByCodeAndName", getEndTimeInMs(startTime));
        }
        return null;
    }

    @Override
    public Map<String, Object> callBpmWithAuthen(Long actionApiId)  { // Call api từ api management
        long startTime = System.currentTimeMillis();
        ApiManagement apiManagement = apiManagementRepository.findById(actionApiId).orElse(null);
        ApiManagement authenApi = null;
        ApiManagement baseUrlApi = null;
        if (apiManagement != null && apiManagement.getType().equalsIgnoreCase("NORMAL")) {
            if (apiManagement.getAuthenApiId() != null)
                authenApi = apiManagementRepository.findById(apiManagement.getAuthenApiId()).orElse(null);
            if (apiManagement.getBaseUrlId() != null)
                baseUrlApi = apiManagementRepository.findById(apiManagement.getBaseUrlId()).orElse(null);
            String url = "";
            HttpHeaders httpHeaders = getHeaders();
            if (authenApi != null) { // Add authen vào header
                AdditionApiInfoDto additionApiInfoDto = new AdditionApiInfoDto();
                additionApiInfoDto.setAuthenApiId(apiManagement.getAuthenApiId());
                additionApiInfoDto.setAuthenUrl(authenApi.getUrl());
                additionApiInfoDto.setAuthenHeader(authenApi.getHeader());
                additionApiInfoDto.setAuthenMethod(authenApi.getMethod());
                additionApiInfoDto.setAuthenBody(authenApi.getBody());
                additionApiInfoDto.setTokenAttribute(authenApi.getTokenAttribute());
                httpHeaders.add(CommonConstants.ApiHeader.ADDITIONAL_INFO, ObjectUtils.toJson(additionApiInfoDto));
            }
            if (baseUrlApi != null) {
                url = baseUrlApi.getUrl() + apiManagement.getUrl();
            }
            Map<String, Object> headerMap = ObjectUtils.toObject(apiManagement.getHeader(), new TypeReference<>() {
            });
            Map<String, Object> params = ObjectUtils.toObject(apiManagement.getBody(), new TypeReference<>() {
            });

            if (!ValidationUtils.isNullOrEmpty(headerMap)) {
                headerMap.forEach((key, val) -> {
                    if (val instanceof List) {
                        httpHeaders.set(key, String.join(",", (List) val));
                    } else if (val != null) {
                        httpHeaders.set(key, val.toString());
                    } else httpHeaders.set(key, null);
                });
            }

            Map<String, Object> mapResponse = null;

            log.info("Starting call bpm-service-authen url: {}", url);
            try {
                if (apiManagement.getMethod().equalsIgnoreCase("POST")) {
                    mapResponse = restHelper.postAndGetBody(
                            url,
                            httpHeaders,
                            params,
                            new ParameterizedTypeReference<>() {
                            });

                    return mapResponse;
                } else {
                    mapResponse = restHelper.getAndGetBody(
                            url,
                            httpHeaders,
                            new ParameterizedTypeReference<>() {
                            });

                    return mapResponse;
                }
            } catch (Exception e) {
                log.error("call bpm-service-authen false : " + e.getMessage(), e);
            } finally {
                log.info("Finished call bpm-service in {} ms", getEndTimeInMs(startTime));
            }
        }

        return null;
    }

    @Override
    public List<Map<String, Object>> loadChartFilter() {
        long startTime = System.currentTimeMillis();
        List<Map<String, Object>> mapResponse = null;
        try {
            HttpHeaders httpHeaders = getHeaders();
            String url = "/chart/loadChartFilter";
            log.info("Starting call bpm-service url: {}", url);
            Map<String, Object> param = new HashMap<>();

            mapResponse = restHelper.postAndGetBody(
                    getFullUrlCustomer(url),
                    httpHeaders,
                    param,
                    new ParameterizedTypeReference<>() {
                    });

            return mapResponse;
        } catch (Exception e) {
            log.error(e.getMessage(), e);

        } finally {
            log.info("Finished call bpm-service in {} ms", getEndTimeInMs(startTime));
            return mapResponse;
        }
    }

    @Override
    public List<Map<String, Object>> loadChartNodeFilter() {
        long startTime = System.currentTimeMillis();

        List<Map<String, Object>> mapResponse = null;
        String url = "/chart-node/loadChartNodeFilter";
        log.info("Starting call bpm-service url: {}", url);
        try {
            HttpHeaders httpHeaders = getHeaders();

            mapResponse = restHelper.getAndGetBody(
                    getFullUrlCustomer(url),
                    httpHeaders,
                    new ParameterizedTypeReference<>() {
                    });

            return mapResponse;
        } catch (Exception e) {
            log.error(e.getMessage(), e);

        } finally {
            log.info("Finished call bpm-service in {} ms", getEndTimeInMs(startTime));
            return mapResponse;
        }
    }

    @Override
    public Object callBpm(String url, String method, Map<String, Object> params, Map<String, Object> headers, Boolean isSaveLog)  {
        if (!ValidationUtils.isNullOrEmpty(headers) && !headers.containsKey("username"))
            headers.put("username", credentialHelper.getJWTPayload().getUsername());
        long startTime = System.currentTimeMillis();
        Object mapResponse = null;

        String responseStatus = "SUCCESS";
        log.info("Starting call bpm-service url: {}", url);
        try {
            HttpHeaders httpHeaders = getHeaders();
            // Iterate through the map and retrieve the key-value pairs
            for (Map.Entry<String, Object> entry : headers.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                httpHeaders.set(key, value.toString());
            }

            if (method.equals("POST")) {
                mapResponse = restHelper.postAndGetBody(
                        url,
                        httpHeaders,
                        params,
                        new ParameterizedTypeReference<>() {
                        });

                return mapResponse;
            } else {
                mapResponse = restHelper.getAndGetBody(
                        url,
                        httpHeaders,
                        new ParameterizedTypeReference<>() {
                        });

                return mapResponse;
            }
        } catch (Exception e) {
            responseStatus = "ERROR";
            log.error("call bpm-service error, url: {}, {}", url, e.getMessage());
        } finally {
            if (isSaveLog) {
                ApiLog apiLog = ApiLog.builder()
                        .url(url)
                        .method(method)
                        .header(HttpUtils.handleNullableJson(ObjectUtils.toJson(headers)))
                        .requestBody(HttpUtils.handleInvalidJson(HttpUtils.handleNullableJson(ObjectUtils.toJson(params))))
                        .requestTime(LocalDateTime.now())
                        .responseTime(LocalDateTime.now())
                        .responseStatus(responseStatus)
                        .responseData(HttpUtils.handleNullableJson(ObjectUtils.toJson(mapResponse)))
                        .apiType(CommonConstants.ApiType.TASK_ACTION)
                        .build();
                redisHelper.enqueue(CommonConstants.QueueName.API_LOG_QUEUE, apiLog);
            }
            log.info("Finished call bpm-service in {} ms", getEndTimeInMs(startTime));
        }
        return null;
    }

    @Override
    public Object callBpmSubSystem(String url,
                                   String method,
                                   Map<String, Object> params,
                                   Map<String, Object> headers,
                                   String subSystem,
                                   Boolean isSaveLog,
                                   Map<String, Object> variables,
                                   Boolean isReturnResponeEntity)  {
        if (headers != null  && !headers.containsKey("username")) {
            try {
                headers.put("username", credentialHelper.getJWTPayload().getUsername());
            } catch (Exception e) {
                headers.put("username", appSuperAdminAccount);
            }
        }
        long startTime = System.currentTimeMillis();
        Object mapResponse = null;

        String responseStatus = "SUCCESS";
        log.info("Starting call bpm-service url: {}", url);
        try {
            HttpHeaders httpHeaders = getHeaders();
            // Iterate through the map and retrieve the key-value pairs
            for (Map.Entry<String, Object> entry : headers.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                httpHeaders.set(key, value.toString());
            }

            if (isReturnResponeEntity) { // Quy trình call API
                mapResponse = restHelper.exchange(url,
                        HttpMethod.valueOf(method.toUpperCase()),
                        httpHeaders,
                        params,
                        new ParameterizedTypeReference<>() {
                        },
                        variables);
                return mapResponse;
            } else {
                if (method.equals("POST")) { //Biểu mẫu call API
                    mapResponse = restHelper.postAndGetBody(
                            url,
                            httpHeaders,
                            params,
                            new ParameterizedTypeReference<>() {
                            });

                    return mapResponse;
                } else {
                    mapResponse = restHelper.getAndGetBody(
                            url,
                            httpHeaders,
                            new ParameterizedTypeReference<>() {
                            });

                    return mapResponse;
                }
            }
        } catch (Exception e) {
            responseStatus = "ERROR";
            log.error(e.getMessage(), e);
        } finally {
            if (isSaveLog) {
                ApiLog apiLog = ApiLog.builder()
                        .url(url)
                        .method(method)
                        .header(HttpUtils.handleNullableJson(ObjectUtils.toJson(headers)))
                        .requestBody(HttpUtils.handleInvalidJson(HttpUtils.handleNullableJson(ObjectUtils.toJson(params))))
                        .requestTime(LocalDateTime.now())
                        .responseTime(LocalDateTime.now())
                        .responseStatus(responseStatus)
                        .responseData(HttpUtils.handleNullableJson(ObjectUtils.toJson(mapResponse)))
                        .apiType(CommonConstants.ApiType.TASK_ACTION)
                        .build();
                redisHelper.enqueue(CommonConstants.QueueName.API_LOG_QUEUE, apiLog);
            }
            log.info("Finished call bpm-service in {} ms", getEndTimeInMs(startTime));
        }
        return null;
    }

//    public void updateCallSubSystemFromDataBase(CallServiceProperties newProperties)  {
//        Base64.Encoder encoder = Base64.getEncoder();
//        String basicAuth = usernameBasicAuth + ":" + password;
//        Map<String, Object> headersMap = new HashMap<>();
//        headersMap.put(HttpHeaders.AUTHORIZATION, encoder.encodeToString(basicAuth.getBytes()));
//
//        Object response = redisHelper.get("HOST", "GET_HOST_DATA", Object.class);
//        if (ValidationUtils.isNullOrEmpty(response)) {
//            Map<String, Object> systemMap = createSystemMap();
//            response = callBpm(customerHost + "/systemConfig/searchSystemConfig", "POST", systemMap, headersMap, false);
//        }
//
//        if (response instanceof Map) {
//            Map<String, Object> mapRes = (Map<String, Object>) response;
//            if (mapRes != null && mapRes.containsKey("data")) {
//                Object dataObj = mapRes.get("data");
//                if (dataObj instanceof Map) {
//                    Map<String, Object> data = (Map<String, Object>) dataObj;
//                    if (data.containsKey("content")) {
//                        List<Map<String, Object>> allData = (List<Map<String, Object>>) data.get("content");
//                        updateCallServiceProperties(allData, newProperties);
//                        redisHelper.put("HOST", "GET_HOST_DATA", response);
//                    }
//                }
//            }
//        }
//    }

//    private Map<String, Object> createSystemMap() {
//        Map<String, Object> systemMap = new HashMap<>();
//        systemMap.put("type", "system");
//        systemMap.put("serverType", "host");
//        systemMap.put("page", 1);
//        systemMap.put("limit", 10);
//        systemMap.put("sortBy", "id");
//        systemMap.put("sortType", "DESC");
//        systemMap.put("listServerType", new String[]{"host"});
//        systemMap.put("isDefault", true);
//        return systemMap;
//    }

//    private void updateCallServiceProperties(List<Map<String, Object>> allData, CallServiceProperties newProperties) {
//        for (Map<String, Object> i : allData) {
//            String configCode = (String) i.getOrDefault("configCode", "");
//            String configValue = (String) i.getOrDefault("configValue", "");
//            if (!ValidationUtils.isNullOrEmpty(configValue)) {
//                if (configCode.equals(SystemConfigConstants.DefaultConfig.HOST_CONS.code)) {
//                    newProperties.getCons().setUrl(configValue);
//                } else if (configCode.equals(SystemConfigConstants.DefaultConfig.HOST_IHRP.code)) {
//                    newProperties.getIhrp().setUrl(configValue);
//                } else if (configCode.equals(SystemConfigConstants.DefaultConfig.HOST_ADMIN.code)) {
//                    newProperties.getAdmin().setUrl(configValue);
//                } else if (configCode.equals(SystemConfigConstants.DefaultConfig.HOST_SAP.code)) {
//                    newProperties.getSap().setUrl(configValue);
//                }
//            }
//        }
//    }

//    public String getHeaderSubSystemAndReturnNewUrl(String subSystem, Map<String, Object> headers, String url) throws IllegalAccessException, VerificationException {
//        if (!ValidationUtils.isNullOrEmpty(headers) && !headers.containsKey("username"))
//            headers.put("username", credentialHelper.getJWTPayload().getUsername());
//        //Get Host subsystem from DB
//        CallServiceProperties newProperties = callServiceProperties;
//        updateCallSubSystemFromDataBase(newProperties);
//        Field[] fields = newProperties.getClass().getDeclaredFields();
//        for (Field field : fields) {
//            field.setAccessible(true);
//            if (field.getName().equals(subSystem.toLowerCase())) {
//                ConfigCallServiceProperties value = (ConfigCallServiceProperties) field.get(newProperties);
//                if (!ValidationUtils.isNullOrEmpty(value.getAccount()) && !ValidationUtils.isNullOrEmpty(value.getPassword())) {
//                    Base64.Encoder encoder = Base64.getEncoder();
//                    String basicAuthen = value.getAccount() + ":" + value.getPassword();
//                    String encodedString = encoder.encodeToString(basicAuthen.getBytes());
//                    headers.put(HttpHeaders.AUTHORIZATION, "Basic " + encodedString);
//                }
//                if (!ValidationUtils.isNullOrEmpty(value.getCookie())) {
//                    headers.put(HttpHeaders.COOKIE, value.getCookie());
//                }
//                if (!ValidationUtils.isNullOrEmpty(value.getXApiKey())) {
//                    headers.put("x-api-key", value.getXApiKey());
//                }
//
//                //Xử lý lấy đúng URL
//                String regex = ".+call-service-" + subSystem.toLowerCase();
//                url = url.replaceAll(regex, value.getUrl());
//
//            }
//        }
//        return url;
//    }

//    @Override
//    public void callSaveLogAdmin(String uuid, Object request, Object response, LocalDateTime processDate, String masterType, int status, String description) {
//        try {
//            log.info("Entering callSaveLogAdmin()...");
//            ObjectMapper mapper = new ObjectMapper();
//            Map<String, Object> messageHeader = new HashMap<>();
//            messageHeader.put("sender", "EAP");
//            messageHeader.put("timestamp", Instant.now().getEpochSecond());
//            messageHeader.put("messageId", uuid);
//
//            Map<String, Object> requestBody = new HashMap<>();
//            requestBody.put("request", mapper.writer().writeValueAsString(request));
//            requestBody.put("response", mapper.writer().writeValueAsString(response));
//
//            requestBody.put("processDate", processDate.format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")));
//            requestBody.put("description", description);
//            requestBody.put("status", status);
//            requestBody.put("receiver", null);
//            requestBody.put("masterType", masterType);
//
//            Map<String, Object> params = new HashMap<>();
//            params.put("messageHeader", messageHeader);
//            params.put("requestBody", requestBody);
//
//            callSaveLogAdmin(params, uuid);
//        } catch (Exception ex) {
//            log.error("Save log admin false {}", ex.getMessage());
//        }
//    }

    @Override
    public List<Map<String, Object>> findCompanyCodeByUsername(String username) {
        long startTime = System.currentTimeMillis();
        List<Map<String, Object>> mapResponse = null;
        String url = "/chart/findCompanyCodeByUsername?username=" + username;
        log.info("Starting call bpm-service url: {}", url);
        try {
            HttpHeaders httpHeaders = getHeaders();

            mapResponse = restHelper.getAndGetBody(
                    getFullUrlCustomer(url),
                    httpHeaders,
                    new ParameterizedTypeReference<>() {
                    });

            return mapResponse;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            log.info("Finished call bpm-service in {} ms", getEndTimeInMs(startTime));
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> findChartAndChartNodeCreateTicket(StartProcessInstanceDto request, String username, Boolean isBasicApi) {
        long startTime = System.currentTimeMillis();
        List<Map<String, Object>> mapResponse = null;
        String url = "/chart/findChartAndChartNodeCreateTicket";
        if (isBasicApi) url += "BasicAuth";
        Map<String, Object> params = convertEntityToMap(request);
        params.put("username", username);
        log.info("Starting call bpm-service url: {}", url);
        try {
            HttpHeaders httpHeaders = getHeaders();

            mapResponse = restHelper.postAndGetBody(
                    getFullUrlCustomer(url),
                    httpHeaders,
                    params,
                    new ParameterizedTypeReference<>() {
                    });

            return mapResponse;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            log.info("Finished call findChartAndChartNodeCreateTicket in {} ms", getEndTimeInMs(startTime));
        }
        return null;
    }

    public Map<String, Object> convertEntityToMap(Object entity) {
        Class<?> personClass = entity.getClass();
        Field[] fields = personClass.getDeclaredFields();

        Map<String, Object> params = new HashMap<>();
        for (Field field : fields) {
            field.setAccessible(true); // Allow access to private fields
            String fieldName = field.getName();
            try {
                Object fieldValue = field.get(entity);
                params.put(fieldName, fieldValue);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return params;
    }
}
