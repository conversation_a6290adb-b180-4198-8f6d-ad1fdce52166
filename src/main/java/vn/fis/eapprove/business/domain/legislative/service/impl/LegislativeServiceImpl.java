package vn.fis.eapprove.business.domain.legislative.service.impl;

import jakarta.persistence.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.constant.LegislativeEnum;
import vn.fis.eapprove.business.domain.authority.service.AuthService;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefLegislativeStatusConfig;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcInstRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcdefLegislativeConfigRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcdefRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTaskRepository;
import vn.fis.eapprove.business.domain.bpm.service.BpmHistoryManager;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcInstManager;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcdefManager;
import vn.fis.eapprove.business.domain.legislative.model.entity.LegislativeProgram;
import vn.fis.eapprove.business.domain.legislative.model.entity.LegislativeProgramDetail;
import vn.fis.eapprove.business.domain.legislative.model.entity.LegislativeProgramHistory;
import vn.fis.eapprove.business.domain.legislative.model.mapper.LegislativeTaskMapper;
import vn.fis.eapprove.business.domain.legislative.model.request.LegislativeDashboardRequest;
import vn.fis.eapprove.business.domain.legislative.model.request.ReportRequest;
import vn.fis.eapprove.business.domain.legislative.model.response.*;
import vn.fis.eapprove.business.domain.legislative.repository.LegislativeProgramDetailRepository;
import vn.fis.eapprove.business.domain.legislative.repository.LegislativeProgramHistoryRepository;
import vn.fis.eapprove.business.domain.legislative.repository.LegislativeProgramRepository;
import vn.fis.eapprove.business.domain.legislative.service.LegislativeService;
import vn.fis.eapprove.business.dto.BpmProcdefDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.FileModel;
import vn.fis.eapprove.business.domain.legislative.model.mapper.LegislativeCreateModel;
import vn.fis.eapprove.business.domain.legislative.model.mapper.LegislativeDetailModel;
import vn.fis.eapprove.business.model.request.HistoryDto;
import vn.fis.eapprove.business.domain.legislative.model.request.LegislativeSearchRequest;
import vn.fis.eapprove.business.domain.legislative.model.request.LegislativeTicketRequest;
import vn.fis.eapprove.business.model.response.*;
import vn.fis.eapprove.business.specification.LegislativeSpecification;
import vn.fis.eapprove.business.tenant.manager.CamundaEngineService;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.constants.ProcInstConstants;
import vn.fis.spro.common.constants.TaskConstants;
import vn.fis.spro.common.constants.UserInfoManagerLevelEnum;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Slf4j
@Service("LegislativeServiceImpl")
@Transactional
public class LegislativeServiceImpl implements LegislativeService {

    private final LegislativeProgramRepository legislativeProgramRepository;
    private final CredentialHelper credentialHelper;
    private final LegislativeProgramDetailRepository legislativeProgramDetailRepository;
    private final CustomerService customerService;
    private final ResponseUtils responseUtils;
    private final LegislativeSpecification legislativeSpecification;
    private final BpmProcInstRepository bpmProcInstRepository;
    private final BpmProcInstManager bpmProcInstManager;
    private final BpmProcdefManager bpmProcdefManager;
    private final LegislativeProgramHistoryRepository legislativeProgramHistoryRepository;
    private final BpmProcdefRepository bpmProcdefRepository;
    private final BpmTaskRepository bpmTaskRepository;
    private final BpmProcdefLegislativeConfigRepository bpmProcdefLegislativeConfigRepository;
    private final BpmHistoryManager bpmHistoryManager;
    private final CamundaEngineService camundaEngineService;
    private final AuthService authService;

    public LegislativeServiceImpl(LegislativeProgramRepository legislativeProgramRepository,
                                  CredentialHelper credentialHelper,
                                  LegislativeProgramDetailRepository legislativeProgramDetailRepository,
                                  CustomerService customerService,
                                  ResponseUtils responseUtils,
                                  LegislativeSpecification legislativeSpecification,
                                  BpmProcInstRepository bpmProcInstRepository,
                                  BpmProcdefManager bpmProcdefManager,
                                  @Lazy BpmProcInstManager bpmProcInstManager,
                                  LegislativeProgramHistoryRepository legislativeProgramHistoryRepository,
                                  BpmProcdefRepository bpmProcdefRepository,
                                  BpmTaskRepository bpmTaskRepository,
                                  BpmProcdefLegislativeConfigRepository bpmProcdefLegislativeConfigRepository,
                                  BpmHistoryManager bpmHistoryManager,
                                  CamundaEngineService camundaEngineService,
                                  AuthService authService
    ) {
        this.legislativeProgramRepository = legislativeProgramRepository;
        this.credentialHelper = credentialHelper;
        this.legislativeProgramDetailRepository = legislativeProgramDetailRepository;
        this.customerService = customerService;
        this.responseUtils = responseUtils;
        this.legislativeSpecification = legislativeSpecification;
        this.bpmProcInstRepository = bpmProcInstRepository;
        this.bpmProcInstManager = bpmProcInstManager;
        this.legislativeProgramHistoryRepository = legislativeProgramHistoryRepository;
        this.bpmProcdefRepository = bpmProcdefRepository;
        this.bpmProcdefManager = bpmProcdefManager;
        this.bpmTaskRepository = bpmTaskRepository;
        this.bpmProcdefLegislativeConfigRepository = bpmProcdefLegislativeConfigRepository;
        this.bpmHistoryManager = bpmHistoryManager;
        this.camundaEngineService = camundaEngineService;
        this.authService = authService;
    }

    public Long createUpdate(LegislativeCreateModel request) {
        String username = credentialHelper.getJWTPayload().getUsername();
        LegislativeProgram legislative;
        String historyDetail;
        List<LegislativeProgramDetail> oldDetails = null;
        int historyVersion = 0;

        if (!ValidationUtils.isNullOrEmpty(request.getId())) {
            legislative = legislativeProgramRepository.findLegislativeProgramById(request.getId());
            oldDetails = legislativeProgramDetailRepository.findLegislativeProgramDetailByLegislativeId(legislative.getId());
            legislative.setUpdatedTime(LocalDateTime.now());
            legislative.setUpdatedUser(username);
            historyVersion = legislativeProgramHistoryRepository.getMaxVersionByLegislativeId(request.getId());
            historyDetail = handleHistoryDetail(request, legislative, oldDetails);
            if ((legislative.getProcInstId() == null && request.getProcInstId() != null) || legislative.getProcInstId() != null && !legislative.getProcInstId().equals(request.getProcInstId())) {
                legislative.setExecutionCount(legislative.getExecutionCount() + 1);
            }
        } else {
            legislative = new LegislativeProgram();
            legislative.setCreatedTime(LocalDateTime.now());
            legislative.setCreatedUser(username);
            legislative.setStatus(LegislativeEnum.LegislativeStatus.RESEARCHING.code);
            legislative.setExecutionCount(request.getProcInstId() != null ? 1 : 0);
            AssigneeInfoResponse userInfo = customerService.getAssigneeInfo(username);
            if (!ValidationUtils.isNullOrEmpty(userInfo)) {
                legislative.setCompanyCode(userInfo.getCompanyCode());
                legislative.setCompanyName(userInfo.getCompanyName());
                legislative.setChartNodeCode(userInfo.getChartNodeCode());
            }
            historyDetail = "Thêm mới bản ghi";
        }
        legislative.setName(request.getName());
        legislative.setType(request.getType());
        legislative.setDescription(request.getDescription());
        legislative.setReleaseYear(request.getReleaseYear());
        legislative.setResponsibleAgency(request.getResponsibleAgency());
        legislative.setProcDefId(request.getProcDefId());
        legislative.setProcessId(request.getProcessId());
        legislative.setPolicyDevelopmentProcess(request.getPolicyDevelopmentProcess());
        legislative.setHasTicket(request.getHasTicket());
        legislative.setTicketId(request.getTicketId());
        legislative.setProcInstId(request.getProcInstId());
        legislative.setTicketTitle(request.getTicketTitle());
        legislative.setRequestCode(request.getRequestCode());
        legislative.setStartKey(request.getStartKey());
        legislative.setTicketStatus(request.getTicketStatus());
        legislative.setApprovalSession(request.getApprovalSession());
        legislative.setReviewSession(request.getReviewSession());
        legislative.setActivityStatus(LegislativeEnum.LegislativeActivityStatus.PROCESSING.code);
        legislative.setProcessType(request.getProcessType());
        legislative.setEstimatedTime(request.getEstimatedTime());
        legislative.setOrganizationSector(request.getOrganizationSector());

        // case gắn với ticket đã có - handle status legislative
        if (!ValidationUtils.isNullOrEmpty(request.getProcInstId())) {
            String status = handleStatus(request.getProcInstId(), request.getProcessId(), request.getProcDefId(), null);
            if (!ValidationUtils.isNullOrEmpty(status)) {
                legislative.setStatus(status);
            }
        }

        legislativeProgramRepository.save(legislative);

        // save lai legislativeId + slaFinish ticket, task
        if (!ValidationUtils.isNullOrEmpty(request.getProcInstId())) {
            handleUpdateTicket(request.getProcInstId(), legislative, request.getDetails());
        }

        // Xoá detail cũ
        if (!ValidationUtils.isNullOrEmpty(oldDetails)) {
            legislativeProgramDetailRepository.deleteAll(oldDetails);
        }
        // save detail
        List<LegislativeProgramDetail> listDetail = saveDetails(request, legislative);
        legislativeProgramDetailRepository.saveAll(listDetail);

        // history
        if (!ValidationUtils.isNullOrEmpty(historyDetail)) {
            LegislativeProgramHistory history = new LegislativeProgramHistory();
            history.setLegislativeId(legislative.getId());
            history.setVersion(!ValidationUtils.isNullOrEmpty(request.getId()) ? historyVersion + 1 : 0);
            history.setDetail(historyDetail);
            history.setCreatedTime(LocalDateTime.now());
            history.setCreatedUser(username);
            history.setProcInstId(request.getProcInstId());
            legislativeProgramHistoryRepository.save(history);
        }

        return legislative.getId();
    }

    private void handleUpdateTicket(String procInstId, LegislativeProgram legislative, List<LegislativeDetailModel> details) {
        BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByProcInstId(procInstId);
        // chỉ update task ACTIVE, PROCESSING
        List<BpmTask> bpmTasks = bpmTaskRepository.findBpmTaskActiveByProcInstId(procInstId);
        bpmProcInst.setLegislativeId(legislative.getId());
        bpmProcInst.setLegislativeType(legislative.getType());
        bpmProcInst.setResponseAgency(legislative.getResponsibleAgency());
        long slaFinish = 0;
        for (LegislativeDetailModel detail : details) {
            if (detail.getFromDate() != null && detail.getToDate() != null) {
                LocalDate fromDate = LocalDate.parse(detail.getFromDate());
                LocalDate toDate = LocalDate.parse(detail.getToDate());
                String processType = detail.getProcessType();
                long totalDate = processType != null && processType.equals("shortcut")
                        ? 7 // quy trình rút gọn mặc định 7days, thêm cấu hình hệ thống sau
                        : ChronoUnit.DAYS.between(fromDate, toDate);
                slaFinish = slaFinish + totalDate;
                BpmTask bpmTask = bpmTasks.stream().filter(x -> x.getTaskDefKey().equals(detail.getTaskDefKey())).findFirst().orElse(null);
                if (bpmTask != null) {
                    LocalDateTime slaFinDate = bpmTask.getTaskCreatedTime().plusDays(totalDate + 1).toLocalDate().atStartOfDay();
                    bpmTask.setSlaFinishTime(slaFinDate);
                    bpmTask.setSlaFinish((double) totalDate);
                    bpmTaskRepository.save(bpmTask);
                }
            }
        }
        if (slaFinish > 0) {
            bpmProcInst.setSlaFinish(Double.parseDouble(String.valueOf(slaFinish)));
        }
        bpmProcInstRepository.save(bpmProcInst);
    }

    private List<LegislativeProgramDetail> saveDetails(LegislativeCreateModel request, LegislativeProgram legislative) {
        List<LegislativeProgramDetail> listDetail = new ArrayList<>();
        if (!ValidationUtils.isNullOrEmpty(request.getFiles())) {
            for (FileModel file : request.getFiles()) {
                LegislativeProgramDetail detail = new LegislativeProgramDetail();
                detail.setLegislativeId(legislative.getId());
                detail.setType(LegislativeEnum.LegislativeDetailType.FILE.code);
                detail.setFileName(file.getFileName());
                detail.setFileUrl(file.getFileUrl());
                listDetail.add(detail);
            }
        }
        if (!ValidationUtils.isNullOrEmpty(request.getProcessUsers())) {
            for (String processUser : request.getProcessUsers()) {
                LegislativeProgramDetail detail = new LegislativeProgramDetail();
                detail.setLegislativeId(legislative.getId());
                detail.setType(LegislativeEnum.LegislativeDetailType.PROCESS_USER.code);
                detail.setUsername(processUser);
                listDetail.add(detail);
            }
        }

        if (!ValidationUtils.isNullOrEmpty(request.getDetails())) {
            List<LegislativeTaskMapper> mapTaskStatus = null;
            if (!ValidationUtils.isNullOrEmpty(request.getTicketId())) {
                List<TaskInfoResponse> lstTaskInfo = bpmProcInstManager.getAllUserTaskInfo(request.getTicketId());
                mapTaskStatus = getMapTaskStatus(lstTaskInfo);
            }
            for (LegislativeDetailModel model : request.getDetails()) {
                LegislativeProgramDetail detail = new LegislativeProgramDetail();
                detail.setLegislativeId(legislative.getId());
                detail.setType(LegislativeEnum.LegislativeDetailType.PROCESS_DETAIL.code);
                detail.setTaskDefKey(model.getTaskDefKey());
                detail.setTaskName(model.getTaskName());
                detail.setFromDate(model.getFromDate());
                detail.setToDate(model.getToDate());
                detail.setProcessType(model.getProcessType());
                detail.setCompanyCode(model.getCompanyCode());
                detail.setCompanyName(model.getCompanyName());

                if (!ValidationUtils.isNullOrEmpty(mapTaskStatus)) {
                    LegislativeTaskMapper map = mapTaskStatus.stream().filter(x -> x.getTaskDefKey().equals(model.getTaskDefKey())).findFirst().orElse(null);
                    if (map != null) {
                        detail.setStatus(map.getLegislativeStatus());
                        detail.setCreatedTime(map.getCreatedTime());
                        detail.setFinishTime(map.getFinishTime());
                    } else {
                        detail.setStatus(LegislativeEnum.LegislativeDetailStatus.DEACTIVE.code);
                    }
                } else {
                    detail.setStatus(LegislativeEnum.LegislativeDetailStatus.WAITING.code);
                }
                listDetail.add(detail);
            }
        }
        return listDetail;
    }

    private String handleStatus(String procInstId, Long processId, String procDefId, String taskDefKey) {
        // lấy task runtime từ camunda
        List<Map<String, Object>> listData = camundaEngineService.getCurrentRuntimeTasks(procInstId);
        log.info("handleStatus:procInstId = {} ,taskDefKey = {}, tasks = {}", procInstId, taskDefKey, listData.toString());

        // config status
        List<BpmProcdefLegislativeStatusConfig> configStatusList = bpmProcdefLegislativeConfigRepository
                .getAllByBpmProcdefIdAndProcDefId(processId, procDefId);

        // phiếu hoàn thành
        if (listData.isEmpty()) {
            String endTaskKey = bpmProcInstRepository.getEndKeyByProcInstId(procInstId);
            return configStatusList.stream()
                    .filter(cfg -> endTaskKey.equals(cfg.getTaskDefKey()))
                    .map(BpmProcdefLegislativeStatusConfig::getLegislativeStatus)
                    .findFirst()
                    .orElse(null);
        }

        if (!ValidationUtils.isNullOrEmpty(configStatusList)) {
            if (taskDefKey == null) {
                taskDefKey = listData.get(0).get("taskDefKey").toString();
            }
            String finalTaskDefKey = taskDefKey;
            return configStatusList.stream()
                    .filter(cfg -> cfg.getTaskDefKey().equals(finalTaskDefKey))
                    .map(BpmProcdefLegislativeStatusConfig::getLegislativeStatus)
                    .findFirst().orElse(null);
        }

        return null;
    }

    private String handleHistoryDetail(LegislativeCreateModel request, LegislativeProgram
            oldLegislative, List<LegislativeProgramDetail> oldDetails) {
        StringBuilder detail = new StringBuilder();
        if (!request.getName().equals(oldLegislative.getName())) {
            detail.append("Thay đổi Tên nhiệm vụ từ ").append(oldLegislative.getName()).append(" thành ").append(request.getName())
                    .append(System.lineSeparator());
        }
        if (!request.getDescription().equals(oldLegislative.getDescription())) {
            detail.append("Thay đổi Ghi chú từ ").append(oldLegislative.getDescription()).append(" thành ").append(request.getDescription())
                    .append(System.lineSeparator());
        }
        if (!request.getReleaseYear().equals(oldLegislative.getReleaseYear())) {
            detail.append("Thay đổi Năm ban hành từ ").append(oldLegislative.getReleaseYear()).append(" thành ").append(request.getReleaseYear())
                    .append(System.lineSeparator());
        }
        if (!request.getResponsibleAgency().equals(oldLegislative.getResponsibleAgency())) {
            detail.append("Thay đổi Cơ quản chủ trì soạn thảo từ ").append(oldLegislative.getResponsibleAgency()).append(" thành ").append(request.getResponsibleAgency())
                    .append(System.lineSeparator());
        }
        if (!request.getHasTicket().equals(oldLegislative.getHasTicket())) {
            String oldString = oldLegislative.getHasTicket() ? "Có" : "Không";
            String newString = request.getHasTicket() ? "Có" : "Không";
            detail.append("Thay đổi Đã có tờ trình xây dựng văn bản quy phạm pháp luật từ ")
                    .append(oldString).append(" thành ")
                    .append(newString)
                    .append(System.lineSeparator());
        }
        if (request.getTicketId() != null && !request.getTicketId().equals(oldLegislative.getTicketId())) {
            String oldTicket = !ValidationUtils.isNullOrEmpty(oldLegislative.getRequestCode())
                    ? (oldLegislative.getRequestCode() + " - " + oldLegislative.getTicketTitle())
                    : oldLegislative.getTicketTitle();
            String newTicket = !ValidationUtils.isNullOrEmpty(request.getRequestCode())
                    ? (request.getRequestCode() + " - " + request.getTicketTitle())
                    : request.getTicketTitle();
            detail.append("Thay đổi Chọn tờ trình xây dựng văn bản quy phạm pháp luật từ ")
                    .append(oldTicket).append(" thành ")
                    .append(newTicket)
                    .append(System.lineSeparator());
        }
        if (!request.getPolicyDevelopmentProcess().equals(oldLegislative.getPolicyDevelopmentProcess())) {
            String oldString = oldLegislative.getPolicyDevelopmentProcess() ? "Có" : "Không";
            String newString = request.getPolicyDevelopmentProcess() ? "Có" : "Không";
            detail.append("Thay đổi Quy trình xây dựng chính sách đối với dự án luật từ ")
                    .append(oldString).append(" thành ")
                    .append(newString)
                    .append(System.lineSeparator());
        }
        if (request.getProcessId() != null && !request.getProcessId().equals(oldLegislative.getProcessId())) {
            String oldName = oldLegislative.getProcessId() != null ? bpmProcdefRepository.getNameById(oldLegislative.getProcessId()) : "";
            String newName = request.getProcessId() != null ? bpmProcdefRepository.getNameById(request.getProcessId()) : "";
            detail.append("Thay đổi Chọn luồng nghiệp vụ từ ")
                    .append(oldName).append(" thành ")
                    .append(newName)
                    .append(System.lineSeparator());
        }
        if (!request.getApprovalSession().equals(oldLegislative.getApprovalSession())) {
            detail.append("Thay đổi Kỳ họp quốc hội thông qua từ ")
                    .append(oldLegislative.getApprovalSession()).append(" thành ")
                    .append(request.getApprovalSession())
                    .append(System.lineSeparator());
        }
        if (!request.getReviewSession().equals(oldLegislative.getReviewSession())) {
            detail.append("Thay đổi Kỳ họp quốc hội cho ý kiến từ ")
                    .append(oldLegislative.getReviewSession()).append(" thành ")
                    .append(request.getReviewSession())
                    .append(System.lineSeparator());
        }
        String oldFileStr = "";
        String newFileStr = "";
        List<String> oldFiles = oldDetails.stream().filter(x -> x.getType().equals(LegislativeEnum.LegislativeDetailType.FILE.code))
                .map(LegislativeProgramDetail::getFileName).toList();
        if (!ValidationUtils.isNullOrEmpty(oldFiles)) {
            oldFileStr = String.join(",", oldFiles);
        }
        if (!ValidationUtils.isNullOrEmpty(request.getFiles())) {
            newFileStr = String.join(",", request.getFiles().stream().map(FileModel::getFileName).toList());
        }
        if (!oldFileStr.equals(newFileStr)) {
            detail.append("Thay đổi Văn bản giao nhiệm vụ từ ")
                    .append(oldFileStr).append(" thành ")
                    .append(newFileStr)
                    .append(System.lineSeparator());
        }
        String oldProcessUserStr = "";
        String newProcessUserStr = "";
        List<String> oldProcessUser = oldDetails.stream()
                .filter(x -> x.getType().equals(LegislativeEnum.LegislativeDetailType.PROCESS_USER.code))
                .map(LegislativeProgramDetail::getUsername).toList();
        if (!ValidationUtils.isNullOrEmpty(oldProcessUser)) {
            oldProcessUserStr = String.join(",", oldProcessUser);
        }
        if (!ValidationUtils.isNullOrEmpty(request.getProcessUsers())) {
            newProcessUserStr = String.join(",", request.getProcessUsers());
        }
        if (!oldProcessUserStr.equals(newProcessUserStr)) {
            if (!oldFileStr.equals(newFileStr)) {
                detail.append("Thay đổi Người thực hiện quản lý kế hoạch, chỉnh sửa chi tiết nhiệm vụ từ ")
                        .append(oldProcessUserStr).append(" thành ")
                        .append(newProcessUserStr)
                        .append(System.lineSeparator());
            }
        }

        List<LegislativeDetailModel> oldDetailModel = new ArrayList<>();
        List<LegislativeProgramDetail> lstDetail = oldDetails.stream()
                .filter(x -> x.getType().equals(LegislativeEnum.LegislativeDetailType.PROCESS_DETAIL.code))
                .toList();
        if (!ValidationUtils.isNullOrEmpty(lstDetail)) {
            for (LegislativeProgramDetail detail1 : lstDetail) {
                LegislativeDetailModel model = toDetailModel(detail1);
                oldDetailModel.add(model);
            }
        }
        List<LegislativeDetailModel> newDetailModel = request.getDetails();
        if (!oldDetailModel.equals(newDetailModel)) {
            detail.append("Thay đổi Chi tiết nhiệm vụ").append(System.lineSeparator());
        }

        return detail.toString();
    }

    private LegislativeDetailModel toDetailModel(LegislativeProgramDetail detail) {
        LegislativeDetailModel model = new LegislativeDetailModel();
        model.setTaskDefKey(detail.getTaskDefKey());
        model.setTaskName(detail.getTaskName());
        model.setFromDate(detail.getFromDate());
        model.setToDate(detail.getToDate());
        model.setProcessType(detail.getProcessType());
        model.setCompanyCode(detail.getCompanyCode());
        model.setCompanyName(detail.getCompanyName());
        model.setStatus(detail.getStatus());
        return model;
    }

    @Override
    public String updateStatus(Long id, String status) {
        LegislativeProgram legislative = legislativeProgramRepository.findLegislativeProgramById(id);
        if (status.equals(LegislativeEnum.LegislativeActivityStatus.DEACTIVE.code)) {
            if (!ValidationUtils.isNullOrEmpty(legislative.getTicketId())) {
                String ticketStatus = bpmProcInstRepository.getTicketStatusByTicketId(legislative.getTicketId());
                if (!ticketStatus.equals(ProcInstConstants.Status.CANCEL.code)) {
                    return LegislativeEnum.LegislativeMessage.VALID_UPDATE_STATUS_CANCEL.code;
                }
            } else {
                return LegislativeEnum.LegislativeMessage.VALID_UPDATE_STATUS_NO_TICKET.code;
            }
        }
        if (status.equals(LegislativeEnum.LegislativeActivityStatus.PROCESSING.code)
                && !legislative.getStatus().equals(LegislativeEnum.LegislativeActivityStatus.DEACTIVE.code)) {
            return LegislativeEnum.LegislativeMessage.VALID_UPDATE_STATUS_DEACTIVE.code;
        }

        // history
        LegislativeProgramHistory history = new LegislativeProgramHistory();
        history.setLegislativeId(legislative.getId());
        history.setProcInstId(legislative.getProcInstId());
        Integer historyVersion = legislativeProgramHistoryRepository.getMaxVersionByLegislativeId(legislative.getId());
        history.setVersion(historyVersion != null ? historyVersion + 1 : 0);
        String oldStatus = LegislativeEnum.LegislativeActivityStatus.getDetailByCode(legislative.getStatus());
        String newStatus = LegislativeEnum.LegislativeActivityStatus.getDetailByCode(status);
        history.setDetail("Thay đổi trạng thái xử lý từ " + oldStatus + " thành " + newStatus);
        history.setCreatedTime(LocalDateTime.now());
        history.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
        legislativeProgramHistoryRepository.save(history);

        legislative.setStatus(status);
        legislative.setUpdatedTime(LocalDateTime.now());
        legislative.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
        legislativeProgramRepository.save(legislative);
        return LegislativeEnum.LegislativeMessage.SUCCESS.code;
    }

    @Override
    public void cancel(Long id) {
        String username = credentialHelper.getJWTPayload().getUsername();
        LegislativeProgram legislative = legislativeProgramRepository.findLegislativeProgramById(id);
        legislative.setStatus(LegislativeEnum.LegislativeActivityStatus.CANCEL.code);
        legislative.setUpdatedTime(LocalDateTime.now());
        legislative.setUpdatedUser(username);
        legislative.setCancelUser(username);
        legislative.setCancelTime(LocalDateTime.now());
        legislativeProgramRepository.save(legislative);

        List<LegislativeProgramDetail> lstDetail = legislativeProgramDetailRepository.findLegislativeProgramDetailByLegislativeIdAndType(id, LegislativeEnum.LegislativeDetailType.PROCESS_DETAIL.code);
        if (!ValidationUtils.isNullOrEmpty(lstDetail)) {
            for (LegislativeProgramDetail detail : lstDetail) {
                detail.setStatus(LegislativeEnum.LegislativeDetailStatus.CANCEL.code);
                detail.setFinishTime(LocalDateTime.now());
            }
        }
        legislativeProgramDetailRepository.saveAll(lstDetail);

        // huỷ phiếu
        if (!ValidationUtils.isNullOrEmpty(legislative.getTicketId())) {
            cancelTicket(legislative.getTicketId(),
                    legislative.getProcInstId(),
                    username,
                    LegislativeEnum.LegislativeMessage.CANCEL_TICKET_WITH_PROGRAM.message
            );
        }

        // history
        LegislativeProgramHistory history = new LegislativeProgramHistory();
        history.setLegislativeId(legislative.getId());
        Integer historyVersion = legislativeProgramHistoryRepository.getMaxVersionByLegislativeId(legislative.getId());
        history.setVersion(historyVersion != null ? historyVersion + 1 : 0);
        history.setDetail("Huỷ nhiệm vụ");
        history.setCreatedTime(LocalDateTime.now());
        history.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
        history.setProcInstId(legislative.getProcInstId());
        legislativeProgramHistoryRepository.save(history);
    }

    private void cancelTicket(Long ticketId, String procInstId, String username, String reason) {
        BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(procInstId);

        HistoryDto hisDto = new HistoryDto();
        hisDto.setActionUser(username);
        hisDto.setAction(TaskConstants.HistoryAction.CANCEL_TICKET.code);
        hisDto.setNote(reason);
        hisDto.setProcInstId(bpmProcInst.getTicketProcInstId());
        hisDto.setTicketId(ticketId);
        bpmHistoryManager.saveHistory(hisDto);

        bpmProcInst.setTicketClosedTime(LocalDateTime.now());
        bpmProcInst.setTicketCanceledTime(LocalDateTime.now());
        bpmProcInst.setTicketStatus(ProcInstConstants.Status.CANCEL.code);
        bpmProcInst.setCancelReason(reason);
        bpmProcInst.setCancelUser(username);
        bpmProcInstRepository.save(bpmProcInst);

        List<BpmTask> bpmTasks = bpmTaskRepository.getAllTasksByStatus(Collections.singletonList(bpmProcInst.getTicketProcInstId()),
                Arrays.asList("ACTIVE", "PROCESSING", "DELETED_BY_RU"));
        if (!ValidationUtils.isNullOrEmpty(bpmTasks)) {
            for (BpmTask e : bpmTasks) {
                e.setTaskStatus(TaskConstants.Status.CANCEL.code);
            }
        }
        bpmTaskRepository.saveAll(bpmTasks);
    }

    @Override
    public LegislativeCreateModel getById(Long id) {

        LegislativeProgram legislative = legislativeProgramRepository.findLegislativeProgramById(id);
        List<LegislativeProgramDetail> lstDetail = legislativeProgramDetailRepository.findLegislativeProgramDetailByLegislativeId(legislative.getId());
        List<String> lstProcessUser = lstDetail.stream()
                .filter(x -> x.getType().equals(LegislativeEnum.LegislativeDetailType.PROCESS_USER.code))
                .map(LegislativeProgramDetail::getUsername)
                .toList();

        String username = credentialHelper.getJWTPayload().getUsername();
        AssigneeInfoResponse userInfo = customerService.getAssigneeInfo(username);
        if (!username.equals(legislative.getCreatedUser())
                && (!ValidationUtils.isNullOrEmpty(lstProcessUser) || !lstProcessUser.contains(userInfo.getUsername()))
                && (!userInfo.getManagerLevel().equals(UserInfoManagerLevelEnum.PRIMARY.managerLevel) || !userInfo.getChartNodeChild().contains(legislative.getChartNodeCode()))
        ) {
            throw new RuntimeException("Bạn không có quyền xem nhiệm vụ này");
        }

        LegislativeCreateModel response = new LegislativeCreateModel();
        response.setId(legislative.getId());
        response.setName(legislative.getName());
        response.setType(legislative.getType());
        response.setDescription(legislative.getDescription());
        response.setReleaseYear(legislative.getReleaseYear());
        response.setResponsibleAgency(legislative.getResponsibleAgency());
        response.setProcDefId(legislative.getProcDefId());
        response.setProcessId(legislative.getProcessId());
        response.setPolicyDevelopmentProcess(legislative.getPolicyDevelopmentProcess());
        response.setHasTicket(legislative.getHasTicket());
        response.setProcInstId(legislative.getProcInstId());
        response.setTicketId(legislative.getTicketId());
        response.setTicketTitle(legislative.getTicketTitle());
        response.setRequestCode(legislative.getRequestCode());
        response.setApprovalSession(legislative.getApprovalSession());
        response.setReviewSession(legislative.getReviewSession());
        response.setStartKey(legislative.getStartKey());
        response.setStatus(legislative.getStatus());
        response.setProcessType(legislative.getProcessType());
        response.setExecutionCount(legislative.getExecutionCount());
        response.setCreatedUser(legislative.getCreatedUser());
        response.setTicketStatus(legislative.getTicketStatus());
        response.setEstimatedTime(legislative.getEstimatedTime());
        response.setOrganizationSector(legislative.getOrganizationSector());

        // file
        List<LegislativeProgramDetail> lstFileDetail = lstDetail.stream()
                .filter(x -> x.getType().equals(LegislativeEnum.LegislativeDetailType.FILE.code))
                .toList();
        if (!ValidationUtils.isNullOrEmpty(lstFileDetail)) {
            List<FileModel> lstFile = new ArrayList<>();
            for (LegislativeProgramDetail detail : lstFileDetail) {
                FileModel file = new FileModel();
                file.setFileName(detail.getFileName());
                file.setFileUrl(detail.getFileUrl());
                lstFile.add(file);
            }
            response.setFiles(lstFile);
        }
        // process user
        if (!ValidationUtils.isNullOrEmpty(lstProcessUser)) {
            response.setProcessUsers(lstProcessUser);
        }
        // process detail
        List<LegislativeProgramDetail> lstProcessDetail = lstDetail.stream()
                .filter(x -> x.getType().equals(LegislativeEnum.LegislativeDetailType.PROCESS_DETAIL.code))
                .toList();
        if (!ValidationUtils.isNullOrEmpty(lstProcessDetail)) {
            List<LegislativeDetailModel> lstProcessDetailModel = getLegislativeDetailModels(lstProcessDetail);
            response.setDetails(lstProcessDetailModel);
        }

        return response;
    }

    private List<LegislativeDetailModel> getLegislativeDetailModels(List<LegislativeProgramDetail> lstProcessDetail) {
        List<LegislativeDetailModel> lstProcessDetailModel = new ArrayList<>();
        for (LegislativeProgramDetail detail : lstProcessDetail) {
            LegislativeDetailModel model = new LegislativeDetailModel();
            model.setTaskDefKey(detail.getTaskDefKey());
            model.setTaskName(detail.getTaskName());
            model.setFromDate(detail.getFromDate());
            model.setToDate(detail.getToDate());
            model.setProcessType(detail.getProcessType());
            model.setCompanyCode(detail.getCompanyCode());
            model.setCompanyName(detail.getCompanyName());
            model.setStatus(detail.getStatus());
            lstProcessDetailModel.add(model);
        }
        return lstProcessDetailModel;
    }

    @Override
    public PageDto search(LegislativeSearchRequest request) {
        int pageNum = request.getPage() - 1;
        Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());

        String username = credentialHelper.getJWTPayload().getUsername();
        request.setUsername(username);
        AssigneeInfoResponse userInfo = customerService.getAssigneeInfo(username);
        if (userInfo != null && userInfo.getManagerLevel().equals(UserInfoManagerLevelEnum.PRIMARY.managerLevel)) {
            request.setListChartNodeCode(userInfo.getChartNodeChild());
        }

        Page<LegislativeProgram> page = legislativeProgramRepository.findAll(
                legislativeSpecification.search(request),
                PageRequest.of(pageNum, request.getLimit(), sort)
        );

        List<LegislativeSearchResponse> lstResponse = new ArrayList<>();
        List<LegislativeProgramDetail> lstDetailFile = legislativeProgramDetailRepository.findLegislativeProgramDetailByType(LegislativeEnum.LegislativeDetailType.FILE.code);
        List<LegislativeProgramDetail> lstDetailProcessUser = legislativeProgramDetailRepository.findLegislativeProgramDetailByType(LegislativeEnum.LegislativeDetailType.PROCESS_USER.code);
        for (LegislativeProgram legislative : page.getContent()) {
            LegislativeSearchResponse response = new LegislativeSearchResponse();
            response.setId(legislative.getId());
            response.setName(legislative.getName());
            response.setType(legislative.getType());
            response.setStatus(legislative.getStatus());
            response.setDescription(legislative.getDescription());
            response.setReleaseYear(legislative.getReleaseYear());
            response.setResponsibleAgency(legislative.getResponsibleAgency());
            response.setExecutionCount(legislative.getExecutionCount());
            response.setApprovalSession(legislative.getApprovalSession());
            response.setReviewSession(legislative.getReviewSession());
            response.setCreatedTime(legislative.getCreatedTime());
            response.setCreatedUser(legislative.getCreatedUser());
            response.setUpdatedTime(legislative.getUpdatedTime());
            response.setUpdatedUser(legislative.getUpdatedUser());
            response.setCompanyName(legislative.getCompanyName());
            response.setProcDefId(legislative.getProcDefId());
            response.setTicketId(legislative.getTicketId());
            response.setRequestCode(legislative.getRequestCode());
            response.setTicketStatus(legislative.getTicketStatus());
            response.setActivityStatus(legislative.getActivityStatus());
            response.setStartKey(legislative.getStartKey());

            List<LegislativeProgramDetail> lstDetail = lstDetailFile.stream().filter(x -> x.getLegislativeId().equals(legislative.getId())).toList();
            if (!ValidationUtils.isNullOrEmpty(lstDetail)) {
                List<FileModel> lstFile = new ArrayList<>();
                for (LegislativeProgramDetail detail : lstDetail) {
                    FileModel file = new FileModel();
                    file.setFileName(detail.getFileName());
                    file.setFileUrl(detail.getFileUrl());
                    lstFile.add(file);
                }
                response.setFiles(lstFile);
            }
            List<LegislativeProgramDetail> lstDetailProcessUserDetail = lstDetailProcessUser.stream().filter(x -> x.getLegislativeId().equals(legislative.getId())).toList();
            if (!ValidationUtils.isNullOrEmpty(lstDetailProcessUserDetail)) {
                List<String> lstProcessUser = lstDetailProcessUserDetail.stream().map(LegislativeProgramDetail::getUsername).toList();
                response.setProcessUsers(lstProcessUser);
            }

            lstResponse.add(response);
        }

        return PageDto.builder().content(lstResponse)
                .number(page.getNumber() + 1)
                .numberOfElements(page.getNumberOfElements())
                .page(page.getNumber() + 1)
                .limit(page.getSize())
                .totalPages(page.getTotalPages())
                .totalElements(page.getTotalElements())
                .sortBy(request.getSortBy())
                .sortType(request.getSortType())
                .build();
    }

    @Override
    public List<LegislativeSearchResponse> searchFilter(LegislativeSearchRequest request) {
        int pageNum = request.getPage() - 1;
        Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());

        String username = credentialHelper.getJWTPayload().getUsername();
        request.setUsername(username);
        AssigneeInfoResponse userInfo = customerService.getAssigneeInfo(username);
        if (userInfo != null && userInfo.getManagerLevel().equals(UserInfoManagerLevelEnum.PRIMARY.managerLevel)) {
            request.setListChartNodeCode(userInfo.getChartNodeChild());
        }

        Page<LegislativeProgram> page = legislativeProgramRepository.findAll(
                legislativeSpecification.search(request),
                PageRequest.of(pageNum, request.getLimit(), sort)
        );

        List<LegislativeSearchResponse> lstResponse = new ArrayList<>();
        List<LegislativeProgramDetail> lstDetailFile = legislativeProgramDetailRepository.findLegislativeProgramDetailByType(LegislativeEnum.LegislativeDetailType.FILE.code);
        for (LegislativeProgram legislative : page.getContent()) {
            LegislativeSearchResponse response = new LegislativeSearchResponse();
            response.setId(legislative.getId());
            response.setName(legislative.getName());
            response.setType(legislative.getType());
            response.setStatus(legislative.getStatus());
            response.setDescription(legislative.getDescription());
            response.setReleaseYear(legislative.getReleaseYear());
            response.setResponsibleAgency(legislative.getResponsibleAgency());
            response.setExecutionCount(legislative.getExecutionCount());
            response.setApprovalSession(legislative.getApprovalSession());
            response.setReviewSession(legislative.getReviewSession());
            response.setCreatedTime(legislative.getCreatedTime());
            response.setCreatedUser(legislative.getCreatedUser());
            response.setUpdatedTime(legislative.getUpdatedTime());
            response.setUpdatedUser(legislative.getUpdatedUser());
            response.setCompanyName(legislative.getCompanyName());
            response.setProcDefId(legislative.getProcDefId());
            response.setTicketId(legislative.getTicketId());
            response.setRequestCode(legislative.getRequestCode());
            response.setTicketStatus(legislative.getTicketStatus());
            response.setActivityStatus(legislative.getActivityStatus());
            response.setStartKey(legislative.getStartKey());

            List<LegislativeProgramDetail> lstDetail = lstDetailFile.stream().filter(x -> x.getLegislativeId().equals(legislative.getId())).toList();
            if (!ValidationUtils.isNullOrEmpty(lstDetail)) {
                List<FileModel> lstFile = new ArrayList<>();
                for (LegislativeProgramDetail detail : lstDetail) {
                    FileModel file = new FileModel();
                    file.setFileName(detail.getFileName());
                    file.setFileUrl(detail.getFileUrl());
                    lstFile.add(file);
                }
                response.setFiles(lstFile);
            }

            lstResponse.add(response);
        }

        return lstResponse;
    }

    @Override
    public List<LegislativeProgramHistory> getHistory(Long legislativeId) {
        return legislativeProgramHistoryRepository.findLegislativeProgramHistoryByLegislativeIdAndVersionIsNotNullOrderById(legislativeId);
    }

    @Override
    public void updateActionApi(LegislativeTicketRequest request) {
        switch (request.getActionCode()) {
            case "CREATE_TICKET":
                updateCreateTicket(request);
                break;
            case "DO_TASK":
                updateDoTask(request);
                break;
            case "REDO":
                updateRedoTicket(request);
                break;
            case "CANCEL_TICKET":
            case "REQUEST_UPDATE":
            case "REQUEST_UPDATE_TO_START":
                updateCancelTicket(request);
                break;
            case "COMPLETE":
                updateCompleteTicket(request);
                break;
            default:
                break;
        }
    }

    private void updateCreateTicket(LegislativeTicketRequest request) {
        LegislativeProgram legislative = legislativeProgramRepository.findLegislativeProgramById(request.getId());
        if (!ValidationUtils.isNullOrEmpty(legislative)) {
            String status = handleStatus(request.getProcInstId(), legislative.getProcessId(), legislative.getProcDefId(), request.getTaskDefKey());
            if (!ValidationUtils.isNullOrEmpty(status)) {
                legislative.setStatus(status);
            }
            legislative.setTicketId(request.getTicketId());
            legislative.setHasTicket(true);
            legislative.setProcInstId(request.getProcInstId());
            legislative.setStartKey(request.getStartKey());
            legislative.setTicketTitle(request.getTicketTitle());
            legislative.setTicketStatus(request.getTicketStatus());
            legislative.setActivityStatus(LegislativeEnum.LegislativeActivityStatus.PROCESSING.code);
            legislative.setExecutionCount(legislative.getExecutionCount() + 1);
            legislative.setUpdatedTime(LocalDateTime.now());
            legislative.setUpdatedUser(request.getUsername());
            legislativeProgramRepository.save(legislative);

            // update detail status
            updateDetailStatus(request);

            // save history
            LegislativeProgramHistory history = new LegislativeProgramHistory();
            history.setLegislativeId(legislative.getId());
            history.setProcInstId(request.getProcInstId());
            history.setDetail(request.getActionCode());
            history.setCreatedUser(request.getUsername());
            history.setCreatedTime(LocalDateTime.now());
            legislativeProgramHistoryRepository.save(history);
        }
    }

    private void updateDoTask(LegislativeTicketRequest request) {
        LegislativeProgram legislative = legislativeProgramRepository.findLegislativeProgramById(request.getId());
        if (!ValidationUtils.isNullOrEmpty(legislative)) {
            BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(request.getProcInstId());
            legislative.setTicketStatus(bpmProcInst.getTicketStatus());
            String status = handleStatus(request.getProcInstId(), legislative.getProcessId(), legislative.getProcDefId(), request.getTaskDefKey());
            if (!ValidationUtils.isNullOrEmpty(status)) {
                legislative.setStatus(status);
            }
            legislative.setUpdatedTime(LocalDateTime.now());
            legislative.setUpdatedUser(request.getUsername());
            legislativeProgramRepository.save(legislative);

            // update detail status
            updateDetailStatus(request);
        }
    }

    private void updateRedoTicket(LegislativeTicketRequest request) {
        LegislativeProgram legislative = legislativeProgramRepository.findLegislativeProgramById(request.getId());
        if (!ValidationUtils.isNullOrEmpty(legislative)) {
            String status = handleStatus(request.getProcInstId(), legislative.getProcessId(), legislative.getProcDefId(), request.getTaskDefKey());
            if (!ValidationUtils.isNullOrEmpty(status)) {
                legislative.setStatus(status);
            }
            BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(request.getProcInstId());
            legislative.setTicketStatus(bpmProcInst.getTicketStatus());
            legislative.setActivityStatus(LegislativeEnum.LegislativeActivityStatus.PROCESSING.code);
            legislative.setExecutionCount(legislative.getExecutionCount() + 1);
            legislative.setUpdatedTime(LocalDateTime.now());
            legislative.setUpdatedUser(request.getUsername());

            // update detail status
            updateDetailStatus(request);

            // save history
            LegislativeProgramHistory history = new LegislativeProgramHistory();
            history.setLegislativeId(legislative.getId());
            history.setProcInstId(request.getProcInstId());
            history.setDetail(request.getActionCode());
            history.setCreatedUser(request.getUsername());
            history.setCreatedTime(LocalDateTime.now());
            legislativeProgramHistoryRepository.save(history);
        }
    }

    private void updateCancelTicket(LegislativeTicketRequest request) {
        LegislativeProgram legislative = legislativeProgramRepository.findLegislativeProgramById(request.getId());
        if (!ValidationUtils.isNullOrEmpty(legislative)) {
            legislative.setActivityStatus(LegislativeEnum.LegislativeActivityStatus.CANCEL.code);
            legislative.setUpdatedUser(request.getUsername());
            legislative.setUpdatedTime(LocalDateTime.now());
            legislative.setCancelTime(LocalDateTime.now());
            legislative.setCancelUser(request.getUsername());
            legislativeProgramRepository.save(legislative);

            // update detail status
            updateDetailStatus(request);

            // save history
            LegislativeProgramHistory history = new LegislativeProgramHistory();
            history.setLegislativeId(legislative.getId());
            history.setProcInstId(request.getProcInstId());
            history.setDetail(request.getActionCode());
            history.setCreatedUser(request.getUsername());
            history.setCreatedTime(LocalDateTime.now());
            legislativeProgramHistoryRepository.save(history);
        }
    }

    private void updateCompleteTicket(LegislativeTicketRequest request) {
        LegislativeProgram legislative = legislativeProgramRepository.findLegislativeProgramById(request.getId());
        BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(request.getProcInstId());
        if (!ValidationUtils.isNullOrEmpty(legislative)) {
            legislative.setActivityStatus(LegislativeEnum.LegislativeActivityStatus.COMPLETED.code);
            String status = handleStatus(request.getProcInstId(), legislative.getProcessId(), legislative.getProcDefId(), request.getTaskDefKey());
            if (!ValidationUtils.isNullOrEmpty(status)) {
                legislative.setStatus(status);
            }
            legislative.setTicketStatus(request.getTicketStatus());
            legislative.setUpdatedUser(request.getUsername());
            legislative.setUpdatedTime(LocalDateTime.now());
            legislative.setTicketCreatedTime(bpmProcInst.getFirstCreatedTime());
            legislative.setTicketFinishTime(LocalDateTime.now());
            Long processTime = ChronoUnit.DAYS.between(bpmProcInst.getFirstCreatedTime().toLocalDate(), LocalDate.now());
            legislative.setProcessTime(processTime);
            legislativeProgramRepository.save(legislative);

            // update detail status
            updateDetailStatus(request);
        }
    }

    private void updateDetailStatus(LegislativeTicketRequest request) {
        List<TaskInfoResponse> lstTaskInfo = bpmProcInstManager.getAllUserTaskInfo(request.getTicketId());
        List<LegislativeTaskMapper> mapTaskStatus = getMapTaskStatus(lstTaskInfo);
        List<LegislativeProgramDetail> lstDetail = legislativeProgramDetailRepository.findLegislativeProgramDetailByLegislativeIdAndType(request.getId(), LegislativeEnum.LegislativeDetailType.PROCESS_DETAIL.code);
        if (!ValidationUtils.isNullOrEmpty(lstDetail)) {
            for (LegislativeProgramDetail detail : lstDetail) {
                LegislativeTaskMapper map = mapTaskStatus.stream().filter(x -> x.getTaskDefKey().equals(detail.getTaskDefKey())).findFirst().orElse(null);
                if (map != null) {
                    detail.setStatus(map.getLegislativeStatus());
                    detail.setCreatedTime(map.getCreatedTime());
                    detail.setFinishTime(map.getFinishTime());
                } else {
                    detail.setStatus(LegislativeEnum.LegislativeDetailStatus.DEACTIVE.code);
                }
            }
            legislativeProgramDetailRepository.saveAll(lstDetail);
        }
    }

    private List<LegislativeTaskMapper> getMapTaskStatus(List<TaskInfoResponse> lstTaskInfo) {
        List<LegislativeTaskMapper> lstResponse = new ArrayList<>();
        for (TaskInfoResponse taskInfo : lstTaskInfo) {
            LegislativeTaskMapper mapper = new LegislativeTaskMapper();
            // legislative: đang xử lý trong case task đơn -- song song sẽ phải check lại logic (BA)
            AssigneeInfo assigneeInfo = taskInfo.getLstAssigneeInfo().get(0);
            String status = assigneeInfo.getTaskStatus();
            if (status == null) {
                status = LegislativeEnum.LegislativeDetailStatus.WAITING.code;
            } else {
                status = switch (status) {
                    case "ACTIVE", "PROCESSING" -> LegislativeEnum.LegislativeDetailStatus.PROCESSING.code;
                    case "COMPLETED" -> LegislativeEnum.LegislativeDetailStatus.COMPLETED.code;
                    case "CANCEL" -> LegislativeEnum.LegislativeDetailStatus.CANCEL.code;
                    default -> LegislativeEnum.LegislativeDetailStatus.WAITING.code;
                };
            }
            mapper.setTaskDefKey(taskInfo.getTaskDefKey());
            mapper.setTaskStatus(assigneeInfo.getTaskStatus());
            mapper.setLegislativeStatus(status);
            mapper.setCreatedTime(assigneeInfo.getCreatedTime());
            mapper.setFinishTime(assigneeInfo.getFinishTime());
            lstResponse.add(mapper);
        }
        return lstResponse;
    }

    public List<LegislativeSearchResponse> getLegislativeActive(Boolean isLoadAll) {
        List<LegislativeProgram> lstLegislative = legislativeProgramRepository.getLegislativeProgramActive(isLoadAll);
        List<LegislativeSearchResponse> lstResponse = new ArrayList<>();
        for (LegislativeProgram legislative : lstLegislative) {
            LegislativeSearchResponse response = new LegislativeSearchResponse();
            response.setId(legislative.getId());
            response.setName(legislative.getName());
            response.setType(legislative.getType());
            response.setStatus(legislative.getStatus());
            lstResponse.add(response);
        }
        return lstResponse;
    }

    private List<BpmProcdefDto> loadInitialProcdef() {
        BpmProcdefDto bpmProcdefDto = new BpmProcdefDto();
        bpmProcdefDto.setSortBy("createdDate");
        bpmProcdefDto.setSortType("DESC");
        bpmProcdefDto.setName("");
        bpmProcdefDto.setLimit(999999);
        bpmProcdefDto.setPage(1);
        bpmProcdefDto.setSpecialFlow(false);
        bpmProcdefDto.setIsShared(false);
        bpmProcdefDto.setSize(10);
        bpmProcdefDto.setSearch("");
        return bpmProcdefManager.searchForLegislative(bpmProcdefDto);
    }

    private List<Map<String, Object>> loadInitialProcinst() {
        List<Tuple> tuples = legislativeProgramRepository.initialCreateTicket(credentialHelper.getJWTPayload().getUsername());
        List<Map<String, Object>> result = new ArrayList<>();
        for (Tuple tuple : tuples) {
            Map<String, Object> item = new HashMap<>();
            item.put("ticketTitle", tuple.get("ticketTitle", String.class));
            item.put("ticketId", tuple.get("ticketId", Long.class));
            item.put("requestCode", tuple.get("requestCode", String.class));
            item.put("procInstId", tuple.get("procInstId", String.class));
            item.put("procDefId", tuple.get("procDefId", String.class));
            item.put("ticketStatus", tuple.get("ticketStatus", String.class));
            item.put("startKey", tuple.get("startKey", String.class));
            result.add(item);
        }

        return result;
    }

    @Override
    public InitialLegislativeResponse loadInit() {
        InitialLegislativeResponse result = new InitialLegislativeResponse();
        try {
            result.setListTicket(loadInitialProcinst());
            result.setListBpmProcdef(loadInitialProcdef());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    @Override
    public List<LegislativePercentChartResponse> getPercentChart(LegislativeDashboardRequest request) {
        String username = credentialHelper.getJWTPayload().getUsername();
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
        request.setListCompanyCode(lstCompanyCode);

        return legislativeSpecification.searchPercentChart(request);
    }

    @Override
    public List<RankTicketResponse> getRankTickets(LegislativeDashboardRequest request) {
        String username = credentialHelper.getJWTPayload().getUsername();
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
        request.setListCompanyCode(lstCompanyCode);

        return legislativeSpecification.searchRankTickets(request);
    }

    @Override
    public PageDto getReport(ReportRequest request) {
        int pageNum = request.getPage() - 1;
        Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());

        String username = credentialHelper.getJWTPayload().getUsername();
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

        Page<LegislativeProgram> page = legislativeProgramRepository.findAll(
                legislativeSpecification.searchReport(request, lstCompanyCode),
                PageRequest.of(pageNum, request.getLimit(), sort)
        );

        List<ReportResponse> lstResponse = new ArrayList<>();
        if (!ValidationUtils.isNullOrEmpty(page.getContent())) {
            List<Long> lstLegislativeId = page.getContent().stream().map(LegislativeProgram::getId).toList();
            // bỏ qua status DEACTIVE (task ko trong luồng)
            List<LegislativeProgramDetail> lstDetail = legislativeProgramDetailRepository.findLegislativeProgramDetailByLegislativeIdInAndType(lstLegislativeId, LegislativeEnum.LegislativeDetailType.PROCESS_DETAIL.code);
            // gom detail theo legislativeId
            Map<Long, List<LegislativeProgramDetail>> detailMap = new HashMap<>();
            for (LegislativeProgramDetail d : lstDetail) {
                detailMap.computeIfAbsent(d.getLegislativeId(), k -> new ArrayList<>()).add(d);
            }
            List<LegislativeProgramDetail> lstFileDetail = legislativeProgramDetailRepository.findLegislativeProgramDetailByLegislativeIdInAndType(lstLegislativeId, LegislativeEnum.LegislativeDetailType.FILE.code);
            Map<Long, List<LegislativeProgramDetail>> fileDetailMap = new HashMap<>();
            for (LegislativeProgramDetail d : lstFileDetail) {
                fileDetailMap.computeIfAbsent(d.getLegislativeId(), k -> new ArrayList<>()).add(d);
            }
            page.getContent().forEach(legislative -> {
                ReportResponse response = new ReportResponse();
                response.setId(legislative.getId());
                response.setName(legislative.getName());
                response.setType(legislative.getType());
                response.setStatus(legislative.getStatus());
                response.setResponsibleAgency(legislative.getResponsibleAgency());
                response.setReleaseYear(legislative.getReleaseYear());
                response.setApprovalSession(legislative.getApprovalSession());
                response.setProcessType(legislative.getProcessType());
                if (legislative.getEstimatedTime() != null) {
                    LocalDate estimatedDate = LocalDate.parse(legislative.getEstimatedTime());
                    LocalDate createdDate = legislative.getCreatedTime().toLocalDate();
                    response.setSlaFinishTime(ChronoUnit.DAYS.between(createdDate, estimatedDate));
                }
                if (legislative.getActivityStatus().equals(LegislativeEnum.LegislativeActivityStatus.COMPLETED.code)) {
                    response.setProcessTime(legislative.getProcessTime());
                } else if (legislative.getActivityStatus().equals(LegislativeEnum.LegislativeActivityStatus.CANCEL.code)) {
                    response.setProcessTime(ChronoUnit.DAYS.between(legislative.getCancelTime().toLocalDate(), legislative.getCreatedTime().toLocalDate()));
                } else {
                    response.setProcessTime(ChronoUnit.DAYS.between(legislative.getCreatedTime().toLocalDate(), LocalDate.now()));
                }
                List<LegislativeProgramDetail> lstFileDetailById = fileDetailMap.get(legislative.getId());
                if (!ValidationUtils.isNullOrEmpty(lstFileDetailById)) {
                    List<FileModel> lstFile = new ArrayList<>();
                    lstFileDetailById.forEach(detail -> {
                        FileModel file = new FileModel();
                        file.setFileName(detail.getFileName());
                        file.setFileUrl(detail.getFileUrl());
                        lstFile.add(file);
                    });
                    response.setFiles(lstFile);
                    List<String> lstFileNames = lstFile.stream().map(FileModel::getFileName).toList();
                    response.setListFileName(lstFileNames);
                }

                List<LegislativeProgramDetail> lstDetailByLegislativeId = detailMap.get(legislative.getId());
                if (!ValidationUtils.isNullOrEmpty(lstDetailByLegislativeId)) {
                    List<ReportDetailResponse> lstDetailResponse = new ArrayList<>();
                    lstDetailByLegislativeId.forEach(x -> {
                        ReportDetailResponse detailResponse = new ReportDetailResponse();
                        detailResponse.setId(x.getId());
                        detailResponse.setTaskName(x.getTaskName());
                        detailResponse.setResponsibleAgency(legislative.getResponsibleAgency());
                        detailResponse.setFromDate(x.getFromDate());
                        detailResponse.setToDate(x.getToDate());
                        detailResponse.setStatus(x.getStatus());
                        if (legislative.getTicketCreatedTime() != null) {
                            LocalDate toDate = LocalDate.parse(x.getToDate());
                            LocalDate createdDate = legislative.getTicketCreatedTime().toLocalDate();
                            detailResponse.setSlaFinishTime(ChronoUnit.DAYS.between(createdDate, toDate));
                            long processTime;
                            if (x.getStatus().equals(LegislativeEnum.LegislativeDetailStatus.WAITING.code)) {
                                processTime = 0L;
                            } else if (x.getStatus().equals(LegislativeEnum.LegislativeDetailStatus.COMPLETED.code)
                                    || x.getStatus().equals(LegislativeEnum.LegislativeDetailStatus.CANCEL.code)) {
                                processTime = ChronoUnit.DAYS.between(createdDate, x.getFinishTime().toLocalDate());
                            } else {
                                processTime = ChronoUnit.DAYS.between(createdDate, LocalDate.now());
                            }
                            detailResponse.setProcessTime(processTime);
                        }
                        lstDetailResponse.add(detailResponse);
                    });
                    response.setDetails(lstDetailResponse);
                    List<String> lstTaskName = lstDetailByLegislativeId.stream().map(LegislativeProgramDetail::getTaskName).toList();
                    response.setListTaskName(lstTaskName);
                }
                lstResponse.add(response);
            });
        }

        return PageDto.builder().content(lstResponse)
                .number(page.getNumber() + 1)
                .numberOfElements(page.getNumberOfElements())
                .page(page.getNumber() + 1)
                .limit(page.getSize())
                .totalPages(page.getTotalPages())
                .totalElements(page.getTotalElements())
                .sortBy(request.getSortBy())
                .sortType(request.getSortType())
                .build();
    }

    @Override
    public List<ReportResponse> getFilterReport(ReportRequest request) {
        int pageNum = request.getPage() - 1;
        Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());

        String username = credentialHelper.getJWTPayload().getUsername();
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

        Page<LegislativeProgram> page = legislativeProgramRepository.findAll(
                legislativeSpecification.searchReport(request, lstCompanyCode),
                PageRequest.of(pageNum, request.getLimit(), sort)
        );

        List<ReportResponse> lstResponse = new ArrayList<>();
        if (!ValidationUtils.isNullOrEmpty(page.getContent())) {
            List<Long> lstLegislativeId = page.getContent().stream().map(LegislativeProgram::getId).toList();
            // bỏ qua status DEACTIVE (task ko trong luồng)
            List<LegislativeProgramDetail> lstDetail = legislativeProgramDetailRepository.findLegislativeProgramDetailByLegislativeIdInAndType(lstLegislativeId, LegislativeEnum.LegislativeDetailType.PROCESS_DETAIL.code);
            // gom detail theo legislativeId
            Map<Long, List<LegislativeProgramDetail>> detailMap = new HashMap<>();
            for (LegislativeProgramDetail d : lstDetail) {
                detailMap.computeIfAbsent(d.getLegislativeId(), k -> new ArrayList<>()).add(d);
            }
            List<LegislativeProgramDetail> lstFileDetail = legislativeProgramDetailRepository.findLegislativeProgramDetailByLegislativeIdInAndType(lstLegislativeId, LegislativeEnum.LegislativeDetailType.FILE.code);
            Map<Long, List<LegislativeProgramDetail>> fileDetailMap = new HashMap<>();
            for (LegislativeProgramDetail d : lstFileDetail) {
                fileDetailMap.computeIfAbsent(d.getLegislativeId(), k -> new ArrayList<>()).add(d);
            }
            page.getContent().forEach(legislative -> {
                ReportResponse response = new ReportResponse();
                response.setId(legislative.getId());
                response.setName(legislative.getName());
                response.setType(legislative.getType());
                response.setStatus(legislative.getStatus());
                response.setResponsibleAgency(legislative.getResponsibleAgency());
                response.setReleaseYear(legislative.getReleaseYear());
                response.setApprovalSession(legislative.getApprovalSession());
                response.setProcessType(legislative.getProcessType());
                if (legislative.getEstimatedTime() != null) {
                    LocalDate estimatedDate = LocalDate.parse(legislative.getEstimatedTime());
                    LocalDate createdDate = legislative.getCreatedTime().toLocalDate();
                    response.setSlaFinishTime(ChronoUnit.DAYS.between(createdDate, estimatedDate));
                }
                if (legislative.getActivityStatus().equals(LegislativeEnum.LegislativeActivityStatus.COMPLETED.code)) {
                    response.setProcessTime(legislative.getProcessTime());
                } else if (legislative.getActivityStatus().equals(LegislativeEnum.LegislativeActivityStatus.CANCEL.code)) {
                    response.setProcessTime(ChronoUnit.DAYS.between(legislative.getCancelTime().toLocalDate(), legislative.getCreatedTime().toLocalDate()));
                } else {
                    response.setProcessTime(ChronoUnit.DAYS.between(legislative.getCreatedTime().toLocalDate(), LocalDate.now()));
                }
                List<LegislativeProgramDetail> lstFileDetailById = fileDetailMap.get(legislative.getId());
                if (!ValidationUtils.isNullOrEmpty(lstFileDetailById)) {
                    List<FileModel> lstFile = new ArrayList<>();
                    List<String> lstFileName = lstFileDetailById.stream().map(LegislativeProgramDetail::getFileName).toList();
                    lstFileDetailById.forEach(detail -> {
                        FileModel file = new FileModel();
                        file.setFileName(detail.getFileName());
                        file.setFileUrl(detail.getFileUrl());
                        lstFile.add(file);
                    });
                    response.setFiles(lstFile);
                    response.setListFileName(lstFileName);
                }

                List<LegislativeProgramDetail> lstDetailByLegislativeId = detailMap.get(legislative.getId());
                if (!ValidationUtils.isNullOrEmpty(lstDetailByLegislativeId)) {
                    List<ReportDetailResponse> lstDetailResponse = new ArrayList<>();
                    lstDetailByLegislativeId.forEach(x -> {
                        ReportDetailResponse detailResponse = new ReportDetailResponse();
                        detailResponse.setId(x.getId());
                        detailResponse.setTaskName(x.getTaskName());
                        detailResponse.setResponsibleAgency(legislative.getResponsibleAgency());
                        detailResponse.setFromDate(x.getFromDate());
                        detailResponse.setToDate(x.getToDate());
                        detailResponse.setStatus(x.getStatus());
                        if (legislative.getTicketCreatedTime() != null) {
                            LocalDate toDate = LocalDate.parse(x.getToDate());
                            LocalDate createdDate = legislative.getTicketCreatedTime().toLocalDate();
                            detailResponse.setSlaFinishTime(ChronoUnit.DAYS.between(createdDate, toDate));
                            long processTime;
                            if (x.getStatus().equals(LegislativeEnum.LegislativeDetailStatus.WAITING.code)) {
                                processTime = 0L;
                            } else if (x.getStatus().equals(LegislativeEnum.LegislativeDetailStatus.COMPLETED.code)
                                    || x.getStatus().equals(LegislativeEnum.LegislativeDetailStatus.CANCEL.code)) {
                                processTime = ChronoUnit.DAYS.between(createdDate, x.getFinishTime().toLocalDate());
                            } else {
                                processTime = ChronoUnit.DAYS.between(createdDate, LocalDate.now());
                            }
                            detailResponse.setProcessTime(processTime);
                        }
                        lstDetailResponse.add(detailResponse);
                    });
                    response.setDetails(lstDetailResponse);
                    List<String> lstTaskName = lstDetailByLegislativeId.stream().map(LegislativeProgramDetail::getTaskName).toList();
                    response.setListTaskName(lstTaskName);
                }
                lstResponse.add(response);
            });
        }

        return lstResponse;
    }
}
