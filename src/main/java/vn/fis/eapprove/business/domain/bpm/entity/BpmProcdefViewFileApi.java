package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "bpm_procdef_view_file_api")
@Data
public class BpmProcdefViewFileApi {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "url")
    private String url;

    @Column(name = "body")
    private String body;

    @Column(name = "header")
    private String header;

    @Column(name = "button_name")
    private String buttonName;

    @Column(name = "method")
    private String method;

    @Column(name = "task_def_key")
    private String taskDefKey;

    @Column(name = "status")
    private Boolean status;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "updated_user")
    private String updatedUser;

    @Column(name = "bpm_procdef_id")
    private Long bpmProcdefId;

    @Column(name = "proc_def_id")
    private String procDefId;

    @Column(name = "response")
    private String response;

    @Column(name = "base_url")
    private String baseUrl;

    @Column(name = "display_condition")
    private String displayCondition;

    @Column(name = "show_button_in_create_ticket")
    private Boolean showButtonInCreateTicket;

}
