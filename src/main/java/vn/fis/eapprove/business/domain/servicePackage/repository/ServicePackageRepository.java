package vn.fis.eapprove.business.domain.servicePackage.repository;

import jakarta.persistence.Tuple;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.dto.BudgetServiceDto;
import vn.fis.eapprove.business.model.response.ChildResponse;


import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;

@Repository
public interface ServicePackageRepository extends JpaRepository<ServicePackage, Long>, JpaSpecificationExecutor<ServicePackage> {

    List<ServicePackage> findAllByMasterParentIdInAndDeletedFalseOrIdInAndDeletedFalse(Set<Long> ids, Set<Long> idps);

    List<ServicePackage> findAllByMasterParentIdInAndDeletedFalseAndNotShowingWebsiteFalseOrIdInAndDeletedFalseAndNotShowingWebsiteFalse(Set<Long> ids, Set<Long> idps);

    List<ServicePackage> findAllByMasterParentIdInAndDeletedFalseAndNotShowingMoblieFalseOrIdInAndDeletedFalseAndNotShowingMoblieFalse(Set<Long> ids, Set<Long> idps);

    List<ServicePackage> findAllByParentIdInAndDeletedFalse(Set<Long> ids);

    List<ServicePackage> findAllByParentIdInAndDeletedFalseAndNotShowingWebsiteFalse(Set<Long> ids);

    List<ServicePackage> findAllByParentIdInAndDeletedFalseAndNotShowingMoblieFalse(Set<Long> ids);

    @Query(value = "SELECT sp, bp.procDefId FROM ServicePackage sp " +
            "INNER JOIN BpmProcdef bp ON sp.processId = bp.id " +
            "WHERE (:isSpecialFlow = false AND sp.specialParentId IS NULL AND sp.serviceName LIKE CONCAT('%', :search, '%')) " +
            "OR (:isSpecialFlow = true AND sp.specialParentId IS NOT NULL AND sp.serviceName LIKE CONCAT('%', :search, '%'))")
    Page<Object> getAllSearch(@Param("search") String search, Pageable pageable, @Param("isSpecialFlow") Boolean isSpecialFlow);


    @Query(value = "SELECT sp FROM ServicePackage sp " +
            "INNER JOIN BpmProcInst bp ON sp.id = bp.serviceId " +
            "where sp.id = :id")
    List<ServicePackage> checkServiceTicket(@Param("id") Long id);

    List<ServicePackage> getAllByParentIdInAndDeletedFalseAndServiceTypeIn(List<Long> parentIds, List<Integer> serviceTypes);

    @Query("SELECT c FROM ServicePackage c WHERE c.id = :id")
    ServicePackage getServicePackagetById(@Param("id") Long id);

    @Query("SELECT c FROM ServicePackage c " +
            "WHERE lower(c.serviceName) = lower(:serviceName)  " +
            "and (c.id <> :id or :id = 0) " +
            "and c.idOrgChart =:idOrgChart " +
            "and  c.deleted = (:deleted) " +
            "and c.serviceType =(:serviceType) " +
            "and ((:parentId is null and c.parentId is null) or (:parentId is not null and c.parentId = :parentId))")
    List<ServicePackage> CheckExistName(@Param("serviceName") String serviceName,
                                        @Param("id") long id,
                                        @Param("idOrgChart") long idOrgChart,
                                        @Param("deleted") Boolean deleted,
                                        @Param("parentId") Long parentId,
                                        @Param("serviceType") Integer serviceType);

    @Query("SELECT c FROM ServicePackage c WHERE c.serviceName = lower(:serviceName)  and (c.id <> :id or :id = 0)")
    ServicePackage CheckExistName(@Param("serviceName") String serviceName, @Param("id") long id);

    @Query("SELECT s.id,(SELECT count(sp.id) FROM ServicePackage sp where sp.parentId = s.id) as child FROM ServicePackage s " +
            "WHERE s.id in :lsId " +
            "GROUP BY s.id")
    List<Object[]> countChildByLsId(List<Long> lsId);

    @Query("SELECT s FROM ServicePackage s WHERE s.parentId = :parentId AND s.idOrgChart = :idOrgChart AND ((:check = true AND s.notShowingWebsite = false) or (:check = false AND s.notShowingMoblie = false )) AND s.deleted = false")
    List<ServicePackage> getServicePackageByParentIdAndIdOrgChart(@Param("parentId") Long parentId, @Param("idOrgChart") Long idOrgChart, @Param("check") Boolean check);

    ServicePackage getServicePackageById(Long id);

    List<ServicePackage> findServicePackageById(Long id);

    List<ServicePackage> getServicePackageByServiceNameContainingIgnoreCase(String serviceName);

    List<ServicePackage> getServicePackagesByServiceNameIn(List<String> listServiceName);

    List<ServicePackage> getServicePackagesByProcessIdAndDeleted(Long proDefId, Boolean deleted);

    Long countByProcessIdAndDeleted(Long processId, Boolean deleted);

    List<ServicePackage> getAllByIdIn(List<Long> lsId);

    @Query("SELECT DISTINCT c.serviceName from ServicePackage  c where c.deleted=false  and c.status='ACTIVE'")
    List<String> getAllServiceActive();

    @Query("select sp from ServicePackage sp " +
            "where lower(sp.serviceName) like lower(concat('%',:search,'%')) and sp.idOrgChart = :chartId ")
    List<ServicePackage> searchServiceMyReport(@Param("search") String search, @Param("chartId") Long chartId);

    //   ServicePackage findByParentId(Long parentId);
    List<ServicePackage> findByIdIn(List<Long> id);


    @Query("select u.id,u.serviceName from ServicePackage u")
    List<Object[]> getServiceNameAndId();

    @Query("select new vn.fis.eapprove.business.dto.BudgetServiceDto(u.id,u.serviceName,u.idOrgChart,u.visibleChart,u.hideChart) " +
            "from ServicePackage u where u.serviceType = 2 " +
            "and u.processId is not null " +
            "and u.deleted is false")
    List<BudgetServiceDto> getServiceNameAndIds();

    Optional<ServicePackage> findTopByServiceNameLikeOrderByIdDesc(String serviceName);

    Stream<ServicePackage> findByParentIdIsNotNull();

    List<ServicePackage> findByProcessIdAndStatusAndDeletedIsFalse(Long processId, String status);

    Stream<ServicePackage> findByParentIdAndDeletedFalse(Long parentId);

    Stream<ServicePackage> findByMasterParentIdAndDeletedFalse(Long masterParentId);

    @Query("select distinct sp from ServicePackage sp " +
            "JOIN BpmProcInst b on sp.id = b.serviceId")
    Page<ServicePackage> getServicePackage(Pageable pageable);

    List<ServicePackage> findByParentId(Long parentId);

    List<ServicePackage> findBySpecialParentId(Long parentId);

    @Query(value = "SELECT * FROM service_package sp WHERE JSON_CONTAINS(sp.parent_service_id, :parentServiceId)", nativeQuery = true)
    List<ServicePackage> findByParentServiceId(@Param("parentServiceId") String parentServiceId);

    @Query(value = " select * from service_package sp " +
            "left join bpm_procdef bf on sp.process_id=bf.id " +
            "where sp.deleted = false " +
            "and ( :visibleAllUser is null or sp.visible_all_user = :visibleAllUser) " +
            "and ( :id is null or :id = 0 or sp.id = :id )" +
            "and ( :processName is null or :processName ='' or replace(bf.name,' ','') LIKE concat('%',(replace(:processName,' ','')),'%'))" +
            "and ( :chartId is null or :chartId = 0 or sp.id_org_chart = :chartId) " +
            "and ( :serviceName is null or :serviceName ='' or replace(sp.service_name,' ','') LIKE concat('%',(replace(:serviceName,' ','')),'%'))" +
            "and ( :serviceTypes is null or sp.service_type in :serviceTypes) " +
            "and ( :notShowingWeb is null or sp.not_showing_website = :notShowingWeb) " +
            "and ( :notShowingMobile is null or sp.not_showing_moblie = :notShowingMobile) " +
            "and ( :createdUser is null or sp.created_user in :createdUser) " +
            "and ( :status is null or :status='' or lower(sp.status) = lower(:status)) " +
            "and ( :fromDate is null or :fromDate='' or sp.created_date >= STR_TO_DATE(:fromDate, '%Y-%m-%d %H:%i:%s'))" +
            "and ( :toDate is null or :toDate='' or sp.created_date <= STR_TO_DATE(:toDate, '%Y-%m-%d %H:%i:%s'))",
            nativeQuery = true)
    List<ServicePackage> findByFilter(@Param("visibleAllUser") Boolean visibleAllUser,
                                      @Param("id") Long id,
                                      @Param("processName") String processName,
                                      @Param("chartId") Long chartId,
                                      @Param("serviceName") String serviceName,
                                      @Param("serviceTypes") List<Integer> serviceTypes,
                                      @Param("notShowingWeb") Boolean notShowingWeb,
                                      @Param("notShowingMobile") Boolean notShowingMobile,
                                      @Param("createdUser") List<String> createdUser,
                                      @Param("status") String status,
                                      @Param("fromDate") String fromDate,
                                      @Param("toDate") String toDate
    );

    @Query("select sp.id from ServicePackage sp " +
            "where lower(sp.serviceName) like lower(concat('%',:search,'%')) " +
            "AND sp.deleted is false ")
    List<String> getListIdsByName(@Param("search") String search);

    @Query("select sp from ServicePackage sp where sp.submissionType = :id")
    List<ServicePackage> findSubmissionType(List<Long> id);

    @Query("select sp.id as id,sp.serviceName as serviceName from ServicePackage sp")
    List<Map<String, Object>> loadFilterService();

//    // Sử dụng @Query để định nghĩa native query với điều kiện username optional
//    @Query("SELECT sp FROM ServicePackage sp WHERE (sp.serviceName=:name)")
//    List<ServicePackage> findByServicePackageName(String username);

    @Query("select s from ServicePackage s " +
            "where s.parentId is null and s.deleted = false " +
            "and JSON_EXTRACT(s.visibleChart,'$[0]') is not null " +
            "and lower(s.serviceName) = lower(:serviceName) " +
            "and s.id != :id")
    List<ServicePackage> getAllParentServicePackage(String serviceName, Long id);

    List<ServicePackage> getServicePackagesBySpecialParentId(Long parentId);

    ServicePackage findServicePackageBySpecialParentIdAndSpecialCompanyCode(Long parentId, String companyCode);

    List<ServicePackage> getServicePackagesBySpecialParentIdAndSpecialCompanyCodeIn(Long parentId, List<String> lstCompanyCode);

    @Query("SELECT COALESCE(s.specialParentId,s.id) FROM ServicePackage s WHERE " +
            "s.id = :serviceId ")
    Long findIdParentServicePackage(Long serviceId);

    @Query("SELECT new vn.fis.eapprove.business.model.response.ChildResponse(t.specialParentId,t.id,t.specialCompanyCode,'','') FROM ServicePackage t " +
            "where t.specialParentId in (:parentIds) " +
            "AND t.specialFlow = true")
    List<ChildResponse> findSpecialFlowChildDataByParentIds(List<Long> parentIds);

    @Query(nativeQuery = true, value = "WITH RECURSIVE Children AS (select sp.* " +
            "                            from service_package sp " +
            "                            where id in (:ids) " +
            "                            UNION ALL " +
            "                            select sp.* " +
            "                            from service_package sp " +
            "                                     inner join Children c on c.id = sp.parent_id) " +
            "SELECT distinct * " +
            "FROM Children")
    List<ServicePackage> getListChildServicePackageByParentId(@Param("ids") List<Long> parentId);

    List<ServicePackage> getAllBySubmissionTypeIn(List<Long> submissionType);

    @Query(value = "select CONCAT(id,' - ',service_name) from service_package where id in (:ids)", nativeQuery = true)
    List<String> getListNameByIds(@Param("ids") List<Long> ids);

    @Query(value = "select distinct id as id, service_name as name from service_package where id in (:ids)", nativeQuery = true)
    List<Tuple> getListIdAndNameByIds(@Param("ids") List<Long> ids);
}

