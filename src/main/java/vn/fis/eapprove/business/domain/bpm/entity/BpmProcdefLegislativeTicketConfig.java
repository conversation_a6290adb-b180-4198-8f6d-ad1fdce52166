package vn.fis.eapprove.business.domain.bpm.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.simpleframework.xml.Transient;
import org.simpleframework.xml.core.Persist;

import java.time.LocalDateTime;

@Entity
@Table(name = "bpm_procdef_legislative_ticket_config")
@Data
public class BpmProcdefLegislativeTicketConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "ticket_field")
    private String ticketField;

    @Column(name = "legislative_field")
    private String legislativeField;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "updated_user")
    private String updatedUser;

    @Column(name = "bpm_procdef_id")
    private Long bpmProcdefId;

    @Column(name = "proc_def_id")
    private String procDefId;

    @Column(name = "status")
    private Boolean status;
}
