package vn.fis.eapprove.business.domain.legislative.model.mapper;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LegislativeDetailModel {
    private Long id;
    private String taskDefKey; //taskKey chi tiết nhiệm vụ
    private String taskName; //taskName chi tiết nhiệm vụ
    private String fromDate;
    private String toDate;
    private String processType; // Loại quy trình: normal-Thông thường, shortcut-Rút gọn
    private String companyCode; // <PERSON><PERSON> quan thực hiện
    private String companyName; // <PERSON><PERSON> quan thực hiện
    private String status; // trạng thái chi tiết nhiệm vụ
}
