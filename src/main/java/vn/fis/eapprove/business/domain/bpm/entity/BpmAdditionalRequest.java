package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "bpm_additional_request")
public class BpmAdditionalRequest {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "proc_inst_id", length = 64)
    private String procInstId;

    @Column(name = "start_user_id", length = 50)
    private String startUserId;

    @Column(name = "content_request", length = 200)
    private String contentRequest;

    @Column(name = "auto_return")
    private Integer autoReturn;

    @Column(name = "is_auto")
    private Boolean isAuto;

    @Column(name = "reminder_date")
    private LocalDateTime reminderDate;

    @Column(name = "expired_date")
    private LocalDateTime expiredDate;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "created_user", length = 50)
    private String createdUser;

    @Column(name = "modified_date")
    private LocalDateTime modifiedDate;

    @Column(name = "modified_user", length = 50)
    private String modifiedUser;

    @PrePersist
    public void prePersist() {
        setCreatedDate(LocalDateTime.now());
        setModifiedDate(LocalDateTime.now());
    }

    @PreUpdate
    public void preUpdate() {
        setModifiedDate(LocalDateTime.now());
    }

}