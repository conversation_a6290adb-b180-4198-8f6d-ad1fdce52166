package vn.fis.eapprove.business.domain.sharedUser.entity;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import vn.fis.eapprove.business.domain.submission.entity.SubmissionType;
import vn.fis.eapprove.business.domain.template.entity.TemplateManage;
import vn.fis.eapprove.business.domain.api.entity.ApiManagement;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrint;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;

import jakarta.persistence.*;


/**
 * Generated by Speed Generator
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 */
@Getter
@Setter
@Entity
@Table(name = "shared_user")
public class SharedUser {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "reference_id", nullable = false)
    private Long referenceId;

    @Column(name = "reference_type", nullable = false)
    private String referenceType;

    @Column(name = "email", nullable = false)
    private String email;

    @Column(name = "addition")
    private String addition;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reference_id", insertable = false, updatable = false)
    @JsonIgnore
    private TemplateManage templateManage;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reference_id", insertable = false, updatable = false)
    @JsonIgnore
    private BpmProcdef bpmProcdef;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reference_id", insertable = false, updatable = false)
    @JsonIgnore
    private BpmTemplatePrint bpmTemplatePrint;

//    @ManyToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "reference_id", insertable = false, updatable = false)
//    @JsonIgnore
//    private MasterData masterData;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reference_id", insertable = false, updatable = false)
    @JsonIgnore
    private PriorityManagement priorityManagement;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reference_id", insertable = false, updatable = false)
    @JsonIgnore
    private ApiManagement apiManagement;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reference_id", insertable = false, updatable = false)
    @JsonIgnore
    private SubmissionType submissionType;

}