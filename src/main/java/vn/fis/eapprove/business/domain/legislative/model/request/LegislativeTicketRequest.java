package vn.fis.eapprove.business.domain.legislative.model.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LegislativeTicketRequest {
    private Long id; // legislativeId
    private Long ticketId;
    private String taskDefKey;
    private String taskStatus;
    private String actionCode;
    private String username;
    // requestUpdate...
    private String procInstId;
    private String startKey;
    private String ticketTitle;
    private String ticketStatus;
    private String procDefId;
}
