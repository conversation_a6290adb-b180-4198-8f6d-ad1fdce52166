package vn.fis.eapprove.business.domain.dashboard.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcInstRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTaskRepository;
import vn.fis.eapprove.business.domain.changeAssignee.repository.ChangeAssigneeHistoryRepository;
import vn.fis.eapprove.business.domain.dashboard.entity.DashboardTask;
import vn.fis.eapprove.business.domain.dashboard.repository.DashboardTaskRepository;
import vn.fis.eapprove.business.domain.dashboard.service.DashboardTaskService;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;
import vn.fis.eapprove.business.domain.priority.repository.PriorityManagementRepository;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.repository.ServicePackageRepository;
import vn.fis.eapprove.business.dto.DashboardTaskDto;
import vn.fis.eapprove.business.mapper.DashboardTaskMapper;
import vn.fis.eapprove.business.model.response.UserInfoByUsername;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class DashboardTaskServiceImpl implements DashboardTaskService {

    private final DashboardTaskRepository dashboardTaskRepository;
    private final BpmProcInstRepository bpmProcInstRepository;
    private final BpmTaskRepository bpmTaskRepository;
    private final ServicePackageRepository servicePackageRepository;
    private final PriorityManagementRepository priorityManagementRepository;
    private final ChangeAssigneeHistoryRepository changeAssigneeHistory;
    private final CustomerService customerService;
    private final DashboardTaskMapper mapper;

    @Override
    public void createDashboardTask(String taskId) {
        try {
            if (!isTaskExist(taskId)) {
                log.info("Insert dashboard task = {}", taskId);
                insert(taskId);
            } else {
                log.info("Update dashboard task = {}", taskId);
                update(taskId);
            }

        } catch (Exception e) {
            log.error("Error createDashboardTask = {}", taskId, e);
        }
    }

    private void insert(String taskId) {
        DashboardTaskDto dashboardTaskDto = mapToDashboardTaskDto(taskId);
        if (dashboardTaskDto == null || ValidationUtils.isNullOrEmpty(dashboardTaskDto.getAssigneeChartNodeId())) {
            return;
        }
        DashboardTask dashboardTask = mapper.to(dashboardTaskDto);
        dashboardTaskRepository.save(dashboardTask);
    }

    private void update(String taskId) {
        DashboardTaskDto dashboardTaskDto = mapToDashboardTaskDto(taskId);
        if (dashboardTaskDto == null || ValidationUtils.isNullOrEmpty(dashboardTaskDto.getAssigneeChartNodeId())) {
            return;
        }
        DashboardTask existed = dashboardTaskRepository.getDashboardTaskByTaskId(taskId);
        DashboardTask update = mapper.merge(existed, dashboardTaskDto);
        dashboardTaskRepository.save(update);
    }

    private boolean isTaskExist(String taskId) {
        DashboardTask existed = dashboardTaskRepository.getDashboardTaskByTaskId(taskId);
        return existed != null;
    }

    private DashboardTaskDto mapToDashboardTaskDto(String bpmTaskId) {
        DashboardTaskDto dashboardTaskDto = new DashboardTaskDto();

        BpmTask bpmTask = bpmTaskRepository.getBpmTaskByTaskId(bpmTaskId);

        if (bpmTask == null) {
            return null;
        }

        BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByProcInstId(bpmTask.getTaskProcInstId());

        if (bpmProcInst == null) {
            return null;
        }

        dashboardTaskDto.setTaskId(bpmTask.getTaskId());
        dashboardTaskDto.setTaskName(bpmTask.getTaskName());
        dashboardTaskDto.setTaskType(bpmTask.getTaskType());
        dashboardTaskDto.setTaskStatus(bpmTask.getTaskStatus());
        dashboardTaskDto.setProcInstId(bpmTask.getTaskProcInstId());
        if (bpmProcInst.getServiceId() != null) {
            Optional<ServicePackage> servicePackage = servicePackageRepository.findById(bpmProcInst.getServiceId());
            if (servicePackage.isPresent()) {
                dashboardTaskDto.setServiceId(bpmProcInst.getServiceId());
                dashboardTaskDto.setServiceName(servicePackage.get().getServiceName());
                dashboardTaskDto.setSubmissionType(servicePackage.get().getSubmissionType());
                dashboardTaskDto.setMasterParentId(servicePackage.get().getMasterParentId());
            }
        }
        dashboardTaskDto.setRequestCode(bpmProcInst.getRequestCode());
        dashboardTaskDto.setTitle(bpmProcInst.getTicketTitle());
        dashboardTaskDto.setPriorityId(bpmProcInst.getPriorityId());
        if (bpmProcInst.getPriorityId() != null) {
            Optional<PriorityManagement> priorityManagement = priorityManagementRepository.findById(bpmProcInst.getPriorityId());
            if (priorityManagement.isPresent()) {
                dashboardTaskDto.setPriorityName(priorityManagement.get().getName());
            } else {
                dashboardTaskDto.setPriorityName("DEFAULT");
            }
        }
        dashboardTaskDto.setLocationId(bpmProcInst.getLocationId());
        dashboardTaskDto.setCreatedTime(bpmTask.getTaskCreatedTime());
        dashboardTaskDto.setSlaFinishTime(bpmTask.getSlaFinishTime());
        dashboardTaskDto.setFinishedTime(bpmTask.getTaskFinishedTime());
        dashboardTaskDto.setStartedTime(bpmTask.getTaskStartedTime());
        dashboardTaskDto.setCancelReason(bpmProcInst.getCancelReason());
        dashboardTaskDto.setTicketId(bpmProcInst.getTicketId());
        dashboardTaskDto.setProcDefId(bpmProcInst.getTicketProcDefId());
        UserInfoByUsername createdUser = customerService.getUserInfoByUsername(bpmProcInst.getCreatedUser());
        if (createdUser == null) {
            return null;
        }
        dashboardTaskDto.setCreatedUser(bpmProcInst.getCreatedUser());
        dashboardTaskDto.setCreatedUserStatus(createdUser.getStatus());
        dashboardTaskDto.setCreatedUserFullName(createdUser.getFullName());
        dashboardTaskDto.setCreatedUserChartId(bpmProcInst.getChartId());
        dashboardTaskDto.setCreatedUserChartShortName(createdUser.getChartShortName());
        dashboardTaskDto.setCreatedUserChartNodeId(createdUser.getChartNodeId());
        dashboardTaskDto.setCreatedUserChartNodeName(createdUser.getChartNodeName());
        dashboardTaskDto.setCreatedUserChartNodeCode(createdUser.getChartNodeCode());
        dashboardTaskDto.setCreatedUserTitleName(createdUser.getTitleName());
        dashboardTaskDto.setCreatedUserStaffCode(createdUser.getStaffCode());
        dashboardTaskDto.setCreatedUserManagerLevel(createdUser.getManagerLevel());
        dashboardTaskDto.setCreatedUserEmail(createdUser.getEmail());
        dashboardTaskDto.setCreatedUserDirectManager(createdUser.getDirectManager());
        // nếu là ủy quyền thì lấy assignee là người tạo task -> lấy theo ng thực hiện task
        if (!ValidationUtils.isNullOrEmpty(bpmTask.getActionUser())) {
            dashboardTaskDto.setAssignee(bpmTask.getActionUser());
            if (setAssigneeInfo(bpmTask.getActionUser(), dashboardTaskDto)) return null;
        } else if (Boolean.TRUE.equals(bpmTask.getAssignType())) {
            String orgAssignee = changeAssigneeHistory.getAssigneeOrgByTaskId(bpmTask.getTaskId());
            dashboardTaskDto.setAssignee(orgAssignee);
            if (setAssigneeInfo(orgAssignee, dashboardTaskDto)) return null;
        } else {
            dashboardTaskDto.setAssignee(bpmTask.getTaskAssignee());
            if (setAssigneeInfo(bpmTask.getTaskAssignee(), dashboardTaskDto)) return null;
        }
        LocalDateTime finishTime = bpmTask.getTaskFinishedTime();
        finishTime = (finishTime == null) ? LocalDateTime.now() : finishTime;
        LocalDateTime estimateTime = bpmTask.getSlaFinishTime();
        dashboardTaskDto.setIsExpire(!finishTime.isBefore(estimateTime));
        return dashboardTaskDto;
    }

    private boolean setAssigneeInfo(String username, DashboardTaskDto dashboardTaskDto) {
        UserInfoByUsername assigneeInfo = customerService.getUserInfoByUsername(username);
        if (assigneeInfo == null) {
            return true;
        }
        dashboardTaskDto.setAssigneeChartNodeId(assigneeInfo.getChartNodeId());
        dashboardTaskDto.setAssigneeChartNodeCode(assigneeInfo.getChartNodeCode());
        dashboardTaskDto.setAssigneeChartNodeName(assigneeInfo.getChartNodeName());
        dashboardTaskDto.setAssigneeChartId(assigneeInfo.getChartId());
        dashboardTaskDto.setAssigneeChartShortName(assigneeInfo.getChartShortName());
        dashboardTaskDto.setAssigneeFullName(assigneeInfo.getFullName());
        dashboardTaskDto.setAssigneeStaffCode(assigneeInfo.getStaffCode());
        dashboardTaskDto.setAssigneeTitleName(assigneeInfo.getTitleName());
        dashboardTaskDto.setAssigneeEmail(assigneeInfo.getEmail());
        dashboardTaskDto.setAssigneeStaffCode(assigneeInfo.getStaffCode());
        dashboardTaskDto.setAssigneeManagerLevel(assigneeInfo.getManagerLevel());
        dashboardTaskDto.setAssigneeDirectManager(assigneeInfo.getDirectManager());
        dashboardTaskDto.setAssigneeStatus(assigneeInfo.getStatus());

        return false;
    }
}
