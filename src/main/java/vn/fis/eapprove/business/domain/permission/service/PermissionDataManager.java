package vn.fis.eapprove.business.domain.permission.service;

import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;

import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.model.request.PermissionDataRequest;
import vn.fis.spro.common.model.response.PermissionDataResponse;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class PermissionDataManager {

    @Autowired
    private PermissionDataManagementRepository permissionDataManagementRepository;

    @Autowired
    private ModelMapper modelMapper;

    @Autowired
    private CredentialHelper credentialHelper;

    public void createPermissionData(PermissionDataRequest data)  {

        if (!ValidationUtils.isNullOrEmpty(data.getTypeId())) {
            List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(data.getTypeId(), data.getTypeName());
            if (!ValidationUtils.isNullOrEmpty(oldData)) {
                permissionDataManagementRepository.deleteAll(oldData);
            }
        }

        if (!ValidationUtils.isNullOrEmpty(data.getCompanyCodes())) {
            List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
            for (String companyCode : data.getCompanyCodes()) {
                PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                permissionDataManagement.setTypeId(data.getTypeId());
                permissionDataManagement.setTypeName(data.getTypeName());
                permissionDataManagement.setCompanyCode(companyCode);
                permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                permissionDataManagement.setCreatedTime(LocalDateTime.now());
                permissionDataManagements.add(permissionDataManagement);
            }

            permissionDataManagementRepository.saveAll(permissionDataManagements);
        }
    }

    public void createAllPermissionData(List<PermissionDataRequest> datas)  {
        List<PermissionDataManagement> deleteList = new ArrayList<>();
        List<PermissionDataManagement> updateList = new ArrayList<>();
        datas.forEach(data->{
            if (!ValidationUtils.isNullOrEmpty(data.getTypeId())) {
                List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(data.getTypeId(), data.getTypeName());
                if (!ValidationUtils.isNullOrEmpty(oldData)) {
                    deleteList.addAll(oldData);
                }
            }

            if (!ValidationUtils.isNullOrEmpty(data.getCompanyCodes())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String companyCode : data.getCompanyCodes()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(data.getTypeId());
                    permissionDataManagement.setTypeName(data.getTypeName());
                    permissionDataManagement.setCompanyCode(companyCode);
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                updateList.addAll(permissionDataManagements);
            }
        });
        permissionDataManagementRepository.deleteAll(deleteList);
        permissionDataManagementRepository.saveAll(updateList);

    }

    public List<PermissionDataResponse> getListPermission(PermissionDataRequest data) {
        List<PermissionDataManagement> lstData;
        if (data.getCompanyCodes().contains(CommonConstants.FILTER_SELECT_ALL)) {
            lstData = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(data.getTypeName());
        } else {
            lstData = permissionDataManagementRepository.findTypeIdByTypeNameAndCompanyCodes(data.getTypeName(), data.getCompanyCodes());
        }
        List<PermissionDataResponse> lstResponse = new ArrayList<>();
        if (!ValidationUtils.isNullOrEmpty(lstData)) {
            lstResponse = lstData.stream()
                    .collect(Collectors.groupingBy(
                            PermissionDataManagement::getTypeId,
                            Collectors.mapping(PermissionDataManagement::getCompanyCode, Collectors.toList())
                    ))
                    .entrySet()
                    .stream()
                    .map(entry -> {
                        PermissionDataResponse result = new PermissionDataResponse();
                        result.setTypeId(entry.getKey());
                        result.setApplyFor(entry.getValue());
                        return result;
                    })
                    .collect(Collectors.toList());
        }
        return lstResponse;
    }

    public List<String> getPermissionById(PermissionDataRequest data) {
        return permissionDataManagementRepository.getApplyForByTypeId(data.getTypeName(), data.getTypeId());
    }
}
