package vn.fis.eapprove.business.domain.legislative.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "legislative_program_detail")
public class LegislativeProgramDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "legislative_program_id", nullable = false)
    private Long legislativeId;

    @Column(name = "type", nullable = false)
    private String type;

    @Column(name = "username")
    private String username;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "file_url")
    private String fileUrl;

    @Column(name = "task_def_key")
    private String taskDefKey;

    @Column(name = "task_name")
    private String taskName;

    @Column(name = "from_date")
    private String fromDate;

    @Column(name = "to_date")
    private String toDate;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "finish_time")
    private LocalDateTime finishTime;

    @Column(name = "status")
    private String status;

    @Column(name = "process_type")
    private String processType;

    @Column(name = "company_code")
    private String companyCode;

    @Column(name = "company_name")
    private String companyName;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "legislative_program_id", referencedColumnName = "id", insertable = false, updatable = false)
    @JsonIgnore
    private LegislativeProgram legislativeProgram;
}
