package vn.fis.eapprove.business.domain.authority.service;

import vn.fis.eapprove.business.dto.AccountInfoDto;

import java.util.List;

public interface AuthService {

    List<String> getListCompanyCodeByUser(String username);
    List<String> getListCompanyCodeMemberAdminByUser(String username);

    List<AccountInfoDto> getAccountByRoleAndUsername();

    // System group
    Object getAllSystemAuthorizationGroup();
    Object getAllPermissionGroup();
}
