package vn.fis.eapprove.business.domain.report.service;

import vn.fis.eapprove.business.domain.report.entity.ReportByChartNodeNew;

import java.util.List;

public interface ReportByChartNodeServiceNew {

    ReportByChartNodeNew insert(String taskId);

    ReportByChartNodeNew update(String taskId);

    ReportByChartNodeNew findByTaskId(String taskId);

    boolean isTaskExist(String taskId);

    void createReportByChartNode(String taskId);

    void syncReportByChartNode(String fromDate, String toDate);

    List<String> getListUserFilter(List<String> usernames);
}
