package vn.fis.eapprove.business.domain.codeGen.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.codeGen.entity.CodeGenStruct;


import java.util.Date;
import java.util.List;

/**
 * Author: AnhVTN
 * Date: 30/03/2023
 */
@Repository
public interface CodeGenStructRepository extends JpaRepository<CodeGenStruct, Long> {
    @Query("select cgs from CodeGenStruct cgs where cgs.codeGenConfigId = :id order by cgs.sortOrder asc")
//    @Lock(LockModeType.PESSIMISTIC_WRITE)
    List<CodeGenStruct> findCodeGenStructByCodeGenConfigId(@Param("id") Long id);

    @Transactional
    @Modifying
    @Query("update CodeGenStruct cgs SET cgs.resetTime= :now,cgs.currentValue = '0' where cgs.id = :id AND  cgs.dataType = 'AUTO_INCREMENT' AND cgs.schedule= :schedule")
    void updateAllCodeGenStructAutoIncrement(Long id, int schedule, Date now);

    @Query("SELECT cgs from CodeGenStruct cgs  where cgs.dataType='AUTO_INCREMENT'" +
            " and cgs.schedule is not null " +
            " and cgs.resetTime is not null ")
    List<CodeGenStruct> listAllAutoCodeGenStruct();

    List<CodeGenStruct> findAllByCodeGenConfigId(Long id);

    @Query("select cs.id from CodeGenStruct cs where cs.dataType = 'AUTO_INCREMENT'")
    List<Long> findAllAutoCodeGenStructIdsByDataType();
}
