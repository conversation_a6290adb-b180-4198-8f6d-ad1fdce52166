package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.*;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "bpm_procdef_notification")
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
//@NamedEntityGraph(
//        name = "BpmProcdefNotification.lstNotifications",
//        attributeNodes = {
//                @NamedAttributeNode("lstNotifications")
//        }
//)
public class BpmProcdefNotification {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "bpm_procdef_id")
    private Long bpmProcdefId;

    @Column(name = "proc_def_id")
    private String procDefId;

    @Column(name = "action_code")
    private String actionCode;

    @Column(name = "notification_object")
    private String notificationObject;

    @Column(name = "notification_template_id")
    private Long notificationTemplateId;

    @Column(name = "task_def_key")
    private String taskDefKey;

    @Column(name = "task_def_key_notification")
    private String taskDefKeyNotification;

    @Column(name = "field_notification")
    private String fieldNotification;

    @Column(name = "status")
    private String status;

    @Column(name = "add_more_config")
    private Boolean addMoreConfig;

    @Column(name = "off_notification")
    private Boolean offNotification;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "updated_user")
    private String updatedUser;

//    @OneToMany(mappedBy = "procdefNotification")
////    @JsonIgnore
//    private Set<BpmProcdefNotificationDetail> lstNotifications;
//    @Transient
//    private String notificationObject;
}
