package vn.fis.eapprove.business.domain.report.service;


import vn.fis.eapprove.business.domain.report.entity.ReportByGroupNew;

public interface ReportByGroupServiceNew {
    ReportByGroupNew insert(Long procInstId);

    ReportByGroupNew update(Long procInstId);

    ReportByGroupNew findByTicketId(Long procInstId);

    boolean isTicketExist(Long procInstId);

    void createReportByGroup(Long procInstId);

    public void syncReportByGroup(String fromDate, String toDate);

    void deleteTaskReturnExist(Long ticketId);

}
