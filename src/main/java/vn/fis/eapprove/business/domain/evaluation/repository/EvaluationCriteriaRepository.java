package vn.fis.eapprove.business.domain.evaluation.repository;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.evaluation.entity.EvaluationCriteria;


import java.util.List;

@Repository
public interface EvaluationCriteriaRepository extends JpaRepository<EvaluationCriteria, Long>, JpaSpecificationExecutor<EvaluationCriteria> {
    @Query("SELECT c FROM EvaluationCriteria c where upper(c.name) = upper(:name) " +
//            "and upper(c.reviewItem) = upper(:reviewItem) " +
            "and c.status = :status")
    List<EvaluationCriteria> getEvaluationCriteriaByNameAndReviewItemAndStatus(@Param("name") String name,
//                                                                               @Param("reviewItem") String reviewItem,
                                                                               @Param("status") String status);

    EvaluationCriteria findFirstByNameAndStatus(String name, String status);

    EvaluationCriteria findEvaluationCriteriaById(Long id);

    @EntityGraph(value = "EvaluationCriteria.evaluationDepartments")
    @Query("SELECT a FROM EvaluationCriteria a "
            + "LEFT JOIN FETCH EvaluationDepartment b "
            + "ON a.id = b.evaluationCriteriaId "
            + "WHERE a.status = :status " +
            " and (:companyCode is null or b.companyCode = :companyCode)")
    List<EvaluationCriteria> getAllDepartmentByReviewAndStatus( @Param("status") String status, @Param("companyCode") String companyCode);

    @Override
    @EntityGraph(attributePaths = {"evaluationDepartments"})
    List<EvaluationCriteria> findAll(Specification<EvaluationCriteria> spec);

    Boolean existsEvaluationCriteriaByReviewItem(String code);
}
