package vn.fis.eapprove.business.domain.system.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.api.entity.ApiLog;
import vn.fis.eapprove.business.domain.system.entity.SystemGroupHistory;


import java.util.List;

@Repository
public interface SystemGroupHistoryRepository extends JpaRepository<SystemGroupHistory, Long>, JpaSpecificationExecutor<ApiLog> {
    @Query("SELECT COALESCE(max(h.version),1) FROM SystemGroupHistory h " +
            "WHERE h.parent = :parent " +
            "AND h.groupType = :groupType ")
    Long findVersionByParentAndGroupType(Long parent,String groupType);

    List<SystemGroupHistory> findAllByGroupTypeAndParentOrderByIdDesc(String groupType, Long id);
}
