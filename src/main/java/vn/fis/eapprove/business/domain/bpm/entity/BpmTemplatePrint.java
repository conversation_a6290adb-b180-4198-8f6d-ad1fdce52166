package vn.fis.eapprove.business.domain.bpm.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;

import jakarta.persistence.*;
import java.util.Date;
import java.util.Set;

@Data
@Entity
@Transactional
@Table(name = "bpm_template_print")
public class BpmTemplatePrint {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "NAME")
    private String name;

    @Column(name = "PROCESS_NAME")
    private String processName;

    @Column(name = "DESCR")
    private String descr;

    @Column(name = "PROC_DEF_ID")
    private String procDefId;

    @Column(name = "IS_DELETED")
    private boolean isDeleted = false;

    @Column(name = "HTML_CONTENT")
    private String content;

    @Column(name = "HEADER_CONTENT")
    private String headerContent;

    @Column(name = "FOOTER_CONTENT")
    private String footerContent;

    @Column(name = "PDF_CONTENT")
    private String pdfContent;

    @Column(name = "HISTORY_CHANGE")
    private String historyChange;

    @Column(name = "SHARE_WITH")
    private String shareWith;

    @Column(name = "UPLOAD_WORDS")
    private String uploadWords;

    @Column(name = "UPLOAD_WORDS_CHANGE")
    private String uploadWordsChange;
    //0: normal
    //1: upload
    @Column(name = "PRINT_TYPE")
    private int printType = 0;

    @Column(name = "config_type")
    private String configType;

    @Column(name = "PROCESS_ID")
    private Long processId;

    @Column(name = "CREATED_USER")
    private String createdUser;

    @Column(name = "CREATED_DATE")
    private Date createdDate;

    @Column(name = "UPDATED_USER")
    private String updatedUser;

    @Column(name = "UPDATED_DATE")
    private Date updatedDate;

    @OneToMany(mappedBy = "bpmTemplatePrint")
    @JsonIgnore
    private Set<PermissionDataManagement> permissionDataManagements;

    @OneToMany(mappedBy = "bpmTemplatePrint")
    @JsonIgnore
    private Set<SharedUser> sharedUsers;

    @Column(name = "company_code")
    private String companyCode;

    @Column(name = "company_name")
    private String companyName;

    @Column(name = "special_flow")
    private Boolean specialFlow;

    @Column(name = "special_parent_id")
    private Long specialParentId;

    @Column(name = "special_company_code")
    private String specialCompanyCode;

    @Column(name = "special_parent_service_id")
    private Long specialParentServiceId;

    @Column(name = "status")
    private Boolean status;
}
