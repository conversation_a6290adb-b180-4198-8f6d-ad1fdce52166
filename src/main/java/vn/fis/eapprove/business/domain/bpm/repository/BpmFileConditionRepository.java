package vn.fis.eapprove.business.domain.bpm.repository;

import jakarta.persistence.Tuple;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.fileCondition.entity.FileCondition;


import java.util.List;

@Repository
public interface BpmFileConditionRepository extends JpaRepository<FileCondition, Long>, JpaSpecificationExecutor<FileCondition> {
    List<FileCondition> findByBpmTemplatePrintId(Long id);

    void deleteAllByBpmTemplatePrintId(Long id);

    @Query("SELECT a.templateHtmlChange as templateHtml, " +
            " a.templateHtmlHeader as templateHeader, " +
            " a.templateHtmlFooter as templateFooter" +
            " FROM FileCondition a WHERE a.id = :id")
    Tuple getTemplateHtmlChangeById(Long id);
}
