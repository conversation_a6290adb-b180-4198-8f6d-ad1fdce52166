package vn.fis.eapprove.business.domain.template.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.GsonBuilder;
import vn.fis.eapprove.business.config.GsonAdapterConfig;
import vn.fis.eapprove.security.CredentialHelper;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import vn.fis.eapprove.business.domain.authority.service.AuthService;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcInstRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcdefRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTaskRepository;
import vn.fis.eapprove.business.domain.groupTable.entity.GroupTableProTemp;
import vn.fis.eapprove.business.domain.groupTable.repository.GroupProcTempRepository;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.repository.ShareUserRepository;
import vn.fis.eapprove.business.domain.sharedUser.service.ShareUserManager;
import vn.fis.eapprove.business.domain.system.repository.SystemGroupRepository;
import vn.fis.eapprove.business.domain.template.entity.TemplateHistory;
import vn.fis.eapprove.business.domain.template.entity.TemplateManage;
import vn.fis.eapprove.business.domain.template.repository.TemplateHistoryRepository;
import vn.fis.eapprove.business.domain.template.repository.TemplateRepository;
import vn.fis.eapprove.business.dto.BpmProcdefDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.FillterTempateRequest;
import vn.fis.eapprove.business.model.request.SearchTemplateHistory;
import vn.fis.eapprove.business.model.request.TemplateManageRequest;
import vn.fis.eapprove.business.model.request.TemplateRequest;
import vn.fis.eapprove.business.model.response.ChildResponse;
import vn.fis.eapprove.business.model.response.NameAndCodeCompanyResponse;
import vn.fis.eapprove.business.model.response.TemplateResponse;
import vn.fis.eapprove.business.specification.BpmTemplateSpecification;
import vn.fis.eapprove.business.tenant.manager.CamundaEngineService;
import vn.fis.eapprove.business.tenant.manager.CustomerService;

import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ShareUserTypeEnum;
import vn.fis.spro.common.constants.TaskConstants;
import vn.fis.spro.common.util.ValidationUtils;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class TemplateManager {

    @Autowired
    CredentialHelper credentialHelper;
    @Autowired
    CustomerService customerService;
    @Autowired
    private ResponseUtils responseUtils;
    @Autowired
    private TemplateRepository templateRepository;
    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private BpmTemplateSpecification bpmTemplateSpec;
    @Autowired
    private BpmTaskRepository bpmTaskRepository;
    @Autowired
    private GroupProcTempRepository groupProcTempRepository;
    @Autowired
    private Common common;
    @Autowired
    private BpmProcdefRepository bpmProcdefRepository;
    @Autowired
    private CamundaEngineService camundaEngineService;
    @Autowired
    private TemplateHistoryRepository templateHistoryRepository;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private ShareUserManager shareUserManager;
    @Autowired
    private ShareUserRepository shareUserRepository;
    @Autowired
    private PermissionDataManagementRepository permissionDataManagementRepository;
    @Autowired
    private AuthService authService;

    @Autowired
    private BpmProcInstRepository bpmProcInstRepository;
    @Autowired
    private SystemGroupRepository systemGroupRepository;

    public static List<TemplateResponse> sortBySublistSize(List<TemplateResponse> superList, String sortType) {
        Collections.sort(superList, (o1, o2) -> {
            if (sortType.equals("ASC")) {
                if (ValidationUtils.isNullOrEmpty(o1.getBpmProcdefDtos()) && ValidationUtils.isNullOrEmpty(o2.getBpmProcdefDtos())) {
                    return 0;
                }
                if (ValidationUtils.isNullOrEmpty(o1.getBpmProcdefDtos()) && !ValidationUtils.isNullOrEmpty(o2.getBpmProcdefDtos())) {
                    return -1;
                }
                if (!ValidationUtils.isNullOrEmpty(o1.getBpmProcdefDtos()) && ValidationUtils.isNullOrEmpty(o2.getBpmProcdefDtos())) {
                    return 1;
                }
                return Integer.valueOf(o1.getBpmProcdefDtos().size()).compareTo(Integer.valueOf(o2.getBpmProcdefDtos().size()));
            } else {
                if (ValidationUtils.isNullOrEmpty(o2.getBpmProcdefDtos()) && ValidationUtils.isNullOrEmpty(o1.getBpmProcdefDtos())) {
                    return 0;
                }
                if (ValidationUtils.isNullOrEmpty(o1.getBpmProcdefDtos()) && !ValidationUtils.isNullOrEmpty(o2.getBpmProcdefDtos())) {
                    return 1;
                }
                if (!ValidationUtils.isNullOrEmpty(o1.getBpmProcdefDtos()) && ValidationUtils.isNullOrEmpty(o2.getBpmProcdefDtos())) {
                    return -1;
                }
                return Integer.valueOf(o2.getBpmProcdefDtos().size()).compareTo(Integer.valueOf(o1.getBpmProcdefDtos().size()));
            }

        });

        return superList;
    }

    public long createTemplate(TemplateRequest templateRequest) {
        try {
            TemplateManage tm = new TemplateManage();
            TemplateHistory templateHistory = new TemplateHistory();
            Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();

            if (templateRequest.getTemplateName() == null) {
                return 0;
            }
            if (templateRequest.getDescription() == null) {
                return 0;
            }
            if (templateRequest.getTemplate() == null) {
                return 0;
            }
            if (templateRequest.getStatus() == null) {
                return 0;
            }
            if (templateRequest.getUrlName() == null) {
                return 0;
            }
            // String url = new Gson().toJson(templateRequest.getUrlName());
            // List<Map<String,String>> Vacationa = new Gson().fromJson(Test, List.class);

            tm.setTemplateName(templateRequest.getTemplateName());
            tm.setDescription(templateRequest.getDescription() != null ? templateRequest.getDescription() : "");
            tm.setTemplate(templateRequest.getTemplate());
            tm.setStatus(templateRequest.getStatus());
            tm.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            tm.setCreatedDate(LocalDateTime.now());
            tm.setUrlName(templateRequest.getUrlName());
            List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
            for (NameAndCodeCompanyResponse response : listCompanyCodeAndName) {
                tm.setCompanyCode(response.getCompanyCode());
                tm.setCompanyName(response.getCompanyName());
            }
            TemplateManage templateId = templateRepository.save(tm);

            // Lưu phân quyền dữ liệu
            if (!ValidationUtils.isNullOrEmpty(templateRequest.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : templateRequest.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(tm.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.TEMPLATE_MANAGE.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                permissionDataManagementRepository.saveAll(permissionDataManagements);
                templateHistory.setApplyFor(g.toJson(permissionDataManagements));

            }

            if (!ValidationUtils.isNullOrEmpty(templateRequest.getShareWith())) {
                List<SharedUser> sharedUsers = new ArrayList<>();
                for (String shareWith : templateRequest.getShareWith()) {
                    SharedUser sharedUser = new SharedUser();
                    sharedUser.setReferenceId(tm.getId());
                    sharedUser.setReferenceType(ShareUserTypeEnum.TEMPLATE.type);
                    sharedUser.setEmail(shareWith);
                    sharedUsers.add(sharedUser);
                }
                shareUserRepository.saveAll(sharedUsers);
                templateHistory.setShareWith(g.toJson(sharedUsers));
            }


            templateHistory.setTemplateId(templateId.getId());
            templateHistory.setTemplateName(templateId.getTemplateName());
            templateHistory.setTemplate(templateId.getTemplate());
            templateHistory.setContentEdit("Tạo mới");
            Integer version = 0;
            templateHistory.setVersion("V" + version);
            templateHistory.setUrlName(templateId.getUrlName());
            templateHistory.setStatusHistory(true);
            templateHistory.setCreatedDate(LocalDateTime.now());
            templateHistory.setCreatedUser(credentialHelper.getJWTPayload().getUsername());

            templateHistoryRepository.save(templateHistory);

            return tm.getId();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return 0;
        }

    }

    public Boolean deleteTemplate(TemplateRequest templateRequest) {
        try {
            templateRepository.deleteById(templateRequest.getId());
            return true;
        } catch (Exception e) {
            return false;
        }
    }
//    public PageDto filterTempalte(FillterTempateRequest fillterTempateRequest){
//
//    }

    public Boolean deleteTemplatev2(List<Long> ids) {
        try {
            templateRepository.deleteAllById(ids);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public PageDto getAll(FillterTempateRequest fillterTempateRequest) {
        try {
            List<TemplateResponse> templateResponses = new ArrayList<>();
            String username = credentialHelper.getJWTPayload().getUsername();
            fillterTempateRequest.setUsername(username);

            // -1: super admin
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
            fillterTempateRequest.setListCompanyCode(lstCompanyCode);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.TEMPLATE_MANAGER.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                fillterTempateRequest.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                fillterTempateRequest.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            if (fillterTempateRequest.getStatus().contains(-1)) {
                List<Integer> status = new ArrayList<>();
                Integer draft = 0;
                Integer active = 1;
                status.add(draft);
                status.add(active);
                fillterTempateRequest.setStatus(status);
            }
            Map<String, Object> mapData = bpmTemplateSpec.getAllTemplate(fillterTempateRequest);
            List<TemplateResponse> data = (List<TemplateResponse>) mapData.get("data");


            List<String> formKeyList = data.stream().map(TemplateResponse::getUrlName).collect(Collectors.toList());
            List<GroupTableProTemp> groupTableProTemps = groupProcTempRepository.findGroupTableProTempByFormKeyIn(formKeyList);
            Map<String, List<GroupTableProTemp>> groupTableProTempMap = groupTableProTemps.stream().collect(Collectors.groupingBy(GroupTableProTemp::getFormKey));
            List<String> procDefIdList = groupTableProTemps.stream().map(GroupTableProTemp::getProDefId).collect(Collectors.toList());
            List<BpmProcdef> bpmProcdefList = bpmProcdefRepository.getBpmProcdefByProcDefIdIn(procDefIdList);

            List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceType(ShareUserTypeEnum.TEMPLATE.type);


            for (TemplateResponse templateResponse : data) {
                if (!groupTableProTemps.isEmpty()) {
                    List<GroupTableProTemp> groupTableProTempList = groupTableProTempMap.get(templateResponse.getUrlName());
                    List<BpmProcdefDto> bpmProcdefDtos = new ArrayList<>();
                    if (groupTableProTempList != null && !groupTableProTempList.isEmpty()) {
                        List<String> procDefIds = groupTableProTempList.stream().map(GroupTableProTemp::getProDefId).filter(e -> ValidationUtils.isNullOrEmpty(fillterTempateRequest.getProcDefIds()) || fillterTempateRequest.getProcDefIds().contains(e)).collect(Collectors.toList());
                        List<BpmProcdef> bpmProcdefs = bpmProcdefList.stream().filter(procDef -> procDefIds.contains(procDef.getProcDefId())).collect(Collectors.toList());
                        for (BpmProcdef bpmProcdef : bpmProcdefs) {
                            BpmProcdefDto bpmProcdefDto = new BpmProcdefDto();
                            bpmProcdefDto.setId(bpmProcdef.getId());
                            bpmProcdefDto.setName(bpmProcdef.getName());
                            bpmProcdefDtos.add(bpmProcdefDto);
                        }
                        templateResponse.setBpmProcdefDtos(bpmProcdefDtos);
                    }
                }

                if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                    List<String> listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(templateResponse.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
                    if (!ValidationUtils.isNullOrEmpty(listEmail)) {
                        templateResponse.setShareWith(listEmail);
                    }
                }

                templateResponses.add(templateResponse);
            }
            //sort with proc def
            if (fillterTempateRequest.getSortBy().equals("bpmProcdefDtos")) {
                sortBySublistSize(templateResponses, fillterTempateRequest.getSortType());
            }
            Long totalItems = (Long) mapData.get("count");
            Integer totalPage = common.getPageCount(totalItems, fillterTempateRequest.getLimit());

            //Get companyCode+name in special child when get Parent (specFlow = false)
            if (!ValidationUtils.isNullOrEmpty(fillterTempateRequest.getSpecialFlow()) && !fillterTempateRequest.getSpecialFlow() && !templateResponses.isEmpty()) {
                List<ChildResponse> child = templateRepository.findSpecialFlowChildDataByParentIds(templateResponses.stream().map(TemplateResponse::getId).collect(Collectors.toList()));
                List<String> companyCodes = child.stream().map(ChildResponse::getSpecialCompanyCode).collect(Collectors.toList());
                List<NameAndCodeCompanyResponse> allCompanyCodeAndName = customerService.findAllCompanyCodeAndNameByCompanyCode(companyCodes);
                if (allCompanyCodeAndName != null) {
                    child.forEach(i -> {
                        i.setSpecialCompanyName(allCompanyCodeAndName.stream().filter(j -> j.getCompanyCode().equals(i.getSpecialCompanyCode())).findFirst().orElse(new NameAndCodeCompanyResponse()).getCompanyName());
                    });
                    for (TemplateResponse tem : templateResponses) {
                        tem.setChild(child.stream().filter(i -> i.getParentId().equals(tem.getId())).distinct().collect(Collectors.toList()));
                    }
                }
            }
            return PageDto.builder()
                    .content(templateResponses)
                    .number(fillterTempateRequest.getPage())
                    .numberOfElements(fillterTempateRequest.getPage())
                    .page(fillterTempateRequest.getPage())
                    .size(fillterTempateRequest.getLimit())
                    .totalPages(totalPage)
                    .totalElements(totalItems)
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<TemplateResponse> getAllExportExcel(FillterTempateRequest fillterTempateRequest) {
        try {
            List<TemplateResponse> templateResponses = new ArrayList<>();
            String username = credentialHelper.getJWTPayload().getUsername();
            fillterTempateRequest.setUsername(username);

            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
            fillterTempateRequest.setListCompanyCode(lstCompanyCode);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.TEMPLATE_MANAGER.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                fillterTempateRequest.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                fillterTempateRequest.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            if (fillterTempateRequest.getStatus().contains(-1)) {
                List<Integer> status = new ArrayList<>();
                Integer draft = 0;
                Integer active = 1;
                status.add(draft);
                status.add(active);
                fillterTempateRequest.setStatus(status);
            }
            List<TemplateResponse> mapData = bpmTemplateSpec.getAllTemplateExportExcel(fillterTempateRequest);
            List<String> formKeyList = mapData.stream().map(TemplateResponse::getUrlName).collect(Collectors.toList());
            List<GroupTableProTemp> groupTableProTemps = groupProcTempRepository.findGroupTableProTempByFormKeyIn(formKeyList);
            Map<String, List<GroupTableProTemp>> groupTableProTempMap = groupTableProTemps.stream().collect(Collectors.groupingBy(GroupTableProTemp::getFormKey));
            List<String> procDefIdList = groupTableProTemps.stream().map(GroupTableProTemp::getProDefId).collect(Collectors.toList());
            List<BpmProcdef> bpmProcdefList = bpmProcdefRepository.getBpmProcdefByProcDefIdIn(procDefIdList);

            List<NameAndCodeCompanyResponse> listResponse = customerService.responseCompanyCodeAndNames(templateResponses.stream().map(TemplateResponse::getCreatedUser).collect(Collectors.toList()));

            for (TemplateResponse templateResponse : mapData) {
                if (!groupTableProTemps.isEmpty()) {
                    List<GroupTableProTemp> groupTableProTempList = groupTableProTempMap.get(templateResponse.getUrlName());
                    List<BpmProcdefDto> bpmProcdefDtos = new ArrayList<>();
                    if (groupTableProTempList != null && !groupTableProTempList.isEmpty()) {
                        List<String> procDefIds = groupTableProTempList.stream().map(GroupTableProTemp::getProDefId).filter(e -> ValidationUtils.isNullOrEmpty(fillterTempateRequest.getProcDefIds()) || fillterTempateRequest.getProcDefIds().contains(e)).collect(Collectors.toList());
                        List<BpmProcdef> bpmProcdefs = bpmProcdefList.stream().filter(procDef -> procDefIds.contains(procDef.getProcDefId())).collect(Collectors.toList());
                        for (BpmProcdef bpmProcdef : bpmProcdefs) {
                            BpmProcdefDto bpmProcdefDto = new BpmProcdefDto();
                            bpmProcdefDto.setId(bpmProcdef.getId());
                            bpmProcdefDto.setName(bpmProcdef.getName());
                            bpmProcdefDtos.add(bpmProcdefDto);
                        }
                        templateResponse.setBpmProcdefDtos(bpmProcdefDtos);
                    }
                }

                if (!ValidationUtils.isNullOrEmpty(listResponse)) {
                    NameAndCodeCompanyResponse response = listResponse.stream().filter(i -> i.getUsername().equals(templateResponse.getCreatedUser())).findAny().orElse(null);
                    if (!ValidationUtils.isNullOrEmpty(response)) {
                        templateResponse.setCompanyCode(response.getCompanyCode());
                        templateResponse.setCompanyName(response.getCompanyName());
                    }
                }
                templateResponses.add(templateResponse);
            }

            return templateResponses;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    public List<TemplateResponse> getAllFilter(FillterTempateRequest fillterTempateRequest) {
        try {
            List<TemplateResponse> templateResponses = new ArrayList<>();
            String username = credentialHelper.getJWTPayload().getUsername();
            fillterTempateRequest.setUsername(username);

            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

            fillterTempateRequest.setListCompanyCode(lstCompanyCode != null ? lstCompanyCode : new ArrayList<>());

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.TEMPLATE_MANAGER.tableName, username);
            fillterTempateRequest.setLstGroupPermissionId(lstGroupPermissionId);

            if (!ValidationUtils.isNullOrEmpty(fillterTempateRequest.getStatus()) && fillterTempateRequest.getStatus().contains(-1)) {
                List<Integer> status = new ArrayList<>();
                Integer draft = 0;
                Integer active = 1;
                status.add(draft);
                status.add(active);
                fillterTempateRequest.setStatus(status);
            }
            Map<String, Object> mapData = bpmTemplateSpec.getAllTemplate(fillterTempateRequest);
            List<TemplateResponse> data = (List<TemplateResponse>) mapData.get("data");


//            List<String> formKeyList = data.stream().map(TemplateResponse::getUrlName).collect(Collectors.toList());
//            List<GroupTableProTemp> groupTableProTemps = groupProcTempRepository.findGroupTableProTempByFormKeyIn(formKeyList);
//            Map<String, List<GroupTableProTemp>> groupTableProTempMap = groupTableProTemps.stream().collect(Collectors.groupingBy(GroupTableProTemp::getFormKey));
//            List<String> procDefIdList = groupTableProTemps.stream().map(GroupTableProTemp::getProDefId).collect(Collectors.toList());
//            List<BpmProcdef> bpmProcdefList = bpmProcdefRepository.getBpmProcdefByProcDefIdIn(procDefIdList);

            List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceType(ShareUserTypeEnum.TEMPLATE.type);

            for (TemplateResponse templateResponse : data) {
                if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                    List<String> listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(templateResponse.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
                    if (!ValidationUtils.isNullOrEmpty(listEmail)) {
                        templateResponse.setShareWith(listEmail);
                    }
                }
                templateResponses.add(templateResponse);
            }

            return templateResponses;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public Object getAllSystemGroup() {
        try {
            FillterTempateRequest fillterTempateRequest = new FillterTempateRequest();
            fillterTempateRequest.setPage(1);
            fillterTempateRequest.setLimit(999999);
            fillterTempateRequest.setSortBy("id");
            fillterTempateRequest.setStatus(Arrays.asList(-1));
            fillterTempateRequest.setSortType("DESC");
            List<Map<String, Object>> templateResponses = new ArrayList<>();
            String username = credentialHelper.getJWTPayload().getUsername();
            fillterTempateRequest.setUsername(username);

            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.TEMPLATE_MANAGER.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                fillterTempateRequest.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                fillterTempateRequest.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            fillterTempateRequest.setListCompanyCode(lstCompanyCode != null ? lstCompanyCode : new ArrayList<>());

            if (!ValidationUtils.isNullOrEmpty(fillterTempateRequest.getStatus()) && fillterTempateRequest.getStatus().contains(-1)) {
                List<Integer> status = new ArrayList<>();
                Integer draft = 0;
                Integer active = 1;
                status.add(draft);
                status.add(active);
                fillterTempateRequest.setStatus(status);
            }
            Map<String, Object> mapData = bpmTemplateSpec.getAllTemplateSystemGroup(fillterTempateRequest);
            List<TemplateResponse> data = (List<TemplateResponse>) mapData.get("data");

            for (TemplateResponse templateResponse : data) {
                Map<String, Object> response = new HashMap<>();
                response.put("id", templateResponse.getId());
                response.put("name", templateResponse.getTemplateName());
                response.put("companyCode", templateResponse.getCompanyCode());
                response.put("companyName", templateResponse.getCompanyName());
                templateResponses.add(response);
            }

            return templateResponses;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<TemplateResponse> getTemplateByTicket(String procDefId, String procInstId) {
        try {
            FillterTempateRequest req = new FillterTempateRequest();
            List<TemplateResponse> listFinal = new ArrayList<>();
            List<BpmTask> bpmTasks = null;
            if (procInstId != null) {
                bpmTasks = bpmTaskRepository.getFirstTask(procInstId);
            }
            // get start event key----------------------------------------------//
            Document doc = responseUtils.getXml(procDefId);

            NodeList nodes = doc.getElementsByTagName("bpmn:startEvent");
            Element startEle = (Element) nodes.item(0);
            String startKey = startEle.getAttributes().getNamedItem("camunda:formKey").getNodeValue();
            String nodeId = startEle.getAttributes().getNamedItem("id").getNodeValue();
            req.setUrlName(Arrays.asList(startKey));
            // ----------------------------------------------------------------//
            List<TemplateManage> template = templateRepository.findAll(bpmTemplateSpec.filter(req));
            if (!template.isEmpty()) {
                TemplateResponse response = modelMapper.map(template.get(0), TemplateResponse.class);
                response.setNodeId(nodeId);
                response.setListTaskName(bpmTasks.stream().map(BpmTask::getTaskName).collect(Collectors.toList()));
                response.setListTaskAssignee(bpmTasks.stream().map(BpmTask::getTaskAssignee).collect(Collectors.toList()));

                listFinal.add(response);
            }
            return listFinal;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public void getOutTaskKey(List<String> listOutKey, NodeList nodes, NodeList nodeGateway,
                              NodeList nodeSequence, NodeList nodeStart, List<String> listTaskKey,
                              Map<String, Map<String, String>> mapTaskName) {
        try {
            Element startElement = (Element) nodeStart.item(0);
            String startKey = startElement.getAttributes().getNamedItem("id").getNodeValue();
            if (listOutKey.contains(startKey)) {
                String startForm = startElement.getAttributes().getNamedItem("camunda:formKey").getNodeValue();
                listOutKey.remove(startKey);
                listTaskKey.add(startForm);
                String assignee = startElement.getAttributes().getNamedItem("camunda:assignee") != null
                        ? startElement.getAttributes().getNamedItem("camunda:assignee").getNodeValue()
                        : "";
                String taskName = startElement.getAttributes().getNamedItem("name") != null
                        ? startElement.getAttributes().getNamedItem("name").getNodeValue()
                        : "";
                Map<String, String> mapData = new HashMap<>();
                mapData.put("name", taskName);
                mapData.put("nodeId", startKey);
                mapData.put("assignee", assignee);
                mapTaskName.put(startForm, mapData);
            }
            for (int i = 0; i < nodes.getLength(); i++) {
                Element nodeElement = (Element) nodes.item(i);
                String taskKey = nodeElement.getAttributes().getNamedItem("id").getNodeValue();
                if (listOutKey.contains(taskKey)) {
                    listOutKey.remove(taskKey);
                    String formKey = nodeElement.getAttributes().getNamedItem("camunda:formKey").getNodeValue();
                    String assignee = nodeElement.getAttributes().getNamedItem("camunda:assignee") != null
                            ? nodeElement.getAttributes().getNamedItem("camunda:assignee").getNodeValue()
                            : "";
                    String taskName = nodeElement.getAttributes().getNamedItem("name") != null
                            ? nodeElement.getAttributes().getNamedItem("name").getNodeValue()
                            : "";
                    listTaskKey.add(formKey);
                    Map<String, String> mapData = new HashMap<>();
                    mapData.put("name", taskName);
                    mapData.put("nodeId", taskKey);
                    mapData.put("assignee", assignee);
                    mapTaskName.put(formKey, mapData);
                    if (!listOutKey.isEmpty()) {
                        getOutTaskKey(listOutKey, nodes, nodeGateway, nodeSequence, nodeStart, listTaskKey,
                                mapTaskName);
                    }
                }
            }
            for (int i = 0; i < nodeSequence.getLength(); i++) {
                Element element = (Element) nodeSequence.item(i);
                String seqKey = element.getAttributes().getNamedItem("id").getNodeValue();
                String seqRef = element.getAttributes().getNamedItem("targetRef").getNodeValue();
                if (listOutKey.contains(seqKey) || listOutKey.contains(seqRef)) {
                    String sourceRef = element.getAttributes().getNamedItem("sourceRef").getNodeValue();
                    listOutKey.remove(seqKey);
                    listOutKey.remove(seqRef);
                    listOutKey.add(sourceRef);
                    getOutTaskKey(listOutKey, nodes, nodeGateway, nodeSequence, nodeStart, listTaskKey, mapTaskName);
                }
            }
            for (int i = 0; i < nodeGateway.getLength(); i++) {
                Element nodeElement = (Element) nodeGateway.item(i);
                String taskKey = nodeElement.getAttributes().getNamedItem("id").getNodeValue();
                if (listOutKey.contains(taskKey)) {
                    NodeList gateOut = nodeElement.getElementsByTagName("bpmn:incoming");
                    listOutKey.remove(taskKey);
                    for (int j = 0; j < gateOut.getLength(); j++) {
                        Element addedKey = (Element) gateOut.item(j);
                        String addKeyStr = addedKey.getTextContent();
                        listOutKey.add(addKeyStr);
                    }
                    getOutTaskKey(listOutKey, nodes, nodeGateway, nodeSequence, nodeStart, listTaskKey, mapTaskName);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // ----------------------GET TEMPLATE BY FORM KEY---------------------------//
    public TemplateResponse getTempaletByFormKey(String formKey) {
        try {
            FillterTempateRequest req = new FillterTempateRequest();
            req.setUrlName(Arrays.asList(formKey));
            List<TemplateManage> template = templateRepository.findAll(bpmTemplateSpec.filter(req));
            if (!template.isEmpty()) {
                TemplateManage taskTemp = template.get(0);
                TemplateResponse finalRes = modelMapper.map(taskTemp, TemplateResponse.class);
                return finalRes;
            }
            return new TemplateResponse();
        } catch (Exception e) {
            return null;
        }
    }

    public TemplateResponse getTemplateById(Integer id) {
        try {
            FillterTempateRequest req = new FillterTempateRequest();
            req.setId(Arrays.asList(id));
            List<TemplateManage> template1 = templateRepository.findAll(bpmTemplateSpec.filter1(req));
            if (!template1.isEmpty()) {
                TemplateManage taskTemp1 = template1.get(0);
                TemplateResponse finalRes1 = modelMapper.map(taskTemp1, TemplateResponse.class);

                List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(taskTemp1.getId(), PermissionDataConstants.Type.TEMPLATE_MANAGE.code);
                List<String> companyCodes = permissionDataManagements.stream().map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
                if (!ValidationUtils.isNullOrEmpty(companyCodes)) {
                    finalRes1.setApplyFor(companyCodes);
                }

//                SharedUser sharedUser = shareUserRepository.findAllByReferenceIdAndReferenceType(finalRes1.getId(),ShareUserTypeEnum.TEMPLATE.type);
//                if(!ValidationUtils.isNullOrEmpty(sharedUser)) {
//                    ObjectMapper mapper = new ObjectMapper();
//                    List<String> listEmail = mapper.readValue(sharedUser.getEmail(), new TypeReference<List<String>>() {
//                    });
//                    finalRes1.setShareUser(listEmail);
//                }
                List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceType(ShareUserTypeEnum.TEMPLATE.type);
                if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                    List<String> listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(finalRes1.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
                    if (!ValidationUtils.isNullOrEmpty(listEmail)) {
                        finalRes1.setShareWith(listEmail);
                    }
                }

                List<GroupTableProTemp> groupTableProTemps = groupProcTempRepository.findGroupTableProTempByFormKeyIn(Arrays.asList(finalRes1.getUrlName()));

                if (!ValidationUtils.isNullOrEmpty(groupTableProTemps))
                    finalRes1.setIsProcDefJoin(Boolean.TRUE);
                return finalRes1;
            }

            return new TemplateResponse();
        } catch (Exception e) {
            return null;
        }
    }

    public List<TemplateResponse> getTemplateByTask(String procDefId, String type, String taskId) {
        try {
            FillterTempateRequest req = new FillterTempateRequest();
            BpmTask task = bpmTaskRepository.getBpmTaskByTaskId(taskId);
            // get start event key----------------------------------------------//
            List<String> listTaskKey = new ArrayList<>();
            List<String> listOutKey = new ArrayList<>();
            Map<String, Map<String, String>> mapTaskName = new HashMap<>();
            String diagramXml = camundaEngineService.getXML(procDefId);

            DocumentBuilder db = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            InputSource is = new InputSource();
            is.setCharacterStream(new StringReader(diagramXml));

            Document doc = db.parse(is);
            NodeList nodes = doc.getElementsByTagName("bpmn:userTask");
            NodeList nodeGateway = doc.getElementsByTagName("bpmn:parallelGateway");
            NodeList nodeSequence = doc.getElementsByTagName("bpmn:sequenceFlow");
            NodeList nodeStart = doc.getElementsByTagName("bpmn:startEvent");
            if (type.equalsIgnoreCase("input")) {
                for (int i = 0; i < nodes.getLength(); i++) {
                    Element nodeElement = (Element) nodes.item(i);
                    String taskKey = nodeElement.getAttributes().getNamedItem("id").getNodeValue();
                    if (taskKey.equals(task.getTaskDefKey())) {
                        NodeList nodeOut = nodeElement.getElementsByTagName("bpmn:incoming");
                        for (int j = 0; j < nodeOut.getLength(); j++) {
                            Element outEle = (Element) nodeOut.item(j);
                            listOutKey.add(outEle.getTextContent());
                        }
                    }
                }
                getOutTaskKey(listOutKey, nodes, nodeGateway, nodeSequence, nodeStart, listTaskKey, mapTaskName);
                req.setUrlName(listTaskKey);
            } else {
                for (int i = 0; i < nodes.getLength(); i++) {
                    Element nodeElement = (Element) nodes.item(i);
                    String taskKey = nodeElement.getAttributes().getNamedItem("id").getNodeValue();
                    if (taskKey.equals(task.getTaskDefKey())) {
                        String formKey = nodeElement.getAttributes().getNamedItem("camunda:formKey").getNodeValue();
//                        String assignee = nodeElement.getAttributes().getNamedItem("camunda:assignee") != null
//                                ? nodeElement.getAttributes().getNamedItem("camunda:assignee").getNodeValue()
//                                : "";
                        String taskName = nodeElement.getAttributes().getNamedItem("name") != null
                                ? nodeElement.getAttributes().getNamedItem("name").getNodeValue()
                                : "";
                        listTaskKey.add(taskKey);
                        Map<String, String> mapData = new HashMap<>();
                        mapData.put("name", taskName);
                        mapData.put("nodeId", taskKey);
                        mapData.put("assignee", task.getTaskAssignee());
                        mapTaskName.put(formKey, mapData);
                        req.setUrlName(Arrays.asList(formKey));
                    }
                }
            }
            Map<String, List<BpmTask>> mapZoom = new HashMap<>();
//            if (listTaskKey.size() > 2) {
            List<BpmTask> listTaskZoom = bpmTaskRepository.listTaskZoom(task.getTaskProcInstId(), listTaskKey,
                    "COMPLETED");
            mapZoom = listTaskZoom.stream().collect(Collectors.groupingBy(BpmTask::getTaskDefKey));
//            }
            // ----------------------------------------------------------------//
            // Page<TemplateManage> data =
            // templateRepository.getAll(req.getSearch(),req.getStatus(),req.getUrlName(),PageRequest.of(req.getPage(),
            // req.getLimit(),sort));
            List<TemplateManage> template = templateRepository.findAll(bpmTemplateSpec.filter(req));
            Map<String, List<BpmTask>> finalMapZoom = mapZoom;
            return template.stream().map(templateManage -> {
                // List<Map<String,String>> createdTemplate = new
                // Gson().fromJson(templateManage.getTemplate(), List.class);
                TemplateResponse response = modelMapper.map(templateManage, TemplateResponse.class);
                String taskKey = mapTaskName.get(response.getUrlName()).get("nodeId");
                response.setTaskName(mapTaskName.get(response.getUrlName()).get("name"));
                response.setAssignee(task.getTaskAssignee());
                response.setNodeId(taskKey);
                if (!finalMapZoom.isEmpty()) {
                    List<BpmTask> zoomFromMap = finalMapZoom.get(taskKey);
                    if (zoomFromMap.size() > 2) {
                        List<String> listUserZoom = zoomFromMap.stream().map(BpmTask::getTaskAssignee)
                                .collect(Collectors.toList());
                        response.setListZoom(listUserZoom);
                    }
                }
                // response.setZoomCollection();
                return response;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

//    public TemplateManage updateTemplate(TemplateRequest templateRequest, String email) {
//
//        try {
//            TemplateManage wt = templateRepository.findById(templateRequest.getId()).get();
//            // String createdTemplate = new Gson().toJson(templateRequest.getTemplate());
//            // String url = new Gson().toJson(templateRequest.getUrlName());
//
//            if (templateRequest.getId() != null) {
//                wt.setId(templateRequest.getId());
//            }
//            if (templateRequest.getTemplateName() != null) {
//                wt.setTemplateName(templateRequest.getTemplateName());
//            }
//            if (templateRequest.getDescription() != null) {
//                wt.setDescription(templateRequest.getDescription());
//            }
//            if (templateRequest.getStatus() != null) {
//                wt.setStatus(templateRequest.getStatus());
//            }
//            if (templateRequest.getTemplate() != null) {
//                wt.setTemplate(templateRequest.getTemplate());
//            }
//            if (templateRequest.getTemplate() != null) {
//                wt.setTemplate(templateRequest.getTemplate());
//            }
//            if (templateRequest.getUrlName() != null) {
//                wt.setUrlName(templateRequest.getUrlName());
//            }
//
//            // if (templateRequest.getCreateDate()!= null) {
//            // wt.setCreateDate(templateRequest.getCreateDate());
//            // }
//            // if (templateRequest.getCreateUser()!= null) {
//            // wt.setCreateUser(templateRequest.getCreateUser());
//            // }
//            wt.setModifiedDate(LocalDateTime.now());
//            wt.setModifiedUser(email);
//
//            templateRepository.save(wt);
//            return wt;
//        } catch (Exception e) {
//            return null;
//        }
//    }

    public TemplateManage updateTemplate(TemplateRequest templateRequest) {

        try {
            TemplateManage wt = templateRepository.findById(templateRequest.getId()).get();
            TemplateHistory templateHistory = new TemplateHistory();
            Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();

            Boolean isChange = false;
            // String createdTemplate = new Gson().toJson(templateRequest.getTemplate());
            // String url = new Gson().toJson(templateRequest.getUrlName());
            StringBuilder contentEdit = new StringBuilder();
            if (templateRequest.getId() != null) {
                wt.setId(templateRequest.getId());
            }
            if (templateRequest.getTemplateName() != null && !templateRequest.getTemplateName().equalsIgnoreCase(wt.getTemplateName())) {
                wt.setTemplateName(templateRequest.getTemplateName());
                isChange = true;

                contentEdit.append(messageSource.getMessage("message.template.update.name", null, Locale.getDefault()));
            }
            if (templateRequest.getDescription() != null && !templateRequest.getDescription().equalsIgnoreCase(wt.getDescription())) {
                wt.setDescription(templateRequest.getDescription());
                isChange = true;

                contentEdit.append(messageSource.getMessage("message.template.update.description", null, Locale.getDefault()));
            }
            if (templateRequest.getStatus() != null && !(templateRequest.getStatus().equals(wt.getStatus()))) {
                wt.setStatus(templateRequest.getStatus());
                isChange = true;
            }
            if (templateRequest.getTemplate() != null && !templateRequest.getTemplate().equals(wt.getTemplate())) {
                wt.setTemplate(templateRequest.getTemplate());
                isChange = true;

                contentEdit.append(messageSource.getMessage("message.template.update.template", null, Locale.getDefault()));
            }

            if (templateRequest.getUrlName() != null && !templateRequest.getUrlName().equalsIgnoreCase(wt.getUrlName())) {
                wt.setUrlName(templateRequest.getUrlName());
                isChange = true;

                contentEdit.append(messageSource.getMessage("message.template.update.url", null, Locale.getDefault()));
            }

            // share user
            shareUserRepository.deleteAllByReferenceIdAndReferenceType(wt.getId(), ShareUserTypeEnum.TEMPLATE.type);
            if (!ValidationUtils.isNullOrEmpty(templateRequest.getShareWith())) {
                List<SharedUser> sharedUsers = new ArrayList<>();
                for (String shareWith : templateRequest.getShareWith()) {
                    SharedUser sharedUser = new SharedUser();
                    sharedUser.setReferenceId(wt.getId());
                    sharedUser.setReferenceType(ShareUserTypeEnum.TEMPLATE.type);
                    sharedUser.setEmail(shareWith);
                    sharedUsers.add(sharedUser);
                }
                shareUserRepository.saveAll(sharedUsers);
                templateHistory.setShareWith(g.toJson(sharedUsers));

                isChange = true;

                contentEdit.append(messageSource.getMessage("message.template.update.shareUser", null, Locale.getDefault()));
            }

            wt.setModifiedDate(LocalDateTime.now());
            wt.setModifiedUser(credentialHelper.getJWTPayload().getUsername());

            // Lưu phân quyền dữ liệu
            // Xóa data cũ
            List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(wt.getId(), PermissionDataConstants.Type.TEMPLATE_MANAGE.code);
            if (!ValidationUtils.isNullOrEmpty(oldData)) {
                permissionDataManagementRepository.deleteAll(oldData);
            }
            if (!ValidationUtils.isNullOrEmpty(templateRequest.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : templateRequest.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(wt.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.TEMPLATE_MANAGE.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                permissionDataManagementRepository.saveAll(permissionDataManagements);
                templateHistory.setApplyFor(g.toJson(permissionDataManagements));
            }

            templateRepository.save(wt);

            if (isChange) {
                wt.setModifiedDate(LocalDateTime.now());
                wt.setModifiedUser(credentialHelper.getJWTPayload().getUsername());
                List<TemplateHistory> updateHisList = new ArrayList<>();
                if (wt.getStatus() == 1) {
                    List<TemplateHistory> list = templateHistoryRepository.findByTemplateId(wt.getId());
                    for (TemplateHistory history : list) {
                        history.setStatusHistory(false);
                    }

                    templateHistory.setTemplateId(wt.getId());
                    templateHistory.setTemplateName(wt.getTemplateName());
                    templateHistory.setTemplate(wt.getTemplate());
                    templateHistory.setContentEdit(contentEdit.toString());
                    Integer version = 0;
                    if (!list.isEmpty())
                        version = templateHistoryRepository.findMaxVersion(list.get(0).getId());
                    templateHistory.setVersion("V" + version);
                    templateHistory.setUrlName(wt.getUrlName());
                    templateHistory.setStatusHistory(true);
                    templateHistory.setCreatedDate(LocalDateTime.now());
                    templateHistory.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    templateHistory.setDescription(templateRequest.getDescription());

                    updateHisList.add(templateHistory);
//                    templateHistoryRepository.save(templateHistory);
                }

                //Update child version
                //All child list
                List<TemplateManage> allSpecialChild = templateRepository.findAllBySpecialParentIdAndSpecialFlowIsTrue(wt.getId());
                List<TemplateHistory> allHisSpec = templateHistoryRepository.findAllByTemplateId(allSpecialChild.stream().map(TemplateManage::getId).collect(Collectors.toList()));
                for (TemplateHistory history : allHisSpec) {
                    String regex = "\\d+"; // Biểu thức chính quy để tìm các ký tự số
                    Pattern pattern = Pattern.compile(regex);
                    Matcher matcher = pattern.matcher(history.getVersion());
                    if (matcher.find()) {
                        Integer newVerString = (Integer.parseInt(matcher.group()) + 1); // Lấy phần số từ chuỗi
                        TemplateHistory newHis = new TemplateHistory().copyWithoutId(history, newVerString);
                        history.setStatusHistory(false);
                        newHis.setStatusHistory(true);
                        newHis.setContentEdit("Biểu mẫu cha thay đổi");
                        newHis.setCreatedUser(credentialHelper.getJWTPayload().getUsername());

                        updateHisList.add(newHis);
                    }
                }
                templateHistoryRepository.saveAll(updateHisList);
                templateRepository.save(wt);
            }

            return wt;
        } catch (Exception e) {
            e.printStackTrace();
            return null;

        }
    }

    public Long cloneTemplate(long workingTimeId) {
        try {
            TemplateHistory templateHistory = new TemplateHistory();
            Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();
            //Get user login
            String email = credentialHelper.getJWTPayload().getUsername();
            TemplateManage currTemplate = templateRepository.findById(workingTimeId).orElse(null);
            // workingTimeRepository.findWorkingTimeById(workingTImeRequest.getWorkingTimeId());
            if (currTemplate == null) {
                return -1L;
            }


            TemplateManage newTemplateManage = new TemplateManage();
            String name = null;
            String urlName = null;
            TemplateManage findTopByTemplateName = templateRepository.findTopByTemplateNameLikeOrderByIdDesc(currTemplate.getTemplateName() + " - copy" + "%").orElse(null);
            TemplateManage findTopByUrlName = templateRepository.findTopByUrlNameLikeOrderByIdDesc(currTemplate.getUrlName() + " - copy" + "%").orElse(null);

            if (findTopByTemplateName != null) {
                name = findTopByTemplateName.getTemplateName();
            }
            if (name == null) {
                newTemplateManage.setTemplateName(currTemplate.getTemplateName() + " - copy");
            } else {
                String numRegexTemplateName = null;
                try {
                    Pattern pattern = Pattern.compile("^(.*)\\((\\d+)\\)$");
                    Matcher matcher = pattern.matcher(name);

                    if (matcher.find()) {
                        numRegexTemplateName = matcher.group(2);
                    } else {
                        numRegexTemplateName = "1";
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                if (numRegexTemplateName != null) {
                    int numTemplateName = Integer.parseInt(numRegexTemplateName);
                    newTemplateManage.setTemplateName(currTemplate.getTemplateName() + " - copy(" + (numTemplateName + 1) + ")");
                }
            }

            if (findTopByUrlName != null) {
                urlName = findTopByUrlName.getUrlName();
            }
            if (urlName == null) {
                newTemplateManage.setUrlName(currTemplate.getUrlName() + " - copy");
            } else {
                String numRegexUrlName = null;
                try {
                    Pattern pattern = Pattern.compile("^(.*)\\((\\d+)\\)$");
                    Matcher matcher = pattern.matcher(urlName);

                    if (matcher.find()) {
                        numRegexUrlName = matcher.group(2);
                    } else {
                        numRegexUrlName = "1";
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                if (numRegexUrlName != null) {
                    int numUrlName = Integer.parseInt(numRegexUrlName);
                    newTemplateManage.setUrlName(currTemplate.getUrlName() + " - copy(" + (numUrlName + 1) + ")");
                }
            }
            //Add valid length
            if (newTemplateManage.getTemplateName().length() > 100)
                return -2L;
            else if (newTemplateManage.getUrlName().length() > 50)
                return -3L;
            newTemplateManage.setDescription(currTemplate.getDescription());
            newTemplateManage.setStatus(currTemplate.getStatus());
            newTemplateManage.setTemplate(currTemplate.getTemplate());
            newTemplateManage.setCreatedDate(LocalDateTime.now());
            newTemplateManage.setCreatedUser(email);
            newTemplateManage.setStatus(0);

            newTemplateManage = templateRepository.save(newTemplateManage);

            // Clone permission data
            List<PermissionDataManagement> cloneData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(workingTimeId, PermissionDataConstants.Type.TEMPLATE_MANAGE.code);
            if (!ValidationUtils.isNullOrEmpty(cloneData)) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (PermissionDataManagement data : cloneData) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(newTemplateManage.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.TEMPLATE_MANAGE.code);
                    permissionDataManagement.setCompanyCode(data.getCompanyCode());
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                permissionDataManagementRepository.saveAll(permissionDataManagements);
            }

            // clone share user
            List<SharedUser> sharedUserList = shareUserRepository.getSharedUsersByReferenceIdAndReferenceType(currTemplate.getId(), ShareUserTypeEnum.TEMPLATE.type);
            if (!ValidationUtils.isNullOrEmpty(sharedUserList)) {
                List<SharedUser> sharedUsers = new ArrayList<>();
                for (SharedUser shareWith : sharedUserList) {
                    SharedUser sharedUser = new SharedUser();
                    sharedUser.setReferenceId(newTemplateManage.getId());
                    sharedUser.setReferenceType(ShareUserTypeEnum.TEMPLATE.type);
                    sharedUser.setEmail(shareWith.getEmail());
                    sharedUsers.add(sharedUser);
                }
                shareUserRepository.saveAll(sharedUsers);

            }


            templateHistory.setTemplateId(newTemplateManage.getId());
            templateHistory.setTemplateName(newTemplateManage.getTemplateName());
            templateHistory.setTemplate(newTemplateManage.getTemplate());
            templateHistory.setContentEdit("Tạo mới");
            Integer version = 0;
            templateHistory.setVersion("V" + version);
            templateHistory.setUrlName(newTemplateManage.getUrlName());
            templateHistory.setStatusHistory(true);
            templateHistory.setCreatedDate(LocalDateTime.now());
            templateHistory.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            templateHistory.setDescription(currTemplate.getDescription());

            templateHistoryRepository.save(templateHistory);

            return 1L;
        } catch (Exception e) {
            return -1L;
        }
    }

    public boolean CheckExistName(String templateName) {
        try {
            List<TemplateManage> templateManage = templateRepository.CheckExistName(templateName);
            if (templateManage.size() > 0) {
                return true;
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public Boolean CheckExistUrlName(String urlName) {
        try {
            List<TemplateManage> templateManage = templateRepository.checkExistUrlName(urlName);
            if (templateManage.size() > 0) {
                return true;
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public Boolean checkExistTemplateNameAndUrl(Long id,String urlName,String templateName) {
        try {
            List<TemplateManage> templateManage = templateRepository.checkExistTemplateNameAndUrl(id,urlName,templateName);
            return !templateManage.isEmpty();
        } catch (Exception e) {
            return false;
        }
    }

    public Boolean checkNameExits(String templateName, Long id) {
        if (id != null) {
            TemplateManage templateManage = templateRepository.findById(id).get();
            if (!templateName.equalsIgnoreCase(templateManage.getTemplateName())) {
                if (CheckExistName(templateName)) {
                    return true;
                }
            }

        } else {
            if (CheckExistName(templateName)) {
                return true;
            }
        }
        return false;
    }

    public Boolean checkUrlNameExits(String urlName, Long id) {
        if (id != null) {
            TemplateManage templateManage = templateRepository.findById(id).get();
            if (!urlName.equalsIgnoreCase(templateManage.getUrlName())) {
                if (CheckExistUrlName(urlName)) {
                    return true;
                }
            }

        } else {
            if (CheckExistUrlName(urlName)) {
                return true;
            }
        }
        return false;
    }


    public Boolean CheckProdefID(Long id) {
        try {

            List<TemplateManage> templateManage = templateRepository.checkProdefId(id);
            return templateManage != null && !templateManage.isEmpty();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public Boolean CheckProdefIDv2(List<Long> ids) {
        try {
            for (Long id : ids) {
                List<TemplateManage> templateManage = templateRepository.checkProdefId(id);
                if (templateManage != null && !templateManage.isEmpty()) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean checkMasterDataId(Long masterDataId) {
        try {
            List<TemplateManage> templateManages = templateRepository.checkMasterData(masterDataId);

            if (!ValidationUtils.isNullOrEmpty(templateManages)) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public Boolean checkMasterDataFilterId(Long masterDataFilterId) {
        try {
            List<TemplateManage> templateManages = templateRepository.checkMasterDataFilter(masterDataFilterId);
            if (!ValidationUtils.isNullOrEmpty(templateManages)) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }


    public List<Map<String, String>> getTemplateField(Long id) {
        List<Map<String, String>> resList = new ArrayList<>();
        try {
            TemplateManage templateManage = templateRepository.getById(id);
            if (templateManage != null) {
                ObjectMapper mapper = new ObjectMapper();
                Map<String, Object> templateMap = (Map<String, Object>) mapper.readValue(templateManage.getTemplate(), Object.class);
                List<Object> formList = (List<Object>) templateMap.get("form");
                for (int i = 0; i < formList.size(); i++) {
                    Map<String, String> mapData = new HashMap<>();
                    Map<String, Object> map = (Map<String, Object>) formList.get(i);

                    mapData.put("label", map.get("label").toString());
                    mapData.put("value", map.get("name").toString());
                    resList.add(mapData);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return resList;
    }

    public List<Map<String, Object>> getAllFormKey() {
        List<Map<String, Object>> response = new ArrayList<>();
        List<TemplateManage> templateManageList = templateRepository.findAll();

        if (!templateManageList.isEmpty()) {
            for (TemplateManage templateManage : templateManageList) {
                LinkedHashMap<String, Object> dataMap = new LinkedHashMap<>();
                dataMap.put("id", templateManage.getId());
                dataMap.put("formKey", templateManage.getUrlName());
                response.add(dataMap);
            }
        }
        return response;
    }

    public List<TemplateManage> getByListFormKey(List<String> lstFormKey) {
        try {
            return templateRepository.findAllByUrlNameIn(lstFormKey);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public void cloneTemplateSpecial(TemplateManage currTemplate, String companyCode, Long parentServiceId) {
        try {
            //Get user login
            String username = credentialHelper.getJWTPayload().getUsername();
            if (currTemplate == null) {
                return;
            }

            TemplateManage newTemplateManage = templateRepository.findTemplateManageBySpecialParentIdAndSpecialCompanyCodeAndSpecialParentServiceId(currTemplate.getId(), companyCode, parentServiceId);
            if (ValidationUtils.isNullOrEmpty(newTemplateManage)) {
                newTemplateManage = new TemplateManage();
                newTemplateManage.setSpecialFlow(true);
                newTemplateManage.setSpecialParentId(currTemplate.getId());
                newTemplateManage.setSpecialCompanyCode(companyCode);
                newTemplateManage.setSpecialParentServiceId(parentServiceId);

                newTemplateManage.setTemplateName(currTemplate.getTemplateName() + "-" + parentServiceId.toString() + "-" + companyCode);
                newTemplateManage.setUrlName(currTemplate.getUrlName() + "-" + parentServiceId + "-" + companyCode);
                newTemplateManage.setDescription(currTemplate.getDescription());
                newTemplateManage.setStatus(currTemplate.getStatus());
                newTemplateManage.setTemplate("");
                newTemplateManage.setCreatedDate(LocalDateTime.now());
                newTemplateManage.setCreatedUser(username);
                newTemplateManage.setStatus(0);

                List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
                if (!ValidationUtils.isNullOrEmpty(listCompanyCodeAndName)) {
                    NameAndCodeCompanyResponse response = listCompanyCodeAndName.get(0);
                    newTemplateManage.setCompanyCode(response.getCompanyCode());
                    newTemplateManage.setCompanyName(response.getCompanyName());
                }

                newTemplateManage = templateRepository.save(newTemplateManage);

                // Lưu phân quyền dữ liệu
                // Xóa data cũ
                List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(newTemplateManage.getId(), PermissionDataConstants.Type.TEMPLATE_MANAGE.code);
                if (!ValidationUtils.isNullOrEmpty(oldData)) {
                    permissionDataManagementRepository.deleteAll(oldData);
                }
                PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                permissionDataManagement.setTypeId(newTemplateManage.getId());
                permissionDataManagement.setTypeName(PermissionDataConstants.Type.TEMPLATE_MANAGE.code);
                permissionDataManagement.setCompanyCode(companyCode);
                permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                permissionDataManagement.setCreatedTime(LocalDateTime.now());

                permissionDataManagementRepository.save(permissionDataManagement);

                TemplateHistory templateHistory = new TemplateHistory();
                templateHistory.setTemplateId(newTemplateManage.getId());
                templateHistory.setTemplateName(newTemplateManage.getTemplateName());
                templateHistory.setTemplate(newTemplateManage.getTemplate());
                templateHistory.setContentEdit("Tạo mới");
                Integer version = 0;
                templateHistory.setVersion("V" + version);
                templateHistory.setUrlName(newTemplateManage.getUrlName());
                templateHistory.setStatusHistory(true);
                templateHistory.setCreatedDate(LocalDateTime.now());
                templateHistory.setCreatedUser(credentialHelper.getJWTPayload().getUsername());

                templateHistoryRepository.save(templateHistory);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public Map<String, Object> getTemplateHistory(SearchTemplateHistory input) {
        Map<String, Object> result = new HashMap<>();
        TemplateHistory templateHistory = null;
        TemplateHistory templateHistoryParent = null;

        BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(input.getProcInstId());

        //Thực hiện lại clear data để get new version
        if (bpmProcInst != null && bpmProcInst.getTicketStatus().equalsIgnoreCase(TaskConstants.Status.DELETED_BY_RU.code)) {
            bpmProcInst = null;
            input.setProcInstId(null);
            input.setTaskDefinitionKey(null);
            input.setParentTemplateId(null);
        }

        // Lấy template theo version procinst
        if (bpmProcInst != null && bpmProcInst.getTemplateVersionId() != null &&
                (ValidationUtils.isNullOrEmpty(input.getTaskDefinitionKey())
                        || input.getTaskDefinitionKey().equals(bpmProcInst.getTicketStartActId()))) {
            templateHistory = templateHistoryRepository.findById(bpmProcInst.getTemplateVersionId()).orElse(null);
            if (bpmProcInst.getParentTemplateVersionId() != null) {
                templateHistoryParent = templateHistoryRepository.findById(bpmProcInst.getParentTemplateVersionId()).orElse(null);
            }
        } else { // Lấy template theo task
            List<BpmTask> bpmTasks = bpmTaskRepository.findBpmTaskByTaskDefKeyAndTaskProcInstId(input.getTaskDefinitionKey(), input.getProcInstId());
            if (!bpmTasks.isEmpty() && bpmTasks.get(0).getTemplateVersionId() != null) {
                templateHistory = templateHistoryRepository.findById(bpmTasks.get(0).getTemplateVersionId()).orElse(null);
                if (bpmTasks.get(0).getParentTemplateVersionId() != null) {
                    templateHistoryParent = templateHistoryRepository.findById(bpmTasks.get(0).getParentTemplateVersionId()).orElse(null);
                }
            }
        }

        // Check if templateHistory and templateHistoryParent are not null before putting into result
        if (templateHistory != null) {
            result.put("template", templateHistory.getTemplate());
        }

        if (templateHistoryParent != null) {
            result.put("templateParent", templateHistoryParent.getTemplate());
        }

        Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();
        // Lấy template theo URL
        if (!ValidationUtils.isNullOrEmpty(input.getUrlName()) && result.isEmpty()) {
            TemplateManage templateManage = templateRepository.findByUrlName(input.getUrlName());
            if (templateManage != null) {
                result.put("template", templateManage.getTemplate());
                if (templateManage.getSpecialFlow() != null && templateManage.getSpecialFlow().equals(Boolean.TRUE)) {
                    TemplateManage parentTemplate = templateRepository.findById(templateManage.getSpecialParentId()).orElse(null);
                    if (parentTemplate != null) {
                        result.put("templateParent", parentTemplate.getTemplate());
                    }
                }
            }
        }
        //Nếu có parent thì meger vào
        if (!result.isEmpty() && result.get("template") != null && result.get("templateParent") != null) {
            TemplateManageRequest child = g.fromJson(result.get("template").toString(), TemplateManageRequest.class);
            TemplateManageRequest parent = g.fromJson(result.get("templateParent").toString(), TemplateManageRequest.class);
            if (child == null) { //Clone nhưng k có data
                child = new TemplateManageRequest(new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
            }
            if (parent == null) {
                parent = new TemplateManageRequest(new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
            }
            List<Object> formChild = child.getForm();
            List<Object> columnChild = child.getColumn();
            List<Object> rowChild = child.getRow();
            List<Object> tabChild = child.getTab();

            List<Object> formParent = parent.getForm();
            List<Object> columnParent = parent.getColumn();
            List<Object> rowParent = parent.getRow();
            List<Object> tabParent = parent.getTab();

            int parentFormLength = formParent.size() + formChild.size();
            int parentRowLength = rowParent.size() + rowChild.size();
            int parentColLength = columnParent.size() + columnChild.size();

            //Update lại index trên biểu mẫu
            formChild = formChild.stream().map(i -> {
                Map<String, Object> obj = (Map<String, Object>) i;
                double sortOrder = ValidationUtils.isNullOrEmpty(obj.get("sortOrder")) ? 0 : (Double) obj.get("sortOrder");
                double rowSortOrder = ValidationUtils.isNullOrEmpty(obj.get("rowSortOrder")) ? 0 : (Double) obj.get("rowSortOrder");
                double colSortOrder = ValidationUtils.isNullOrEmpty(obj.get("colSortOrder")) ? 0 : (Double) obj.get("colSortOrder");
                double fieldSortOrder = ValidationUtils.isNullOrEmpty(obj.get("fieldSortOrder")) ? 0 : (Double) obj.get("fieldSortOrder");
                obj.put("sortOrder", sortOrder + parentFormLength);
                obj.put("rowSortOrder", rowSortOrder + parentFormLength);
                obj.put("colSortOrder", colSortOrder + parentFormLength);
                obj.put("fieldSortOrder", fieldSortOrder + parentFormLength);
                return obj;
            }).collect(Collectors.toList());
            rowChild = rowChild.stream().map(i -> {
                Map<String, Object> obj = (Map<String, Object>) i;
                double sortOrder = ValidationUtils.isNullOrEmpty(obj.get("sortOrder")) ? 0 : (Double) obj.get("sortOrder");
                obj.put("sortOrder", sortOrder + parentRowLength);
                return obj;
            }).collect(Collectors.toList());
            columnChild = columnChild.stream().map(i -> {
                Map<String, Object> obj = (Map<String, Object>) i;
                double sortOrder = ValidationUtils.isNullOrEmpty(obj.get("sortOrder")) ? 0 : (Double) obj.get("sortOrder");
                obj.put("sortOrder", sortOrder + parentColLength);
                return obj;
            }).collect(Collectors.toList());

            List<Object> finalFormParent = formParent;
            formParent = formParent.stream().filter(i -> {
                Map<String, Object> obj = (Map<String, Object>) i;
                if (obj.get("columns") != null && obj.get("columns") instanceof ArrayList) {
                    List<Map<String, Object>> columns = (List<Map<String, Object>>) obj.get("columns");
                    int length = columns.size();
                    int startColCheck = (length / 2) - 1;
                    List<Integer> removedList = new ArrayList<>();
                    int idx = 0;
                    for (Map<String, Object> field : columns) {
                        if (idx > startColCheck)
                            for (Map.Entry<String, Object> entry : field.entrySet()) {
                                String key = entry.getKey();
                                Object value = entry.getValue();
                                if (key.equalsIgnoreCase("isCloneInSpecFlow") &&
                                        (value != null && Objects.equals(value, Boolean.TRUE))) {
                                    removedList.add(idx);
                                    removedList.add(idx - startColCheck-1);
                                }


                            }
                        idx++;
                    }
                    if(!removedList.isEmpty()){
                        List<Map<String, Object>> finalColumns = columns;
                        columns = columns.stream().filter(field->!removedList.contains(finalColumns.indexOf(field))).collect(Collectors.toList());
                        obj.put("columns",columns);
                    }
                }
                Object value = obj.get("isCloneInSpecFlow");

                if (obj.get("splitter") != null) {
                    String splitterId = obj.get("splitter").toString();

                    Object objSplitter = finalFormParent.stream()
                            .filter(j -> j instanceof LinkedTreeMap && splitterId.equals(((LinkedTreeMap<String, Object>) j).get("id")))
                            .findFirst()
                            .orElse(null);

                    if (objSplitter != null) {
                        value = ((LinkedTreeMap<String, Object>) objSplitter).get("isCloneInSpecFlow");
                    }
                }


                return value == null || value instanceof ArrayList && ((ArrayList<?>) value).isEmpty() || !(value instanceof Boolean && (Boolean) value);

            }).collect(Collectors.toList());
            formParent.addAll(formChild);
            columnParent.addAll(columnChild);
            rowParent.addAll(rowChild);
            tabParent.addAll(tabChild);
            parent.setForm(formParent);
            parent.setColumn(columnParent);
            parent.setRow(rowParent);
            parent.setTab(tabParent);

            result.put("template", g.toJson(parent));
            result.remove("templateParent");
        }

        return result.isEmpty() ? null : result;
    }

}
