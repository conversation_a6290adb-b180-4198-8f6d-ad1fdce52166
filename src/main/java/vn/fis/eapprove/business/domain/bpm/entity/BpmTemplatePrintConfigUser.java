package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Data;

import jakarta.persistence.*;

@Data
@Entity
@Table(name = "bpm_template_print_config_user")
public class BpmTemplatePrintConfigUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;
    @Column(name = "username")
    private String username;
    @Column(name = "add_name")
    private String addName;
    @Column(name = "add_title")
    private String addTitle;
    @Column(name = "add_sign_image")
    private String addSignImage;
    @Column(name = "add_sign_date")
    private String addSignDate;
    @Column(name = "add_sign_action")
    private String addSignAction;
}
