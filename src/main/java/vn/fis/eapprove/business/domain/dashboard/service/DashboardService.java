package vn.fis.eapprove.business.domain.dashboard.service;

import vn.fis.eapprove.business.model.request.DashboardConfigRequest;
import vn.fis.eapprove.business.model.request.DashboardRequest;
import vn.fis.eapprove.business.model.response.DashboardConfigResponse;
import vn.fis.eapprove.business.model.response.DashboardResponse;
import vn.fis.eapprove.business.model.response.DashboardTotalResponse;

import java.util.List;

public interface DashboardService {
    List<DashboardResponse> getChartBarEmployee(DashboardRequest request);
    List<DashboardResponse> getChartPieStatus(DashboardRequest request);
    List<DashboardResponse> getChartPiePriority(DashboardRequest request);
    DashboardTotalResponse getTableEmployeeWorkload(DashboardRequest request);
    DashboardTotalResponse getTableProposalStatus(DashboardRequest request);

    // config
    List<DashboardConfigResponse> getDashboardConfig(String username);
    void createDashboardConfig(List<DashboardConfigRequest> lstRequest);
}
