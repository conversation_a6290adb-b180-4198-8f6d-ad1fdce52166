
package vn.fis.eapprove.business.domain.api.entity;

import lombok.*;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Author: PhucVM
 * Date: 29/11/2022
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "api_log")
public class ApiLog implements Serializable {

    private static final long serialVersionUID = 3276384015636232058L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "URL")
    private String url;

    @Column(name = "METHOD")
    private String method;

    @Column(name = "HEADER")
    private String header;

    @Column(name = "REQUEST_BODY")
    private String requestBody;

    @Column(name = "REQUEST_TIME")
    private LocalDateTime requestTime;

    @Column(name = "RESPONSE_TIME")
    private LocalDateTime responseTime;

    @Column(name = "RESPONSE_STATUS")
    private String responseStatus;

    @Column(name = "RESPONSE_DATA")
    private String responseData;

    @Column(name = "BPM_PROCDEF_API_ID")
    private Long bpmProcdefApiId;

    @Column(name = "BPM_PROCINST_ID")
    private Long bpmProcinstId;

    @Column(name = "API_TYPE")
    private String apiType;

    @Column(name = "RETRY_TIME")
    private LocalDateTime retryTime;

    @Column(name = "RETRY_COUNT")
    private Integer retryCount = 0;

    @Column(name = "APP_CODE")
    private String appCode;

    @Column(name = "ticket_name")
    private String ticketName;

    @Column(name = "task_def_key")
    private String taskDefKey;

    @Column(name = "api_name")
    private String apiName;

    @Column(name = "request_code")
    private String requestCode;

    @Transient
    private String realm;

    @Transient
    private boolean isLogAfter;

    @Transient
    private String procInstId;
}
