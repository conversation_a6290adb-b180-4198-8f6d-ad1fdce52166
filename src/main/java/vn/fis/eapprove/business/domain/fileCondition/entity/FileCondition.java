package vn.fis.eapprove.business.domain.fileCondition.entity;

import lombok.Data;

import jakarta.persistence.*;

@Data
@Entity
@Table(name = "condition_file")
public class FileCondition {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "TEMPLATE_HTML_ID")
    private Long htmlId;

    @Column(name = "TEMPLATE_HTML_CHANGE")
    private String templateHtmlChange;

    @Column(name = "TEMPLATE_HTML_HEADER")
    private String templateHtmlHeader;

    @Column(name = "TEMPLATE_HTML_FOOTER")
    private String templateHtmlFooter;

    @Column(name = "UPLOAD_WORDS")
    private String uploadWords;

    @Column(name = "UPLOAD_WORDS_CHANGE")
    private String uploadWordsChange;

    @Column(name = "STEP_NAME")
    private String name;

    @Column(name = "TASK_DEF_KEY")
    private String taskDefKey;

    @Column(name = "FORM_KEY")
    private String formKey;
    //0: execution
    //1: approval
    @Column(name = "TASK_TYPE")
    private int taskType = 0;

    @Column(name = "BPM_TEMPLATE_PRINT_ID")
    private Long bpmTemplatePrintId;

    @Column(name = "CONDITION_TEXT")
    private String conditionText;

}
