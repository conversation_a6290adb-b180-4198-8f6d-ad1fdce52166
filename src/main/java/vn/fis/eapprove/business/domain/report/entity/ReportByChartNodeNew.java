package vn.fis.eapprove.business.domain.report.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@Entity
@Table(name = "report_by_chart_node_new")
public class ReportByChartNodeNew {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "task_id")
    private String taskId;

    @Column(name = "proc_inst_id")
    private String procInstId;

    @Column(name = "task_type")
    private String taskType;

    @Column(name = "task_status")
    private String taskStatus;

    @Column(name = "service_id")
    private Long serviceId;

    @Column(name = "service_name")
    private String serviceName;

    @Column(name = "submission_type")
    private Long submissionType;

    @Column(name = "master_parent_id")
    private Long masterParentId;

    @Column(name = "request_code")
    private String requestCode;

    @Column(name = "title")
    private String title;

    @Column(name = "priority_id")
    private Long priorityId;

    @Column(name = "priority_name")
    private String priorityName;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "created_user_status")
    private String createdUserStatus;

    @Column(name = "created_user_full_name")
    private String createdUserFullName;

    @Column(name = "created_user_chart_id")
    private Long createdUserChartId;

    @Column(name = "created_user_chart_short_name")
    private String createdUserChartShortName;

    @Column(name = "created_user_chart_node_id")
    private Long createdUserChartNodeId;

    @Column(name = "created_user_chart_node_name")
    private String createdUserChartNodeName;

    @Column(name = "created_user_chart_node_code")
    private String createdUserChartNodeCode;

    @Column(name = "created_user_title_name")
    private String createdUserTitleName;

    @Column(name = "created_user_staff_code")
    private String createdUserStaffCode;

    @Column(name = "created_user_manager_level")
    private String createdUserManagerLevel;

    @Column(name = "created_user_email")
    private String createdUserEmail;

    @Column(name = "created_user_direct_manager")
    private String createdUserDirectManager;

    @Column(name = "assignee")
    private String assignee;

    @Column(name = "assignee_full_name")
    private String assigneeFullName;

    @Column(name = "assignee_chart_id")
    private Long assigneeChartId;

    @Column(name = "assignee_chart_short_name")
    private String assigneeChartShortName;

    @Column(name = "assignee_chart_node_id")
    private String assigneeChartNodeId;

    @Column(name = "assignee_chart_node_name")
    private String assigneeChartNodeName;

    @Column(name = "assignee_chart_node_code")
    private String assigneeChartNodeCode;

    @Column(name = "assignee_title_name")
    private String assigneeTitleName;

    @Column(name = "assignee_staff_code")
    private String assigneeStaffCode;

    @Column(name = "assignee_manager_level")
    private String assigneeManagerLevel;

    @Column(name = "assignee_email")
    private String assigneeEmail;

    @Column(name = "assignee_direct_manager")
    private String assigneeDirectManager;

    @Column(name = "location_id")
    private Long locationId;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "sla_finish_time")
    private LocalDateTime slaFinishTime;

    @Column(name = "finished_time")
    private LocalDateTime finishedTime;

    @Column(name = "is_expire")
    private Boolean isExpire;

    @Column(name = "started_time")
    private LocalDateTime startedTime;

    @Column(name = "task_name")
    private String taskName;

    @Column(name = "cancel_reason")
    private String cancelReason;

    @Column(name = "ticket_id")
    private Long ticketId;

    @Column(name = "proc_def_id")
    private String procDefId;

    @Column(name = "assignee_status")
    private String assigneeStatus;

    @Column(name = "version_time")
    private LocalDate versionTime;
}