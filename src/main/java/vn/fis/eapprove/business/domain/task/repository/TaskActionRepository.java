package vn.fis.eapprove.business.domain.task.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.task.entity.TaskAction;


import java.util.List;

@Repository
public interface TaskActionRepository extends JpaRepository<TaskAction, Long>, JpaSpecificationExecutor<TaskAction> {

    @Query("SELECT a FROM TaskAction a WHERE a.status = 1")
    List<TaskAction> findAllActiveTaskAction();
}
