package vn.fis.eapprove.business.domain.bpm.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcinstLink;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcinstLinkRepository;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcinstLinkManager;


import java.util.Collections;
import java.util.List;

@Service("BpmProcinstLinkManagerImplV1")
@Slf4j
@Transactional
public class BpmProcinstLinkManagerImpl implements BpmProcinstLinkManager {
    @Autowired
    BpmProcinstLinkRepository bpmProcinstLinkRepository;

    @Override
    public void deleteAllBpmProcinstLinkById(Long procInstId) {
        if (procInstId == null || procInstId < -1) {
            return;
        }
        bpmProcinstLinkRepository.deleteAllBpmProcinstLinkById(procInstId);
    }

    @Override
    public void saveAll(List<BpmProcinstLink> bpmProcinstLinkList) {
        if (bpmProcinstLinkList == null) {
            return;
        }
        bpmProcinstLinkRepository.saveAll(bpmProcinstLinkList);
    }

    @Override
    public void save(BpmProcinstLink procinstLink) {
        if (procinstLink == null) {
            return;
        }
        bpmProcinstLinkRepository.save(procinstLink);
    }

    @Override
    public List<BpmProcinstLink> findBpmProcinstLinkByBpmProcinstId(Long bpmProcinstId) {
        if (bpmProcinstId == null || bpmProcinstId < -1) {
            return null;
        }
        return bpmProcinstLinkRepository.findBpmProcinstLinkByBpmProcinstId(bpmProcinstId);
    }

    @Override
    public List<Long> getProcinstLinkByBpmProcinstId(Long bpmProcinstId) {
        if (bpmProcinstId == null || bpmProcinstId < -1) {
            return null;
        }
        return bpmProcinstLinkRepository.getProcinstLinkByBpmProcinstId(bpmProcinstId);
    }

    @Override
    public List<Long> getProcinstLinkByBpmProcinstLinkId(Long bpmProcinstLinkId) {
        if (bpmProcinstLinkId == null || bpmProcinstLinkId < -1) {
            return Collections.emptyList();
        }
        return bpmProcinstLinkRepository.getProcinstLinkByBpmProcinstLinkId(bpmProcinstLinkId);
    }
}
