package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcinstRecall;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 12/05/2023
 */
@Repository
public interface BpmProcinstRecallRepository extends JpaRepository<BpmProcinstRecall, Long>, JpaSpecificationExecutor<BpmProcinstRecall> {

    Optional<BpmProcinstRecall> findTop1ByProcInstIdOrderByRecallTimeDesc(@Param("procInstId") String procInstId);
}
