package vn.fis.eapprove.business.domain.assign.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "assign_history")
public class AssignHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "assign_id ")
    private Long assignId;

    @Column(name = "version")
    private String version;

    @Column(name = "ticket_id")
    private Long ticketId;

    @Column(name = "assign_time")
    private String assignTime;

    @Column(name = "assign_user")
    private String assignUser;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "request_code")
    private String requestCode;
}
