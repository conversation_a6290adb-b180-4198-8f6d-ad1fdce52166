package vn.fis.eapprove.business.domain.notification.service.impl;

import vn.fis.eapprove.security.CredentialHelper;
import jakarta.persistence.Tuple;
import lombok.extern.slf4j.Slf4j;

import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.authority.service.AuthService;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcdefRepository;
import vn.fis.eapprove.business.domain.notification.entity.NotificationTemplate;
import vn.fis.eapprove.business.domain.notification.repository.NotificationTemplateRepository;
import vn.fis.eapprove.business.domain.notification.service.NotificationTemplateService;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.system.repository.SystemGroupRepository;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.NotificationTemplateSearchRequest;
import vn.fis.eapprove.business.model.response.NotificationTemplateResponse;
import vn.fis.eapprove.business.specification.NotificationTemplateSpecification;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.util.QueryUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: AnhVTN
 * Date: 03/01/2023
 */

@Service
@Slf4j
@Transactional
public class NotificationTemplateServiceImpl implements NotificationTemplateService {

    private final NotificationTemplateRepository notificationTemplateRepo;
    private final NotificationTemplateSpecification notificationTemplateSpecification;
    private final ResponseUtils responseUtils;
    private final CredentialHelper credentialHelper;
    private final AuthService authService;
    private final CustomerService customerService;
    private final PermissionDataManagementRepository permissionDataManagementRepository;
    private final ModelMapper modelMapper;
    private final SystemGroupRepository systemGroupRepository;
    private final BpmProcdefRepository bpmProcdefRepository;

    @Autowired
    public NotificationTemplateServiceImpl(NotificationTemplateRepository notificationTemplateRepo,
                                           NotificationTemplateSpecification notificationTemplateSpecification,
                                           ResponseUtils responseUtils,
                                           CredentialHelper credentialHelper,
                                           AuthService authService,
                                           CustomerService customerService,
                                           PermissionDataManagementRepository permissionDataManagementRepository,
                                           ModelMapper modelMapper, SystemGroupRepository systemGroupRepository, BpmProcdefRepository bpmProcdefRepository) {
        this.notificationTemplateRepo = notificationTemplateRepo;
        this.notificationTemplateSpecification = notificationTemplateSpecification;
        this.responseUtils = responseUtils;
        this.credentialHelper = credentialHelper;
        this.authService = authService;
        this.customerService = customerService;
        this.permissionDataManagementRepository = permissionDataManagementRepository;
        this.modelMapper = modelMapper;
        this.systemGroupRepository = systemGroupRepository;
        this.bpmProcdefRepository = bpmProcdefRepository;
    }

    @Override
    public NotificationTemplate getTemplateByCondition(Long id, String type) {
        List<NotificationTemplate> lstTemp = notificationTemplateRepo.getTemplateByCondition(id, type);
        if (lstTemp.size() > 0) {
            return lstTemp.get(0);
        }
        return null;
    }

    public NotificationTemplate findNotificationTemplateById(Long id) {
        NotificationTemplate notificationTemplate = notificationTemplateRepo.findNotificationTemplateById(id);
        return notificationTemplate;
    }

    public List<NotificationTemplate> findNotificationTemplatesByActionCodeAndTypeAndNotificationObject(String actionCode, String type, String notificationObject) {
        List<NotificationTemplate> notificationTemplates = notificationTemplateRepo.findNotificationTemplatesByActionCodeAndTypeAndNotificationObject(actionCode, type, notificationObject);
        return notificationTemplates;
    }

    public List<NotificationTemplate> findNotificationTemplatesByActionCodeAndType(String actionCode, String type) {
        List<NotificationTemplate> notificationTemplates = notificationTemplateRepo.findNotificationTemplatesByActionCodeAndType(actionCode, type);
        return notificationTemplates;
    }

    public List<NotificationTemplate> findNotificationTemplatesByTitleAndType(String title, String type) {
        List<NotificationTemplate> notificationTemplates = notificationTemplateRepo.findNotificationTemplatesByTitleEqualsIgnoreCaseAndType(title, type);
        return notificationTemplates;
    }

    @Override
    public PageDto getTemplates(NotificationTemplateSearchRequest request)  {
        int pageNum = request.getPage() - 1;
        Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());
        List<NotificationTemplate> templates = new ArrayList<>();
        List<NotificationTemplateResponse> listResponses = new ArrayList<>();

        String username = credentialHelper.getJWTPayload().getUsername();
        // Lấy list companyCode cấu hình QL vai trò người dùng
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

        // Phân quyền theo nhóm
        List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.NOTIFICATION_TEMPLATE.tableName, username);
        // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
        if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
            request.setLstGroupPermissionId(lstGroupPermissionId);
            // get list company code role admin member
            List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
            request.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
        }

        Page<NotificationTemplate> page = notificationTemplateRepo.findAll(
                notificationTemplateSpecification.filter(request, lstCompanyCode),
                PageRequest.of(pageNum, request.getSize(), sort)
        );
        if (page.hasContent()) {
            templates = page.getContent();
            List<Tuple> notiTemplates = bpmProcdefRepository.findAllByTemplateNotiId(templates.stream().map(NotificationTemplate::getId).collect(Collectors.toList()));
            Map<Long, Set<String>> notiTemplateProcdefApply = new HashMap<>();
            for (Tuple tuple : notiTemplates) {
                Integer idOld = QueryUtils.getValueFromTuple(tuple, "templateId", Integer.class);
                Long id = idOld != null ? Long.valueOf(idOld) : null;
                String name = QueryUtils.getValueFromTuple(tuple, "name", String.class);
                Set<String> oldList;
                if (notiTemplateProcdefApply.get(id) == null || notiTemplateProcdefApply.get(id).isEmpty()) {
                    oldList = new HashSet<>();
                } else {
                    oldList = notiTemplateProcdefApply.get(id);
                }
                oldList.add(name);
                notiTemplateProcdefApply.put(id, oldList);
            }

            listResponses = templates.stream().map(x -> modelMapper.map(x, NotificationTemplateResponse.class)).collect(Collectors.toList());
            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.NOTIFICATION_TEMPLATE.code);

            for (NotificationTemplateResponse response : listResponses) {
                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(response.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
                if(notiTemplateProcdefApply.get(response.getId()) != null) {
                    response.setProcDefApply(new ArrayList<>(notiTemplateProcdefApply.get(response.getId())));
                }
                if (!ValidationUtils.isNullOrEmpty(companyCodes)) {
                    response.setApplyFor(companyCodes);
                }
            }
        }
        return PageDto.builder().content(listResponses)
                .number(page.getNumber() + 1)
                .numberOfElements(page.getNumberOfElements())
                .page(page.getNumber() + 1)
                .size(page.getSize())
                .sortBy(request.getSortBy())
                .sortBy(request.getSortType())
                .totalPages(page.getTotalPages())
                .totalElements(page.getTotalElements())
                .build();
    }

    @Override
    public List<NotificationTemplateResponse> getTemplatesFilter(NotificationTemplateSearchRequest request)  {
//        int pageNum = request.getPage() - 1;
//        Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());
        List<NotificationTemplate> templates;
        List<NotificationTemplateResponse> listResponses = new ArrayList<>();

        String username = credentialHelper.getJWTPayload().getUsername();
        // Lấy list companyCode cấu hình QL vai trò người dùng
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

        // Phân quyền theo nhóm
        List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.NOTIFICATION_TEMPLATE.tableName, username);
        // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
        if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
            request.setLstGroupPermissionId(lstGroupPermissionId);
            // get list company code role admin member
            List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
            request.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
        }

        List<NotificationTemplate> page = notificationTemplateRepo.findAll(
                notificationTemplateSpecification.filter(request, lstCompanyCode)
//                , PageRequest.of(pageNum, request.getSize(), sort)
        );
        if (!ValidationUtils.isNullOrEmpty(page)) {
            templates = page;
            listResponses = templates.stream().map(x -> modelMapper.map(x, NotificationTemplateResponse.class)).collect(Collectors.toList());
            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.NOTIFICATION_TEMPLATE.code);

            for (NotificationTemplateResponse response : listResponses) {
                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(response.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
                if (!ValidationUtils.isNullOrEmpty(companyCodes)) {
                    response.setApplyFor(companyCodes);
                }
            }
        }
        return listResponses;
    }


    @Override
    public NotificationTemplate save(NotificationTemplate request) {
        return notificationTemplateRepo.save(request);
    }

    @Override
    public void deleteByIds(Long[] ids) {
        notificationTemplateRepo.deleteByIds(ids);
    }

    @Override
    public List<NotificationTemplate> getAllTemplates() {
        List<NotificationTemplate> templates = notificationTemplateRepo.findAll();
        return notificationTemplateRepo.findAll();
    }

    @Override
    public Object getAllSystemGroup()  {
        List<Map<String, Object>> lstResponse = new ArrayList<>();
        NotificationTemplateSearchRequest request = new NotificationTemplateSearchRequest();
        request.setPage(1);
        request.setLimit(999999);
        request.setSortBy("id");
        request.setSortType("DESC");
        request.setType("");

        int pageNum = request.getPage() - 1;
        Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());

        String username = credentialHelper.getJWTPayload().getUsername();
        // Lấy list companyCode cấu hình QL vai trò người dùng
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

        // Phân quyền theo nhóm
        List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.NOTIFICATION_TEMPLATE.tableName, username);
        // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
        if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
            request.setLstGroupPermissionId(lstGroupPermissionId);
            // get list company code role admin member
            List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
            request.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
        }

        Page<NotificationTemplate> page = notificationTemplateRepo.findAll(
                notificationTemplateSpecification.filter(request, lstCompanyCode),
                PageRequest.of(pageNum, request.getLimit(), sort));
        List<NotificationTemplate> lstNoti = page.getContent();
        for (NotificationTemplate template : lstNoti) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", template.getId());
            map.put("name", template.getTitle());
            map.put("companyCode", template.getCompanyCode());
            map.put("companyName", template.getCompanyName());
            map.put("type", template.getType());

            lstResponse.add(map);
        }

        return lstResponse;
    }
}
