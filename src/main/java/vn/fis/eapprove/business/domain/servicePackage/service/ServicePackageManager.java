package vn.fis.eapprove.business.domain.servicePackage.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import vn.fis.eapprove.security.CredentialHelper;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import vn.fis.eapprove.business.config.GsonAdapterConfig;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.bpmn.instance.UserTask;

import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import vn.fis.eapprove.business.domain.authority.service.AuthService;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrint;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcInstRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcdefRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTemplatePrintRepository;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcdefManager;
import vn.fis.eapprove.business.domain.bpm.service.BpmTemplatePrintManager;
import vn.fis.eapprove.business.domain.evaluation.repository.EvaluationCriteriaRepository;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;
import vn.fis.eapprove.business.domain.priority.repository.PriorityManagementRepository;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.repository.ServicePackageRepository;
import vn.fis.eapprove.business.domain.system.repository.SystemGroupRepository;
import vn.fis.eapprove.business.domain.template.entity.TemplateManage;
import vn.fis.eapprove.business.domain.template.service.TemplateManager;
import vn.fis.eapprove.business.dto.BudgetServiceDto;
import vn.fis.eapprove.business.dto.ServicePackageDto;
import vn.fis.eapprove.business.dto.UserInfoDto;
import vn.fis.eapprove.business.exception.BusinessException;
import vn.fis.eapprove.business.model.request.BasePageRequest;
import vn.fis.eapprove.business.model.request.SearchServicePackRequest;
import vn.fis.eapprove.business.model.request.ServicePackageFilter;
import vn.fis.eapprove.business.model.request.ServicePackageRequest;
import vn.fis.eapprove.business.model.response.ChildResponse;
import vn.fis.eapprove.business.model.response.NameAndCodeCompanyResponse;
import vn.fis.eapprove.business.model.response.ServicePackageResponse;
import vn.fis.eapprove.business.specification.ServicePackageSpecification;
import vn.fis.eapprove.business.tenant.manager.*;
import vn.fis.eapprove.business.utils.*;
import vn.fis.spro.common.FileNameAwareByteArrayResource;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ProcDefConstants;
import vn.fis.spro.common.model.response.UserInfoResponse;
import vn.fis.spro.common.util.ListCompare;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.exception.FileOperationException;
import vn.fis.spro.file.manager.FileManager;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static vn.fis.eapprove.business.exception.ServicePackageErrorCode.SERVICE_PACKAGE_NOT_EXIST;
import static vn.fis.spro.common.constants.ProcDefConstants.Status.DEACTIVE;

@Service
@Slf4j
@Transactional
public class ServicePackageManager {

    public static List<Integer> SERVICE_TYPE_1_2 = new ArrayList<>() {{
        add(1);
        add(2);
    }};
    public static List<String> HIDE_NOT = new ArrayList<>() {{
        add("HIDE");
        add("NOT");
    }};
    @Value("${app.superAdmin.account}")
    private String superAdminRealm;
    @Value("${app.s3.bucket}")
    private String bucket;
    @Autowired
    ModelMapper modelMapper;
    @Autowired
    CustomerService customerService;
    @Autowired
    CredentialHelper credentialHelper;
    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private EvaluationCriteriaRepository evaluationCriteriaRepository;
    @Autowired
    private ServicePackageRepository servicePackageRepository;
    @Autowired
    private SproProperties sproProperties;
    @Autowired
    private RedirectApiUtils redirectApiUtils;
    @Autowired
    private BpmProcdefRepository bpmProcdefRepository;
    @Autowired
    private BpmProcInstRepository bpmProcInstRepository;
    @Autowired
    private ServicePackageSpecification servicePackageSpecification;
    @Autowired
    private ResponseUtils responseUtils;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private PermissionDataManagementRepository permissionDataManagementRepository;
    @Autowired
    private AuthService authService;
    @Autowired
    PriorityManagementRepository priorityManagementRepository;
    @Autowired
    private CamundaEngineService camundaEngineService;
    @Autowired
    @Lazy
    private BpmProcdefManager bpmProcdefManager;
    @Autowired
    private TemplateManager templateManager;
    @Autowired
    private BpmTemplatePrintRepository bpmTemplatePrintRepository;
    @Autowired
    @Lazy
    private BpmTemplatePrintManager bpmTemplatePrintManager;
    @Autowired
    private SystemGroupRepository systemGroupRepository;

    public ServicePackageResponse createServicePackage(ServicePackageRequest servicePackageRequest) {
        try {
            ServicePackage sp = new ServicePackage();

            if (servicePackageRequest.getServiceName() == null) {
                return null;
            }
            String hideName = new Gson().toJson(servicePackageRequest.getHideName());
            String visibleName = new Gson().toJson(servicePackageRequest.getVisibleName());
            String visibleGroup = new Gson().toJson(servicePackageRequest.getVisibleGroup());
            String hideGroup = new Gson().toJson(servicePackageRequest.getHideGroup());
            String visibleChart = new Gson().toJson(servicePackageRequest.getVisibleChart());
            String hideChart = new Gson().toJson(servicePackageRequest.getHideChart());

            sp.setParentId(servicePackageRequest.getParentId() != null ? servicePackageRequest.getParentId() : null);

            sp.setServiceName(servicePackageRequest.getServiceName());
            sp.setColor(servicePackageRequest.getColor());
            sp.setIcon(servicePackageRequest.getIcon());
            sp.setServiceType(servicePackageRequest.getServiceType());
            sp.setProcessId(servicePackageRequest.getProcessId());
//            sp.setPositionPackage(servicePackageRequest.getPositionPackage());
            sp.setUrl(servicePackageRequest.getUrl());
            sp.setDescription(servicePackageRequest.getDescription() != null ? servicePackageRequest.getDescription() : "");
            sp.setNote(servicePackageRequest.getNote());
            sp.setNotShowingMoblie(servicePackageRequest.getNotShowingMoblie() != null ? servicePackageRequest.getNotShowingMoblie() : false);
            sp.setNotShowingWebsite(servicePackageRequest.getNotShowingWebsite() != null ? servicePackageRequest.getNotShowingWebsite() : false);
            sp.setHideName(hideName);
            sp.setVisibleName(visibleName);
            sp.setVisibleGroup(visibleGroup);
            sp.setHideGroup(hideGroup);
            sp.setIdOrgChart(servicePackageRequest.getIdOrgChart());
            sp.setMasterParentId(servicePackageRequest.getMasterParentId());
            sp.setDeleted(false);
            sp.setCreatedDate(LocalDateTime.now());
            sp.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            sp.setHideAllUser(servicePackageRequest.getHideAllUser());
            sp.setVisibleAllUser(servicePackageRequest.getVisibleAllUser());
            sp.setSubmissionType(servicePackageRequest.getSubmissionType());
            sp.setLegislativeRequirement(servicePackageRequest.getLegislativeRequirement());

            sp.setVisibleChart(visibleChart);
            sp.setHideChart(hideChart);

            if (servicePackageRequest.getServiceType() == 2) {
                sp.setStatus(DEACTIVE.code);
            }

            List<String> lstParentCompanyCode = servicePackageRequest.getParentCompanyCode();

            if (!ValidationUtils.isNullOrEmpty(servicePackageRequest.getSpecialFlow())) {
                sp.setSpecialFlow(servicePackageRequest.getSpecialFlow());
            }

            if (!ValidationUtils.isNullOrEmpty(servicePackageRequest.getSpecialApplyFor())) {
                sp.setSpecialApplyFor(servicePackageRequest.getSpecialApplyFor());

                // get list companyCode by parentCodes
                if (servicePackageRequest.getSpecialApplyFor()) {
                    List<String> lstCompanyCode = customerService.listChildCompanyCodeByParentCode(servicePackageRequest.getParentCompanyCode());
                    if (!ValidationUtils.isNullOrEmpty(lstCompanyCode)) {
                        lstParentCompanyCode.addAll(lstCompanyCode);
                    }
                }
            }
            if (!ValidationUtils.isNullOrEmpty(lstParentCompanyCode)) {
                lstParentCompanyCode = lstParentCompanyCode.stream().distinct().collect(Collectors.toList());
                String parentCompanyCode = new Gson().toJson(lstParentCompanyCode);
                sp.setParentCompanyCode(parentCompanyCode);
            }

            List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
            for (NameAndCodeCompanyResponse response : listCompanyCodeAndName) {
                sp.setCompanyCode(response.getCompanyCode());
                sp.setCompanyName(response.getCompanyName());
            }

            ServicePackage spn = servicePackageRepository.save(sp);
            sp.setPositionPackage(Math.toIntExact(spn.getId()));
            spn = servicePackageRepository.save(sp);

            // Lưu phân quyền dữ liệu
            if (!ValidationUtils.isNullOrEmpty(servicePackageRequest.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : servicePackageRequest.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(spn.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.SERVICE_PACKAGE.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                permissionDataManagementRepository.saveAll(permissionDataManagements);
            }

            BpmProcdef bpmProcdef = null;
            ServicePackageResponse spr = modelMapper.map(spn, ServicePackageResponse.class);
            if (servicePackageRequest.getServiceType() == 2) {
                if (servicePackageRequest.getProcessId() != null) {
                    bpmProcdef = bpmProcdefRepository.findBpmProcdefById(servicePackageRequest.getProcessId()).get(0);
                    if (bpmProcdef != null) {
                        if (!bpmProcdef.getStatus().equals(ProcDefConstants.Status.ACTIVE.code)) {
                            Long serviceCount = bpmProcdef.getServiceCount();
                            bpmProcdef.setServiceCount(serviceCount != null ? serviceCount + 1 : 1);
                            bpmProcdef.setStatus(ProcDefConstants.Status.ACTIVE.code);
                            bpmProcdefRepository.save(bpmProcdef);
                        }
                    }
                }
            }

            // Clone luồng đặc biệt
            if (!ValidationUtils.isNullOrEmpty(servicePackageRequest.getSpecialFlow()) && !ValidationUtils.isNullOrEmpty(lstParentCompanyCode)) {
                if (servicePackageRequest.getProcessId() != null && servicePackageRequest.getSpecialFlow() && bpmProcdef != null) {
                    for (String companyCode : lstParentCompanyCode) {
                        Resource resource = new FileNameAwareByteArrayResource(bpmProcdef.getResourceName(), bpmProcdef.getBytes(), null);

                        Map<String, Object> mapResponseCreate = camundaEngineService.createDeployment(resource);
                        String deploymentId = mapResponseCreate.get("id").toString();
                        BpmnModelInstance bpmnModelInstance = CamundaUtils.convertBytesToBpmnModelInstance(bpmProcdef.getBytes());

                        // Lấy ds formkey trong file camunda
                        List<String> lstFormKey = new ArrayList<>();
                        Collection<StartEvent> startEvents = bpmnModelInstance.getModelElementsByType(StartEvent.class);
                        for (StartEvent startEvent : startEvents) {
                            lstFormKey.add(startEvent.getCamundaFormKey());
                        }
                        Collection<UserTask> userTasks = CamundaUtils.getUserTasks(bpmnModelInstance);
                        for (UserTask userTask : userTasks) {
                            lstFormKey.add(userTask.getCamundaFormKey());
                        }
                        List<TemplateManage> lstTemplate = templateManager.getByListFormKey(lstFormKey);

                        // Quy trình
                        List<String> lstFormKeyClone = lstFormKey.stream().map(formKey -> formKey + "-" + sp.getId().toString() + "-" + companyCode).collect(Collectors.toList());
                        BpmProcdef bpmProcDefResponse = bpmProcdefManager.createCopySpecial(bpmProcdef, deploymentId, companyCode, lstFormKeyClone, sp.getId());

                        // Mẫu trình ký
                        List<BpmTemplatePrint> lstTemplatePrint = bpmTemplatePrintRepository.getByProcDefId(bpmProcdef.getProcDefId());
                        if (!ValidationUtils.isNullOrEmpty(lstTemplatePrint)) {
                            bpmTemplatePrintManager.cloneTemplatePrintSpecial(lstTemplatePrint, bpmProcDefResponse, sp.getId(), companyCode);
                        }

                        // Dịch vụ
                        copyServicePackageSpecial(sp, companyCode, bpmProcDefResponse.getId());

                        // Biểu mẫu
                        if (!ValidationUtils.isNullOrEmpty(lstTemplate)) {
                            for (TemplateManage templateManage : lstTemplate) {
                                templateManager.cloneTemplateSpecial(templateManage, companyCode, sp.getId());
                            }
                        }
                    }
                }
            }

            return spr;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Map<String, Object> deleteServicePackageByListId(List<Long> lstId) {
        List<Long> checkPackage = new ArrayList<>();
        Map<String, Object> response = new HashMap<>();
        for (Long id : lstId) {
            ServicePackage servicePackage = servicePackageRepository.getServicePackageById(id);

            // check nếu gói dịch vụ là loại Tạo yêu cầu ( type = 2) và đã phát sinh dịch vụ con thi không cho xóa

            if (servicePackage.getServiceType().equals(2) && servicePackage.getStatus().equals("ACTIVE")) {
                response.put("status", 0);
                response.put("message", "Dịch vụ đã phát sinh phiếu yêu cầu. Không được xóa!");
                return response;
            }

            List<ServicePackage> listChild = servicePackageRepository.getListChildServicePackageByParentId(Collections.singletonList(id));
            for (ServicePackage e : listChild) {
                if (e.getServiceType().equals(2) && e.getStatus().equals("ACTIVE")) {
                    response.put("status", 0);
                    response.put("message", "Dịch vụ đã phát sinh dịch vụ con . Không được xóa!");
                    return response;
                }
            }

            // Xóa special clone service
            if (servicePackage.getSpecialFlow()) {
                // find special child service
                List<Long> specialIds = getListIdBySpecialParentId(id);
                Boolean check = CheckServiceTicket(ValidationUtils.isNullOrEmpty(specialIds) ? Collections.singletonList(id) : specialIds);
                if (check) {
                    response.put("status", 0);
                    response.put("message", "Dịch vụ đang tồn tại trong Quản lý phiếu yêu cầu. Không được xóa!");
                    return response;
                }

                Boolean result = deleteSpecialServicePackage(id);
                if (!result) {
                    return null;
                }
            } else { // normal service
                List<Long> ids = getListIdByParentId(id);
                checkPackage.addAll(ids);
                Boolean check = CheckServiceTicket(ids.isEmpty() ? Collections.singletonList(id) : ids);
                if (check) {
                    response.put("status", 0);
                    response.put("message", "Dịch vụ đang tồn tại trong Quản lý phiếu yêu cầu. Không được xóa!");
                    return response;
                }

                Boolean result = deleteServicePackage(id);
                if (!result) {
                    return null;
                }
            }
        }
        if (!checkPackage.isEmpty()) {
            response.put("status", 1);
            response.put("message", "Xóa gói vụ thành công");
            return response;
        } else {
            response.put("status", 1);
            response.put("message", "Xóa dịch vụ thành công");
            return response;
        }
    }

    public Boolean deleteServicePackage(Long id) {
        try {
            ServicePackage servicePackage = servicePackageRepository.findById(id).orElseThrow(() -> new BusinessException(SERVICE_PACKAGE_NOT_EXIST));
            deleteService(id);
            List<ServicePackage> servicePackageListChild = servicePackageRepository.findByParentId(servicePackage.getId());
            if (servicePackageListChild != null && !servicePackageListChild.isEmpty()) {
                for (ServicePackage sp : servicePackageListChild) {
                    deleteServicePackage(sp.getId());
                }
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public Boolean deleteSpecialServicePackage(Long id) {
        try {
            ServicePackage servicePackage = servicePackageRepository.findById(id).orElseThrow(() -> new BusinessException(SERVICE_PACKAGE_NOT_EXIST));
            deleteService(id);
            List<ServicePackage> servicePackageListChild = servicePackageRepository.findBySpecialParentId(servicePackage.getId());
            if (servicePackageListChild != null && servicePackageListChild.size() > 0) {
                for (ServicePackage sp : servicePackageListChild) {
                    deleteServicePackage(sp.getId());
                }
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public void deleteService(Long id) {
        try {
            ServicePackage servicePackage = servicePackageRepository.findById(id).orElseThrow(() -> new BusinessException(SERVICE_PACKAGE_NOT_EXIST));
            servicePackage.setDeleted(true);
            servicePackageRepository.saveAndFlush(servicePackage);
            Long countService = servicePackageRepository.countByProcessIdAndDeleted(servicePackage.getProcessId(), false);
            if (Objects.equals(countService, 0l)) {
                bpmProcdefRepository.resetStatus(servicePackage.getProcessId());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public List<Long> getListIdByParentId(Long parentId) {
        try {
            List<Long> listId = new ArrayList<>();
            List<ServicePackage> servicePackageList = servicePackageRepository.findByParentId(parentId);
            if (servicePackageList != null && servicePackageList.size() > 0) {
                for (ServicePackage sp : servicePackageList) {
                    listId.add(sp.getId());
                    getListIdByParentId(sp.getId());
                }
            }
            return listId;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public List<Long> getListIdBySpecialParentId(Long parentId) {
        try {
            List<Long> listId = new ArrayList<>();
            List<ServicePackage> servicePackageList = servicePackageRepository.findBySpecialParentId(parentId);
            if (!ValidationUtils.isNullOrEmpty(servicePackageList)) {
                for (ServicePackage sp : servicePackageList) {
                    listId.add(sp.getId());
                }
            }
            return listId;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Boolean checkExistName(String serviceName, Long id, Long idOrgChart, Boolean deleted, Long parentId, List<String> visibleChart, Integer serviceType) {
        try {
            if (parentId == null) {
                Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();
                List<ServicePackage> listServiceParent = servicePackageRepository.getAllParentServicePackage(serviceName, id);
                for (ServicePackage service : listServiceParent) {
                    List<String> listVisibleChart = g.fromJson(service.getVisibleChart(), ArrayList.class);
                    if (
                            listVisibleChart.contains("-1") ||
                                    visibleChart.contains("-1") ||
                                    listVisibleChart.stream().anyMatch(visibleChart::contains))
                        return true;
                }
            } else
                return !servicePackageRepository.CheckExistName(serviceName, id, idOrgChart, deleted, parentId, serviceType).isEmpty();
            return false;
//            return servicePackage != null;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }
//
//    public List<ServicePackage> getAllParentServicePackage() {
//        try {
//            List<ServicePackage> servicePackage = servicePackageRepository.getAllParentServicePackage();
//            return servicePackage;
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return null;
//        }
//    }

//    public Boolean isExistName(String serviceName, Long id, Long idOrgChart, Boolean deleted) {
//        try {
//            ServicePackage servicePackage = servicePackageRepository.CheckExistName(serviceName, id, idOrgChart, deleted);
//            if (servicePackage != null) {
//                return true;
//            } else {
//                return false;
//            }
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return true;
//        }
//    }

    public Boolean CheckServiceTicket(List<Long> listIds) {
        try {
            for (Long id : listIds) {
                ServicePackage servicePackage = servicePackageRepository.findById(id).get();
                if (servicePackage.getServiceType().equals(2)) {
                    return bpmProcInstRepository.existsByServiceIdIn(Collections.singleton(id));
                } else if (servicePackage.getServiceType().equals(1)) {
                    List<ServicePackage> start = Collections.singletonList(servicePackage);
                    while (true) {
                        List<Long> serviceIdsNeedCheck = start.stream().map(ServicePackage::getId).collect(Collectors.toList());
                        List<ServicePackage> needChecks;
                        needChecks = servicePackageRepository.getAllByParentIdInAndDeletedFalseAndServiceTypeIn(
                                serviceIdsNeedCheck, SERVICE_TYPE_1_2
                        );
                        if (CollectionUtils.isEmpty(needChecks)) {
                            return false;
                        } else {
                            List<ServicePackage> list1 = new ArrayList<>();
                            List<ServicePackage> list2 = new ArrayList<>();
                            needChecks.forEach(o -> {
                                if (o.getServiceType().equals(2)) {
                                    list2.add(o);
                                } else if (o.getServiceType().equals(1)) {
                                    list1.add(o);
                                }
                            });

                            Set<Long> id2 = list2.stream().map(ServicePackage::getId).collect(Collectors.toSet());
                            if (bpmProcInstRepository.existsByServiceIdIn(id2)) {
                                return true;
                            } else {
                                if (list1.isEmpty()) {
                                    return false;
                                } else {
                                    start = list1;
                                }
                            }
                        }
                    }
                }
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return true;
        }
    }

    private Set<ServicePackage> getParent(List<ServicePackage> all, ServicePackage servicePackage, Set<ServicePackage> response) {
        for (ServicePackage aPackage : all) {
            if (aPackage.getParentId() == servicePackage.getId()) {
                getChild(all, aPackage, response);
                response.add(aPackage);
            }
        }
        return response;
    }

    public List<String> getAllservicePackagesActive() {
        return servicePackageRepository.getAllServiceActive();
    }

    public List<Map<String, Object>> loadFilterService() {
        return servicePackageRepository.loadFilterService();
    }

    private Set<ServicePackage> getChild(List<ServicePackage> all, ServicePackage servicePackage, Set<ServicePackage> response) {
        for (ServicePackage aPackage : all) {
            if (aPackage.getId() == servicePackage.getParentId()) {
                getChild(all, aPackage, response);
                response.add(aPackage);
            }
        }
        return response;
    }

    //Hàm lấy data và search ở trên web và app
    public List<ServicePackageResponse> getAll(SearchServicePackRequest searchService, String realm, boolean isAdmin) {
        try {
            // Chỉnh phần filter
            boolean isWeb = CastUtils.toBooleanDefaultIfNull(searchService.getShowWeb(), true);
            List<Integer> serviceTypes = searchService.getServiceType();
            serviceTypes = Objects.isNull(serviceTypes) ? new ArrayList<>() : serviceTypes;
            List<Integer> specialServiceTypes = searchService.getSpecialServiceType();
            specialServiceTypes = Objects.isNull(specialServiceTypes) ? new ArrayList<>() : specialServiceTypes;
            int pageNum = searchService.getPage() - 1;
            Sort sort = responseUtils.getSort(searchService.getSortBy(), searchService.getSortType());
            String username = credentialHelper.getJWTPayload().getUsername();

            // Lấy list companyCode cấu hình QL vai trò người dùng
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.SERVICE_PACKAGE.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                searchService.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                searchService.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            // Ham query chính
            Page<ServicePackage> page = servicePackageRepository.findAll(
                    servicePackageSpecification.getServicePackageAll(
                            searchService.getProcessName(),
                            searchService.getServiceName(),
                            serviceTypes,
                            searchService.getChartId(),
                            isWeb,
                            isAdmin,
                            searchService.getListStatus(),
                            searchService.getCreatedUser(),
                            searchService.getModifiedUser(),
                            searchService.getFromDate(),
                            searchService.getToDate(),
                            searchService.getVisibleAllUser(),
                            lstCompanyCode,
                            searchService.getSpecialFlow(),
                            specialServiceTypes,
                            searchService.getIsShared(), searchService, false
                    ), PageRequest.of(pageNum, searchService.getLimit(), sort));

            List<ServicePackage> packages = page.getContent().stream().map(e -> modelMapper.map(e, ServicePackage.class)).collect(Collectors.toList());

            // Lọc theo hide vs visible
            List<ServicePackage> toRemove = new ArrayList<>();

            // admin chỉ filter theo applyFor
//            if (!isAdmin) {
//                toRemove = filterByUserAndGroup(packages, username);
//            }

            // Lấy tất cả các master parent Id
            List<ServicePackage> listRes = new ArrayList<ServicePackage>(packages);
            Set<Long> masterParentIds = listRes.stream().map(ServicePackage::getMasterParentId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            Set<Long> masterParentIdIn = listRes.stream()
                    .filter(o -> Objects.isNull(o.getParentId()))
                    .map(ServicePackage::getId)
                    .collect(Collectors.toSet());
            masterParentIds = CastUtils.mergeSet(masterParentIds, masterParentIdIn);

            // Lấy tất cả các data từ các master để về sau có data các cha con đệ quy tìm ra địa chỉ
            List<ServicePackage> listAll;
            if (isAdmin) {
                listAll = servicePackageRepository.findAllByMasterParentIdInAndDeletedFalseOrIdInAndDeletedFalse(
                        masterParentIds, masterParentIds
                );
            } else {
                if (isWeb) {
                    listAll = servicePackageRepository.findAllByMasterParentIdInAndDeletedFalseAndNotShowingWebsiteFalseOrIdInAndDeletedFalseAndNotShowingWebsiteFalse(
                            masterParentIds, masterParentIds
                    );
                } else {
                    listAll = servicePackageRepository.findAllByMasterParentIdInAndDeletedFalseAndNotShowingMoblieFalseOrIdInAndDeletedFalseAndNotShowingMoblieFalse(
                            masterParentIds, masterParentIds
                    );
                }
            }
            listAll.removeAll(listRes);
            listAll.addAll(listRes);


            // Viết tách ra 1 hàm khác xử lý ẩn, phân quyền lọc bớt để trả về listRes là kết quả cuối
            listRes = processListServicePackageToShow(listRes, listAll, isAdmin, isWeb);

            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.SERVICE_PACKAGE.code);

            // tab search shared
            if (!ValidationUtils.isNullOrEmpty(searchService.getIsShared()) && searchService.getIsShared()) {
                listRes = filterSharedByUserAndGroup(packages, username);
            } else
                // admin search sort service
                if (!ValidationUtils.isNullOrEmpty(searchService.getSortCompanyCode())) {
                    List<Long> lstTypeId = permissionDataManagements.stream().map(data -> {
                        if (searchService.getSortCompanyCode().contains(data.getCompanyCode()) || data.getCompanyCode().equalsIgnoreCase(CommonConstants.FILTER_SELECT_ALL)) {
                            return data.getTypeId();
                        }
                        return null;
                    }).distinct().toList();
                    listRes = listRes.stream()
                            .filter(e -> lstTypeId.contains(e.getId())
                                    || (!ValidationUtils.isNullOrEmpty(searchService.getLstGroupPermissionId())
                                    && searchService.getLstGroupPermissionId().contains(e.getId()))
                            )
                            .collect(Collectors.toList());
                } else
                    // admin search tab normal
                    if (!ValidationUtils.isNullOrEmpty(lstCompanyCode) && !lstCompanyCode.contains(CommonConstants.FILTER_SELECT_ALL)) {
                        if (!ValidationUtils.isNullOrEmpty(searchService.getIsShared()) && !searchService.getIsShared()) {
                            List<Long> lstTypeId;
                            // phân quyền bổ sung - ưu tiên group permission
                            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                                lstTypeId = permissionDataManagements.stream().map(data -> {
                                    if (searchService.getListCompanyCodeMemberAdmin().contains(data.getCompanyCode()) || data.getCompanyCode().equalsIgnoreCase(CommonConstants.FILTER_SELECT_ALL)) {
                                        return data.getTypeId();
                                    }
                                    return null;
                                }).distinct().collect(Collectors.toList());
                            } else {
                                lstTypeId = permissionDataManagements.stream().map(data -> {
                                    if (lstCompanyCode.contains(data.getCompanyCode()) || data.getCompanyCode().equalsIgnoreCase(CommonConstants.FILTER_SELECT_ALL)) {
                                        return data.getTypeId();
                                    }
                                    return null;
                                }).distinct().collect(Collectors.toList());
                            }
                            listRes = listRes.stream()
                                    .filter(e -> lstTypeId.contains(e.getId())
                                            || (!ValidationUtils.isNullOrEmpty(searchService.getLstGroupPermissionId())
                                            && searchService.getLstGroupPermissionId().contains(e.getId()))
                                    )
                                    .collect(Collectors.toList());
                        }
                    }
            if (!isAdmin) {
                toRemove = filterByUserAndGroup(listRes, username);
            }
            listRes.removeAll(toRemove);

            // lấy thông tin phân quyền dữ liệu
            List<Long> listProcessId = listRes.stream().map(ServicePackage::getProcessId).collect(Collectors.toList());
            listProcessId.removeIf(Objects::isNull);
            List<BpmProcdef> bpmProcdefs = bpmProcdefRepository.getBpmProcdefListId(listProcessId);
            Map<Long, BpmProcdef> mapProdef = new HashMap<>();
            for (BpmProcdef bmpBpmProcdef : bpmProcdefs) {
                mapProdef.put(bmpBpmProcdef.getId(), bmpBpmProcdef);
            }
//            if (!ValidationUtils.isNullOrEmpty(searchService.getCompanyCode())){
//                for (PermissionDataManagement e : permissionDataManagements) {
//                    List<String> companyCodes = permissionDataManagements
//
//                }
//            }

            // count child
            List<Long> lsId = new ArrayList<>();
            for (ServicePackage s : listRes) {
                lsId.add(s.getId());
            }
            List<Object[]> lsCountChild = servicePackageRepository.countChildByLsId(lsId);
            Map<Long, Long> mapChild = new HashMap<>();
            for (Object[] o : lsCountChild) {
                mapChild.put((Long) o[0], (Long) o[1]);
            }

            // Admin search normal service tab
            if (isAdmin && !ValidationUtils.isNullOrEmpty(searchService.getSpecialFlow())) {
                if (!searchService.getSpecialFlow()) {
                    // normal service
                    listRes = listRes.stream().filter(res -> res.getSpecialParentId() == null).collect(Collectors.toList());
                }
                if (!specialServiceTypes.isEmpty()) {
                    if (!specialServiceTypes.contains(-1) && specialServiceTypes.contains(1)) {
                        listRes = listRes.stream().filter(res -> res.getSpecialFlow() == null).collect(Collectors.toList());
                    }
                }
            } else if (!isAdmin) {
                listRes = listRes.stream().filter(res -> res.getSpecialParentId() == null).collect(Collectors.toList());
            }

            List<ServicePackageResponse> servicePackageResponses = listRes.stream().map(x -> {
                ServicePackageResponse a = modelMapper.map(x, ServicePackageResponse.class);

                //set Icon
                String base64StringIcon = getFileConvertBase64String(realm, x.getIcon());

                List<String> hideName = ObjectUtils.toObject(x.getHideName(), new TypeReference<>() {
                });

                List<String> visibleName = ObjectUtils.toObject(x.getVisibleName(), new TypeReference<>() {
                });

                List<Integer> hideGroup = ObjectUtils.toObject(x.getHideGroup(), new TypeReference<>() {
                });
                List<Integer> visibleGroup = ObjectUtils.toObject(x.getVisibleGroup(), new TypeReference<>() {
                });
                List<String> visibleCharts = ObjectUtils.toObject(x.getVisibleChart(), new TypeReference<>() {
                });
                List<String> hideCharts = ObjectUtils.toObject(x.getHideChart(), new TypeReference<>() {
                });

                a.setIcon(base64StringIcon);
                a.setIconName(x.getIcon());
                a.setCountChild(mapChild.get(a.getId()));
                a.setProcessName(mapProdef.get(x.getProcessId()) != null ? mapProdef.get(x.getProcessId()).getName() : null);
                a.setProcessId(mapProdef.get(x.getProcessId()) != null ? mapProdef.get(x.getProcessId()).getId() : null);
                a.setProcDefId(mapProdef.get(x.getProcessId()) != null ? mapProdef.get(x.getProcessId()).getProcDefId() : null);
                a.setHideName(hideName);
                a.setVisibleName(visibleName);
                a.setHideGroup(hideGroup);
                a.setVisibleChart(visibleCharts);
                a.setHideChart(hideCharts);
                a.setVisibleGroup(visibleGroup);

                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(a.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
                if (!ValidationUtils.isNullOrEmpty(companyCodes)) {
                    a.setApplyFor(companyCodes);
                }

                // Special clone form key
                a.setSpecialFlow(x.getSpecialFlow());
                a.setSpecialApplyFor(x.getSpecialApplyFor());
                a.setSpecialParentId(x.getSpecialParentId());
                a.setSpecialCompanyCode(x.getSpecialCompanyCode());
                List<String> lstFormKey = ObjectUtils.toObject(x.getParentCompanyCode(), new TypeReference<>() {
                });
                a.setListParentCompanyCode(lstFormKey);
                a.setLegislativeRequirement(x.getLegislativeRequirement());

                return a;
            }).collect(Collectors.toList());

            //Xử lý filter serviceType truyền vào.
            //Code xử lý ở trên đag xử lý theo hướng có cha => mới get đc list child => filter sai
            if (!ValidationUtils.isNullOrEmpty(searchService.getServiceType())) {
                servicePackageResponses = servicePackageResponses.stream().filter(i ->
                        searchService.getServiceType().contains(i.getServiceType()) || i.getServiceType() == 1
                ).collect(Collectors.toList());
            }


            List<Long> parentIds = servicePackageResponses.stream().map(ServicePackageResponse::getId).collect(Collectors.toList());
            List<ChildResponse> child = servicePackageRepository.findSpecialFlowChildDataByParentIds(parentIds);

            Set<String> companyCodes = child.stream().map(ChildResponse::getSpecialCompanyCode).collect(Collectors.toSet());
            List<NameAndCodeCompanyResponse> allCompanyCodeAndName = customerService.findAllCompanyCodeAndNameByCompanyCode(new ArrayList<>(companyCodes));
            if (allCompanyCodeAndName != null) {
                child.forEach(i -> {
                    i.setSpecialCompanyName(allCompanyCodeAndName.stream().filter(j -> j.getCompanyCode().equals(i.getSpecialCompanyCode())).findFirst().orElse(new NameAndCodeCompanyResponse()).getCompanyName());
                });
            }

            servicePackageResponses.forEach(i -> {
                i.setChild(child.stream().filter(c -> c.getParentId().equals(i.getId())).distinct().collect(Collectors.toList()));
            });

            if (searchService.getIsExactServiceType() && searchService.getServiceType() != null && !searchService.getServiceType().isEmpty()) {
                servicePackageResponses = servicePackageResponses.stream().filter(i ->
                        searchService.getServiceType().contains(i.getServiceType())
                ).collect(Collectors.toList());
            }

            return servicePackageResponses;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public List<ServicePackageResponse> getAllFilter(SearchServicePackRequest searchService, boolean isAdmin, boolean isPrivateFilterOption) {
        try {
            // Chỉnh phần filter
            boolean isWeb = CastUtils.toBooleanDefaultIfNull(searchService.getShowWeb(), true);
            List<Integer> serviceTypes = searchService.getServiceType();
            serviceTypes = Objects.isNull(serviceTypes) ? new ArrayList<>() : serviceTypes;
            List<Integer> specialServiceTypes = searchService.getSpecialServiceType();
            specialServiceTypes = Objects.isNull(specialServiceTypes) ? new ArrayList<>() : specialServiceTypes;
//            Integer pageNum = searchService.getPage() - 1;
//            Sort sort = responseUtils.getSort(searchService.getSortBy(), searchService.getSortType());
            String username = credentialHelper.getJWTPayload().getUsername();

            // Lấy list companyCode cấu hình QL vai trò người dùng
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.SERVICE_PACKAGE.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                searchService.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                searchService.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            // Ham query chính
            List<ServicePackage> page = servicePackageRepository.findAll(
                    servicePackageSpecification.getServicePackageAll(
                            searchService.getProcessName(),
                            searchService.getServiceName(),
                            serviceTypes,
                            searchService.getChartId(),
                            isWeb,
                            isAdmin,
                            searchService.getListStatus(),
                            searchService.getCreatedUser(),
                            searchService.getModifiedUser(),
                            searchService.getFromDate(),
                            searchService.getToDate(),
                            searchService.getVisibleAllUser(),
                            lstCompanyCode,
                            searchService.getSpecialFlow(),
                            specialServiceTypes,
                            searchService.getIsShared(),
                            searchService,
                            isPrivateFilterOption
                    ));

            List<ServicePackage> packages = page.stream().map(e -> modelMapper.map(e, ServicePackage.class)).collect(Collectors.toList());

            // Lọc theo hide vs visible
            List<ServicePackage> toRemove = new ArrayList<>();

            // admin chỉ filter theo applyFor
            if (!isAdmin) {
                toRemove = filterByUserAndGroup(packages, username);
            }

            // Lấy tất cả các master parent Id
            List<ServicePackage> listRes = new ArrayList<ServicePackage>(packages);
            Set<Long> masterParentIds = listRes.stream().map(ServicePackage::getMasterParentId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            Set<Long> masterParentIdIn = listRes.stream()
                    .filter(o -> Objects.isNull(o.getParentId()))
                    .map(ServicePackage::getId)
                    .collect(Collectors.toSet());
            masterParentIds = CastUtils.mergeSet(masterParentIds, masterParentIdIn);

            // Lấy tất cả các data từ các master để về sau có data các cha con đệ quy tìm ra địa chỉ
            List<ServicePackage> listAll;
            if (isAdmin || isPrivateFilterOption) {
                listAll = servicePackageRepository.findAllByMasterParentIdInAndDeletedFalseOrIdInAndDeletedFalse(
                        masterParentIds, masterParentIds
                );
            } else {
                if (isWeb) {
                    listAll = servicePackageRepository.findAllByMasterParentIdInAndDeletedFalseAndNotShowingWebsiteFalseOrIdInAndDeletedFalseAndNotShowingWebsiteFalse(
                            masterParentIds, masterParentIds
                    );
                } else {
                    listAll = servicePackageRepository.findAllByMasterParentIdInAndDeletedFalseAndNotShowingMoblieFalseOrIdInAndDeletedFalseAndNotShowingMoblieFalse(
                            masterParentIds, masterParentIds
                    );
                }
            }
            listAll.removeAll(listRes);
            listAll.addAll(listRes);


            // Viết tách ra 1 hàm khác xử lý ẩn, phân quyền lọc bớt để trả về listRes là kết quả cuối
            listRes = processListServicePackageToShow(listRes, listAll, isAdmin, isWeb);

            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.SERVICE_PACKAGE.code);

            // tab search shared
            if (!ValidationUtils.isNullOrEmpty(searchService.getIsShared()) && searchService.getIsShared()) {
                listRes = filterSharedByUserAndGroup(packages, username);
            } else
                // admin search tab normal
                if (!ValidationUtils.isNullOrEmpty(lstCompanyCode) && !lstCompanyCode.contains(CommonConstants.FILTER_SELECT_ALL)) {
                    if (!ValidationUtils.isNullOrEmpty(searchService.getIsShared()) && !searchService.getIsShared()) {
                        List<Long> lstTypeId = permissionDataManagements.stream().map(data -> {
                            if (lstCompanyCode.contains(data.getCompanyCode()) || data.getCompanyCode().equalsIgnoreCase(CommonConstants.FILTER_SELECT_ALL)) {
                                return data.getTypeId();
                            }
                            return null;
                        }).distinct().collect(Collectors.toList());
                        listRes = listRes.stream().filter(e -> lstTypeId.contains(e.getId())).collect(Collectors.toList());
                    }
                }

            listRes.removeAll(toRemove);

            // lấy thông tin phân quyền dữ liệu
            List<Long> listProcessId = listRes.stream().map(ServicePackage::getProcessId).collect(Collectors.toList());
            listProcessId.removeIf(Objects::isNull);
            List<BpmProcdef> bpmProcdefs = bpmProcdefRepository.getBpmProcdefListId(listProcessId);
            Map<Long, BpmProcdef> mapProdef = new HashMap<>();
            for (BpmProcdef bmpBpmProcdef : bpmProcdefs) {
                mapProdef.put(bmpBpmProcdef.getId(), bmpBpmProcdef);
            }

            // count child
            List<Long> lsId = new ArrayList<>();
            for (ServicePackage s : listRes) {
                lsId.add(s.getId());
            }
            List<Object[]> lsCountChild = servicePackageRepository.countChildByLsId(lsId);
            Map<Long, Long> mapChild = new HashMap<>();
            for (Object[] o : lsCountChild) {
                mapChild.put((Long) o[0], (Long) o[1]);
            }

            // Admin search normal service tab
            if (isAdmin && !ValidationUtils.isNullOrEmpty(searchService.getSpecialFlow())) {
                if (!searchService.getSpecialFlow()) {
                    // normal service
                    listRes = listRes.stream().filter(res -> res.getSpecialParentId() == null).collect(Collectors.toList());
                }
                if (!specialServiceTypes.isEmpty()) {
                    if (!specialServiceTypes.contains(-1) && specialServiceTypes.contains(1)) {
                        listRes = listRes.stream().filter(res -> res.getSpecialFlow() == null).collect(Collectors.toList());
                    }
                }
            } else if (!isAdmin) {
                listRes = listRes.stream().filter(res -> res.getSpecialParentId() == null).collect(Collectors.toList());
            }

            List<ServicePackageResponse> servicePackageResponses = listRes.stream().map(x -> {
                ServicePackageResponse a = modelMapper.map(x, ServicePackageResponse.class);

//                //set Icon
//                String base64StringIcon = getFileConvertBase64String(realm, x.getIcon());

//                List<String> hideName = ObjectUtils.toObject(x.getHideName(), new TypeReference<>() {
//                });
//
//                List<String> visibleName = ObjectUtils.toObject(x.getVisibleName(), new TypeReference<>() {
//                });
//
//                List<Integer> hideGroup = ObjectUtils.toObject(x.getHideGroup(), new TypeReference<>() {
//                });
//                List<Integer> visibleGroup = ObjectUtils.toObject(x.getVisibleGroup(), new TypeReference<>() {
//                });
//                List<String> visibleCharts = ObjectUtils.toObject(x.getVisibleChart(), new TypeReference<>() {
//                });
//                List<String> hideCharts = ObjectUtils.toObject(x.getHideChart(), new TypeReference<>() {
//                });

//                a.setIcon(base64StringIcon);
//                a.setIconName(x.getIcon());
//                a.setCountChild(mapChild.get(a.getId()));
//                a.setProcessName(mapProdef.get(x.getProcessId()) != null ? mapProdef.get(x.getProcessId()).getName() : null);
//                a.setProcessId(mapProdef.get(x.getProcessId()) != null ? mapProdef.get(x.getProcessId()).getId() : null);
                a.setProcDefId(mapProdef.get(x.getProcessId()) != null ? mapProdef.get(x.getProcessId()).getProcDefId() : null);
//                a.setHideName(hideName);
//                a.setVisibleName(visibleName);
//                a.setHideGroup(hideGroup);
//                a.setVisibleChart(visibleCharts);
//                a.setHideChart(hideCharts);
//                a.setVisibleGroup(visibleGroup);

//                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(a.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
//                if (!ValidationUtils.isNullOrEmpty(companyCodes)) {
//                    a.setApplyFor(companyCodes);
//                }

                // Special clone form key
//                a.setSpecialFlow(x.getSpecialFlow());
//                a.setSpecialApplyFor(x.getSpecialApplyFor());
//                a.setSpecialParentId(x.getSpecialParentId());
//                a.setSpecialCompanyCode(x.getSpecialCompanyCode());
//                List<String> lstFormKey = ObjectUtils.toObject(x.getParentCompanyCode(), new TypeReference<>() {
//                });
//                a.setListParentCompanyCode(lstFormKey);
                List<NameAndCodeCompanyResponse> response = customerService.responseCompanyCodeAndName(x.getCreatedUser());
                for (NameAndCodeCompanyResponse response1 : response) {
                    a.setCompanyCode(response1.getCompanyCode());
                    a.setCompanyName(response1.getCompanyName());
                }
                return a;
            }).collect(Collectors.toList());

            //Xử lý filter serviceType truyền vào.
            //Code xử lý ở trên đag xử lý theo hướng có cha => mới get đc list child => filter sai
            if (!ValidationUtils.isNullOrEmpty(searchService.getServiceType())) {
                servicePackageResponses = servicePackageResponses.stream().filter(i -> {
                    return searchService.getServiceType().contains(i.getServiceType());
                }).collect(Collectors.toList());
            }

            return servicePackageResponses;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public List<ServicePackage> filterByUserAndGroup(List<ServicePackage> servicePackages, String username) throws JsonProcessingException {

        // get username
        List<String> userName = new ArrayList<>();
        userName.add(username);

        // get chartNodeIds
        UserInfoResponse userInfoResponse = customerService.getCharNodeIds(username);
        List<Long> listChartNodeIds = userInfoResponse.getChartNodeIds();
        List<String> listChartIds = userInfoResponse.getChartIds();

        List<String> newList = new ArrayList<>(listChartNodeIds.size());
        for (Long e : listChartNodeIds) {
            newList.add(String.valueOf(e));
        }

        List<ServicePackage> listRemove = new ArrayList<>();
        // get list remove by filter
        for (ServicePackage servicePackage : servicePackages) {
            List<String> listHideName = ListCompare.jsonToList(servicePackage.getHideName());
            List<String> listHideGroup = ListCompare.jsonToList(servicePackage.getHideGroup());
            List<String> listVisibleName = ListCompare.jsonToList(servicePackage.getVisibleName());
            List<String> listVisibleGroup = ListCompare.jsonToList(servicePackage.getVisibleGroup());
            List<String> listVisibleChart = ListCompare.jsonToList(servicePackage.getVisibleChart());
            List<String> listHideChart = ListCompare.jsonToList(servicePackage.getHideChart());

            if (ListCompare.containsCommonElement(userName, listHideName)
                    || ListCompare.containsCommonElement(newList, listHideGroup)
                    || ListCompare.containsCommonElement(listChartIds, listHideChart)
                    || listHideChart.contains("-1")
            ) {
                listRemove.add(servicePackage);
            }
            if (!listVisibleName.isEmpty() && (!ListCompare.containsCommonElement(userName, listVisibleName))) {
                listRemove.add(servicePackage);
            }
            if (!listVisibleGroup.isEmpty() && (!ListCompare.containsCommonElement(newList, listVisibleGroup))) {
                listRemove.add(servicePackage);
            }
            if (listVisibleChart.isEmpty() ||
                    ((!ListCompare.containsCommonElement(listChartIds, listVisibleChart))
                            && !listVisibleChart.contains("-1") || listVisibleChart.isEmpty())) {
                listRemove.add(servicePackage);
            }
        }
        return listRemove;
    }

    public List<ServicePackage> filterSharedByUserAndGroup(List<ServicePackage> servicePackages, String username) throws JsonProcessingException {

        // get username
        List<String> userName = new ArrayList<>();
        userName.add(username);

        // get chartNodeIds
        UserInfoResponse userInfoResponse = customerService.getCharNodeIds(username);
        List<Long> listChartNodeIds = userInfoResponse.getChartNodeIds();
        List<String> listChartIds = userInfoResponse.getChartIds();

        List<String> newList = new ArrayList<>(listChartNodeIds.size());
        for (Long e : listChartNodeIds) {
            newList.add(String.valueOf(e));
        }

        List<ServicePackage> listShared = new ArrayList<>();
        // get list remove by filter
        for (ServicePackage servicePackage : servicePackages) {
            List<String> listHideName = ListCompare.jsonToList(servicePackage.getHideName());
            List<String> listHideGroup = ListCompare.jsonToList(servicePackage.getHideGroup());
            List<String> listVisibleName = ListCompare.jsonToList(servicePackage.getVisibleName());
            List<String> listVisibleGroup = ListCompare.jsonToList(servicePackage.getVisibleGroup());
            List<String> listVisibleChart = ListCompare.jsonToList(servicePackage.getVisibleChart());
            List<String> listHideChart = ListCompare.jsonToList(servicePackage.getHideChart());

            if ((ListCompare.containsCommonElement(userName, listVisibleName)
                    || ListCompare.containsCommonElement(newList, listVisibleGroup)
                    || ListCompare.containsCommonElement(listChartIds, listVisibleChart)
                    || listVisibleChart.contains("-1"))
                    && !ListCompare.containsCommonElement(userName, listHideName)
                    && !ListCompare.containsCommonElement(newList, listHideGroup)
                    && !ListCompare.containsCommonElement(listChartIds, listHideChart)
            ) {
                listShared.add(servicePackage);
            }
        }
        return listShared;
    }

    public List<ServicePackageResponse> getAllByIdAndProcessId(Long serviceId, String realm) {
        try {
            // Chỉnh phần filter

            // Ham query chính
            List<ServicePackage> listRes = servicePackageRepository.findAll(
                    servicePackageSpecification.getServicePackageAllByIdAndProcessId(
                            serviceId)
            );

//            List<ServicePackage> listRes= page.getContent().stream().map(e -> modelMapper.map(e, ServicePackage.class)).collect(Collectors.toList());

            // data procdef to response
            List<Long> listProcessId = listRes.stream().map(ServicePackage::getProcessId).collect(Collectors.toList());
            listProcessId.removeIf(Objects::isNull);

            List<BpmProcdef> bpmProcdefs = bpmProcdefRepository.getBpmProcdefListId(listProcessId);
            Map<Long, BpmProcdef> mapProdef = new HashMap<>();
            for (BpmProcdef bmpBpmProcdef : bpmProcdefs) {
                mapProdef.put(bmpBpmProcdef.getId(), bmpBpmProcdef);
            }
            return listRes.stream().map(x -> {
                ServicePackageResponse a = modelMapper.map(x, ServicePackageResponse.class);
                //set Icon
                String base64StringIcon = getFileConvertBase64String(realm, x.getIcon());

                List<String> hideName = ObjectUtils.toObject(x.getHideName(), new TypeReference<>() {
                });

                List<String> visibleName = ObjectUtils.toObject(x.getVisibleName(), new TypeReference<>() {
                });

                List<Integer> hideGroup = ObjectUtils.toObject(x.getHideGroup(), new TypeReference<>() {
                });
                List<Integer> visibleGroup = ObjectUtils.toObject(x.getHideGroup(), new TypeReference<>() {
                });

                List<String> visibleCharts = ObjectUtils.toObject(x.getVisibleChart(), new TypeReference<>() {
                });
                List<String> hideCharts = ObjectUtils.toObject(x.getHideChart(), new TypeReference<>() {
                });


                a.setIcon(base64StringIcon);
                a.setIconName(x.getIcon());
                BpmProcdef bpmProcdef = mapProdef.get(x.getProcessId()) != null ? mapProdef.get(x.getProcessId()) : null;
                if (bpmProcdef != null) {
                    a.setProcessName(bpmProcdef.getName());
                    a.setProcessId(bpmProcdef.getId());
                    a.setProcDefId(bpmProcdef.getProcDefId());
                    PriorityManagement priorityManagement = priorityManagementRepository.findById(bpmProcdef.getPriorityId()).orElse(null);
                    a.setPriority(priorityManagement != null ? priorityManagement.getName() : null);
                    a.setPriorityId(bpmProcdef.getPriorityId());
                }
                a.setHideName(hideName);
                a.setVisibleName(visibleName);
                a.setHideGroup(hideGroup);
                a.setVisibleGroup(visibleGroup);
                a.setVisibleChart(visibleCharts);
                a.setHideChart(hideCharts);
                return a;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<ServicePackageResponse> getAllByIdAndProcessIdBasicAuth(Long serviceId, String realm) {
        try {
            // Chỉnh phần filter

            // Ham query chính
            List<ServicePackage> listRes = servicePackageRepository.findAll(
                    servicePackageSpecification.getServicePackageAllByIdAndProcessId(
                            serviceId)
            );

//            List<ServicePackage> listRes= page.getContent().stream().map(e -> modelMapper.map(e, ServicePackage.class)).collect(Collectors.toList());

            // data procdef to response
            List<Long> listProcessId = listRes.stream().map(ServicePackage::getProcessId).collect(Collectors.toList());
            listProcessId.removeIf(Objects::isNull);

            List<BpmProcdef> bpmProcdefs = bpmProcdefRepository.getBpmProcdefListId(listProcessId);
            Map<Long, BpmProcdef> mapProdef = new HashMap<>();
            for (BpmProcdef bmpBpmProcdef : bpmProcdefs) {
                mapProdef.put(bmpBpmProcdef.getId(), bmpBpmProcdef);
            }
            return listRes.stream().map(x -> {
                ServicePackageResponse a = modelMapper.map(x, ServicePackageResponse.class);
                //set Icon
                String base64StringIcon = getFileConvertBase64String(realm, x.getIcon());

                List<String> hideName = ObjectUtils.toObject(x.getHideName(), new TypeReference<>() {
                });

                List<String> visibleName = ObjectUtils.toObject(x.getVisibleName(), new TypeReference<>() {
                });

                List<Integer> hideGroup = ObjectUtils.toObject(x.getHideGroup(), new TypeReference<>() {
                });
                List<Integer> visibleGroup = ObjectUtils.toObject(x.getHideGroup(), new TypeReference<>() {
                });

                List<String> visibleCharts = ObjectUtils.toObject(x.getVisibleChart(), new TypeReference<>() {
                });
                List<String> hideCharts = ObjectUtils.toObject(x.getHideChart(), new TypeReference<>() {
                });


                a.setIcon(base64StringIcon);
                a.setIconName(x.getIcon());
                BpmProcdef bpmProcdef = mapProdef.get(x.getProcessId()) != null ? mapProdef.get(x.getProcessId()) : null;
                if (bpmProcdef != null) {
                    a.setProcessName(bpmProcdef.getName());
                    a.setProcessId(bpmProcdef.getId());
                    a.setProcDefId(bpmProcdef.getProcDefId());
                    PriorityManagement priorityManagement = priorityManagementRepository.findById(bpmProcdef.getPriorityId()).orElse(null);
                    a.setPriority(priorityManagement != null ? priorityManagement.getName() : null);
                    a.setPriorityId(bpmProcdef.getPriorityId());
                }
                a.setHideName(hideName);
                a.setVisibleName(visibleName);
                a.setHideGroup(hideGroup);
                a.setVisibleGroup(visibleGroup);
                a.setVisibleChart(visibleCharts);
                a.setHideChart(hideCharts);
                return a;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<ServicePackageResponse> findByParent(Long parentId, String realm, String token,
                                                     Boolean check, boolean isAdmin) {
        try {
            boolean isWeb = check;
            Set<Long> parentIdSet = Collections.singleton(parentId);

            List<ServicePackage> listRes;
            listRes = isAdmin ? servicePackageRepository.findAllByParentIdInAndDeletedFalse(parentIdSet) :
                    (isWeb ? servicePackageRepository.findAllByParentIdInAndDeletedFalseAndNotShowingWebsiteFalse(parentIdSet) :
                            servicePackageRepository.findAllByParentIdInAndDeletedFalseAndNotShowingMoblieFalse(parentIdSet)
                    );
            Set<Long> masterIds = listRes.stream().map(ServicePackage::getMasterParentId).collect(Collectors.toSet());
            List<ServicePackage> listAll;
            if (isAdmin) {
                listAll = servicePackageRepository.findAllByMasterParentIdInAndDeletedFalseOrIdInAndDeletedFalse(
                        masterIds, masterIds
                );
            } else {
                if (isWeb) {
                    listAll = servicePackageRepository.findAllByMasterParentIdInAndDeletedFalseAndNotShowingWebsiteFalseOrIdInAndDeletedFalseAndNotShowingWebsiteFalse(
                            masterIds, masterIds
                    );
                } else {
                    listAll = servicePackageRepository.findAllByMasterParentIdInAndDeletedFalseAndNotShowingMoblieFalseOrIdInAndDeletedFalseAndNotShowingMoblieFalse(
                            masterIds, masterIds
                    );
                }
            }
            listAll.removeAll(listRes);
            listAll.addAll(listRes);

            // data procdef to response
            List<Long> listProcessId = listRes.stream().map(ServicePackage::getProcessId).collect(Collectors.toList());
            listProcessId.removeIf(Objects::isNull);
            List<BpmProcdef> bpmProcdefs = bpmProcdefRepository.getBpmProcdefListId(listProcessId);
            Map<Long, BpmProcdef> mapProdef = new HashMap<>();
            for (BpmProcdef bmpBpmProcdef : bpmProcdefs) {
                mapProdef.put(bmpBpmProcdef.getId(), bmpBpmProcdef);
            }
            listRes = listRes.stream().filter(res -> res.getSpecialParentId() == null).collect(Collectors.toList());
            return listRes.stream().map(x -> {
                ServicePackageResponse a = modelMapper.map(x, ServicePackageResponse.class);
                a.setProcessName(mapProdef.get(x.getProcessId()) != null ? mapProdef.get(x.getProcessId()).getName() : null);
                a.setProcessId(mapProdef.get(x.getProcessId()) != null ? mapProdef.get(x.getProcessId()).getId() : null);
                a.setProcDefId(mapProdef.get(x.getProcessId()) != null ? mapProdef.get(x.getProcessId()).getProcDefId() : null);
                // Special clone form key
                a.setSpecialFlow(x.getSpecialFlow());
                a.setSpecialApplyFor(x.getSpecialApplyFor());
                a.setSpecialParentId(x.getSpecialParentId());
                a.setSpecialCompanyCode(x.getSpecialCompanyCode());
                List<String> lstFormKey = ObjectUtils.toObject(x.getParentCompanyCode(), new TypeReference<>() {
                });
                a.setListParentCompanyCode(lstFormKey);

                return a;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    // Dùng 1 hàm để kiểm tra ẩn hiện,
    // trả về NOT (nếu bị ẩn theo thuộc tính notShowingWebsite, notShowingMoblie)
    // trả về ALWAYS (luôn hiện theo visibleName hoặc visibleGroup)
    // trả về HIDE (bị ẩn theo quyền)
    // trả về SHOW (trạng thái bình thường, chưa bị ảnh hưởng)
//    public String checkServicePackageVisibleType(ServicePackage servicePackage, List<GroupNameUsersResponses> groupList,
//                                                 String userId, boolean isWeb) {
//        if (isWeb) {
//            if (servicePackage.getNotShowingWebsite()) {
//                return "NOT";
//            }
//        } else {
//            if (servicePackage.getNotShowingMoblie()) {
//                return "NOT";
//            }
//        }
//
//        List<String> visibleName = CastUtils.stringToListString(servicePackage.getVisibleName());
//        if (!CollectionUtils.isEmpty(visibleName)) {
//            return visibleName.contains(userId) ? "ALWAYS" : "HIDE";
//        }
//
//        List<Long> visibleGroups = CastUtils.stringToListLong(servicePackage.getVisibleGroup());
//        if (!CollectionUtils.isEmpty(visibleGroups)) {
//            List<String> allUserInAllGroupVisible = visibleGroups.stream()
//                    .map(o -> groupList.stream().filter(u -> o.equals(u.getGroupId())).collect(Collectors.toList()))
//                    .flatMap(List::stream)
//                    .map(GroupNameUsersResponses::getUsers)
//                    .flatMap(Set::stream)
//                    .collect(Collectors.toList());
//            return allUserInAllGroupVisible.contains(userId) ? "ALWAYS" : "HIDE";
//        }
//
//        List<String> hideName = CastUtils.stringToListString(servicePackage.getHideName());
//        if (hideName.contains(userId)) {
//            return "HIDE";
//        }
//
//        List<Long> hideGroup = CastUtils.stringToListLong(servicePackage.getHideGroup());
//        List<String> allUserInAllGroupHide = hideGroup.stream()
//                .map(o -> groupList.stream().filter(u -> o.equals(u.getGroupId())).collect(Collectors.toList()))
//                .flatMap(List::stream)
//                .map(GroupNameUsersResponses::getUsers)
//                .flatMap(Set::stream)
//                .collect(Collectors.toList());
//        if (allUserInAllGroupHide.contains(userId)) {
//            return "HIDE";
//        }
//
//        return "SHOW";
//    }

    // Hàm lọc data bằng đệ quy (data tìm thấy, data All chứa đủ data cần, ...)
    public List<ServicePackage> processListServicePackageToShow(List<ServicePackage> listResultSearch,
                                                                List<ServicePackage> servicePackageList,
                                                                boolean admin, boolean isWeb) {
        // Nếu trong kết quả tìm kiếm có thư mục hoặc gói thì hiện thêm các child của nó
        listResultSearch = listResultSearch.stream()
                .map(o -> {
                    if (Objects.isNull(o.getParentId())) {
                        List<ServicePackage> lists = servicePackageList.stream()
                                .filter(u -> o.getId().equals(u.getMasterParentId()))
                                .collect(Collectors.toList());
                        lists.add(o);
                        return lists;
                    } else if (o.getServiceType().equals(1)) {
                        List<ServicePackage> ofParent = new ArrayList<>();
                        ofParent.add(o);
                        List<ServicePackage> parents = Collections.singletonList(o);
                        while (CollectionUtils.isEmpty(parents)) {
                            List<ServicePackage> children = parents.stream()
                                    .map(k -> servicePackageList.stream().filter(u -> k.getId().equals(u.getParentId()))
                                            .collect(Collectors.toList()))
                                    .flatMap(Collection::stream)
                                    .collect(Collectors.toList());
                            ofParent.addAll(children);
                            parents = children.stream().filter(u -> u.getServiceType().equals(1))
                                    .collect(Collectors.toList());
                        }
                        return ofParent;
                    } else return Collections.singletonList(o);
                })
                .flatMap(Collection::stream)
                .filter(CastUtils.distinctByKey(ServicePackage::getId))
                .collect(Collectors.toList());

        // Tìm stack địa chỉ của từng item theo parent (lấy data từ listAll)
        Map<Long, Stack<ServicePackage>> mapAddress = new HashMap<>();
        listResultSearch.forEach(servicePackage -> {
            Long idOriginal = servicePackage.getId();

            Stack<ServicePackage> stack = new Stack<>();
            ServicePackage currentServicePackage = servicePackage;
            while (true) {
                stack.push(currentServicePackage);

                Long checkParentId = currentServicePackage.getParentId();
                if (Objects.isNull(checkParentId)) {
                    mapAddress.put(idOriginal, stack);
                    break;
                } else {
                    ServicePackage finalCurrentServicePackage = currentServicePackage;
                    Optional<ServicePackage> optional = servicePackageList.stream()
                            .filter(u -> finalCurrentServicePackage.getParentId().equals(u.getId()))
                            .findFirst();
                    if (optional.isPresent()) {
                        currentServicePackage = optional.get();
                    } else {
                        log.info(finalCurrentServicePackage.toString());
                        break;
                    }
                }
            }
        });

        List<ServicePackage> listShow = new ArrayList<>();

        // Theo từng địa chỉ thì kiểm tra xem nó có được hiện hay không,
        // được hiện thì add cả stack vào list tổng rồi sau distinct để được kết quả cuối
        mapAddress.keySet().forEach(id -> {
            Stack<ServicePackage> stackAddressServicePackage = mapAddress.get(id);
//            List<ServicePackage> list = new ArrayList<>() {{
//                addAll(stackAddressServicePackage);
//            }};

            listShow.addAll(stackAddressServicePackage);

//            if (admin) {
//                listShow.addAll(list);
//            }

//            Integer indexAlways = null;
//            for (int i = 0; i < list.size(); i++) {
//                ServicePackage checkI = list.get(i);
//                if ("ALWAYS".equals(checkServicePackageVisibleType(checkI, groupList, userId, isWeb))) {
//                    indexAlways = i;
//                }
//            }

//            while (true) {
//                ServicePackage servicePackage;
//                try {
//                    servicePackage = stackAddressServicePackage.pop();
//                    String checkHideNot = checkServicePackageVisibleType(servicePackage, groupList, userId, isWeb);
//                    if ("NOT".equals(checkHideNot)) {
//                        return;
//                    }
//                    if ("HIDE".equals(checkHideNot)) {
//                        int indexHide = list.indexOf(servicePackage);
//                        if (Objects.nonNull(indexAlways) && (indexHide - indexAlways <= 1)) {
//                            listShow.addAll(list);
//                            break;
//                        }
//                        return;
//                    }
//                } catch (EmptyStackException e) {
//                    listShow.addAll(list);
//                    break;
//                }
//            }
        });

        return listShow.stream().filter(CastUtils.distinctByKey(ServicePackage::getId)).collect(Collectors.toList());
    }

    public ServicePackage updateServicePackage(ServicePackageRequest servicePackageRequest) {
        try {
            ServicePackage sp = servicePackageRepository.findById(servicePackageRequest.getId()).orElseThrow(() -> new BusinessException(SERVICE_PACKAGE_NOT_EXIST));
            String visibleGroup = new Gson().toJson(servicePackageRequest.getVisibleGroup());
            String hideGroup = new Gson().toJson(servicePackageRequest.getHideGroup());
            String hideName = new Gson().toJson(servicePackageRequest.getHideName());
            String visibleName = new Gson().toJson(servicePackageRequest.getVisibleName());
            String visibleChart = new Gson().toJson(servicePackageRequest.getVisibleChart());
            String hideChart = new Gson().toJson(servicePackageRequest.getHideChart());

            if (servicePackageRequest.getParentId() != null) {
                sp.setParentId(servicePackageRequest.getParentId() == 0 ? null : servicePackageRequest.getParentId());
                // set lại masterParentId
                ServicePackage parentSp = servicePackageRepository.findById(servicePackageRequest.getParentId()).orElseThrow(() -> new BusinessException(SERVICE_PACKAGE_NOT_EXIST));
                if (parentSp.getMasterParentId() != null) {
                    sp.setMasterParentId(parentSp.getMasterParentId());
                } else {
                    sp.setMasterParentId(parentSp.getId());
                }
            }
            if (servicePackageRequest.getServiceName() != null) {
                sp.setServiceName(servicePackageRequest.getServiceName());
            }
            if (servicePackageRequest.getColor() != null) {
                sp.setColor(servicePackageRequest.getColor());
            }
//            if (servicePackageRequest.getIcon() != null) {
//                sp.setIcon(servicePackageRequest.getIcon());
//            }
            if (servicePackageRequest.getServiceType() != null) {
                sp.setServiceType(servicePackageRequest.getServiceType());
            }
            if (servicePackageRequest.getProcessId() != null) {
                sp.setProcessId(servicePackageRequest.getProcessId());
            }

            if (servicePackageRequest.getUrl() != null) {
                if (!ValidationUtils.isNullOrEmpty(sp.getUrl()) && !sp.getUrl().equalsIgnoreCase(servicePackageRequest.getUrl())) {
                    fileManager.deleteFile(bucket, sp.getUrl());
                    log.info("Delete url successfully");
                }
                sp.setUrl(servicePackageRequest.getUrl());
            }
            if (servicePackageRequest.getIcon() != null) {
                if (!ValidationUtils.isNullOrEmpty(sp.getIcon()) && !sp.getIcon().equalsIgnoreCase(servicePackageRequest.getIcon())) {
                    fileManager.deleteFile(bucket, sp.getIcon());
                    log.info("Delete icon successfully");
                }
                sp.setIcon(servicePackageRequest.getIcon());
            }

            if (servicePackageRequest.getDescription() != null) {
                sp.setDescription(servicePackageRequest.getDescription());
            }
            if (servicePackageRequest.getNote() != null) {
                sp.setNote(servicePackageRequest.getNote());
            }
            if (servicePackageRequest.getNotShowingMoblie() != null) {
                sp.setNotShowingMoblie(servicePackageRequest.getNotShowingMoblie());
            }
            if (servicePackageRequest.getNotShowingWebsite() != null) {
                sp.setNotShowingWebsite(servicePackageRequest.getNotShowingWebsite());
            }
            if (servicePackageRequest.getHideName() != null) {
                sp.setHideName(hideName);
            }
            if (servicePackageRequest.getVisibleName() != null) {
                sp.setVisibleName(visibleName);
            }
            if (servicePackageRequest.getPositionPackage() != null) {
                sp.setPositionPackage(servicePackageRequest.getPositionPackage());
            }
            if (servicePackageRequest.getIdOrgChart() != null) {
                sp.setIdOrgChart(servicePackageRequest.getIdOrgChart());
            }
            if (servicePackageRequest.getHideGroup() != null) {
                sp.setHideGroup(hideGroup);
            }
            if (servicePackageRequest.getVisibleGroup() != null) {
                sp.setVisibleGroup(visibleGroup);
            }

            if (servicePackageRequest.getUrl() != null) {
                sp.setUrl(servicePackageRequest.getUrl());
            }
            if (servicePackageRequest.getHideAllUser() != null) {

                sp.setHideAllUser(servicePackageRequest.getHideAllUser());
            }
            if (servicePackageRequest.getVisibleAllUser() != null) {
                sp.setVisibleAllUser(servicePackageRequest.getVisibleAllUser());
            }
//            if (servicePackageRequest.getSubmissionType() != null) {
            sp.setSubmissionType(servicePackageRequest.getSubmissionType());
//            }

            sp.setVisibleChart(visibleChart);
            sp.setHideChart(hideChart);

            sp.setLegislativeRequirement(servicePackageRequest.getLegislativeRequirement());

            sp.setModifiedDate(LocalDateTime.now());
            sp.setModifiedUser(credentialHelper.getJWTPayload().getUsername());

            BpmProcdef bpmProcdef = null;
            List<String> lstOldCompanyCode = new ArrayList<>();
            if (servicePackageRequest.getServiceType() == 2) {
                if (servicePackageRequest.getProcessId() != null) {
                    bpmProcdef = bpmProcdefRepository.findBpmProcdefById(servicePackageRequest.getProcessId()).get(0);
                    if (bpmProcdef != null) {
                        if (!bpmProcdef.getStatus().equals(ProcDefConstants.Status.ACTIVE.code)) {
                            Long serviceCount = bpmProcdef.getServiceCount();
                            bpmProcdef.setServiceCount(serviceCount != null ? serviceCount + 1 : 1);
                            bpmProcdef.setStatus(ProcDefConstants.Status.ACTIVE.code);
                            bpmProcdefRepository.save(bpmProcdef);
                        }
                    }

                    // Clone luồng đặc biệt
                    if (!ValidationUtils.isNullOrEmpty(sp.getParentCompanyCode()) && !sp.getParentCompanyCode().equalsIgnoreCase("[]")) {
                        lstOldCompanyCode = ObjectUtils.toObject(sp.getParentCompanyCode(), new TypeReference<>() {
                        });
                        // disable các dịch vụ con đã được clone trước đó
                        List<ServicePackage> lstOldServiceSpecial = servicePackageRepository.getServicePackagesBySpecialParentIdAndSpecialCompanyCodeIn(sp.getId(), lstOldCompanyCode);
                        if (!ValidationUtils.isNullOrEmpty(lstOldServiceSpecial)) {
                            for (ServicePackage oldServiceSpecial : lstOldServiceSpecial) {
                                if (!oldServiceSpecial.getStatus().equalsIgnoreCase("ACTIVE")) {
                                    oldServiceSpecial.setStatus("DISABLED");
                                }
                            }
                            servicePackageRepository.saveAll(lstOldServiceSpecial);
                        }
                    }
                }
            }

            List<String> lstParentCompanyCode = servicePackageRequest.getParentCompanyCode();

            if (!ValidationUtils.isNullOrEmpty(servicePackageRequest.getSpecialFlow())) {
                sp.setSpecialFlow(servicePackageRequest.getSpecialFlow());
                if (servicePackageRequest.getSpecialFlow()) {
                    sp.setStatus("ACTIVE");
                }
            }

            if (!ValidationUtils.isNullOrEmpty(servicePackageRequest.getSpecialApplyFor())) {
                sp.setSpecialApplyFor(servicePackageRequest.getSpecialApplyFor());

                // get list companyCode by parentCodes
                if (!ValidationUtils.isNullOrEmpty(lstParentCompanyCode) && servicePackageRequest.getSpecialApplyFor()) {
                    List<String> lstCompanyCode = customerService.listChildCompanyCodeByParentCode(lstParentCompanyCode);
                    if (!ValidationUtils.isNullOrEmpty(lstCompanyCode)) {
                        lstParentCompanyCode.addAll(lstCompanyCode);
                    }
                }
            }

            if (lstParentCompanyCode != null) {
                lstParentCompanyCode = lstParentCompanyCode.stream().distinct().collect(Collectors.toList());
                String parentCompanyCode = new Gson().toJson(lstParentCompanyCode);
                sp.setParentCompanyCode(parentCompanyCode);
            }

            // Lưu phân quyền dữ liệu
            // Xóa data cũ
            List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(sp.getId(), PermissionDataConstants.Type.SERVICE_PACKAGE.code);
            if (!ValidationUtils.isNullOrEmpty(oldData)) {
                permissionDataManagementRepository.deleteAll(oldData);
            }
            if (!ValidationUtils.isNullOrEmpty(servicePackageRequest.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : servicePackageRequest.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(sp.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.SERVICE_PACKAGE.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                permissionDataManagementRepository.saveAll(permissionDataManagements);
            }

            servicePackageRepository.save(sp);

            // Clone luồng đặc biệt
            if (!ValidationUtils.isNullOrEmpty(servicePackageRequest.getSpecialFlow()) && lstParentCompanyCode != null) {
                if (servicePackageRequest.getProcessId() != null && servicePackageRequest.getSpecialFlow() && bpmProcdef != null) {

                    for (String companyCode : lstParentCompanyCode) {
                        Resource resource = new FileNameAwareByteArrayResource(bpmProcdef.getResourceName(), bpmProcdef.getBytes(), null);

                        Map<String, Object> mapResponseCreate = camundaEngineService.createDeployment(resource);
                        String deploymentId = mapResponseCreate.get("id").toString();
                        BpmnModelInstance bpmnModelInstance = CamundaUtils.convertBytesToBpmnModelInstance(bpmProcdef.getBytes());

                        // Lấy ds formkey trong file camunda
                        List<String> lstFormKey = new ArrayList<>();
                        Collection<StartEvent> startEvents = bpmnModelInstance.getModelElementsByType(StartEvent.class);
                        for (StartEvent startEvent : startEvents) {
                            lstFormKey.add(startEvent.getCamundaFormKey());
                        }
                        Collection<UserTask> userTasks = CamundaUtils.getUserTasks(bpmnModelInstance);
                        for (UserTask userTask : userTasks) {
                            lstFormKey.add(userTask.getCamundaFormKey());
                        }
                        List<TemplateManage> lstTemplate = templateManager.getByListFormKey(lstFormKey);

                        List<String> lstFormKeyClone = lstFormKey.stream().map(formKey -> formKey + "-" + sp.getId().toString() + "-" + companyCode).collect(Collectors.toList());
                        // Quy trình
                        BpmProcdef bpmProcDefResponse = bpmProcdefManager.createCopySpecial(bpmProcdef, deploymentId, companyCode, lstFormKeyClone, sp.getId());

                        // Mẫu trình ký
                        List<BpmTemplatePrint> lstTemplatePrint = bpmTemplatePrintRepository.getByProcDefId(bpmProcdef.getProcDefId());
                        if (!ValidationUtils.isNullOrEmpty(lstTemplatePrint)) {
                            bpmTemplatePrintManager.cloneTemplatePrintSpecial(lstTemplatePrint, bpmProcDefResponse, sp.getId(), companyCode);
                        }

                        // Dịch vụ
                        copyServicePackageSpecial(sp, companyCode, bpmProcDefResponse.getId());

                        // Biểu mẫu
                        if (!ValidationUtils.isNullOrEmpty(lstTemplate)) {
                            for (TemplateManage templateManage : lstTemplate) {
                                templateManager.cloneTemplateSpecial(templateManage, companyCode, sp.getId());
                            }
                        }
                    }
                }
            }

            return sp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Boolean copyServicePackageSpecial(ServicePackage servicePackage, String companyCode, Long cloneProcessId) {

        try {
            // find service package exist
            if (servicePackage == null) {
                return false;
            }
            // check service con da duoc tao -> update
            ServicePackage serviceClone = servicePackageRepository.findServicePackageBySpecialParentIdAndSpecialCompanyCode(servicePackage.getId(), companyCode);
            if (ValidationUtils.isNullOrEmpty(serviceClone)) {
                serviceClone = new ServicePackage();
            }
            serviceClone.setSpecialFlow(true);
            serviceClone.setSpecialParentId(servicePackage.getId());
            serviceClone.setSpecialCompanyCode(companyCode);
            serviceClone.setServiceName(servicePackage.getServiceName() + "-" + companyCode);

            serviceClone.setParentId(servicePackage.getParentId());
            serviceClone.setColor(servicePackage.getColor());
            serviceClone.setIcon(servicePackage.getIcon());
            serviceClone.setServiceType(servicePackage.getServiceType());
            serviceClone.setProcessId(cloneProcessId);
            serviceClone.setUrl(servicePackage.getUrl());
            serviceClone.setDescription(servicePackage.getDescription() != null ? servicePackage.getDescription() : "");
            serviceClone.setNote(servicePackage.getNote());
            serviceClone.setNotShowingMoblie(servicePackage.getNotShowingMoblie() != null ? servicePackage.getNotShowingMoblie() : false);
            serviceClone.setNotShowingWebsite(servicePackage.getNotShowingWebsite() != null ? servicePackage.getNotShowingWebsite() : false);
            serviceClone.setHideName("[]");
            serviceClone.setVisibleName("[]");
            serviceClone.setVisibleGroup("[]");
            serviceClone.setHideGroup("[]");
            serviceClone.setIdOrgChart(servicePackage.getIdOrgChart());
            serviceClone.setMasterParentId(servicePackage.getMasterParentId());
            serviceClone.setCreatedDate(LocalDateTime.now());
            serviceClone.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            serviceClone.setDeleted(false);
            serviceClone.setHideAllUser(servicePackage.getHideAllUser());
            serviceClone.setVisibleAllUser(servicePackage.getVisibleAllUser());
            serviceClone.setSubmissionType(servicePackage.getSubmissionType());
            serviceClone.setVisibleChart("[]");
            serviceClone.setHideChart("[]");
            if (servicePackage.getServiceType() == 2 && (ValidationUtils.isNullOrEmpty(serviceClone.getStatus()) || !serviceClone.getStatus().equalsIgnoreCase("ACTIVE"))) {
                serviceClone.setStatus(DEACTIVE.code);
            }
            List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
            if (!ValidationUtils.isNullOrEmpty(listCompanyCodeAndName)) {
                NameAndCodeCompanyResponse response = listCompanyCodeAndName.get(0);
                serviceClone.setCompanyCode(response.getCompanyCode());
                serviceClone.setCompanyName(response.getCompanyName());
            }
            servicePackageRepository.save(serviceClone);
            serviceClone.setPositionPackage(Math.toIntExact(serviceClone.getId()));
            servicePackageRepository.save(serviceClone);

            // clone phân quyền dữ liệu
            List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(serviceClone.getId(), PermissionDataConstants.Type.SERVICE_PACKAGE.code);
            if (!ValidationUtils.isNullOrEmpty(oldData)) {
                permissionDataManagementRepository.deleteAll(oldData);
            }
            PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
            permissionDataManagement.setTypeId(serviceClone.getId());
            permissionDataManagement.setTypeName(PermissionDataConstants.Type.SERVICE_PACKAGE.code);
            permissionDataManagement.setCompanyCode(companyCode);
            permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            permissionDataManagement.setCreatedTime(LocalDateTime.now());
            permissionDataManagementRepository.save(permissionDataManagement);

            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public List<ServicePackage> getServicePackagesBySpecialParentId(Long parentId) {
        try {
            return servicePackageRepository.getServicePackagesBySpecialParentId(parentId);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public List<ServicePackage> listByProcessId(Long processId) {
        List<ServicePackage> servicePackageList = servicePackageRepository.getServicePackagesByProcessIdAndDeleted(processId, false);
        return servicePackageList;
    }

    public ServicePackage getServicePackageById(Long id) {
        return servicePackageRepository.getServicePackageById(id);
    }

    public List<ServicePackage> servicePackageListByLikeName(String serviceName) {
        return servicePackageRepository.getServicePackageByServiceNameContainingIgnoreCase(serviceName);
    }

    public List<ServicePackage> servicePackageListByLikeName(List<String> listServiceName) {
        return servicePackageRepository.getServicePackagesByServiceNameIn(listServiceName);
    }

    public List<ServicePackageResponse> getParentService(Long id) {
        try {
            List<ServicePackage> servicePackages = servicePackageRepository.findAll(servicePackageSpecification.getParentByChild(id));
            Set<ServicePackage> listFinal = new HashSet<>();
            getParentByChild(servicePackages, listFinal, id);

            List<ServicePackageResponse> response = listFinal.stream().map(servicePackage -> {
                ServicePackageResponse resModel = modelMapper.map(servicePackage, ServicePackageResponse.class);
                if (Objects.equals(servicePackage.getId(), id)) {
                    BpmProcdef bpmProcdefs = bpmProcdefRepository.findById(servicePackage.getProcessId()).get();
                    resModel.setPriority(bpmProcdefs.getPrioritized());
                    switch (bpmProcdefs.getPrioritized().toLowerCase()) {
                        case "medium":
                            resModel.setPriorityLabel("Trung bình");
                            break;
                        case "high":
                            resModel.setPriorityLabel("Cao");
                            break;
                        default:
                            resModel.setPriorityLabel("Thấp");
                            break;
                    }
                }
                return resModel;
            }).collect(Collectors.toList());
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private void getParentByChild(List<ServicePackage> servicePackages, Set<ServicePackage> getFinal, Long id) {
        try {
            ServicePackage check = servicePackages.stream().filter(x -> Objects.equals(x.getId(), id)).findFirst().orElse(null);
            if (check != null) {
                getFinal.add(check);
                if (check.getParentId() != null) {
                    getFinal.add(check);
                    getParentByChild(servicePackages, getFinal, check.getParentId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<ServicePackageResponse> getAllService(BasePageRequest listServicePackRequest) {
        try {
            Page<Object> data = servicePackageRepository.getAllSearch(listServicePackRequest.getSearch(),
                    PageRequest.of(listServicePackRequest.getPage(),
                            listServicePackRequest.getLimit()), listServicePackRequest.getIsSpecialFlow());
            List<ServicePackageResponse> servicePackageResponses = data.stream().map(o -> {

                Object[] da = (Object[]) o;
                ServicePackageResponse servicePackageResponse = modelMapper.map(da[0], ServicePackageResponse.class);
                servicePackageResponse.setProcDefId((String) da[1]);

                return servicePackageResponse;
            }).collect(Collectors.toList());
            return servicePackageResponses;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<ServicePackageDto> getByChartAndNode(Long id, Long chartId) {
        try {
            List<ServicePackage> servicePackages = servicePackageRepository.findAll(servicePackageSpecification.getByChartAndNode(id, chartId));
            List<ServicePackage> res = new ArrayList<>();
            List<Long> longList = new ArrayList<>();
            for (ServicePackage servicePackage : servicePackages) {
                longList.addAll(new Gson().fromJson(servicePackage.getHideName(), new TypeToken<List<Long>>() {
                }.getType()));
                longList.addAll(new Gson().fromJson(servicePackage.getVisibleName(), new TypeToken<List<Long>>() {
                }.getType()));
                if (longList.contains(id)) {
                    res.add(servicePackage);
                }
            }
            return res.stream().map(servicePackage -> modelMapper.map(servicePackage, ServicePackageDto.class)).collect(Collectors.toList());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<ServicePackageDto> searchServiceMyReport(String search, Long chartId) {
        try {
            List<ServicePackage> servicePackages = servicePackageRepository.searchServiceMyReport(search, chartId);
            List<ServicePackageDto> servicePackageDtos = servicePackages.stream().map(servicePackage -> modelMapper.map(servicePackage, ServicePackageDto.class)).collect(Collectors.toList());
            return servicePackageDtos;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    public ServicePackage clone(Long id, Long chartId)  {

        ServicePackage servicePackages = servicePackageRepository.findById(id).orElse(null);
        if (ValidationUtils.isNullOrEmpty(servicePackages)) {
            throw new BusinessException(SERVICE_PACKAGE_NOT_EXIST);
        }

        ServicePackage cloneService = copyServicePackage(id, null, null, chartId);
        if (cloneService == null) {
        }
        servicePackageRepository.save(cloneService);
        List<ServicePackage> childServicePackages = servicePackageRepository.findByParentId(id);
        if (childServicePackages.size() != 0) {
            for (ServicePackage childServicePackage : childServicePackages) {

                ServicePackage servicePackage = clone(childServicePackage.getId(), childServicePackage.getIdOrgChart());
                if (childServicePackage.getDeleted() == true) {
                    servicePackage.setDeleted(true);
                }
                servicePackage.setParentId(cloneService.getId());
                servicePackageRepository.save(servicePackage);
            }

        }
        return cloneService;
    }


    public ServicePackage copyServicePackage(Long id, Long parentId, Long masterParentId, Long chartId)  {

        // find service package exist
        ServicePackage servicePackage = servicePackageRepository.findById(id).orElse(null);
        if (servicePackage == null) {
            return null;
        }
        ServicePackage servicePackages = new ServicePackage();
        String serviceName = null;
        ServicePackage findTopByServiceName = servicePackageRepository.findTopByServiceNameLikeOrderByIdDesc(servicePackage.getServiceName() + " - copy" + "%").orElse(null);

        if (findTopByServiceName != null) {
            serviceName = findTopByServiceName.getServiceName();
        }
        if (serviceName == null) {
            servicePackages.setServiceName(servicePackage.getServiceName() + " - copy");
        } else {
            String numRegexServiceName = null;
            try {
                Pattern pattern = Pattern.compile("^(.*)\\((\\d+)\\)$");
                Matcher matcher = pattern.matcher(serviceName);

                if (matcher.find()) {
                    numRegexServiceName = matcher.group(2);
                } else {
                    numRegexServiceName = "1";
                }

            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (numRegexServiceName != null) {
                int numTemplateName = Integer.parseInt(numRegexServiceName);
                servicePackages.setServiceName(servicePackage.getServiceName() + " - copy(" + (numTemplateName + 1) + ")");
            }
        }
        if (servicePackages.getServiceName().length() >= 100) {
            throw new BusinessException("000411", "Tên dịch vụ: " + servicePackages.getServiceName() + " vượt quá ký tự giới hạn", HttpStatus.BAD_REQUEST);
        }

        servicePackages.setParentId(ValidationUtils.isNullOrEmpty(parentId) ? servicePackage.getParentId() : parentId);
        servicePackages.setColor(servicePackage.getColor());
        servicePackages.setIcon(servicePackage.getIcon());
        servicePackages.setServiceType(servicePackage.getServiceType());
        servicePackages.setProcessId(servicePackage.getProcessId());
//        servicePackages.setPositionPackage(servicePackage.getPositionPackage());
        servicePackages.setUrl(servicePackage.getUrl());
        servicePackages.setDescription(servicePackage.getDescription() != null ? servicePackage.getDescription() : "");
        servicePackages.setNote(servicePackage.getNote());
        servicePackages.setNotShowingMoblie(servicePackage.getNotShowingMoblie() != null ? servicePackage.getNotShowingMoblie() : false);
        servicePackages.setNotShowingWebsite(servicePackage.getNotShowingWebsite() != null ? servicePackage.getNotShowingWebsite() : false);
        servicePackages.setHideName(servicePackage.getHideName());
        servicePackages.setVisibleName(servicePackage.getVisibleName());
        servicePackages.setVisibleGroup(servicePackage.getVisibleGroup());
        servicePackages.setHideName(servicePackage.getHideName());
        servicePackages.setHideGroup(servicePackage.getHideGroup());
        servicePackages.setIdOrgChart(chartId);
        servicePackages.setMasterParentId(ValidationUtils.isNullOrEmpty(masterParentId) ? servicePackage.getMasterParentId() : masterParentId);
        servicePackages.setCreatedDate(LocalDateTime.now());
        servicePackages.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
//        servicePackages.setCreatedUser("system");
        servicePackages.setDeleted(false);
        servicePackages.setHideAllUser(servicePackage.getHideAllUser());
        servicePackages.setVisibleAllUser(servicePackage.getVisibleAllUser());
        servicePackages.setSubmissionType(servicePackage.getSubmissionType());
        if (servicePackage.getServiceType() == 2) {
            servicePackages.setStatus(DEACTIVE.code);
        }
        servicePackages.setVisibleChart(servicePackage.getVisibleChart());
        servicePackages.setHideChart(servicePackage.getHideChart());
        ServicePackage spn = servicePackageRepository.save(servicePackages);
        spn.setPositionPackage(Math.toIntExact(spn.getId()));
        servicePackageRepository.save(spn);

        // clone phân quyền dữ liệu
        List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementByTypeId(id);
        List<PermissionDataManagement> listClone = new ArrayList<>();
        for (PermissionDataManagement data : permissionDataManagements) {
            PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
            permissionDataManagement.setTypeId(spn.getId());
            permissionDataManagement.setTypeName(data.getTypeName());
            permissionDataManagement.setCompanyCode(data.getCompanyCode());
            permissionDataManagement.setCreatedUser(spn.getCreatedUser());
            permissionDataManagement.setCreatedTime(LocalDateTime.now());
            listClone.add(permissionDataManagement);
        }
        permissionDataManagementRepository.saveAll(listClone);
        return spn;
    }

    public ServicePackage updatePosition(ServicePackageRequest servicePackageRequest) {
        try {

            ServicePackage sp = servicePackageRepository.findById(servicePackageRequest.getId()).get();

            if (servicePackageRequest.getPositionPackage() != null) {
                sp.setPositionPackage(servicePackageRequest.getPositionPackage());
            }

            servicePackageRepository.save(sp);
            return sp;
        } catch (Exception e) {
            return null;
        }
    }

    public Boolean saveIcon(String realm, MultipartFile icon, Long id) {
        try {
            ServicePackage servicePackage = servicePackageRepository.getServicePackageById(id);
            if (icon != null) {
                boolean checkExistBucket = fileManager.isBucketExisting(realm);
                if (!checkExistBucket) {
                    fileManager.createBucket(realm);
                }
                String iconName = fileManager.putFile(realm, icon.getOriginalFilename(), icon.getSize(), icon.getInputStream());

                servicePackage.setIcon(iconName);
            }
            servicePackageRepository.save(servicePackage);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public Boolean saveFile(String realm, MultipartFile file, Long id) {
        try {
            ServicePackage servicePackage = servicePackageRepository.getServicePackageById(id);
            DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
            if (file != null) {
                boolean checkExistBucket = fileManager.isBucketExisting(realm);
                if (!checkExistBucket) {
                    fileManager.createBucket(realm);
                }
                String fileName = fileManager.putFile(realm, file.getOriginalFilename(), file.getSize(), file.getInputStream());
                servicePackage.setUrl(fileName + df.format(new Date()));
            }
            servicePackageRepository.save(servicePackage);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;

        }
    }

    public String uploadFile(String realm, MultipartFile file) {
        try {
            DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
            if (file != null) {
                boolean checkExistBucket = fileManager.isBucketExisting(realm);
                if (!checkExistBucket) {
                    fileManager.createBucket(realm);
                }
                String fileName = fileManager.putFile(realm, file.getOriginalFilename(), file.getSize(), file.getInputStream());
                log.info("===========================================================");
                log.info(file.getOriginalFilename());
                log.info(String.valueOf(file.getSize()));
                log.info(fileName);
                return fileName;
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public Boolean deleteFile(String fileName)  {
        try {
            fileManager.deleteFile(bucket, fileName);
            return true;
        } catch (FileOperationException e) {
            throw new RuntimeException(e);
        }
    }

    private String getFileConvertBase64String(String realm, String fileName) {
        if (ValidationUtils.isNullOrEmpty(fileName)) {
            return "";
        }

        try {
            byte[] bytes = fileManager.getFile(realm, fileName);
            return Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public List<String> getListIdsByName(String name) {
        try {
            return servicePackageRepository.getListIdsByName(name);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }


    public List<BudgetServiceDto> getListIdsAndNameById() {
        try {
            return servicePackageRepository.getServiceNameAndIds();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

//    public List<ServicePackage> viewService(List<ServicePackage> listResultSearch, List<ServicePackage> servicePackageList, String userId, boolean admin, boolean isWeb) {
//        try {
//
//            return listResultSearch;
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return null;
//        }
//    }

    public Page<ServicePackage> getServicePackageByFilter(ServicePackageFilter filter) {
        Integer pageNum = filter.getPage() - 1;
        Sort sort = responseUtils.getSort(filter.getSortBy(), filter.getSortType());
        Pageable pageable = PageRequest.of(pageNum, filter.getSize(), sort);
        try {
            List<ServicePackage> servicePackages = servicePackageRepository.findByFilter(
                    filter.getVisibleAll(),
                    filter.getId(),
                    filter.getProcessName(),
                    filter.getChartId(),
                    filter.getServiceName(),
                    filter.getServiceTypes(),
                    filter.getNotShowingWeb(),
                    filter.getNotShowingMobile(),
                    filter.getCreatedUser(),
                    filter.getStatus(),
                    filter.getFromDate(),
                    filter.getToDate());
            Page<ServicePackage> page = PageUtils.generatePageFromList(servicePackages, pageable);
            return page;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }


    public List<ServicePackageResponse> getAllTest(ServicePackageFilter searchService, String realm, String token, boolean isAdmin) {
        try {
            Boolean isWeb = true;
            String username = credentialHelper.getJWTPayload().getUsername();
            // Lấy list companyCode cấu hình QL vai trò người dùng
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

            // Ham query chính
            Page<ServicePackage> packages = getServicePackageByFilter(searchService);
            List<ServicePackage> servicePackageList = packages.getContent();

            // Lọc theo hide vs visible
            List<ServicePackage> toRemove = filterByUserAndGroup(servicePackageList, username);

            // Lấy tất cả các master parent Id
            List<ServicePackage> listRes = new ArrayList<ServicePackage>(servicePackageList);
            Set<Long> masterParentIds = listRes.stream().map(ServicePackage::getMasterParentId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            Set<Long> masterParentIdIn = listRes.stream()
                    .filter(o -> Objects.isNull(o.getParentId()))
                    .map(ServicePackage::getId)
                    .collect(Collectors.toSet());
            masterParentIds = CastUtils.mergeSet(masterParentIds, masterParentIdIn);

            // Lấy tất cả các data từ các master để về sau có data các cha con đệ quy tìm ra địa chỉ
            //todo: check
            List<ServicePackage> listAll;
            if (isAdmin) {
                listAll = servicePackageRepository.findAllByMasterParentIdInAndDeletedFalseOrIdInAndDeletedFalse(
                        masterParentIds, masterParentIds
                );
            } else {
                if (isWeb) {
                    listAll = servicePackageRepository.findAllByMasterParentIdInAndDeletedFalseAndNotShowingWebsiteFalseOrIdInAndDeletedFalseAndNotShowingWebsiteFalse(
                            masterParentIds, masterParentIds
                    );
                } else {
                    listAll = servicePackageRepository.findAllByMasterParentIdInAndDeletedFalseAndNotShowingMoblieFalseOrIdInAndDeletedFalseAndNotShowingMoblieFalse(
                            masterParentIds, masterParentIds
                    );
                }
            }
            listAll.removeAll(listRes);
            listAll.addAll(listRes);


            // Viết tách ra 1 hàm khác xử lý ẩn, phân quyền lọc bớt để trả về listRes là kết quả cuối
            listRes = processListServicePackageToShow(listRes, listAll, isAdmin, isWeb);

            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.SERVICE_PACKAGE.code);
            if (!ValidationUtils.isNullOrEmpty(lstCompanyCode) && !lstCompanyCode.contains(CommonConstants.FILTER_SELECT_ALL)) {
                List<Long> lstTypeId = permissionDataManagements.stream().map(data -> {
                    if (lstCompanyCode.contains(data.getCompanyCode()) || data.getCompanyCode().equalsIgnoreCase(CommonConstants.FILTER_SELECT_ALL)) {
                        return data.getTypeId();
                    }
                    return null;
                }).distinct().collect(Collectors.toList());
                listRes = listRes.stream().filter(e -> lstTypeId.contains(e.getId())).collect(Collectors.toList());
            }

            listRes.removeAll(toRemove);

            //todo: check lại xem có thể lấy data procdef từ query chính ko , nếu cần thì lấy list processId ở vòng for trên
            List<Long> listProcessId = listRes.stream().map(ServicePackage::getProcessId).collect(Collectors.toList());
            listProcessId.removeIf(Objects::isNull);
            List<BpmProcdef> bpmProcdefs = bpmProcdefRepository.getBpmProcdefListId(listProcessId);
            Map<Long, BpmProcdef> mapProdef = new HashMap<>();
            for (BpmProcdef bmpBpmProcdef : bpmProcdefs) {
                mapProdef.put(bmpBpmProcdef.getId(), bmpBpmProcdef);
            }
            // count child
            List<Long> lsId = new ArrayList<>();
            for (ServicePackage s : listRes) {
                lsId.add(s.getId());
            }
            List<Object[]> lsCountChild = servicePackageRepository.countChildByLsId(lsId);
            Map<Long, Long> mapChild = new HashMap<>();
            for (Object[] o : lsCountChild) {
                mapChild.put((Long) o[0], (Long) o[1]);
            }

            return listRes.stream().map(x -> {
                ServicePackageResponse a = modelMapper.map(x, ServicePackageResponse.class);

                //set Icon
                String base64StringIcon = getFileConvertBase64String(realm, x.getIcon());

                List<String> hideName = ObjectUtils.toObject(x.getHideName(), new TypeReference<>() {
                });

                List<String> visibleName = ObjectUtils.toObject(x.getVisibleName(), new TypeReference<>() {
                });

                List<Integer> hideGroup = ObjectUtils.toObject(x.getHideGroup(), new TypeReference<>() {
                });
//                List<Integer> visibleGroup = ObjectUtils.toObject(x.getVisibleGroup(), new TypeReference<>() {
//                });

                a.setIcon(base64StringIcon);
                a.setIconName(x.getIcon());
                a.setCountChild(mapChild.get(a.getId()));
                a.setProcessName(mapProdef.get(x.getProcessId()) != null ? mapProdef.get(x.getProcessId()).getName() : null);
                a.setProcessId(mapProdef.get(x.getProcessId()) != null ? mapProdef.get(x.getProcessId()).getId() : null);
                a.setProcDefId(mapProdef.get(x.getProcessId()) != null ? mapProdef.get(x.getProcessId()).getProcDefId() : null);
                a.setHideName(hideName);
                a.setVisibleName(visibleName);
                a.setHideGroup(hideGroup);
//                a.setVisibleGroup(visibleGroup);

                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(a.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
                if (!ValidationUtils.isNullOrEmpty(companyCodes)) {
                    a.setApplyFor(companyCodes);
                }

                return a;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public ServicePackageResponse getServiceByIdWithPermission(Long id) {
        try {
            // Lấy import chart id theo user -> check visibleChart
            String username = credentialHelper.getJWTPayload().getUsername();
            UserInfoDto userInfoDto = customerService.getEmailByUser(username);

            ServicePackageResponse response = null;
            ServicePackage servicePackage = servicePackageRepository.getServicePackagetById(id);
            if (!ValidationUtils.isNullOrEmpty(servicePackage)) {
                List<String> visibleCharts = ObjectUtils.toObject(servicePackage.getVisibleChart(), new TypeReference<>() {
                });
                if (ValidationUtils.isNullOrEmpty(visibleCharts)
                        || (visibleCharts.contains("-1")
                        || (!ValidationUtils.isNullOrEmpty(userInfoDto.getImportChartId()) && visibleCharts.contains(userInfoDto.getImportChartId().toString())))) {
                    response = modelMapper.map(servicePackage, ServicePackageResponse.class);
                }
            }

            return response;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public ServicePackageResponse getServiceSpecialByParentId(Long id) {
        try {
            // Lấy list companyCode cấu hình QL vai trò người dùng
            String username = credentialHelper.getJWTPayload().getUsername();
            List<Map<String, Object>> customerRes = customerService.findCompanyCodeByUsername(username);

            ServicePackage servicePackage = servicePackageRepository.getServicePackageById(id);
            List<String> listParentCompanyCode = ObjectUtils.toObject(servicePackage.getParentCompanyCode(), new TypeReference<>() {
            });
            ServicePackageResponse response = null;
            if (!ValidationUtils.isNullOrEmpty(customerRes)) {
                String companyCode = customerRes.get(0).get("companyCode").toString();
                if (!ValidationUtils.isNullOrEmpty(listParentCompanyCode) && listParentCompanyCode.contains(companyCode)) {
                    servicePackage = servicePackageRepository.findServicePackageBySpecialParentIdAndSpecialCompanyCode(id, companyCode);
                }
                BpmProcdef bpmProcdef = bpmProcdefManager.getByServiceId(servicePackage.getId());

                response = modelMapper.map(servicePackage, ServicePackageResponse.class);
                response.setProcessName(bpmProcdef == null ? null : bpmProcdef.getName());
                response.setProcessId(bpmProcdef == null ? null : bpmProcdef.getId());
                response.setProcDefId(bpmProcdef == null ? null : bpmProcdef.getProcDefId());
            }

            return response;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    @Transactional
    public boolean switchPositionPackage(Long idPackNo1, Long idPackNo2) {
        try {
            ServicePackage servicePackageNo1 = servicePackageRepository.getServicePackageById(idPackNo1);
            ServicePackage servicePackageNo2 = servicePackageRepository.getServicePackageById(idPackNo2);

            if (servicePackageNo1 == null || servicePackageNo2 == null) {
                return false;
            }
            if (ValidationUtils.isNullOrEmpty(servicePackageNo1.getMasterParentId())
                    && ValidationUtils.isNullOrEmpty(servicePackageNo2.getMasterParentId())
                    || Objects.equals(servicePackageNo1.getMasterParentId(), servicePackageNo2.getMasterParentId())) {
                Integer positionPack1 = servicePackageNo1.getPositionPackage();
                Integer positionPack2 = servicePackageNo2.getPositionPackage();
                Long masterParentId1 = servicePackageNo1.getMasterParentId();
                Long masterParentId2 = servicePackageNo2.getMasterParentId();

                servicePackageNo1.setPositionPackage(positionPack2).setMasterParentId(masterParentId2);
                servicePackageNo2.setPositionPackage(positionPack1).setMasterParentId(masterParentId1);

                servicePackageRepository.save(servicePackageNo1);
                servicePackageRepository.save(servicePackageNo2);
                return true;
            }
            if (!ValidationUtils.isNullOrEmpty(servicePackageNo1.getMasterParentId()) && ValidationUtils.isNullOrEmpty(servicePackageNo2.getMasterParentId())) {
                ServicePackage newPosition = copyServicePackage(servicePackageNo1.getId(), servicePackageNo1.getParentId(), servicePackageNo1.getMasterParentId(), servicePackageNo1.getIdOrgChart());
                newPosition.setPositionPackage(Math.toIntExact(newPosition.getId())).setParentId(servicePackageNo2.getId()).setMasterParentId(servicePackageNo2.getId()).setServiceName(servicePackageNo1.getServiceName());
                servicePackageRepository.delete(servicePackageNo1);
                servicePackageRepository.save(newPosition);
                return true;
            }
            if (!ValidationUtils.isNullOrEmpty(servicePackageNo2.getMasterParentId()) && ValidationUtils.isNullOrEmpty(servicePackageNo1.getMasterParentId())) {
                ServicePackage newPosition = copyServicePackage(servicePackageNo2.getId(), servicePackageNo2.getParentId(), servicePackageNo2.getMasterParentId(), servicePackageNo2.getIdOrgChart());
                newPosition.setPositionPackage(Math.toIntExact(newPosition.getId())).setParentId(servicePackageNo1.getId()).setMasterParentId(servicePackageNo1.getId()).setServiceName(servicePackageNo2.getServiceName());
                servicePackageRepository.delete(servicePackageNo2);
                servicePackageRepository.save(newPosition);
                return true;
            }

        } catch (Exception e) {
            log.error("An error occurred while switching position packages.", e);
            return false;
        }
        return false;
    }

    public ServicePackage updatePositionPackage(ServicePackageRequest servicePackageRequest) {
        ServicePackage servicePackage = servicePackageRepository.findById(servicePackageRequest.getId()).orElseThrow(() -> new BusinessException(SERVICE_PACKAGE_NOT_EXIST));
        servicePackage.setPositionPackage(servicePackageRequest.getPositionPackage());
        if (servicePackageRequest.getParentId() != null) {
            servicePackage.setParentId(servicePackageRequest.getParentId());
            servicePackage.setMasterParentId(servicePackageRequest.getParentId());
        }
        servicePackageRepository.save(servicePackage);
        return servicePackage;
    }

    public boolean updateListPosition(List<ServicePackageRequest> servicePackageRequestList) {
        try {
            for (ServicePackageRequest e : servicePackageRequestList) {
                updatePositionPackage(e);
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public boolean switchPackage(List<List<Long>> listPosition) {
        try {
            for (List<Long> id : listPosition) {
                return switchPositionPackage(id.get(0), id.get(1));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
        return false;
    }


    public List<ServicePackage> findAll() {
        return servicePackageRepository.findAll();
    }

    // Phân quyền theo nhóm
    public Object getAllSystemGroup() {
        try {
            List<Map<String, Object>> lstResponse = new ArrayList<>();
            SearchServicePackRequest searchService = new SearchServicePackRequest();
            searchService.setPage(1);
            searchService.setLimit(999999);
            searchService.setSortBy("id");
            searchService.setSortType("DESC");
            Integer pageNum = searchService.getPage() - 1;

            boolean isWeb = CastUtils.toBooleanDefaultIfNull(searchService.getShowWeb(), true);
            boolean isAdmin = true;
            List<Integer> serviceTypes = searchService.getServiceType();
            serviceTypes = Objects.isNull(serviceTypes) ? new ArrayList<>() : serviceTypes;
            List<Integer> specialServiceTypes = searchService.getSpecialServiceType();
            specialServiceTypes = Objects.isNull(specialServiceTypes) ? new ArrayList<>() : specialServiceTypes;
            Sort sort = responseUtils.getSort(searchService.getSortBy(), searchService.getSortType());
            String username = credentialHelper.getJWTPayload().getUsername();

            // Lấy list companyCode cấu hình QL vai trò người dùng
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.SERVICE_PACKAGE.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                searchService.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                searchService.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            // Ham query chính
            Page<ServicePackage> page = servicePackageRepository.findAll(
                    servicePackageSpecification.getServicePackageAll(
                            searchService.getProcessName(),
                            searchService.getServiceName(),
                            serviceTypes,
                            searchService.getChartId(),
                            isWeb,
                            isAdmin,
                            searchService.getListStatus(),
                            searchService.getCreatedUser(),
                            searchService.getModifiedUser(),
                            searchService.getFromDate(),
                            searchService.getToDate(),
                            searchService.getVisibleAllUser(),
                            lstCompanyCode,
                            searchService.getSpecialFlow(),
                            specialServiceTypes,
                            searchService.getIsShared(), searchService, false
                    ), PageRequest.of(pageNum, searchService.getLimit(), sort));

            List<ServicePackage> packages = page.getContent().stream().map(e -> modelMapper.map(e, ServicePackage.class)).collect(Collectors.toList());

            // Lọc theo hide vs visible
            List<ServicePackage> toRemove = new ArrayList<>();

            // admin chỉ filter theo applyFor
            if (!isAdmin) {
                toRemove = filterByUserAndGroup(packages, username);
            }

            // Lấy tất cả các master parent Id
            List<ServicePackage> listRes = new ArrayList<ServicePackage>(packages);
            Set<Long> masterParentIds = listRes.stream().map(ServicePackage::getMasterParentId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            Set<Long> masterParentIdIn = listRes.stream()
                    .filter(o -> Objects.isNull(o.getParentId()))
                    .map(ServicePackage::getId)
                    .collect(Collectors.toSet());
            masterParentIds = CastUtils.mergeSet(masterParentIds, masterParentIdIn);

            // Lấy tất cả các data từ các master để về sau có data các cha con đệ quy tìm ra địa chỉ
            List<ServicePackage> listAll;
            if (isWeb) {
                listAll = servicePackageRepository.findAllByMasterParentIdInAndDeletedFalseAndNotShowingWebsiteFalseOrIdInAndDeletedFalseAndNotShowingWebsiteFalse(
                        masterParentIds, masterParentIds
                );
            } else {
                listAll = servicePackageRepository.findAllByMasterParentIdInAndDeletedFalseAndNotShowingMoblieFalseOrIdInAndDeletedFalseAndNotShowingMoblieFalse(
                        masterParentIds, masterParentIds
                );
            }
            listAll.removeAll(listRes);
            listAll.addAll(listRes);


            // Viết tách ra 1 hàm khác xử lý ẩn, phân quyền lọc bớt để trả về listRes là kết quả cuối
            listRes = processListServicePackageToShow(listRes, listAll, isAdmin, isWeb);

            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.SERVICE_PACKAGE.code);

            // tab search shared
            if (!ValidationUtils.isNullOrEmpty(searchService.getIsShared()) && searchService.getIsShared()) {
                listRes = filterSharedByUserAndGroup(packages, username);
            } else
                // admin search tab normal
                if (!ValidationUtils.isNullOrEmpty(lstCompanyCode) && !lstCompanyCode.contains(CommonConstants.FILTER_SELECT_ALL)) {
                    if (!ValidationUtils.isNullOrEmpty(searchService.getIsShared()) && !searchService.getIsShared()) {
                        List<Long> lstTypeId = permissionDataManagements.stream().map(data -> {
                            if (lstCompanyCode.contains(data.getCompanyCode()) || data.getCompanyCode().equalsIgnoreCase(CommonConstants.FILTER_SELECT_ALL)) {
                                return data.getTypeId();
                            }
                            return null;
                        }).distinct().collect(Collectors.toList());
                        listRes = listRes.stream().filter(e -> lstTypeId.contains(e.getId())).collect(Collectors.toList());
                    }
                }

            listRes.removeAll(toRemove);

            listRes = listRes.stream().filter(res -> res.getSpecialParentId() == null).collect(Collectors.toList());

            for (ServicePackage servicePackage : listRes) {
                Map<String, Object> response = new HashMap<>();
                response.put("id", servicePackage.getId());
                response.put("name", servicePackage.getServiceName());
                response.put("companyCode", servicePackage.getCompanyCode());
                response.put("companyName", servicePackage.getCompanyName());
                response.put("serviceType", getServiceTypeName(servicePackage.getServiceType()));

                lstResponse.add(response);
            }

            return lstResponse;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public String getServiceTypeName(Integer serviceType) {
        String name = "";
        switch (serviceType) {
            case 1:
                name = "Gói dịch vụ";
                break;
            case 2:
                name = "Tạo yêu cầu";
                break;
            case 3:
                name = "Liên kết tới url";
                break;
            case 4:
                name = "Tải tệp";
                break;
            default:
                break;
        }
        return name;
    }

    public List<Map<String, Object>> getListShareService()  {
        List<Map<String, Object>> lstResponse = new ArrayList<>();

        String username = credentialHelper.getJWTPayload().getUsername();
        // Lấy list companyCode cấu hình QL vai trò người dùng
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
        // Phân quyền theo nhóm
        List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.SERVICE_PACKAGE.tableName, username);

        List<ServicePackage> servicePackages = servicePackageRepository.findAll(
                servicePackageSpecification.getListShareService(lstCompanyCode, lstGroupPermissionId));

        List<Long> lstServiceId = servicePackages.stream().map(ServicePackage::getId).collect(Collectors.toList());
        List<Map<String, Object>> mapProcDef = bpmProcdefRepository.findByListServiceId(lstServiceId);

        if (!ValidationUtils.isNullOrEmpty(servicePackages)) {
            for (ServicePackage servicePackage : servicePackages) {
                Map<String, Object> response = new HashMap<>();
                response.put("id", servicePackage.getId());
                response.put("name", servicePackage.getServiceName());
                response.put("specialParentId", servicePackage.getSpecialParentId());
                response.put("procDefId", mapProcDef.stream()
                        .filter(e -> e.get("serviceId").equals(servicePackage.getId()))
                        .map(e -> e.get("procDefId").toString())
                        .findFirst().orElse(null)
                );
                lstResponse.add(response);
            }

            Map<String, List<Map<String, Object>>> groupChild = lstResponse.stream()
                    .collect(Collectors.groupingBy(i -> i.get("specialParentId") == null ? i.get("id").toString() : i.get("specialParentId").toString()));
            // sort
            for (List<Map<String, Object>> list : groupChild.values()) {
                list.sort(Comparator.comparing(i -> i.get("specialParentId") == null ? null : i.get("id").toString(), Comparator.nullsFirst(String::compareTo)));
            }

            lstResponse = groupChild.values().stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
        }

        return lstResponse;
    }
}