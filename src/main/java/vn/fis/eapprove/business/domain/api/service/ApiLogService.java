package vn.fis.eapprove.business.domain.api.service;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpResponse;
import vn.fis.eapprove.business.domain.api.entity.ApiLog;


import java.io.IOException;
import java.util.List;

/**
 * Author: PhucVM
 * Date: 29/11/2022
 */
public interface ApiLogService {

    ApiLog save(ApiLog apiLog);

    List<ApiLog> saveAll(List<ApiLog> apiLogs);

    void logging(HttpRequest request, ClientHttpResponse response, String requestBody, long startTime, long endTime) throws IOException;

    void setAdditionalInfo(ApiLog apiLog, HttpHeaders httpHeaders);
}
