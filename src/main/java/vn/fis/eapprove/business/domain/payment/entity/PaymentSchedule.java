package vn.fis.eapprove.business.domain.payment.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "payment_schedule")
@Data
public class PaymentSchedule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "code_ticket")
    private String codeTicket;

    @Column(name = "contract_number")
    private String contractNumber;

    @Column(name = "name_project")
    private String nameProject;

    @Column(name = "payment_installment")
    private String paymentInstallment;

    @Column(name = "ticket_id")
    private Long ticketId;

    @Column(name = "payment_amount")
    private String paymentAmount;

    @Column(name = "payment_deadline")
    private LocalDateTime paymentDeadline;


    @Column(name = "actual_amount")
    private String actualAmount;

    @Column(name = "amount_paid")
    private String amountPaid;

    @Column(name = "create_date")
    private LocalDateTime createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "reason")
    private String reason;

    @Column(name = "frequency")
    private String frequency;

    @Column(name = "paid_at")
    private LocalDateTime paidAt;


    @Column(name = "company_code")
    private String companyCode;

}
