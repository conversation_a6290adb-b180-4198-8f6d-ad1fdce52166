package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmDiscussion;


import java.util.List;

@Repository
public interface BpmDiscussionRepository extends JpaRepository<BpmDiscussion, Long> {

    @Query("SELECT d.groupId \n" +
            "FROM BpmDiscussion d \n" +
            "WHERE d.procInstId = :procInstId \n" +
            "GROUP BY d.groupId")
    Page<String> getListGroup(@Param("procInstId") String procInstId, Pageable pageable);

    @Query("SELECT d \n" +
            "FROM BpmDiscussion d \n" +
            "WHERE d.groupId in :group \n")
    List<BpmDiscussion> getDiscusByGroup(@Param("group") List<String> group);

    Page<BpmDiscussion> findByProcInstId(String procInstId, Pageable pageable);

    @Query("SELECT d FROM BpmDiscussion d WHERE d.procInstId = :procInstId AND (d.content LIKE CONCAT('%',:search,'%') or d.createdUser LIKE CONCAT('%',:search,'%')) order by d.createdDate desc ")
    Page<BpmDiscussion> findByProcInstId(@Param("procInstId") String procInstId, @Param("search") String search, Pageable pageable);

    @Query("SELECT d FROM BpmDiscussion d " +
            "WHERE d.ticketId = :ticketId " +
            "AND (d.content LIKE CONCAT('%',:search,'%') or d.createdUser " +
            "LIKE CONCAT('%',:search,'%')) " +
            "AND d.isAdditionalRequest = :isAdditionalRequest order by d.createdDate desc ")
    Page<BpmDiscussion> findByTicketId(@Param("ticketId") Long ticketId, @Param("search") String search,@Param("isAdditionalRequest") Boolean isAdditionalRequest, Pageable pageable);

    Long countByProcInstId(String procInstId);


    List<BpmDiscussion> getAllByGroupIdAndProcInstIdAndIsAdditionalRequestAndGroupIdNotNullOrderByCreatedDateAsc(Long groupId, String procInstId,Boolean isAdditionalRequest);

    List<BpmDiscussion> getAllByGroupIdAndTicketIdAndIsAdditionalRequestAndGroupIdNotNullOrderByCreatedDateAsc(Long groupId, Long ticketId,Boolean isAdditionalRequest);

    List<BpmDiscussion> getAllByGroupIdAndProcInstIdAndIsAdditionalRequestAndGroupIdIsNullOrderByCreatedDateAsc(Long groupId, String procInstId,Boolean isAdditionalRequest);

    List<BpmDiscussion> getAllByGroupIdAndTicketIdAndIsAdditionalRequestAndGroupIdIsNullOrderByCreatedDateAsc(Long groupId, Long ticketId,Boolean isAdditionalRequest);

    void deleteAllByGroupId(Long id);

    BpmDiscussion findBpmDiscussionById(Long id);

    @Query("select distinct d.ticketId from BpmDiscussion d where d.isAdditionalRequest is true")
    List<Long> findAllTicketIdAdditionalRequest();

    @Query("select distinct d.ticketId from BpmDiscussion d " +
            " where d.isAdditionalRequest is true " +
            "   and d.createdUser = :username")
    List<Long> findAllTicketIdAdditionalRequestByCreatedUser(String username);


    BpmDiscussion getBpmDiscussionById(Long id);

    @Query(value = "select d from BpmDiscussion d " +
            " where d.ticketId = :ticketId and d.createdUser = :username " +
            " and d.isAdditionalRequest = true " +
            " and d.isAdditionalRequestCompleted = false" + // lấy request của người tạo ycbs
            " and d.statusRequest = 0 ") // status_request = cần bổ sung
    List<BpmDiscussion> getAdditionalRequestRemainByUsername(@Param("username") String username, @Param("ticketId") Long ticketId);

    @Query(value = "select count(d.id) from BpmDiscussion d " +
            " where d.ticketId = :ticketId and d.isAdditionalRequest = true" +
            " and d.statusRequest = 0 ")
    Long countAdditionalRequestRemain(Long ticketId);
}
