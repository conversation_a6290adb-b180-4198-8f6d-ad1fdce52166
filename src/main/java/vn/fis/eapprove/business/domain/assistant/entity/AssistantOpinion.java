package vn.fis.eapprove.business.domain.assistant.entity;

import lombok.*;

import jakarta.persistence.*;
import java.util.Date;

/**
 * Author: AnhVTN
 * Date: 01/03/2023
 */

@Getter
@Setter
@ToString
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "assistant_opinion")
public class AssistantOpinion {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;
    @Column(name = "ticket_id")
    private Long ticketId;
    @Column(name = "assistant_email")
    private String assistantEmail;
    @Column(name = "opinion")
    private String opinion;
    @Column(name = "file_url")
    private String fileUrl;
    @Column(name = "status")
    private Long status;
    @Column(name = "file_name")
    private String fileName;
    @Column(name = "create_at")
    private Date createAt;
    @Column(name = "update_at")
    private Date updateAt;
    @Transient
    private String formatedCreateAt;
    @Transient
    private String formatedUpdateAt;
}
