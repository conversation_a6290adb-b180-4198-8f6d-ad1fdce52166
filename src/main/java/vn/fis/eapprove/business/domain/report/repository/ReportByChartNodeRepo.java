package vn.fis.eapprove.business.domain.report.repository;


import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.report.entity.ReportByChartNode;


import java.util.List;

@Repository
public interface ReportByChartNodeRepo extends JpaRepository<ReportByChartNode, Long> {

    ReportByChartNode findByTaskId(String taskId);

    @Query(nativeQuery = true,value = "select *\n" +
            "from (select distinct (rc.created_user)\n" +
            "      from report_by_chart_node rc\n" +
            "      where rc.assignee in :usernames\n" +
            "      UNION\n" +
            "      select distinct (rc.assignee)\n" +
            "      from report_by_chart_node rc\n" +
            "      where rc.created_user in :usernames) as result")
    List<String> getListUserFilter(@Param("usernames") List<String> usernames);
    @Modifying
    @Transactional
    @Query(nativeQuery = true,value = "delete from report_by_chart_node where task_id in :taskId")
    int deleteByTaskId(@Param("taskId") List<String> taskId);
}
