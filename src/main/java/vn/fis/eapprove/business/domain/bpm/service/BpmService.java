package vn.fis.eapprove.business.domain.bpm.service;


import org.springframework.http.HttpHeaders;
import vn.fis.eapprove.business.model.request.StartProcessInstanceDto;

import java.util.List;
import java.util.Map;

/**
 * Author: AnhVTN
 * Date: 15/03/2023
 */
public interface BpmService {
    Object callBpm(String url, String method, Map<String, Object> params, Map<String, Object> headers,Boolean isSaveLog) ;

    HttpHeaders getHeaders() ;

    Map<String, Object> findSystemConfigByCodeAndName(String code, String name);

    Map<String, Object> callBpmWithAuthen(Long actionApiId) ;


    List<Map<String, Object>> loadChartFilter();

    List<Map<String, Object>> loadChartNodeFilter();

//    void callSaveLogAdmin(String uuid, Object request, Object response, LocalDateTime processDate,String masterType,int status,String description);

    List<Map<String, Object>> findCompanyCodeByUsername(String username);

    List<Map<String, Object>> findChartAndChartNodeCreateTicket(StartProcessInstanceDto startProcessInstanceDto, String username,Boolean isBasicAuth);

    Object callBpmSubSystem(String url,
                                    String method,
                                    Map<String, Object> params,
                                    Map<String, Object> headers,
                                    String subSystem,
                                    Boolean isSaveLog,
                                Map<String, Object> variables,
                            Boolean isReturnResponeEntity) throws IllegalAccessException;
}
