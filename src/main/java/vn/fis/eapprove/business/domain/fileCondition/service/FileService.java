package vn.fis.eapprove.business.domain.fileCondition.service;


import org.springframework.web.multipart.MultipartFile;
import vn.fis.spro.common.model.response.FileUploadResponse;

import java.util.Date;
import java.util.List;

/**
 * Author: PhucVM
 * Date: 25/11/2022
 */
public interface FileService {

    String getFileFolderByType(String type);

    String getBucket() ;

    FileUploadResponse save(MultipartFile file, String type, boolean folderByDate);

    List<FileUploadResponse> uploadByFileName(MultipartFile[] file, String folder,List<String> fileName);

    List<FileUploadResponse> saveAll(MultipartFile[] files, String type, boolean folderByDate);

    String saveFileToMinIO(String encodedBase64, String folder, String fileName, Date date, boolean splitFolderByDate);
}
