package vn.fis.eapprove.business.domain.bpm.service;

import com.google.common.collect.ComparisonChain;
import com.google.common.collect.Ordering;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.bpm.entity.BpmHistory;
import vn.fis.eapprove.business.domain.bpm.repository.BpmHistoryRepository;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.HistoryDto;
import vn.fis.eapprove.business.model.response.BpmHistoryResponse;
import vn.fis.eapprove.business.model.response.TicketDefaultResponse;
import vn.fis.eapprove.business.specification.BpmHistorySpecification;

import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service("BpmHistoryManagerV1")
@Slf4j
@Transactional
public class BpmHistoryManager {
    @Autowired
    private BpmHistoryRepository bpmHistoryRepo;
    @Autowired
    private BpmHistorySpecification bpmHistorySpecification;
    @Autowired
    private ResponseUtils responseUtils;
    @Autowired
    private ModelMapper modelMapper;


    /**
     * Convert DTO to entity
     *
     * <AUTHOR>
     */
    public BpmHistory dtoToEntity(HistoryDto historyDto) {
        if (historyDto == null) {
            return null;
        }

        BpmHistory newHist = new BpmHistory();
        newHist.setTicketId(historyDto.getTicketId());
        newHist.setProcInstId(historyDto.getProcInstId());
        newHist.setTaskInstId(historyDto.getTaskInstId());
        newHist.setAction(historyDto.getAction());
        newHist.setNote(historyDto.getNote() != null ? historyDto.getNote() : "");
        newHist.setActionUser(historyDto.getActionUser());
        newHist.setTaskDefKey(historyDto.getTaskDefKey());
        newHist.setCreatedTime(new Date());
        newHist.setFromTask(historyDto.getFromTask());
        newHist.setToTask(historyDto.getToTask() != null ? historyDto.getToTask() : null);
        newHist.setFromTaskKey(historyDto.getFromTaskKey() != null ? historyDto.getFromTaskKey() : null);
        newHist.setToTaskKey(historyDto.getToTaskKey() != null ? historyDto.getToTaskKey() : null);
        newHist.setAffectedTask(historyDto.getAffectedTask() != null ? historyDto.getAffectedTask() : "");
        newHist.setTaskType(historyDto.getTaskType() != null ? historyDto.getTaskType() : null);
        newHist.setOldTaskId(historyDto.getOldTaskId() != null ? historyDto.getOldTaskId() : null);
        newHist.setOldProcInstId(historyDto.getOldProcInstId() != null ? historyDto.getOldProcInstId() : null);
        newHist.setAttachFiles(historyDto.getAttachFiles() != null ? historyDto.getAttachFiles() : null);
        newHist.setAttachFilesName(historyDto.getAttachFilesName() != null ? historyDto.getAttachFilesName() : null);
        newHist.setAttachFilesSize(historyDto.getAttachFilesSize() != null ? historyDto.getAttachFilesSize() : null);
        newHist.setTaskAssignee(historyDto.getTaskAssignee() != null ? historyDto.getTaskAssignee() : null);
        newHist.setOldDefaultField(historyDto.getOldDefaultField() != null ? historyDto.getOldDefaultField() : null);
        newHist.setActionUserInfo(historyDto.getActionUserInfo() != null ? historyDto.getActionUserInfo() : null);
        newHist.setReceivedTime(historyDto.getReceivedTime() != null ? historyDto.getReceivedTime() : null);
        return newHist;
    }

    public Boolean saveHistory(HistoryDto historyDto) {
        try {
            BpmHistory newHist = dtoToEntity(historyDto);
            save(newHist);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public Boolean saveAllHistory(List<HistoryDto> dtos) {
        try {
            if (!ValidationUtils.isNullOrEmpty(dtos)) {
                List<BpmHistory> listSave = dtos.stream().map(this::dtoToEntity).collect(Collectors.toList());
                saveAll(listSave);
            }

            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public PageDto getHistoryByProcess(HistoryDto historyDto) {
        try {
            Map<String, Object> mapHistory = bpmHistorySpecification.queryHistory(historyDto);
            List<BpmHistoryResponse> listRes = (List<BpmHistoryResponse>) mapHistory.get("data");
            for (BpmHistoryResponse bpmHistoryResponse : listRes) {
                if (!ValidationUtils.isNullOrEmpty(bpmHistoryResponse.getToTask())) {
                    if (bpmHistoryResponse.getToTask().equalsIgnoreCase("startTicket")) {
                        bpmHistoryResponse.setToTaskName("startTicket");
                    }
                }
            }
            Long totalItems = (Long) mapHistory.get("count");
            Integer totalPage = 0;
            if (totalItems > historyDto.getLimit() && totalItems > 0 && totalItems % historyDto.getLimit() == 0) {
                totalPage = Math.toIntExact(totalItems / historyDto.getLimit());
            } else if (totalItems > historyDto.getLimit() && totalItems > 0 && totalItems % historyDto.getLimit() > 0) {
                totalPage = Math.toIntExact((totalItems / historyDto.getLimit()) + 1);
            } else {
                totalPage = 1;
            }
            if (historyDto.getSortBy().equalsIgnoreCase("fromTaskName")) {
                sortByFromTask(listRes, historyDto.getSortType());
            } else if (historyDto.getSortBy().equalsIgnoreCase("toTaskName")) {
                sortByToTask(listRes, historyDto.getSortType());
            }

            return PageDto.builder()
                    .content(listRes)
                    .number(historyDto.getPage())
                    .numberOfElements(Math.toIntExact(totalItems))
                    .page(historyDto.getPage())
                    .size(historyDto.getLimit())
                    .totalPages(totalPage)
                    .totalElements(totalItems)
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    ;

    public List<BpmHistoryResponse> sortByFromTask(List<BpmHistoryResponse> superList, String sortType) {
        Collections.sort(superList, (o1, o2) -> {
            if (o2.getFromTaskName() == null || o1.getFromTaskName() == null) {
                return compareSortFromTask(o1, o2);
            }
            if (sortType.equals("DESC")) {
                if (o1.getFromTaskName() == null && o2.getFromTaskName() != null) {
                    return 1;
                }
                return (o2.getFromTaskName()).compareTo(o1.getFromTaskName());
            } else {

                return (o1.getFromTaskName()).compareTo(o2.getFromTaskName());
            }
        });

        return superList;
    }

    public int compareSortFromTask(BpmHistoryResponse o1, BpmHistoryResponse o2) {
        return ComparisonChain.start()
                .compare(o1.getFromTaskName(), o2.getFromTaskName(), Ordering.natural().nullsLast())
                .result();
    }

    public List<BpmHistoryResponse> sortByToTask(List<BpmHistoryResponse> superList, String sortType) {
        Collections.sort(superList, (o1, o2) -> {
            if (o2.getToTaskName() == null || o1.getToTaskName() == null) {
                return compareSortToTask(o1, o2);
            }
            if (sortType.equals("DESC")) {
                if (o1.getToTaskName() == null && o2.getToTaskName() != null) {
                    return 1;
                }
                return (o2.getToTaskName()).compareTo(o1.getToTaskName());
            } else {

                return (o1.getToTaskName()).compareTo(o2.getToTaskName());
            }
        });

        return superList;
    }

    public int compareSortToTask(BpmHistoryResponse o1, BpmHistoryResponse o2) {
        return ComparisonChain.start()
                .compare(o1.getToTaskName(), o2.getToTaskName(), Ordering.natural().nullsLast())
                .result();
    }

    public BpmHistory checkHistory(String taskId) {
        return bpmHistoryRepo.getBpmHistoryRuByTaskId(taskId);
    }


    public PageDto getRuHistoRy(HistoryDto historyDto) {
        try {
            Integer pageNum = historyDto.getPage() - 1;
            Sort sort = responseUtils.getSort(historyDto.getSortBy(), historyDto.getSortType());
            Page<BpmHistory> pageHistory = bpmHistoryRepo.findAll(bpmHistorySpecification.filterRU(historyDto),
                    PageRequest.of(pageNum, historyDto.getLimit(), sort));

            return PageDto.builder()
                    .content(pageHistory.getContent())
                    .number(historyDto.getPage())
                    .numberOfElements(pageHistory.getNumberOfElements())
                    .page(historyDto.getPage())
                    .size(pageHistory.getSize())
                    .totalPages(pageHistory.getTotalPages())
                    .totalElements(pageHistory.getTotalElements())
                    .build();
        } catch (Exception e) {
            return null;
        }
    }


    @Transactional(noRollbackFor = RuntimeException.class)
    public List<BpmHistory> getAllByTaskDefKey(Long ticketId, String taskDefKey) {
        try {
            return bpmHistoryRepo.getAllByTaskDefKey(ticketId, taskDefKey);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return new ArrayList<>();
    }

    public BpmHistory updateBpmHistory(BpmHistory bpmHistory) {
        return bpmHistoryRepo.save(bpmHistory);
    }

    public List<BpmHistory> getHistoryByAction(String action) {
        return bpmHistoryRepo.getBpmHistoryByAction(action);
    }

    /**
     * Save entity
     *
     * <AUTHOR>
     */
    @Transactional(noRollbackFor = RuntimeException.class)
    public BpmHistory save(BpmHistory bpmHistory) {
        if (bpmHistory != null) {
            try {
                return bpmHistoryRepo.save(bpmHistory);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }

        return bpmHistory;
    }

    /**
     * Save all entities
     *
     * <AUTHOR>
     */
    @Transactional(noRollbackFor = RuntimeException.class)
    public List<BpmHistory> saveAll(List<BpmHistory> bpmHistories) {
        if (!ValidationUtils.isNullOrEmpty(bpmHistories)) {
            try {
                return bpmHistoryRepo.saveAll(bpmHistories);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }

        return new ArrayList<>();
    }

    public Map<String, Object> getFilterHistory(Long ticketId) {
        try {
            List<String> listAction = bpmHistoryRepo.getAllAction(ticketId);
            List<String> listActionUser = bpmHistoryRepo.getAllActionUser(ticketId);

            Map<String, Object> mapFinal = new HashMap<>();
            mapFinal.put("listAction", listAction);
            mapFinal.put("listActionUser", listActionUser);
            return mapFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public BpmHistory getHistoryByActionAndProcInstId(String action, String procInstId) {
        try {
            List<BpmHistory> lstBpmHistory = bpmHistoryRepo.findBpmHistoriesByActionAndProcInstIdOrderByIdDesc(action, procInstId);
            if (!ValidationUtils.isNullOrEmpty(lstBpmHistory)) {
                return lstBpmHistory.get(0);
            }
            return null;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public BpmHistory getHistoryByTaskInstIdAndAction(String action, String taskInstId) {
        return bpmHistoryRepo.getBpmHistoryByTaskInstIdAndAction(action, taskInstId);
    }

    public BpmHistory getHistoryByProcInstId(String procInstId) {
        try {
            List<BpmHistory> lstBpmHistory = bpmHistoryRepo.getBpmHistoriesByProcInstIdAndActionIn(procInstId);
            if (!ValidationUtils.isNullOrEmpty(lstBpmHistory)) {
                return lstBpmHistory.get(0);
            }
            return null;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public String getOldTaskIdAffected(Long ticketId, String procInstId, String taskDefKey, String actionUser) {
        try {
            String oldProcInstId = bpmHistoryRepo.getOldProcInstId(procInstId, ticketId);
            if (!ValidationUtils.isNullOrEmpty(oldProcInstId)) {
                return bpmHistoryRepo.getOldTaskIdAffected(ticketId, taskDefKey, oldProcInstId, actionUser);
            }
            return null;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public TicketDefaultResponse getOldTicketDefaultField(Long ticketId, String procInstId) {
        try {
            String strOldTicketDefault = bpmHistoryRepo.getOldTicketDefaultField(ticketId, procInstId);
            Object objOldTicketDefault = ObjectUtils.readValue(strOldTicketDefault);
            return modelMapper.map(objOldTicketDefault, TicketDefaultResponse.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }
}
