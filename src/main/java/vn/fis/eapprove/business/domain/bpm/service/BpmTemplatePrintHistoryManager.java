package vn.fis.eapprove.business.domain.bpm.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import vn.fis.eapprove.business.config.GsonAdapterConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrint;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrintHistory;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTpTask;
import vn.fis.eapprove.business.domain.bpm.repository.*;
import vn.fis.eapprove.business.domain.fileCondition.entity.FileCondition;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.repository.ShareUserRepository;
import vn.fis.eapprove.business.dto.DataTemplatePrint;
import vn.fis.eapprove.business.dto.FileConditionDto;
import vn.fis.eapprove.business.dto.HistoryChangeDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.TemplatePrintHistoryRequest;
import vn.fis.eapprove.business.model.response.BpmTemplatePrintHistoryResponse;
import vn.fis.eapprove.business.specification.BpmTemplatePrintHistorySpecification;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ShareUserTypeEnum;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.manager.FileManager;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("BpmTemplatePrintHistoryManagerV1")
@Transactional
public class BpmTemplatePrintHistoryManager {

    @Autowired
    private BpmTemplatePrintHistorySpecification bpmProcDefHistorySpecification;
    @Autowired
    private Common common;
    @Autowired
    private BpmTemplatePrintHistoryRepository bpmTemplatePrintHistoryRepository;
    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private FileManager fileManager;
    @Value("${app.s3.bucket}")
    private String bucket;
    @Autowired
    private BpmTemplatePrintRepository bpmTemplatePrintRepository;
    @Autowired
    private ShareUserRepository shareUserRepository;
    @Autowired
    private PermissionDataManagementRepository permissionDataManagementRepository;
    @Autowired
    private BpmFileConditionRepository bpmFileConditionRepository;
    @Autowired
    private BpmTpTaskRepository bpmTpTaskRepository;
    @Autowired
    private BpmProcdefRepository bpmProcdefRepository;

    public PageDto getAll(TemplatePrintHistoryRequest request) {

        Map<String, Object> mapData = bpmProcDefHistorySpecification.getAllHistory(request);
        List<BpmTemplatePrintHistoryResponse> result = (List<BpmTemplatePrintHistoryResponse>) mapData.get("data");
        Long totalItems = (Long) mapData.get("count");
        Integer totalPage = common.getPageCount(totalItems, request.getLimit());

        return PageDto.builder()
                .content(result)
                .number(request.getPage())
                .numberOfElements(request.getLimit())
                .page(request.getPage())
                .size(request.getLimit())
                .totalPages(totalPage)
                .totalElements(totalItems)
                .build();
    }

    public List<BpmTemplatePrintHistoryResponse> getAllFilter(TemplatePrintHistoryRequest request) {
        return bpmProcDefHistorySpecification.getAllFilter(request);
    }

    public DataTemplatePrint getDetailHistory(Long id) {
        DataTemplatePrint data = new DataTemplatePrint();
        BpmTemplatePrintHistory history = bpmTemplatePrintHistoryRepository.getBpmTemplatePrintHistoryById(id);
        if (history == null) {
            throw new RuntimeException("Không tìm thấy bản ghi lịch sử.");
        }
        Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();
        data.setId(history.getBpmTemplatePrintId());
        data.setName(history.getName());
        data.setProcessName(history.getProcessName());
        data.setProcDefId(history.getProcDefId());
        data.setDescr(history.getDescr());

        List<SharedUser> sharedUsers = g.fromJson(history.getShareWith(), new TypeReference<List<SharedUser>>(){}.getType());
        List<String> listEmail = new ArrayList<>();
        if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
            listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(data.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
        }
        data.setShareWith(listEmail);

        List<PermissionDataManagement> permissionDataManagements = g.fromJson(history.getApplyFor(), new TypeReference<List<PermissionDataManagement>>(){}.getType());
        if (!ValidationUtils.isNullOrEmpty(permissionDataManagements)) {
            List<String> companyCodes = permissionDataManagements.stream().map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
            data.setApplyFor(companyCodes);
        }

        List<HistoryChangeDto> historyChange = g.fromJson(history.getHistoryChange(), new TypeReference<List<HistoryChangeDto>>(){}.getType());
        data.setHistoryChangeDtoList(historyChange);

        List<BpmTpTask> bpmTpTasks = g.fromJson(history.getTpTask(), new TypeReference<List<BpmTpTask>>(){}.getType());
        data.setBpmTpTaskList(bpmTpTasks);

        List<FileCondition> fileConditions = g.fromJson(history.getConditionFile(), new TypeReference<List<FileCondition>>(){}.getType());
        List<FileConditionDto> fileConditionDtos = new ArrayList<>();
        List<Long> htmlConfigs = new ArrayList<>();
        if (!ValidationUtils.isNullOrEmpty(fileConditions)) {
            try {
                fileConditionDtos = fileConditions.stream().map(fileCondition -> modelMapper.map(fileCondition, FileConditionDto.class)).collect(Collectors.toList());
                for (FileConditionDto condition : fileConditionDtos) {
                    if (!ValidationUtils.isNullOrEmpty(condition.getUploadWords())) {
                        InputStream inputStream = fileManager.getFileInputStream(bucket, condition.getUploadWords());
                        byte[] sourceBytes = IOUtils.toByteArray(inputStream);
                        String encodedString = Base64.getEncoder().encodeToString(sourceBytes);
                        condition.setBase64(encodedString);
                    }
                    if (!ValidationUtils.isNullOrEmpty(condition.getHtmlId())) {
                        htmlConfigs.add(condition.getHtmlId());
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        data.setFileConditionList(fileConditionDtos);
        data.setConfigType(history.getConfigType());
        data.setHtmlConfig(htmlConfigs);

        return data;
    }

    public void restoreHistory(Long id, String username) {
        BpmTemplatePrintHistory history = bpmTemplatePrintHistoryRepository.getBpmTemplatePrintHistoryById(id);
        if (history == null) {
            throw new RuntimeException("Bản ghi lịch sử không tồn tại.");
        }
        BpmTemplatePrint templatePrint = bpmTemplatePrintRepository.getBpmTemplatePrintById(history.getBpmTemplatePrintId());
        if (templatePrint == null) {
            throw new RuntimeException("Mẫu trình ký không tồn tại.");
        }
        List<BpmTemplatePrint> lstExist = bpmTemplatePrintRepository.checkExistByName(history.getBpmTemplatePrintId(), history.getName());
        if (!ValidationUtils.isNullOrEmpty(lstExist)) {
            throw new RuntimeException("Tên bản ghi khôi phục trùng với bản ghi đã tồn tại.");
        }
        Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();

        templatePrint.setName(history.getName());
        templatePrint.setDescr(history.getDescr());
        templatePrint.setHistoryChange(history.getHistoryChange());
        templatePrint.setUpdatedDate(new Date());
        templatePrint.setUpdatedUser(username);

        // gán lại quy trình theo processId - version active
        BpmProcdef bpmProcdef = bpmProcdefRepository.getBpmProcDefById(history.getProcessId());
        if (bpmProcdef == null) {
            throw new RuntimeException("Quy trình gán với bản ghi lịch sử không tồn tại.");
        }
        templatePrint.setProcDefId(bpmProcdef.getProcDefId());
        templatePrint.setProcessName(bpmProcdef.getName());
        templatePrint.setProcessId(history.getProcessId());

        // share user
        shareUserRepository.deleteAllByReferenceIdAndReferenceType(templatePrint.getId(), ShareUserTypeEnum.TEMPLATEPRINT.type);
        List<SharedUser> sharedUsers = g.fromJson(history.getShareWith(), new TypeReference<List<SharedUser>>(){}.getType());
        if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
            List<SharedUser> sharedUsersNew = new ArrayList<>();
            for (SharedUser sharedUser : sharedUsers) {
                SharedUser sharedNew = new SharedUser();
                sharedNew.setReferenceId(templatePrint.getId());
                sharedNew.setReferenceType(ShareUserTypeEnum.TEMPLATEPRINT.type);
                sharedNew.setEmail(sharedUser.getEmail());
                sharedUsersNew.add(sharedNew);
            }
            shareUserRepository.saveAll(sharedUsersNew);
        }
        // permission
        List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(templatePrint.getId(), PermissionDataConstants.Type.BPM_TEMPLATE_PRINT.code);
        if (!ValidationUtils.isNullOrEmpty(oldData)) {
            permissionDataManagementRepository.deleteAll(oldData);
        }
        List<PermissionDataManagement> perHistories = g.fromJson(history.getApplyFor(), new TypeReference<List<PermissionDataManagement>>(){}.getType());
        if (!ValidationUtils.isNullOrEmpty(perHistories)) {
            List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
            for (PermissionDataManagement perHistory : perHistories) {
                PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                permissionDataManagement.setTypeId(templatePrint.getId());
                permissionDataManagement.setTypeName(PermissionDataConstants.Type.BPM_TEMPLATE_PRINT.code);
                permissionDataManagement.setCompanyCode(perHistory.getCompanyCode());
                permissionDataManagement.setCreatedUser(username);
                permissionDataManagement.setCreatedTime(LocalDateTime.now());
                permissionDataManagements.add(permissionDataManagement);
            }
            permissionDataManagementRepository.saveAll(permissionDataManagements);
        }

        bpmFileConditionRepository.deleteAllByBpmTemplatePrintId(templatePrint.getId());
        List<FileCondition> fileConditions = g.fromJson(history.getConditionFile(), new TypeReference<List<FileCondition>>(){}.getType());
        for (FileCondition fileCondition : fileConditions) {
            fileCondition.setId(null);
        }
        bpmFileConditionRepository.saveAll(fileConditions);

        bpmTpTaskRepository.deleteAllByBpmTemplatePrintId(templatePrint.getId());
        List<BpmTpTask> lstBpmTpTask = g.fromJson(history.getTpTask(), new TypeReference<List<BpmTpTask>>(){}.getType());
        for (BpmTpTask task : lstBpmTpTask) {
            task.setId(null);
            task.setProcDefId(bpmProcdef.getProcDefId());
        }
        bpmTpTaskRepository.saveAll(lstBpmTpTask);

        // save old history
        List<BpmTemplatePrintHistory> lstOldHistory = bpmTemplatePrintHistoryRepository.findByBpmTemplatePrintId(templatePrint.getId());
        for (BpmTemplatePrintHistory oldHistory : lstOldHistory) {
            oldHistory.setStatusHistory(false);
        }
        bpmTemplatePrintHistoryRepository.saveAll(lstOldHistory);

        // create new version history
        int version = Math.toIntExact(bpmTemplatePrintHistoryRepository.countByBpmTemplatePrintId(templatePrint.getId()));
        modelMapper.getConfiguration().setDeepCopyEnabled(true);
        BpmTemplatePrintHistory newHistory = modelMapper.map(history, BpmTemplatePrintHistory.class);
        newHistory.setId(null);
        newHistory.setStatusHistory(true);
        newHistory.setVersion("V" + version);
        newHistory.setCreatedDate(LocalDateTime.now());
        newHistory.setCreatedUser(username);
        newHistory.setContentEdit("Khôi phục từ phiên bản " + history.getVersion());
        newHistory.setProcDefId(bpmProcdef.getProcDefId());
        newHistory.setProcessName(bpmProcdef.getName());
        bpmTemplatePrintHistoryRepository.save(newHistory);
    }
}
