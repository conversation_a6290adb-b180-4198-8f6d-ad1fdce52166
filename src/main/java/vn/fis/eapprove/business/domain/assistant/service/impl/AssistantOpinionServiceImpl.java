package vn.fis.eapprove.business.domain.assistant.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.assistant.entity.AssistantOpinion;
import vn.fis.eapprove.business.domain.assistant.repository.AssistantOpinionRepository;
import vn.fis.eapprove.business.domain.assistant.service.AssistantOpinionService;

import java.util.List;

/**
 * Author: AnhVTN
 * Date: 01/03/2023
 */

@Service("AssistantOpinionServiceImplV1")
@Slf4j
@Transactional
public class AssistantOpinionServiceImpl implements AssistantOpinionService {
    private final AssistantOpinionRepository repository;

    @Autowired
    public AssistantOpinionServiceImpl(AssistantOpinionRepository repository) {
        this.repository = repository;
    }

    @Override
    public List<AssistantOpinion> getOpinionsByAssistantEmail(String assistantEmail) {
        return repository.getAssistantOpinionByAssistantEmail(assistantEmail);
    }
}
