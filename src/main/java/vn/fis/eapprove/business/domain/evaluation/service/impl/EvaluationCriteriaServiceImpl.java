package vn.fis.eapprove.business.domain.evaluation.service.impl;

import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;

import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.authority.service.AuthService;
import vn.fis.eapprove.business.domain.bpm.service.BpmService;
import vn.fis.eapprove.business.domain.evaluation.entity.EvaluationCriteria;
import vn.fis.eapprove.business.domain.evaluation.entity.EvaluationDepartment;
import vn.fis.eapprove.business.domain.evaluation.repository.EvaluationCriteriaRepository;
import vn.fis.eapprove.business.domain.evaluation.repository.EvaluationDepartmentRepository;
import vn.fis.eapprove.business.domain.evaluation.service.EvaluationCriteriaService;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.system.repository.SystemGroupRepository;
import vn.fis.eapprove.business.dto.ChartNodeDto;
import vn.fis.eapprove.business.dto.EvaluationCriteriaDto;
import vn.fis.eapprove.business.dto.EvaluationCriteriaSearchDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.ChartNodeDtoRequest;
import vn.fis.eapprove.business.model.request.EvaluationCriteriaRequest;
import vn.fis.eapprove.business.model.response.EvaluationCriteriaResponse;
import vn.fis.eapprove.business.model.response.NameAndCodeCompanyResponse;
import vn.fis.eapprove.business.specification.EvaluationCriteriaSpecification;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.EntityGraph;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service("EvaluationCriteriaServiceImplV1")
@Transactional
public class EvaluationCriteriaServiceImpl implements EvaluationCriteriaService {

    private final EvaluationCriteriaRepository evaluationCriteriaRepository;
    private final EvaluationDepartmentRepository evaluationDepartmentRepository;
    private final CredentialHelper credentialHelper;
    private final EvaluationCriteriaSpecification evaluationCriteriaSpecification;
    private final CustomerService customerService;
    private final ResponseUtils responseUtils;
    private final PermissionDataManagementRepository permissionDataManagementRepository;
    private final AuthService authService;
    private final ModelMapper modelMapper;
    private final BpmService bpmService;

    private final EntityManagerFactory entityManagerFactory;
    private final Common common;
    private final SystemGroupRepository systemGroupRepository;

    @Autowired
    public EvaluationCriteriaServiceImpl(EvaluationCriteriaRepository evaluationCriteriaRepository,
                                         EvaluationDepartmentRepository evaluationDepartmentRepository,
                                         CredentialHelper credentialHelper,
                                         EvaluationCriteriaSpecification evaluationCriteriaSpecification,
                                         CustomerService customerService, ResponseUtils responseUtils,
                                         EntityManagerFactory entityManagerFactory, Common common,
                                         PermissionDataManagementRepository permissionDataManagementRepository,
                                         AuthService authService, ModelMapper modelMapper, BpmService bpmService, SystemGroupRepository systemGroupRepository) {
        this.evaluationCriteriaRepository = evaluationCriteriaRepository;
        this.evaluationDepartmentRepository = evaluationDepartmentRepository;
        this.credentialHelper = credentialHelper;
        this.evaluationCriteriaSpecification = evaluationCriteriaSpecification;
        this.customerService = customerService;
        this.responseUtils = responseUtils;
        this.entityManagerFactory = entityManagerFactory;
        this.common = common;
        this.permissionDataManagementRepository = permissionDataManagementRepository;
        this.authService = authService;
        this.modelMapper = modelMapper;
        this.bpmService = bpmService;
        this.systemGroupRepository = systemGroupRepository;
    }

    @Override
    public void saveAll(EvaluationCriteriaRequest request) throws Exception {
        if (request == null) {
            return;
        }
        String account = credentialHelper.getJWTPayload().getUsername();
        //Kiểm tra xem có trùng không
        List<EvaluationCriteria> checkCriterias = evaluationCriteriaRepository.getEvaluationCriteriaByNameAndReviewItemAndStatus(request.getName(),  "1");
        if (checkCriterias == null || checkCriterias.isEmpty() || (checkCriterias.size() == 1 && request.getId() != null && checkCriterias.get(0).getId().equals(request.getId()))) {
            EvaluationCriteria saveEvaluationCriteria = null;

            if (ValidationUtils.isNullOrEmpty(request.getId())) {//Tạo mới
                List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
                EvaluationCriteria criteria = new EvaluationCriteria();
                for(NameAndCodeCompanyResponse response : listCompanyCodeAndName){
                    criteria.setCompanyCode(response.getCompanyCode());
                    criteria.setCompanyName(response.getCompanyName());
                }
                saveEvaluationCriteria = EvaluationCriteria.builder()
                        .name(request.getName())
//                        .reviewItem(request.getReviewItem())
                        .description(request.getDescription())
                        .status(request.getStatus())
                        .companyCode(criteria.getCompanyCode())
                        .companyName(criteria.getCompanyName())
                        .build();
                saveEvaluationCriteria.setCreatedUser(account);
                saveEvaluationCriteria.setCreatedTime(LocalDateTime.now());
            } else {//update
                saveEvaluationCriteria = evaluationCriteriaRepository.findEvaluationCriteriaById(request.getId());
                if (saveEvaluationCriteria != null) {
//                    saveEvaluationCriteria.setReviewItem(request.getReviewItem());
                    saveEvaluationCriteria.setName(request.getName());
                    saveEvaluationCriteria.setDescription(request.getDescription());
                    saveEvaluationCriteria.setStatus(request.getStatus());
                    saveEvaluationCriteria.setUpdatedUser(account);
                    saveEvaluationCriteria.setUpdatedTime(LocalDateTime.now());

                    // Xóa data phân quyền dữ liệu cũ
                    List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(saveEvaluationCriteria.getId(), PermissionDataConstants.Type.EVALUATION_CRITERIA.code);
                    if (!ValidationUtils.isNullOrEmpty(oldData)) {
                        permissionDataManagementRepository.deleteAll(oldData);
                    }
                } else {
                    throw new Exception(common.getMessage("saveEvaluationCriteria.save-not-found", new Object[]{request.getId()}));
                }
            }

            EvaluationCriteria evaluationCriteria = evaluationCriteriaRepository.save(saveEvaluationCriteria);

            // Lưu phân quyền dữ liệu
            if (!ValidationUtils.isNullOrEmpty(request.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : request.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(evaluationCriteria.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.EVALUATION_CRITERIA.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(account);
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                permissionDataManagementRepository.saveAll(permissionDataManagements);
            }

            //Lưu các phòng ban
            if (request.getLstDepartments() != null) {
                evaluationDepartmentRepository.deleteAllByEvaluationCriteriaId(evaluationCriteria.getId());

                // get companyCode theo chartNodeId
                List<Map<String, Object>> lstCompanyCode = customerService.getCompanyCodeByChartNodeIdIn(request.getLstDepartments());
                if (!request.getLstDepartments().isEmpty()) {
                    List<EvaluationDepartment> evaluationDepartments = new ArrayList<>();
                    request.getLstDepartments().forEach(item -> {
                        String companyCode = lstCompanyCode.stream()
                                .filter(e -> e.get("departmentCode").toString().equalsIgnoreCase(item))
                                .map(e -> e.get("companyCode").toString())
                                .findFirst().orElse(null);
                        evaluationDepartments.add(EvaluationDepartment.builder()
                                .evaluationCriteriaId(evaluationCriteria.getId())
                                .departmentCodes(StringUtil.nvl(item, ""))
                                .companyCode(companyCode)
                                .build());
                    });
                    evaluationDepartmentRepository.saveAll(evaluationDepartments);
                }
            }
        } else {
//            throw new Exception(common.getMessage("saveEvaluationCriteria.save-error", new Object[]{request.getName(), request.getReviewItem()}));
            throw new Exception(common.getMessage("saveEvaluationCriteria.save-error"));
        }
    }

    @Override
    public void deleteByIds(EvaluationCriteriaRequest request) throws Exception {
        if (request.getId() == null) {
            return;
        }
        String email = credentialHelper.getJWTPayload().getEmail();
        EvaluationCriteria saveEvaluationCriteria = evaluationCriteriaRepository.findEvaluationCriteriaById(request.getId());
        if (saveEvaluationCriteria != null) {
            saveEvaluationCriteria.setStatus("0");
            saveEvaluationCriteria.setUpdatedUser(email);
            saveEvaluationCriteria.setUpdatedTime(LocalDateTime.now());
            evaluationCriteriaRepository.save(saveEvaluationCriteria);
        }
    }

    @Override
    public PageDto search(EvaluationCriteriaDto request)  {
        int pageNum = request.getPage() - 1;
        Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());
        List<EvaluationCriteria> templates = new ArrayList<>();
        List<EvaluationCriteriaResponse> listResponses = new ArrayList<>();

        String username = credentialHelper.getJWTPayload().getUsername();
        // Lấy list companyCode cấu hình QL vai trò người dùng
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
        request.setLstCompanyCode(lstCompanyCode);

        // Phân quyền theo nhóm
        List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.EVALUATION_CRITERIA.tableName, username);
        // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
        if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
            request.setLstGroupPermissionId(lstGroupPermissionId);
            // get list company code role admin member
            List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
            request.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
        }

        //Xử lý search theo reviewItem
        List<String> reviewItemList = new ArrayList<>();
        Map<String, Object> mapResponse = bpmService.findSystemConfigByCodeAndName("category_check", request.getSearch());
        if (mapResponse != null)
            reviewItemList = (List<String>) mapResponse.get("data");

        Page<EvaluationCriteria> page = evaluationCriteriaRepository.findAll(evaluationCriteriaSpecification.filter(request, reviewItemList),
                PageRequest.of(pageNum, request.getSize(), sort));
        if (page.hasContent()) {
            page.getContent().forEach(item -> {
                List<String> lstDepartments = new ArrayList<>();
                if (item.getEvaluationDepartments() != null) {
                    lstDepartments = item.getEvaluationDepartments().stream().collect(Collectors.mapping(EvaluationDepartment::getDepartmentCodes, Collectors.toList()));
                }
                item.setLstDepartments(lstDepartments);
            });
            templates = page.getContent();

            listResponses = templates.stream().map(x -> modelMapper.map(x, EvaluationCriteriaResponse.class)).collect(Collectors.toList());
            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.EVALUATION_CRITERIA.code);

            for (EvaluationCriteriaResponse response : listResponses) {
                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(response.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
                if (!ValidationUtils.isNullOrEmpty(companyCodes)) {
                    response.setApplyFor(companyCodes);
                }
            }

        }
        return PageDto.builder().content(listResponses)

          .number(page.getNumber() + 1)
                .numberOfElements(page.getNumberOfElements())
                .page(page.getNumber() + 1)
                .size(page.getSize())
                .sortBy(request.getSortBy())
                .sortBy(request.getSortType())
                .totalPages(page.getTotalPages())
                .totalElements(page.getTotalElements())
                .build();
    }

    @Override
    public List<EvaluationCriteriaResponse> searchFilter(EvaluationCriteriaDto request)  {
        List<EvaluationCriteriaResponse> listResponses = new ArrayList<>();

        String username = credentialHelper.getJWTPayload().getUsername();
        // Lấy list companyCode cấu hình QL vai trò người dùng
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
        // Phân quyền theo nhóm
        List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.EVALUATION_CRITERIA.tableName, username);
        // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
        if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
            request.setLstGroupPermissionId(lstGroupPermissionId);
            // get list company code role admin member
            List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
            request.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
        }

        request.setLstCompanyCode(lstCompanyCode);

        //Xử lý search theo reviewItem
        List<String> reviewItemList = new ArrayList<>();
        Map<String, Object> mapResponse = bpmService.findSystemConfigByCodeAndName("category_check", request.getSearch());
        if (mapResponse != null)
            reviewItemList = (List<String>) mapResponse.get("data");

        List<EvaluationCriteria> templates = evaluationCriteriaRepository.findAll(evaluationCriteriaSpecification.filter(request, reviewItemList));
        if (!ValidationUtils.isNullOrEmpty(templates)) {
            templates.forEach(item -> {
                List<String> lstDepartments = new ArrayList<>();
                if (item.getEvaluationDepartments() != null) {
                    lstDepartments = item.getEvaluationDepartments().stream().collect(Collectors.mapping(EvaluationDepartment::getDepartmentCodes, Collectors.toList()));
                }
                item.setLstDepartments(lstDepartments);
            });

            listResponses = templates.stream().map(x -> modelMapper.map(x, EvaluationCriteriaResponse.class)).collect(Collectors.toList());
            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.EVALUATION_CRITERIA.code);

            for (EvaluationCriteriaResponse response : listResponses) {
                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(response.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
                if (!ValidationUtils.isNullOrEmpty(companyCodes)) {
                    response.setApplyFor(companyCodes);
                }
            }

        }
        return listResponses;
    }

    @Override
    public List<EvaluationCriteria> getAllCriteriaByReviewAndDepartment(EvaluationCriteriaSearchDto request) {
        List<EvaluationCriteria> lstEvaluationCriteria = evaluationCriteriaRepository.findAll(evaluationCriteriaSpecification.filterSearch(request));
        lstEvaluationCriteria.forEach(item -> {
            List<String> lstDepartments = new ArrayList<>();
            if (item.getEvaluationDepartments() != null) {
                lstDepartments = item.getEvaluationDepartments().stream().collect(Collectors.mapping(EvaluationDepartment::getDepartmentCodes, Collectors.toList()));
            }
            item.setLstDepartments(lstDepartments);
        });
        return lstEvaluationCriteria;
    }

    @Override
    public List<ChartNodeDto> getAllDepartmentByReview(String companyCode) {
        List<EvaluationCriteria> lstEvaluationCriteria = evaluationCriteriaRepository.getAllDepartmentByReviewAndStatus("1", companyCode);
        List<Long> lstFinalDepartments = new ArrayList<>();
        List<ChartNodeDto> chartNodeDtos = new ArrayList<>();
        if (lstEvaluationCriteria != null) {
            lstEvaluationCriteria.forEach(evaluationCriteria -> {
                List<String> lstDepartments = evaluationCriteria.getEvaluationDepartments().stream()
                        .filter(e -> {
                            if (!ValidationUtils.isNullOrEmpty(companyCode)) {
                                return e.getCompanyCode().equalsIgnoreCase(companyCode);
                            } else {
                                return true;
                            }
                        })
                        .map(EvaluationDepartment::getDepartmentCodes)
                        .collect(Collectors.toList());
                lstDepartments.forEach(s -> {
                    try {
                        lstFinalDepartments.add(Long.valueOf(s));
                    } catch (Exception e) {
                    }
                });
            });
            if (!ValidationUtils.isNullOrEmpty(lstFinalDepartments)) {
                ChartNodeDtoRequest params = ChartNodeDtoRequest.builder()
                        .lstIds(lstFinalDepartments).build();
                chartNodeDtos = customerService.getAllDepartmentByCodes(params);
            }
        }
        return chartNodeDtos;
    }

    @Override
    public List<EvaluationCriteria> getAllCriteria(EvaluationCriteriaDto request) {
        return evaluationCriteriaRepository.findAll(evaluationCriteriaSpecification.filterNew(request));
    }

    @Override
    public List<EvaluationCriteria> getAllCriteriaByPredicate(EvaluationCriteriaDto request) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<EvaluationCriteria> query = cb.createQuery(EvaluationCriteria.class);

            Root<EvaluationCriteria> evaluationCriteria = query.from(EvaluationCriteria.class);
            Predicate predicate = evaluationCriteriaSpecification.filterNew(request).toPredicate(evaluationCriteria, query, cb);
            query.where(predicate);

            EntityGraph<?> entityGraph = em.getEntityGraph("EvaluationCriteria.evaluationDepartments");
            TypedQuery<EvaluationCriteria> typedQuery = em.createQuery(query);
            typedQuery.setHint("jakarta.persistence.fetchgraph", entityGraph);

            return typedQuery.getResultList();
        } catch (Exception e) {
            return null;
        }finally {
            if(em != null)
                em.close();
        }
    }

    @Override
    public Object getAllSystemGroup()  {
        List<Map<String, Object>> lstResponse = new ArrayList<>();
        EvaluationCriteriaDto request = new EvaluationCriteriaDto();
        request.setPage(1);
        request.setLimit(999999);
        request.setSortBy("id");
        request.setSortType("DESC");

        int pageNum = request.getPage() - 1;
        Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());

        String username = credentialHelper.getJWTPayload().getUsername();
        // Lấy list companyCode cấu hình QL vai trò người dùng
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
        // Phân quyền theo nhóm
        List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.EVALUATION_CRITERIA.tableName, username);
        // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
        if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
            request.setLstGroupPermissionId(lstGroupPermissionId);
            // get list company code role admin member
            List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
            request.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
        }

        request.setLstCompanyCode(lstCompanyCode);

        //Xử lý search theo reviewItem
        List<String> reviewItemList = new ArrayList<>();
        Map<String, Object> mapResponse = bpmService.findSystemConfigByCodeAndName("category_check", request.getSearch());
        if (mapResponse != null) {
            reviewItemList = (List<String>) mapResponse.get("data");
        }

        Page<EvaluationCriteria> page = evaluationCriteriaRepository.findAll(
                evaluationCriteriaSpecification.filter(request, reviewItemList),
                PageRequest.of(pageNum, request.getLimit(), sort));
        List<EvaluationCriteria> lstPage = page.getContent();
        for (EvaluationCriteria evaluationCriteria : lstPage) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", evaluationCriteria.getId());
            map.put("name", evaluationCriteria.getName());
            map.put("companyCode", evaluationCriteria.getCompanyCode());
            map.put("companyName", evaluationCriteria.getCompanyName());
            lstResponse.add(map);
        }

        return lstResponse;
    }
}
