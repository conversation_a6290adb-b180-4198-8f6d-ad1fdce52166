package vn.fis.eapprove.business.domain.assign.service;

import vn.fis.eapprove.security.CredentialHelper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import vn.fis.eapprove.business.domain.assign.entity.AssignHistory;
import vn.fis.eapprove.business.domain.assign.entity.AssignManagement;
import vn.fis.eapprove.business.domain.assign.repository.AssignHistoryRepository;
import vn.fis.eapprove.business.domain.assign.repository.AssignRepository;
import vn.fis.eapprove.business.domain.authority.service.*;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcInstRepository;
import vn.fis.eapprove.business.domain.changeAssignee.entity.ChangeAssigneeHistory;
import vn.fis.eapprove.business.domain.changeAssignee.repository.ChangeAssigneeHistoryRepository;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.repository.ServicePackageRepository;
import vn.fis.eapprove.business.dto.*;
import vn.fis.eapprove.business.model.request.AssignRequest;
import vn.fis.eapprove.business.model.response.LoadAssignResponse;
import vn.fis.eapprove.business.model.response.NameAndCodeCompanyResponse;
import vn.fis.eapprove.business.specification.AssignSpecification;
import vn.fis.eapprove.business.tenant.manager.CustomerService;

import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.manager.FileManager;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static vn.fis.eapprove.business.constant.Constant.*;
import static vn.fis.eapprove.business.utils.TimeUtils.localDateTimeToString;
import static vn.fis.eapprove.business.utils.TimeUtils.localDateToString;

@Service("AssignManagerV1")
@Slf4j
@Transactional
public class AssignManager {

    @Value("${app.superAdmin.account}")
    private String appSuperAdminAccount;

    @Autowired
    private AssignRepository assignRepository;

    @Autowired
    private AssignHistoryRepository assignHistoryRepository;

    @Autowired
    private ServicePackageRepository servicePackageRepository;

    @Autowired
    private ChangeAssigneeHistoryRepository changeAssigneeHistoryRepository;

    @Autowired
    private ResponseUtils responseUtils;

    @Autowired
    private ModelMapper modelMapper;

    @Autowired
    private AssignSpecification assignSpecification;

    @Autowired
    private BpmProcInstRepository bpmProcInstRepository;

    @Autowired
    private FileManager fileManager;

    @Autowired
    private CredentialHelper credentialHelper;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private PermissionDataManagementRepository permissionDataManagementRepository;

    @Autowired
    private AuthService authService;

    @Value("${app.s3.bucket}")
    private String bucket;

    public String createAssign(AssignRequest assignRequest) {
        try {
            String account = appSuperAdminAccount;
            try {
                account = credentialHelper.getJWTPayload().getUsername();
            } catch (Exception e) {
            }
            if (Boolean.TRUE.equals(assignRequest.getTicketAssign())) {
                log.info("Start createAssign, request = {}", assignRequest);
                AssignManagement assign = new AssignManagement();
                Long version = 1l;
                Integer status = assignRequest.getStatus();
                if (!ValidationUtils.isNullOrEmpty(assignRequest.getId())) {
                    assign = assignRepository.getById(assignRequest.getId());
                    assign.setUpdatedDate(LocalDateTime.now());
                    assign.setUpdatedUser(account);
                    version = assignHistoryRepository.countByAssignId(assign.getId()) + 1;
                } else {
                    assign.setCreatedUser(assignRequest.getCreatedUser());
                    assign.setCreatedDate(LocalDateTime.now());
                }
                assign.setAssignName(assignRequest.getAssignName());
                assign.setAssignUser(assignRequest.getAssignUser());
                assign.setAssignedUser(assignRequest.getAssignedUser());
                List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(assign.getCreatedUser());
                for(NameAndCodeCompanyResponse response : listCompanyCodeAndName){
                    assign.setCompanyCode(response.getCompanyCode());
                    assign.setCompanyName(response.getCompanyName());
                }
                if (!ValidationUtils.isNullOrEmpty(assignRequest.getTicketId())) {
                    assign.setTicketId(assignRequest.getTicketId());
                }
                if (!ValidationUtils.isNullOrEmpty(assignRequest.getRequestCode())) {
                    assign.setRequestCode(assignRequest.getRequestCode());
                }
//                if (!ValidationUtils.isNullOrEmpty(assignRequest.getNewRequestCode())) {
//                    assign.setNewRequestCode(assignRequest.getNewRequestCode());
//                }

//                assign.setServiceRange(assignRequest.getServiceRange().stream().map(String::valueOf).collect(Collectors.joining(",")));
                assign.setServiceRange(assignRequest.getServiceId().toString());
                assign.setStartDate(assignRequest.getStartDate());
                assign.setEndDate(assignRequest.getEndDate());

                if (!ValidationUtils.isNullOrEmpty(assignRequest.getStatus()) && assignRequest.getStatus() != 0) {
//                    assign.setNewTicket(assignRequest.getNewTicket());

                    // Cập nhật lại assignManagementTicketId
                    List<ChangeAssigneeHistory> changeAssigneeHistories = changeAssigneeHistoryRepository.findChangeAssigneeHistoriesByAssignTicketId(Long.valueOf(assignRequest.getNewTicket()));
                    if (!ValidationUtils.isNullOrEmpty(changeAssigneeHistories) && !ValidationUtils.isNullOrEmpty(assignRequest.getTicketId())) {
                        for (ChangeAssigneeHistory changeAssigneeHistory : changeAssigneeHistories) {
                            changeAssigneeHistory.setAssignTicketId(assignRequest.getTicketId());
                        }

                        changeAssigneeHistoryRepository.saveAll(changeAssigneeHistories);
                    }

                    //Cập nhập lại status + requestCode của thằng cũ
                    AssignManagement old = assignRepository.findAssignManagementByTicketId(Long.valueOf(assignRequest.getNewTicket()));
//                    Integer oldStatus = status;
                    if (old != null) {
//                        old.setHistoryStatus(oldStatus);
                        old.setStatus(status == 1 ? -1 : -2);
                        old.setNewTicket(assignRequest.getTicketId().toString());
                        old.setNewRequestCode(assignRequest.getRequestCode());
                        old.setUpdatedDate(LocalDateTime.now());
                        old.setUpdatedUser(account);
//                        old.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
                        assignRepository.save(old);
                    }
                }


                assign.setStatus(status);
                assign.setHistoryStatus(status);
                assign.setEffect(assignRequest.getEffect());
                assign.setDescription(assignRequest.getDescription());
                assign.setType(assignRequest.getType() != 1 ? 0 : 1);
                assign.setAssignTitle(assignRequest.getAssignTitle());
                assign.setAssignDuty(assignRequest.getAssignDuty() != null ? assignRequest.getAssignDuty().replaceAll("<br/>","\n") : "" );
                assign.setAssignCompanyName(assignRequest.getAssignCompanyName());
                assign.setAssignDecision(assignRequest.getAssignDecision());
                assign.setAssignedTitle(assignRequest.getAssignedTitle());
                assign.setAssignStorage(assignRequest.getAssignStorage());

                assign.setFileName(assignRequest.getFileName());
                assign.setServiceId(assignRequest.getServiceId());
                assign.setAuthorityConditions(assignRequest.getAuthorityConditions().size() > 0 ? assignRequest.getAuthorityConditions().get(0) : null);
                assign = assignRepository.save(assign);

                // Lưu phân quyền dữ liệu theo 3 user
                List<String> lstCompanyCode = new ArrayList<>();
                List<ChartDto> assignChart = customerService.getLstChartByUsername(assign.getAssignUser());
                if (!ValidationUtils.isNullOrEmpty(assignChart)) {
                    List<String> defaultCompanyCode = assignChart.stream().map(ChartDto::getCode).distinct().collect(Collectors.toList());
                    if (!ValidationUtils.isNullOrEmpty(defaultCompanyCode)) {
                        lstCompanyCode.addAll(defaultCompanyCode);
                    }
                }
                List<ChartDto> assignedChart = customerService.getLstChartByUsername(assign.getAssignedUser());
                if (!ValidationUtils.isNullOrEmpty(assignedChart)) {
                    List<String> defaultCompanyCode = assignedChart.stream().map(ChartDto::getCode).distinct().collect(Collectors.toList());
                    if (!ValidationUtils.isNullOrEmpty(defaultCompanyCode)) {
                        lstCompanyCode.addAll(defaultCompanyCode);
                    }
                }
                List<ChartDto> createdChart = customerService.getLstChartByUsername(assign.getCreatedUser());
                if (!ValidationUtils.isNullOrEmpty(createdChart)) {
                    List<String> defaultCompanyCode = createdChart.stream().map(ChartDto::getCode).distinct().collect(Collectors.toList());
                    if (!ValidationUtils.isNullOrEmpty(defaultCompanyCode)) {
                        lstCompanyCode.addAll(defaultCompanyCode);
                    }
                }

                if (!ValidationUtils.isNullOrEmpty(lstCompanyCode)) {
                    List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                    for (String data : lstCompanyCode) {
                        PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                        permissionDataManagement.setTypeId(assign.getId());
                        permissionDataManagement.setTypeName(PermissionDataConstants.Type.ASSIGN_MANAGEMENT.code);
                        permissionDataManagement.setCompanyCode(data);
                        permissionDataManagement.setCreatedUser(account);
                        permissionDataManagement.setCreatedTime(LocalDateTime.now());
                        permissionDataManagements.add(permissionDataManagement);
                    }

                    permissionDataManagementRepository.saveAll(permissionDataManagements);
                }


//            AssignManagement saveAuth = assignRepository.save(assign);
//            assignHistory.setAssignId(saveAuth.getId());
//
//            assignHistory.setVersion(VERSION_NAME + version);
//            assignHistory.setTicketId(assignRequest.getTicketId());
//            assignHistory.setAssignTime(localDateToString(assignRequest.getStartDate(), FORMAT_DATE_2) + " - " + localDateToString(assignRequest.getEndDate(), FORMAT_DATE_2));
//            assignHistory.setAssignUser(saveAuth.getAssignUser());
//            assignHistory.setCreatedDate(LocalDateTime.now());
//            assignHistory.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
//            assignHistoryRepository.save(assignHistory);
                return "Success";
            } else return "NoApprove";
        } catch (Exception e) {
            log.error("CreateAssign Error =>", e);
            return "Fail";
        } finally {
            log.info("Create success");
        }
    }

    public boolean updatedAssign(AssignRequest assignRequest) {
        try {
            boolean check = false;
            AssignManagement assign = assignRepository.getById(assignRequest.getId());
            if (!assignRequest.getAssignName().equalsIgnoreCase(assign.getAssignName())) {
                check = true;
            }
            if (!assignRequest.getAssignUser().equalsIgnoreCase(assign.getAssignUser())) {
                check = true;
            }
            if (!assignRequest.getAssignedUser().equalsIgnoreCase(assign.getAssignedUser())) {
                check = true;
            }
            if (!Objects.equals(assignRequest.getTicketId(), assign.getTicketId())) {
                check = true;
            }
            if (!assignRequest.getRequestCode().equalsIgnoreCase(assign.getRequestCode())) {
                check = true;
            }
            if (!assignRequest.getNewRequestCode().equalsIgnoreCase(assign.getNewRequestCode())) {
                check = true;
            }
            List<String> myList1 = new ArrayList<String>(Arrays.asList(assign.getServiceRange().split(",")));
            if (myList1.size() == assignRequest.getServiceRange().size()) {
                if (!assignRequest.getServiceRange().containsAll(myList1.stream().map(Long::parseLong).collect(Collectors.toList()))) {
                    check = true;
                }
            } else {
                check = true;
            }
            if (assignRequest.getStartDate().compareTo(assign.getStartDate()) != 0) {
                check = true;
            }
            if (assignRequest.getEndDate().compareTo(assign.getEndDate()) != 0) {
                check = true;
            }
            if (!Objects.equals(assignRequest.getStatus(), assign.getStatus())) {
                check = true;
            }
            if (!Objects.equals(assignRequest.getEffect(), assign.getEffect())) {
                check = true;
            }
            if (!assignRequest.getDescription().equalsIgnoreCase(assign.getDescription())) {
                check = true;
            }
            if (!assignRequest.getFileName().equalsIgnoreCase(assign.getFileName())) {
                check = true;
            }

            if (check) {
                createAssign(assignRequest);
            }

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            log.info("Updated success");
        }
    }

    public int deleteAssign(List<Long> listId) {
        try {
            if (listId.size() > 1) {
                for (Long id : listId) {
                    assignRepository.deleteById(id);
                    log.info("Delete working time successfully");
                }
                return SUCCESS;
            } else {
                assignRepository.deleteById(listId.get(0));
                log.info("Delete working time successfully");
                return SUCCESS;
            }
        } catch (Exception e) {
            log.info("Delete failed");
            return FAILED;
        }
    }

    public PageDto searchAssign(AssignDto criteria) {
        try {
            String username = appSuperAdminAccount;
            try {
                username = credentialHelper.getJWTPayload().getUsername();
            } catch (Exception e) {
            }
            int pageNum = criteria.getPage() - 1;
            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());

            // Lấy list companyCode cấu hình QL vai trò người dùng
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
            criteria.setLstCompanyCode(lstCompanyCode);

            Page<AssignManagement> page = assignRepository.findAll(assignSpecification.filter(criteria), PageRequest.of(pageNum, criteria.getLimit(), sort));
            List<LoadAssignResponse> loadAssignResponse1 = new ArrayList<>();
            for (AssignManagement assign : page.getContent()) {
                LoadAssignResponse loadAssignResponse = new LoadAssignResponse();
                loadAssignResponse.setId(assign.getId());
                loadAssignResponse.setAssignName(assign.getAssignName());
                loadAssignResponse.setAssignUser(assign.getAssignUser());
                loadAssignResponse.setAssignedUser(assign.getAssignedUser());
                if (!ValidationUtils.isNullOrEmpty(assign.getTicketId())) {
                    loadAssignResponse.setTicketId(assign.getTicketId());
                }
                if (!ValidationUtils.isNullOrEmpty(assign.getRequestCode())) {
                    loadAssignResponse.setRequestCode(assign.getRequestCode());
                }
                if (!ValidationUtils.isNullOrEmpty(assign.getNewRequestCode())) {
                    loadAssignResponse.setNewRequestCode(assign.getNewRequestCode());
                }
                if (!assign.getServiceRange().equals("")) {
                    List<String> stringList = new ArrayList<>(Arrays.asList(assign.getServiceRange().split(",")));
                    List<Long> list = stringList.stream().map(Long::valueOf).collect(Collectors.toList());
                    List<ServicePackage> servicePackageList = servicePackageRepository.findByIdIn(list);
                    List<ServicePackageDto> servicePackageDtos = servicePackageList.stream().map(servicePackage -> modelMapper.map(servicePackage, ServicePackageDto.class)).collect(Collectors.toList());
                    loadAssignResponse.setServiceRange(servicePackageDtos);
                }
                loadAssignResponse.setStartDate(localDateToString(assign.getStartDate(), FORMAT_DATE_2));
                loadAssignResponse.setEndDate(localDateToString(assign.getEndDate(), FORMAT_DATE_2));
                loadAssignResponse.setStatus(assign.getStatus());
                loadAssignResponse.setEffect(assign.getEffect());
                loadAssignResponse.setDescription(assign.getDescription());
                loadAssignResponse.setCreatedDate(localDateTimeToString(assign.getCreatedDate(), FORMAT_DATE_TIME_2));
                loadAssignResponse.setFileName(assign.getFileName());
                loadAssignResponse.setAuthorityConditions(assign.getAuthorityConditions());
                loadAssignResponse.setServiceId(assign.getServiceId());
                loadAssignResponse.setCompanyName(assign.getCompanyName());
                loadAssignResponse.setCompanyCode(assign.getCompanyCode());
                if (assign.getUpdatedDate() != null) {
                    loadAssignResponse.setUpdatedDate(localDateTimeToString(assign.getUpdatedDate(), FORMAT_DATE_TIME_2));
                }
                loadAssignResponse.setCreatedUser(assign.getCreatedUser());
//                loadAssignResponse.setUpdatedUser(!ValidationUtils.isNullOrEmpty(assign.getUpdatedUser()) ? assign.getUpdatedUser() : "");
//                try {
//                    if (!ValidationUtils.isNullOrEmpty(assign.getFileName())) {
                        loadAssignResponse.setFileName(assign.getFileName());
//                        InputStream inputStream = fileManager.getFileInputStream(bucket, assign.getFileName());
//                        byte[] sourceBytes = IOUtils.toByteArray(inputStream);
//                        String encodedString = Base64.getEncoder().encodeToString(sourceBytes);
//                        loadAssignResponse.setBase64(encodedString);
//                    }
//                } catch (Exception ex) {
//
//                }
                if (!ValidationUtils.isNullOrEmpty(assign.getNewTicket())) {
                    loadAssignResponse.setNewTicket(assign.getNewTicket());
                }

                loadAssignResponse1.add(loadAssignResponse);

            }
            return PageDto.builder().content(loadAssignResponse1)
                    .number(page.getNumber() + 1)
                    .numberOfElements(page.getNumberOfElements())
                    .page(page.getNumber() + 1)
                    .limit(page.getSize())
                    .totalPages(page.getTotalPages())
                    .totalElements(page.getTotalElements())
                    .sortBy(criteria.getSortBy())
                    .sortType(criteria.getSortType())
                    .build();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            e.printStackTrace();
            return null;
        }
    }

    public List<LoadAssignResponse> searchAssignFilter(AssignDto criteria) {
        try {
//            Integer pageNum = criteria.getPage() - 1;
//            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());
//            Page<Authorize> page = authorizeRepository.findAll(authorizeSpecification.filter(criteria),
//                    PageRequest.of(pageNum, criteria.getLimit(),
//                            sort));
            String username = appSuperAdminAccount;
            try {
                username = credentialHelper.getJWTPayload().getUsername();
            } catch (Exception e) {
            }
//            Integer pageNum = criteria.getPage() - 1;
//            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());

            // Lấy list companyCode cấu hình QL vai trò người dùng
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
            criteria.setLstCompanyCode(lstCompanyCode);

            List<AssignManagement> page = assignRepository.findAll(assignSpecification.filter(criteria)
//                    , PageRequest.of(pageNum, criteria.getLimit(), sort)
            );
            List<LoadAssignResponse> loadAssignResponse1 = new ArrayList<>();
            for (AssignManagement assign : page) {
                LoadAssignResponse loadAssignResponse = new LoadAssignResponse();
                loadAssignResponse.setId(assign.getId());
                loadAssignResponse.setAssignName(assign.getAssignName());
                loadAssignResponse.setAssignUser(assign.getAssignUser());
                loadAssignResponse.setAssignedUser(assign.getAssignedUser());
                if (!ValidationUtils.isNullOrEmpty(assign.getTicketId())) {
                    loadAssignResponse.setTicketId(assign.getTicketId());
                }
                if (!ValidationUtils.isNullOrEmpty(assign.getRequestCode())) {
                    loadAssignResponse.setRequestCode(assign.getRequestCode());
                }
                if (!ValidationUtils.isNullOrEmpty(assign.getNewRequestCode())) {
                    loadAssignResponse.setNewRequestCode(assign.getNewRequestCode());
                }
                if (!assign.getServiceRange().equals("")) {
                    List<String> stringList = new ArrayList<>(Arrays.asList(assign.getServiceRange().split(",")));
                    List<Long> list = stringList.stream().map(Long::valueOf).collect(Collectors.toList());
                    List<ServicePackage> servicePackageList = servicePackageRepository.findByIdIn(list);
                    List<ServicePackageDto> servicePackageDtos = servicePackageList.stream().map(servicePackage -> modelMapper.map(servicePackage, ServicePackageDto.class)).collect(Collectors.toList());
                    loadAssignResponse.setServiceRange(servicePackageDtos);
                }
                loadAssignResponse.setStartDate(localDateToString(assign.getStartDate(), FORMAT_DATE_2));
                loadAssignResponse.setEndDate(localDateToString(assign.getEndDate(), FORMAT_DATE_2));
                loadAssignResponse.setStatus(assign.getStatus());
                loadAssignResponse.setEffect(assign.getEffect());
                loadAssignResponse.setDescription(assign.getDescription());
                loadAssignResponse.setCreatedDate(localDateTimeToString(assign.getCreatedDate(), FORMAT_DATE_TIME_2));
                loadAssignResponse.setFileName(assign.getFileName());
                loadAssignResponse.setAuthorityConditions(assign.getAuthorityConditions());
                loadAssignResponse.setServiceId(assign.getServiceId());
                loadAssignResponse.setCompanyName(assign.getCompanyName());
                loadAssignResponse.setCompanyCode(assign.getCompanyCode());
                if (assign.getUpdatedDate() != null) {
                    loadAssignResponse.setUpdatedDate(localDateTimeToString(assign.getUpdatedDate(), FORMAT_DATE_TIME_2));
                }
                loadAssignResponse.setCreatedUser(assign.getCreatedUser());
                loadAssignResponse.setUpdatedUser(!ValidationUtils.isNullOrEmpty(assign.getUpdatedUser()) ? assign.getUpdatedUser() : "");
//                try {
//                    if (!ValidationUtils.isNullOrEmpty(assign.getFileName())) {
//                        loadAssignResponse.setFileName(assign.getFileName());
//                        InputStream inputStream = fileManager.getFileInputStream(bucket, assign.getFileName());
//                        byte[] sourceBytes = IOUtils.toByteArray(inputStream);
//                        String encodedString = Base64.getEncoder().encodeToString(sourceBytes);
//                        loadAssignResponse.setBase64(encodedString);
//                    }
//                } catch (Exception ex) {
//
//                }
                if (!ValidationUtils.isNullOrEmpty(assign.getNewTicket())) {
                    loadAssignResponse.setNewTicket(assign.getNewTicket());
                }

                loadAssignResponse1.add(loadAssignResponse);

            }
            return loadAssignResponse1;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            e.printStackTrace();
            return null;
        }
    }

    //    public LoadAssignResponse loadAssign(Long id) {
//        AssignManagement assign = assignRepository.getById(id);
//        LoadAssignResponse loadAssignResponse = new LoadAssignResponse();
//        loadAssignResponse.setId(assign.getId());
//        loadAssignResponse.setAssignName(assign.getAssignName());
//        loadAssignResponse.setAssignUser(assign.getAssignUser());
//        loadAssignResponse.setAssignedUser(assign.getAssignedUser());
//        List<String> myList = new ArrayList<>(Arrays.asList(assign.getTicketId().split(",")));
//        loadAssignResponse.setTicketId(myList.stream().map(Long::valueOf).collect(Collectors.toList()));
//        loadAssignResponse.setDescription(assign.getDescription());
//        loadAssignResponse.setStatus(assign.getStatus());
//        loadAssignResponse.setEffect(assign.getEffect());
//        loadAssignResponse.setStartDate(localDateToString(assign.getStartDate(), FORMAT_DATE_2));
//        loadAssignResponse.setEndDate(localDateToString(assign.getEndDate(), FORMAT_DATE_2));
//
//        List<String> stringList = new ArrayList<>(Arrays.asList(assign.getServiceRange().split(",")));
//        List<Long> list = stringList.stream().map(Long::valueOf).collect(Collectors.toList());
//        List<ServicePackage> servicePackageList = servicePackageRepository.findByIdIn(list);
//        List<ServicePackageDto> servicePackageDtos = servicePackageList.stream().map(servicePackage -> modelMapper.map(servicePackage, ServicePackageDto.class)).collect(Collectors.toList());
//        loadAssignResponse.setServiceRange(servicePackageDtos);
//        return loadAssignResponse;
//    }
    public List<AssignHistoryDto> getAllAssignHistory(Long id) {
        try {
            List<AssignHistory> assignHistories = assignHistoryRepository.findByAssignId(id);
            List<AssignHistoryDto> assignHistoryDtos = new ArrayList<>();
            for (AssignHistory assignHistory : assignHistories) {
                AssignHistoryDto assignHistoryDto = AssignHistoryDto.builder()
                        .id(assignHistory.getId())
                        .assignId(assignHistory.getAssignId())
                        .version(assignHistory.getVersion())
                        .ticketId(assignHistory.getTicketId())
                        .requestCode(assignHistory.getRequestCode())
                        .assignTime(assignHistory.getAssignTime())
                        .assignUser(assignHistory.getAssignUser())
                        .createdDate(localDateTimeToString(assignHistory.getCreatedDate(), LOCALFORMAT))
                        .createdUser(assignHistory.getCreatedUser())
                        .build();
                assignHistoryDtos.add(assignHistoryDto);
            }
            return assignHistoryDtos;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<ListTicketDto> getAllProcInst() {
        try {
            List<BpmProcInst> bpmProcInstList = bpmProcInstRepository.findBpmProcInstByTicketStatusIn(new String[]{"COMPLETED", "CLOSED"});
            List<ListTicketDto> listTicketDtos = bpmProcInstList.stream().map(bpmProcInst -> modelMapper.map(bpmProcInst, ListTicketDto.class)).collect(Collectors.toList());

            return listTicketDtos;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public String upload(MultipartFile file) {
        try {
            if (!fileManager.isBucketExisting(bucket)) {
                fileManager.createBucket(bucket);
            }
            fileManager.putFile(bucket, file.getOriginalFilename(), file.getSize(), file.getInputStream());
            return file.getOriginalFilename();
        } catch (Exception e) {
            log.error("Happened error when upload file: ");
            return null;
        }
    }

    public List<AssignManagement> findAssignManagementByAssignUser(String user) {
        LocalDate date = LocalDate.now().plusDays(-1);
        return assignRepository.findAssignManagementByAssignUserOrderByIdDesc(user, date);
    }

    public AssignManagement findAssignManagementByAssignedUser(String user) {
        return assignRepository.findAssignManagementByAssignedUser(user);
    }


    public List<ServicePackage> findServiceRangeById(Long id) {
        try {
            String assignManagements = assignRepository.findServiceRangeById(id);
            List<String> myList = new ArrayList<>(Arrays.asList(assignManagements.split(",")));
            List<Long> serviceId = myList.stream().map(Long::parseLong).collect(Collectors.toList());
            List<ServicePackage> servicePackages = servicePackageRepository.findByIdIn(serviceId);
            return servicePackages;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<AssignManagement> getAllAssign(String username) {
        try {
            Gson gson = new Gson();
            int[] status = {0, 1};
            List<AssignManagement> assignManagements = assignRepository.getAllAssign(status, username);
            assignManagements = assignManagements.stream().map(i -> {
                if (!ValidationUtils.isNullOrEmpty(i.getAuthorityConditions())) {
                    try {
                        Map<String, Object> jsonConditionData = gson.fromJson(i.getAuthorityConditions(), HashMap.class);
                        List<Object> arrayList = (List<Object>) jsonConditionData.get("data");
                        i.setConditionList(arrayList);
                    } catch (Exception e) {
                        i.setConditionList(null);
                    }
                }
                return i;
            }).collect(Collectors.toList());
            return assignManagements;
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    //Lấy ds ủy quyền còn hoạt động theo username
    public List<AssignManagement> getAllAssignActiveByUsername(String username) {
        try {
            int[] status = {0, 1};
            List<AssignManagement> assignManagements = assignRepository.getAllAssign(status, username);
            return assignManagements;
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }


    public LoadAssignResponse getHistoryAssigned(Long ticketId) {//Lấy ds lịch sử phiếu
        try {
            AssignManagement assignManagement = assignRepository.getHistoryAssign(ticketId);
            LoadAssignResponse loadAssignResponse = new LoadAssignResponse();
            loadAssignResponse.setId(assignManagement.getId());
            loadAssignResponse.setAssignName(assignManagement.getAssignName());
            loadAssignResponse.setAssignUser(assignManagement.getAssignUser());
            loadAssignResponse.setAssignedUser(assignManagement.getAssignedUser());
            if (!ValidationUtils.isNullOrEmpty(assignManagement.getTicketId())) {
                loadAssignResponse.setTicketId(assignManagement.getTicketId());
            }
            if (!ValidationUtils.isNullOrEmpty(assignManagement.getRequestCode())) {
                loadAssignResponse.setRequestCode(assignManagement.getRequestCode());
            }
            if (!ValidationUtils.isNullOrEmpty(assignManagement.getNewRequestCode())) {
                loadAssignResponse.setNewRequestCode(assignManagement.getNewRequestCode());
            }
            loadAssignResponse.setCreatedDate(localDateTimeToString(assignManagement.getCreatedDate(), FORMAT_DATE_TIME_2));
            loadAssignResponse.setCreatedUser(assignManagement.getCreatedUser());
            loadAssignResponse.setStatus(assignManagement.getStatus());
            loadAssignResponse.setAssignTitle((assignManagement.getAssignTitle()));
            loadAssignResponse.setTitleName(customerService.getUserTitle(assignManagement.getCreatedUser()));
            loadAssignResponse.setHistoryStatus(assignManagement.getHistoryStatus());
            return loadAssignResponse;
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public AssignManagement getInfoAutoChangeImplement(Long ticketId, Boolean isNewTicket) { // Lấy lịch sử ủy quyền ủy quyền tự động theo tờ trình liên ket
        try {
            AssignManagement assignManagement = null;
            if (isNewTicket) {
                assignManagement = assignRepository.getByNewTicket(ticketId.toString());
            } else
                assignManagement = assignRepository.getByTicketId(ticketId);

            if (assignManagement != null) { // Bị gia hạn
                switch (assignManagement.getStatus()) {
                    case 1: //Gia hạn
                        break;
                    case -1: // Bị gia hạn
                        assignManagement = getInfoAutoChangeImplement(assignManagement.getTicketId(), true);
                        break;
                    case 2:// Bị hủy
                    case -2: // Hủy
                        assignManagement = null;
                    default:
                        break;
                }
            }
            return assignManagement;
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public List<AssignManagement> getActiveAssignments(String assignUser, String assignedUser) {
        return assignRepository.getActiveAssignments(assignUser, assignedUser);
    }

    public boolean isAssignmentExpired(String assignUser, String assignedUser, Long serviceId) {
        List<AssignManagement> assignManagements = getActiveAssignments(assignUser, assignedUser);
        Long serviceParent = servicePackageRepository.findIdParentServicePackage(serviceId);
        if (!ValidationUtils.isNullOrEmpty(assignManagements)) {
            // check service
            List<AssignManagement> filterList = assignManagements.stream().filter(e -> e.getServiceId().equals(serviceParent)).collect(Collectors.toList());
            // Check ủy quyền theo bản ghi mới nhất
            if (!ValidationUtils.isNullOrEmpty(filterList)) {
                AssignManagement assignManagement = filterList.get(0);
                return assignManagement.getEndDate().isBefore(LocalDate.now()) && !ValidationUtils.isNullOrEmpty(assignManagement.getEffect()) && assignManagement.getEffect() == 0;
            } else {
                return true;
            }
        }
        return true;
    }

    public void updateStatusAssignManager() {
        int[] status = {0, 1};
        List<AssignManagement> assigns = assignRepository.findByStatus(status);
        for (AssignManagement assignManagement : assigns) {
            if (!ValidationUtils.isNullOrEmpty(assignManagement.getEndDate())) {
                if (assignManagement.getEndDate().isBefore(LocalDate.now())) {
                    assignManagement.setStatus(-3);
                }
                assignRepository.save(assignManagement);
            }
        }
    }

    public LoadAssignResponse getAllAssignByRequestCode(String requestCode) {//Lấy ds lịch sử phiếu
        try {
            AssignManagement assign = assignRepository.getAllAssignByRequestCode(requestCode);
            LoadAssignResponse loadAssignResponse = new LoadAssignResponse();
            loadAssignResponse.setId(assign.getId());
            loadAssignResponse.setAssignName(assign.getAssignName());
            loadAssignResponse.setAssignUser(assign.getAssignUser());
            loadAssignResponse.setAssignedUser(assign.getAssignedUser());
            if (!ValidationUtils.isNullOrEmpty(assign.getTicketId())) {
                loadAssignResponse.setTicketId(assign.getTicketId());
            }
            if (!ValidationUtils.isNullOrEmpty(assign.getRequestCode())) {
                loadAssignResponse.setRequestCode(assign.getRequestCode());
            }
            if (!ValidationUtils.isNullOrEmpty(assign.getNewRequestCode())) {
                loadAssignResponse.setNewRequestCode(assign.getNewRequestCode());
            }
            if (!assign.getServiceRange().equals("")) {
                List<String> stringList = new ArrayList<>(Arrays.asList(assign.getServiceRange().split(",")));
                List<Long> list = stringList.stream().map(Long::valueOf).collect(Collectors.toList());
                List<ServicePackage> servicePackageList = servicePackageRepository.findByIdIn(list);
                List<ServicePackageDto> servicePackageDtos = servicePackageList.stream().map(servicePackage -> modelMapper.map(servicePackage, ServicePackageDto.class)).collect(Collectors.toList());
                loadAssignResponse.setServiceRange(servicePackageDtos);
            }
            loadAssignResponse.setStartDate(localDateToString(assign.getStartDate(), FORMAT_DATE_2));
            loadAssignResponse.setEndDate(localDateToString(assign.getEndDate(), FORMAT_DATE_2));
            loadAssignResponse.setStatus(assign.getStatus());
            loadAssignResponse.setHistoryStatus(assign.getHistoryStatus());
            loadAssignResponse.setEffect(assign.getEffect());
            loadAssignResponse.setDescription(assign.getDescription());
            loadAssignResponse.setCreatedDate(localDateTimeToString(assign.getCreatedDate(), FORMAT_DATE_TIME_2));
            loadAssignResponse.setFileName(assign.getFileName());
            loadAssignResponse.setAuthorityConditions(assign.getAuthorityConditions());
            loadAssignResponse.setServiceId(assign.getServiceId());
            loadAssignResponse.setCompanyName(assign.getCompanyName());
            loadAssignResponse.setCompanyCode(assign.getCompanyCode());
            if (assign.getUpdatedDate() != null) {
                loadAssignResponse.setUpdatedDate(localDateTimeToString(assign.getUpdatedDate(), FORMAT_DATE_TIME_2));
            }
            loadAssignResponse.setCreatedUser(assign.getCreatedUser());
            loadAssignResponse.setUpdatedUser(!ValidationUtils.isNullOrEmpty(assign.getUpdatedUser()) ? assign.getUpdatedUser() : "");
//            try {
//                if (!ValidationUtils.isNullOrEmpty(assign.getFileName())) {
                    loadAssignResponse.setFileName(assign.getFileName());
//                    InputStream inputStream = fileManager.getFileInputStream(bucket, assign.getFileName());
//                    byte[] sourceBytes = IOUtils.toByteArray(inputStream);
//                    String encodedString = Base64.getEncoder().encodeToString(sourceBytes);
//                    loadAssignResponse.setBase64(encodedString);
//                }
//            } catch (Exception ex) {
//
//            }
            if (!ValidationUtils.isNullOrEmpty(assign.getNewTicket())) {
                loadAssignResponse.setNewTicket(assign.getNewTicket());
            }

            return loadAssignResponse;
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }
}
