package vn.fis.eapprove.business.domain.manageApi.service;


import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.CreateShareTicketRequest;
import vn.fis.eapprove.business.model.request.SearchShareTicketRequest;
import vn.fis.eapprove.business.model.response.ManageShareTicketDetailResponse;
import vn.fis.eapprove.business.model.response.ManageShareTicketResponse;

import java.time.LocalDate;
import java.util.List;

public interface ManageShareTicketService {
    boolean createUpdate(CreateShareTicketRequest request);
    String validate(CreateShareTicketRequest request);
    String validateUpdateStatus(CreateShareTicketRequest request);
    boolean updateStatus(Long id, String status) ;
    PageDto search(SearchShareTicketRequest request) ;
    List<ManageShareTicketResponse> searchFilter(SearchShareTicketRequest request) ;
    ManageShareTicketDetailResponse getDetail(Long id);

    void processShareTicket(String procInstId, String procDefId, String companyCode, String createdUser, String chartNodeCode, String serviceId, LocalDate createdDate);
}
