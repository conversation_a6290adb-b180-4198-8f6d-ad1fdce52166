package vn.fis.eapprove.business.domain.authority.service;


import vn.fis.eapprove.business.domain.assign.entity.AssignManagement;
import vn.fis.eapprove.business.domain.authority.entity.AuthorityManagement;


import java.util.List;

/**
 * <AUTHOR>
 * @project business-process-service
 * @created 3/13/2023 - 4:57 PM
 */
public interface AuthorityManagementService {
    AuthorityManagement saveAuthority(String ticketId, AssignManagement assignManagement, String taskId, String taskDefkey);

    List<AuthorityManagement> findAuthorityByEmail(String fromAccount, String toAccount);

    void deleteAuthority(Long id);

    AuthorityManagement getByTicketId(Long ticketId, Integer type);

    void updateAuthorityById(Long id, String fromAccount, String toAccount, Integer type) ;

    AuthorityManagement saveAuthorityByTask(String ticketId, String fromAccount, String toAccount, String taskId, String taskDefkey) ;

    AuthorityManagement saveAuthorityByTicket(String ticketId, String fromAccount, String toAccount) ;

    AuthorityManagement saveAuthorityByAssistance(String ticketId, String fromAccount, String toAccount) ;
}
