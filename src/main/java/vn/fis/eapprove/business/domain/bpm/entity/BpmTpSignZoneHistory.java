package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "bpm_tp_sign_zone_history")
public class BpmTpSignZoneHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "BPM_TP_SIGN_ZONE_ID")
    private Long bpmTpSignZoneId;

    @Column(name = "BPM_TEMPLATE_PRINT_ID")
    private Long bpmTemplatePrintId;


    @Column(name = "SIGN")
    private String sign;

    @Column(name = "TASK_DEF_KEY")
    private String taskDefKey;

    @Column(name = "PROC_INST_ID")
    private String procInstId;

    @Column(name = "ORDER_SIGN")
    private Long orderSign;

    @Column(name = "EMAIL")
    private String email;

    @Column(name = "FIRST_NAME")
    private String firstName;

    @Column(name = "LAST_NAME")
    private String lastName;

    @Column(name = "POSITION")
    private String position;

    @Column(name = "SIGN_PAGE")
    private Long page;

    @Column(name = "X")
    private Long x;

    @Column(name = "Y")
    private Long y;

    @Column(name = "W")
    private Long w;

    @Column(name = "H")
    private Long h;

    @Column(name = "CREATED_DATE")
    private Date createdDate;
}
