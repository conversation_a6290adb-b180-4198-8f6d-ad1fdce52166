package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.time.LocalDateTime;


@Data
@Entity
@Table(name = "notify_group")
@AllArgsConstructor
@NoArgsConstructor
public class BpmProInstNotifyGroup {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "bpm_procinst_id")
    private Long bpmProcinstId;

    @Column(name = "group_id")
    private String groupId;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "created_user")
    private String createUser;

    @Column(name = "chart_id")
    private Long chartId;
}
