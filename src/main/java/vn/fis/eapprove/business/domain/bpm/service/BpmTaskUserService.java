package vn.fis.eapprove.business.domain.bpm.service;


import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTaskUser;


import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Author: PhucVM
 * Date: 23/11/2022
 */
public interface BpmTaskUserService {

    void updateNewUserName(String procInstId, String taskId, String oldUserName, String newUserName);

    void saveFromBpmTasks(List<BpmTask> bpmTasks);

    void changeAssignUserAndUpdateBpmTask(List<BpmTask> bpmTasks, String ticketId, String procInstId, String serviceId) ;

    void saveFromBpmTask(BpmTask bpmTask);

    List<BpmTaskUser> saveAll(List<BpmTaskUser> bpmTaskUsers);

    BpmTaskUser save(BpmTaskUser bpmTaskUser);

    void deleteAllByTaskIds(List<String> taskIds);

    void deleteAllByTaskDefKeys(String procInstId, Set<String> taskDefKeys);

    List<BpmTaskUser> getAllTaskUserByTaskIdIn(List<String> listTaskId);

    List<BpmTaskUser> getAllByUserName(String userName);

    List<String> getAllUserName(String procInstId, String taskDefKey);

    List<BpmTaskUser> getAllByProcInstId(String procInstId);

    Map<String, Set<String>> getTaskDefKeyToUserMap(String procInstId);
}
