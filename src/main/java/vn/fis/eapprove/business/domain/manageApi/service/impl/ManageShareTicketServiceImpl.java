package vn.fis.eapprove.business.domain.manageApi.service.impl;

import jakarta.persistence.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.domain.assign.entity.AssignManagement;
import vn.fis.eapprove.business.domain.assign.repository.AssignRepository;
import vn.fis.eapprove.business.domain.authority.service.AuthService;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcInstManager;
import vn.fis.eapprove.business.domain.manageApi.entity.ManageShareTicket;
import vn.fis.eapprove.business.domain.manageApi.entity.ManageShareTicketDetail;
import vn.fis.eapprove.business.domain.manageApi.repository.ManageShareTicketDetailRepository;
import vn.fis.eapprove.business.domain.manageApi.repository.ManageShareTicketRepository;
import vn.fis.eapprove.business.domain.manageApi.service.ManageShareTicketService;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.repository.ServicePackageRepository;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.AccountModel;
import vn.fis.eapprove.business.model.request.CreateShareTicketRequest;
import vn.fis.eapprove.business.model.request.PermissionHistoryRequest;
import vn.fis.eapprove.business.model.request.SearchShareTicketRequest;
import vn.fis.eapprove.business.model.request.WorkFlowRequest;
import vn.fis.eapprove.business.model.response.ManageShareTicketDetailResponse;
import vn.fis.eapprove.business.model.response.ManageShareTicketResponse;
import vn.fis.eapprove.business.model.response.NameAndCodeCompanyResponse;
import vn.fis.eapprove.business.specification.ManageShareTicketSpecification;
import vn.fis.eapprove.business.tenant.manager.ActHiVarInstManager;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.tenant.manager.SproService;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.business.utils.TimeUtils;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.camunda.SproFlow;
import vn.fis.spro.common.camunda.SproFlowNode;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static vn.fis.eapprove.business.constant.Constant.LOCALFORMAT;

@Service("ManageShareTicketServiceImplV1")
@Slf4j
public class ManageShareTicketServiceImpl implements ManageShareTicketService {

    private final ManageShareTicketRepository manageShareTicketRepository;
    private final ManageShareTicketDetailRepository manageShareTicketDetailRepository;
    private final CredentialHelper credentialHelper;
    private final CustomerService customerService;
    private final AuthService authService;
    private final PermissionDataManagementRepository permissionDataManagementRepository;
    private final ServicePackageRepository servicePackageRepository;
    private final ResponseUtils responseUtils;
    private final ManageShareTicketSpecification manageShareTicketSpecification;
    private final SproService sproService;
    private final BpmProcInstManager bpmProcInstManager;
    private final AssignRepository assignRepository;

    public ManageShareTicketServiceImpl(
            ManageShareTicketRepository manageShareTicketRepository,
            ManageShareTicketDetailRepository manageShareTicketDetailRepository,
            CredentialHelper credentialHelper,
            CustomerService customerService,
            PermissionDataManagementRepository permissionDataManagementRepository,
            ServicePackageRepository servicePackageRepository,
            AuthService authService,
            ResponseUtils responseUtils,
            ManageShareTicketSpecification manageShareTicketSpecification,
            SproService sproService,
            BpmProcInstManager bpmProcInstManager, AssignRepository assignRepository
    ) {
        this.manageShareTicketRepository = manageShareTicketRepository;
        this.manageShareTicketDetailRepository = manageShareTicketDetailRepository;
        this.credentialHelper = credentialHelper;
        this.customerService = customerService;
        this.permissionDataManagementRepository = permissionDataManagementRepository;
        this.servicePackageRepository = servicePackageRepository;
        this.authService = authService;
        this.responseUtils = responseUtils;
        this.manageShareTicketSpecification = manageShareTicketSpecification;
        this.sproService = sproService;
        this.bpmProcInstManager = bpmProcInstManager;
        this.assignRepository = assignRepository;
    }

    @Override
    public boolean createUpdate(CreateShareTicketRequest request) {
        try {
            String actionUser = credentialHelper.getJWTPayload().getUsername();
            ManageShareTicket manageShareTicket = new ManageShareTicket();
            String historyDetail = "Thêm mới chia sẻ quyền xem phiếu yêu cầu";
            String actionHistory = "CREATE";
            // update
            if (!ValidationUtils.isNullOrEmpty(request.getId())) {
                manageShareTicket = manageShareTicketRepository.getManageShareTicketById(request.getId());
                manageShareTicket.setUpdatedDate(LocalDateTime.now());
                manageShareTicket.setUpdatedUser(actionUser);

                List<ManageShareTicketDetail> listDetailOld = manageShareTicketDetailRepository.findByManageShareTicketId(request.getId());
                List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(request.getId(), PermissionDataConstants.Type.MANAGE_SHARE_TICKET.code);
                // handle detail history
                historyDetail = handleHistoryDetail(request, manageShareTicket, listDetailOld, oldData);
                actionHistory = "UPDATE";

                // Xóa detail cũ
                if (!ValidationUtils.isNullOrEmpty(listDetailOld)) {
                    manageShareTicketDetailRepository.deleteAll(listDetailOld);
                }

                // Xóa data phân quyền dữ liệu cũ
                if (!ValidationUtils.isNullOrEmpty(oldData)) {
                    permissionDataManagementRepository.deleteAll(oldData);
                }
            } else { // create
                manageShareTicket.setCreatedDate(LocalDateTime.now());
                manageShareTicket.setCreatedUser(actionUser);

                List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(actionUser);
                if (!ValidationUtils.isNullOrEmpty(listCompanyCodeAndName)) {
                    NameAndCodeCompanyResponse response = listCompanyCodeAndName.get(0);
                    manageShareTicket.setCompanyCode(response.getCompanyCode());
                    manageShareTicket.setCompanyName(response.getCompanyName());
                }
            }

            manageShareTicket.setName(request.getName());
            manageShareTicket.setDescription(request.getDescription());
            manageShareTicket.setStatus(request.getStatus());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            manageShareTicket.setFromDate(LocalDate.parse(request.getFromDate(), formatter));
            if (!ValidationUtils.isNullOrEmpty(request.getToDate())) {
                manageShareTicket.setToDate(LocalDate.parse(request.getToDate(), formatter));
            } else {
                manageShareTicket.setToDate(null);
            }

            manageShareTicketRepository.save(manageShareTicket);

            List<ManageShareTicketDetail> listDetail = new ArrayList<>();
            // get list service
            if (!ValidationUtils.isNullOrEmpty(request.getListServiceId())) {
                List<Tuple> tuples = servicePackageRepository.getListIdAndNameByIds(request.getListServiceId().stream().map(Long::valueOf).collect(Collectors.toList()));
                Map<String, String> mapServiceName = tuples.stream().collect(Collectors.toMap(
                        tuple -> tuple.get("id").toString(),
                        tuple -> tuple.get("name").toString()
                ));
                getListDetail(listDetail, request.getListServiceId(), "service", manageShareTicket.getId(), mapServiceName);
            }
            // get map name from employee service
            Map<String, String> mapName = customerService.getMapNameShareTicketDetail(request.getListShareUser(), request.getListCompanyCode(), request.getListChartNodeCode(), request.getListCreatedUser(), request.getListAssignee());
            // get list share user
            if (!ValidationUtils.isNullOrEmpty(request.getListShareUser())) {
                getListDetail(listDetail, request.getListShareUser(), "shareUser", manageShareTicket.getId(), mapName);
            }
            // get list company code
            if (!ValidationUtils.isNullOrEmpty(request.getListCompanyCode())) {
                getListDetail(listDetail, request.getListCompanyCode(), "companyCode", manageShareTicket.getId(), mapName);
            }
            // get list chart node code
            if (!ValidationUtils.isNullOrEmpty(request.getListChartNodeCode())) {
                getListDetail(listDetail, request.getListChartNodeCode(), "chartNodeCode", manageShareTicket.getId(), mapName);
            }
            // get list created user
            if (!ValidationUtils.isNullOrEmpty(request.getListCreatedUser())) {
                getListDetail(listDetail, request.getListCreatedUser(), "createdUser", manageShareTicket.getId(), mapName);
            }
            // get list assignee
            if (!ValidationUtils.isNullOrEmpty(request.getListAssignee())) {
                getListDetail(listDetail, request.getListAssignee(), "assignee", manageShareTicket.getId(), mapName);
            }

            // save all detail
            manageShareTicketDetailRepository.saveAll(listDetail);

            // Lưu phân quyền dữ liệu
            if (!ValidationUtils.isNullOrEmpty(request.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : request.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(manageShareTicket.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.MANAGE_SHARE_TICKET.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(actionUser);
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }
                permissionDataManagementRepository.saveAll(permissionDataManagements);
            }

            // Lưu lịch sử chỉnh sửa
            if (!ValidationUtils.isNullOrEmpty(historyDetail)) {
                PermissionHistoryRequest permissionHistoryRequest = new PermissionHistoryRequest();
                permissionHistoryRequest.setAction(actionHistory);
                permissionHistoryRequest.setDetail(historyDetail);
                permissionHistoryRequest.setObjectCode(manageShareTicket.getId());
                permissionHistoryRequest.setObjectType(PermissionDataConstants.Type.MANAGE_SHARE_TICKET.code);
                customerService.savePermissionHistory(permissionHistoryRequest);
            }

            return true;
        } catch (Exception e) {
            log.error("Create/ update manage share ticket fail: {}", e.getMessage());
            return false;
        }
    }

    private String handleHistoryDetail(CreateShareTicketRequest request, ManageShareTicket old, List<ManageShareTicketDetail> listDetailOld, List<PermissionDataManagement> listPerOld) {
        StringBuilder builder = new StringBuilder();
        if (!request.getName().equals(old.getName())) {
            builder.append("Chỉnh sửa Tên quyền xem phiếu yêu cầu từ ").append(old.getName()).append(" thành ").append(request.getName()).append(System.lineSeparator());
        }
        if (!request.getDescription().equals(old.getDescription())) {
            builder.append("Chỉnh sửa mô tả từ ").append(old.getDescription()).append(" thành ").append(request.getDescription()).append(System.lineSeparator());
        }
        if (!request.getStatus().equals(old.getStatus())) {
            builder.append("Thay đổi trạng thái từ ")
                    .append(old.getStatus().equals("active") ? "Hoạt động" : "Vô hiệu hoá")
                    .append(" thành ")
                    .append(old.getStatus().equals("active") ? "Vô hiệu hoá" : "Hoạt động")
                    .append(System.lineSeparator());
        }
        String oldFromDate = old.getFromDate() == null ? "" : TimeUtils.localDateToString(old.getFromDate(), "dd/MM/yyyy");
        if (request.getFromDate() != null && !request.getFromDate().equals(oldFromDate)) {
            builder.append("Chỉnh sửa Thời gian hiệu lực từ ngày ")
                    .append(oldFromDate)
                    .append(" thành ")
                    .append(request.getFromDate())
                    .append(System.lineSeparator());
        }
        String oldToDate = old.getToDate() == null ? "" : TimeUtils.localDateToString(old.getToDate(), "dd/MM/yyyy");
        if (request.getToDate() != null && !request.getToDate().equals(oldToDate)) {
            builder.append("Chỉnh sửa Thời gian hiệu lực đến ngày ")
                    .append(oldToDate)
                    .append(" thành ")
                    .append(request.getToDate())
                    .append(System.lineSeparator());
        } else if (old.getToDate() != null && request.getToDate() == null) {
            builder.append("Chỉnh sửa Thời gian hiệu lực đến ngày ")
                    .append(oldToDate)
                    .append(" thành ")
                    .append(System.lineSeparator());
        }
        Set<String> lstOldPerId = listPerOld.stream().map(PermissionDataManagement::getCompanyCode).collect(Collectors.toSet());
        Set<String> lstNewPerId = new HashSet<>(request.getApplyFor());
        if (!lstOldPerId.equals(lstNewPerId)) {
            builder.append("Chỉnh sửa Cho phép IT ở các công ty chỉnh sửa từ ")
                    .append(String.join(",", lstOldPerId))
                    .append(" thành ")
                    .append(String.join(",", lstNewPerId))
                    .append(System.lineSeparator());
        }
        Set<String> lstOldServiceId = listDetailOld.stream()
                .filter(e -> e.getType().equals("service"))
                .map(ManageShareTicketDetail::getValue)
                .collect(Collectors.toSet());
        Set<String> lstNewServiceId = new HashSet<>(request.getListServiceId());
        if (!lstOldServiceId.equals(lstNewServiceId)) {
            List<String> lstOldServiceName = servicePackageRepository.getListNameByIds(lstOldServiceId.stream().map(Long::valueOf).toList());
            List<String> lstNewServiceName = servicePackageRepository.getListNameByIds(lstNewServiceId.stream().map(Long::valueOf).toList());
            builder.append("Chỉnh sửa Chọn dịch vụ cần chia sẻ quyền xem phiếu từ ")
                    .append(String.join(",", lstOldServiceName))
                    .append(" thành ")
                    .append(String.join(",", lstNewServiceName))
                    .append(System.lineSeparator());
        }
        Set<String> lstOldShareUser = listDetailOld.stream()
                .filter(e -> e.getType().equals("shareUser"))
                .map(ManageShareTicketDetail::getValue)
                .collect(Collectors.toSet());
        Set<String> lstNewShareUser = new HashSet<>(request.getListShareUser());
        if (!lstOldShareUser.equals(lstNewShareUser)) {
            builder.append("Chỉnh sửa Chọn người được chia sẻ quyền xem phiếu từ ")
                    .append(String.join(",", lstOldShareUser))
                    .append(" thành ")
                    .append(String.join(",", lstNewShareUser))
                    .append(System.lineSeparator());
        }
        Set<String> lstOldAssignee = listDetailOld.stream()
                .filter(e -> e.getType().equals("assignee"))
                .map(ManageShareTicketDetail::getValue)
                .collect(Collectors.toSet());
        Set<String> lstNewAssignee = new HashSet<>(request.getListAssignee());
        if (!lstOldAssignee.equals(lstNewAssignee)) {
            builder.append("Chỉnh sửa Chọn người duyệt từ ")
                    .append(String.join(",", lstOldAssignee))
                    .append(" thành ")
                    .append(String.join(",", lstNewAssignee))
                    .append(System.lineSeparator());
        }
        Set<String> lstOldChartNodeCode = listDetailOld.stream()
                .filter(e -> e.getType().equals("chartNodeCode"))
                .map(ManageShareTicketDetail::getValue)
                .collect(Collectors.toSet());
        Set<String> lstNewChartNodeCode = new HashSet<>(request.getListChartNodeCode());
        if (!lstOldChartNodeCode.equals(lstNewChartNodeCode)) {
            builder.append("Chỉnh sửa Chọn phòng ban người đệ trình từ ")
                    .append(String.join(",", lstOldChartNodeCode))
                    .append(" thành ")
                    .append(String.join(",", lstNewChartNodeCode))
                    .append(System.lineSeparator());
        }
        Set<String> lstOldCompanyCode = listDetailOld.stream()
                .filter(e -> e.getType().equals("companyCode"))
                .map(ManageShareTicketDetail::getValue)
                .collect(Collectors.toSet());
        Set<String> lstNewCompanyCode = new HashSet<>(request.getListCompanyCode());
        if (!lstOldCompanyCode.equals(lstNewCompanyCode)) {
            builder.append("Chỉnh sửa Chọn công ty người đệ trình từ ")
                    .append(String.join(",", lstOldCompanyCode))
                    .append(" thành ")
                    .append(String.join(",", lstNewCompanyCode))
                    .append(System.lineSeparator());
        }
        Set<String> lstOldCreatedUser = listDetailOld.stream()
                .filter(e -> e.getType().equals("createdUser"))
                .map(ManageShareTicketDetail::getValue)
                .collect(Collectors.toSet());
        Set<String> lstNewCreatedUser = new HashSet<>(request.getListCreatedUser());
        if (!lstOldCreatedUser.equals(lstNewCreatedUser)) {
            builder.append("Chỉnh sửa Chọn Người đệ trình từ ")
                    .append(String.join(",", lstOldCreatedUser))
                    .append(" thành ")
                    .append(String.join(",", lstNewCreatedUser))
                    .append(System.lineSeparator());
        }

        return builder.toString();
    }

    @Override
    public String validate(CreateShareTicketRequest request) {
        String errorMessage = "";
        // valid name với quyền active
        ManageShareTicket data = manageShareTicketRepository.getManageShareTicketByNameAndStatus(request.getName(), "active");
        if (data != null && !data.getId().equals(request.getId())) {
            errorMessage = "Tên quyền đã tồn tại. Vui lòng kiểm tra và thử lại.";
            return errorMessage;
        }

        // valid service + date + shareUser
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        LocalDate fromDate = LocalDate.parse(request.getFromDate(), formatter);
        LocalDate toDate = null;
        if (!ValidationUtils.isNullOrEmpty(request.getToDate())) {
            toDate = LocalDate.parse(request.getToDate(), formatter);
        }
        List<ManageShareTicket> listValid = manageShareTicketRepository.validateServiceAndDateAndShareUser(request.getListServiceId(), fromDate, toDate, request.getId(), request.getListShareUser());
        if (!ValidationUtils.isNullOrEmpty(listValid)) {
            List<ManageShareTicketDetail> listDetailServiceValid = manageShareTicketDetailRepository.findManageShareTicketDetailsByManageShareTicketIdAndType(listValid.get(0).getId(), "service");
            List<String> listServiceValid = listDetailServiceValid.stream().map(ManageShareTicketDetail::getValue).collect(Collectors.toList());
            listServiceValid.addAll(request.getListServiceId());
            Set<String> setServiceValid = new HashSet<>();
            List<Long> lstServiceDuplicate = listServiceValid.stream()
                    .filter(e -> !setServiceValid.add(e))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            List<ServicePackage> lstService = servicePackageRepository.getAllByIdIn(lstServiceDuplicate);
            List<String> lstServiceName = lstService.stream().map(ServicePackage::getServiceName).collect(Collectors.toList());

            List<ManageShareTicketDetail> listDetailShareUserValid = manageShareTicketDetailRepository.findManageShareTicketDetailsByManageShareTicketIdAndType(listValid.get(0).getId(), "shareUser");
            List<String> listShareUserValid = listDetailShareUserValid.stream().map(ManageShareTicketDetail::getValue).collect(Collectors.toList());
            listShareUserValid.addAll(request.getListShareUser());
            Set<String> setUserValid = new HashSet<>();
            List<String> lstUserDuplicate = listShareUserValid.stream().filter(e -> !setUserValid.add(e)).collect(Collectors.toList());
            List<AccountModel> accountModels = customerService.getAccountByUsernames(lstUserDuplicate);
            List<String> lstUsername = accountModels.stream().map(e -> e.getUsername() + " - " + e.getLastname() + " " + e.getFirstname()).collect(Collectors.toList());

            errorMessage = String.join(",", lstUsername) + " đã được chia sẻ quyền xem phiếu yêu cầu của " + String.join(",", lstServiceName) + " trong nhóm " + listValid.get(0).getName() + " trùng ngày hiệu lực " + request.getFromDate();
        }
        return errorMessage;
    }

    @Override
    public String validateUpdateStatus(CreateShareTicketRequest request) {
        String errorMessage = "";
        ManageShareTicket manageShareTicket = manageShareTicketRepository.getManageShareTicketById(request.getId());
        if (manageShareTicket == null) {
            errorMessage = "Quyền chia sẻ phiéu không tồn tại.";
            return errorMessage;
        }
        // valid name
        ManageShareTicket data = manageShareTicketRepository.getManageShareTicketByName(manageShareTicket.getName());
        if (data != null && !data.getId().equals(manageShareTicket.getId())) {
            errorMessage = "Tên quyền đã tồn tại. Vui lòng kiểm tra và thử lại.";
            return errorMessage;
        }

        // valid service + date + shareUser
        LocalDate fromDate = manageShareTicket.getFromDate();
        LocalDate toDate = manageShareTicket.getToDate();
        List<ManageShareTicketDetail> lstDetailService = manageShareTicketDetailRepository.findManageShareTicketDetailsByManageShareTicketIdAndType(manageShareTicket.getId(), "service");
        List<String> listService = lstDetailService.stream().map(ManageShareTicketDetail::getValue).collect(Collectors.toList());
        List<ManageShareTicketDetail> lstDetailShareUser = manageShareTicketDetailRepository.findManageShareTicketDetailsByManageShareTicketIdAndType(manageShareTicket.getId(), "shareUser");
        List<String> listShareUser = lstDetailShareUser.stream().map(ManageShareTicketDetail::getValue).collect(Collectors.toList());

        List<ManageShareTicket> listValid = manageShareTicketRepository.validateServiceAndDateAndShareUser(listService, fromDate, toDate, manageShareTicket.getId(), listShareUser);
        if (!ValidationUtils.isNullOrEmpty(listValid)) {
            List<ManageShareTicketDetail> listDetailValid = manageShareTicketDetailRepository.findManageShareTicketDetailsByManageShareTicketIdAndType(listValid.get(0).getId(), "service");
            List<String> listServiceValid = listDetailValid.stream().map(ManageShareTicketDetail::getValue).collect(Collectors.toList());
            listServiceValid.addAll(listService);
            Set<String> setServiceValid = new HashSet<>();
            List<Long> lstServiceDuplicate = listServiceValid.stream()
                    .filter(e -> !setServiceValid.add(e))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            List<ServicePackage> lstService = servicePackageRepository.getAllByIdIn(lstServiceDuplicate);
            List<String> lstServiceName = lstService.stream().map(ServicePackage::getServiceName).collect(Collectors.toList());

            List<ManageShareTicketDetail> listDetailShareUserValid = manageShareTicketDetailRepository.findManageShareTicketDetailsByManageShareTicketIdAndType(listValid.get(0).getId(), "shareUser");
            List<String> listShareUserValid = listDetailShareUserValid.stream().map(ManageShareTicketDetail::getValue).collect(Collectors.toList());
            listShareUserValid.addAll(listShareUser);
            Set<String> setUserValid = new HashSet<>();
            List<String> lstUserDuplicate = listShareUserValid.stream().filter(e -> !setUserValid.add(e)).collect(Collectors.toList());
            List<AccountModel> accountModels = customerService.getAccountByUsernames(lstUserDuplicate);
            List<String> lstUsername = accountModels.stream().map(e -> e.getUsername() + " - " + e.getLastname() + " " + e.getFirstname()).collect(Collectors.toList());

            errorMessage = String.join(",", lstUsername) + " đã được chia sẻ quyền xem phiếu yêu cầu của " + String.join(",", lstServiceName) + " trong nhóm " + listValid.get(0).getName() + " trùng ngày hiệu lực " + manageShareTicket.getFromDate();
        }
        return errorMessage;
    }

    private void getListDetail(List<ManageShareTicketDetail> listDetail,
                               List<String> listValue, String type,
                               Long manageShareTicketId,
                               Map<String, String> mapName
    ) {
        for (String value : listValue) {
            ManageShareTicketDetail detail = new ManageShareTicketDetail();
            detail.setManageShareTicketId(manageShareTicketId);
            detail.setValue(value);
            detail.setType(type);
            if (mapName != null) {
                detail.setName(mapName.get(value));
            }
            listDetail.add(detail);
        }
    }

    public boolean updateStatus(Long id, String status) {
        String username = credentialHelper.getJWTPayload().getUsername();
        ManageShareTicket manageShareTicket = manageShareTicketRepository.getManageShareTicketById(id);
        if (manageShareTicket == null) {
            return false;
        }

        // Lưu lịch sử chỉnh sửa
        String detail = "Thay đổi trạng thái từ " +
                (manageShareTicket.getStatus().equals("active") ? "Hoạt động" : "Vô hiệu hoá") +
                " thành " +
                (manageShareTicket.getStatus().equals("active") ? "Vô hiệu hoá" : "Hoạt động");
        PermissionHistoryRequest permissionHistoryRequest = new PermissionHistoryRequest();
        permissionHistoryRequest.setAction("UPDATE");
        permissionHistoryRequest.setDetail(detail);
        permissionHistoryRequest.setObjectCode(manageShareTicket.getId());
        permissionHistoryRequest.setObjectType(PermissionDataConstants.Type.MANAGE_SHARE_TICKET.code);
        customerService.savePermissionHistory(permissionHistoryRequest);

        manageShareTicket.setUpdatedUser(username);
        manageShareTicket.setUpdatedDate(LocalDateTime.now());
        manageShareTicket.setStatus(status);
        manageShareTicketRepository.save(manageShareTicket);
        return true;
    }

    @Override
    public PageDto search(SearchShareTicketRequest request) {

        int pageNum = request.getPage() - 1;
        Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());

        String username = credentialHelper.getJWTPayload().getUsername();
        // Lấy list companyCode cấu hình QL vai trò người dùng
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
        request.setListCompanyCode(lstCompanyCode);

        Page<ManageShareTicket> page = manageShareTicketRepository.findAll(
                manageShareTicketSpecification.filter(request),
                PageRequest.of(pageNum, request.getLimit(), sort)
        );

        List<ManageShareTicketResponse> lstResponse = new ArrayList<>();
        for (ManageShareTicket manageShareTicket : page.getContent()) {
            ManageShareTicketResponse response = ManageShareTicketResponse.builder()
                    .id(manageShareTicket.getId())
                    .name(manageShareTicket.getName())
                    .description(manageShareTicket.getDescription())
                    .fromDate(TimeUtils.localDateToString(manageShareTicket.getFromDate(), "dd/MM/yyyy"))
                    .toDate(manageShareTicket.getToDate() == null ? "" : TimeUtils.localDateToString(manageShareTicket.getToDate(), "dd/MM/yyyy"))
                    .createdDate(TimeUtils.localDateTimeToString(manageShareTicket.getCreatedDate(), LOCALFORMAT))
                    .createdUser(manageShareTicket.getCreatedUser())
                    .updatedDate(manageShareTicket.getUpdatedDate() == null ? "" : TimeUtils.localDateTimeToString(manageShareTicket.getUpdatedDate(), LOCALFORMAT))
                    .updatedUser(manageShareTicket.getUpdatedUser())
                    .companyCode(manageShareTicket.getCompanyCode())
                    .companyName(manageShareTicket.getCompanyName())
                    .status(manageShareTicket.getStatus())
                    .build();
            lstResponse.add(response);
        }

        return PageDto.builder()
                .content(lstResponse)
                .number(page.getNumber() + 1)
                .numberOfElements(page.getNumberOfElements())
                .page(page.getNumber() + 1)
                .limit(page.getSize())
                .totalPages(page.getTotalPages())
                .totalElements(page.getTotalElements())
                .sortBy(request.getSortBy())
                .sortType(request.getSortType())
                .build();
    }

    @Override
    public List<ManageShareTicketResponse> searchFilter(SearchShareTicketRequest request) {

        String username = credentialHelper.getJWTPayload().getUsername();
        // Lấy list companyCode cấu hình QL vai trò người dùng
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
        request.setListCompanyCode(lstCompanyCode);

        List<ManageShareTicket> page = manageShareTicketRepository.findAll(manageShareTicketSpecification.filter(request));

        List<ManageShareTicketResponse> lstResponse = new ArrayList<>();
        for (ManageShareTicket manageShareTicket : page) {
            ManageShareTicketResponse response = ManageShareTicketResponse.builder()
                    .id(manageShareTicket.getId())
                    .name(manageShareTicket.getName())
                    .description(manageShareTicket.getDescription())
                    .fromDate(TimeUtils.localDateToString(manageShareTicket.getFromDate(), "dd/MM/yyyy"))
                    .toDate(manageShareTicket.getToDate() == null ? "" : TimeUtils.localDateToString(manageShareTicket.getToDate(), "dd/MM/yyyy"))
                    .createdDate(TimeUtils.localDateTimeToString(manageShareTicket.getCreatedDate(), LOCALFORMAT))
                    .createdUser(manageShareTicket.getCreatedUser())
                    .updatedDate(manageShareTicket.getUpdatedDate() == null ? "" : TimeUtils.localDateTimeToString(manageShareTicket.getUpdatedDate(), LOCALFORMAT))
                    .updatedUser(manageShareTicket.getUpdatedUser())
                    .companyCode(manageShareTicket.getCompanyCode())
                    .companyName(manageShareTicket.getCompanyName())
                    .status(manageShareTicket.getStatus())
                    .build();
            lstResponse.add(response);
        }

        return lstResponse;
    }

    @Override
    public ManageShareTicketDetailResponse getDetail(Long id) {
        ManageShareTicket manageShareTicket = manageShareTicketRepository.getManageShareTicketById(id);
        if (manageShareTicket == null) {
            return null;
        }
        List<Long> listServiceId = new ArrayList<>();
        List<String> listShareUser = new ArrayList<>();
        List<String> listCompanyCode = new ArrayList<>();
        List<String> listChartNodeCode = new ArrayList<>();
        List<String> listCreatedUser = new ArrayList<>();
        List<String> listAssignee = new ArrayList<>();
        List<ManageShareTicketDetail> listDetail = manageShareTicketDetailRepository.findManageShareTicketDetailsByManageShareTicketIdAndType(id, "service");
        if (!ValidationUtils.isNullOrEmpty(listDetail)) {
            listServiceId = listDetail.stream()
                    .map(ManageShareTicketDetail::getValue)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        }
        listDetail = manageShareTicketDetailRepository.findManageShareTicketDetailsByManageShareTicketIdAndType(id, "shareUser");
        if (!ValidationUtils.isNullOrEmpty(listDetail)) {
            listShareUser = listDetail.stream()
                    .map(ManageShareTicketDetail::getValue)
                    .collect(Collectors.toList());
        }
        listDetail = manageShareTicketDetailRepository.findManageShareTicketDetailsByManageShareTicketIdAndType(id, "companyCode");
        if (!ValidationUtils.isNullOrEmpty(listDetail)) {
            listCompanyCode = listDetail.stream()
                    .map(ManageShareTicketDetail::getValue)
                    .collect(Collectors.toList());
        }
        listDetail = manageShareTicketDetailRepository.findManageShareTicketDetailsByManageShareTicketIdAndType(id, "chartNodeCode");
        if (!ValidationUtils.isNullOrEmpty(listDetail)) {
            listChartNodeCode = listDetail.stream()
                    .map(ManageShareTicketDetail::getValue)
                    .collect(Collectors.toList());
        }
        listDetail = manageShareTicketDetailRepository.findManageShareTicketDetailsByManageShareTicketIdAndType(id, "createdUser");
        if (!ValidationUtils.isNullOrEmpty(listDetail)) {
            listCreatedUser = listDetail.stream()
                    .map(ManageShareTicketDetail::getValue)
                    .collect(Collectors.toList());
        }
        listDetail = manageShareTicketDetailRepository.findManageShareTicketDetailsByManageShareTicketIdAndType(id, "assignee");
        if (!ValidationUtils.isNullOrEmpty(listDetail)) {
            listAssignee = listDetail.stream()
                    .map(ManageShareTicketDetail::getValue)
                    .collect(Collectors.toList());
        }

        List<PermissionDataManagement> listPermission = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(manageShareTicket.getId(), PermissionDataConstants.Type.MANAGE_SHARE_TICKET.code);
        List<String> applyFor = listPermission.stream().map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());

        return ManageShareTicketDetailResponse.builder()
                .id(manageShareTicket.getId())
                .name(manageShareTicket.getName())
                .description(manageShareTicket.getDescription())
                .listServiceId(listServiceId)
                .listShareUser(listShareUser)
                .listCompanyCode(listCompanyCode)
                .listChartNodeCode(listChartNodeCode)
                .listCreatedUser(listCreatedUser)
                .listAssignee(listAssignee)
                .status(manageShareTicket.getStatus())
                .fromDate(TimeUtils.localDateToString(manageShareTicket.getFromDate(), "dd/MM/yyyy"))
                .toDate(manageShareTicket.getToDate() == null ? "" : TimeUtils.localDateToString(manageShareTicket.getToDate(), "dd/MM/yyyy"))
                .applyFor(applyFor)
                .build();
    }

    @Override
    public void processShareTicket(String procInstId, String procDefId, String companyCode, String createdUser, String chartNodeCode, String serviceId, LocalDate createdDate) {
        // split origin companyCode
        String[] splitCompanyCode = StringUtils.split(companyCode, "_");
        if (splitCompanyCode.length > 0) {
            companyCode = splitCompanyCode[0];
        }
        // get by required field
        List<ManageShareTicket> lstManageShareTicket = manageShareTicketRepository.getByServiceIdAndCompanyCode(serviceId, createdDate);
        if (!ValidationUtils.isNullOrEmpty(lstManageShareTicket)) {
            for (ManageShareTicket manageShareTicket : lstManageShareTicket) {
                ManageShareTicketDetailResponse detail = getDetail(manageShareTicket.getId());
                // check optional field
                Boolean isShareTicket = checkAllowShareTicket(procInstId, procDefId, createdUser, chartNodeCode, companyCode, detail, serviceId);

                if (isShareTicket) {
                    Map<String, Object> data = new HashMap<>();
                    data.put("procInstId", procInstId);
                    data.put("type", "SHARED");
                    data.put("listUser", detail.getListShareUser());
                    bpmProcInstManager.createShared(data, "Hệ thống");
                }
            }
        }
    }

    private Boolean checkAllowShareTicket(String procInstId, String procDefId, String createdUser, String chartNodeCode,
                                          String companyCode, ManageShareTicketDetailResponse detail, String serviceId) {
        // get flow info with assignee from camunda
        WorkFlowRequest workFlowRequest = new WorkFlowRequest();
        workFlowRequest.setProcDefId(procDefId);
        workFlowRequest.setProcInstId(procInstId);
        List<SproFlow> workFlow = sproService.getWorkFlow(workFlowRequest);
        List<SproFlowNode> nodes = workFlow.stream()
                .flatMap(e -> e.getNodes().stream().filter(node -> node.getType().equals("userTask")))
                .toList();
        // list assignee camunda
        List<String> lstAssignee = nodes.stream()
                .filter(json -> json.getAssignee() != null)
                .map(json -> json.getAssignee().replaceAll("\\[\"|\"\\]|\\\\|\"", ""))
                .map(json -> Arrays.stream(json.split(",")).map(String::trim))
                .flatMap(Stream::distinct)
                .distinct()
                .collect(Collectors.toList());

        // get list auto-assign - UQ tờ trình
        List<AssignManagement> lstAssignManagement = assignRepository.getByListAssignUserAndServiceId(lstAssignee, Long.valueOf(serviceId));
        if (!ValidationUtils.isNullOrEmpty(lstAssignManagement)) {
            List<String> lstAssigned = lstAssignManagement.stream().map(AssignManagement::getAssignedUser).toList();
            lstAssignee.addAll(lstAssigned);
        }

        // có cấu hình companyCode
        if (!ValidationUtils.isNullOrEmpty(detail.getListCompanyCode()) && !detail.getListCompanyCode().contains(companyCode)) {
            return false;
        }
        // có cấu hình createdUser
        if (!ValidationUtils.isNullOrEmpty(detail.getListCreatedUser()) && !detail.getListCreatedUser().contains(createdUser)) {
            return false;
        }
        // có cấu hình chartNodeCode
        if (!ValidationUtils.isNullOrEmpty(detail.getListChartNodeCode()) && !detail.getListChartNodeCode().contains(chartNodeCode)) {
            return false;
        }
        // có cấu hình assignee
        if (!ValidationUtils.isNullOrEmpty(detail.getListAssignee()) && !ValidationUtils.isNullOrEmpty(lstAssignee)) {
            lstAssignee.addAll(detail.getListAssignee());
            Set<String> assigneeSet = new HashSet<>();
            List<String> duplicateLevelCode = lstAssignee.stream()
                    .filter(assignee -> !assigneeSet.add(assignee))
                    .toList();
            return !ValidationUtils.isNullOrEmpty(duplicateLevelCode);
        }
        return true;
    }
}
