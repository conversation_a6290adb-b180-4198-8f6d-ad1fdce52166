package vn.fis.eapprove.business.domain.dashboard.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "dashboard_config")
public class DashboardConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "username")
    private String username;

    @Column(name = "name")
    private String name;

    @Column(name = "type")
    private String type;

    @Column(name = "filter")
    private String filter;

    @Column(name = "is_show")
    private Boolean isShow;

    @Column(name = "is_clone")
    private Boolean isClone;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
}
