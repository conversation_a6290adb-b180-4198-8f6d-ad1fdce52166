package vn.fis.eapprove.business.domain.bpm.service;

import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.repository.BpmAdditionalRequestRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcInstRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTaskRepository;
import vn.fis.eapprove.business.dto.BpmAdditionalRequestDto;
import vn.fis.eapprove.business.mapper.BpmAdditionalRequestMapper;
import vn.fis.eapprove.business.model.ActionApiContext;
import vn.fis.eapprove.business.model.NotificationUser;
import vn.fis.eapprove.business.model.request.HistoryDto;
import vn.fis.eapprove.business.model.request.VariableValueDto;
import vn.fis.eapprove.business.model.response.UserTitleResponse;
import vn.fis.eapprove.business.producer.ReportProducer;
import vn.fis.eapprove.business.tenant.manager.ActionApiService;
import vn.fis.eapprove.business.tenant.manager.CamundaEngineService;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.constants.ProcInstConstants;
import vn.fis.spro.common.constants.TaskActionConstants;
import vn.fis.spro.common.constants.TaskConstants;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("BpmAdditionalRequestManagerV1")
@Slf4j
public class BpmAdditionalRequestManager {

    @Autowired
    private BpmAdditionalRequestRepository bpmAdditionalRequestRepository;

    @Autowired
    private BpmProcInstRepository bpmProcInstRepository;

    @Autowired
    private BpmHistoryManager bpmHistoryManager;

    @Autowired
    private BpmTaskRepository bpmTaskRepository;

    @Autowired
    private BpmAdditionalRequestMapper bpmAdditionalRequestMapper;

    @Autowired
    private ActionApiService actionApiService;

    @Autowired
    private CamundaEngineService camundaEngineService;

    @Autowired
    private Common common;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private ReportProducer reportProducer;

    @Autowired
    private CredentialHelper credentialHelper;

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Value("${spring.kafka.consumer.topic.insert-report-by-group}")
    private String insertReportByGroupTopic;
    @Value("${spring.kafka.consumer.topic.notification-user}")
    private String notificationUser;

    public ResponseEntity<?> saveBpmAdditionalRequest(BpmAdditionalRequestDto dto) {
        try {
            List<String> checkStatus = Arrays.asList(TaskConstants.Status.COMPLETED.code, ProcInstConstants.Status.COMPLETED.code, ProcInstConstants.Status.CLOSED.code, ProcInstConstants.Status.CANCEL.code);
            BpmTask bpmTask = bpmTaskRepository.getBpmTaskByTaskId(dto.getTaskId());

            // check task completed
            if (bpmTask != null && checkStatus.contains(bpmTask.getTaskStatus())) {
                return ResponseHelper.invalid(common.getMessage("message.task.additional-request.completed.not-allow", new Object[]{bpmTask.getTaskName()}));
            }

            BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(dto.getProcInstId());
            if (bpmProcInst != null & checkStatus.contains(bpmProcInst.getTicketStatus())) {
                return ResponseHelper.invalid(common.getMessage("message.task.additional-request.completed.fail", new Object[]{bpmTask.getTaskName()}));
            }

            /* BEGIN handle call action api on beginning */
            Map<String, VariableValueDto> variables = actionApiService.getTicketVariables(dto.getProcInstId());
            ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(
                    TaskActionConstants.Action.ADDITIONAL_REQUEST.code,
                    bpmProcInst != null ? bpmProcInst.getTicketProcDefId() : null,
                    null,
                    dto.getProcInstId(),
                    actionApiService.createVariablesMap(dto, bpmProcInst, variables));
            /* END handle call action api on beginning */

            bpmAdditionalRequestRepository.save(bpmAdditionalRequestMapper.toEntity(dto));
            // update ticket
            if (bpmProcInst != null) {
                bpmProcInst.setTicketStatus(ProcInstConstants.Status.ADDITIONAL_REQUEST.code);
                bpmProcInstRepository.save(bpmProcInst);
            }
            reportProducer.sendKafka(bpmProcInst.getTicketId(), insertReportByGroupTopic);

            // update current task
//            List<BpmTask> bpmTasks = bpmTaskRepository.getBpmTaskByTaskProcInstId(dto.getProcInstId());
//            if (!CollectionUtils.isEmpty(bpmTasks)) {
//                for (BpmTask task : bpmTasks) {
//                    if (task.getTaskStatus().equals(TaskConstants.Status.PROCESSING.code) ||
//                            task.getTaskStatus().equals(TaskConstants.Status.ACTIVE.code)) {
//                        task.setTaskStatus(TaskConstants.Status.WAIT.code);
//                    }
//                    bpmTaskRepository.saveAll(bpmTasks);
//                }
//            }

            saveAdditionalRequestHistory(bpmProcInst, bpmTask, dto);
//            saveAdditionalHistory(bpmProcInst, bpmTask, dto);
            bpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(dto.getProcInstId());

            BpmnModelInstance modelInstance = camundaEngineService.getModelInstance(bpmProcInst.getTicketProcDefId());
            if (modelInstance != null) {
                // get start event
//                StartEvent startEvent = CamundaUtils.getStartEvent(modelInstance);
//                String startKey = startEvent != null ? startEvent.getId() : null;
                String startKey = bpmProcInst.getTicketStartActId();
                if (startKey != null) {
                    Map<String, VariableValueDto> variablesNotifi = new HashMap<>();
                    VariableValueDto variableDto = new VariableValueDto();
                    variableDto.setType("String");
                    variableDto.setValue(dto.getContentRequest());
                    variablesNotifi.put("txt_LyDoYeuCauBoSung", variableDto);
                    //Thông báo cho user liên quan
                    NotificationUser request = new NotificationUser();
                    request.setBpmProcdefId(null);
                    request.setNextTaskDefKey(startKey);
                    request.setVariables(variablesNotifi);
                    request.setTicketId(bpmProcInst.getTicketId());
                    request.setIsGetOldVariable(true);
                    request.setLstCustomerEmails(null);
                    request.setActionCode(ProcInstConstants.Notifications.ADDITIONAL_REQUEST.code);
                    request.setEmailExe(credentialHelper.getJWTPayload().getUsername());
                    kafkaTemplate.send(notificationUser, request);
//                    bpmProcdefNotificationService.addNotificationsByConfig(null, startKey, variablesNotifi,
//                            bpmProcInst.getTicketId(), true, null, ProcInstConstants.Notifications.ADDITIONAL_REQUEST.code);
                }
            }

            /* BEGIN handle call action api at the end */
            actionApiService.endHandleActionApi(actionApiContext, actionApiService.createVariablesMap(dto, bpmProcInst, variables));
            /* END handle call action api at the end */
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return ResponseHelper.ok();
    }

    private void saveAdditionalRequestHistory(BpmProcInst bpmProcInst, BpmTask bpmTask, BpmAdditionalRequestDto dto) {
        //update history
        HistoryDto historyDto = new HistoryDto();
        historyDto.setTicketId(bpmProcInst != null ? bpmProcInst.getTicketId() : null);
        historyDto.setFromTask(bpmTask != null ? bpmTask.getTaskId() : null);
        historyDto.setFromTaskKey(bpmTask != null ? bpmTask.getTaskDefKey() : null);
        historyDto.setToTask("startTicket");
        historyDto.setAction(TaskConstants.HistoryAction.ADDITIONAL.code);
        historyDto.setProcInstId(bpmTask != null ? bpmTask.getTaskProcInstId() : null);
        historyDto.setActionUser(dto.getCreatedUser());
        historyDto.setTaskDefKey(bpmTask != null ? bpmTask.getTaskDefKey() : null);
        historyDto.setTaskInstId(bpmTask != null ? bpmTask.getTaskId() : null);
        historyDto.setNote(dto.getContentRequest());
        historyDto.setReceivedTime(bpmTask.getTaskCreatedTime());

        // get action_user_info
        List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(dto.getCreatedUser());
        if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
            Map<String, Object> actionUserInfo = new HashMap<>();
            String userTitle = lstUserTitle.stream()
                    .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                    .map(title -> {
                        String strTitle = StringUtil.nvl(title.getTitle(), "");
                        int concurrently = title.getConcurrently();
                        return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                    })
                    .collect(Collectors.joining(" "));
            actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
            actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
            historyDto.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
        }
        bpmHistoryManager.saveHistory(historyDto);
    }

    private void saveAdditionalHistory(BpmProcInst bpmProcInst, BpmTask bpmTask, BpmAdditionalRequestDto dto) {
        //update history
        HistoryDto historyDto = new HistoryDto();
        historyDto.setTicketId(bpmProcInst != null ? bpmProcInst.getTicketId() : null);
        historyDto.setFromTask("startTicket");
        historyDto.setToTask(bpmTask != null ? bpmTask.getTaskId() : null);
        historyDto.setToTaskKey(bpmTask != null ? bpmTask.getTaskDefKey() : null);
        historyDto.setAction(TaskConstants.HistoryAction.ADDITIONAL_REQUEST.code);
        historyDto.setProcInstId(bpmProcInst != null ? bpmProcInst.getTicketProcInstId() : null);
        historyDto.setActionUser(dto.getCreatedUser());
        historyDto.setNote(dto.getContentRequest());
        bpmHistoryManager.saveHistory(historyDto);
    }
}
