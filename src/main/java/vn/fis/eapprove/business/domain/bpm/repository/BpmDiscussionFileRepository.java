package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vn.fis.eapprove.business.domain.bpm.entity.BpmDiscussionFile;


import java.util.List;
import java.util.Map;

public interface BpmDiscussionFileRepository extends JpaRepository<BpmDiscussionFile, Long> {

    List<BpmDiscussionFile> getBpmDiscussionFileByDiscussionIdIn(List<Long> discussionId);

    List<BpmDiscussionFile> getBpmDiscussionFileByDiscussionId(Long discussionId);

    @Query("Select a from BpmDiscussionFile a where a.discussionId = :discussionId")
    List<BpmDiscussionFile> findByDiscussionId(Long discussionId);

    void deleteByDiscussionId(Long discussionId);

    @Query("SELECT COALESCE(a.fileName, '') as fileName, COALESCE(b.createdUser, '') as createdUser, COALESCE(a.downloadUrl, '') as downloadUrl, COALESCE(b.createdDate, null) as createdDate, " +
            " COALESCE(a.fileSize, '') as fileSize FROM BpmDiscussionFile a " +
            "JOIN BpmDiscussion b ON a.discussionId = b.id " +
            "WHERE b.ticketId = :ticketId")
    List<Map<String,Object>> findHistoryFileByTicketId(Long ticketId);



}
