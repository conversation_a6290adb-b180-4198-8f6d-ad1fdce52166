package vn.fis.eapprove.business.domain.codeGen.service.impl;

import vn.fis.eapprove.security.CredentialHelper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.support.atomic.RedisAtomicInteger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.authority.service.AuthService;
import vn.fis.eapprove.business.domain.bpm.service.impl.BpmServiceImpl;
import vn.fis.eapprove.business.domain.codeGen.entity.CodeGenConfig;
import vn.fis.eapprove.business.domain.codeGen.entity.CodeGenStruct;
import vn.fis.eapprove.business.domain.codeGen.repository.CodeGenConfigRepository;
import vn.fis.eapprove.business.domain.codeGen.repository.CodeGenStructRepository;
import vn.fis.eapprove.business.domain.codeGen.service.CodeGenConfigService;
import vn.fis.eapprove.business.domain.evaluation.repository.EvaluationCriteriaRepository;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.system.repository.SystemGroupRepository;
import vn.fis.eapprove.business.domain.template.entity.TemplateManage;
import vn.fis.eapprove.business.domain.template.repository.TemplateRepository;
import vn.fis.eapprove.business.dto.CodeGenConfigDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.CodeGenConfigCrudRequest;
import vn.fis.eapprove.business.model.response.CodeGenConfigResponse;
import vn.fis.eapprove.business.model.response.NameAndCodeCompanyResponse;
import vn.fis.eapprove.business.specification.CodeGenConfigSpecification;

import vn.fis.eapprove.business.tenant.manager.CustomerService;

import vn.fis.eapprove.business.utils.DateToLocalDateTime;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.util.LogUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.IsoFields;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: AnhVTN
 * Date: 30/03/2023
 */

@Slf4j
@Service("CodeGenConfigServiceImplV1")
@Transactional
public class CodeGenConfigServiceImpl implements CodeGenConfigService {


    private final CodeGenConfigRepository configRepository;
    private final CodeGenStructRepository structRepository;
    private final CodeGenConfigSpecification configSpecification;
    private final ResponseUtils responseUtils;
    private final DateToLocalDateTime dateToLocalDateTime;
    private final BpmServiceImpl bpmService;
    private final PermissionDataManagementRepository permissionDataManagementRepository;
    private final CredentialHelper credentialHelper;
    private final AuthService authService;
    private final CustomerService customerService;
    private final ModelMapper modelMapper;
    private final EvaluationCriteriaRepository evaluationCriteriaRepository;
    private final SystemGroupRepository systemGroupRepository;
    private final RedisConnectionFactory connectionFactory;
    private final CodeGenStructRepository codeGenStructRepository;

    private TemplateRepository templateRepository;

    @Autowired
    public CodeGenConfigServiceImpl(CodeGenConfigRepository configRepository,
                                    CodeGenStructRepository structRepository,
                                    CodeGenConfigSpecification configSpecification,
                                    ResponseUtils responseUtils,
                                    DateToLocalDateTime dateToLocalDateTime,
                                    BpmServiceImpl bpmService,
                                    PermissionDataManagementRepository permissionDataManagementRepository,
                                    CredentialHelper credentialHelper,
                                    AuthService authService,
                                    CustomerService customerService,
                                    ModelMapper modelMapper,
                                    EvaluationCriteriaRepository evaluationCriteriaRepository,
                                    TemplateRepository templateRepository, SystemGroupRepository systemGroupRepository,
                                    RedisConnectionFactory connectionFactory,
                                    CodeGenStructRepository codeGenStructRepository) {
        this.configRepository = configRepository;
        this.structRepository = structRepository;
        this.configSpecification = configSpecification;
        this.responseUtils = responseUtils;
        this.dateToLocalDateTime = dateToLocalDateTime;
        this.bpmService = bpmService;
        this.permissionDataManagementRepository = permissionDataManagementRepository;
        this.credentialHelper = credentialHelper;
        this.authService = authService;
        this.customerService = customerService;
        this.modelMapper = modelMapper;
        this.evaluationCriteriaRepository = evaluationCriteriaRepository;
        this.templateRepository = templateRepository;
        this.systemGroupRepository = systemGroupRepository;
        this.connectionFactory = connectionFactory;
        this.codeGenStructRepository = codeGenStructRepository;
    }


    /**
     * @param request object request has field CodeGenConfig and CodeGenStruct
     */
    @Override
    public void saveAll(CodeGenConfigCrudRequest request) throws Exception {

        // Xóa data phân quyền dữ liệu cũ
        if (!ValidationUtils.isNullOrEmpty(request.getId())) {
            List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(request.getId(), PermissionDataConstants.Type.CODE_GEN_CONFIG.code);
            if (!ValidationUtils.isNullOrEmpty(oldData)) {
                permissionDataManagementRepository.deleteAll(oldData);
            }
        }
        CodeGenConfig codeGenConfig1 = new CodeGenConfig();
        List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
        for(NameAndCodeCompanyResponse response : listCompanyCodeAndName){
            codeGenConfig1.setCompanyCode(response.getCompanyCode());
            codeGenConfig1.setCompanyName(response.getCompanyName());
        }

        CodeGenConfig codeGenConfig = configRepository.save(
                CodeGenConfig.builder()
                        .id(request.getId())
                        .code(request.getCode())
                        .structorCode(request.getStructorCode())
                        .description(request.getDescription())
                        .createdAt(request.getId() == null ? new Date() : request.getCreatedAt())
                        .updatedAt(request.getId() != null ? new Date() : null)
                        .userCreate(request.getUserCreate())
                        .status(request.getStatus())
                        .userUpdate(credentialHelper.getJWTPayload().getUsername())
                        .companyCode(codeGenConfig1.getCompanyCode())
                        .companyName(codeGenConfig1.getCompanyName())
                        .build());

        // Lưu phân quyền dữ liệu
        if (!ValidationUtils.isNullOrEmpty(request.getApplyFor())) {
            List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
            for (String data : request.getApplyFor()) {
                PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                permissionDataManagement.setTypeId(codeGenConfig.getId());
                permissionDataManagement.setTypeName(PermissionDataConstants.Type.CODE_GEN_CONFIG.code);
                permissionDataManagement.setCompanyCode(data);
                permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                permissionDataManagement.setCreatedTime(LocalDateTime.now());
                permissionDataManagements.add(permissionDataManagement);
            }

            permissionDataManagementRepository.saveAll(permissionDataManagements);
        }

        List<CodeGenStruct> listGenStructOld = structRepository.findAllByCodeGenConfigId(codeGenConfig.getId());

        request.getLstCodeGenStruct().forEach(codeGenStruct -> {
            codeGenStruct.setCodeGenConfigId(codeGenConfig.getId());
            if (codeGenStruct.getDataType().equalsIgnoreCase("AUTO_INCREMENT")) {
                codeGenStruct.setResetTime(new Date());
            }
        });

        //Remove delete
        List<Long> idsOld = listGenStructOld.stream().map(CodeGenStruct::getId).collect(Collectors.toList());
        List<Long> idsNew = request.getLstCodeGenStruct().stream().map(CodeGenStruct::getId).collect(Collectors.toList());
        structRepository.deleteAllById(idsOld.stream().filter(id -> !idsNew.contains(id)).collect(Collectors.toList()));

        structRepository.saveAll(request.getLstCodeGenStruct());
    }

    /**
     * @param ids List id of codeGenConfig
     */
    @Override
    public Boolean deleteByIds(List<Long> ids) {
        if (!handleCheckConfigUsing(ids, "deactive")) {
            configRepository.deleteAllById(ids);
            return false;
        }
        return true;

    }

    @Override
    public Boolean updateStatusByIds(List<Long> ids, String status)  {
        String account = credentialHelper.getJWTPayload().getUsername();
        if (!handleCheckConfigUsing(ids, status)) {
            List<CodeGenConfig> configs = configRepository.findAllById(ids);
            configs = configs.stream().peek(i -> {
                i.setStatus(status);
                i.setUpdatedAt(new Date());
                i.setUserUpdate(account);
            }).collect(Collectors.toList());
            configRepository.saveAll(configs);
            return false;
        }
        return true;
    }

    public Boolean handleCheckConfigUsing(List<Long> ids, String status) {
        if (status.equals("deactive")) {
            boolean check = false;
            Integer[] s = {0, 1};
            List<TemplateManage> templateManages = templateRepository.getAllByStatus(s);
            for (TemplateManage templateManage : templateManages) {
                String template = templateManage.getTemplate();
                Gson gson = new Gson();
                Map<String, Object> jsonMap = gson.fromJson(template, Map.class);
                if (!ValidationUtils.isNullOrEmpty(jsonMap) && jsonMap.get("form") != null && jsonMap.get("form") instanceof List) {
                    List<Object> formData = (List<Object>) jsonMap.get("form");
                    if (!formData.isEmpty()) {
                        for (Object fieldItem : formData) {
                            Map<String, Object> field = (Map<String, Object>) fieldItem;
                            if (!ValidationUtils.isNullOrEmpty(field) && !ValidationUtils.isNullOrEmpty(field.get("autoGenId")) && field.get("autoGenId").equals(Boolean.TRUE)
                                    && !ValidationUtils.isNullOrEmpty(field.get("autoGenIdValue")) && !field.get("autoGenIdValue").equals("")) {
                                Long idCheck = ((Double) field.get("autoGenIdValue")).longValue();
                                if (ids.contains(idCheck)) {
                                    check = true;
                                    break;
                                }
                            }
                        }
                    }
                    if (check)
                        break;
                }
            }
            return check;
        } else {
            return configRepository.checkExistActiveByIds(ids);
        }
    }

    /**
     * @param request
     * @return page
     */
    @Override
    public PageDto getCodeGenConfig(CodeGenConfigDto request)  {
        Map<String, Object> mapResponse = bpmService.findSystemConfigByCodeAndName("config_type_auto_gen", request.getSearch());
        if (mapResponse != null && mapResponse.get("data") != null) {
            request.setSystemConfigCode((List<String>) mapResponse.get("data"));
        } else
            request.setSystemConfigCode(new ArrayList<>());

        String username = credentialHelper.getJWTPayload().getUsername();
        // Lấy list companyCode cấu hình QL vai trò người dùng
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
        request.setListCompanyCode(lstCompanyCode);

        // Phân quyền theo nhóm
        List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.CODE_GEN_CONFIG.tableName, username);
        // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
        if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
            request.setLstGroupPermissionId(lstGroupPermissionId);
            // get list company code role admin member
            List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
            request.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
        }

        int pageNum = request.getPage() - 1;
        Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());
        List<CodeGenConfig> templates = new ArrayList<>();
        List<CodeGenConfigResponse> listResponses = new ArrayList<>();
        Page<CodeGenConfig> page = configRepository.findAll(configSpecification.filter(request), PageRequest.of(pageNum, request.getSize(), sort));

        if (page.hasContent()) {
            page.getContent().forEach(item -> {
                List<CodeGenStruct> codeGenStructs = structRepository.findCodeGenStructByCodeGenConfigId(item.getId());
                item.setLstCodeGenStruct(codeGenStructs);
            });
            templates = page.getContent();

            listResponses = templates.stream().map(x -> modelMapper.map(x, CodeGenConfigResponse.class)).collect(Collectors.toList());
            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.CODE_GEN_CONFIG.code);

            for (CodeGenConfigResponse response : listResponses) {
                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(response.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
                if (!ValidationUtils.isNullOrEmpty(companyCodes)) {
                    response.setApplyFor(companyCodes);
                }
            }
        }
        return PageDto.builder().content(listResponses)
                .number(page.getNumber() + 1)
                .numberOfElements(page.getNumberOfElements())
                .page(page.getNumber() + 1)
                .size(page.getSize())
                .sortBy(request.getSortBy())
                .sortBy(request.getSortType())
                .totalPages(page.getTotalPages())
                .totalElements(page.getTotalElements())
                .build();
    }

    @Override
    public List<CodeGenConfigResponse> getCodeGenConfigFilter(CodeGenConfigDto request)  {
        Map<String, Object> mapResponse = bpmService.findSystemConfigByCodeAndName("config_type_auto_gen", request.getSearch());
        if (mapResponse != null && mapResponse.get("data") != null) {
            request.setSystemConfigCode((List<String>) mapResponse.get("data"));
        } else
            request.setSystemConfigCode(new ArrayList<>());

        String username = credentialHelper.getJWTPayload().getUsername();
        // Lấy list companyCode cấu hình QL vai trò người dùng
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
        // Phân quyền theo nhóm
        List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.CODE_GEN_CONFIG.tableName, username);
        // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
        if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
            request.setLstGroupPermissionId(lstGroupPermissionId);
            // get list company code role admin member
            List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
            request.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
        }

        request.setListCompanyCode(lstCompanyCode);

        int pageNum = request.getPage() - 1;
        Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());
        List<CodeGenConfig> templates = new ArrayList<>();
        List<CodeGenConfigResponse> listResponses = new ArrayList<>();
        Page<CodeGenConfig> page = configRepository.findAll(configSpecification.filter(request), PageRequest.of(pageNum, request.getSize(), sort));
        if (page.hasContent()) {
            page.getContent().forEach(item -> {
                List<CodeGenStruct> codeGenStructs = structRepository.findCodeGenStructByCodeGenConfigId(item.getId());
                item.setLstCodeGenStruct(codeGenStructs);
            });
            templates = page.getContent();

            listResponses = templates.stream().map(x -> modelMapper.map(x, CodeGenConfigResponse.class)).collect(Collectors.toList());
            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.CODE_GEN_CONFIG.code);

            for (CodeGenConfigResponse response : listResponses) {
                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(response.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
                if (!ValidationUtils.isNullOrEmpty(companyCodes)) {
                    response.setApplyFor(companyCodes);
                }
            }
        }
        return listResponses;
    }

    @Override
    public CodeGenConfig getCodeGenEdit(Long id) {
        List<CodeGenStruct> codeGenStructs = structRepository.findCodeGenStructByCodeGenConfigId(id);
        CodeGenConfig codeGenConfig = new CodeGenConfig();
        Optional<CodeGenConfig> optionalCodeGenConfig = configRepository.findById(id);
        if (optionalCodeGenConfig.isPresent()) codeGenConfig = optionalCodeGenConfig.get();
        codeGenConfig.setLstCodeGenStruct(codeGenStructs);
        return codeGenConfig;
    }

    @Override
    public boolean checkExist(String code) {
        CodeGenConfig codeGenConfig = configRepository.findCodeGenConfigByCodeAndStatus(code, "active");
        return codeGenConfig == null;
    }

    @Override
    public boolean checkExistByCode(String code, String configCode) {
        if (configCode.equalsIgnoreCase("config_type_auto_gen")) {
            return configRepository.existsCodeGenConfigByCode(code);
        } else if (configCode.equalsIgnoreCase("category_check")) {
            return evaluationCriteriaRepository.existsEvaluationCriteriaByReviewItem(code);
        }
        return false;
    }

    @Override
    public boolean checkUpdateDuplicateCode(Long id, String code) {
        Optional<CodeGenConfig> codeGenConfig = configRepository.findById(id);
        return codeGenConfig.isPresent() && codeGenConfig.get().getCode().equalsIgnoreCase(code);
    }

    public String handleSpecialGenerateKey(String key)  {
        String returnValue = "";
        //Xử lý 1 số key đặc biệt
        switch (key) {
            case "company_code":
                List<Map<String, Object>> chartObj = bpmService.findCompanyCodeByUsername(credentialHelper.getJWTPayload().getUsername());
                returnValue = chartObj.isEmpty() ? "" : chartObj.get(0).get("companyCode").toString();
                break;
            default:
                break;
        }
        return returnValue;
    }

    public String handleSpecialGenerateKey(String key, String account) {
        String returnValue = "";
        //Xử lý 1 số key đặc biệt
        switch (key) {
            case "company_code":
                List<Map<String, Object>> chartObj = bpmService.findCompanyCodeByUsername(account);
                returnValue = chartObj.isEmpty() ? "" : chartObj.get(0).get("companyCode").toString();
                break;
            default:
                break;
        }
        return returnValue;
    }

    @Override
    public String autoGenerateCode(String code, Map<String, Object> mDataConfig) {
        if (ValidationUtils.isNullOrEmpty(code)) {
            return null;
        }
        CodeGenConfig codeGenConfig = configRepository.findCodeGenConfigByCodeAndStatus(code, "active");
        StringBuilder codeGenerate = new StringBuilder();
        if (codeGenConfig != null) {
            //Lấy dách sách cấu hình chi tiết
            List<CodeGenStruct> codeGenStructs = structRepository.findCodeGenStructByCodeGenConfigId(codeGenConfig.getId());
            if (codeGenStructs != null && !codeGenStructs.isEmpty()) {
                codeGenStructs.forEach(structItem -> {
                    if (structItem != null) {
                        String type = StringUtil.nvl(structItem.getDataType(), "NORMAL").toUpperCase();
                        String value = "";
                        switch (type) {
                            case "NORMAL":
                                value = StringUtil.nvl(structItem.getValue(), "");
                                break;
                            case "AUTO_INCREMENT":
                                // get increment value from redis
                                Integer nextValue = getAtomicInteger(String.valueOf(structItem.getId()));
                                String strNextValue = String.valueOf(nextValue);

                                //Tạo giá trị theo độ dài
                                int length = structItem.getLength() == null ? 1 : structItem.getLength();
                                value = StringUtils.leftPad(strNextValue, length, "0");
                                break;
                            case "INPUT":
                                if (mDataConfig != null && mDataConfig.containsKey(structItem.getValue())) {
                                    value = StringUtil.nvl(mDataConfig.get(structItem.getValue()), "");
                                } else {
                                    try {// Xử lý 1 số key đặc biệt
                                        value = handleSpecialGenerateKey(structItem.getValue());
                                    } catch (Exception e) {
                                        value = "";
                                    }
                                    if (value.isEmpty())
                                        value = StringUtil.nvl(structItem.getDefaultValue(), "");
                                }
                                break;
                            case "DATE":
                                String dateFormat = StringUtil.nvl(structItem.getFormat(), "dd/MM/yyyy");
                                SimpleDateFormat format = new SimpleDateFormat(dateFormat);
                                value = format.format(new Date());
                                break;
                        }
                        codeGenerate.append(value);
                    }
                });
            }
        }
        log.info("autoGenerateCode: {}", codeGenerate);
        return codeGenerate.toString();
    }

    @Override
    public String autoGenerateCodeTicket(Long id, Map<String, Object> mDataConfig) {
        if (ValidationUtils.isNullOrEmpty(id)) {
            log.error("autoGenerateCodeTicket: id is null or empty");
            return null;
        }
        CodeGenConfig codeGenConfig = configRepository.findCodeGenConfigByIdAndStatus(id, "active");
        StringBuilder codeGenerate = new StringBuilder();
        if (codeGenConfig != null) {
            //Lấy dách sách cấu hình chi tiết
            List<CodeGenStruct> codeGenStructs = structRepository.findCodeGenStructByCodeGenConfigId(codeGenConfig.getId());
            if (codeGenStructs != null && !codeGenStructs.isEmpty()) {
                codeGenStructs.forEach(structItem -> {
                    if (structItem != null) {
                        String type = StringUtil.nvl(structItem.getDataType(), "NORMAL").toUpperCase();
                        String value = "";
                        switch (type) {
                            case "NORMAL":
                                value = StringUtil.nvl(structItem.getValue(), "");
                                break;
                            case "AUTO_INCREMENT":
                                // get increment value from redis
                                Integer nextValue = getAtomicInteger(String.valueOf(structItem.getId()));
                                String strNextValue = String.valueOf(nextValue);

                                //Tạo giá trị theo độ dài
                                int length = structItem.getLength() == null ? 1 : structItem.getLength();
                                value = StringUtils.leftPad(strNextValue, length, "0");
                                break;
                            case "INPUT":
                                if (mDataConfig != null && mDataConfig.containsKey(structItem.getValue())) {
                                    value = StringUtil.nvl(mDataConfig.get(structItem.getValue()), "");
                                } else {
                                    try {// Xử lý 1 số key đặc biệt
                                        value = handleSpecialGenerateKey(structItem.getValue());
                                    } catch (Exception e) {
                                        value = "";
                                    }
                                    if (value.isEmpty())
                                        value = StringUtil.nvl(structItem.getDefaultValue(), "");
                                }
                                break;
                            case "DATE":
                                String dateFormat = StringUtil.nvl(structItem.getFormat(), "dd/MM/yyyy");
                                SimpleDateFormat format = new SimpleDateFormat(dateFormat);
                                value = format.format(new Date());
                                break;
                        }
                        codeGenerate.append(value);
                    }
                });
            }
        }
        log.info("autoGenerateCodeTicket: {}", codeGenerate);
        return codeGenerate.toString();
    }

    @Override
    public String autoGenerateCodeBasicAuth(String code, Map<String, Object> mDataConfig) {
        String account = mDataConfig != null ? StringUtil.nvl(mDataConfig.get("account"), "") : "";
        if (ValidationUtils.isNullOrEmpty(code)) {
            return null;
        }
        CodeGenConfig codeGenConfig = configRepository.findCodeGenConfigByCodeAndStatus(code, "active");
        StringBuilder codeGenerate = new StringBuilder();
        if (codeGenConfig != null) {
            //Lấy dách sách cấu hình chi tiết
            List<CodeGenStruct> codeGenStructs = structRepository.findCodeGenStructByCodeGenConfigId(codeGenConfig.getId());
            if (codeGenStructs != null && codeGenStructs.size() > 0) {
                codeGenStructs.forEach(structItem -> {
                    if (structItem != null) {
                        String type = StringUtil.nvl(structItem.getDataType(), "NORMAL").toUpperCase();
                        String value = "";
                        switch (type) {
                            case "NORMAL":
                                value = StringUtil.nvl(structItem.getValue(), "");
                                break;
                            case "AUTO_INCREMENT":
//                                Integer nextValue = 1;
//                                try {
//                                    nextValue = Integer.valueOf(structItem.getCurrentValue()) + 1;
//                                } catch (Exception e) {
//                                }
//                                String strNextValue = String.valueOf(nextValue);
//                                structItem.setCurrentValue(strNextValue);
//                                structRepository.save(structItem);

                                // get increment value from redis
                                Integer nextValue = getAtomicInteger(String.valueOf(structItem.getId()));
                                String strNextValue = String.valueOf(nextValue);
                                //Tạo giá trị theo độ dài
                                Integer length = structItem.getLength() == null ? 1 : structItem.getLength();
                                value = StringUtils.leftPad(strNextValue, length, "0");
                                break;
                            case "INPUT":
                                if (mDataConfig != null && mDataConfig.containsKey(structItem.getValue())) {
                                    value = StringUtil.nvl(mDataConfig.get(structItem.getValue()), "");
                                } else {
                                    value = handleSpecialGenerateKey(structItem.getValue(), account);

                                    if (value.equals(""))
                                        value = StringUtil.nvl(structItem.getDefaultValue(), "");
                                }
                                break;
                            case "DATE":
                                String dateFormat = StringUtil.nvl(structItem.getFormat(), "dd/MM/yyyy");
                                SimpleDateFormat format = new SimpleDateFormat(dateFormat);
                                value = format.format(new Date());
                                break;
                        }
                        codeGenerate.append(value);
                    }
                });
            }
        }
        return codeGenerate.toString();
    }

    @Override
    public void autoResetGenerateCode() {
        long startTime = System.currentTimeMillis();
        try {
            updateStructCode();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    public void updateStructCode() {
        try {
            Date now = new Date();
            Date startOfDay = DateToLocalDateTime.convertToDate(LocalDateTime.now().toLocalDate().atStartOfDay());
            List<CodeGenStruct> listAllAutoCodeGenStruc = structRepository.listAllAutoCodeGenStruct();
            listAllAutoCodeGenStruc.forEach(objects -> {
                Date startOfDayResetTime;
                LocalDate resetTime = null;
                if (!ValidationUtils.isNullOrEmpty(objects.getResetTime())) {
                    resetTime = dateToLocalDateTime.convertToLocalDateTimeViaInstant(objects.getResetTime()).toLocalDate();
                }
                if (!ValidationUtils.isNullOrEmpty(objects.getSchedule())) {
                    switch (objects.getSchedule()) {
                        case 1:
                            startOfDayResetTime = DateToLocalDateTime.convertToDate(resetTime.atStartOfDay());
                            if (startOfDay.after(startOfDayResetTime)) {
                                structRepository.updateAllCodeGenStructAutoIncrement(objects.getId(), objects.getSchedule(), now);
                            }
                            break;
                        case 2:
                            int dayOfWeekResetTime = resetTime.getDayOfWeek().getValue();
                            LocalDateTime dateOfNextWeekResetTime = dateToLocalDateTime.convertToLocalDateTimeViaInstant(objects.getResetTime()).plusDays(8 - dayOfWeekResetTime).toLocalDate().atStartOfDay();
                            if (LocalDateTime.now().isAfter(dateOfNextWeekResetTime)) {
                                structRepository.updateAllCodeGenStructAutoIncrement(objects.getId(), objects.getSchedule(), now);
                            }
                            break;
                        case 3:
                            int monthOfResetTime = resetTime.getMonthValue();
                            int monthOfNow = LocalDateTime.now().getMonthValue();
                            if (LocalDateTime.now().isAfter(dateToLocalDateTime.convertToLocalDateTimeViaInstant(objects.getResetTime())) && monthOfNow != monthOfResetTime) {
                                structRepository.updateAllCodeGenStructAutoIncrement(objects.getId(), objects.getSchedule(), now);
                            }
                            break;
                        case 4:
                            if (LocalDateTime.now().isAfter(dateToLocalDateTime.convertToLocalDateTimeViaInstant(objects.getResetTime())) && LocalDateTime.now().get(IsoFields.QUARTER_OF_YEAR) != resetTime.get(IsoFields.QUARTER_OF_YEAR)) {
                                structRepository.updateAllCodeGenStructAutoIncrement(objects.getId(), objects.getSchedule(), now);
                            }
                            break;
                        case 5:
                            if (LocalDateTime.now().isAfter(dateToLocalDateTime.convertToLocalDateTimeViaInstant(objects.getResetTime())) && LocalDateTime.now().getYear() > resetTime.getYear()) {
                                structRepository.updateAllCodeGenStructAutoIncrement(objects.getId(), objects.getSchedule(), now);
                            }
                        default:
                            break;
                    }
                }


            });

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public Object getAllSystemGroup()  {
        List<Map<String, Object>> lstResponse = new ArrayList<>();
        CodeGenConfigDto request = new CodeGenConfigDto();
        request.setPage(1);
        request.setLimit(999999);
        request.setSortBy("id");
        request.setSortType("DESC");
        Map<String, Object> mapResponse = bpmService.findSystemConfigByCodeAndName("config_type_auto_gen", request.getSearch());
        if (mapResponse != null && mapResponse.get("data") != null) {
            request.setSystemConfigCode((List<String>) mapResponse.get("data"));
        } else
            request.setSystemConfigCode(new ArrayList<>());

        String username = credentialHelper.getJWTPayload().getUsername();
        // Lấy list companyCode cấu hình QL vai trò người dùng
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
        // Phân quyền theo nhóm
        List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.CODE_GEN_CONFIG.tableName, username);
        // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
        if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
            request.setLstGroupPermissionId(lstGroupPermissionId);
            // get list company code role admin member
            List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
            request.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
        }

        request.setListCompanyCode(lstCompanyCode);

        int pageNum = request.getPage() - 1;
        Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());

        Page<CodeGenConfig> page = configRepository.findAll(configSpecification.filter(request), PageRequest.of(pageNum, request.getLimit(), sort));
        List<CodeGenConfig> lstCode = page.getContent();
        for (CodeGenConfig codeGenConfig : lstCode) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", codeGenConfig.getId());
            map.put("name", codeGenConfig.getCode());
            map.put("companyCode", codeGenConfig.getCompanyCode());
            map.put("companyName", codeGenConfig.getCompanyName());
            lstResponse.add(map);
        }

        return lstResponse;
    }

    @Override
    public Integer getAtomicInteger(String key) {
        RedisAtomicInteger redisAtomicInteger = new RedisAtomicInteger(key, connectionFactory);
        return redisAtomicInteger.getAndIncrement();
    }

    @Override
    public void resetRedisAtomicInteger() {
        List<Long> keys = codeGenStructRepository.findAllAutoCodeGenStructIdsByDataType();
        if (!ValidationUtils.isNullOrEmpty(keys)) {
            for (Long key : keys) {
                RedisAtomicInteger redisAtomicInteger = new RedisAtomicInteger(key.toString(), connectionFactory);
                redisAtomicInteger.set(1);
            }
        }
    }

    @Override
    public CodeGenConfig findById(Long id) {
        return configRepository.findById(id).orElse(null);
    }
}
