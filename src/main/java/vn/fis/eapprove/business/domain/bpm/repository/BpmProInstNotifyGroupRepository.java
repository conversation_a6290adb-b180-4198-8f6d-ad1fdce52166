package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProInstNotifyGroup;


import java.util.List;

public interface BpmProInstNotifyGroupRepository extends JpaRepository<BpmProInstNotifyGroup, Long> {
    @Query("SELECT a from BpmProInstNotifyGroup a where a.bpmProcinstId = :ticketId")
    List<BpmProInstNotifyGroup> getNotifyGroupsByBpmProcinstId(Long ticketId);

    List<BpmProInstNotifyGroup> findByBpmProcinstId(Long bpmProcinstId);

    @Modifying(clearAutomatically = true, flushAutomatically = true)
    void deleteAllByBpmProcinstId(@Param("bpmProcInstId") Long bpmProcInstId);

}
