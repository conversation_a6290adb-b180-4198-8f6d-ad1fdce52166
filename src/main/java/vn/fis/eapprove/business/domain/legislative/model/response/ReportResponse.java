package vn.fis.eapprove.business.domain.legislative.model.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.model.FileModel;

import java.util.List;

@Data
@NoArgsConstructor
public class ReportResponse {
    private Long id;
    private String name;
    private String type;
    private String responsibleAgency;
    private String status;
    private Long releaseYear;
    private String approvalSession;
    private String processType;
    private Long slaFinishTime;
    private Long processTime;
    private List<FileModel> files;
    private List<String> listFileName;
    private List<String> listTaskName;
    private List<ReportDetailResponse> details;
}
