package vn.fis.eapprove.business.domain.template.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.template.entity.TemplateManage;
import vn.fis.eapprove.business.model.response.ChildResponse;


import java.util.List;
import java.util.Optional;

@Repository
public interface TemplateRepository extends JpaRepository<TemplateManage, Long>, JpaSpecificationExecutor<TemplateManage> {

    @Query("SELECT c FROM TemplateManage c WHERE (LOWER(c.templateName) LIKE LOWER(CONCAT('%',:search,'%')) OR LOWER(c.modifiedUser) LIKE LOWER (CONCAT('%',:search,'%'))) AND ( -1 IN (:status) OR c.status IN (:status)) AND ( concat('',:createdUser,'') is null or ( c.createdUser IN (:createdUser)))")
    Page<TemplateManage> getAll(@Param("search") String search, @Param("status") List<Integer> status, @Param("createdUser") List<String> createdUser, Pageable pageable);

    @Query("SELECT  c FROM TemplateManage c WHERE  c.status IN (:status)")
    List<TemplateManage> getAllByStatus(@Param("status") Integer[] status);
    @Query("SELECT c FROM TemplateManage c WHERE c.templateName = :templateName AND c.createdDate is not null")
    List<TemplateManage> CheckExistName(@Param("templateName") String templateName);

    @Query("SELECT c FROM TemplateManage c WHERE c.urlName = :urlName AND c.createdDate is not null")
    List<TemplateManage> checkExistUrlName(@Param("urlName") String urlName);

    @Query("SELECT c FROM TemplateManage c WHERE (c.urlName = :urlName OR c.templateName = :templateName) AND c.id != :id AND c.createdDate is not null")
    List<TemplateManage> checkExistTemplateNameAndUrl(@Param("id")Long id,@Param("urlName") String urlName,@Param("templateName") String templateName);

    @Query(value = "SELECT sp.* FROM template_manage sp " +
            "INNER JOIN group_table_proc_temp bp ON sp.url_name COLLATE utf8mb4_unicode_ci = bp.form_key COLLATE utf8mb4_unicode_ci " +
            "INNER JOIN bpm_procdef bpd ON bp.pro_def_id COLLATE utf8mb4_unicode_ci = bpd.proc_def_id COLLATE utf8mb4_unicode_ci " +
            "WHERE sp.id COLLATE utf8mb4_unicode_ci = :id", nativeQuery = true)
    List<TemplateManage> checkProdefId(@Param("id") Long id);


    @Query("SELECT c FROM TemplateManage c WHERE c.template like concat('%','\"masterDataId\":',:masterDataId,'%')")
    List<TemplateManage> checkMasterData(Long masterDataId);

    @Query("SELECT c FROM TemplateManage c WHERE c.template like concat('%','\"masterDataFilterId\":',:masterDataFilterId,'%')")
    List<TemplateManage> checkMasterDataFilter(Long masterDataFilterId);

    Optional<TemplateManage> findTopByTemplateNameLikeOrderByIdDesc(String name);

    Optional<TemplateManage> findTopByUrlNameLikeOrderByIdDesc(String urlName);

    TemplateManage findByUrlName(String formKeyChoose);

    @Query("SELECT u.id,u.templateName,u.urlName from TemplateManage u where u.status = 1")
    List<Object[]> getTemplateNameAndId();

    List<TemplateManage> findAllByUrlNameIn(List<String> urlNames);

    TemplateManage findTemplateManageBySpecialParentIdAndSpecialCompanyCodeAndSpecialParentServiceId(Long parentId, String companyCode, Long parentServiceId);

    @Query("SELECT new vn.fis.eapprove.business.model.response.ChildResponse(t.specialParentId,t.id,t.specialCompanyCode,'','') FROM TemplateManage t " +
            "where t.specialParentId in (:parentIds) " +
            "AND t.specialFlow = true")
    List<ChildResponse> findSpecialFlowChildDataByParentIds(List<Long> parentIds);

    List<TemplateManage> findAllBySpecialParentIdAndSpecialFlowIsTrue(Long parentIds);
}


