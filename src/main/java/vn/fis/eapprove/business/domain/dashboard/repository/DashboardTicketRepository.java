package vn.fis.eapprove.business.domain.dashboard.repository;

import jakarta.persistence.Tuple;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.dashboard.entity.DashboardTicket;

import java.util.List;

@Repository
public interface DashboardTicketRepository extends JpaRepository<DashboardTicket, Long> {

    DashboardTicket getDashboardTicketByTicketId(Long ticketId);
    
    @Query(value = "select proc_inst_status as ticketStatus, count(proc_inst_status) as count  " +
            "from dashboard_ticket  " +
            "where 1 = 1  " +
            "  and created_user = :username  " +
            "  and created_time >= :fromDate  " +
            "  and (:toDate is null or created_time < :toDate)  " +
            "group by proc_inst_status", nativeQuery = true)
    List<Tuple> getChartPieStatus(@Param("username") String username, @Param("fromDate") String fromDate, @Param("toDate") String toDate);

    @Query(value = "select count(*) from dashboard_ticket  " +
            " where created_user = :username " +
            " and created_time >= :fromDate " +
            " and (:toDate is null or created_time < :toDate)", nativeQuery = true)
    Integer countDashBoardTicket(@Param("username") String username, @Param("fromDate") String fromDate, @Param("toDate") String toDate);
}
