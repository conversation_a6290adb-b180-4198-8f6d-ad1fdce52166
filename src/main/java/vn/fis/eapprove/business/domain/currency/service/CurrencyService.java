package vn.fis.eapprove.business.domain.currency.service;

/**
 * Author: AnhVTN
 * Date: 31/03/2023
 */

import vn.fis.eapprove.business.domain.currency.entity.Currency;
import vn.fis.eapprove.business.dto.CurrencyFilterDto;
import vn.fis.eapprove.business.dto.PageDto;

import java.util.List;

public interface CurrencyService {
    Currency saveCurrency(Currency currency) throws Exception;

    Currency findById(Long id);

    void deleteByIds(List<Long> id);

    PageDto getCurrencys(CurrencyFilterDto request);

    List<Currency> getCurrencysFilter(CurrencyFilterDto request);

    boolean isExistCode(String code);

    boolean isUpdateChangeCode(Long id, String code);

    boolean isNullCode(String code);

    boolean checkNameExist(String name);
    boolean checkAnotherNameExist(String name);
    boolean checkNameExistForUpdate(Long id,String name);
    boolean checkAnotherNameExistForUpdate(Long id,String name);
    boolean checkCodeExistForUpdate(Long id,String code);
}
