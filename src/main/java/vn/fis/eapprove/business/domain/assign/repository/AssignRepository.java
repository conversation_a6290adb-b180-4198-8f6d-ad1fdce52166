
package vn.fis.eapprove.business.domain.assign.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.assign.entity.AssignManagement;


import java.time.LocalDate;
import java.util.List;


@Repository
public interface AssignRepository extends JpaRepository<AssignManagement, Long>, JpaSpecificationExecutor<AssignManagement> {
    AssignManagement findAssignManagementByAssignedUser(String user);

    //    AssignManagement findAssignManagementByAssignUser(String user);
    List<AssignManagement> findAssignManagementByAssignUser(String user);

    @Query("select u from AssignManagement u where u.assignUser = :user " +
            "and u.status in (0,1) " +
            "and (u.endDate is null OR u.endDate > :date ) " +
            "order by u.id desc ")
    List<AssignManagement> findAssignManagementByAssignUserOrderByIdDesc(String user, LocalDate date);

    @Query("select u.serviceRange from AssignManagement u where u.id =:id ")
    String findServiceRangeById(Long id);

    @Query("select u from AssignManagement u where u.status in :status and (" +
            "u.createdUser =:username OR u.assignUser=:username OR u.assignedUser=:username" +
            ")")
    List<AssignManagement> getAllAssign(int[] status, String username);

    @Query("select u from AssignManagement u where u.effect =1")
    List<AssignManagement> getAllAssignByEffect();

    AssignManagement findAssignManagementByTicketId(Long id);

    @Query("select u from AssignManagement u where u.status in (0,1) " +
            "and u.assignedUser = :username " +
            "or u.assignName = :username " +
            "or u.assignUser = :username")
    List<AssignManagement> getAllAssignActiveByUsername(String username);

    @Query(value = "SELECT * FROM assign_management where ticket_id = :ticketId ORDER BY id ASC", nativeQuery = true)
    AssignManagement getHistoryAssign(Long ticketId);

    @Query(value = "SELECT u FROM AssignManagement u where u.requestCode = :requestCode ")
    AssignManagement getAllAssignByRequestCode(String requestCode);

    @Query("SELECT a FROM AssignManagement a "
            + "WHERE a.status in (0,1) AND a.assignUser = :assignUser AND a.assignedUser = :assignedUser order by a.id desc")
    List<AssignManagement> getActiveAssignments(@Param("assignUser") String assignUser, @Param("assignedUser") String assignedUser);

    AssignManagement getByTicketId(Long id);

    AssignManagement getByNewTicket(String id);

    @Query("SELECT a FROM AssignManagement a WHERE a.status in (:status) ")
    List<AssignManagement> findByStatus(int[] status);


    @Query("SELECT COUNT(a) FROM AssignManagement a WHERE a.assignUser =:username or a.assignedUser =:username or a.createdUser =:username")
    int countByUsername(String username);

    @Query("SELECT a from AssignManagement a WHERE a.status in (0, 1) and a.serviceId = :serviceId and a.assignUser in (:lstAssignUser)")
    List<AssignManagement> getByListAssignUserAndServiceId(List<String> lstAssignUser, Long serviceId);
}
