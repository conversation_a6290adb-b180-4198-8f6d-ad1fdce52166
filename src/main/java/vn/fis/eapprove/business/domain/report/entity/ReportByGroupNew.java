package vn.fis.eapprove.business.domain.report.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "report_by_group_new")
@Accessors(chain = true)
public class ReportByGroupNew {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "service_id")
    private Long serviceId;

    @Column(name = "service_name")
    private String serviceName;

    @Column(name = "submission_type")
    private Long submissionType;

    @Column(name = "master_parent_id")
    private String masterParentId;

    @Column(name = "proc_inst_id")
    private String procInstId;

    @Column(name = "title")
    private String title;

    @Column(name = "request_code")
    private String requestCode;

    @Column(name = "priority_id")
    private Long priorityId;

    @Column(name = "priority_name")
    private String priorityName;

    @Column(name = "proc_inst_status")
    private String procInstStatus;

    @Column(name = "sla_finish")
    private Float slaFinish;

    @Column(name = "started_time")
    private LocalDateTime startedTime;

    @Column(name = "finished_time")
    private LocalDateTime finishedTime;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "location_id")
    private Long locationId;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "created_user_full_name")
    private String createdUserFullName;

    @Column(name = "chart_node_name")
    private String chartNodeName;

    @Column(name = "chart_node_code")
    private String chartNodeCode;

    @Column(name = "chart_node_id")
    private Long chartNodeId;

    @Column(name = "chart_short_name")
    private String chartShortName;

    @Column(name = "chart_id")
    private Long chartId;

    @Column(name = "user_title_name")
    private String userTitleName;

    @Column(name = "direct_manager")
    private String directManager;

    @Column(name = "staff_code")
    private String staffCode;

    @Column(name = "user_status")
    private String userStatus;

    @Column(name = "is_expire")
    private Boolean isExpire;

    @Column(name = "manager_level")
    private String managerLevel;

    @Column(name = "email")
    private String email;

    @Column(name = "ticket_id")
    private Long ticketId;

    @Column(name = "proc_def_id")
    private String procDefId;

    @Column(name = "sla_finish_time")
    private LocalDateTime slaFinishTime;

    @Column(name = "assignee")
    private String assignee;

    @Column(name = "assignee_chart_node_id")
    private String assigneeChartNodeId;

    @Column(name = "assignee_chart_id")
    private String assigneeChartId;

    @Column(name = "task_type")
    private String taskType;

    @Column(name = "assignee_status")
    private String assigneeStatus;

    @Column(name = "action_current_time")
    private LocalDateTime actionCurrentTime;

    @Column(name = "version_time")
    private LocalDate versionTime;
}
