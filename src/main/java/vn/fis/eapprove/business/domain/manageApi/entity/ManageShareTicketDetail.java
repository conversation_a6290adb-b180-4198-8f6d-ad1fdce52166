package vn.fis.eapprove.business.domain.manageApi.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;
import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "manage_share_ticket_detail")
public class ManageShareTicketDetail implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "manage_share_ticket_id")
    private Long manageShareTicketId;

    @Column(name = "type")
    private String type;

    @Column(name = "value")
    private String value;

    @Column(name = "name")
    private String name;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "manage_share_ticket_id", insertable = false, updatable = false)
    @JsonIgnore
    private ManageShareTicket manageShareTicket;
}
