package vn.fis.eapprove.business.domain.api.repository;

import jakarta.persistence.Tuple;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.api.entity.ApiLog;


/**
 * Author: PhucVM
 * Date: 29/11/2022
 */
@Repository
public interface ApiLogRepository extends JpaRepository<ApiLog, Long>, JpaSpecificationExecutor<ApiLog> {

    @Query(value = "select pa.task_def_key as taskDefKey, " +
            " am.name as apiName " +
            " from bpm_procdef_api pa " +
            " join api_management am on am.id = pa.api_id " +
            " where pa.id = :bpmProcDefId"
            , nativeQuery = true)
    Tuple getApiInfoByBpmProcDefId(Long bpmProcDefId);

    @Query(value = "select title as ticketName," +
            " request_code as requestCode" +
            " from bpm_procinst where id = :ticketId", nativeQuery = true)
    Tuple getTicketInfoByTicketId(Long ticketId);
}
