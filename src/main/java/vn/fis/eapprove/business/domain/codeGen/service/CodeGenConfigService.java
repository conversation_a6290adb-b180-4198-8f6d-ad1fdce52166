package vn.fis.eapprove.business.domain.codeGen.service;


import vn.fis.eapprove.business.domain.codeGen.entity.CodeGenConfig;
import vn.fis.eapprove.business.dto.CodeGenConfigDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.CodeGenConfigCrudRequest;
import vn.fis.eapprove.business.model.response.CodeGenConfigResponse;


import java.util.List;
import java.util.Map;

/**
 * Author: AnhVTN
 * Date: 30/03/2023
 */
public interface CodeGenConfigService {

    void saveAll(CodeGenConfigCrudRequest request) throws Exception;

    Boolean deleteByIds(List<Long> ids);

    Boolean updateStatusByIds(List<Long> ids,String status) ;

    PageDto getCodeGenConfig(CodeGenConfigDto request) ;
    List<CodeGenConfigResponse> getCodeGenConfigFilter(CodeGenConfigDto request) ;

    CodeGenConfig getCodeGenEdit(Long id);

    boolean checkExist(String code);

    boolean checkUpdateDuplicateCode(Long id, String code);

    String autoGenerateCode(String code, Map<String, Object> mDataConfig);
    String autoGenerateCodeBasicAuth(String code, Map<String, Object> mDataConfig);
    String autoGenerateCodeTicket(Long id, Map<String, Object> mDataConfig);

    void autoResetGenerateCode();

    boolean checkExistByCode(String code, String configCode);

    Object getAllSystemGroup() ;

    Integer getAtomicInteger(String key);

    void resetRedisAtomicInteger();

    CodeGenConfig findById(Long id);
}
