package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Data;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Data
@Entity
@Transactional
@Table(name = "bpm_template_print_history")
public class BpmTemplatePrintHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "BPM_TEMPLATE_PRINT_ID")
    private Long bpmTemplatePrintId;

    @Column(name = "VERSION")
    private String version;

    @Column(name = "STATUS_HISTORY")
    private Boolean statusHistory;

    @Column(name = "CONTENT_EDIT")
    private String contentEdit;

    @Column(name = "NAME")
    private String name;

    @Column(name = "PROCESS_NAME")
    private String processName;

    @Column(name = "DESCR")
    private String descr;

    @Column(name = "PROC_DEF_ID")
    private String procDefId;

    @Column(name = "HISTORY_CHANGE")
    private String historyChange;

    @Column(name = "PRINT_TYPE")
    private int printType = 0;

    @Column(name = "config_type")
    private String configType;

    @Column(name = "PROCESS_ID")
    private Long processId;

    @Column(name = "CREATED_USER")
    private String createdUser;

    @Column(name = "CREATED_DATE")
    private LocalDateTime createdDate;

    @Column(name = "APPLY_FOR")
    private String applyFor;

    @Column(name = "SHARE_WITH")
    private String shareWith;

    @Column(name = "CONDITION_FILE")
    private String conditionFile;

    @Column(name = "TP_TASK")
    private String tpTask;
}
