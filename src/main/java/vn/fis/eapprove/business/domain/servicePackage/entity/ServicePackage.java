package vn.fis.eapprove.business.domain.servicePackage.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "service_package")
public class ServicePackage implements Serializable {

    private static final long serialVersionUID = 7059724308535406655L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "service_name")
    private String serviceName;

    @Column(name = "color")
    private String color;

    @Column(name = "icon")
    private String icon;

    @Column(name = "service_type")
    private Integer serviceType;

    @Column(name = "process_id")
    private Long processId;

    @Column(name = "description")
    private String description;

    @Column(name = "note")
    private String note;

    @Column(name = "url")
    private String url;

    @Column(name = "position_package")
    private Integer positionPackage;

    @Column(name = "id_org_chart")
    private Long idOrgChart;

    @Column(name = "not_showing_website")
    private Boolean notShowingWebsite;

    @Column(name = "not_showing_moblie")
    private Boolean notShowingMoblie;

    @Column(name = "hide_name")
    private String hideName;

    @Column(name = "visible_name")
    private String visibleName;

    @Column(name = "visible_group")
    private String visibleGroup;

    @Column(name = "hide_group")
    private String hideGroup;

    @Column(name = "deleted")
    private Boolean deleted;

    @Column(name = "master_parent_id")
    private Long masterParentId;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "modified_date")
    private LocalDateTime modifiedDate;

    @Column(name = "modified_user")
    private String modifiedUser;

    @Column(name = "status")
    private String status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "process_id", insertable = false, updatable = false)
    @JsonIgnore
    private BpmProcdef bpmProcdef;

    @Column(name = "hide_all_user")
    private Boolean hideAllUser;

    @Column(name = "visible_all_user")
    private Boolean visibleAllUser;

    @Column(name = "submission_type")
    private Long submissionType;

    @Column(name = "visible_chart")
    private String visibleChart;

    @Column(name = "hide_chart")
    private String hideChart;

    @Column(name = "special_flow")
    private Boolean specialFlow;

    @Column(name = "special_parent_id")
    private Long specialParentId;

    @Column(name = "special_company_code")
    private String specialCompanyCode;

    @Column(name = "parent_company_code")
    private String parentCompanyCode;

    @Column(name = "special_apply_for")
    private Boolean specialApplyFor;

    @OneToMany(mappedBy = "servicePackage")
    @JsonIgnore
    private Set<PermissionDataManagement> permissionDataManagements;

    @OneToMany(mappedBy = "servicePackage")
    @JsonIgnore
    private Set<BpmProcInst> bpmProcInsts;

    @Column(name = "company_code")
    private String companyCode;
    @Column(name = "company_name")
    private String companyName;

    @Column(name = "legislative_requirement")
    private Boolean legislativeRequirement;

}

