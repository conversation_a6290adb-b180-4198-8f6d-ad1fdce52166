package vn.fis.eapprove.business.domain.api.service;


import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import vn.fis.eapprove.business.domain.api.entity.ApiManagement;
import vn.fis.eapprove.business.domain.api.repository.ApiManagementRepository;
import vn.fis.eapprove.business.domain.authority.service.*;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefApi;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcdefApiRepository;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.repository.ShareUserRepository;
import vn.fis.eapprove.business.domain.sharedUser.service.ShareUserManager;
import vn.fis.eapprove.business.domain.system.repository.SystemGroupRepository;
import vn.fis.eapprove.business.dto.PageSearchDto;
import vn.fis.eapprove.business.exception.ErrorMessage;
import vn.fis.eapprove.business.model.request.ApiManagementFilterRequest;
import vn.fis.eapprove.business.model.request.ApiManagementRequest;
import vn.fis.eapprove.business.model.response.ApiManagementResponse;
import vn.fis.eapprove.business.model.response.CountApiManagementResponse;
import vn.fis.eapprove.business.model.response.NameAndCodeCompanyResponse;
import vn.fis.eapprove.business.specification.ApiActionSpecification;

import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.business.utils.TimeUtils;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.MapKeyEnum;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ShareUserTypeEnum;
import vn.fis.spro.common.util.QueryUtils;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.Tuple;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static vn.fis.eapprove.business.constant.Constant.*;


@Slf4j
@Service("ApiManagerV1")
@Transactional
public class ApiManager {
    @Autowired
    ResponseUtils responseUtils;
    @Autowired
    ApiActionSpecification apiActionSpecification;
    @Autowired
    RestTemplate restTemplate;
    @Autowired
    SproProperties sproProperties;
    @Autowired
    ApiManagementRepository apiManagementRepository;
    @Autowired
    ModelMapper modelMapper;
    @Autowired
    CustomerService customerService;
    @Autowired
    MessageSource messageSource;
    @Autowired
    CredentialHelper credentialHelper;
    @Autowired
    ShareUserManager shareUserManager;
    @Autowired
    ShareUserRepository shareUserRepository;
    @Autowired
    private BpmProcdefApiRepository bpmProcdefApiRepository;
    @Autowired
    private PermissionDataManagementRepository permissionDataManagementRepository;

    @Autowired
    private AuthService authService;

    @Value("${app.superAdmin.account}")
    private String appSuperAdminAccount;
    @Autowired
    private SystemGroupRepository systemGroupRepository;

    public List<ApiManagementResponse> getAll() {
        try {
            List<ApiManagement> apiManagement = apiManagementRepository.findAll();
            List<ApiManagementResponse> listRp = apiManagement.stream().map(x -> modelMapper.map(x, ApiManagementResponse.class)).collect(Collectors.toList());
            return listRp;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public PageSearchDto searchApiAction(ApiManagementFilterRequest criteria) {
        try {
            int pageNum = criteria.getPage() - 1;
            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());

            String username = credentialHelper.getJWTPayload().getUsername();
            // Lấy list companyCode cấu hình QL vai trò người dùng
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
            criteria.setListCompanyCode(lstCompanyCode);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.API_MANAGEMENT.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                criteria.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            Page<ApiManagement> page = apiManagementRepository.findAll(
                    apiActionSpecification.filter(criteria, username),
                    PageRequest.of(pageNum, criteria.getLimit(),
                            sort));
            List<ApiManagementResponse> apiManagementResponses = new ArrayList<>();

            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.API_MANAGEMENT.code);

            // Lấy list share user
            List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceType(ShareUserTypeEnum.APIMANAGEMENT.type);

            List<ApiManagement> resultList = page.getContent();
            if(!resultList.isEmpty()) {
                List<Tuple> countList = apiManagementRepository.countInProcDef(resultList.stream().map(ApiManagement::getId).collect(Collectors.toList()));
                List<CountApiManagementResponse> countListProcdef = new ArrayList<>();
                if(!countList.isEmpty()){
                    countList.forEach(i->{
                        countListProcdef.add(new CountApiManagementResponse(
                                Objects.requireNonNull(QueryUtils.getValueFromTuple(i, "id", Integer.class)).longValue(),
                                QueryUtils.getValueFromTuple(i,"name",String.class)
                        ));
                    });

                }
                for (ApiManagement management : resultList) {

                    List<String> listEmail = new ArrayList<>();
                    if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                        listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(management.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
                    }

                    List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(management.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());

                    List<CountApiManagementResponse> count = countListProcdef.stream().filter(i -> i.getId().equals(management.getId())).collect(Collectors.toList());
                    ApiManagementResponse apiManagementResponse = ApiManagementResponse.builder()
                            .id(management.getId())
                            .name(management.getName())
                            .body(ValidationUtils.isNullOrEmpty(management.getBody()) ? "" : management.getBody())
                            .header(ValidationUtils.isNullOrEmpty(management.getHeader()) ? "" : management.getHeader())
                            .type(management.getType())
                            .url(management.getUrl())
                            .tokenAttribute(management.getTokenAttribute())
                            .authenApiId(management.getAuthenApiId())
                            .method(management.getMethod())
                            .shareWith(listEmail)
                            .status(management.getStatus())
                            .baseUrlId(management.getBaseUrlId())
                            .description(ValidationUtils.isNullOrEmpty(management.getDescription()) ? "" : management.getDescription())
                            .createdUser(management.getCreatedUser())
                            .createdTime(TimeUtils.localDateTimeToString(management.getCreatedTime(), LOCALFORMAT))
                            .updatedUser(management.getUpdatedUser())
                            .updatedTime(management.getUpdatedTime() == null ? "" : TimeUtils.localDateTimeToString(management.getUpdatedTime(), LOCALFORMAT))
                            .response(ValidationUtils.isNullOrEmpty(management.getResponse()) ? "" : management.getResponse())
                            .returnResponse(management.getReturnResponse())
                            .errorAttribute(ValidationUtils.isNullOrEmpty(management.getErrorAttribute()) ? "" : management.getErrorAttribute())
                            .successCondition(ValidationUtils.isNullOrEmpty(management.getSuccessCondition()) ? "" : management.getSuccessCondition())
                            .continueOnError(management.getContinueOnError())
                            .applyFor(companyCodes)
                            .companyCode(management.getCompanyCode())
                            .companyName(management.getCompanyName())
                            .procDefApplyName(count.stream().map(CountApiManagementResponse::getName).distinct().collect(Collectors.toList()))
                            .build();
                    apiManagementResponses.add(apiManagementResponse);

                }
            }
//            if(criteria.getShareWith() != null && !criteria.getShareWith().contains(CommonConstants.FILTER_SELECT_ALL)){
//                apiManagementResponses = apiManagementResponses.stream().filter(i->criteria.getShareWith().contains(i)).collect(Collectors.toList());
//            }
            return PageSearchDto.builder().content(apiManagementResponses)
                    .number(page.getNumber() + 1)
                    .numberOfElements(page.getNumberOfElements())
                    .page(page.getNumber() + 1)
                    .limit(page.getSize())
                    .totalPages(page.getTotalPages())
                    .totalElements(page.getTotalElements())
                    .sortBy(criteria.getSortBy())
                    .sortType(criteria.getSortType())
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }


    public List<ApiManagementResponse> searchFilter(ApiManagementFilterRequest criteria) {
        try {
            String username = credentialHelper.getJWTPayload().getUsername();
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
            criteria.setListCompanyCode(lstCompanyCode);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.API_MANAGEMENT.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                criteria.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            List<ApiManagement> page = apiManagementRepository.findAll(apiActionSpecification.filter(criteria, username));
            List<ApiManagementResponse> apiManagementResponses = new ArrayList<>();

            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.API_MANAGEMENT.code);

            // Lấy list share user
            List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceType(ShareUserTypeEnum.APIMANAGEMENT.type);

            for (ApiManagement management : page) {

                List<String> listEmail = new ArrayList<>();
                if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                    listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(management.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
                }

                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(management.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());

                ApiManagementResponse apiManagementResponse = ApiManagementResponse.builder()
                        .id(management.getId())
                        .name(management.getName())
                        .body(ValidationUtils.isNullOrEmpty(management.getBody()) ? "" : management.getBody())
                        .header(ValidationUtils.isNullOrEmpty(management.getHeader()) ? "" : management.getHeader())
                        .type(management.getType())
                        .url(management.getUrl())
                        .tokenAttribute(management.getTokenAttribute())
                        .authenApiId(management.getAuthenApiId())
                        .method(management.getMethod())
                        .shareWith(listEmail)
                        .status(management.getStatus())
                        .baseUrlId(management.getBaseUrlId())
                        .description(ValidationUtils.isNullOrEmpty(management.getDescription()) ? "" : management.getDescription())
                        .createdUser(management.getCreatedUser())
                        .createdTime(TimeUtils.localDateTimeToString(management.getCreatedTime(), LOCALFORMAT))
                        .updatedUser(management.getUpdatedUser())
                        .updatedTime(management.getUpdatedTime() == null ? "" : TimeUtils.localDateTimeToString(management.getUpdatedTime(), LOCALFORMAT))
                        .response(ValidationUtils.isNullOrEmpty(management.getResponse()) ? "" : management.getResponse())
                        .returnResponse(management.getReturnResponse())
                        .errorAttribute(ValidationUtils.isNullOrEmpty(management.getErrorAttribute()) ? "" : management.getErrorAttribute())
                        .successCondition(ValidationUtils.isNullOrEmpty(management.getSuccessCondition()) ? "" : management.getSuccessCondition())
                        .continueOnError(management.getContinueOnError())
                        .applyFor(companyCodes)
                        .companyCode(management.getCompanyCode())
                        .companyName(management.getCompanyName())
                        .build();
                apiManagementResponses.add(apiManagementResponse);

            }
            return apiManagementResponses;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    public String createAction(ApiManagementRequest apiManagementRequest) {
        try {
            if (!ValidationUtils.isNullOrEmpty(apiManagementRequest.getStatus())) {
                ApiManagement management = new ApiManagement();
                management.setName(apiManagementRequest.getName());
                management.setDescription(apiManagementRequest.getDescription());
                management.setUrl(apiManagementRequest.getUrl());
                management.setAuthenApiId(apiManagementRequest.getAuthenApiId());
                management.setBaseUrlId(apiManagementRequest.getBaseUrlId());
                management.setMethod(apiManagementRequest.getMethod());
                management.setHeader(apiManagementRequest.getHeader());
                management.setType(apiManagementRequest.getType());
                management.setBody(apiManagementRequest.getBody());
                management.setTokenAttribute(apiManagementRequest.getTokenAttribute());
                management.setStatus(apiManagementRequest.getStatus());
                management.setResponse(apiManagementRequest.getResponse());
                management.setReturnResponse(apiManagementRequest.getReturnResponse());
                management.setErrorAttribute(apiManagementRequest.getErrorAttribute());
                management.setSuccessCondition(apiManagementRequest.getSuccessCondition());
                management.setContinueOnError(apiManagementRequest.getContinueOnError());

                List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
                for(NameAndCodeCompanyResponse response : listCompanyCodeAndName){
                    management.setCompanyCode(response.getCompanyCode());
                    management.setCompanyName(response.getCompanyName());
                }

                management.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                management.setCreatedTime(LocalDateTime.now());
                management.setIsDeleted(0);
                apiManagementRepository.save(management);

                if (!ValidationUtils.isNullOrEmpty(apiManagementRequest.getApplyFor())) {
                    List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                    for (String data : apiManagementRequest.getApplyFor()) {
                        PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                        permissionDataManagement.setTypeId(management.getId());
                        permissionDataManagement.setTypeName(PermissionDataConstants.Type.API_MANAGEMENT.code);
                        permissionDataManagement.setCompanyCode(data);
                        permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                        permissionDataManagement.setCreatedTime(LocalDateTime.now());
                        permissionDataManagements.add(permissionDataManagement);
                    }

                    permissionDataManagementRepository.saveAll(permissionDataManagements);
                }

                // Lưu người được chia sẻ
                if (!ValidationUtils.isNullOrEmpty(apiManagementRequest.getShareWith())) {
                    List<SharedUser> sharedUsers = new ArrayList<>();
                    for (String shareWith : apiManagementRequest.getShareWith()) {
                        SharedUser sharedUser = new SharedUser();
                        sharedUser.setReferenceId(management.getId());
                        sharedUser.setReferenceType(ShareUserTypeEnum.APIMANAGEMENT.type);
                        sharedUser.setEmail(shareWith);
                        sharedUsers.add(sharedUser);
                    }
                    shareUserRepository.saveAll(sharedUsers);
                }

                return MESSAGE_SUCCESS;
            }


        } catch (Exception e) {
            e.printStackTrace();
            return MESSAGE_FAIL;
        }
        return MESSAGE_FAIL;
    }

    public String updateApiAction(ApiManagementRequest apiManagementRequest) {

        try {
            if (!ValidationUtils.isNullOrEmpty(apiManagementRequest.getStatus())) {
                ApiManagement management = apiManagementRepository.findById(apiManagementRequest.getId()).get();
                management.setName(apiManagementRequest.getName());
                management.setDescription(apiManagementRequest.getDescription());
                management.setUrl(apiManagementRequest.getUrl());
                management.setType(apiManagementRequest.getType());
                management.setAuthenApiId(apiManagementRequest.getAuthenApiId());
                management.setBaseUrlId(apiManagementRequest.getBaseUrlId());
                management.setMethod(apiManagementRequest.getMethod());
                management.setHeader(apiManagementRequest.getHeader());
                management.setBody(apiManagementRequest.getBody());
                management.setTokenAttribute(apiManagementRequest.getTokenAttribute());
                management.setStatus(apiManagementRequest.getStatus());
                management.setResponse(apiManagementRequest.getResponse());
                management.setReturnResponse(apiManagementRequest.getReturnResponse());
                management.setErrorAttribute(apiManagementRequest.getErrorAttribute());
                management.setSuccessCondition(apiManagementRequest.getSuccessCondition());
                management.setContinueOnError(apiManagementRequest.getContinueOnError());
                management.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
                management.setUpdatedTime(LocalDateTime.now());
                apiManagementRepository.save(management);

                // Lưu phân quyền dữ liệu
                // Xóa data cũ
                List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(management.getId(), PermissionDataConstants.Type.API_MANAGEMENT.code);
                if (!ValidationUtils.isNullOrEmpty(oldData)) {
                    permissionDataManagementRepository.deleteAll(oldData);
                }
                if (!ValidationUtils.isNullOrEmpty(apiManagementRequest.getApplyFor())) {
                    List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                    for (String data : apiManagementRequest.getApplyFor()) {
                        PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                        permissionDataManagement.setTypeId(management.getId());
                        permissionDataManagement.setTypeName(PermissionDataConstants.Type.API_MANAGEMENT.code);
                        permissionDataManagement.setCompanyCode(data);
                        permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                        permissionDataManagement.setCreatedTime(LocalDateTime.now());
                        permissionDataManagements.add(permissionDataManagement);
                    }

                    permissionDataManagementRepository.saveAll(permissionDataManagements);
                }

                // share user
                shareUserRepository.deleteAllByReferenceIdAndReferenceType(apiManagementRequest.getId(), ShareUserTypeEnum.APIMANAGEMENT.type);
                if (!ValidationUtils.isNullOrEmpty(apiManagementRequest.getShareWith())) {
                    List<SharedUser> sharedUsers = new ArrayList<>();
                    for (String shareWith : apiManagementRequest.getShareWith()) {
                        SharedUser sharedUser = new SharedUser();
                        sharedUser.setReferenceId(management.getId());
                        sharedUser.setReferenceType(ShareUserTypeEnum.APIMANAGEMENT.type);
                        sharedUser.setEmail(shareWith);
                        sharedUsers.add(sharedUser);
                    }
                    shareUserRepository.saveAll(sharedUsers);
                }

                return MESSAGE_SUCCESS;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return MESSAGE_FAIL;
        }
        return MESSAGE_FAIL;

    }

    public ApiManagement cloneApiAction(Long id) {
        try {
            ApiManagement apiManagement = apiManagementRepository.findById(id).get();
            String shareWith = apiManagement.getShareWith();
            ApiManagement apiManagementCopy = new ApiManagement();
            String name = null;
            ApiManagement apiManagement2 = apiManagementRepository.findTopByNameLikeOrderByIdDesc(apiManagement.getName() + " - copy" + "%").orElse(null);
            if (apiManagement2 != null) {
                name = apiManagement2.getName();
            }
            if (name == null) {
                apiManagementCopy.setName(apiManagement.getName() + " - copy");
                if (apiManagementCopy.getName().length() > 100) {
                    return null;
                }
            } else {
                String numRegex = null;
                try {
                    Pattern pattern = Pattern.compile("^(.*)\\((\\d+)\\)$");
                    Matcher matcher = pattern.matcher(name);

                    if (matcher.find()) {
                        numRegex = matcher.group(2);
                    } else {
                        numRegex = "1";
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                if (numRegex != null) {
                    int num = Integer.parseInt(numRegex);
                    apiManagementCopy.setName(apiManagement.getName() + " - copy(" + (num + 1) + ")");
                }
            }
            apiManagementCopy.setDescription(apiManagement.getDescription());
            apiManagementCopy.setType(apiManagement.getType());
            apiManagementCopy.setUrl(apiManagement.getUrl());
            apiManagementCopy.setMethod(apiManagement.getMethod());
            apiManagementCopy.setHeader(apiManagement.getHeader());
            apiManagementCopy.setBody(apiManagement.getBody());
            apiManagementCopy.setTokenAttribute(apiManagement.getTokenAttribute());
            apiManagementCopy.setStatus(1);
            apiManagementCopy.setShareWith(shareWith);
            apiManagementCopy.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            apiManagementCopy.setCreatedTime(LocalDateTime.now());
            apiManagementCopy.setResponse(apiManagement.getResponse());
            apiManagementCopy.setReturnResponse(apiManagement.getReturnResponse());
            apiManagementCopy.setErrorAttribute(apiManagement.getErrorAttribute());
            apiManagementCopy.setSuccessCondition(apiManagement.getSuccessCondition());
            apiManagementCopy.setContinueOnError(apiManagement.getContinueOnError());
            apiManagementCopy.setIsDeleted(apiManagement.getIsDeleted());
            apiManagementCopy = apiManagementRepository.save(apiManagementCopy);

            List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(apiManagement.getId(), PermissionDataConstants.Type.API_MANAGEMENT.code);
//            if (!ValidationUtils.isNullOrEmpty(oldData)) {
//                permissionDataManagementRepository.deleteAll(oldData);
//            }
            if(!ValidationUtils.isNullOrEmpty(oldData)) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (PermissionDataManagement data : oldData) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(apiManagementCopy.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.API_MANAGEMENT.code);
                    permissionDataManagement.setCompanyCode(data.getCompanyCode());
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }
                permissionDataManagementRepository.saveAll(permissionDataManagements);
            }
            return apiManagementCopy;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String deleteApiAction(List<Long> listId) {
        try {
            Boolean check = false;
            List<BpmProcdefApi> bpmProcdefApis = bpmProcdefApiRepository.findAllByApiIdIn(listId);
            List<Long> listApiBpmProcdef = bpmProcdefApis.stream().map(BpmProcdefApi::getApiId).collect(Collectors.toList());
            for (Long idApiAction : listId) {
                Optional<Long> result = listApiBpmProcdef.stream().filter(idApi -> Objects.equals(idApi, idApiAction)).findAny();
                List<ApiManagement> resultBaseUrlId = apiManagementRepository.findBaseUrlId(idApiAction);
                if (!ValidationUtils.isNullOrEmpty(resultBaseUrlId)) {
                    return String.format("baseURL đã được sử dụng ở bản ghi NORMAL");
                }
                if (result.isPresent()) {
                    check = true;
                }
                if (check) {
                    return String.format("API có id = %d đã được gắn trong quy trình, không thể vô hiệu hóa", idApiAction);
                } else {
                    ApiManagement apiManagers = apiManagementRepository.findById(idApiAction).orElse(null);
                    if(apiManagers != null) {
                        apiManagers.setStatus(0);
                        apiManagers.setUpdatedTime(LocalDateTime.now());
                        apiManagers.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
                    }
                    apiManagementRepository.save(apiManagers);
                }
            }
            return messageSource.getMessage("message.api-management.deleteApiAction.success", null, Locale.getDefault());
        } catch (Exception e) {
            log.info("Delete failed");
            return null;
        }
    }

    public void active(List<Long> listId)  {
            String loginUser = credentialHelper.getJWTPayload().getUsername();
            List<ApiManagement> apiList = apiManagementRepository.findAllByIdInAndStatus(listId,0);
            apiList.forEach(api-> {
                api.setStatus(1);
                api.setUpdatedTime(LocalDateTime.now());
                api.setUpdatedUser(loginUser);
            });
            apiManagementRepository.saveAll(apiList);

    }

    public boolean CheckExistName(String nameApiAction) {
        try {
            List<ApiManagement> apiManagements = apiManagementRepository.CheckExistName(nameApiAction);
            if (apiManagements.size() > 0) {
                return true;
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public Boolean checkNameExits(String nameApiAction, Long id) {
        if (id != null) {
            ApiManagement apiManagement = apiManagementRepository.findById(id).get();
            if (!nameApiAction.equalsIgnoreCase(apiManagement.getName())) {
                if (CheckExistName(nameApiAction)) {
                    return true;
                }
            }

        } else {
            if (CheckExistName(nameApiAction)) {
                return true;
            }
        }
        return false;
    }

    public List<ApiManagement> listBaseUrl() {
        List<ApiManagement> apiManagement = apiManagementRepository.listBaseUrl();
        return apiManagement;
    }

    public List<ApiManagement> listApiAuthen() {
        List<ApiManagement> apiManagement = apiManagementRepository.listApiAuthen();
        return apiManagement;
    }

    public Set<String> shareWithApiAction() {
        try {
            String token = credentialHelper.getJWTToken();
            //String realm = credentialHelper.getRealm();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("key_role", "public");
            //headers.set("realm", realm);
            headers.set("Authorization", "Bearer " + token);
            HttpEntity<String> requestEntity = new HttpEntity<String>(headers);
            ResponseEntity<List<String>> responseCustomer = restTemplate.exchange(sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key) + "/groupUser/shareWith", HttpMethod.GET, requestEntity, new ParameterizedTypeReference<List<String>>() {
            });

            List<String> listCus = responseCustomer.getBody();

            if (CollectionUtils.isEmpty(listCus)) {
                throw new ErrorMessage("");
            }
            return listCus.stream().collect(Collectors.toSet());
        } catch (Exception e) {
            throw new ErrorMessage("");
        }
    }

    public Object getAllSystemGroup()  {
        List<Map<String, Object>> lstResponse = new ArrayList<>();
        ApiManagementFilterRequest criteria = new ApiManagementFilterRequest();
        criteria.setPage(1);
        criteria.setLimit(999999);
        criteria.setSortBy("id");
        criteria.setSortType("DESC");

        int pageNum = criteria.getPage() - 1;
        Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());

        String username = credentialHelper.getJWTPayload().getUsername();
        // Lấy list companyCode cấu hình QL vai trò người dùng
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);
        criteria.setListCompanyCode(lstCompanyCode);
        // Phân quyền theo nhóm
        List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.API_MANAGEMENT.tableName, username);
        // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
        if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
            criteria.setLstGroupPermissionId(lstGroupPermissionId);
            // get list company code role admin member
            List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
            criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
        }

        Page<ApiManagement> page = apiManagementRepository.findAll(
                apiActionSpecification.filter(criteria, username),
                PageRequest.of(pageNum, criteria.getLimit(), sort));
        List<ApiManagement> lstApi = page.getContent();
        for (ApiManagement apiManagement : lstApi) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", apiManagement.getId());
            map.put("name", apiManagement.getName());
            map.put("companyCode", apiManagement.getCompanyCode());
            map.put("companyName", apiManagement.getCompanyName());

            lstResponse.add(map);
        }

        return lstResponse;
    }
}