package vn.fis.eapprove.business.domain.sequence.repository;

import jakarta.persistence.LockModeType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.sequence.entity.SequenceData;


@Repository
public interface SequenceDataRepository extends JpaRepository<SequenceData, Long>, JpaSpecificationExecutor<SequenceData> {

    @Query("SELECT c FROM SequenceData c WHERE c.sequenceName =:sequenceName")
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    SequenceData getSequenceDataInfo(@Param("sequenceName") String sequenceName);
}
