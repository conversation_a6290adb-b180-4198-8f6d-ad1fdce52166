package vn.fis.eapprove.business.domain.bpm.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import vn.fis.eapprove.business.domain.changeAssignee.entity.ChangeAssigneeHistory;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@Entity
@Table(name = "bpm_task")
public class BpmTask implements Serializable {

    private static final long serialVersionUID = 1641329166414707620L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "TASK_ID", nullable = false)
    private String taskId;

    @Column(name = "EXECUTION_ID")
    private String taskExecutionId;

    @Column(name = "PROC_INST_ID")
    private String taskProcInstId;

    @Column(name = "PROC_DEF_ID")
    private String taskProcDefId;

    @Column(name = "CASE_INST_ID")
    private String taskCaseInstId;

    @Column(name = "TASK_DEF_KEY")
    private String taskDefKey;

    @Column(name = "NAME")
    private String taskName;

    @Column(name = "SIGN_STATUS")
    private Integer signStatus;

    @Column(name = "PRIORITY")
    private String taskPriority;

    @Column(name = "ASSIGNEE")
    private String taskAssignee;

    @Column(name = "CREATED_TIME")
    private LocalDateTime taskCreatedTime;

    @Column(name = "STARTED_TIME")
    private LocalDateTime taskStartedTime;

    @Column(name = "SLA_FINISH")
    private Double slaFinish;

    @Column(name = "SLA_RESPONSE")
    private Double slaResponse;

    @Column(name = "FINISHED_TIME")
    private LocalDateTime taskFinishedTime;

    @Column(name = "SLA_RESPONSE_TIME")
    private LocalDateTime slaResponseTime;

    @Column(name = "SLA_FINISH_TIME")
    private LocalDateTime slaFinishTime;

    @Column(name = "RESPONSE_DURATION")
    private Long responseDuration;

    @Column(name = "FINISH_DURATION")
    private Long finishDuration;

    @Column(name = "DONE_TIME")
    private Double taskDoneTime;

    @Column(name = "TYPE")
    private String taskType;

    @Column(name = "CREATED_USER")
    private String taskCreatedUser;

    @Column(name = "STATUS")
    private String taskStatus;

    @Column(name = "IS_FIRST")
    private boolean taskIsFirst;

    @Column(name = "ASSIGN_TYPE")
    private Boolean assignType;

    @Column(name = "ACTION_USER")
    private String actionUser;

    @Column(name = "template_version_id")
    private Long templateVersionId;

    @Column(name = "parent_template_version_id")
    private Long parentTemplateVersionId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PROC_INST_ID", referencedColumnName = "PROC_INST_ID", insertable = false, updatable = false)
    @JsonIgnore
    private BpmProcInst bpmProcInst;

    @OneToMany(mappedBy = "bpmTask")
    @JsonIgnore
    private Set<BpmTaskUser> bpmTaskUsers;

    @OneToMany(mappedBy = "bpmTask", fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<ChangeAssigneeHistory> changeAssigneeHistories;

//    @OneToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "task_id", referencedColumnName = "task_id", insertable = false, updatable = false)
//    @JsonIgnore
//    private AuthorityManagement authorityManagement;
}
