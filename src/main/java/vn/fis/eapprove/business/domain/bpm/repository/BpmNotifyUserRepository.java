package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmNotifyUser;


import java.util.List;

@Repository
public interface BpmNotifyUserRepository extends JpaRepository<BpmNotifyUser, Long>, JpaSpecificationExecutor<BpmNotifyUser> {
    @Query("SELECT CONCAT(a.recipient, '_', a.type) as recipient FROM BpmNotifyUser a WHERE a.ticketId = :ticketId")
    List<String> getAllUserNotificationByTicket(@Param("ticketId") Long ticketId);
}
