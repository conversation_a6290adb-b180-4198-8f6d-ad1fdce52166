package vn.fis.eapprove.business.domain.currency.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.currency.entity.Currency;


/**
 * Author: AnhVTN
 * Date: 31/03/2023
 */

@Repository
public interface CurrencyRepository extends JpaRepository<Currency, Long>, JpaSpecificationExecutor<Currency> {
    Currency findCurrencyByCode(String code);

    Currency findCurrencyByName(String name);

}
