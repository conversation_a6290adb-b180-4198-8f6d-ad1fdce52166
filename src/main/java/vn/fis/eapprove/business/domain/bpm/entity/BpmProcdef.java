package vn.fis.eapprove.business.domain.bpm.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@Entity
@Table(name = "bpm_procdef")
public class BpmProcdef implements Serializable {

    private static final long serialVersionUID = -4548547753069827382L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "[ID]")
    private Long id;

    @Column(name = "[PROC_DEF_ID]")
    private String procDefId;

    @Column(name = "[NAME]")
    private String name;

    @Column(name = "[TYPE]")
    private Integer processType;

    @Column(name = "[DESCRIPTION]")
    private String description;

    @Column(name = "[KEY]")
    private String key;

    @Column(name = "[DEPLOYMENT_ID]")
    private String deploymentId;

    @Column(name = "[RESOURCE_NAME]")
    private String resourceName;

    @Column(name = "[BYTES]")
    private byte[] bytes;

    @Column(name = "[PRIORITIZED]")
    private String prioritized;

    @Column(name = "[USER_CREATED]")
    private String userCreated;

    @Column(name = "[CREATED_DATE]")
    private LocalDateTime createdDate;

    @Column(name = "[USER_UPDATED]")
    private String userUpdate;

    @Column(name = "[UPDATED_DATE]")
    private LocalDateTime updatedDate;

    @Column(name = "[AUTO_CLOSED]")
    private Double autoClose;

    @Column(name = "[AUTO_CANCEL]")
    private Double autoCancel;

    @Column(name = "[STATUS]")
    private String status;

    @Column(name = "[STEP_BY_STEP_RESULTS_FOR_CREATED]")
    private Boolean stepByStepResultForCreate;

    @Column(name = "[INFORM_TO]")
    private Boolean inFormTo;

    @Column(name = "[LOCATION]")
    private Boolean location;

    @Column(name = "[UPDATE]")
    private Boolean update;

    @Column(name = "[REQUEST_UPDATE]")
    private Boolean requestUpdate;

    @Column(name = "[HIDE_RU_TASKS]")
    private String hideRuTasks;

    @Column(name = "[CREATE_NEW_AND_DOUBLE]")
    private Boolean createNewAndDouble;

    @Column(name = "[AUTO_INHERITS]")
    private Boolean autoInherits;

    @Column(name = "[OFF_NOTIFICATION]")
    private Boolean offNotification;

    @Column(name = "[PARENTS_ID]")
    private Long parentsId;

    @Column(name = "[SERVICE_COUNT]")
    private Long serviceCount;

    @Column(name = "[PRIORITY_ID]")
    private Long priorityId;

    @Column(name = "[CANCEL]")
    private Boolean cancel;

    @Column(name = "[SHOW_INFO]")
    private Boolean showInfo;

    @Column(name = "[HIDE_INFO]")
    private Boolean hideInfo;
    @Column(name = "[IS_ASSISTANT]")
    private Boolean isAssistant;

    @Column(name = "[IS_EDIT_ASSISTANT]")
    private Boolean isEditAssistant;

    @Column(name = "[HIDE_INFO_TASKS]")
    private String hideInfoTasks;

    @Column(name = "[show_info_tasks]")
    private String showInfoTaks;
    @Column(name = "[IS_AUTO_CANCEL]")
    private Boolean isAutoCancel;

    @Column(name = "[ADDITIONAL_REQUEST]")
    private Boolean additionalRequest;

    @Column(name = "[CANCEL_TASKS]")
    private String cancelTasks;

    @Column(name = "[HIDE_RELATED_TICKET]")
    private Boolean hideRelatedTicket;

    @Column(name = "[HIDE_RELATED_TICKET_VALUE]")
    private String hideRelatedTicketValue;

    @Column(name = "[AUTHORITY_ON_TICKET]")
    private Boolean authorityOnTicket;

    @Column(name = "[AUTHORITY_ON_TICKET_VALUE]")
    private String authorityOnTicketValue;

//    @Column(name = "[CHANGE_IMPLEMENTER]")
//    private Boolean changeImplementer;

    @Column(name = "[CHANGE_IMPLEMENTER_VALUE]")
    private String changeImplementerValue;

    @Column(name = "[AUTHORITY_ON_TICKET_STEP]")
    private String authorityOnTicketStep;

    @Column(name = "[RECALL]")
    private Boolean recall;

    @Column(name = "[SHOW_INPUT_TASK]")
    private Boolean showInputTask;

    @Column(name = "[SHOW_INPUT_TASK_DEF_KEYS]")
    private String showInputTaskDefKeys;

    @Column(name = "[HIDE_INHERIT]")
    private Boolean hideInherit;

    @Column(name = "[HIDE_INHERIT_TASKS]")
    private String hideInheritTasks;

    @Column(name = "[HIDE_COMMENT]")
    private Boolean hideComment;

    @Column(name = "[HIDE_COMMENT_TASKS]")
    private String hideCommentTasks;

    @Column(name = "[SPECIAL_FLOW]")
    private Boolean specialFlow;

    @Column(name = "SPECIAL_PARENT_ID")
    private Long specialParentId;

    @Column(name = "SPECIAL_COMPANY_CODE")
    private String specialCompanyCode;

    @Column(name = "[SPECIAL_FORM_KEY]")
    private String specialFormKey;

    @Column(name = "[SPECIAL_PARENT_SERVICE_ID]")
    private Long specialParentServiceId;

    @Column(name = "[HIDE_DOWNLOAD]")
    private Boolean hideDownload;

    @Column(name = "[HIDE_DOWNLOAD_TASKS]")
    private String hideDownloadTasks;

    @Column(name = "[HIDE_SHARE_TICKET]")
    private Boolean hideShareTicket;

    @Column(name = "auto_complete_task")
    private Boolean autoCompleteTask;

    @OneToMany(mappedBy = "bpmProcdef")
    @JsonIgnore
    private Set<ServicePackage> servicePackages;

    @OneToMany(mappedBy = "bpmProcdef")
    @JsonIgnore
    private Set<BpmOwnerProcess> bpmOwnerProcesses;

    @OneToMany(mappedBy = "bpmProcdef")
    @JsonIgnore
    private Set<PermissionDataManagement> permissionDataManagements;

    @OneToMany(mappedBy = "bpmProcdef")
    @JsonIgnore
    private Set<SharedUser> sharedUsers;

    @Column(name = "company_code")
    private String companyCode;
    @Column(name = "company_name")
    private String companyName;

    @Column(name = "disable_approved_ticket")
    private Boolean disableApprovedTicket;

    @Column(name = "warning_approved_ticket")
    private Boolean warningApprovedTicket;

    @Column(name = "warning_approved_ticket_tasks")
    private String warningApprovedTicketTasks;

    @Column(name = "disabled_approved_ticket_tasks")
    private String disabledApprovedTicketTasks;

    @Column(name = "legislative_requirement")
    private Boolean legislativeRequirement;


}
