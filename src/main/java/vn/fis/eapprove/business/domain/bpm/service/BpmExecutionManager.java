package vn.fis.eapprove.business.domain.bpm.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.fis.eapprove.business.domain.bpm.entity.BpmExecution;
import vn.fis.eapprove.business.domain.bpm.repository.BpmExecutionRepository;
import vn.fis.eapprove.business.mapper.BpmExecutionMapper;


@Service("BpmExecutionManagerV1")
public class BpmExecutionManager {

    RestTemplate restTemplate = new RestTemplate();
    @Autowired
    private BpmExecutionRepository bpmExecutionRepository;
    @Autowired
    private BpmExecutionMapper bpmExecutionMapper;

    public void saveExecutionBPMDB(String id) {

        try {
            String serverUrlGetActRuExecution = "http://localhost:8888/actRuExecution/RuExecutionById/" + id;
            ResponseEntity<String> responseExecution = restTemplate.getForEntity(serverUrlGetActRuExecution, String.class);

            String jsonResponseExecution = new String((responseExecution.getBody()).getBytes());
            JSONObject jsonObjectExecution = new JSONObject(jsonResponseExecution);

            BpmExecution bpmExecution = new BpmExecution();
            bpmExecution.setId(jsonObjectExecution.getString("id"));
            bpmExecution.setProcInstId(jsonObjectExecution.getString("procInstId"));
            bpmExecution.setProcDefId(jsonObjectExecution.getString("procDefId"));
            bpmExecution.setActId(jsonObjectExecution.getString("actId"));
            bpmExecution.setActInstId(jsonObjectExecution.getString("actInstId"));
            bpmExecution.setIsActive(Integer.parseInt(jsonObjectExecution.getString("isActive")));
            bpmExecution.setIsConcurrent(Integer.parseInt(jsonObjectExecution.getString("git")));
            bpmExecution.setSequenceCounter(null);
            bpmExecution.setTenantId(null);

            bpmExecutionRepository.save(bpmExecution);
        } catch (Exception e) {

        }
    }
}
