package vn.fis.eapprove.business.domain.legislative.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.model.FileModel;

import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LegislativeSearchResponse {
    private Long id;
    private String name;
    private String type;
    private String status;
    private String description;
    private Long releaseYear;
    private String responsibleAgency;
    private List<FileModel> files;
    private Integer executionCount;
    private String approvalSession;
    private String reviewSession;
    private String createdUser;
    private String updatedUser;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    private String companyName;
    private List<String> processUsers;
    private Long ticketId;
    private String requestCode;
    private String procDefId;
    private String ticketStatus;
    private String activityStatus;
    private String startKey;
}
