package vn.fis.eapprove.business.domain.bpm.service.impl;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import vn.fis.eapprove.business.domain.assign.entity.AssignManagement;
import vn.fis.eapprove.business.domain.assign.service.AssignManager;
import vn.fis.eapprove.business.domain.authority.service.AuthorityManagementService;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTaskUser;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTaskUserRepository;
import vn.fis.eapprove.business.domain.bpm.service.BpmTaskManager;
import vn.fis.eapprove.business.domain.bpm.service.BpmTaskUserService;
import vn.fis.eapprove.business.domain.servicePackage.repository.ServicePackageRepository;
import vn.fis.eapprove.business.tenant.manager.*;
import vn.fis.spro.common.camunda.IdentityLink;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: PhucVM
 * Date: 23/11/2022
 */
@Slf4j
@Service("BpmTaskUserServiceImplV1")
@Transactional
public class BpmTaskUserServiceImpl implements BpmTaskUserService {

    private final BpmTaskUserRepository bpmTaskUserRepository;
    private final CamundaEngineService camundaEngineService;
    private final BpmTaskManager taskManagerService;
    private final AssignManager assignManager;
    private final AuthorityManagementService authorityManagementService;
    private final ServicePackageRepository servicePackageRepository;

    @Autowired
    public BpmTaskUserServiceImpl(BpmTaskUserRepository bpmTaskUserRepository,
                                  CamundaEngineService camundaEngineService,
                                  AssignManager assignManager,
                                  AuthorityManagementService authorityManagementService,
                                  BpmTaskManager taskManagerService, ServicePackageRepository servicePackageRepository) {
        this.bpmTaskUserRepository = bpmTaskUserRepository;
        this.camundaEngineService = camundaEngineService;
        this.taskManagerService = taskManagerService;
        this.assignManager = assignManager;
        this.authorityManagementService = authorityManagementService;
        this.servicePackageRepository = servicePackageRepository;
    }

    @Override
    public void updateNewUserName(String procInstId, String taskId, String oldUserName, String newUserName) {
        bpmTaskUserRepository.updateNewUserName(procInstId, taskId, oldUserName, newUserName);
    }

    @Override
    public void saveFromBpmTasks(List<BpmTask> bpmTasks) {
        if (!ValidationUtils.isNullOrEmpty(bpmTasks)) {
            StringBuilder procInstId = new StringBuilder();
            List<BpmTaskUser> bpmTaskUsers = new ArrayList<>();
            bpmTasks.forEach(task -> {
                if (task.getId() == null) {
                    return;
                }

                String assignee = task.getTaskAssignee();
                String taskId = task.getTaskId();
                if (procInstId.length() == 0) {
                    procInstId.append(task.getTaskProcInstId());
                }
                if (!ValidationUtils.isNullOrEmpty(assignee)) {
                    bpmTaskUsers.add(createBpmTaskUserFromBpmTask(task, assignee));
                } else {
                    // call camunda engine service to get identity-link users
                    List<IdentityLink> identityLinks = camundaEngineService.getIdentityLinks(taskId);
                    identityLinks.forEach(identity -> bpmTaskUsers.add(createBpmTaskUserFromBpmTask(task, identity.getUserId())));
                }
            });

            if (!ValidationUtils.isNullOrEmpty(bpmTaskUsers)) {
                // delete old records by procInstId before save
//                Set<String> taskDefKeys = bpmTaskUsers.stream().map(BpmTaskUser::getTaskDefKey).collect(Collectors.toSet());
//                deleteAllByTaskDefKeys(procInstId.toString(), taskDefKeys);

                saveAll(bpmTaskUsers);
            }
        }
    }

    /**
     * Change assignee user to authority user
     */
    @Override
    public void changeAssignUserAndUpdateBpmTask(List<BpmTask> bpmTasks, String ticketId, String procInstId, String serviceId)  {
        if (!ValidationUtils.isNullOrEmpty(bpmTasks)) {
            List<BpmTaskUser> bpmTaskUsers = new ArrayList<>();
            for (BpmTask task : bpmTasks) {
                if (task.getId() == null) {
                    return;
                }
                String assignee = task.getTaskAssignee();
                String taskId = task.getTaskId();
                if (!ValidationUtils.isNullOrEmpty(assignee) && serviceId != null) {
                    // get authority and check
                    List<AssignManagement> asmList = assignManager.findAssignManagementByAssignUser(assignee);
                    AssignManagement asm = null;
                    //Check child service -> If child => Check change implement by parent service
                    Long parentServiceId = servicePackageRepository.findIdParentServicePackage(Long.valueOf(serviceId));
                    for (AssignManagement asmObject : asmList) {
                        if (Arrays.asList(asmObject.getServiceRange().split(",")).contains(parentServiceId.toString())
                                && (asmObject.getStartDate().isBefore(LocalDate.now()) || asmObject.getStartDate().equals(LocalDate.now()))
                                && (asmObject.getEndDate().isAfter(LocalDate.now()) || asmObject.getEndDate().equals(LocalDate.now()))
                                && asmObject.getType() != 1 // Chỉ lấy ra type =! 1 (Tờ trình ủy quyền)
                                && (asmObject.getStatus() == 0 || asmObject.getStatus() == 1)
                        ) {
                            asm = asmObject;
                            break;
                        }
                    }
                    if (asm != null) {
                        Map<String, Object> body = new HashMap<>();
                        body.put("ticketId", procInstId);
                        body.put("taskId", task.getTaskId());
                        body.put("taskDefKey", task.getTaskDefKey());
                        body.put("email", asm.getAssignedUser());
                        body.put("assignTicketId", asm.getTicketId());
                        body.put("title", asm.getAssignTitle() != null ? asm.getAssignTitle() : "");
                        body.put("reason", "");
                        body.put("id", ticketId);
                        this.authorityManagementService.saveAuthority(ticketId, asm, task.getTaskId(), task.getTaskDefKey());
                        taskManagerService.changeImplementer(body, assignee, 1);
                    }
                }
//                else {
//                    // call camunda engine service to get identity-link users
//                    List<IdentityLink> identityLinks = camundaEngineService.getIdentityLinks(taskId);
//                    // crate a list to add new identityLinks
//                    List<IdentityLink> identityLinksAfterCheck = new ArrayList<>();
//
//                    //loop identityLinks for check and add new after add will delete old identityLink
//                    identityLinks.forEach(identityLink -> {
//                        // get authority and check
//                        List<AssignManagement> asmList = assignManager.findAssignManagementByAssignUser(identityLink.getUserId());
//                        bpmTaskUsers.add(createBpmTaskUserFromBpmTask(task, identityLink.getUserId()));
//                        AssignManagement asm = null;
//                        for (AssignManagement asmObject : asmList) {
//                            if (Arrays.asList(asmObject.getServiceRange().split(",")).contains(serviceId)
//                                    && (asmObject.getStartDate().isBefore(LocalDate.now()) || asmObject.getStartDate().equals(LocalDate.now()))
//                                    && asmObject.getEndDate().isAfter(LocalDate.now())
//                            ) {
//                                asm = asmObject;
//                                break;
//                            }
//                        }
//                        if (asm != null) {
//                            //  task được uỷ quyền
////                            task.setAssignType(true);
//                            identityLinksAfterCheck.add(
//                                    IdentityLink.builder()
//                                            .userId(asm.getAssignedUser())
//                                            .type(identityLink.getType())
//                                            .groupId(identityLink.getGroupId())
//                                            .build());
////                            this.authorityManagementService.saveAuthority(ticketId, asm, task.getTaskId(), task.getTaskDefKey());
//                            //delete current IndentityLink -> ko xóa user ủy quyền
////                            camundaEngineService.deleteIdentityLink(taskId, identityLink.getUserId(), identityLink.getGroupId(), identityLink.getType());
//                            // add new IdentityLink by new user id
//                            camundaEngineService.addIdentityLink(taskId, asm.getAssignedUser(), identityLink.getGroupId(), identityLink.getType());
//                        }
//                    });
//                    identityLinksAfterCheck.forEach(identity -> bpmTaskUsers.add(createBpmTaskUserFromBpmTask(task, identity.getUserId())));
//                }
            }

//            if (!ValidationUtils.isNullOrEmpty(bpmTaskUsers)) {
//                // delete old records by procInstId before save
//                Set<String> taskDefKeys = bpmTaskUsers.stream().map(BpmTaskUser::getTaskDefKey).collect(Collectors.toSet());
//                deleteAllByTaskDefKeys(procInstId, taskDefKeys);
//
//                saveAll(bpmTaskUsers);
//            }
        }
    }

    @Override
    public void saveFromBpmTask(BpmTask bpmTask) {
        saveFromBpmTasks(List.of(bpmTask));
    }

    private BpmTaskUser createBpmTaskUserFromBpmTask(BpmTask bpmTask, String userName) {
        BpmTaskUser bpmTaskUser = new BpmTaskUser();
        bpmTaskUser.setBpmTaskId(bpmTask.getId());
        bpmTaskUser.setTaskId(bpmTask.getTaskId());
        bpmTaskUser.setTaskDefKey(bpmTask.getTaskDefKey());
        bpmTaskUser.setTaskName(bpmTask.getTaskName());
        bpmTaskUser.setProcInstId(bpmTask.getTaskProcInstId());
        bpmTaskUser.setUserName(userName);

        return bpmTaskUser;
    }

    @Override
    public List<BpmTaskUser> getAllTaskUserByTaskIdIn(List<String> listId) {
        return bpmTaskUserRepository.getBpmTaskUserByTaskIdIn(listId);
    }

    @Override
    public List<BpmTaskUser> saveAll(List<BpmTaskUser> bpmTaskUsers) {
        return bpmTaskUserRepository.saveAll(bpmTaskUsers);
    }

    @Override
    public BpmTaskUser save(BpmTaskUser bpmTaskUser) {
        return bpmTaskUserRepository.save(bpmTaskUser);
    }

    @Override
    public void deleteAllByTaskIds(List<String> taskIds) {
        bpmTaskUserRepository.deleteAllByTaskIds(taskIds);
    }

    @Override
    public void deleteAllByTaskDefKeys(String procInstId, Set<String> taskDefKeys) {
        bpmTaskUserRepository.deleteAllByTaskDefKeys(procInstId, taskDefKeys);
    }

    @Override
    public List<BpmTaskUser> getAllByUserName(String userName) {
        return bpmTaskUserRepository.getBpmTaskUsersByUserName(userName);
    }

    @Override
    @Transactional(noRollbackFor = RuntimeException.class)
    public List<String> getAllUserName(String procInstId, String taskDefKey) {
        try {
            return bpmTaskUserRepository.getAllUserName(procInstId, taskDefKey);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return new ArrayList<>();
    }

    @Override
    @Transactional(noRollbackFor = RuntimeException.class)
    public List<BpmTaskUser> getAllByProcInstId(String procInstId) {
        try {
            return bpmTaskUserRepository.getAllByProcInstId(procInstId);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return new ArrayList<>();
    }

    @Override
    public Map<String, Set<String>> getTaskDefKeyToUserMap(String procInstId) {
        Map<String, Set<String>> resultMap = new HashMap<>();
        if (!ValidationUtils.isNullOrEmpty(procInstId)) {
            List<BpmTaskUser> bpmTaskUsers = getAllByProcInstId(procInstId);
            try {
                resultMap.putAll(bpmTaskUsers.stream().collect(Collectors.groupingBy(BpmTaskUser::getTaskDefKey, Collectors.mapping(BpmTaskUser::getUserName, Collectors.toSet()))));
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }

        return resultMap;
    }
}
