package vn.fis.eapprove.business.domain.api.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@Entity
@Table(name = "api_management")
public class ApiManagement {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "url")
    private String url;

    @Column(name = "method")
    private String method;

    @Column(name = "header")
    private String header;

    @Column(name = "body")
    private String body;

    @Column(name = "response")
    private String response;

    @Column(name = "return_response")
    private Integer returnResponse = 0;

    @Column(name = "token_attribute")
    private String tokenAttribute;

    @Column(name = "error_attribute")
    private String errorAttribute;

    @Column(name = "success_condition")
    private String successCondition;

    @Column(name = "continue_on_error")
    private Integer continueOnError = 1;

    @Column(name = "type")
    private String type;

    @Column(name = "status")
    private Integer status;

    @Column(name = "authen_api_id")
    private Long authenApiId;

    @Column(name = "base_url_id")
    private Long baseUrlId;

    @Column(name = "description")
    private String description;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "updated_user")
    private String updatedUser;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "share_with")
    private String shareWith;

    @Column(name = "is_deleted")
    private Integer isDeleted;

    @OneToMany(mappedBy = "apiManagement")
    @JsonIgnore
    private Set<PermissionDataManagement> permissionDataManagements;

    @OneToMany(mappedBy = "apiManagement")
    @JsonIgnore
    private Set<SharedUser> sharedUsers;
    @Column(name = "company_code")
    private String companyCode;
    @Column(name = "company_name")
    private String companyName;
}