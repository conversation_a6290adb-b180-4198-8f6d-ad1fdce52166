package vn.fis.eapprove.business.domain.bpm.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcinstRecall;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcinstRecallRepository;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcinstRecallService;


/**
 * <AUTHOR>
 * @since 12/05/2023
 */
@Slf4j
@Service("BpmProcinstRecallServiceImplV1")
@Transactional
public class BpmProcinstRecallServiceImpl implements BpmProcinstRecallService {

    private final BpmProcinstRecallRepository bpmProcinstRecallRepository;

    @Autowired
    public BpmProcinstRecallServiceImpl(BpmProcinstRecallRepository bpmProcinstRecallRepository) {
        this.bpmProcinstRecallRepository = bpmProcinstRecallRepository;
    }

    @Override
    public BpmProcinstRecall save(BpmProcinstRecall entity) {
        return bpmProcinstRecallRepository.save(entity);
    }

    @Override
    public BpmProcinstRecall findByProcInstId(String procInstId) {
        return bpmProcinstRecallRepository.findTop1ByProcInstIdOrderByRecallTimeDesc(procInstId).orElse(null);
    }
}
