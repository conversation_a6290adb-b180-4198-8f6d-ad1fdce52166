package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * Author: PhucVM
 * Date: 28/03/2023
 */
@Getter
@Setter
@Entity
@IdClass(BpmProcinstLinkId.class)
@Table(name = "bpm_procinst_link")
public class BpmProcinstLink implements Serializable {

    private static final long serialVersionUID = -5202380690014402425L;

    @Id
    @Column(name = "BPM_PROCINST_ID", nullable = false)
    private Long bpmProcinstId;

    @Id
    @Column(name = "BPM_PROCINST_LINK_ID", nullable = false)
    private Long bpmProcinstLinkId;
}
