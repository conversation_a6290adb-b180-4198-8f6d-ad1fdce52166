package vn.fis.eapprove.business.domain.manageApi.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.util.Date;


@Entity
@Table(name = "manage_api")
@Data
public class ManageAPI {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "nameAPI")
    private String nameAPI;

    @Column(name = "url")
    private String url;

    @Column(name = "ticket_phase")
    private String ticketPhase;

    @Column(name = "description")
    private String description;

    @Column(name = "ticketId")
    private String ticketId;

    @Column(name = "ticket_name")
    private String ticketName;

    @Column(name = "status")
    private Integer status;

    @Column(name = "modified_user")
    private String createdUser;

    @Column(name = "created_date")
    private Date createdDate;

    @Column(name = "created_user")
    private String modifiedUser;

    @Column(name = "modified_date")
    private Date modifiedDate;

}
