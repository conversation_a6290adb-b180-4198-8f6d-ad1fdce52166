package vn.fis.eapprove.business.domain.legislative.model.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.dto.BaseDto;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportRequest extends BaseDto {
    private String organizationSector; // phân loại tổ chức
    private String type; // Phân loại
    private String dateType; // createdTime, ticketFinishTime, cancelTime
    private Date fromDate;
    private Date toDate;
    private String name; // Tên nhiệm vụ
    private String taskName; // Tên chi tiết nhiệm vụ
    private List<String> listProcessType; // Loại nhiệm vụ
    private List<String> listResponsibleAgency; // Cơ quan soạn thảo
    private List<String> listCompanyCode; // C<PERSON> quan thực hiện
    private List<String> listPolicyDevProcess; // Quy trình xây dựng chính sách đối với dự án luật
    private List<Long> listReleaseYear; // năm ban hành
    private List<String> listActivityStatus; // Trạng thái
    private List<String> listFileName;
    private List<String> listName;
    private List<String> listTaskName;
}
