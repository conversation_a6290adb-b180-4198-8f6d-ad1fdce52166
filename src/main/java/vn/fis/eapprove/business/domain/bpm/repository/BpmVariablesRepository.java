package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmVariables;


import java.util.List;
import java.util.Set;

@Repository
public interface BpmVariablesRepository extends JpaRepository<BpmVariables, Long>, JpaSpecificationExecutor<BpmVariables> {

    List<BpmVariables> getAllByTaskId(String taskId);

    List<BpmVariables> getAllByTaskIdAndIsDraft(String taskId, Integer isDraft);

    List<BpmVariables> findByProcInstIdAndTaskId(String procInstId, String taskId);

    List<BpmVariables> findBpmVariablesByProcInstId(String procInstId);

    void deleteAllByTaskIdAndIsDraft(String taskId, Integer isDraft);

    @Modifying
    @Query("UPDATE BpmVariables vari SET vari.isDraft = 0 WHERE vari.procInstId = :procInstId AND vari.taskId = :taskId AND vari.isDraft = 1")
    void updateTaskType(@Param("procInstId") String procInstId, @Param("taskId") String taskId);

    void deleteBpmVariablesByProcInstId(String procInstId);

    void deleteBpmVariablesByProcInstIdIn(List<String> procInstIds);

    void deleteBpmVariablesByProcInstIdAndTaskIdIsNull(String procInstId);

    @Query("SELECT a FROM BpmVariables a "
            + "WHERE a.procInstId = :procInstId AND a.isDraft = 0 "
            + "AND a.taskId IN ("
            + "SELECT b.taskId FROM BpmTask b "
            + "WHERE b.taskProcInstId = a.procInstId "
            + "AND b.taskStatus = 'COMPLETED' "
            + "AND b.taskDefKey = :taskDefKey"
            + ")")
    List<BpmVariables> getAllByIncomingTaskDefKey(@Param("procInstId") String procInstId, @Param("taskDefKey") String taskDefKey);

    @Query("SELECT a FROM BpmVariables a "
            + "WHERE a.procInstId = :procInstId "
            + "AND (a.taskId IS NULL OR a.taskId = :taskId) "
            + "AND a.name IN (:variables) "
            + "AND UPPER(a.type) IN ('STRING', 'JSON')")
    List<BpmVariables> getAllByVariableName(@Param("procInstId") String procInstId, @Param("taskId") String taskId, @Param("variables") List<String> variables);

    @Modifying(clearAutomatically = true, flushAutomatically = true)
    @Query("DELETE FROM BpmVariables "
            + "WHERE procInstId = :procInstId "
            + "AND ((:taskId IS NULL AND taskId IS NULL) OR taskId = :taskId) "
            + "AND name IN (:variables)")
    void deleteVariables(@Param("procInstId") String procInstId, @Param("taskId") String taskId, @Param("variables") Set<String> variables);

    @Query("select a from BpmVariables a WHERE a.taskId = :taskId")
    List<BpmVariables> getListVariablesByTaskId(String taskId);

    @Query("select  a from BpmVariables a where a.procInstId= :procInstId and a.taskId is null ")
    List<BpmVariables> getListVariablesByProcInstId(String procInstId);

    List<BpmVariables> findAllByProcInstIdAndTypeOrderByIdDesc(String procInstId,String type);

    List<BpmVariables> findAllByIdAndTypeOrderByIdDesc(Long id,String type);

    @Query("select  a from BpmVariables a where a.procInstId= :procInstId and a.taskId is null and a.isDraft = 1")
    List<BpmVariables> getListVariablesDraftByProcInstId(String procInstId);
}
