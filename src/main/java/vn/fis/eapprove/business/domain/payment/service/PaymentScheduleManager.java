package vn.fis.eapprove.business.domain.payment.service;

import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.payment.entity.PaymentSchedule;
import vn.fis.eapprove.business.domain.payment.repository.PaymentScheduleRepository;
import vn.fis.eapprove.business.dto.BaseSearchDto;
import vn.fis.eapprove.business.dto.PageSearchDto;
import vn.fis.eapprove.business.model.request.PaymentScheduleRequest;
import vn.fis.eapprove.business.model.request.SyncPaymentScheduleRequest;
import vn.fis.eapprove.business.model.response.PaymentScheduleResponse;
import vn.fis.eapprove.business.specification.PaymentScheduleSpecification;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.business.utils.TimeUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static vn.fis.eapprove.business.constant.Constant.*;

@Slf4j
@Service
@Transactional
public class PaymentScheduleManager {
    @Autowired
    ResponseUtils responseUtils;
    @Autowired
    PaymentScheduleRepository paymentScheduleRepository;
    @Autowired
    PaymentScheduleSpecification paymentScheduleSpecification;
    @Autowired
    CredentialHelper credentialHelper;
    @Autowired
    MessageSource messageSource;

    public String createPaymentSchedule(PaymentScheduleRequest paymentScheduleRequest) {
        try {
            PaymentSchedule paymentSchedule = new PaymentSchedule();
            Long countPayment = paymentScheduleRepository.countByTicketId(paymentScheduleRequest.getTicketId());
            String paymentInstallment = paymentScheduleRequest.getPaymentInstallment().stream().collect(Collectors.joining(","));
            paymentSchedule.setCodeTicket(paymentScheduleRequest.getCodeTicket());
            paymentSchedule.setContractNumber(paymentScheduleRequest.getContractNumber());
            paymentSchedule.setNameProject(paymentScheduleRequest.getNameProject());
            paymentSchedule.setPaymentInstallment(paymentInstallment);
            paymentSchedule.setTicketId(paymentScheduleRequest.getTicketId());
            paymentSchedule.setPaymentAmount(paymentScheduleRequest.getPaymentAmount());
            paymentSchedule.setPaymentDeadline(paymentScheduleRequest.getPaymentDeadline());
            paymentSchedule.setAmountPaid(paymentScheduleRequest.getAmountPaid());
            paymentSchedule.setCreateDate(LocalDateTime.now());
            paymentSchedule.setCreateUser(credentialHelper.getJWTPayload().getUsername());
            paymentSchedule.setReason(paymentScheduleRequest.getReason());
            paymentSchedule.setCompanyCode(paymentScheduleRequest.getCompanyCode());
            paymentScheduleRepository.save(paymentSchedule);
            return MESSAGE_SUCCESS;
        } catch (Exception e) {
            e.printStackTrace();
            return MESSAGE_FAIL;
        }
    }

    public PageSearchDto search(BaseSearchDto criteria) {
        try {
            Integer pageNum;
            if (criteria.getPage() > 1 && !ValidationUtils.isNullOrEmpty(criteria.getSearch())) {
                pageNum = 0;
            } else {
                pageNum = criteria.getPage() - 1;
            }
            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());
            Page<PaymentSchedule> page = paymentScheduleRepository.findAll(
                    paymentScheduleSpecification.filter(criteria),
                    PageRequest.of(pageNum, criteria.getLimit(),
                            sort));
            List<PaymentScheduleResponse> paymentScheduleResponses = new ArrayList<>();
            for (PaymentSchedule paymentSchedule : page.getContent()) {
                PaymentScheduleResponse paymentScheduleResponse = PaymentScheduleResponse.builder()
                        .id(paymentSchedule.getId())
                        .codeTicket(paymentSchedule.getCodeTicket())
                        .contractNumber(paymentSchedule.getContractNumber())
                        .nameProject(paymentSchedule.getNameProject())
                        .paymentInstallment(paymentSchedule.getPaymentInstallment())
                        .procInstId(paymentSchedule.getTicketId())
                        .paymentAmount(paymentSchedule.getPaymentAmount())
                        .paymentDeadline(paymentSchedule.getPaymentDeadline())
                        .actualSpending(paymentSchedule.getActualAmount())
                        .amountPaid(paymentSchedule.getAmountPaid())
                        .reason(paymentSchedule.getReason())
                        .createUser(paymentSchedule.getCreateUser())
                        .createDate(TimeUtils.localDateTimeToString(paymentSchedule.getCreateDate(), FORMAT_DATE_TIME_2))
                        .frequency(paymentSchedule.getFrequency())
                        .companyCode(paymentSchedule.getCompanyCode())
                        .paidAt(TimeUtils.localDateTimeToString(paymentSchedule.getPaidAt(), FORMAT_DATE_TIME_2))
                        .build();
                paymentScheduleResponses.add(paymentScheduleResponse);

            }
            return PageSearchDto.builder().content(paymentScheduleResponses)
                    .number(page.getNumber() + 1)
                    .numberOfElements(page.getNumberOfElements())
                    .page(page.getNumber() + 1)
                    .limit(page.getSize())
                    .totalPages(page.getTotalPages())
                    .totalElements(page.getTotalElements())
                    .sortBy(criteria.getSortBy())
                    .sortType(criteria.getSortType())
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    public String syncPaymentSchedule(SyncPaymentScheduleRequest syncPaymentScheduleRequest)  {
        List<PaymentSchedule> schedule = paymentScheduleRepository.findByTicketId(syncPaymentScheduleRequest.getTicketId());
        if (!ValidationUtils.isNullOrEmpty(schedule)) {
            PaymentSchedule paymentSchedule = new PaymentSchedule();
            for (PaymentSchedule listPaymentSchedule : schedule) {
                Long countPayment = paymentScheduleRepository.countByTicketId(listPaymentSchedule.getTicketId());
                paymentSchedule.setCodeTicket(listPaymentSchedule.getCodeTicket());
                paymentSchedule.setContractNumber(listPaymentSchedule.getContractNumber());
                paymentSchedule.setNameProject(listPaymentSchedule.getNameProject());
                paymentSchedule.setPaymentInstallment(listPaymentSchedule.getPaymentInstallment());
                paymentSchedule.setTicketId(listPaymentSchedule.getTicketId());
                paymentSchedule.setPaymentAmount(listPaymentSchedule.getPaymentAmount());
                paymentSchedule.setPaymentDeadline(listPaymentSchedule.getPaymentDeadline());
                paymentSchedule.setAmountPaid(listPaymentSchedule.getAmountPaid());
                paymentSchedule.setCreateDate(LocalDateTime.now());
                paymentSchedule.setCreateUser(credentialHelper.getJWTPayload().getUsername());
                paymentSchedule.setReason(listPaymentSchedule.getReason());
                paymentSchedule.setCompanyCode(listPaymentSchedule.getCompanyCode());
                paymentSchedule.setFrequency("lần: " + (countPayment));
                paymentSchedule.setActualAmount(syncPaymentScheduleRequest.getActualAmount());
                paymentSchedule.setAmountPaid(syncPaymentScheduleRequest.getPaidAt());
                paymentSchedule.setPaidAt(TimeUtils.stringToLocalDateTime(syncPaymentScheduleRequest.getPaidAt(), FORMAT_DATE_TIME_2));
            }
            paymentScheduleRepository.save(paymentSchedule);

            return MESSAGE_SUCCESS;
        }
        return MESSAGE_FAIL;
    }
}
