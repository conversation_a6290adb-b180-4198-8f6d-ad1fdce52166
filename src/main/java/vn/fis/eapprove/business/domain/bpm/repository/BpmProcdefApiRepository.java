package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefApi;
import vn.fis.eapprove.business.dto.ActionApiDto;


import java.util.List;

@Repository
public interface BpmProcdefApiRepository extends JpaRepository<BpmProcdefApi, Long>, JpaSpecificationExecutor<BpmProcdefApi> {

    List<BpmProcdefApi> getAllByBpmProcdefId(Long bpmProcdefId);

    List<BpmProcdefApi> findBpmProcdefApisByProcDefId(String procDefId);

    @Query("SELECT new vn.fis.eapprove.business.dto.ActionApiDto(a.id, a.header, a.body, a.callOrder, b.successCondition, a.successCondition, "
            + "b.url, b.method, b.header, b.body, b.authenApiId, "
            + "c.url, c.method, c.header, c.body, c.tokenAttribute, "
            + "b.baseUrlId, d.url, b.response, b.returnResponse, a.response, "
            + "b.errorAttribute, b.continueOnError, a.continueOnError, a.callCondition) "
            + "FROM BpmProcdefApi a "
            + "JOIN FETCH ApiManagement b on (a.apiId = b.id and b.status = 1) "
            + "LEFT JOIN FETCH ApiManagement c on (b.authenApiId = c.id and c.status = 1) "
            + "LEFT JOIN FETCH ApiManagement d on (b.baseUrlId = d.id and d.status = 1) "
            + "WHERE (:procDefId IS NULL OR a.procDefId = :procDefId) "
            + "AND (:taskDefKey IS NULL OR a.taskDefKey = '' OR LOWER(a.taskDefKey) = 'start' OR a.taskDefKey = :taskDefKey) "
            + "AND (:actionId IS NULL OR a.actionId = :actionId)")
    List<ActionApiDto> getBpmProcdefApiInfo(@Param("procDefId") String procDefId, @Param("taskDefKey") String taskDefKey, @Param("actionId") Long actionId);

    @Query("SELECT COUNT(a) FROM BpmProcdefApi a "
            + "WHERE a.status = 1 AND a.procDefId = :procDefId")
    Long countByProcDefId(@Param("procDefId") String procDefId);

    List<BpmProcdefApi> findAllByApiIdIn(List<Long> apiId);
}
