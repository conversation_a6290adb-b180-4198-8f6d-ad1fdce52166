package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Author: PhucVM
 * Date: 28/03/2023
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class BpmProcinstLinkId implements Serializable {

    private static final long serialVersionUID = -6760922234467680297L;

    private Long bpmProcinstId;
    private Long bpmProcinstLinkId;
}
