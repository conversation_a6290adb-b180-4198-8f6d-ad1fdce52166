package vn.fis.eapprove.business.domain.bpm.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.Tuple;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.UserTask;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import vn.fis.eapprove.business.constant.AppConstants;
import vn.fis.eapprove.business.constant.BusinessEnum;
import vn.fis.eapprove.business.constant.LegislativeEnum;
import vn.fis.eapprove.business.domain.assign.entity.AssignManagement;
import vn.fis.eapprove.business.domain.assign.service.AssignManager;
import vn.fis.eapprove.business.domain.assistant.entity.Assistant;
import vn.fis.eapprove.business.domain.assistant.repository.AssistantRepository;
import vn.fis.eapprove.business.domain.authority.service.AuthorityManagementService;
import vn.fis.eapprove.business.domain.bpm.entity.*;
import vn.fis.eapprove.business.domain.bpm.repository.*;
import vn.fis.eapprove.business.domain.changeAssignee.entity.ChangeAssigneeHistory;
import vn.fis.eapprove.business.domain.changeAssignee.service.ChangeAssigneeHistoryService;
import vn.fis.eapprove.business.domain.legislative.model.entity.LegislativeProgramDetail;
import vn.fis.eapprove.business.domain.legislative.repository.LegislativeProgramDetailRepository;
import vn.fis.eapprove.business.domain.notification.entity.NotificationTemplate;
import vn.fis.eapprove.business.domain.notification.repository.NotificationTemplateRepository;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagementHistory;
import vn.fis.eapprove.business.domain.priority.repository.PriorityHistoryRepository;
import vn.fis.eapprove.business.domain.priority.repository.PriorityManagementRepository;
import vn.fis.eapprove.business.domain.priority.service.PriorityManager;
import vn.fis.eapprove.business.domain.servicePackage.repository.ServicePackageRepository;
import vn.fis.eapprove.business.domain.servicePackage.service.ServicePackageManager;
import vn.fis.eapprove.business.domain.submission.service.SubmissionTypeManager;
import vn.fis.eapprove.business.domain.task.entity.TaskSettingConfig;
import vn.fis.eapprove.business.domain.task.repository.TaskSettingConfigRepository;
import vn.fis.eapprove.business.domain.template.repository.TemplateHistoryRepository;
import vn.fis.eapprove.business.dto.*;
import vn.fis.eapprove.business.mapper.BpmTaskMapper;
import vn.fis.eapprove.business.model.AccountModel;
import vn.fis.eapprove.business.model.ActionApiContext;
import vn.fis.eapprove.business.model.NotificationUser;
import vn.fis.eapprove.business.model.request.*;
import vn.fis.eapprove.business.model.response.*;
import vn.fis.eapprove.business.specification.BpmProcInstSpecification;
import vn.fis.eapprove.business.specification.BpmTaskSpecification;
import vn.fis.eapprove.business.tenant.manager.*;
import vn.fis.eapprove.business.utils.CamundaUtils;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.business.utils.RedirectApiUtils;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.camunda.*;
import vn.fis.spro.common.constants.MapKeyEnum;
import vn.fis.spro.common.constants.ProcInstConstants;
import vn.fis.spro.common.constants.TaskActionConstants;
import vn.fis.spro.common.constants.TaskConstants;
import vn.fis.spro.common.model.response.UserInfoResponse;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static vn.fis.eapprove.business.constant.Constant.defaultSlaFinish;
import static vn.fis.eapprove.business.constant.Constant.defaultSlaResponse;

@Slf4j
@Service("BpmTaskManagerV1")
@Transactional
public class BpmTaskManager {

    @Autowired
    PriorityManagementRepository priorityManagementRepository;
    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;
    @Autowired
    private AssignManager assignManager;
    @Autowired
    private BpmTaskRepository bpmTaskRepository;
    @Autowired
    private BpmProcInstRepository bpmProcInstRepository;
    @Autowired
    private ServicePackageManager servicePackageManager;
    @Autowired
    private BpmTaskMapper bpmTaskMapper;
    @Autowired
    private SproProperties sproProperties;
    @Autowired
    private BpmTaskSpecification bpmTaskSpecification;
    @Autowired
    private BpmProcInstSpecification bpmProcInstSpec;
    @Autowired
    private RedirectApiUtils redirectApiUtils;
    @Autowired
    private BpmVariablesRepository bpmDraftVariablesRepo;
    @Autowired
    private ResponseUtils responseUtils;
    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private BpmHistoryManager bpmHistoryManager;
    @Autowired
    private BpmTpTaskRepository bpmPrintPhaseTaskRepository;
    @Autowired
    private BpmTemplatePrintRepository bpmTemplatePrintRepository;
    @Autowired
    private TaskSettingConfigRepository taskSettingConfigRepository;
    @Autowired
    private Common common;
    @Autowired
    private CamundaEngineService camundaEngineService;
    @Autowired
    private CredentialHelper credentialHelper;
    @Autowired
    private PriorityManager priorityManager;
    @Autowired
    private BpmTpSignZoneRepository bpmTpSignZoneRepository;
    @Autowired
    private BpmVariablesRepository bpmVariablesRepository;
    @Autowired
    private CustomerService customerService;
    @Autowired
    @Lazy
    private BpmTaskUserService bpmTaskUserService;
    @Autowired
    private BpmTpSignZoneManager bpmTpSignZoneManager;
    @Autowired
    private NotificationTemplateRepository notificationTemplateRepository;
    @Autowired
    private ActionApiService actionApiService;
    @Autowired
    private SproService sproService;
    @Autowired
    @Lazy
    private BpmProcInstManager bpmProcInstManager;
    @Autowired
    private ActHiVarInstManager actHiVarInstManager;
    @Autowired
    private BpmVariablesService bpmVariablesService;
    @Autowired
    private AuthorityManagementService authorityManagementService;
    @Autowired
    @Lazy
    private BpmProcdefNotificationService bpmProcdefNotificationService;
    @Autowired
    private BpmNotifyUserRepository bpmNotifyUserRepository;
    @Autowired
    private ChangeAssigneeHistoryService changeAssigneeHistoryService;
    @Autowired
    private SubmissionTypeManager submissionTypeManager;
    @Autowired
    private BpmProcdefRepository bpmProcdefRepository;
    @Autowired
    private AssistantRepository assistantRepo;
    @Autowired
    @Lazy
    private BpmRuManager bpmRuManager;
    @Autowired
    private TemplateHistoryRepository templateHistoryRepository;
    @Autowired
    private ServicePackageRepository servicePackageRepository;
    @Autowired
    private PriorityHistoryRepository priorityHistoryRepository;
    @Value("${app.superAdmin.account}")
    private String appSuperAdminAccount;
    @Autowired
    @Lazy
    private BpmTemplatePrintManager bpmTemplatePrintManager;
    @Autowired
    private BpmDiscussionRepository bpmDiscussionRepository;
    @Autowired
    private LegislativeProgramDetailRepository legislativeProgramDetailRepository;

    @Value("${spring.kafka.consumer.topic.notification-user}")
    private String notificationUser;

    public static List<MyTaskResponse> sortByRemainingTime(List<MyTaskResponse> superList, String sortType) {
        superList.sort((o1, o2) -> {
            if (o2.getRemainingTime() == 0 || o1.getRemainingTime() == 0) {
                return -1;
            }
            if (sortType.equals("DESC")) {
                return Long.compare(o2.getRemainingTime(), o1.getRemainingTime());
            } else {
                return Long.compare(o1.getRemainingTime(), o2.getRemainingTime());
            }
        });

        return superList;
    }

    public List<BpmTaskDto> loadActiveTask(String procInstId) {
        List<BpmTask> bpmTasks = bpmTaskRepository.loadActiveTask(procInstId);
        return bpmTasks.stream().map(bpmTaskMapper::entityToDto).collect(Collectors.toList());
    }

    public List<BpmTaskDto> getTaskInfoByTaskDefKey(String procInstId, String taskDefKey) {
        List<BpmTask> bpmTask = bpmTaskRepository.getBpmTaskInfoByTaskDefKeyAndProcInstId(procInstId, taskDefKey);
        return bpmTask.stream().map(bpmTaskMapper::entityToDto).collect(Collectors.toList());
    }

    public List<BpmTask> getByTicketIdAndStatus(String procInstId, List<String> listStatus) {
        try {
            return bpmTaskRepository.getBpmTaskByTaskProcInstIdAndTaskStatusIn(procInstId, listStatus);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public Map<String, Map<String, String>> getXmlData(String procDefId) {
        try {
            Map<String, Map<String, String>> result = new HashMap<>();

            Document doc = responseUtils.getXml(procDefId);
            NodeList nodes = doc.getElementsByTagName("bpmn:userTask");

            for (int i = 0; i < nodes.getLength(); i++) {
                Map<String, String> mapProp = new HashMap<>();
                Element element = (Element) nodes.item(i);
                String taskDefKey = element.getAttributes().getNamedItem("id").getNodeValue();
                NodeList name = element.getElementsByTagName("camunda:property");

                String candidateG = element.getAttributes().getNamedItem("camunda:candidateGroups") != null ?
                        element.getAttributes().getNamedItem("camunda:candidateGroups").getNodeValue() : null;
                String candidateU = element.getAttributes().getNamedItem("camunda:candidateUsers") != null ?
                        element.getAttributes().getNamedItem("camunda:candidateUsers").getNodeValue() : null;
                String assignee = element.getAttributes().getNamedItem("camunda:assignee") != null ?
                        element.getAttributes().getNamedItem("camunda:assignee").getNodeValue() : null;
                mapProp.put("candidateGroup", candidateG);
                mapProp.put("candidateUser", candidateU);
                mapProp.put("assignee", assignee);
                mapProp.put("setting_slaFinish", defaultSlaFinish);
                mapProp.put("setting_slaResponse", defaultSlaResponse);
                mapProp.put("setting_taskType", "APPROVAL");
                mapProp.put("shortcut", "0");
                for (int j = 0; j < name.getLength(); j++) {
                    Element propEle = (Element) name.item(j);
                    String propName = propEle.getAttributes().getNamedItem("name") != null ?
                            propEle.getAttributes().getNamedItem("name").getNodeValue() : null;
                    String propVal = propEle.getAttributes().getNamedItem("value") != null ?
                            propEle.getAttributes().getNamedItem("value").getNodeValue() : null;
                    if (propName != null && propVal != null) {
                        mapProp.put(propName, propVal);
                    } else if (propName != null && propName.equals("setting_taskType")) {
                        mapProp.put(propName, "APPROVAL");
                    } else if (propName != null && propName.equals("setting_slaFinish")) {
                        mapProp.put("setting_slaFinish", defaultSlaFinish);
                    } else if (propName != null && propName.equals("setting_slaResponse")) {
                        mapProp.put("setting_slaResponse", defaultSlaResponse);
                    } else if (propName != null && propName.equals("setting_autoAssign")) {
                        mapProp.put("setting_autoAssign", "false");
                    }
                }
                result.put(taskDefKey, mapProp);
            }
            return result;
        } catch (Exception e) {
            return null;
        }
    }

    public AssignManagement getAuthority(String email, long ticketId, String taskId, String taskDefkey) throws JsonProcessingException {
        BpmProcInst bpmProcInst = bpmProcInstManager.findById(ticketId);
        if (bpmProcInst != null) {
            List<AssignManagement> asmList = assignManager.findAssignManagementByAssignUser(email);
            AssignManagement asm = null;
            List<BpmVariables> variables = this.bpmVariablesRepository.findByProcInstIdAndTaskId(bpmProcInst.getTicketProcInstId(), null);
            //Check child service -> If child => Check change implement by parent service
            Long serviceId = servicePackageRepository.findIdParentServicePackage(bpmProcInst.getServiceId());
            //Check active user
            List<String> lstUsernameActive = customerService.getListUsernameActive(asmList.stream().map(AssignManagement::getAssignedUser).collect(Collectors.toList()));
            for (AssignManagement asmObject : asmList) {
                if (lstUsernameActive.contains(asmObject.getAssignedUser()) && Arrays.asList(asmObject.getServiceRange().split(",")).contains(serviceId.toString()) &&
                        (asmObject.getEffect() == 1 //Trường hợp tích chọn đến hoàn thành công việc thì k quan tâm thời gian bản ủy quyền
                                || (
                                (asmObject.getStartDate().isBefore(LocalDate.now()) || asmObject.getStartDate().equals(LocalDate.now()))
                                        && (asmObject.getEndDate().isAfter(LocalDate.now()) || asmObject.getEndDate().equals(LocalDate.now()))
                        )
                        )
                        && asmObject.getType() != 1 // Chỉ lấy ra type =! 1 (Tờ trình ủy quyền)
                        && (asmObject.getStatus() == 0 || asmObject.getStatus() == 1)
                        && this.checkAuthorityCondition(variables, asmObject.getAuthorityConditions()) // check dk uy quyen da cau hinh
                ) {
                    asm = asmObject;
                    break;
                }
            }
            if (asm != null) {
                authorityManagementService.saveAuthority(String.valueOf(ticketId), asm, taskId, taskDefkey);
                return asm;
            }
        }
        return null;
    }

    /// check legislative SLA
    public void saveTaskForCreateTicket(String id,
                                        long ticketId,
                                        String aProcessDefinitionId,
                                        Boolean isDraft,
                                        List<Long> linkProcInstId, //Liên kết từ phiếu đã hoàn thành
                                        String procInstId,
                                        StartProcessInstanceDto startProcessInstanceDto,
                                        LocalDateTime createTicketDate,
                                        String account
    ) {
        try {
            List<Map<String, Object>> data = camundaEngineService.getCurrentRuntimeTasks(id);

            Map<String, Map<String, String>> mapXmlData = getXmlData(aProcessDefinitionId);
            List<BpmTask> listTask = new ArrayList<>();
            LocalDateTime createNow = LocalDateTime.now();
            String taskDefKey = "";
            AssignManagement assignManagement = null;
            //Kiểm tra liên kết phiếu hoàn thành có ủy quyền hay k
            String fromAssignee = "", toAssignee = "";
            if (!ValidationUtils.isNullOrEmpty(linkProcInstId)) {
                for (Long item : linkProcInstId) {
                    boolean isServiceApply = false;
                    BpmProcInst bpmProcInst = bpmProcInstManager.findById(item);
                    AssignManagement check = assignManager.getInfoAutoChangeImplement(item, false);
                    if (check != null && bpmProcInst != null) {
                        String[] serviceList = check.getServiceRange().split(",");
                        for (String i : serviceList) {
                            if (i.equals(bpmProcInst.getServiceId().toString())) {
                                isServiceApply = true;
                                break;
                            }
                        }
                        if (Boolean.TRUE.equals(bpmProcInst.getTicketAssign()) && isServiceApply) { // Get assign
                            fromAssignee = check.getAssignUser();
                            toAssignee = check.getAssignedUser();
                            assignManagement = check;
                            break;
                        }
                    }
                }
            }
            log.info("SAVE TASK CREATE TICKET fromAssignee: {}", fromAssignee);
            log.info("SAVE TASK CREATE TICKET toAssignee: {}", toAssignee);

            // get flow info with assignee from camunda
            WorkFlowRequest workFlowRequest = new WorkFlowRequest();
            workFlowRequest.setProcDefId(aProcessDefinitionId);
            workFlowRequest.setProcInstId(procInstId);
            List<SproFlow> workFlow = sproService.getWorkFlow(workFlowRequest);

            for (Map<String, Object> taskObj : data) {
                taskDefKey = taskObj.get("taskDefKey").toString();
                String newAssignee = null;

                // Check candidate task - auto change implementer
                if (!fromAssignee.isEmpty() && !toAssignee.isEmpty() && taskObj.get("assignee") != null
                        && taskObj.get("assignee").equals(fromAssignee)) { // Ưu tiên ủy quyền theo phiếu liên kết
                    newAssignee = toAssignee;
                } else if (taskObj.get("assignee") != null) {
                    assignManagement = getAuthority(
                            taskObj.get("assignee").toString(),
                            ticketId,
                            taskObj.get("id").toString(),
                            taskDefKey
                    );
                    if (!ValidationUtils.isNullOrEmpty(assignManagement)) {

                        List<SproFlowNode> nodes = workFlow.stream()
                                .flatMap(e -> e.getNodes().stream().filter(node -> node.getType().equals("userTask")))
                                .toList();

                        List<String> lstAssignee = nodes.stream()
                                .filter(e -> e.getId().equalsIgnoreCase(taskObj.get("taskDefKey").toString()))
                                .map(node -> node.getAssignee() != null ? List.of(node.getAssignee().replaceAll("\\[\"|\"\\]|\\\\|\"", "").split(",")) : null)
                                .findFirst().orElse(null);
                        // check case uq task song song/ tuan tu add 1 user nhieu task
                        if (!ValidationUtils.isNullOrEmpty(lstAssignee) && lstAssignee.stream().map(String::trim).toList().contains(assignManagement.getAssignedUser())) {
                            newAssignee = null;
                        } else {
                            newAssignee = assignManagement.getAssignedUser();
                        }
                    }
                }

                BpmTask bpmTask = new BpmTask();
                // Check candidate task - auto change implementer
                if (newAssignee != null && taskObj.get("assignee") != null) {
                    bpmTask.setAssignType(true);

                    // save change assignee history
                    ChangeAssigneeHistory changeAssigneeHistory = new ChangeAssigneeHistory();
                    changeAssigneeHistory.setBpmProcinstId(ticketId);
                    changeAssigneeHistory.setProcInstId(taskObj.get("procInstId").toString());
                    changeAssigneeHistory.setTaskId(taskObj.get("id").toString());
                    changeAssigneeHistory.setTaskDefKey(taskDefKey);
                    changeAssigneeHistory.setChangeTime(LocalDateTime.now());
                    changeAssigneeHistory.setOrgAssignee(taskObj.get("assignee").toString());
                    changeAssigneeHistory.setFromAssignee(taskObj.get("assignee").toString());
                    changeAssigneeHistory.setToAssignee(newAssignee);
                    changeAssigneeHistory.setOrgAssigneeTitle(assignManagement != null ? assignManagement.getAssignTitle() : "");
                    changeAssigneeHistory.setType(1);
                    changeAssigneeHistory.setAssignTicketId(assignManagement != null ? assignManagement.getTicketId() : null);

                    changeAssigneeHistoryService.save(changeAssigneeHistory);
                }
                bpmTask.setTaskId(taskObj.get("id").toString());
                bpmTask.setTaskExecutionId(taskObj.get("executionId").toString());
                bpmTask.setTaskProcInstId(taskObj.get("procInstId").toString());
                bpmTask.setTaskProcDefId(taskObj.get("procDefId").toString());
                bpmTask.setTaskDefKey(taskDefKey);
                bpmTask.setTaskCaseInstId(taskObj.get("caseInstId") != null ? taskObj.get("caseInstId").toString() : null);
                bpmTask.setTaskName(taskObj.get("name").toString());
                // Check candidate task - auto change implementer
                bpmTask.setTaskAssignee(taskObj.get("assignee") != null ? newAssignee != null ? newAssignee : taskObj.get("assignee").toString() : null);
                bpmTask.setTaskCreatedTime(createNow);
                // Check candidate task - auto change implementer
                if (newAssignee != null && taskObj.get("assignee") != null) {
                    camundaEngineService.setTaskAssignee(taskObj.get("id").toString(), newAssignee);
                }
                // XML data-----------------------------------------//
                Map<String, String> mapTaskData = mapXmlData.get(taskObj.get("taskDefKey").toString());
                String setting_taskType = mapTaskData.get("setting_taskType");
                String setting_slaFinish = mapTaskData.get("setting_slaFinish");
                String setting_slaResponse = mapTaskData.get("setting_slaResponse");

//                LocalDateTime slaFinDate = null;
//                Double finalSlaFinish = null;
//
//                // Get value sla config system
//                double valueConfigSla = Double.parseDouble("0");
//                String strSystemConfig = customerService.getConfigSlaShift(bpmTask.getTaskAssignee());
//                if (!ValidationUtils.isNullOrEmpty(strSystemConfig)) {
//                    valueConfigSla = Double.parseDouble(strSystemConfig);
//                }
//
//                BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByticketProcInstId(taskObj.get("procInstId").toString());
//                Long priorityHistoryId = bpmProcInst.getPriorityHistoryId();
//                Long priorityId = bpmProcInst.getPriorityId();

                // get priority from priority_management_history -> btp bỏ
//                if (!ValidationUtils.isNullOrEmpty(priorityHistoryId)) {
//                    Double slaValue = priorityHistoryRepository.getValueById(priorityHistoryId);
//                    if (ValidationUtils.isNullOrEmpty(slaValue)) {
//                        slaValue = 100.0;
//                    }
//                    if (!ValidationUtils.isNullOrEmpty(slaValue)) {
//                        // (Thời gian SLA cài đặt + Giá trị cài đặt của config_sla_shift (nếu có)) * Chỉ số tăng giảm SLA (Cài đặt ở Độ ưu tiên-nếu có)
//                        // CHANGED: chỉ số tăng giảm sla -> tỉ lệ %
//                        finalSlaFinish = (Double.parseDouble(setting_slaFinish) + valueConfigSla) * (slaValue / 100.0);
//
//                        long timePriorityFinish = Math.round(finalSlaFinish * 60);
//                        slaFinDate = customerService.getTimeSla(timePriorityFinish, bpmTask.getTaskCreatedTime(), bpmTask.getTaskAssignee());
//                    }
//                } else if (!ValidationUtils.isNullOrEmpty(priorityId)) {
//                    Double slaValue = priorityManagementRepository.getValueById(priorityId);
//                    if (ValidationUtils.isNullOrEmpty(slaValue)) {
//                        slaValue = 100.0;
//                    }
//                    if (!ValidationUtils.isNullOrEmpty(slaValue)) {
//                        // (Thời gian SLA cài đặt + Giá trị cài đặt của config_sla_shift (nếu có)) * Chỉ số tăng giảm SLA (Cài đặt ở Độ ưu tiên-nếu có)
//                        // CHANGED: chỉ số tăng giảm sla -> tỉ lệ %
//                        finalSlaFinish = (Double.parseDouble(setting_slaFinish) + valueConfigSla) * (slaValue / 100.0);
//
//                        long timePriorityFinish = Math.round(finalSlaFinish * 60);
//                        slaFinDate = customerService.getTimeSla(timePriorityFinish, bpmTask.getTaskCreatedTime(), bpmTask.getTaskAssignee());
//                    }
//                }
//
//                if (slaFinDate == null) {
//                    // (Thời gian SLA cài đặt + Giá trị cài đặt của config_sla_shift (nếu có)) * Chỉ số tăng giảm SLA (Cài đặt ở Độ ưu tiên-nếu có)
//                    finalSlaFinish = Double.parseDouble(setting_slaFinish) + valueConfigSla;
//
//                    long slaFinMinute = Math.round(finalSlaFinish * 60);
//                    slaFinDate = LocalDateTime.now().plusMinutes(slaFinMinute);
//                }

                // legislative sla - tính theo ngày cả t7, cn
                LocalDateTime slaFinDate = null;
                Double finalSlaFinish = null;
                // có cấu hình nhiệm vụ lập pháp
                if (!ValidationUtils.isNullOrEmpty(startProcessInstanceDto.getLegislativeId())) {
                    LegislativeProgramDetail detail = legislativeProgramDetailRepository.findLegislativeProgramDetailByLegislativeIdAndTypeAndTaskDefKey(startProcessInstanceDto.getLegislativeId(),
                            LegislativeEnum.LegislativeDetailType.PROCESS_DETAIL.code, taskDefKey);
                    if (!ValidationUtils.isNullOrEmpty(detail) && detail.getFromDate() != null && detail.getToDate() != null) {
                        LocalDate fromDate = LocalDate.parse(detail.getFromDate());
                        LocalDate toDate = LocalDate.parse(detail.getToDate());
                        String processType = detail.getProcessType();
                        long totalDate = processType != null && processType.equals("shortcut")
                                ? 7 // quy trình rút gọn mặc định 7days, thêm cấu hình hệ thống sau
                                : ChronoUnit.DAYS.between(fromDate, toDate);
                        finalSlaFinish = (double) totalDate;
                        slaFinDate = bpmTask.getTaskCreatedTime()
                                .plusDays(totalDate + 1)
                                .toLocalDate().atStartOfDay();
                    }
                }
                // default
                if (slaFinDate == null) {
                    slaFinDate = bpmTask.getTaskCreatedTime()
                            .plusDays(Math.round(Double.parseDouble(setting_slaFinish)) + 1)
                            .toLocalDate().atStartOfDay();
                }
                bpmTask.setSlaFinishTime(slaFinDate);
                // END calc SLA

                // ---------------------------------------------//
                bpmTask.setSlaFinish(!ValidationUtils.isNullOrEmpty(finalSlaFinish) ? finalSlaFinish : (!ValidationUtils.isNullOrEmpty(setting_slaFinish) ? Double.valueOf(setting_slaFinish) : null));
                bpmTask.setSlaResponse(!ValidationUtils.isNullOrEmpty(setting_slaResponse) ? Double.valueOf(setting_slaResponse) : null);

                if (setting_taskType.equalsIgnoreCase(TaskConstants.Type.APPROVAL.code)) {
                    bpmTask.setTaskType(TaskConstants.Type.APPROVAL.code);
                } else {
                    bpmTask.setTaskType(TaskConstants.Type.EXECUTION.code);
                    bpmTask.setTaskStartedTime(null);
                }
                bpmTask.setTaskStatus(TaskConstants.Status.ACTIVE.code);
                if (isDraft) {
                    bpmTask.setTaskStatus(TaskConstants.Status.DRAFT.code);
                    bpmTask.setTaskStartedTime(null);
                }
                bpmTask.setTaskFinishedTime(null);
                bpmTask.setTaskDoneTime(null);
                bpmTask.setTaskCreatedUser(null);
                bpmTask.setTaskIsFirst(true);
                listTask.add(bpmTask);

                // (phucvm3) save create task history
                saveCreateTaskTicketHistory(bpmTask, ticketId);

            }
            List<BpmTask> savedBpmTasks = bpmTaskRepository.saveAll(listTask);
            //Xử ly luu tro ly
            if (ticketId != 0) {
                List<Assistant> listAssistantOld = assistantRepo.findAllByTicketId(String.valueOf(ticketId));
                if (!listAssistantOld.isEmpty())
                    assistantRepo.deleteAllById(listAssistantOld.stream().map(Assistant::getId).collect(Collectors.toList()));
            }
            HashSet<String> listAssistant = new HashSet<>();
            if (!StringUtil.isEmpty(ticketId)
                    && startProcessInstanceDto.getAssistantEmail() != null
                    && startProcessInstanceDto.getAssistantEmail().length > 0) {
                listAssistant.addAll(Arrays.asList(startProcessInstanceDto.getAssistantEmail()));
            }

            if (!StringUtil.isEmpty(ticketId)
                    && startProcessInstanceDto.getChartId() != null
                    && Boolean.TRUE.equals(startProcessInstanceDto.getIsAssistant())) {
                List<String> emailAssignee = bpmProcInstManager.getAllListUserNameUserTask(ticketId);
                String[] assistantChartNode = getAssistantFromChartNode(String.join(",", emailAssignee),
                        String.valueOf(ticketId), String.join(",", credentialHelper.getJWTPayload().getUsername()));
                if (assistantChartNode != null)
                    listAssistant.addAll(Arrays.asList(assistantChartNode));
            }
            saveAssistant(listAssistant.toArray(new String[0]), String.valueOf(ticketId));

            // (phucvm3) save bpm_task_user
            bpmTaskUserService.saveFromBpmTasks(savedBpmTasks);

            //Xử lý lưu version biểu mẫu
            handleSaveVersionTemplateTask(savedBpmTasks, createTicketDate, startProcessInstanceDto.getServiceId());

            //Thông báo cho user liên quan
            if (!ValidationUtils.isNullOrEmpty(savedBpmTasks)) {
                // Thông báo cho user tạo phiếu
                bpmProcdefNotificationService.addNotificationsByConfig(null, taskDefKey, new HashMap<>(),
                        ticketId, true, null, ProcInstConstants.Notifications.OPENED_FOR_CREATED_USER.code, account);

                // Thông báo cho assignee
                for (BpmTask bpmTask : savedBpmTasks) {
                    Map<String, VariableValueDto> variablesNotifi = new HashMap<>();
                    VariableValueDto variableDto = new VariableValueDto();
                    variableDto.setType("String");
                    variableDto.setValue(bpmTask.getTaskName());
                    variablesNotifi.put("nextTaskName", variableDto);

//                    NotificationUser payload = new NotificationUser();
//                    payload.setBpmProcdefId(null);
//                    payload.setNextTaskDefKey(bpmTask.getTaskDefKey());
//                    payload.setVariables(variablesNotifi);
//                    payload.setTicketId(ticketId);
//                    payload.setIsGetOldVariable(true);
//                    payload.setLstCustomerEmails(null);
//                    payload.setActionCode(ProcInstConstants.Notifications.OPENED.code);
//                    payload.setEmailExe(account);
//                    kafkaTemplate.send(notificationUser, payload);
                    bpmProcdefNotificationService.addNotificationsByConfig(null, bpmTask.getTaskDefKey(), variablesNotifi,
                            ticketId, true, null, ProcInstConstants.Notifications.OPENED.code, account);
                }
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    void handleSaveVersionTemplateTask(List<BpmTask> bpmTasks, LocalDateTime createTicketTime, Long serviceId) {
        try {
            if (!bpmTasks.isEmpty()) {
                String procDefId = bpmTasks.get(0).getTaskProcDefId();
                BpmProcdef bpmProcdef = bpmProcdefRepository.findByServiceId(serviceId);
                BpmnModelInstance bpmnModelInstance = camundaEngineService.getModelInstance(procDefId);
                Collection<UserTask> userTasks = CamundaUtils.getUserTasks(bpmnModelInstance);

                List<String> urlNames = new ArrayList<>();
                Map<String, String> taskKeyAndUrlName = new HashMap<>();
                for (BpmTask bpmTask : bpmTasks) {
                    String startEvent = bpmTask.getTaskDefKey();
                    if (bpmTask.getTemplateVersionId() == null) { // Lưu version tạo task
                        for (UserTask userTask : userTasks) {
                            if (startEvent.equals(userTask.getId()) && userTask.getCamundaFormKey() != null) {
                                urlNames.add(userTask.getCamundaFormKey());
                                taskKeyAndUrlName.put(startEvent, userTask.getCamundaFormKey());
                            }
                        }
                    }
                }
                if (!urlNames.isEmpty()) {
                    List<TemplateHistoryResponse> templateHistories = templateHistoryRepository.findAllMaxVersionByUrlName(urlNames, createTicketTime);
                    List<TemplateHistoryResponse> templateChildHistories = null;
                    if (bpmProcdef.getSpecialFlow() != null && bpmProcdef.getSpecialFlow().equals(Boolean.TRUE)) {
                        List<Long> allIdChilds = templateHistories.stream().map(TemplateHistoryResponse::getTemplateId).collect(Collectors.toList());
                        templateChildHistories = templateHistoryRepository.findAllChildMaxVersionByUrlName(allIdChilds, createTicketTime);
                    }
                    for (BpmTask bpmTask : bpmTasks) {
                        TemplateHistoryResponse templateHistory = templateHistories.stream().filter(i -> i.getUrlName().equals(taskKeyAndUrlName.get(bpmTask.getTaskDefKey()))).findFirst().orElse(null);
                        if (templateHistory != null) {
                            //Lưu version task cha
                            bpmTask.setTemplateVersionId(templateHistory.getId());
                            //Luu verison task child
                            if (templateChildHistories != null) {
                                TemplateHistoryResponse templateHisParent = templateChildHistories.stream().filter(i -> i.getInputId().equals(templateHistory.getTemplateId())).findFirst().orElse(null);
                                if (templateHisParent != null && !templateHisParent.getId().equals(bpmTask.getTemplateVersionId()))
                                    bpmTask.setParentTemplateVersionId(templateHisParent.getId());
                            }
                        }

                    }
                }

                bpmTaskRepository.saveAll(bpmTasks);
            }
        } catch (Exception ex) {
            log.error("Save version Template false {}", ex.getMessage());
        }
    }

    public String[] getAssistantFromChartNode(String emailAssignee, String tiketId, String createUser) {
        try {
            String[] lstAssitant = customerService.getAssistantByChartId(emailAssignee, createUser);
            Set<String> uniqueSet = new HashSet<>(Arrays.asList(lstAssitant));
            String[] uniqueArr = uniqueSet.toArray(new String[uniqueSet.size()]);
            if (uniqueArr.length > 0) {
                saveAssistant(uniqueArr, tiketId);
                return uniqueArr;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    private void saveAssistant(String[] assistants, String ticketId) {
        List<Assistant> res = new ArrayList<>();
        for (String assistant : assistants) {
            res.add(
                    Assistant.builder()
                            .ticketId(ticketId)
                            .assistantEmail(assistant)
                            .createAt(new Date())
                            .updateAt(new Date())
                            .build()
            );
        }
        assistantRepo.saveAll(res);
    }

    public List<BpmTask> saveTaskFromCamunda(List<Map<String, Object>> data, String procDefId, String account, String taskId, String procInstId, BpmProcInst bpmProcInst, String taskDefKey, String taskType) {
        try {
            Map<String, Map<String, String>> mapXmlData = getXmlData(procDefId);
            List<BpmTask> listSave = new ArrayList<>();
            for (Map<String, Object> taskObj : data) {
                BpmTask bpmTask = new BpmTask();
                bpmTask.setTaskId(taskObj.get("id").toString());
                bpmTask.setTaskExecutionId(taskObj.get("executionId").toString());
                bpmTask.setTaskProcInstId(taskObj.get("procInstId").toString());
                bpmTask.setTaskProcDefId(taskObj.get("procDefId").toString());
                bpmTask.setTaskDefKey(taskObj.get("taskDefKey").toString());
                bpmTask.setTaskCaseInstId(taskObj.get("caseInstId") != null ? taskObj.get("caseInstId").toString() : null);
                bpmTask.setTaskName(taskObj.get("name").toString());

                // check auto change implementer
                String newAssignee = null;
                if (taskObj.get("assignee") != null) {
                    AssignManagement assignManagement = getAuthority(
                            taskObj.get("assignee").toString(),
                            bpmProcInst.getTicketId(),
                            taskObj.get("id").toString(),
                            taskDefKey
                    );
                    if (!ValidationUtils.isNullOrEmpty(assignManagement)) {
                        Map<String, Object> mapAssignee = bpmProcInstManager.getAllAssigneeForDrawFlow(bpmProcInst.getTicketId());
                        List<String> lstAssignee = (List<String>) mapAssignee.get(taskObj.get("taskDefKey").toString());
                        // check case uq task song song/ tuan tu add 1 user nhieu task
                        if (lstAssignee != null && !lstAssignee.contains(assignManagement.getAssignedUser())) {
                            newAssignee = assignManagement.getAssignedUser();
                            bpmTask.setAssignType(true);

                            // save change assignee history
                            ChangeAssigneeHistory changeAssigneeHistory = new ChangeAssigneeHistory();
                            changeAssigneeHistory.setBpmProcinstId(bpmProcInst.getTicketId());
                            changeAssigneeHistory.setProcInstId(taskObj.get("procInstId").toString());
                            changeAssigneeHistory.setTaskId(taskObj.get("id").toString());
                            changeAssigneeHistory.setTaskDefKey(taskObj.get("taskDefKey").toString());
                            changeAssigneeHistory.setChangeTime(LocalDateTime.now());
                            changeAssigneeHistory.setOrgAssignee(taskObj.get("assignee").toString());
                            changeAssigneeHistory.setFromAssignee(taskObj.get("assignee").toString());
                            changeAssigneeHistory.setToAssignee(newAssignee);
                            changeAssigneeHistory.setOrgAssigneeTitle(assignManagement.getAssignTitle());
                            changeAssigneeHistory.setType(1);
                            changeAssigneeHistory.setAssignTicketId(assignManagement.getTicketId());
                            changeAssigneeHistoryService.save(changeAssigneeHistory);

                            // save camunda assignee
                            camundaEngineService.setTaskAssignee(taskObj.get("id").toString(), newAssignee);

                            // update sign zone
                            BpmTpSignZone signZone = bpmTpSignZoneRepository.findByProcInstIdAndTaskDefKeyAndEmail(procInstId, taskObj.get("taskDefKey").toString(), taskObj.get("assignee").toString());
                            if (signZone != null) {
                                // get user info
                                List<AccountModel> lstUserInfo = customerService.getAccountByUsernames(Arrays.asList(newAssignee, taskObj.get("assignee").toString(), bpmProcInst.getCreatedUser()));
                                if (!ValidationUtils.isNullOrEmpty(lstUserInfo)) {
                                    signZone.setFirstName(lstUserInfo.stream().filter(e -> e.getUsername().equalsIgnoreCase(assignManagement.getAssignedUser())).map(AccountModel::getFirstname).findFirst().orElse(null));
                                    signZone.setLastName(lstUserInfo.stream().filter(e -> e.getUsername().equalsIgnoreCase(assignManagement.getAssignedUser())).map(AccountModel::getLastname).findFirst().orElse(null));
                                    signZone.setPosition(lstUserInfo.stream().filter(e -> e.getUsername().equalsIgnoreCase(assignManagement.getAssignedUser())).map(AccountModel::getFinalTitle).findFirst().orElse(null));
                                }

                                signZone.setEmail(newAssignee);
                                signZone.setSign(null);
                                signZone.setSignedDate(null);
                                signZone.setComment(null);
                                signZone.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
                                signZone.setUpdatedDate(new Date());

                                bpmTpSignZoneRepository.save(signZone);
                            }
                        }
                    }
                }
                // Check candidate task - auto change implementer
                bpmTask.setTaskAssignee(taskObj.get("assignee") != null ? newAssignee != null ? newAssignee : taskObj.get("assignee").toString() : null);

                bpmTask.setTaskCreatedTime(LocalDateTime.now());
                Map<String, String> mapTaskData = mapXmlData.get(taskObj.get("taskDefKey").toString());
                String setting_taskType = mapTaskData.get("setting_taskType");
                String setting_slaFinish = mapTaskData.get("setting_slaFinish");
                String setting_slaResponse = mapTaskData.get("setting_slaResponse");

//                LocalDateTime slaFinDate = null;
//                Double finalSlaFinish = null;
                // Get value sla config system
//                double valueConfigSla = Double.parseDouble("0");
//                String strSystemConfig = customerService.getConfigSlaShift(bpmTask.getTaskAssignee());
//                if (!ValidationUtils.isNullOrEmpty(strSystemConfig)) {
//                    valueConfigSla = Double.parseDouble(strSystemConfig);
//                }
//                Long priorityHistoryId = bpmProcInst.getPriorityHistoryId();
//                Long priorityId = bpmProcInst.getPriorityId();
//                // get priority from priority_management_history
//                if (!ValidationUtils.isNullOrEmpty(priorityHistoryId)) {
//                    Double slaValue = priorityHistoryRepository.getValueById(priorityHistoryId);
//                    if (ValidationUtils.isNullOrEmpty(slaValue)) {
//                        slaValue = 100.0;
//                    }
//                    if (!ValidationUtils.isNullOrEmpty(slaValue)) {
//                        // (Thời gian SLA cài đặt + Giá trị cài đặt của config_sla_shift (nếu có)) * Chỉ số tăng giảm SLA (Cài đặt ở Độ ưu tiên-nếu có)
//                        // CHANGED: chỉ số tăng giảm sla -> tỉ lệ %
//                        finalSlaFinish = (Double.parseDouble(setting_slaFinish) + valueConfigSla) * (slaValue / 100.0);
//
//                        long timePriorityFinish = Math.round(finalSlaFinish * 60);
//                        slaFinDate = customerService.getTimeSla(timePriorityFinish, bpmTask.getTaskCreatedTime(), bpmTask.getTaskAssignee());
//                    }
//                } else if (!ValidationUtils.isNullOrEmpty(priorityId)) {
//                    Double slaValue = priorityManagementRepository.getValueById(priorityId);
//                    if (ValidationUtils.isNullOrEmpty(slaValue)) {
//                        slaValue = 100.0;
//                    }
//                    if (!ValidationUtils.isNullOrEmpty(slaValue)) {
//                        // (Thời gian SLA cài đặt + Giá trị cài đặt của config_sla_shift (nếu có)) * Chỉ số tăng giảm SLA (Cài đặt ở Độ ưu tiên-nếu có)
//                        // CHANGED: chỉ số tăng giảm sla -> tỉ lệ %
//                        finalSlaFinish = (Double.parseDouble(setting_slaFinish) + valueConfigSla) * (slaValue / 100.0);
//
//                        long timePriorityFinish = Math.round(finalSlaFinish * 60);
//                        slaFinDate = customerService.getTimeSla(timePriorityFinish, bpmTask.getTaskCreatedTime(), bpmTask.getTaskAssignee());
//                    }
//                }
//
//                if (slaFinDate == null) {
//                    // (Thời gian SLA cài đặt + Giá trị cài đặt của config_sla_shift (nếu có)) * Chỉ số tăng giảm SLA (Cài đặt ở Độ ưu tiên-nếu có)
//                    finalSlaFinish = Double.parseDouble(setting_slaFinish) + valueConfigSla;
//
//                    long slaFinMinute = Math.round(finalSlaFinish * 60);
//                    slaFinDate = LocalDateTime.now().plusMinutes(slaFinMinute);
//                }
//                bpmTask.setSlaResponseTime(slaResDate);
                // legislative sla - tính theo ngày cả t7, cn
                LocalDateTime slaFinDate = null;
                Double finalSlaFinish = null;
                // có cấu hình nhiệm vụ lập pháp
                if (!ValidationUtils.isNullOrEmpty(bpmProcInst.getLegislativeId())) {
                    LegislativeProgramDetail detail = legislativeProgramDetailRepository.findLegislativeProgramDetailByLegislativeIdAndTypeAndTaskDefKey(
                            bpmProcInst.getLegislativeId(), LegislativeEnum.LegislativeDetailType.PROCESS_DETAIL.code, taskObj.get("taskDefKey").toString()
                    );
                    if (detail.getFromDate() != null && detail.getToDate() != null) {
                        LocalDate fromDate = LocalDate.parse(detail.getFromDate());
                        LocalDate toDate = LocalDate.parse(detail.getToDate());
                        String processType = detail.getProcessType();
                        long totalDate = processType != null && processType.equals("shortcut")
                                ? 7 // quy trình rút gọn mặc định 7days, thêm cấu hình hệ thống sau
                                : ChronoUnit.DAYS.between(fromDate, toDate);
                        finalSlaFinish = (double) totalDate;
                        slaFinDate = bpmTask.getTaskCreatedTime()
                                .plusDays(totalDate + 1)
                                .toLocalDate().atStartOfDay();
                    }
                }
                // default
                if (slaFinDate == null) {
                    slaFinDate = bpmTask.getTaskCreatedTime()
                            .plusDays(Math.round(Double.parseDouble(setting_slaFinish)) + 1)
                            .toLocalDate().atStartOfDay();
                }
                bpmTask.setSlaFinishTime(slaFinDate);

                bpmTask.setSlaFinish(!ValidationUtils.isNullOrEmpty(finalSlaFinish) ? finalSlaFinish : (!ValidationUtils.isNullOrEmpty(setting_slaFinish) ? Double.valueOf(setting_slaFinish) : null));
                bpmTask.setSlaResponse(!ValidationUtils.isNullOrEmpty(setting_slaResponse) ? Double.valueOf(setting_slaResponse) : null);

                if (setting_taskType.equalsIgnoreCase(TaskConstants.Type.APPROVAL.code)) {
                    bpmTask.setTaskType(TaskConstants.Type.APPROVAL.code);
                } else {
                    bpmTask.setTaskType(TaskConstants.Type.EXECUTION.code);
                    bpmTask.setTaskStartedTime(null);
                }

                bpmTask.setTaskFinishedTime(null);
                bpmTask.setTaskDoneTime(null);
                bpmTask.setTaskCreatedUser(null);
                bpmTask.setTaskStatus(TaskConstants.Status.ACTIVE.code);
                bpmTask.setTaskIsFirst(false);

                listSave.add(bpmTask);

            }
            List<BpmTask> savedBpmTasks;
            log.info("list save task: {}", listSave);

            try {
                savedBpmTasks = bpmTaskRepository.saveAll(listSave);

                // (phucvm3) save bpm_task_user
                bpmTaskUserService.saveFromBpmTasks(savedBpmTasks);
            } catch (Exception e) {
                log.error("retry save task fail: {}", e.getMessage());

                savedBpmTasks = bpmTaskRepository.saveAll(listSave);
                bpmTaskUserService.saveFromBpmTasks(savedBpmTasks);
            }

            if (!ValidationUtils.isNullOrEmpty(savedBpmTasks)) {
                List<String> taskDefKeys = savedBpmTasks.stream().map(BpmTask::getTaskDefKey).distinct().toList();
                for (String taskDefkey : taskDefKeys) {
                    //Thông báo cho user liên quan APPROVAL/EXECUTION
                    Map<String, VariableValueDto> variablesNotifi = new HashMap<>();
                    VariableValueDto variableDto;

                    variableDto = new VariableValueDto();
                    variableDto.setType("String");
                    variableDto.setValue(savedBpmTasks.stream().filter(e -> e.getTaskDefKey().equalsIgnoreCase(taskDefkey)).toList().get(0).getTaskName());
                    variablesNotifi.put("nextTaskName", variableDto);

//                    NotificationUser payload = new NotificationUser();
//                    payload.setBpmProcdefId(null);
//                    payload.setNextTaskDefKey(taskDefkey);
//                    payload.setVariables(variablesNotifi);
//                    payload.setTicketId(bpmProcInst.getTicketId());
//                    payload.setIsGetOldVariable(true);
//                    payload.setLstCustomerEmails(null);
//                    payload.setActionCode(taskType);
//                    payload.setEmailExe(account);
//                    kafkaTemplate.send(notificationUser, payload);
                    bpmProcdefNotificationService.addNotificationsByConfig(null, taskDefkey, variablesNotifi,
                            bpmProcInst.getTicketId(), true, null, taskType, account);
                }
            }

            //Xử lý lưu version biểu mẫu
            handleSaveVersionTemplateTask(savedBpmTasks, bpmProcInst.getTicketCreatedTime(), bpmProcInst.getServiceId());

            try {
                for (BpmTask task : listSave) {
                    saveCreateTaskHistory(task, taskId, procInstId, bpmProcInst, taskDefKey);
                }
            } catch (Exception e) {
                log.error("retry save task history, err: {}", e.getMessage());
                for (BpmTask task : listSave) {
                    saveCreateTaskHistory(task, taskId, procInstId, bpmProcInst, taskDefKey);
                }
            }

            return savedBpmTasks;

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public void updateTaskBpm(BpmTask bpmTask, String account, BpmProcInst bpmProcInst, List<String> listTaskDefKey, Map<String, VariableValueDto> variableDto, Boolean isAutoComplete) {
        bpmTask.setTaskStatus(TaskConstants.Status.COMPLETED.code);
        bpmTask.setTaskFinishedTime(LocalDateTime.now());
        if (bpmTask.getTaskStartedTime() == null) {
            bpmTask.setTaskStartedTime(LocalDateTime.now());
        }
        LocalDateTime startTime = bpmTask.getTaskStartedTime() == null ? LocalDateTime.now() : bpmTask.getTaskStartedTime();
        long doneTime = Duration.between(startTime, LocalDateTime.now()).toMinutes();
        bpmTask.setFinishDuration(doneTime);
        bpmTask.setTaskDoneTime((double) doneTime);
        bpmTask.setActionUser(account);
        bpmTaskRepository.save(bpmTask);

        String note = "";
        for (Map.Entry<String, VariableValueDto> entry : variableDto.entrySet()) {
            String key = entry.getKey();
            if (key.contains("attachmentSignComment")) {
                note = entry.getValue().getValue().toString();
            }
        }

        if (isAutoComplete) {
            note = "Hệ thống tự động hoàn thành phê duyệt do cùng người duyệt.";
        }

        // Lưu lịch sử hoàn thành task
        if (!ValidationUtils.isNullOrEmpty(listTaskDefKey)) {
            for (String toTaskDefKey : listTaskDefKey) {
                saveCompleteTaskHistory(toTaskDefKey, account, bpmTask, bpmProcInst, note);
            }
        }
    }

    public List<BpmTaskDto> loadActiveTaskByType(String type, String username) {
        try {
            List<BpmTask> bpmTasks = bpmTaskRepository.loadActiveTaskByType(type, username);
            return bpmTasks.stream().map(bpmTaskMapper::entityToDto).collect(Collectors.toList());
        } catch (Exception e) {
            return null;
        }
    }

    public PageDto myTask(MyTaskRequest req, String account) {
        try {

            List<BpmTaskUser> bpmTaskUserList = bpmTaskUserService.getAllByUserName(account);
            List<String> taskIds = bpmTaskUserList.stream().map(BpmTaskUser::getTaskId).collect(Collectors.toList());
            req.setTaskId(taskIds);
            req.setUser(account);

            Map<String, Object> mapData = bpmTaskSpecification.getMyTask(req);
            if (!ValidationUtils.isNullOrEmpty(mapData)) {
                Long totalItems = (Long) mapData.get("count");
                Integer totalPage = common.getPageCount(totalItems, req.getLimit());
                List<MyTaskResponse> listRes = (List<MyTaskResponse>) mapData.get("data");

                // get list assignee
                List<String> procInstId = listRes.stream().map(MyTaskResponse::getProcInstId).collect(Collectors.toList());
                List<BpmTask> listTask = bpmTaskRepository.getBpmTaskByTaskStatusInAndTaskProcInstIdIn(TaskConstants.TabStatus.PROCESSING, procInstId);
                Map<String, List<BpmTask>> groupByProcId = listTask.stream().collect(Collectors.groupingBy(BpmTask::getTaskProcInstId));
                List<BpmTaskUser> lstTaskUser = bpmTaskUserService.getAllTaskUserByTaskIdIn(listTask.stream().map(BpmTask::getTaskId).collect(Collectors.toList()));

                if (req.getSortBy().equalsIgnoreCase("remainingTime")) {
                    sortByRemainingTime(listRes, req.getSortType());
                }

                for (MyTaskResponse response : listRes) {
                    List<BpmTask> listTaskById = groupByProcId.get(response.getProcInstId());
                    if (listTaskById != null && !listTaskById.isEmpty()) {
                        List<TaskDetailResponse> ticketTaskDtoList = listTaskById.stream().map(x -> modelMapper.map(x, TaskDetailResponse.class)).collect(Collectors.toList());
                        for (TaskDetailResponse taskDetailResponse : ticketTaskDtoList) {
                            if (ValidationUtils.isNullOrEmpty(taskDetailResponse.getTaskAssignee()) && lstTaskUser != null) { // check đồng duyệt
                                List<String> userCandidates = lstTaskUser.stream()
                                        .filter(e -> e.getTaskId().equalsIgnoreCase(taskDetailResponse.getTaskId()))
                                        .map(BpmTaskUser::getUserName).collect(Collectors.toList());
                                if (!ValidationUtils.isNullOrEmpty(userCandidates)) {
                                    taskDetailResponse.setTaskUsers(userCandidates);
                                }
                            }
                        }
                        response.setTicketTaskDtoList(ticketTaskDtoList);
                    } else {
                        response.setTicketTaskDtoList(new ArrayList<>());
                    }
                }

                return PageDto.builder()
                        .content(listRes)
                        .number(req.getPage())
                        .numberOfElements(req.getPage())
                        .page(req.getPage())
                        .size(req.getLimit())
                        .totalPages(totalPage)
                        .totalElements(totalItems)
                        .build();
            }
            return null;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public List<Map<String, Object>> myTaskFilter(MyTaskRequest req, String account) {
        try {
            List<BpmTaskUser> bpmTaskUserList = bpmTaskUserService.getAllByUserName(account);
            List<String> taskIds = bpmTaskUserList.stream().map(BpmTaskUser::getTaskId).collect(Collectors.toList());
            req.setTaskId(taskIds);
            req.setUser(account);
            return bpmTaskSpecification.getMyTaskFilter(req);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Map<String, Object> getFilterMyTask(MyTaskRequest req, String email) {
        try {
            List<String> listServices = servicePackageManager.getAllservicePackagesActive();
            List<String> listUsers = bpmProcInstManager.getAllUserCreate();
            List<String> listSubmissionType = submissionTypeManager.getAllSubmissionTypeName();

            Map<String, Object> mapFinal = new HashMap<>();
            mapFinal.put("listUsers", listUsers);
            mapFinal.put("listServices", listServices);
            mapFinal.put("listSubmissionType", listSubmissionType);
            return mapFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public TaskDetailResponse getTaskDetail(Long procInstId, String taskKey, String user, String status, String account) {
        try {
            BpmTaskDto dto = new BpmTaskDto();
            Sort sort = responseUtils.getSort("taskCreatedTime", "DESC");
            Map<String, Object> mapStart = new HashMap<>();
            Map<String, Object> mapFinish = new HashMap<>();
            Map<String, Object> mapActionUser = new HashMap<>();
            Map<String, Object> mapOrgAssignee = new HashMap<>();
            List<Map<String, Object>> listPrintPhaseRes;
            List<BpmTemplatePrint> listPrintPhase;
            dto.setTicketId(procInstId);
            dto.setTaskDefKey(taskKey);
            dto.setTaskAssignee(user);
            Page<BpmTask> pageTask = bpmTaskRepository.findAll(bpmTaskSpecification.filter(dto), PageRequest.of(0, 2, sort));
            List<BpmTask> bpmTasks = pageTask.getContent();
            TaskDetailResponse taskDetailRes = new TaskDetailResponse();
            List<Map<String, Object>> listVariables = new ArrayList<>();
            List<Map<String, Object>> draftVariables = new ArrayList<>();
            BpmTask task = null;
            BpmProcInst bpmProcInst = bpmProcInstManager.findById(procInstId);
            if (!ValidationUtils.isNullOrEmpty(bpmTasks)) {
                task = bpmTasks.get(0);
                if (bpmTasks.size() == 2 && status != null && status.equalsIgnoreCase(TaskConstants.Status.DELETED_BY_RU.code)) {
                    task = bpmTasks.get(1);
                }
                BpmTpTask printPhase = null;
                List<BpmTpTask> listPrint = bpmPrintPhaseTaskRepository.findBpmTpTaskByTaskDefKeyAndProcDefId(task.getTaskDefKey(), task.getTaskProcDefId());
                if (!listPrint.isEmpty()) {
                    printPhase = listPrint.get(0);
                }

                // check permission of start button
                Integer checkStart = customerService.checkStartPhase(bpmTasks.get(0).getTaskProcDefId(), bpmTasks.get(0).getTaskDefKey());

                // check task đồng duyệt
                Map<String, Map<String, String>> mapXmlData = getXmlData(task.getTaskProcDefId());
                Map<String, String> mapTaskData = mapXmlData.get(task.getTaskDefKey());
                String candidateUser = mapTaskData.get("candidateUser");

                // phucvm3 [********]: BEGIN check identity links if asignee is null + kienpn4 [********]: if task is candidateTask
                if (task.getTaskAssignee() != null && candidateUser == null) {
                    if (task.getTaskAssignee().equalsIgnoreCase(account)) {
                        checkStart = 2;
                    }

                    // check task has changed assignee
                    List<ChangeAssigneeHistory> changeAssigneeHistories = changeAssigneeHistoryService.getLatestChangeByToAssignee(procInstId, task.getTaskId());
                    if (!ValidationUtils.isNullOrEmpty(changeAssigneeHistories)) {
                        ChangeAssigneeHistory changeAssigneeHistory = changeAssigneeHistories.get(0);
                        String orgAssignee = changeAssigneeHistory.getOrgAssignee();
                        String orgAssigneeTitle = changeAssigneeHistory.getOrgAssigneeTitle();
                        Integer typeAssignee = changeAssigneeHistory.getType();

                        //Được thực hiện bước hiện tại cho đến khi hoàn thành phiếu
                        int effect = 1;
                        if (typeAssignee == 1) {
                            effect = changeAssigneeHistory.getAssignManagement().getEffect();
                        }

                        if (orgAssignee != null && orgAssigneeTitle != null) {
                            String orgAssigneeFullName = responseUtils.getFullname(orgAssignee);
                            mapOrgAssignee.put("account", orgAssignee);
                            mapOrgAssignee.put("title", orgAssigneeTitle);
                            mapOrgAssignee.put("fullName", orgAssigneeFullName);
                            mapOrgAssignee.put("isExpired", false);
                            mapOrgAssignee.put("typeAssignee", typeAssignee); // 0: Ủy quyền trong phiếu - 1: ủy quyền tờ trình
                        }

                        // check expire assignment
                        if (effect != 1
                                && !orgAssignee.equalsIgnoreCase(task.getTaskAssignee())
                                && assignManager.isAssignmentExpired(orgAssignee, task.getTaskAssignee(), bpmProcInst.getServiceId())) {
                            checkStart = 0;
                            mapOrgAssignee.put("isExpired", true);
                        }

                        if (orgAssignee != null && orgAssignee.equalsIgnoreCase(account)) {
                            checkStart = 2;
                        }
                    }
                } else {
                    List<IdentityLink> identityLinks = camundaEngineService.getIdentityLinks(task.getTaskId());
                    IdentityLink identity = identityLinks.stream().filter(identityLink -> account.equalsIgnoreCase(identityLink.getUserId())).findAny().orElse(null);
                    if (identity != null) {
                        checkStart = 3;
                    }
                }
                // phucvm3 [********]: END check identity links if asignee is null

                // End check
                taskDetailRes = modelMapper.map(task, TaskDetailResponse.class);
                taskDetailRes.setProcDefId(task.getTaskProcDefId());
                taskDetailRes.setStartPermission(checkStart);
                taskDetailRes.setNewId(bpmTasks.get(0).getId());
                taskDetailRes.setNewTaskId(bpmTasks.get(0).getTaskId());
                taskDetailRes.setNewStatus(bpmTasks.get(0).getTaskStatus());
                taskDetailRes.setProcInstId(task.getTaskProcInstId());
                taskDetailRes.setFullName(responseUtils.getFullname(task.getTaskAssignee()));
                if (printPhase != null) {
                    taskDetailRes.setPrintId(printPhase.getId());
                }

                // get origin assignee info
                taskDetailRes.setTaskOrgAssignee(mapOrgAssignee);

                // get list ticket---------------------------//
                if (bpmProcInst != null) {
                    taskDetailRes.setTicketId(bpmProcInst.getTicketId());

                    // Ưu tiên lấy priority theo lịch sử
                    if (bpmProcInst.getPriorityHistoryId() != null) {
                        Optional<PriorityManagementHistory> priorityManagementHistoryOptional = priorityHistoryRepository.findById(bpmProcInst.getPriorityHistoryId());
                        if (priorityManagementHistoryOptional.isPresent()) {
                            PriorityManagementHistory priorityManagementHistory = priorityManagementHistoryOptional.get();
                            taskDetailRes.setPriorityId(priorityManagementHistory.getPriorityId());
                            taskDetailRes.setTaskPriority(priorityManagementHistory.getName());
                            taskDetailRes.setColor(priorityManagementHistory.getColor());
                        }
                    } else if (bpmProcInst.getPriorityId() != null) {
                        Optional<PriorityManagement> priorityManagementOpt = priorityManager.getById(bpmProcInst.getPriorityId());
                        if (priorityManagementOpt.isPresent()) {
                            PriorityManagement priorityManagement = priorityManagementOpt.get();
                            taskDetailRes.setTaskPriority(priorityManagement.getName());
                            taskDetailRes.setPriorityId(priorityManagement.getId());
                            taskDetailRes.setColor(priorityManagement.getColor());
                        }
                    }
                }

                // get list variables---------------------------//
                List<BpmVariables> listVari;
                // get list variables draft
                List<BpmVariables> listDraftVariables;
                if (bpmTasks.size() == 2) {
                    listVari = bpmDraftVariablesRepo.getAllByTaskId(bpmTasks.get(1).getTaskId());
                    listDraftVariables = bpmDraftVariablesRepo.getAllByTaskIdAndIsDraft(bpmTasks.get(0).getTaskId(), 1);
                } else {
                    listVari = bpmDraftVariablesRepo.getAllByTaskId(bpmTasks.get(0).getTaskId());
                    listDraftVariables = bpmDraftVariablesRepo.getAllByTaskIdAndIsDraft(bpmTasks.get(0).getTaskId(), 1);
                }

                Double slaResponse = task.getSlaResponse();
                Double slaFinish = task.getSlaFinish();

                Date startTime = Date.from(task.getTaskCreatedTime().atZone(ZoneId.systemDefault()).toInstant());

                mapStart.put("sla", slaResponse);
                if (task.getTaskStartedTime() == null) {
                    mapStart.put("expected", startTime);
                    mapStart.put("actual", null);
                    mapStart.put("status", -1);
                } else {
                    Date startActual = Date.from(task.getTaskStartedTime().atZone(ZoneId.systemDefault()).toInstant());
                    if (task.getTaskStartedTime().isAfter(task.getTaskCreatedTime())) {
                        mapStart.put("expected", startTime);
                        mapStart.put("actual", startActual);
                        mapStart.put("status", -1);
                    } else if (task.getTaskStartedTime().isBefore(task.getTaskCreatedTime())) {
                        mapStart.put("expected", startTime);
                        mapStart.put("actual", startActual);
                        mapStart.put("status", 1);
                    } else {
                        mapStart.put("expected", startTime);
                        mapStart.put("actual", startActual);
                        mapStart.put("status", 0);
                    }
                }

                Date finishTime = Date.from(task.getSlaFinishTime().atZone(ZoneId.systemDefault()).toInstant());

                mapFinish.put("sla", slaFinish);
                if (task.getTaskFinishedTime() == null) {
                    mapFinish.put("expected", finishTime);
                    mapFinish.put("actual", null);
                    mapFinish.put("status", -1);
                } else {
                    Date finishActual = Date.from(task.getTaskFinishedTime().atZone(ZoneId.systemDefault()).toInstant());
                    if (task.getTaskFinishedTime().isAfter(task.getTaskCreatedTime())) {
                        mapFinish.put("expected", finishTime);
                        mapFinish.put("actual", finishActual);
                        mapFinish.put("status", -1);
                    } else if (task.getTaskFinishedTime().isBefore(task.getTaskCreatedTime())) {
                        mapFinish.put("expected", finishTime);
                        mapFinish.put("actual", finishActual);
                        mapFinish.put("status", 1);
                    } else {
                        mapFinish.put("expected", finishTime);
                        mapFinish.put("actual", finishActual);
                        mapFinish.put("status", 0);
                    }
                }

                // ---------------- Handle print sign template---------------------------//
                final String taskKeySign = task.getTaskDefKey();
                if (task.getTaskType().equalsIgnoreCase("approval")) {
                    listPrintPhase = bpmTemplatePrintRepository.getApprovalPrintPhase(task.getTaskProcDefId(), task.getTaskProcInstId(), Collections.singletonList(task.getTaskDefKey()));
                    listPrintPhaseRes = listPrintPhase.stream().map(x -> {
                        Map<String, Object> mapPrint = new HashMap<>();
                        mapPrint.put("id", x.getId());
                        mapPrint.put("pdfContent", x.getPdfContent());
                        mapPrint.put("content", x.getContent());
                        mapPrint.put("taskDefKey", taskKeySign);
                        mapPrint.put("templateName", x.getName());
                        mapPrint.put("uploadWordsChange", x.getUploadWordsChange());
                        return mapPrint;
                    }).collect(Collectors.toList());
                } else {
                    List<String> listNextKey = responseUtils.getNextTaskKey(task.getTaskProcDefId(), task.getTaskDefKey());
                    Boolean checkEdit = bpmTaskRepository.existsByTaskProcInstIdAndTaskStatusAndTaskDefKeyIn(task.getTaskProcInstId(), "PROCESSING", listNextKey);
                    taskDetailRes.setEditPermission(!checkEdit);
                    listPrintPhase = bpmTemplatePrintRepository.getExecutionPrintPhase(task.getTaskProcDefId(), Collections.singletonList(task.getTaskDefKey()));
                    listPrintPhaseRes = listPrintPhase.stream().map(x -> {
                        Map<String, Object> mapPrint = new HashMap<>();
                        mapPrint.put("id", x.getId());
                        mapPrint.put("pdfContent", x.getPdfContent());
                        mapPrint.put("content", x.getContent());
                        mapPrint.put("taskDefKey", taskKeySign);
                        mapPrint.put("templateName", x.getName());
                        mapPrint.put("uploadWordsChange", x.getUploadWordsChange());
                        return mapPrint;
                    }).collect(Collectors.toList());
                }
                // ----------------------End Handle sign template---------------------------//
                taskDetailRes.setResponseTime(mapStart);
                taskDetailRes.setFinishTime(mapFinish);
                taskDetailRes.setListSignForm(listPrintPhaseRes);
                if (!listVari.isEmpty()) {
                    for (BpmVariables var : listVari) {
                        Map<String, Object> variable = new HashMap<>();
                        variable.put("name", var.getName());
                        variable.put("type", var.getType());
                        switch (var.getType().toLowerCase()) {
                            case "string":
                                variable.put("value", var.getStringVal());
                                break;
                            case "double":
                                variable.put("value", var.getDoubleVal());
                                break;
                            case "long":
                            case "integer":
                                variable.put("value", var.getLongVal());
                                break;
                            case "json":
                                variable.put("value", var.getJsonVal());
                                break;
                            case "file":
                                variable.put("value", var.getDownloadUrl());
                                break;
                        }
                        listVariables.add(variable);
                    }
                    taskDetailRes.setListVariables(listVariables);
                }

                if (!listDraftVariables.isEmpty()) {
                    for (BpmVariables varDraft : listDraftVariables) {
                        Map<String, Object> variableDraft = new HashMap<>();
                        variableDraft.put("name", varDraft.getName());
                        variableDraft.put("type", varDraft.getType());
                        variableDraft.put("isDraft", varDraft.getIsDraft());
                        variableDraft.put("additionalVal", varDraft.getAdditionalVal());
                        switch (varDraft.getType().toLowerCase()) {
                            case "string":
                                variableDraft.put("value", varDraft.getStringVal());
                                break;
                            case "double":
                                variableDraft.put("value", varDraft.getDoubleVal());
                                break;
                            case "long":
                            case "integer":
                                variableDraft.put("value", varDraft.getLongVal());
                                break;
                            case "json":
                                variableDraft.put("value", varDraft.getJsonVal());
                                break;
                            case "file":
                                variableDraft.put("value", varDraft.getDownloadUrl());
                                break;
                        }
                        draftVariables.add(variableDraft);
                    }
                    taskDetailRes.setDraftVariables(draftVariables);
                }
            }

            // get info actionUser bpm_task
            if (bpmTasks.get(0).getActionUser() != null) {
                String actionUserFullName = responseUtils.getFullname(bpmTasks.get(0).getActionUser());
                mapActionUser.put("account", bpmTasks.get(0).getActionUser());
                mapActionUser.put("fullName", actionUserFullName);
            } else {
                mapActionUser.put("account", null);
                mapActionUser.put("fullName", null);
            }

            taskDetailRes.setTaskActionUser(mapActionUser);

            // phucvm3: get list of user_name from bpm_task_user
            if (task != null) {
                taskDetailRes.setTaskUsers(bpmTaskUserService.getAllUserName(task.getTaskProcInstId(), task.getTaskDefKey()));

                // (phucvm3) add signed files
                taskDetailRes.setSignedFiles(bpmTpSignZoneManager.getSignedFiles(task.getTaskProcInstId(), null));
            }

            return taskDetailRes;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Map<String, Long> countTaskStatus(String type, MyTaskRequest request, Boolean filterChangeAssignee, String search) {
        try {
            String account = credentialHelper.getJWTPayload().getUsername();

            List<BpmTaskUser> bpmTaskUserList = bpmTaskUserService.getAllByUserName(account);
            List<String> taskIds = bpmTaskUserList.stream().map(BpmTaskUser::getTaskId).collect(Collectors.toList());

            Long countOngoing = bpmTaskSpecification.countOngoing(account, type, filterChangeAssignee, taskIds, search);
            Long countCompleted = bpmTaskSpecification.countCompleted(account, type, filterChangeAssignee, taskIds, search);
            Long countCancel = bpmTaskSpecification.countCancel(account, type, filterChangeAssignee, taskIds, search);
            Long countShared = bpmTaskSpecification.countShare(account, type, filterChangeAssignee, search);
            Long countRecalled = bpmTaskSpecification.countRecalled(account, type, filterChangeAssignee, taskIds, search);
            Map<String, Long> mapFinal = new HashMap<>();

            mapFinal.put("CANCEL", countCancel);
            mapFinal.put("SHARED", countShared);
            mapFinal.put("COMPLETED", countCompleted);
            mapFinal.put("ONGOING", countOngoing);
            mapFinal.put("RECALLED", countRecalled);
            return mapFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Map<String, Long> countTypeByUser(String account) {
        try {
            List<Object[]> bpmTasks = bpmTaskRepository.CountType(account);
            Map<String, Long> mapFinal = new HashMap<>();
            if (bpmTasks != null && !bpmTasks.isEmpty()) {
                for (Object[] o : bpmTasks) {
                    mapFinal.put(o[0].toString(), Long.parseLong(o[1].toString()));
                }
            }
            return mapFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Date startPhase(String taskId, String procInstId, Boolean isAutoClaim, String account) {
        log.info("Start phase ==> procInstId={}, taskId={}", procInstId, taskId);
        LocalDateTime date = LocalDateTime.now();
        BpmTask task = getBpmTaskByTaskId(taskId);
        BpmProcInst bpmProcInst = bpmProcInstManager.findBpmProcInstByTicketProcInstId(procInstId);

        if (task == null) {
            return null;
        }

        // check task allow process
        List<String> checkStatus = Arrays.asList(ProcInstConstants.Status.RECALLING.code, ProcInstConstants.Status.CANCEL.code, ProcInstConstants.Status.RECALLED.code);
        if (task.getTaskStatus().equalsIgnoreCase(TaskConstants.Status.DELETED_BY_RU.code)
                || task.getTaskStatus().equalsIgnoreCase(TaskConstants.Status.COMPLETED.code)
                || checkStatus.contains(bpmProcInst.getTicketStatus())) {
            throw new RuntimeException(common.getMessage("message.task.completed.not-allow"));
        }

        /* BEGIN handle call action api on beginning */
        ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(TaskActionConstants.Action.START_TASK.code,
                task.getTaskProcDefId(),
                null,
                procInstId,
                actionApiService.createVariablesMap(task, bpmProcInst));
        /* END handle call action api on beginning */

        List<BpmProcInst> listTicket = bpmProcInstRepository.findAll(bpmProcInstSpec.filterStartTicket(task.getTaskProcInstId()));

        if (!listTicket.isEmpty()) {
            BpmProcInst ticket = listTicket.get(0);
            ticket.setTicketStartedTime(date);
            bpmProcInstRepository.save(ticket);
        }

        // (phucvm3) claim task by camunda engine - case candidate task
        if (isAutoClaim) {
            camundaEngineService.claimTask(taskId, account);
            task.setTaskAssignee(account);

            // (phucvm3) update sign zones info
            bpmTpSignZoneManager.updateSignZones(procInstId, task.getTaskDefKey(), account, bpmProcInst.getCreatedUser());

            // (kienpn4) update history action CREATE_TASK
            BpmHistory bpmHistory = bpmHistoryManager.getHistoryByTaskInstIdAndAction(task.getTaskId(), "CREATE_TASK");
            if (bpmHistory != null) {
                bpmHistory.setActionUser(account);

                // get action_user_info
                List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(account);
                if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
                    Map<String, Object> actionUserInfo = new HashMap<>();
                    String userTitle = lstUserTitle.stream()
                            .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                            .map(title -> {
                                String strTitle = StringUtil.nvl(title.getTitle(), "");
                                int concurrently = title.getConcurrently();
                                return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                            })
                            .collect(Collectors.joining(" "));
                    actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
                    actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
                    bpmHistory.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
                }
                bpmHistoryManager.save(bpmHistory);
            }
        }

        Long duration = Duration.between(task.getTaskCreatedTime(), LocalDateTime.now()).toMinutes();
        task.setResponseDuration(duration);
        task.setTaskStartedTime(date);
        task.setTaskStatus(TaskConstants.Status.PROCESSING.code);
        save(task);

        // only update ticket status to PROCESSING if current status different ADDITIONAL_REQUEST (...PENDING...)
        if (!bpmProcInst.getTicketStatus().equalsIgnoreCase(ProcInstConstants.Status.ADDITIONAL_REQUEST.code)) {
            bpmProcInstRepository.ongoingTicket(procInstId);
        }
//        bpmProcInstRepository.ongoingTicket(procInstId);

        /* BEGIN handle call action api at the end */
        bpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(procInstId);
        actionApiService.endHandleActionApi(actionApiContext, actionApiService.createVariablesMap(task, bpmProcInst));
        /* END handle call action api at the end */

        return Date.from(date.atZone(ZoneId.systemDefault()).toInstant());
    }

    public BpmTask save(BpmTask bpmTask) {
        bpmTaskRepository.save(bpmTask);
        return bpmTask;
    }

    public BpmTask getBpmTaskByTaskId(String taskId) {
        return bpmTaskRepository.getBpmTaskByTaskId(taskId);
    }

    /**
     * Save start-phase history
     *
     * <AUTHOR>
     */
    private void saveStartPhaseHistory(String account, BpmProcInst bpmProcInst, BpmTask task) {
        HistoryDto historyStartPharse = new HistoryDto();
        historyStartPharse.setActionUser(account);
        historyStartPharse.setAction(TaskConstants.HistoryAction.START_TASK.code);

        String procInstId = null;
        if (bpmProcInst != null) {
            historyStartPharse.setTicketId(bpmProcInst.getTicketId());
            historyStartPharse.setProcInstId(bpmProcInst.getTicketProcInstId());
            procInstId = bpmProcInst.getTicketProcInstId();
        }

        if (task != null) {
            historyStartPharse.setTaskDefKey(task.getTaskDefKey());
            historyStartPharse.setFromTaskKey(task.getTaskDefKey());
            historyStartPharse.setFromTask(task.getTaskId());
            historyStartPharse.setTaskInstId(task.getTaskId());
            historyStartPharse.setTaskType(task.getTaskType());
        }

        // get action_user_info
        List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(account);
        if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
            Map<String, Object> actionUserInfo = new HashMap<>();
            String userTitle = lstUserTitle.stream()
                    .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                    .map(title -> {
                        String strTitle = StringUtil.nvl(title.getTitle(), "");
                        int concurrently = title.getConcurrently();
                        return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                    })
                    .collect(Collectors.joining(" "));
            actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
            actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
            historyStartPharse.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
        }

        bpmHistoryManager.saveHistory(historyStartPharse);

        if (procInstId != null && task != null) {
            // Set history action-user
            List<BpmHistory> bpmHistories = bpmHistoryManager.getAllByTaskDefKey(bpmProcInst.getTicketId(), task.getTaskDefKey());
            if (!ValidationUtils.isNullOrEmpty(bpmHistories)) {
                bpmHistories.forEach(e -> e.setActionUser(account));
                bpmHistoryManager.saveAll(bpmHistories);
            }
        }
    }

    private void saveCreateTaskTicketHistory(BpmTask bpmTask, Long ticketId) {
        HistoryDto historyCreateTaskTicket = new HistoryDto();
        historyCreateTaskTicket.setTicketId(ticketId);
        historyCreateTaskTicket.setToTaskKey(null);
        historyCreateTaskTicket.setAction(TaskConstants.HistoryAction.CREATE_TASK.code);
        historyCreateTaskTicket.setToTask(null);

        if (bpmTask != null) {
            historyCreateTaskTicket.setActionUser(!ValidationUtils.isNullOrEmpty(bpmTask.getTaskAssignee()) ? bpmTask.getTaskAssignee() : null);
            historyCreateTaskTicket.setTaskDefKey(bpmTask.getTaskDefKey());
            historyCreateTaskTicket.setFromTask(bpmTask.getTaskId());
            historyCreateTaskTicket.setProcInstId(bpmTask.getTaskProcInstId());
            historyCreateTaskTicket.setTaskInstId(bpmTask.getTaskId());
            historyCreateTaskTicket.setFromTaskKey(bpmTask.getTaskDefKey());
            historyCreateTaskTicket.setReceivedTime(LocalDateTime.now());

            // get action_user_info
            if (!ValidationUtils.isNullOrEmpty(bpmTask.getTaskAssignee())) {
                List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(bpmTask.getTaskAssignee());
                if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
                    Map<String, Object> actionUserInfo = new HashMap<>();
                    String userTitle = lstUserTitle.stream()
                            .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                            .map(title -> {
                                String strTitle = StringUtil.nvl(title.getTitle(), "");
                                int concurrently = title.getConcurrently();
                                return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                            })
                            .collect(Collectors.joining(" "));
                    actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
                    actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
                    historyCreateTaskTicket.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
                }
            }
        }

        bpmHistoryManager.saveHistory(historyCreateTaskTicket);
    }

    private void saveCreateTaskHistory(BpmTask task, String taskId, String procInstId, BpmProcInst bpmProcInst, String taskDefKey) {
        HistoryDto historyCreateTask = new HistoryDto();
        historyCreateTask.setActionUser(!ValidationUtils.isNullOrEmpty(task.getTaskAssignee()) ? task.getTaskAssignee() : null);
        historyCreateTask.setTicketId(bpmProcInst.getTicketId());
        historyCreateTask.setAction(TaskConstants.HistoryAction.CREATE_TASK.code);
        historyCreateTask.setFromTask(task.getTaskId());
        historyCreateTask.setToTask(null);
        historyCreateTask.setFromTaskKey(task.getTaskDefKey());
        historyCreateTask.setToTaskKey(null);
        historyCreateTask.setTaskInstId(task.getTaskId());
        historyCreateTask.setTaskDefKey(task.getTaskDefKey());
        historyCreateTask.setTaskType(task.getTaskType());
        historyCreateTask.setProcInstId(procInstId);
        historyCreateTask.setReceivedTime(LocalDateTime.now());

        // get action_user_info
        if (!ValidationUtils.isNullOrEmpty(task.getTaskAssignee())) {
            List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(task.getTaskAssignee());
            if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
                Map<String, Object> actionUserInfo = new HashMap<>();
                String userTitle = lstUserTitle.stream()
                        .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                        .map(title -> {
                            String strTitle = StringUtil.nvl(title.getTitle(), "");
                            int concurrently = title.getConcurrently();
                            return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                        })
                        .collect(Collectors.joining(" "));
                actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
                actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
                historyCreateTask.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
            }
        }
        bpmHistoryManager.saveHistory(historyCreateTask);
    }

    private void saveCompleteTaskHistory(String toTaskDefKey, String account, BpmTask bpmTask, BpmProcInst bpmProcInst, String note) {
        //Check task được trả về
        BpmHistory bpmHistory = bpmHistoryManager.checkHistory(bpmTask.getTaskId());
        //Check task được trả về liên đới (trả về bước bắt đầu)
        String oldTaskIdAffected = bpmHistoryManager.getOldTaskIdAffected(bpmProcInst.getTicketId(), bpmProcInst.getTicketProcInstId(), bpmTask.getTaskDefKey(), account);

        HistoryDto historyryCompelteTask = new HistoryDto();
        historyryCompelteTask.setActionUser(account);
        historyryCompelteTask.setTicketId(bpmProcInst.getTicketId());
        historyryCompelteTask.setFromTask(bpmTask.getTaskId());
//        historyryCompelteTask.setToTask(task.getTaskId());
        historyryCompelteTask.setTaskDefKey(bpmTask.getTaskDefKey());
        historyryCompelteTask.setFromTaskKey(bpmTask.getTaskDefKey());
        historyryCompelteTask.setToTaskKey(toTaskDefKey);
        historyryCompelteTask.setAction(TaskConstants.HistoryAction.COMPLETE_TASK.code);
        historyryCompelteTask.setTaskInstId(bpmTask.getTaskId());
        historyryCompelteTask.setTaskType(bpmTask.getTaskType());
        historyryCompelteTask.setProcInstId(bpmProcInst.getTicketProcInstId());
//        historyryCompelteTask.setNote(convertHtmlToString(StringUtil.nvl(note, "")));
        historyryCompelteTask.setNote(StringUtil.nvl(note, ""));
        if (!ValidationUtils.isNullOrEmpty(bpmHistory)) {
            historyryCompelteTask.setOldTaskId(bpmHistory.getOldTaskId());
        } else if (!ValidationUtils.isNullOrEmpty(oldTaskIdAffected)) {
            historyryCompelteTask.setOldTaskId(oldTaskIdAffected);
        }

        // get action_user_info
        List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(account);
        if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
            Map<String, Object> actionUserInfo = new HashMap<>();
            String userTitle = lstUserTitle.stream()
                    .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                    .map(title -> {
                        String strTitle = StringUtil.nvl(title.getTitle(), "");
                        int concurrently = title.getConcurrently();
                        return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                    })
                    .collect(Collectors.joining(" "));
            actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
            actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
            historyryCompelteTask.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
        }
        bpmHistoryManager.saveHistory(historyryCompelteTask);
    }

    public Boolean claimTask(String account, String taskId) {
        try {
            String tenantId = (account.split("@"))[0];
            camundaEngineService.claimTask(taskId, tenantId);
            bpmTaskRepository.claimTask(tenantId, taskId);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public Map<String, String> changeImplementer(Map<String, Object> body, String account, Integer assignType) {
        /* AssignType
         * 0: Ủy quyền trong phiếu
         * 1: Ủy quyền tự động
         * 2: ...
         * */
        Map<String, String> mapFinal = new HashMap<>();

        String ticketId = body.get("ticketId") != null ? body.get("ticketId").toString() : null;
        String taskId = body.get("taskId") != null ? body.get("taskId").toString() : null;
        String taskDefKey = body.get("taskDefKey") != null ? body.get("taskDefKey").toString() : null;
        String newUser = body.get("email") != null ? body.get("email").toString() : null;
        String orgAssigneeTitle = body.get("title") != null ? body.get("title").toString() : null;
        Long assignTicketId = null;

        BpmTask task = bpmTaskRepository.getBpmTaskByTaskId(taskId);
        if (task == null) {
            return mapFinal;
        }
        BpmProcInst bpmProcInst = bpmProcInstManager.findBpmProcInstByTicketProcInstId(task.getTaskProcInstId());

        List<String> checkStatus = Arrays.asList(ProcInstConstants.Status.RECALLING.code, ProcInstConstants.Status.CANCEL.code, ProcInstConstants.Status.RECALLED.code, ProcInstConstants.Status.COMPLETED.code, ProcInstConstants.Status.DELETED_BY_RU.code);
        if (checkStatus.contains(task.getTaskStatus()) || checkStatus.contains(bpmProcInst.getTicketStatus())) {
            throw new RuntimeException(common.getMessage("message.task.change-assignee.not-allow"));
        }

        // set vào dto thay entity
        BpmTaskCallApiDto taskDto = modelMapper.map(task, BpmTaskCallApiDto.class);
        if (assignType != 2 && assignType != 3) {
            taskDto.setAssignType(true);
        }

        if (assignType == 1) {
            assignTicketId = (Long) body.get("assignTicketId");
        }

        Map<String, Object> optVariable = new HashMap<>();
        optVariable.put("accountLogin", account);
        /* BEGIN handle call action api on beginning */
        Map<String, VariableValueDto> variables = actionApiService.getTicketVariables(task.getTaskProcInstId());
        ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(
                TaskActionConstants.Action.CHANGE_EXECUTOR.code,
                task.getTaskProcDefId(),
                taskDefKey,
                task.getTaskProcInstId(),
                actionApiService.createVariablesMap(taskDto, bpmProcInst, variables, optVariable));
        /* END handle call action api on beginning */

        // update task assignee to new username
        camundaEngineService.setTaskAssignee(taskId, newUser);

        String oldUser = task.getTaskAssignee();

        String orgAssignee = null;
        String cancelAssignee = null;
        //Thông báo cho user được ủy quyền - chỉ UQ trong phiếu
        if (assignType == 0) {
            Map<String, VariableValueDto> variablesNotifi = new HashMap<>();
            VariableValueDto variableDto = null;

            variableDto = new VariableValueDto();
            variableDto.setType("String");
            variableDto.setValue(body.get("reason") != null ? body.get("reason").toString() : null);
            variablesNotifi.put("txt_LyDoUyQuyen", variableDto);

            variableDto = new VariableValueDto();
            variableDto.setType("String");
            variableDto.setValue(task.getTaskName());
            variablesNotifi.put("nextTaskName", variableDto);

            NotificationUser payload = new NotificationUser();
            payload.setBpmProcdefId(null);
            payload.setNextTaskDefKey(taskDefKey);
            payload.setVariables(variablesNotifi);
            payload.setTicketId(bpmProcInst.getTicketId());
            payload.setIsGetOldVariable(true);
            payload.setLstCustomerEmails(Collections.singletonList(newUser));
            payload.setActionCode(ProcInstConstants.Notifications.CHANGE_EXECUTOR.code);
            payload.setEmailExe(credentialHelper.getJWTPayload().getUsername());
            kafkaTemplate.send(notificationUser, payload);
//            bpmProcdefNotificationService.addNotificationsByConfig(null, taskDefKey, variablesNotifi,
//                    bpmProcInst.getTicketId(), true, Collections.singletonList(newUser), ProcInstConstants.Notifications.CHANGE_EXECUTOR.code);

            List<ChangeAssigneeHistory> changeAssigneeHistories = changeAssigneeHistoryService.getLatestChangeByToAssignee(bpmProcInst.getTicketId(), taskId);
            if (!ValidationUtils.isNullOrEmpty(changeAssigneeHistories)) { // Đã có ủy quyền
                ChangeAssigneeHistory changeAssigneeHistory = changeAssigneeHistories.get(0);
                orgAssignee = changeAssigneeHistory.getOrgAssignee();
                if (!changeAssigneeHistory.getOrgAssignee().equalsIgnoreCase(changeAssigneeHistory.getToAssignee())) {
                    cancelAssignee = task.getTaskAssignee();
                }
            } else { // Ủy quyền lần đầu
                orgAssignee = task.getTaskAssignee();
            }
            //Thông báo cho user ủy quyền
            payload = new NotificationUser();
            payload.setBpmProcdefId(null);
            payload.setNextTaskDefKey(taskDefKey);
            payload.setVariables(variablesNotifi);
            payload.setTicketId(bpmProcInst.getTicketId());
            payload.setIsGetOldVariable(true);
            payload.setLstCustomerEmails(Collections.singletonList(orgAssignee));
            payload.setActionCode(ProcInstConstants.Notifications.ORG_CHANGE_EXECUTOR.code);
            payload.setEmailExe(credentialHelper.getJWTPayload().getUsername());
            kafkaTemplate.send(notificationUser, payload);
//            bpmProcdefNotificationService.addNotificationsByConfig(null, taskDefKey, variablesNotifi,
//                    bpmProcInst.getTicketId(), true, Collections.singletonList(orgAssignee), ProcInstConstants.Notifications.ORG_CHANGE_EXECUTOR.code);
            // Thông báo cho user hủy ủy quyền
            if (!ValidationUtils.isNullOrEmpty(cancelAssignee)) {
                payload = new NotificationUser();
                payload.setBpmProcdefId(null);
                payload.setNextTaskDefKey(taskDefKey);
                payload.setVariables(variablesNotifi);
                payload.setTicketId(bpmProcInst.getTicketId());
                payload.setIsGetOldVariable(true);
                payload.setLstCustomerEmails(Collections.singletonList(cancelAssignee));
                payload.setActionCode(ProcInstConstants.Notifications.CANCEL_CHANGE_EXECUTOR.code);
                payload.setEmailExe(credentialHelper.getJWTPayload().getUsername());
                kafkaTemplate.send(notificationUser, payload);
//                bpmProcdefNotificationService.addNotificationsByConfig(null, taskDefKey, variablesNotifi,
//                        bpmProcInst.getTicketId(), true, Collections.singletonList(cancelAssignee), ProcInstConstants.Notifications.CANCEL_CHANGE_EXECUTOR.code);
            }
        }

        // update assignType cùng với assignee
        bpmTaskRepository.updateOldTaskToNewAssignee(ticketId, taskDefKey, oldUser, newUser, assignType != 2);

        // update sign zone
        BpmTpSignZone signZone = bpmTpSignZoneRepository.findByProcInstIdAndTaskDefKeyAndEmail(ticketId, taskDefKey, oldUser);
        if (signZone != null) {
            // get user info
            List<AccountModel> lstUserInfo = customerService.getAccountByUsernames(Arrays.asList(newUser, orgAssignee, bpmProcInst.getCreatedUser()));
            if (!ValidationUtils.isNullOrEmpty(lstUserInfo)) {
                signZone.setFirstName(lstUserInfo.stream().filter(e -> e.getUsername().equalsIgnoreCase(newUser)).map(AccountModel::getFirstname).findFirst().orElse(null));
                signZone.setLastName(lstUserInfo.stream().filter(e -> e.getUsername().equalsIgnoreCase(newUser)).map(AccountModel::getLastname).findFirst().orElse(null));
                signZone.setPosition(lstUserInfo.stream().filter(e -> e.getUsername().equalsIgnoreCase(newUser)).map(AccountModel::getFinalTitle).findFirst().orElse(null));
            }

            signZone.setEmail(newUser);
            signZone.setSign(null);
            signZone.setSignedDate(null);
            signZone.setComment(null);
            signZone.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
            signZone.setUpdatedDate(new Date());

            bpmTpSignZoneRepository.save(signZone);
        }

        // update bpm_task_user => create new taskUser
        BpmTaskUser bpmTaskUser = new BpmTaskUser();
        bpmTaskUser.setBpmTaskId(task.getId());
        bpmTaskUser.setTaskId(task.getTaskId());
        bpmTaskUser.setTaskDefKey(task.getTaskDefKey());
        bpmTaskUser.setTaskName(task.getTaskName());
        bpmTaskUser.setProcInstId(task.getTaskProcInstId());
        bpmTaskUser.setUserName(newUser);
        bpmTaskUserService.save(bpmTaskUser);
//        bpmTaskUserService.updateNewUserName(ticketId, taskId, oldUser, newUser);

        mapFinal.put("old", oldUser);
        mapFinal.put("new", newUser);
        mapFinal.put("taskName", task.getTaskName());
        mapFinal.put("procInstId", task.getTaskProcInstId());

        // save bpm_history - case UQ trong phiếu
        if (assignType == 0) {
            saveChangeImplementerHis(body, task, account);
        }

        // save change_assignee_history
        if (assignType != 3) {
            changeAssigneeHistoryService.save(bpmProcInst, task, oldUser, newUser, orgAssigneeTitle, assignType, assignTicketId);
        }
        /* BEGIN handle call action api at the end */
        actionApiService.endHandleActionApi(actionApiContext, actionApiService.createVariablesMap(taskDto, bpmProcInst, variables));
        /* END handle call action api at the end */

        return mapFinal;
    }

    private void saveChangeImplementerHis(@NonNull Map<String, Object> body, @NonNull BpmTask task, String account) {
        String taskDefKey = body.get("taskDefKey").toString();
        String taskId = body.get("taskId").toString();

        HistoryDto hisDto = new HistoryDto();
        hisDto.setActionUser(account);
        hisDto.setAction(TaskConstants.HistoryAction.CHANGE_IMPLEMENTER.code);
        hisDto.setTaskDefKey(taskDefKey);
        hisDto.setNote(body.get("reason").toString());
        hisDto.setFromTaskKey(taskDefKey);
        hisDto.setFromTask(taskId);
        hisDto.setTaskInstId(taskId);
        hisDto.setProcInstId(task.getTaskProcInstId());
        hisDto.setTicketId(body.get("id") != null ? Long.parseLong(body.get("id").toString()) : null);
//        hisDto.setReceivedTime(task.getTaskCreatedTime());

        // get action_user_info
        List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(account);
        if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
            Map<String, Object> actionUserInfo = new HashMap<>();
            String userTitle = lstUserTitle.stream()
                    .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                    .map(title -> {
                        String strTitle = StringUtil.nvl(title.getTitle(), "");
                        int concurrently = title.getConcurrently();
                        return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                    })
                    .collect(Collectors.joining(" "));
            actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
            actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
            hisDto.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
        }
        bpmHistoryManager.saveHistory(hisDto);
    }

    public void updateCamundaVariables(String procInstId) {
        List<BpmVariables> bpmVariables = bpmVariablesRepository.findBpmVariablesByProcInstId(procInstId);

        if (!CollectionUtils.isEmpty(bpmVariables)) {
            VariableModification variableModification = new VariableModification();
            variableModification.setModifications(getVariableMap(bpmVariables));

            camundaEngineService.updateProcessVariables(procInstId, variableModification);
        }
    }

    private Map<String, Variable> getVariableMap(List<BpmVariables> bpmVariables) {
        Map<String, Variable> variableMap = new HashMap<>();
        bpmVariables.forEach(var -> {
            Variable v = new Variable();
            v.setType(var.getType() != null ? var.getType().toLowerCase() : null);
            String value = null;
            if (!ValidationUtils.isNullOrEmpty(var.getStringVal())) {
                value = var.getStringVal();
            } else if (!ValidationUtils.isNullOrEmpty(var.getJsonVal())) {
                value = var.getJsonVal();
            }
            v.setValue(value);
            variableMap.put(var.getName(), v);
        });

        return variableMap;
    }

    public PageDto search(BpmTaskDto criteria) {
        log.info("process=search tasks, criteria={}", criteria);
        Page<BpmTask> page = bpmTaskRepository.findAll(bpmTaskSpecification.filter(criteria), PageRequest.
                of(criteria.getPage(), criteria.getSize(), Sort.by(Sort.Direction.valueOf(criteria.getSortBy()), criteria.getSortType())));
        return PageDto.builder()
                .content(page.getContent().stream().map(bpmTaskMapper::entityToDto).collect(Collectors.toList()))
                .number(page.getNumber())
                .numberOfElements(page.getNumberOfElements())
                .page(page.getNumber())
                .size(page.getSize())
                .totalPages(page.getTotalPages())
                .totalElements(page.getTotalElements())
                .build();
    }

    public void updateVariStatus(String procInstId, String taskId) {
        try {
            bpmDraftVariablesRepo.updateTaskType(procInstId, taskId);
        } catch (Exception e) {
            log.error("updateVariStatus, error={}", e.getMessage());
        }
    }

    public void saveTaskVari(Map<String, VariableValueDto> variables, String taskId, String procInstId) {
        saveTaskVari(variables, taskId, procInstId, false);
    }

    public void saveTaskVari(Map<String, VariableValueDto> variables, String taskId, String procInstId, boolean isDraft) {
        bpmVariablesService.saveVariables(procInstId, taskId, variables, isDraft);
    }

    public List<Map<String, Object>> getAssigneeByTicket(String procInstId, List<String> listTaskKey) {
        try {
            List<BpmTask> listTask = bpmTaskRepository.getTaskByTasksAndStatus(procInstId, listTaskKey, TaskConstants.TabStatus.PROCESSING);
            List<Map<String, Object>> listFinal = new ArrayList<>();
            if (!listTask.isEmpty()) {
                listFinal = listTask.stream().map(x -> {
                    Map<String, Object> dataFinal = new HashMap<>();
                    dataFinal.put("taskId", x.getTaskId());
                    dataFinal.put("assignee", x.getTaskAssignee());
                    dataFinal.put("taskName", x.getTaskName());
                    dataFinal.put("taskDefKey", x.getTaskDefKey());
                    return dataFinal;
                }).collect(Collectors.toList());
            }

            return listFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public CountTaskStatisticResponse countTaskCallCenter(List<String> accounts, Long chartId) {
        try {

            if (accounts.isEmpty()) {
                RestTemplate restTemplate = new RestTemplate();
                String url = sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key) + "/chart-node/getAllChartNodeByManager?chartId={chartId}";
                HashMap<String, Object> param = new HashMap<>();
                param.put("chartId", chartId);
                HttpEntity<?> entity = redirectApiUtils.RedirectGetAPI(credentialHelper.getJWTToken());
                ResponseEntity<Object> response = restTemplate.exchange(url, HttpMethod.GET, entity, Object.class, param);
                HashMap<String, Object> body = (HashMap<String, Object>) response.getBody();
                List<HashMap<String, Object>> data = (List<HashMap<String, Object>>) body.get("data");
                for (HashMap<String, Object> d : data) {
                    accounts.add((String) d.get("email"));
                }
            }
            List<BpmTask> bpmTasks = bpmTaskRepository.findAll(bpmTaskSpecification.countTaskCallCenter(accounts, chartId));

            return countTask(bpmTasks);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    private CountTaskStatisticResponse countTask(List<BpmTask> bpmTasks) {
        int processing = 0;
        int waiting = 0;
        int overdue = 0;
        long totalMilliseconds = 0L;
        for (BpmTask bpmTask : bpmTasks) {
            if (bpmTask.getTaskStartedTime() != null &&
                    (bpmTask.getSlaFinishTime() != null && Date.from(bpmTask.getSlaFinishTime().atZone(ZoneId.systemDefault()).toInstant()).after(new Date()))) {
                processing += 1;
            } else if (bpmTask.getTaskStartedTime() == null &&
                    (bpmTask.getSlaResponseTime() != null && Date.from(bpmTask.getSlaResponseTime().atZone(ZoneId.systemDefault()).toInstant()).after(new Date()))) {
                waiting += 1;
            } else if ((bpmTask.getTaskStartedTime() == null && (bpmTask.getSlaResponseTime() != null && Date.from(bpmTask.getSlaResponseTime().atZone(ZoneId.systemDefault()).toInstant()).before(new Date())))) {
                overdue += 1;
                waiting += 1;
                long milliseconds = (new Date()).getTime() - Date.from(bpmTask.getSlaResponseTime().atZone(ZoneId.systemDefault()).toInstant()).getTime();
                totalMilliseconds += milliseconds;
            } else if ((bpmTask.getTaskStartedTime() != null &&
                    (bpmTask.getSlaFinishTime() != null && Date.from(bpmTask.getSlaFinishTime().atZone(ZoneId.systemDefault()).toInstant()).before(new Date())))) {
                overdue += 1;
                processing += 1;
                long milliseconds = (new Date()).getTime() - Date.from(bpmTask.getSlaFinishTime().atZone(ZoneId.systemDefault()).toInstant()).getTime();
                totalMilliseconds += milliseconds;
            }
        }
        if (overdue != 0) {
            totalMilliseconds = totalMilliseconds / overdue;
        }
        CountTaskStatisticResponse count = new CountTaskStatisticResponse();
        count.setProcessing(processing);
        count.setWaiting(waiting);
        count.setOverdue(overdue);
        count.setOverdueTime(totalMilliseconds);

        return count;
    }

    public PageDto getCountTaskListDetail(CountTaskListDetailRequest req, Long chartId) {
        try {
            List<CountTaskStatisticResponse> response = new ArrayList<>();

            // get all user
            if (req.getLsUser().isEmpty()) {
                RestTemplate restTemplate = new RestTemplate();
                String url = sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key) + "/chart-node/getTalentPool";
                Map<String, Object> paramsRestChartRequest = new HashMap<>();
                paramsRestChartRequest.put("chartId", chartId);
                paramsRestChartRequest.put("check", true);
                paramsRestChartRequest.put("sortType", "ASC");
                paramsRestChartRequest.put("page", 1);
                paramsRestChartRequest.put("limit", Integer.MAX_VALUE);

                HttpEntity<?> httpEntity = redirectApiUtils.RedirectFormAPIs(paramsRestChartRequest, credentialHelper.getJWTToken());
                ResponseEntity<Object> responseData = restTemplate.exchange(url, HttpMethod.POST, httpEntity, Object.class);
                HashMap<String, Object> body = (HashMap<String, Object>) responseData.getBody();
                LinkedHashMap data = (LinkedHashMap) body.get("data");
                List<LinkedHashMap> content = (List<LinkedHashMap>) data.get("content");
                Set<String> listUser = new HashSet<>();
                for (LinkedHashMap<String, String> d : content) {
                    listUser.add(d.get("email"));
                }
                req.getLsUser().addAll(listUser);
            }

            List<BpmTask> bpmTasks = bpmTaskRepository
                    .findAll(bpmTaskSpecification.countTaskCallCenter(req.getLsUser(), chartId));
            Map<String, List<BpmTask>> mapUserTask = bpmTasks.stream()
                    .collect(Collectors.groupingBy(BpmTask::getTaskAssignee));

            for (String user : req.getLsUser()) {
                List<BpmTask> tasks = mapUserTask.get(user) != null ? mapUserTask.get(user) : Collections.emptyList();
                CountTaskStatisticResponse res = countTask(tasks);
                res.setUser(user);

                response.add(res);
            }

            response.sort((o1, o2) -> o2.getOverdue().compareTo(o1.getOverdue()));
            // paging
            int totalItem = response.size();
            int totalPage = 0;
            if (totalItem > 0 && totalItem % req.getLimit() == 0) {
                totalPage = Math.toIntExact(totalItem / req.getLimit());
            } else if (totalItem > 0 && totalItem % req.getLimit() >= 1) {
                totalPage = Math.toIntExact((totalItem / req.getLimit()) + 1);
            }
            int fromIndex = (req.getPage() - 1) * req.getLimit();
            if (req.getLsUser() == null || totalItem <= fromIndex) {
                return PageDto.builder().content(Collections.emptyList())
                        .page(req.getPage())
                        .size(req.getLimit())
                        .totalElements(totalItem)
                        .number(req.getLimit())
                        .numberOfElements(req.getLimit())
                        .totalPages(totalPage)
                        .build();
            }

            List<CountTaskStatisticResponse> temp = response.subList(fromIndex,
                    Math.min(fromIndex + req.getLimit(), totalItem));

            return PageDto.builder().content(temp)
                    .page(req.getPage())
                    .size(req.getLimit())
                    .totalElements(totalItem)
                    .number(req.getLimit())
                    .numberOfElements(Math.toIntExact(totalItem))
                    .totalPages(totalPage)
                    .build();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public PageDto getByAssigneeAndStatus(TaskCallCenterRequest req, Long chartId) {
        try {
            List<String> account = new ArrayList<>();
            if (req.getUser() == null || req.getUser().isEmpty()) {
                RestTemplate restTemplate = new RestTemplate();
                String url = sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key) + "/chart-node/getAllChartNodeByManager?chartId={chartId}";
                HashMap<String, Object> param = new HashMap<>();
                param.put("chartId", chartId);
                HttpEntity<?> entity = redirectApiUtils.RedirectGetAPI(credentialHelper.getJWTToken());
                ResponseEntity<Object> response = restTemplate.exchange(url, HttpMethod.GET, entity, Object.class, param);
                HashMap<String, Object> body = (HashMap<String, Object>) response.getBody();
                List<HashMap<String, Object>> data = (List<HashMap<String, Object>>) body.get("data");
                for (HashMap<String, Object> d : data) {
                    account.add((String) d.get("email"));
                }
            } else {
                account.addAll(req.getUser());
            }
            List<TaskDetailCallCenter> response = new ArrayList<>();

            req.setLsPriority(req.getLsPriority().stream().map(String::toLowerCase).collect(Collectors.toList()));
            Sort sort = responseUtils.getSort(req.getSortBy(), req.getSortType());
            Page<Object[]> bpmTasks = bpmTaskRepository.findBpmTaskActiveCallCenter(chartId, req.getSearch(), account,
                    req.getLsStatus(), req.getLsPriority(), PageRequest.of(req.getPage() - 1, req.getLimit(), sort));
            for (Object[] o : bpmTasks) {
                TaskDetailCallCenter dto = new TaskDetailCallCenter();
                dto.setTaskId((String) o[0]);
                dto.setTaskName((String) o[1]);
                dto.setTaskAssignee((String) o[2]);
                dto.setTaskSla((Double) o[3]);

                LocalDateTime startTime = (LocalDateTime) o[4];
                LocalDateTime endTime = (LocalDateTime) o[5];

                Date currDate = new Date();
                Long timeRemain = Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()).getTime()
                        - currDate.getTime();

                dto.setTaskSlaRemain(timeRemain);
                dto.setStatus((Integer) o[6]);
                dto.setPriority((String) o[7]);
                dto.setTicketId((Long) o[8]);
                dto.setTicketName((String) o[9]);
                dto.setTicketProcDefId((String) o[10]);
                dto.setTaskDefKey((String) o[11]);

                response.add(dto);

            }

            return PageDto.builder().content(response)
                    .number(req.getPage())
                    .numberOfElements(bpmTasks.getNumberOfElements())
                    .page(req.getPage())
                    .size(bpmTasks.getSize())
                    .totalPages(bpmTasks.getTotalPages())
                    .totalElements(bpmTasks.getTotalElements())
                    .build();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public TaskSettingConfigDto getSettingConfig() {
        try {
            String email = credentialHelper.getJWTPayload().getEmail();
            TaskSettingConfig taskSettingConfig = taskSettingConfigRepository.findByEmail(email);
            if (taskSettingConfig == null) {
                taskSettingConfig = new TaskSettingConfig();
                taskSettingConfig.setEmail(email);
                taskSettingConfig.setExpiredTaskValueNormal(5);
                taskSettingConfig.setExpiredTaskValueWarning(6);
                taskSettingConfig.setExpiredTaskValueDanger(10);
                taskSettingConfig.setExpiredHourValueNormal(3);
                taskSettingConfig.setExpiredHourValueWarning(4);
                taskSettingConfig.setExpiredHourValueDanger(5);
            }
            return modelMapper.map(taskSettingConfig, TaskSettingConfigDto.class);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public Boolean saveSettingConfig(TaskSettingConfigDto dto) {
        try {
            TaskSettingConfig taskSettingConfig = taskSettingConfigRepository.findByEmail(credentialHelper.getJWTPayload().getEmail());
            if (taskSettingConfig == null) {
                log.info("taskSettingConfig{}", taskSettingConfig);
                taskSettingConfig = new TaskSettingConfig();
                taskSettingConfig.setEmail(credentialHelper.getJWTPayload().getEmail());
            }
            taskSettingConfig.setExpiredTaskValueNormal(dto.getExpiredTaskValueNormal());
            taskSettingConfig.setExpiredTaskValueWarning(dto.getExpiredTaskValueWarning());
            taskSettingConfig.setExpiredTaskValueDanger(dto.getExpiredTaskValueDanger());
            taskSettingConfig.setExpiredHourValueNormal(dto.getExpiredHourValueNormal());
            taskSettingConfig.setExpiredHourValueWarning(dto.getExpiredHourValueWarning());
            taskSettingConfig.setExpiredHourValueDanger(dto.getExpiredHourValueDanger());
            taskSettingConfigRepository.save(taskSettingConfig);
            log.info("save successfully{}", taskSettingConfig);
            return true;
        } catch (Exception e) {
            log.error("saveSettingConfig error:{}", e.getMessage());
            return false;
        }
    }

    public Integer editTask(String taskDefKey, String procInstId, CompleteTaskDto completeTaskDto) {
        try {
            List<BpmTask> currentTask = bpmTaskRepository.listTaskCompleted(taskDefKey, procInstId);
            bpmTaskRepository.deleteTaskByEdit(taskDefKey, procInstId);
            Map<String, Object> body = new HashMap<>();
            body.put("skipCustomListeners", true);
            body.put("skipIoMappings", true);
            List<Map<String, String>> listInstruction = new ArrayList<>();

            Map<String, String> ruObject = new HashMap<>();
            ruObject.put("type", "startBeforeActivity");
            ruObject.put("activityId", taskDefKey);
            listInstruction.add(ruObject);

            body.put("instructions", listInstruction);
            body.put("annotation", "Modified to resolve an error.");
            camundaEngineService.modifyProcessInstance(procInstId, body);

            List<Map<String, Object>> listData = camundaEngineService.getCurrentRuntimeTasks(procInstId);

            completeTaskDto.setWithVariablesInReturn(true);
            camundaEngineService.completeTask(listData.get(0).get("id").toString(), completeTaskDto);

            List<Map<String, Object>> listData2 = camundaEngineService.getCurrentRuntimeTasks(procInstId);

            BpmTask newTask = new BpmTask();
            newTask.setTaskStatus(currentTask.get(0).getTaskStatus());
            newTask.setTaskCreatedTime(currentTask.get(0).getTaskCreatedTime());
            newTask.setTaskName(currentTask.get(0).getTaskName());
            newTask.setTaskType(currentTask.get(0).getTaskType());
            newTask.setTaskProcDefId(currentTask.get(0).getTaskProcDefId());
            newTask.setTaskProcInstId(currentTask.get(0).getTaskProcInstId());
            newTask.setSlaResponse(currentTask.get(0).getSlaResponse());
            newTask.setSlaFinish(currentTask.get(0).getSlaFinish());
            newTask.setSlaResponseTime(currentTask.get(0).getSlaResponseTime());
            newTask.setSlaFinishTime(currentTask.get(0).getSlaFinishTime());
            newTask.setFinishDuration(Duration.between(currentTask.get(0).getTaskStartedTime(), LocalDateTime.now()).toMinutes());
            newTask.setResponseDuration(currentTask.get(0).getResponseDuration());
            newTask.setTaskPriority(currentTask.get(0).getTaskPriority());
            newTask.setTaskAssignee(currentTask.get(0).getTaskAssignee());
            newTask.setTaskCreatedUser(currentTask.get(0).getTaskCreatedUser());
            newTask.setTaskDefKey(currentTask.get(0).getTaskDefKey());
            newTask.setTaskStartedTime(currentTask.get(0).getTaskStartedTime());
            newTask.setTaskIsFirst(currentTask.get(0).isTaskIsFirst());
            newTask.setTaskFinishedTime(LocalDateTime.now());
            newTask.setTaskId(listData2.get(0).get("id").toString());
            BpmTask savedBpmTask = bpmTaskRepository.save(newTask);

            // (phucvm3) save bpm_task_user
            bpmTaskUserService.saveFromBpmTask(savedBpmTask);
            saveTaskVari(completeTaskDto.getVariables(), listData2.get(0).get("id").toString(), currentTask.get(0).getTaskProcInstId());

            return 1;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return 0;
        }
    }

    @Transactional(noRollbackFor = RuntimeException.class)
    public void updateSignZones(String procInstId, String account) {
        try {
            List<BpmTpSignZone> bpmTpSignZones = bpmTpSignZoneRepository.getAllSignZonesNotAssignEmail(procInstId);
            if (!ValidationUtils.isNullOrEmpty(bpmTpSignZones)) {
                List<BpmVariables> bpmVariables = bpmVariablesRepository.findBpmVariablesByProcInstId(procInstId);
                if (!ValidationUtils.isNullOrEmpty(bpmVariables)) {
                    List<BpmTpSignZone> updateSignZones = new ArrayList<>();
                    Map<String, BpmVariables> mapVariables = bpmVariables.stream().collect(Collectors.toMap(BpmVariables::getName, Function.identity()));

                    for (BpmTpSignZone zone : bpmTpSignZones) {
                        String varName = zone.getEmail() != null ? zone.getEmail().substring(1) : null;
                        if (varName != null) {
                            BpmVariables bpmVariable = mapVariables.get(varName);
                            if (bpmVariable != null) {
                                String email = !ValidationUtils.isNullOrEmpty(bpmVariable.getStringVal()) ? bpmVariable.getStringVal() : bpmVariable.getJsonVal();
                                email = common.isJson(email) ? common.jsonArrayToString(email) : email;

                                // assign email
                                zone.setEmail(email);

                                // get user info
                                UserInfoResponse userInfo = customerService.getUserInfo(email);
                                if (userInfo != null) {
                                    zone.setFirstName(userInfo.getFirstname());
                                    zone.setLastName(userInfo.getLastname());
                                    zone.setPosition(userInfo.getTitle());
                                    zone.setUpdatedUser(account);
                                    zone.setUpdatedDate(new Date());
                                }

                                updateSignZones.add(zone);
                            }
                        }
                    }

                    // update sign zones
                    if (!updateSignZones.isEmpty()) {
                        bpmTpSignZoneRepository.saveAll(updateSignZones);
                    }
                }
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    /**
     * Get all tasks has status is PROCESSING, ACTIVE
     *
     * <AUTHOR>
     */
    public List<BpmTask> getAllProcessingTasks(List<String> procInstIds) {
        return getAllTasksByStatus(procInstIds, TaskConstants.TabStatus.PROCESSING);
    }

    /**
     * Get all tasks by list of status
     *
     * <AUTHOR>
     */
    public List<BpmTask> getAllTasksByStatus(List<String> procInstIds, List<String> status) {
        return bpmTaskRepository.getAllTasksByStatus(procInstIds, status);
    }

    public Map<String, Object> completeTask(String taskId, String procInstId, CompleteTaskDto completeTaskDto, Boolean signComplete) throws Exception {
        Map<String, Object> resultData = new HashMap<>();
        String account = credentialHelper.getJWTPayload().getUsername();


        BpmTask bpmTask = getBpmTaskByTaskId(taskId);
        BpmProcInst bpmProcInst = bpmProcInstManager.findBpmProcInstByTicketProcInstId(procInstId);

        // check task allow process
        List<String> checkStatus = Arrays.asList(ProcInstConstants.Status.RECALLING.code, ProcInstConstants.Status.CANCEL.code);
        if (bpmTask.getTaskStatus().equalsIgnoreCase(TaskConstants.Status.DELETED_BY_RU.code)
                || bpmTask.getTaskStatus().equalsIgnoreCase(TaskConstants.Status.COMPLETED.code)
                || checkStatus.contains(bpmProcInst.getTicketStatus())) {
            throw new RuntimeException(common.getMessage("message.task.completed.not-allow"));
        }

        Map<String, Object> camundaTask = camundaEngineService.getTaskById(taskId);
        String processInstanceId = null;
        String procDefId = null;
        String taskDefKey = null;
        if (!ValidationUtils.isNullOrEmpty(camundaTask)) {
            processInstanceId = camundaTask.get("processInstanceId").toString();
            procDefId = camundaTask.get("processDefinitionId").toString();
            taskDefKey = camundaTask.get("taskDefinitionKey").toString();
        }

        /* Save ticketAssign (Trạng thái xác nhận ủy quyền tờ trình)*/
        saveIsAssignProcinst(completeTaskDto.getVariables(), bpmProcInst);
        Map<String, VariableValueDto> ticketVariables = actionApiService.getTicketVariables(procInstId);

        /* BEGIN handle call action api on beginning */
        ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(
                TaskActionConstants.Action.DO_TASK.code,
                bpmProcInst.getTicketProcDefId(),
                taskDefKey,
                procInstId,
                account,
                actionApiService.createVariablesMap(
                        completeTaskDto.getVariables(),
                        bpmProcInst,
                        camundaTask,
                        ticketVariables
                )
        );

        // check response of api call BEFORE
        actionApiService.setActionApiResponse(actionApiContext, resultData);
        Map<String, Object> optVariable = new HashMap<>();
        optVariable.put("accountLogin", account);

        // check allow post process of api call BEFORE
        if (!actionApiContext.isAllowPostProcess()) {
            // reset sign zone
            bpmRuManager.resetSignZoneByTaskDefKeyAndUser(procInstId, taskDefKey, account);
            finishCompleteTicket(actionApiContext, completeTaskDto, procInstId, camundaTask, optVariable);
            resultData.put("isComplete", false);
            return resultData;
        }
        /* END handle call action api on beginning */

        // lấy taskDefKey các task tiếp theo của task hoàn thành bằng workFlow
        WorkFlowRequest workFlowRequest = new WorkFlowRequest();
        workFlowRequest.setProcDefId(bpmProcInst.getTicketProcDefId());
        workFlowRequest.setProcInstId(procInstId);
        List<SproFlow> workFlow = sproService.getWorkFlow(workFlowRequest);
        List<String> listTaskDefKey = new ArrayList<>();
        try {
            listTaskDefKey = workFlow.stream()
                    .flatMap(group -> group.getNodes().stream())
                    .filter(n -> n.getId().equals(bpmTask.getTaskDefKey()))
                    .flatMap(node -> node.getNextNodes().stream())
                    .map(NodeInfo::getId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        boolean isComplete = false;
        // Check task runtime là task cuối cùng trong flow -> call api BEFORE COMPLETE TICKET trước khi gọi completeTask bên camunda
        boolean isFinalTask = isFinalTaskInFlow(bpmTask, workFlow);

        if (isFinalTask) {
            isComplete = bpmProcInstManager.completeTicket(procInstId, account, bpmProcInst, taskDefKey, taskId, camundaTask, resultData, completeTaskDto.getVariables());
            if (!isComplete) {
                bpmRuManager.resetSignZoneByTaskDefKeyAndUser(procInstId, taskDefKey, account);
                resultData.put("isComplete", false);
                return resultData;
            }
        }

        if (signComplete) {
            updateVariStatus(procInstId, taskId);
        } else {
            // Xóa các biến đã lưu nháp
            List<BpmVariables> lstDraftVari = bpmDraftVariablesRepo.getAllByTaskIdAndIsDraft(taskId, 1);
            if (!ValidationUtils.isNullOrEmpty(lstDraftVari)) {
                bpmVariablesRepository.deleteAll(lstDraftVari);
            }
            saveTaskVari(completeTaskDto.getVariables(), taskId, procInstId);
        }

        completeTaskDto.setWithVariablesInReturn(true);

        camundaEngineService.completeTask(taskId, completeTaskDto);

        updateTaskBpm(bpmTask, account, bpmProcInst, listTaskDefKey, completeTaskDto.getVariables(), false);

        List<Map<String, Object>> listData = camundaEngineService.getCurrentRuntimeTasks(procInstId);
        List<BpmTask> listSaveTask = new ArrayList<>();
        if (listData.isEmpty()) {
            // lưu lịch sử phiếu
            bpmProcInstManager.saveCompleteTicketHistory(procInstId, account, bpmProcInst, taskDefKey, taskId);
//            isComplete = bpmProcInstManager.completeTicket(procInstId, account, bpmProcInst, taskDefKey, taskId, camundaTask, resultData);
        } else {
            List<String> listExistId = new ArrayList<>();
            List<BpmTask> listTask = getByTicketIdAndStatus(processInstanceId, TaskConstants.TabStatus.PROCESSING);

            for (BpmTask task : listTask) {
                listExistId.add(task.getTaskId());
            }
            // (phucvm3) remove exists task
            listData.removeIf(object -> listExistId.contains(object.get("id").toString()));
            listSaveTask = saveTaskFromCamunda(listData, procDefId, account, taskId, procInstId, bpmProcInst, taskDefKey, bpmTask.getTaskType());

            // change ticket status -> PROCESSING
            if (bpmProcInst.getTicketStatus().equalsIgnoreCase(ProcInstConstants.Status.ADDITIONAL_REQUEST.code)) {
                // lấy các request cần bổ sung nếu có -> set status_request = 2
                List<BpmDiscussion> lstRemain = bpmDiscussionRepository.getAdditionalRequestRemainByUsername(account, bpmProcInst.getTicketId());
                if (!ValidationUtils.isNullOrEmpty(lstRemain)) {
                    for (BpmDiscussion discussion : lstRemain) {
                        discussion.setStatusRequest(BusinessEnum.AdditionStatusRequest.COMPLETED_NO_UPDATE.code);
                    }
                    bpmDiscussionRepository.saveAll(lstRemain);
                }
                // không còn ycbs hoặc task song song/ tuần tự đã duyệt hết
                Long countAdditionalRemain = bpmDiscussionRepository.countAdditionalRequestRemain(bpmProcInst.getTicketId());
                if (countAdditionalRemain == 0 || !ValidationUtils.isNullOrEmpty(listData)) {
                    bpmProcInstRepository.ongoingTicket(procInstId);
                }
            } else {
                bpmProcInstRepository.ongoingTicket(procInstId);
            }

            // check ko có task mới được tạo -> case song song nhiều task
            if (ValidationUtils.isNullOrEmpty(listSaveTask)) {
                listSaveTask.addAll(listTask);
            } else {
                // legislative: đang xử lý trong case task đơn -- song song sẽ phải check lại logic (BA)
                optVariable.put("nextTaskDefKey", listSaveTask.get(0).getTaskDefKey());
            }
            optVariable.put("actionCode", TaskActionConstants.Action.DO_TASK.code);
            // process sign zone
            updateSignZones(procInstId, account);
            isComplete = true;
        }

        /* BEGIN handle call action api at the end */
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // Chỉ chạy sau khi transaction commit
                finishCompleteTicket(actionApiContext, completeTaskDto, procInstId, camundaTask, optVariable);
            }
        });
        /* END handle call action api at the end */

        //TODO: Auto complete task
        /*
         * -> check procDef config
         * -> check completed task assignee = listData assignee (include current task) + assignType != 1
         * -> variable: check case zoom - normal task -> replace taskDefKey
         * -> sign -> copy from completed task -> combine signed file
         * -> recursive
         * */

        BpmProcdef bpmProcdef = bpmProcdefRepository.findByServiceId(bpmProcInst.getServiceId());
        // check cấu hình quy trình + bỏ qua khi ko sinh task mới bên camunda
        if (bpmProcdef.getAutoCompleteTask() != null && bpmProcdef.getAutoCompleteTask() && !ValidationUtils.isNullOrEmpty(listSaveTask)) {

            // lấy list task completed + ko có ủy quyền
            List<BpmTask> listTaskCompleted = bpmTaskRepository.getBpmTaskByTaskProcInstIdAndTaskStatusInAndAssignTypeFalse(processInstanceId, Collections.singletonList(TaskConstants.Status.COMPLETED.code));

            // filter task ko có assignee + ko có template version + có ủy quyền
            List<BpmTask> listSaveTaskFilter = listSaveTask.stream()
                    .filter(task -> task.getTaskAssignee() != null && task.getTemplateVersionId() != null && (task.getAssignType() == null || !task.getAssignType()))
                    .toList();

            if (!ValidationUtils.isNullOrEmpty(listTaskCompleted) && !ValidationUtils.isNullOrEmpty(listSaveTaskFilter)) {
                for (BpmTask saveTask : listSaveTaskFilter) {
                    BpmTask taskCompleted = listTaskCompleted.stream()
                            .filter(task -> task.getTaskAssignee().equalsIgnoreCase(saveTask.getTaskAssignee())
                                    && task.getTemplateVersionId().equals(saveTask.getTemplateVersionId()))
                            .findFirst().orElse(null);

                    // check assignee status
                    List<String> lstUsernameActive = customerService.getListUsernameActive(Collections.singletonList(saveTask.getTaskAssignee()));
                    if (taskCompleted != null && !ValidationUtils.isNullOrEmpty(lstUsernameActive)) {
                        // change ticket status - kafka
//                        bpmProcInst.setTicketStatus(ProcInstConstants.Status.WAITING_FOR_APPROVAL.code);
                        // auto complete
                        ProcessAutoCompleteTask autoRequest = new ProcessAutoCompleteTask();
                        autoRequest.setCompletedTaskId(taskCompleted.getTaskId());
                        autoRequest.setProcInstId(procInstId);
                        autoRequest.setOldTaskDefKey(taskCompleted.getTaskDefKey());
                        autoRequest.setBpmTask(saveTask);
//                        kafkaTemplate.send(TopicConstants.TOPIC_AUTO_COMPLETE_TASK, autoRequest);
                        resultData = autoCompleteTask(taskCompleted.getTaskId(), procInstId, taskCompleted.getTaskDefKey(), saveTask, workFlow);
                        isComplete = (boolean) resultData.get("isComplete");
                    }
                }
            }
        }

        resultData.put("isComplete", isComplete);

        return resultData;
    }

    private Boolean isFinalTaskInFlow(BpmTask bpmTask, List<SproFlow> workFlow) {

        // check case song song nhiều task
        List<BpmTask> listTask = getByTicketIdAndStatus(bpmTask.getTaskProcInstId(), TaskConstants.TabStatus.PROCESSING);
        if (!ValidationUtils.isNullOrEmpty(listTask) && listTask.size() > 1) {
            return false;
        }

        List<SproFlowNode> nodes = workFlow.stream()
                .flatMap(e -> e.getNodes().stream())
                .toList();

        SproFlowNode node = nodes.stream()
                .filter(e -> e.getId().equalsIgnoreCase(bpmTask.getTaskDefKey()))
                .filter(e -> {
                    String nextNodeId = e.getNextNodes().stream().map(NodeInfo::getId).findFirst().orElse(null);
                    if (nextNodeId != null) {
                        SproFlowNode nextNode = nodes.stream().filter(fn -> fn.getId().equalsIgnoreCase(nextNodeId) && fn.getType().equalsIgnoreCase("endEvent")).findFirst().orElse(null);
                        return nextNode != null;
                    }
                    return false;
                })
                .findFirst().orElse(null);

        if (!ValidationUtils.isNullOrEmpty(node)) {
            // trường hợp task multi phải check thêm các task cùng taskDefKey đã hoàn thành chưa
            if (node.isMultiInstance()) {
                // số task = số assignee được gán bên camunda
                long countAssignee = Arrays.stream(node.getAssignee().replaceAll("\\[\"|\"\\]|\\\\|\"", "").split(",")).count();
                List<BpmTask> bpmTasks = bpmTaskRepository.getBpmTaskInfoByTaskDefKeyAndProcInstId(bpmTask.getTaskDefKey(), bpmTask.getTaskProcInstId());
                long countTaskCompleted = bpmTasks.stream().filter(t -> t.getTaskStatus().equalsIgnoreCase(TaskConstants.Status.COMPLETED.code)).count();

                // số task được tạo = assignee && số task hoàn thành = assignee - 1 => final task
                return countAssignee == bpmTasks.size() && countAssignee - 1 == countTaskCompleted;
            }
            return true;
        }

        return false;
    }

    private void saveIsAssignProcinst(Map<String, VariableValueDto> variableIns, BpmProcInst bpmProcInst) {
        try {
            log.info("saveIsAssignProcinst() :");
            for (var entry : variableIns.entrySet()) {
                if (entry.getKey().contains("assign_xacNhanUyQuyen")) {
                    boolean isAssign = entry.getValue().getValue().toString().equalsIgnoreCase("yes");
                    //Ưu tiên false nếu trong khi duyệt có 1 người không đồng ý Ủy quyền
                    bpmProcInst.setTicketAssign((bpmProcInst.getTicketAssign() == null
                            || bpmProcInst.getTicketAssign()) && isAssign);
                }
            }
        } catch (Exception e) {
            log.error("saveIsAssignProcinst err : {}", e.getMessage());
        }
    }

    public Boolean draftTask(String taskId, String procInstId, CompleteTaskDto completeTaskDto) {
        try {
            boolean isCancelDraft = false;
            if (!ValidationUtils.isNullOrEmpty(completeTaskDto.getIsCancelDraft())) {
                isCancelDraft = completeTaskDto.getIsCancelDraft();
            }
            // Xóa bản ghi nháp cũ nếu có
            Map<String, VariableValueDto> variables = completeTaskDto.getVariables();
            if (!ValidationUtils.isNullOrEmpty(variables)) {
                Set<String> variableNameSet = variables.keySet();
                // delete old draft variable
                if (isCancelDraft) {
                    bpmVariablesService.deleteVariables(procInstId, null, variableNameSet);
                } else {
                    bpmVariablesService.deleteVariables(procInstId, taskId, variableNameSet);
                }
            }
            if (isCancelDraft) {
                saveTaskVari(completeTaskDto.getVariables(), null, procInstId, true);
            } else {
                saveTaskVari(completeTaskDto.getVariables(), taskId, procInstId, true);
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    private void finishCompleteTicket(ActionApiContext actionApiContext, CompleteTaskDto completeTaskDto, String procInstId, Map<String, Object> mapObjectTask, Map<String, Object> optVariable) {
        actionApiService.endHandleActionApi(
                actionApiContext,
                actionApiService.createVariablesMap(
                        completeTaskDto.getVariables(),
                        bpmProcInstManager.findBpmProcInstByTicketProcInstId(procInstId),
                        mapObjectTask,
                        actionApiService.getTicketVariables(procInstId),
                        optVariable
                )
        );
    }

    private void finishCompleteTicketWithActionUser(ActionApiContext actionApiContext, CompleteTaskDto completeTaskDto, String procInstId, Map<String, Object> mapObjectTask, String actionUser) {
        actionApiService.endHandleActionApiWithActionUser(
                actionApiContext,
                null,
                null,
                actionUser,
                actionApiService.createVariablesMap(
                        completeTaskDto.getVariables(),
                        bpmProcInstManager.findBpmProcInstByTicketProcInstId(procInstId),
                        mapObjectTask,
                        actionApiService.getTicketVariables(procInstId)
                )
        );
    }

    public void expired() {
        try {
            Pageable pageable = PageRequest.of(0, 100);
//            String[] statusFinish = new String[]{TaskConstants.Status.ACTIVE.code};
            List<ExpireDto> bpmTasksFinish = bpmTaskRepository.findBpmTaskByTaskStatus(TaskConstants.Status.ACTIVE.code, pageable);
            NotificationTemplate notificationTemplate = notificationTemplateRepository.findByTypeNoti("EXPIRE_TICKET");
            for (ExpireDto bpmTask : bpmTasksFinish) {
                if (LocalDateTime.now().isAfter(bpmTask.getSlaResponseTime().minusHours(1)) && LocalDateTime.now().isBefore(bpmTask.getSlaResponseTime())) {
                    BpmNotifyUser bpmNotifyUser = new BpmNotifyUser();
                    bpmNotifyUser.setCreatedTime(LocalDateTime.now());
                    bpmNotifyUser.setCreatedUser(credentialHelper.getJWTPayload().getEmail());
                    bpmNotifyUser.setType("MAIL");
                    bpmNotifyUser.setStatus("1");
                    bpmNotifyUser.setRecipient(customerService.getEmailByUser(bpmTask.getTaskAssignee()).getEmail());
                    bpmNotifyUser.setTicketId(bpmTask.getId());
                    bpmNotifyUser.setMessage(notificationTemplate.getContent());
                    bpmNotifyUser.setTitle(notificationTemplate.getTitle());
                    bpmNotifyUserRepository.save(bpmNotifyUser);
                }
            }
//            String[] statusResponse = new String[]{TaskConstants.Status.PROCESSING.code};
            List<ExpireDto> bpmTasksResponse = bpmTaskRepository.findBpmTaskByTaskStatus(TaskConstants.Status.PROCESSING.code, pageable);
            for (ExpireDto bpmTask : bpmTasksResponse) {
                if (LocalDateTime.now().isAfter(bpmTask.getSlaFinishTime().minusHours(1)) && LocalDateTime.now().isBefore(bpmTask.getSlaFinishTime())) {
                    BpmNotifyUser bpmNotifyUser = new BpmNotifyUser();
                    bpmNotifyUser.setCreatedTime(LocalDateTime.now());
                    bpmNotifyUser.setCreatedUser(credentialHelper.getJWTPayload().getEmail());
                    bpmNotifyUser.setType("MAIL");
                    bpmNotifyUser.setStatus("1");
                    bpmNotifyUser.setRecipient(customerService.getEmailByUser(bpmTask.getTaskAssignee()).getEmail());
                    bpmNotifyUser.setTicketId(bpmTask.getId());
                    bpmNotifyUser.setMessage(notificationTemplate.getContent());
                    bpmNotifyUser.setTitle(notificationTemplate.getTitle());
                    bpmNotifyUserRepository.save(bpmNotifyUser);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public BpmTask findById(Long id) {
        return bpmTaskRepository.findById(id).orElse(null);
    }

    /**
     * Update task recall status
     *
     * @param id Equivalent bpm_task.id
     */
    public Map<String, Object> recall(Long id, String status, TaskRecallRequest request) {
        // find task by bpm_task_id
        BpmTask bpmTask = findById(id);
        if (bpmTask == null) {
            throw new RuntimeException(common.getMessage("message.task.not-exists"));
        }

        // find bpm_procinst
        BpmProcInst bpmProcInst = bpmProcInstManager.findBpmProcInstByTicketProcInstId(bpmTask.getTaskProcInstId());
        if (bpmProcInst == null) {
            throw new RuntimeException(common.getMessage("message.ticket.not-exists"));
        }

        String currentStatus = bpmTask.getTaskStatus();
        if (!TaskConstants.TabStatus.PROCESSING.contains(currentStatus.toUpperCase())) {
            throw new RuntimeException(common.getMessage("message.task.recall.not-allow", new Object[]{bpmTask.getTaskName()}));
        }

        String username = credentialHelper.getJWTPayload().getUsername();

        /* BEGIN handle call action api on beginning */
        // Optional variable
        Map<String, Object> optVariable = new HashMap<>();
        optVariable.put("actionCode", TaskActionConstants.Action.RECALLED.code);
        optVariable.put("accountLogin", username);

        ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(
                TaskActionConstants.Action.RECALLED.code,
                bpmProcInst.getTicketProcDefId(),
                bpmTask.getTaskDefKey(),
                bpmProcInst.getTicketProcInstId(),
                actionApiService.createVariablesMap(bpmProcInst, actionApiService.getTicketVariables(bpmProcInst.getTicketProcInstId()), optVariable));

        // check response of api call BEFORE
        Map<String, Object> resultData = new HashMap<>();
        actionApiService.setActionApiResponse(actionApiContext, resultData);

        // check allow post process of api call BEFORE
        if (!actionApiContext.isAllowPostProcess()) {
            resultData.put("isSuccess", false);
            return resultData;
        }

        boolean isAgree = false;

        //--- Begin: Update status ---//
        String taskStatus = null;
        if (status != null) {
            if (status.equalsIgnoreCase(AppConstants.RecallStatus.AGREE)) {
                taskStatus = TaskConstants.Status.AGREE_TO_RECALL.code;
                isAgree = true;
            } else if (status.equalsIgnoreCase(AppConstants.RecallStatus.DISAGREE)) {
                taskStatus = TaskConstants.Status.DISAGREE_TO_RECALL.code;
            }

            bpmTask.setTaskStatus(taskStatus);
            save(bpmTask);
        }
        //call report by chart node
//        reportByChartNodeService.createReportByChartNode(bpmTask.getTaskId());
        //--- End: Update status ---//

        // determine change ticket status to RECALLED
        if (isAgree && !isExistsTaskNotAgreeToRecall(bpmProcInst.getTicketProcInstId(), id)) {
            updateTicketToRecalled(bpmProcInst, bpmTask, request.getOpinion());
            // call report
//            reportByGroupService.createReportByGroup(bpmProcInst.getTicketId());
            //Thông báo cho user liên quan
            NotificationUser payload = new NotificationUser();
            payload.setBpmProcdefId(null);
            payload.setNextTaskDefKey(bpmProcInst.getTicketStartActId());
            payload.setVariables(new HashMap<>());
            payload.setTicketId(bpmProcInst.getTicketId());
            payload.setIsGetOldVariable(true);
            payload.setLstCustomerEmails(Collections.singletonList(bpmProcInst.getCreatedUser()));
            payload.setActionCode(ProcInstConstants.Notifications.RECALLED.code);
            payload.setEmailExe(username);
            kafkaTemplate.send(notificationUser, payload);
//            bpmProcdefNotificationService.addNotificationsByConfig(null, bpmProcInst.getTicketStartActId(), new HashMap<>(),
//                    bpmProcInst.getTicketId(), true, Arrays.asList(bpmProcInst.getCreatedUser()), ProcInstConstants.Notifications.RECALLED.code);
        }


        /* BEGIN handle call action api at the end */
        actionApiService.endHandleActionApi(
                actionApiContext,
                actionApiService.createVariablesMap(
                        bpmProcInst,
                        actionApiService.getTicketVariables(bpmProcInst.getTicketProcInstId())
                ));

        resultData.put("isSuccess", true);
        return resultData;
    }

    private void updateTicketToRecalled(BpmProcInst bpmProcInst, BpmTask bpmTask, String opinion) {
        bpmProcInst.setTicketStatus(ProcInstConstants.Status.RECALLED.code);
        bpmProcInst.setTicketEditTime(LocalDateTime.now());
        bpmProcInstManager.save(bpmProcInst);

        // cancel ticket from camunda
        camundaEngineService.deleteProcessInstance(bpmProcInst.getTicketProcInstId());

        // save history
        // Lấy receivedTime từ action RECALLING
        BpmHistory recallingHis = bpmHistoryManager.getHistoryByActionAndProcInstId(TaskConstants.HistoryAction.RECALLING.code, bpmProcInst.getTicketProcInstId());

        BpmHistory history = new BpmHistory();
        history.setTicketId(bpmProcInst.getTicketId());
        history.setProcInstId(bpmProcInst.getTicketProcInstId());
        history.setFromTaskKey(bpmTask.getTaskDefKey());
        history.setTaskDefKey(bpmTask.getTaskDefKey());
        history.setFromTask(bpmTask.getTaskId());
        history.setAction(TaskConstants.HistoryAction.RECALLED.code);
        history.setActionUser(credentialHelper.getJWTPayload().getUsername());
        history.setCreatedTime(new Date());
        history.setNote(opinion);

        // get action_user_info
        List<UserTitleResponse> lstUserTitle = customerService.getUserTitleByUserName(credentialHelper.getJWTPayload().getUsername());
        if (!ValidationUtils.isNullOrEmpty(lstUserTitle)) {
            Map<String, Object> actionUserInfo = new HashMap<>();
            String userTitle = lstUserTitle.stream()
                    .filter(title -> title.getChartType().equalsIgnoreCase("import") && title.getConcurrently() != null)
                    .map(title -> {
                        String strTitle = StringUtil.nvl(title.getTitle(), "");
                        int concurrently = title.getConcurrently();
                        return concurrently == 0 ? strTitle : "kiêm " + strTitle;
                    })
                    .collect(Collectors.joining(" "));
            actionUserInfo.put("fullName", lstUserTitle.get(0).getFullName());
            actionUserInfo.put("userTitle", StringUtil.nvl(userTitle, ""));
            history.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
        }

        bpmHistoryManager.save(history);
    }

    public boolean isExistsTaskNotAgreeToRecall(String procInstId, Long bpmTaskId) {
        try {
            List<String> status = new ArrayList<>(TaskConstants.TabStatus.PROCESSING);
            status.add(TaskConstants.Status.DISAGREE_TO_RECALL.code);
            status.remove(TaskConstants.Status.AGREE_TO_RECALL.code);

            Long countRemain = bpmTaskRepository.countRemainTask(procInstId, bpmTaskId, status);
            return countRemain > 0;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return false;
    }

    public Map<String, Object> reminderTaskProcessing(long delayTime) {
        try {
            Map<String, Object> respone = new HashMap<>();
            long reviceTime = System.currentTimeMillis();
            // Lấy thời gian hiện tại
            LocalDateTime currentTime = LocalDateTime.now();
            LocalDateTime oneWeekAgo = currentTime.minusWeeks(1);

            List<ReminderTaskProcessingResponse> listReminder = bpmTaskRepository.getListReminderTaskProcessing(oneWeekAgo);
            respone.put("reminderList", listReminder.stream().map(ReminderTaskProcessingResponse::getId).collect(Collectors.toList()));

            List<PriorityManagement> priorities = priorityManagementRepository.findAll();
            int count = 0;
            for (ReminderTaskProcessingResponse i : listReminder) {
                long startTime = i.getCreatedTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                long slaFinish = i.getSlaFinishTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                PriorityManagement priority = priorities.stream().filter(_i -> _i.getId().equals(i.getPriorityId())).findFirst().orElse(null);

                if (priority != null && priority.getConfigReminder().equals(Boolean.TRUE)) {
                    if (System.currentTimeMillis() > slaFinish) {
                        //Cảnh báo quá hạn
                        count += (addNotiReminderTask(i.getId(), priority.getReminderTime(), priority.getReminderType(), priority.getReminderValue()
                                , i, startTime, slaFinish, delayTime, reviceTime, ProcInstConstants.Notifications.REMINDER_LATE.code));
                    } else {
                        //Cảnh báo sắp đến hạn
//                        count.getAndAdd(addNotiReminderTask(i.getId(), priority.getReminderBeingTime(), priority.getReminderBeingType(), priority.getReminderBeingValue()
//                                , i, startTime, slaFinish, delayTime, reviceTime,slaValue, ProcInstConstants.Notifications.REMINDER_BEING_LATE.code));
                        count += (addNotiReminderTask(i.getId(), priority.getReminderBeingTime(), priority.getReminderBeingType(), priority.getReminderBeingValue()
                                , i, startTime, slaFinish, delayTime, reviceTime, ProcInstConstants.Notifications.REMINDER_BEING_LATE.code));
                    }
                }
            }
            respone.put("sendMailSuccess", count);
            return respone;
        } catch (Exception ex) {
            return null;
        }
    }


    public int addNotiReminderTask(Long taskId,
                                   Integer reminderTime, String reminderType, Integer reminderValue,
                                   ReminderTaskProcessingResponse reminder,
                                   Long startTime,
                                   Long slaFinish,
                                   Long delayTime,
                                   Long reviceTime,
                                   String actionCode) {
        long stepTime; //Bước nhảy thời gian hợp lệ
        List<BpmNotifyUser> listRs = null;
        if (reminderType.equals("day")) {
            stepTime = (long) (86400000L * reminderValue);
        } else {
            stepTime = (long) (3600000L * reminderValue);
        }


//        log.info("Process reminder task => {} - {} -> sendType:{} * {}", taskId, actionCode, reminderType, reminderValue);
//        log.info("Process reminder task => Start time : {}", convertTime(startTime));
        //Kiểm tra giá trị SLA đã thỏa mãn
        double validTime = (System.currentTimeMillis() - startTime) - ((double) ((slaFinish - startTime) * reminderTime) / 100);

        if (validTime > 0) {
            //Thời gian bắt đầu tính gửi mail theo cấu hình
            long slaValidTime = startTime + ((slaFinish - startTime) * reminderTime) / 100;
//            log.info("Process reminder task => Sla start : {}", convertTime(slaValidTime));

            //Trường hợp thời gian hiện tại = thời gian SLA cảnh báo
            boolean isSlaIsNow = (System.currentTimeMillis() > slaValidTime) && (
                    System.currentTimeMillis() - slaValidTime < (delayTime + System.currentTimeMillis() - reviceTime));


            //Tìm thời gian gần nhất với thời gian hiện tại để so sánh xem có hợp lệ không
            long timeStep = slaValidTime + stepTime * ((System.currentTimeMillis() - slaValidTime) / stepTime - 1);
//            log.info("Process reminder task => timeStep first: {}", convertTime(timeStep));

            switch (actionCode) {
                case "REMINDER_LATE": {
                    do {
                        //Thời gian hiện tại hợp lệ gửi mail trong khoảng thời gian quét tiến trình + thời gian thực thi task hiện tai (Tiến trình 1p   1 lần)
                        if (isSlaIsNow || (System.currentTimeMillis() > timeStep &&
                                (System.currentTimeMillis() - timeStep < (delayTime + System.currentTimeMillis() - reviceTime)))) {
                            listRs = bpmProcdefNotificationService.addNotificationsByConfig(null, reminder.getTaskDefKey(), new HashMap<>(),
                                    reminder.getTicketId(), true,
                                    Collections.singletonList(reminder.getAssignee()),
                                    actionCode, appSuperAdminAccount);
                            break;
                        }
                        timeStep += stepTime;
//                        log.info("Process reminder task => timeStep check: {}", convertTime(timeStep));
                    }


                    while (timeStep - System.currentTimeMillis() < 2 * stepTime);
                    break;
                }
                case "REMINDER_BEING_LATE": {
                    do {
                        //Thời gian hiện tại hợp lệ gửi mail trong khoảng thời gian quét tiến trình + thời gian thực thi task hiện tai (Tiến trình 1p   1 lần)
                        if (isSlaIsNow || ((System.currentTimeMillis() > timeStep) && ((System.currentTimeMillis() - timeStep)
                                < (delayTime + System.currentTimeMillis() - reviceTime)))) {
                            listRs = bpmProcdefNotificationService.addNotificationsByConfig(null, reminder.getTaskDefKey(), new HashMap<>(),
                                    reminder.getTicketId(), true,
                                    Collections.singletonList(reminder.getAssignee()),
                                    actionCode, appSuperAdminAccount);
                            break;
                        }
                        timeStep += stepTime;
//                        log.info("Process reminder task => timeStep check: {}", convertTime(timeStep));

                    } while (timeStep < System.currentTimeMillis() + stepTime);
                    break;

                }
                default:
                    break;
            }
        }
//        else
//            log.info("Process reminder task SLA start reminder :{}", convertTime((startTime + (slaFinish - startTime) * reminderTime / 100)));

        return listRs != null ? listRs.size() : 0;
    }

    public boolean checkAuthorityCondition(List<BpmVariables> variables, String dataConfig) throws JsonProcessingException {
        final String F_OPERATOR_KEY = "slt_dieuKienSoSanh";
        final String F_VALUE_KEY = "txt_giaTriUyQuyen";
        final String F_NAME_KEY = "slt_dieuKienUyQuyen";
        final String COMPARISON_PREFIX = "\\,";

        Map<String, List<Map<String, Object>>> result = new ObjectMapper().readValue(dataConfig, HashMap.class);
        List<Map<String, Object>> dataAuthorityConfig = result.get("data");

        boolean isValid = dataAuthorityConfig.isEmpty();
        for (Map<String, Object> dataCnf : dataAuthorityConfig) {
            boolean isMatched = false;

            //Kiểm tra từng thằng lưu xuống
            for (BpmVariables variable : variables) {
                if (("start_" + dataCnf.get(F_NAME_KEY)).equals(variable.getName())) {
                    if (!dataCnf.get(F_OPERATOR_KEY).toString().equals("=") && (!ValidationUtils.isNumeric(variable.getStringVal()) || !ValidationUtils.isNumeric(dataCnf.get(F_VALUE_KEY).toString())))
                        break;
                    double valueInput = ValidationUtils.isNumeric(variable.getStringVal()) ? Double.parseDouble(variable.getStringVal()) : 0;
                    double valueConfig = ValidationUtils.isNumeric(dataCnf.get(F_VALUE_KEY).toString()) ? Double.parseDouble(dataCnf.get(F_VALUE_KEY).toString()) : 0;
                    switch (dataCnf.get(F_OPERATOR_KEY).toString()) {
                        case ">":
                            isMatched = valueInput > valueConfig;
                            break;
                        case ">=":
                            isMatched = valueInput >= valueConfig;
                            break;
                        case "<":
                            isMatched = valueInput < valueConfig;
                            break;
                        case "<=":
                            isMatched = valueInput <= valueConfig;
                            break;
                        case "=":
                            String key = dataCnf.get(F_VALUE_KEY).toString().trim();
                            String[] items = {key};
                            if (key.startsWith("[") && key.endsWith("]")) {
                                items = key.substring(1, key.length() - 1).split(COMPARISON_PREFIX);
                            }

                            String compareData = variable.getStringVal();
                            List<String> compareArray = null;
                            boolean isArrayCompare = false;
                            if (compareData.startsWith("[\"") && compareData.endsWith("\"]")) { //Checkbox,select multi
                                compareData = compareData.replaceAll("\"", "");
                                compareData = compareData.substring(1, compareData.length() - 1);
                                compareArray = List.of(compareData.split(","));
                                isArrayCompare = !compareArray.isEmpty();
                            }
                            if (isArrayCompare && !Arrays.asList(items).isEmpty()) {
                                List<String> itemsCompare = Arrays.stream(items).map(String::trim).toList();
                                if (compareArray.size() >= itemsCompare.size()) {
                                    compareArray = compareArray.stream().filter(i -> !itemsCompare.contains(i.trim())).collect(Collectors.toList());
                                    if (compareArray.isEmpty()) {
                                        isMatched = true;
                                        break;
                                    }
                                }
                            } else {
                                for (String item : items) {
                                    if (item.trim().equals(variable.getStringVal())) {
                                        isMatched = true;
                                        break;
                                    }
                                }
                            }
                            break;
                        default:
                            break;
                    }

                    if (isMatched) {
                        break;
                    }
                }
            }

            //1 Trường hợp false return luôn
            if (!isMatched) {
                return isMatched;
            }

            isValid = true;
        }

        return isValid;
    }

    public List<BpmTask> getTaskByProcInstId(String procInstId) {
        return bpmTaskRepository.getBpmTaskByTaskProcInstId(procInstId);
    }

    public void changeUserHandover(Map<String, Object> body, String account, Integer assignType, String handoverUser) {
        /* AssignType
         * 3: Bàn giao công việc
         */

        String ticketId = body.get("ticketId") != null ? body.get("ticketId").toString() : null;
        String taskId = body.get("taskId") != null ? body.get("taskId").toString() : null;
        String taskDefKey = body.get("taskDefKey") != null ? body.get("taskDefKey").toString() : null;
        String newUser = body.get("email") != null ? body.get("email").toString() : null;

        BpmTask task = bpmTaskRepository.getBpmTaskByTaskId(taskId);
        if (task == null) {
            return;
        }
        BpmProcInst bpmProcInst = bpmProcInstManager.findBpmProcInstByTicketProcInstId(task.getTaskProcInstId());

        List<String> checkStatus = Arrays.asList(ProcInstConstants.Status.CANCEL.code, ProcInstConstants.Status.COMPLETED.code, ProcInstConstants.Status.DELETED_BY_RU.code);
        if (checkStatus.contains(task.getTaskStatus()) || checkStatus.contains(bpmProcInst.getTicketStatus())) {
            throw new RuntimeException(common.getMessage("message.task.change-assignee.not-allow"));
        }

        String oldUser = task.getTaskAssignee();

        // update task assignee to new username
        camundaEngineService.setTaskAssignee(taskId, newUser);

        // update assignee task
        bpmTaskRepository.updateOldTaskToNewAssignee(ticketId, taskDefKey, oldUser, newUser, false);

        // update sign zone
        BpmTpSignZone signZone = bpmTpSignZoneRepository.findByProcInstIdAndTaskDefKeyAndEmail(ticketId, taskDefKey, oldUser);

        // chỉ update sign zone khi user bàn giao = assignee trong task
        if (signZone != null) {
            // get user info
            List<AccountModel> lstUserInfo = customerService.getAccountByUsernames(Collections.singletonList(newUser));
            if (!ValidationUtils.isNullOrEmpty(lstUserInfo)) {
                signZone.setFirstName(lstUserInfo.stream().filter(e -> e.getUsername().equalsIgnoreCase(newUser)).map(AccountModel::getFirstname).findFirst().orElse(null));
                signZone.setLastName(lstUserInfo.stream().filter(e -> e.getUsername().equalsIgnoreCase(newUser)).map(AccountModel::getLastname).findFirst().orElse(null));
                signZone.setPosition(lstUserInfo.stream().filter(e -> e.getUsername().equalsIgnoreCase(newUser)).map(AccountModel::getFinalTitle).findFirst().orElse(null));
            }

            signZone.setEmail(newUser);
            signZone.setSign(null);
            signZone.setSignedDate(null);
            signZone.setComment(null);
            signZone.setUpdatedUser(credentialHelper.getJWTPayload().getUsername());
            signZone.setUpdatedDate(new Date());

            bpmTpSignZoneRepository.save(signZone);
        }


        // update bpm_task_user
        bpmTaskUserService.updateNewUserName(ticketId, taskId, oldUser, newUser);

        // change_assignee_history với trường hợp user inactive là orgAssignee -> xóa bản ghi ủy quyền
        List<ChangeAssigneeHistory> lstHistory = changeAssigneeHistoryService.findAllByBpmProcinstIdAndTaskIdAndOrgAssignee(bpmProcInst.getTicketId(), taskId, handoverUser);
        if (!ValidationUtils.isNullOrEmpty(lstHistory)) {
            changeAssigneeHistoryService.deleteAll(lstHistory);
        }

    }

    public Map<String, Object> autoCompleteTask(String completedTaskId, String procInstId, String oldTaskDefKey, BpmTask bpmTask, List<SproFlow> workFlow) throws Exception {
        Map<String, Object> resultData = new HashMap<>();

        String account = bpmTask.getTaskAssignee();

        BpmProcInst bpmProcInst = bpmProcInstManager.findBpmProcInstByTicketProcInstId(procInstId);

        // lấy taskDefKey các task tiếp theo của task hoàn thành bằng workFlow

        List<SproFlowNode> nodes = workFlow.stream()
                .flatMap(e -> e.getNodes().stream().filter(node -> node.getType().equals("userTask")))
                .toList();

        // check task multi
        Boolean oldTaskIsMultiInstance = nodes.stream()
                .anyMatch(e -> e.getId().equalsIgnoreCase(oldTaskDefKey) && (e.isMultiInstance() || e.isSequential()));
        Boolean newTaskIsMultiInstance = nodes.stream()
                .anyMatch(e -> e.getId().equalsIgnoreCase(bpmTask.getTaskDefKey()) && (e.isMultiInstance() || e.isSequential()));
        // get task variable
        CompleteTaskDto completeTaskDto = actHiVarInstManager.getAutoCompleteVariable(procInstId, completedTaskId, oldTaskDefKey,
                bpmTask.getTaskDefKey(), oldTaskIsMultiInstance, newTaskIsMultiInstance, account);

        Map<String, Object> camundaTask = camundaEngineService.getTaskById(bpmTask.getTaskId());
        String processInstanceId = null;
        String procDefId = null;
        String taskDefKey = null;
        if (!ValidationUtils.isNullOrEmpty(camundaTask)) {
            processInstanceId = camundaTask.get("processInstanceId").toString();
            procDefId = camundaTask.get("processDefinitionId").toString();
            taskDefKey = camundaTask.get("taskDefinitionKey").toString();
        }

        /* Save ticketAssign (Trạng thái xác nhận ủy quyền tờ trình)*/
        saveIsAssignProcinst(completeTaskDto.getVariables(), bpmProcInst);

        /* BEGIN handle call action api on beginning */
        ActionApiContext actionApiContext = actionApiService.beginHandleActionApi(
                TaskActionConstants.Action.DO_TASK.code,
                bpmProcInst.getTicketProcDefId(),
                taskDefKey,
                procInstId,
                account,
                actionApiService.createVariablesMap(
                        completeTaskDto.getVariables(),
                        bpmProcInst,
                        camundaTask,
                        actionApiService.getTicketVariables(procInstId)
                )
        );

        // check response of api call BEFORE
        actionApiService.setActionApiResponse(actionApiContext, resultData);

        // check allow post process of api call BEFORE
        if (!actionApiContext.isAllowPostProcess()) {
            // reset sign zone
            bpmRuManager.resetSignZoneByTaskDefKeyAndUser(procInstId, taskDefKey, account);
            finishCompleteTicketWithActionUser(actionApiContext, completeTaskDto, procInstId, camundaTask, account);
            resultData.put("isComplete", false);
            return resultData;
        }
        /* END handle call action api on beginning */

        List<String> listTaskDefKey = new ArrayList<>();
        try {
            listTaskDefKey = workFlow.stream()
                    .flatMap(group -> group.getNodes().stream())
                    .filter(n -> n.getId().equals(bpmTask.getTaskDefKey()))
                    .flatMap(node -> node.getNextNodes().stream())
                    .map(NodeInfo::getId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        boolean isComplete = false;
        // Check task runtime là task cuối cùng trong flow -> call api BEFORE COMPLETE TICKET trước khi gọi completeTask bên camunda
        boolean isFinalTask = isFinalTaskInFlow(bpmTask, workFlow);

        if (isFinalTask) {
            isComplete = bpmProcInstManager.completeTicket(procInstId, account, bpmProcInst, taskDefKey, bpmTask.getTaskId(), camundaTask, resultData, completeTaskDto.getVariables());
            if (!isComplete) {
                bpmRuManager.resetSignZoneByTaskDefKeyAndUser(procInstId, taskDefKey, account);
                resultData.put("isComplete", false);
                return resultData;
            }
        }

        // Xóa các biến đã lưu nháp
        List<BpmVariables> lstDraftVari = bpmDraftVariablesRepo.getAllByTaskIdAndIsDraft(bpmTask.getTaskId(), 1);
        if (!ValidationUtils.isNullOrEmpty(lstDraftVari)) {
            bpmVariablesRepository.deleteAll(lstDraftVari);
        }
        saveTaskVari(completeTaskDto.getVariables(), bpmTask.getTaskId(), procInstId);

        completeTaskDto.setWithVariablesInReturn(true);

        camundaEngineService.completeTask(bpmTask.getTaskId(), completeTaskDto);

        updateTaskBpm(bpmTask, account, bpmProcInst, listTaskDefKey, completeTaskDto.getVariables(), true);

        // update sign zone
        List<BpmTpSignZone> signZones = bpmTpSignZoneRepository.findBpmTpSignZoneByProcInstId(procInstId);
        BpmTpSignZone signZone = signZones.stream()
                .filter(e -> e.getTaskDefKey().equalsIgnoreCase(bpmTask.getTaskDefKey()) && e.getEmail().equalsIgnoreCase(account))
                .findFirst().orElse(null);
        if (!ValidationUtils.isNullOrEmpty(signZone)) {
            String comment = "";
            for (Map.Entry<String, VariableValueDto> entry : completeTaskDto.getVariables().entrySet()) {
                String key = entry.getKey();
                if (key.contains("attachmentSignComment")) {
                    comment = entry.getValue().getValue().toString();
                }
            }
            signZone.setSignedDate(new Date());
            signZone.setComment(comment);
            signZone.setSignedFile(signZones.get(0).getSignedFile());
            signZone.setSign(customerService.getDefaultSignatureByUsername(account));
        }

        List<Map<String, Object>> listData = camundaEngineService.getCurrentRuntimeTasks(procInstId);
        List<BpmTask> listSaveTask = new ArrayList<>();
        if (listData.isEmpty()) {
            // lưu lịch sử phiếu
            bpmProcInstManager.saveCompleteTicketHistory(procInstId, account, bpmProcInst, taskDefKey, bpmTask.getTaskId());
        } else {
            List<String> listExistId = new ArrayList<>();
            List<BpmTask> listTask = getByTicketIdAndStatus(processInstanceId, TaskConstants.TabStatus.PROCESSING);

            for (BpmTask task : listTask) {
                listExistId.add(task.getTaskId());
            }
            // (phucvm3) remove exists task
            listData.removeIf(object -> listExistId.contains(object.get("id").toString()));
            listSaveTask = saveTaskFromCamunda(listData, procDefId, account, bpmTask.getTaskId(), procInstId, bpmProcInst, taskDefKey, bpmTask.getTaskType());
            // process sign zone
            updateSignZones(procInstId, account);
            isComplete = true;

            // set lại ticket status - kafka
//            bpmProcInst.setTicketStatus(ProcInstConstants.Status.PROCESSING.code);
        }

        /* BEGIN handle call action api at the end */
        finishCompleteTicketWithActionUser(actionApiContext, completeTaskDto, procInstId, camundaTask, account);
        /* END handle call action api at the end */

        //TODO: Auto complete task
        /*
         * -> transaction
         * -> check procDef config
         * -> check completed task assignee = listData assignee (include current task) + assignType != 1
         * -> variable: check case zoom - normal task -> replace taskDefKey
         * -> recursive
         * */

        BpmProcdef bpmProcdef = bpmProcdefRepository.findByServiceId(bpmProcInst.getServiceId());
        // check cấu hình quy trình + bỏ qua khi ko sinh task mới bên camunda
        if (bpmProcdef.getAutoCompleteTask() != null && bpmProcdef.getAutoCompleteTask() && !ValidationUtils.isNullOrEmpty(listSaveTask)) {

            // lấy list task completed + ko có ủy quyền
            List<BpmTask> listTaskCompleted = bpmTaskRepository.getBpmTaskByTaskProcInstIdAndTaskStatusInAndAssignTypeFalse(processInstanceId, Collections.singletonList(TaskConstants.Status.COMPLETED.code));

            // filter task ko có assignee + ko có template version + có ủy quyền
            List<BpmTask> listSaveTaskFilter = listSaveTask.stream()
                    .filter(task -> task.getTaskAssignee() != null && task.getTemplateVersionId() != null && (task.getAssignType() == null || !task.getAssignType()))
                    .toList();

            if (!ValidationUtils.isNullOrEmpty(listTaskCompleted) && !ValidationUtils.isNullOrEmpty(listSaveTaskFilter)) {
                for (BpmTask saveTask : listSaveTaskFilter) {
                    BpmTask taskCompleted = listTaskCompleted.stream()
                            .filter(task -> task.getTaskAssignee().equalsIgnoreCase(saveTask.getTaskAssignee())
                                    && task.getTemplateVersionId().equals(saveTask.getTemplateVersionId()))
                            .findFirst().orElse(null);
                    if (taskCompleted != null) {
                        // change ticket status - kafka
//                        bpmProcInst.setTicketStatus(ProcInstConstants.Status.WAITING_FOR_APPROVAL.code);
                        // auto complete - recursive
                        resultData = autoCompleteTask(taskCompleted.getTaskId(), procInstId, taskCompleted.getTaskDefKey(), saveTask, workFlow);
                    }
                }
            }
        }

        // check ticket status != wait -> combine file ký
        if (!ValidationUtils.isNullOrEmpty(signZone)) {
            bpmTemplatePrintManager.combineSignedFile(signZone.getBpmTemplatePrintId(), signZone.getProcInstId());
        }
        resultData.put("isComplete", isComplete);

        return resultData;
    }

    public Map<String, String> getTaskInfoByProcInstId(String procInstId) {
        List<Tuple> tuple = bpmTaskRepository.getTaskInfoByProcInstId(procInstId);
        Map<String, String> result = new HashMap<>();
        for (Tuple t : tuple) {
            result.put(t.get("taskDefKey").toString(), t.get("taskStatus").toString());
        }
        return result;
    }

}
