package vn.fis.eapprove.business.domain.report.service.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import vn.fis.eapprove.business.domain.report.service.ReportProcInstService;
import vn.fis.eapprove.business.dto.filter.ReportProcInstFilter;
import vn.fis.eapprove.business.dto.report.ReportProcInstByChartNodeDto;
import vn.fis.eapprove.business.dto.report.ReportProcInstByGroupDto;
import vn.fis.eapprove.business.exception.report.BusinessCode;
import vn.fis.eapprove.business.exception.report.BusinessException;
import vn.fis.eapprove.business.model.response.ReportProcInstByChartNodeResponse;
import vn.fis.eapprove.business.model.response.ReportProcInstByGroupResponse;
import vn.fis.eapprove.business.model.response.UserInfoByUsername;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.utils.ReportHelper;
import vn.fis.spro.common.util.ListCompare;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.Tuple;
import java.math.BigInteger;
import java.text.ParseException;
import java.util.*;


@Service
@AllArgsConstructor
public class ReportProcInstServiceImpl implements ReportProcInstService {

    private EntityManager entityManager;

    private ReportHelper reportHelper;

    private CustomerService customerService;

    @Override
    @SuppressWarnings("unchecked")
    public ReportProcInstByGroupResponse getReportProcInstByGroup(ReportProcInstFilter filter, String username) {
        String status = null;
        if (!ValidationUtils.isNullOrEmpty(filter.getUserStatus()) && filter.getUserStatus().size() == 1) {
            status = filter.getUserStatus().get(0);
        }
        Set<String> users = new HashSet<>(customerService.getUserDefault(username, status));
        users.add(username);
        filter.getDefaultUser().addAll(users);
        if (filter.getUserStatus().size() == 1 && filter.getUserStatus().get(0).equalsIgnoreCase("inactive")) {
            filter.getDefaultUser().remove(username);
        }
        List<Long> chartNodes = customerService.getChartNodeIdsByUsernames(filter.getDefaultUser());
        filter.setDefaultChartNodeId(chartNodes);

        ReportProcInstByGroupResponse reportProcInstByGroupResponse = new ReportProcInstByGroupResponse();

        List<ReportProcInstByGroupDto> list = getReportProcInstByGroupList(filter);
        BigInteger total = countReportProcInstByGroup(filter);

        reportProcInstByGroupResponse.setList(list);
        reportProcInstByGroupResponse.setTotal(total);
        return reportProcInstByGroupResponse;
    }


    @Override
    public ReportProcInstByGroupResponse getReportTaskByUser(ReportProcInstFilter filter, String username) {
        String status = null;
        if (!ValidationUtils.isNullOrEmpty(filter.getUserStatus()) && filter.getUserStatus().size() == 1) {
            status = filter.getUserStatus().get(0);
        }
        Set<String> users = new HashSet<>(customerService.getUserDefault(username, status));
        users.add(username);
        filter.getDefaultUser().addAll(users);
        if (filter.getUserStatus().size() == 1 && filter.getUserStatus().get(0).equalsIgnoreCase("inactive")) {
            filter.getDefaultUser().remove(username);
        }
        List<Long> chartNodes = customerService.getChartNodeIdsByUsernames(filter.getDefaultUser());
        filter.setDefaultChartNodeId(chartNodes);

        ReportProcInstByGroupResponse reportProcInstByGroupResponse = new ReportProcInstByGroupResponse();
        List<ReportProcInstByGroupDto> list = getReportTaskByUserList(filter);
        BigInteger total = countReportTaskByUser(filter);
        reportProcInstByGroupResponse.setList(list);
        reportProcInstByGroupResponse.setTotal(total);
        return reportProcInstByGroupResponse;
    }

    @Override
    @SuppressWarnings("unchecked")
    public ReportProcInstByChartNodeResponse getReportProcInstByChartNode(ReportProcInstFilter filter, String username) {
        String status = null;
        if (!ValidationUtils.isNullOrEmpty(filter.getUserStatus()) && filter.getUserStatus().size() == 1) {
            status = filter.getUserStatus().get(0);
        }
        Set<String> users = new HashSet<>(customerService.getUserDefault(username, status));
        users.add(username);
        filter.getDefaultUser().addAll(users);
        if (filter.getUserStatus().size() == 1 && filter.getUserStatus().get(0).equalsIgnoreCase("inactive")) {
            filter.getDefaultUser().remove(username);
        }
        List<Long> chartNodes = customerService.getChartNodeIdsByUsernames(filter.getDefaultUser());
        filter.setDefaultChartNodeId(chartNodes);

        ReportProcInstByChartNodeResponse response = new ReportProcInstByChartNodeResponse();
        Map<String, List<ReportProcInstByChartNodeDto>> resultMap = new HashMap<>();
        List<ReportProcInstByChartNodeDto> listUserChartNode = getReportProcInstByChartNodeList(filter);
        List<UserInfoByUsername> userInfoByUsernames = customerService.getInfoByListUser(filter.getDefaultUser());
        mapperUserInfoToReportByChartNode(userInfoByUsernames, listUserChartNode);
        for (ReportProcInstByChartNodeDto dto : listUserChartNode) {
            if ((ObjectUtils.isEmpty(filter.getDefaultChartNodeId()) && ObjectUtils.isEmpty(dto.getChartNodeId()) || ListCompare.containsCommonElement(filter.getDefaultChartNodeId(), dto.getChartNodeId()))) {
                for (String chartNodeCode : dto.getChartNodeCode()) {
                    resultMap.computeIfAbsent(chartNodeCode, k -> new ArrayList<>()).add(dto);
                }
            }
        }
        response.setResult(resultMap);
        response.setTotal(BigInteger.valueOf(resultMap.size()));
        return response;
    }

    private static void mapperUserInfoToReportByChartNode(List<UserInfoByUsername> userInfoByUsernames, List<ReportProcInstByChartNodeDto> listUserChartNode) {
        Map<String, UserInfoByUsername> userInfoByUsernameMap = new HashMap<>();
        for (UserInfoByUsername userInfo : userInfoByUsernames) {
            userInfoByUsernameMap.put(userInfo.getUsername(), userInfo);
        }
        for (ReportProcInstByChartNodeDto e : listUserChartNode) {
            UserInfoByUsername userInfo = userInfoByUsernameMap.get(e.getAssignee());
            if (userInfo != null) {
                e.setAssigneeFullName(userInfo.getFullName());
                e.setUserTitleName(userInfo.getTitleName());
                e.setChartShortName(userInfo.getChartShortName());
                e.setChartNodeName(userInfo.getChartNodeName());
                e.setChartId(userInfo.getChartId());
                e.setStaffCode(userInfo.getStaffCode());
            }
        }
    }

    private List<ReportProcInstByGroupDto> getReportProcInstByGroupList(ReportProcInstFilter filter) {

        String mainQuery =
                "select d.* from (select * from (select b.*,\n" +
                        "       ifnull(b.carryoverPreviousApproval, 0) + ifnull(b.duringApprovalPeriod, 0) as pending\n" +
                        " from (SELECT service_id,service_name, \n" +
                        "             SUM(CASE\n" +
                        "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND\n" +
                        "                          created_time < :fromDate THEN 1\n" +
                        "                     ELSE 0 END) as carryoverPreviousApproval,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND\n" +
                        "                          (created_time BETWEEN :fromDate AND :toDate) THEN 1\n" +
                        "                     ELSE 0 END) as duringApprovalPeriod,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN proc_inst_status IN ('COMPLETED','CLOSED') AND (created_time BETWEEN :fromDate AND :toDate) THEN 1\n" +
                        "                     ELSE 0\n" +
                        "                 END)            AS approved,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN\n" +
                        "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time <= sla_finish_time AND \n" +
                        "                                 (created_time BETWEEN :fromDate AND :toDate) \n" +
                        "                         THEN 1\n" +
                        "                     ELSE 0\n" +
                        "                 END)            AS approvedOnTime,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN\n" +
                        "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time > sla_finish_time AND \n" +
                        "                                 (created_time BETWEEN :fromDate AND :toDate) \n" +
                        "                         THEN 1\n" +
                        "                     ELSE 0\n" +
                        "                 END)            AS approvedDelayed,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN\n" +
                        "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST') \n" +
                        "                             AND current_timestamp <= sla_finish_time AND\n" +
                        "                                 created_time < :toDate THEN 1\n" +
                        "                     ELSE 0\n" +
                        "                 END)            AS approvingNotDelayed,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN\n" +
                        "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST')\n" +
                        "                             AND current_timestamp > sla_finish_time AND\n" +
                        "                                 created_time < :toDate THEN 1\n" +
                        "                     ELSE 0\n" +
                        "                 END)            AS approvingDelayed,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN proc_inst_status in  ('DELETED_BY_RU','RECALLED','RECALLING') AND created_time < :toDate THEN 1\n" +
                        "                     ELSE 0\n" +
                        "                 END)            AS approvalReturned,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN proc_inst_status = 'CANCEL' AND (created_time BETWEEN :fromDate AND :toDate) THEN 1\n" +
                        "                     ELSE 0\n" +
                        "                 END)            AS approvalCanceled \n" +
                        "      FROM report_by_group rp \n" +
                        "       WHERE rp.created_user in :defaultUser AND rp.chart_node_id in :defaultChartNodeId AND ";

        // create stringBuilder
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        reportHelper.buildReportByGroupFilter(filter, stringBuilder);
        stringBuilder.append(" 1=1  GROUP BY rp.service_id,rp.service_name) as b \n");
        stringBuilder.append(" where b.carryoverPreviousApproval > 0\n" +
                "   or b.duringApprovalPeriod > 0\n" +
                "   or b.approved > 0\n" +
                "   or b.approvedOnTime > 0\n" +
                "   or b.approvedDelayed > 0\n" +
                "   or b.approvingNotDelayed > 0\n" +
                "   or b.approvingDelayed > 0\n" +
                "   or b.approvalReturned > 0\n" +
                "   or b.approvalCanceled > 0 ");
        stringBuilder.append(" ) AS c LIMIT :index,:size ) as d\n");
        reportHelper.buildReportByGroupSort(filter, stringBuilder);

        // create query
        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);

        reportHelper.buildReportByGroupParameter(filter, query);
        query.setParameter("index", (filter.getPage() - 1) * filter.getPageSize());
        query.setParameter("size", filter.getPageSize());

        List<Tuple> result = query.getResultList();
        List<ReportProcInstByGroupDto> list = new ArrayList<>();
        result.forEach(tuple -> {
            ReportProcInstByGroupDto reportProcInstByGroupDto = new ReportProcInstByGroupDto();
            try {
                reportHelper.tupleToReportProcInstDto(tuple, reportProcInstByGroupDto);
            } catch (ParseException e) {
                throw new BusinessException(BusinessCode.INTERNAL_SERVER_ERROR);
            }

            list.add(reportProcInstByGroupDto);
        });

        return list;
    }


    private BigInteger countReportProcInstByGroup(ReportProcInstFilter filter) {

        String mainQuery = " (select * from (select b.*,\n" +
                "       ifnull(b.carryoverPreviousApproval, 0) + ifnull(b.duringApprovalPeriod, 0) as pending\n" +
                " from (SELECT service_id,service_name, \n" +
                "             SUM(CASE\n" +
                "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND\n" +
                "                          created_time < :fromDate THEN 1\n" +
                "                     ELSE 0 END) as carryoverPreviousApproval,\n" +
                "             SUM(CASE\n" +
                "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND\n" +
                "                          (created_time BETWEEN :fromDate AND :toDate) THEN 1\n" +
                "                     ELSE 0 END) as duringApprovalPeriod,\n" +
                "             SUM(CASE\n" +
                "                     WHEN proc_inst_status IN ('COMPLETED','CLOSED') AND (created_time BETWEEN :fromDate AND :toDate) THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approved,\n" +
                "             SUM(CASE\n" +
                "                     WHEN\n" +
                "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time <= sla_finish_time AND \n" +
                "                                 (created_time BETWEEN :fromDate AND :toDate) \n" +
                "                         THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvedOnTime,\n" +
                "             SUM(CASE\n" +
                "                     WHEN\n" +
                "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time > sla_finish_time AND \n" +
                "                                 (created_time BETWEEN :fromDate AND :toDate) \n" +
                "                         THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvedDelayed,\n" +
                "             SUM(CASE\n" +
                "                     WHEN\n" +
                "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST') \n" +
                "                             AND current_timestamp <= sla_finish_time AND\n" +
                "                                 created_time < :toDate THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvingNotDelayed,\n" +
                "             SUM(CASE\n" +
                "                     WHEN\n" +
                "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST')\n" +
                "                             AND current_timestamp > sla_finish_time AND\n" +
                "                                 created_time < :toDate THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvingDelayed,\n" +
                "             SUM(CASE\n" +
                "                     WHEN proc_inst_status in  ('DELETED_BY_RU','RECALLED','RECALLING') AND created_time < :toDate THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvalReturned,\n" +
                "             SUM(CASE\n" +
                "                     WHEN proc_inst_status = 'CANCEL' AND (created_time BETWEEN :fromDate AND :toDate) THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvalCanceled \n" +
                "      FROM report_by_group rp \n" +
                "       WHERE rp.created_user in :defaultUser AND rp.chart_node_id in :defaultChartNodeId AND ";

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select count(service_id) as count from ").append(mainQuery);
        reportHelper.buildReportByGroupFilter(filter, stringBuilder);

        stringBuilder.append(" 1=1  GROUP BY rp.service_id,rp.service_name) as b\n");
        stringBuilder.append(" where b.carryoverPreviousApproval > 0\n" +
                "   or b.duringApprovalPeriod > 0\n" +
                "   or b.approved > 0\n" +
                "   or b.approvedOnTime > 0\n" +
                "   or b.approvedDelayed > 0\n" +
                "   or b.approvingNotDelayed > 0\n" +
                "   or b.approvingDelayed > 0\n" +
                "   or b.approvalReturned > 0\n" +
                "   or b.approvalCanceled > 0 ");
        stringBuilder.append(" ) as c) as count \n");

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);

        reportHelper.buildReportByGroupParameter(filter, query);

        List<Tuple> result = query.getResultList();
        return result.get(0).get("count", BigInteger.class);
    }

    private List<ReportProcInstByChartNodeDto> getReportProcInstByChartNodeList(ReportProcInstFilter filter) {
        StringBuilder stringBuilder = new StringBuilder();
        String subQuery =
                " WITH information_user as (select distinct rc.assignee as assignee_task,assignee_chart_id,\n" +
                        "       assignee_chart_node_id,assignee_chart_node_code \n" +
                        "from report_by_chart_node rc where assignee in :defaultUser and assignee_chart_node_id in :defaultChartNodeId \n";
        stringBuilder.append(subQuery);
        if (!ValidationUtils.isNullOrEmpty(filter.getChartNodeId())) {
            stringBuilder.append(" and assignee_chart_node_id in :chartNodeId \n");
        }
        stringBuilder.append(" )\n");
        String mainQuery = " select b.*,\n" +
                "       information_user.*,\n" +
                "       ifnull(b.carryoverPreviousApproval,\n" +
                "              0) + ifnull(b.duringApprovalPeriod,0) as pending\n" +
                " from (SELECT assignee,\n" +
                "SUM(CASE\n" +
                "                     WHEN rc.task_status IN ('PROCESSING', 'ACTIVE') AND created_time < :fromDate  THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END) as carryoverPreviousApproval, \n" +
                "             SUM(CASE\n" +
                "                     WHEN task_status IN ('PROCESSING','ACTIVE') AND created_time between :fromDate AND :toDate THEN 1\n" +
                "                     ELSE 0 END) as duringApprovalPeriod,\n" +
                "             SUM(CASE\n" +
                "                     WHEN task_status = 'COMPLETED' AND created_time between :fromDate AND :toDate THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approved,\n" +
                "             SUM(CASE\n" +
                "                     WHEN\n" +
                "                                 task_status = 'COMPLETED' \n" +
                "                             AND finished_time <= sla_finish_time AND created_time between :fromDate AND :toDate THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvedOnTime,\n" +
                "             SUM(CASE\n" +
                "                     WHEN\n" +
                "                                 task_status = 'COMPLETED'\n" +
                "                             AND finished_time > sla_finish_time AND created_time between :fromDate AND :toDate THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvedDelayed,\n" +
                "             SUM(CASE\n" +
                "                     WHEN\n" +
                "                                 task_status IN ('PROCESSING','ACTIVE') \n" +
                "                             AND current_timestamp <= sla_finish_time AND created_time < :toDate THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvingNotDelayed,\n" +
                "             SUM(CASE\n" +
                "                     WHEN\n" +
                "                                 task_status IN ('PROCESSING','ACTIVE')\n" +
                "                             AND current_timestamp > sla_finish_time AND created_time < :toDate THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvingDelayed,\n" +
                "             SUM(CASE\n" +
                "                     WHEN task_status in ('DELETED_BY_RU','AGREE_TO_RECALL') AND created_time < :toDate THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvalReturned,\n" +
                "             SUM(CASE\n" +
                "                     WHEN task_status = 'CANCEL' AND created_time between :fromDate AND :toDate THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvalCanceled\n" +
                "      FROM report_by_chart_node rc\n" +
                "      WHERE rc.assignee in :defaultUser AND rc.assignee_chart_node_id in :defaultChartNodeId AND \n";


        stringBuilder.append(mainQuery);
        reportHelper.buildReportTaskByChartNodeFilter(filter, stringBuilder);
        stringBuilder.append(" 1=1 \n" +
                "      GROUP BY assignee) as b\n" +
                "     JOIN information_user on information_user.assignee_task = b.assignee \n" +
                " WHERE "
        );
        stringBuilder.append(" b.carryoverPreviousApproval > 0\n" +
                "   or b.duringApprovalPeriod > 0\n" +
                "   or b.approved > 0\n" +
                "   or b.approvedOnTime > 0\n" +
                "   or b.approvedDelayed > 0\n" +
                "   or b.approvingNotDelayed > 0\n" +
                "   or b.approvingDelayed > 0\n" +
                "   or b.approvalReturned > 0\n" +
                "   or b.approvalCanceled > 0 AND ");
        stringBuilder.append(" 1=1");
        reportHelper.buildReportByGroupSort(filter, stringBuilder);
        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);

        reportHelper.buildReportTaskByChartNodeParameter(filter, query);

        List<Tuple> result = query.getResultList();
        List<ReportProcInstByChartNodeDto> list = new ArrayList<>();
        result.forEach(tuple -> {
            ReportProcInstByChartNodeDto reportProcInstByChartNodeDto = new ReportProcInstByChartNodeDto();
            try {
                reportHelper.tupleToReportProcInstDto(tuple, reportProcInstByChartNodeDto);
            } catch (ParseException e) {
                throw new BusinessException(BusinessCode.INTERNAL_SERVER_ERROR);
            }

            list.add(reportProcInstByChartNodeDto);
        });
        return list;
    }

    private List<ReportProcInstByGroupDto> getReportTaskByUserList(ReportProcInstFilter filter) {

        String mainQuery =
                "select d.* from (select * from (select b.*,\n" +
                        "       ifnull(b.carryoverPreviousApproval, 0) + ifnull(b.duringApprovalPeriod, 0) as pending\n" +
                        " from (SELECT service_id,service_name, \n" +
                        "             SUM(CASE\n" +
                        "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND\n" +
                        "                          action_current_time < :fromDate THEN 1\n" +
                        "                     ELSE 0 END) as carryoverPreviousApproval,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND\n" +
                        "                          (action_current_time BETWEEN :fromDate AND :toDate) THEN 1\n" +
                        "                     ELSE 0 END) as duringApprovalPeriod,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN proc_inst_status IN ('COMPLETED','CLOSED') AND (action_current_time BETWEEN :fromDate AND :toDate) THEN 1\n" +
                        "                     ELSE 0\n" +
                        "                 END)            AS approved,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN\n" +
                        "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time <= sla_finish_time AND \n" +
                        "                                 (action_current_time BETWEEN :fromDate AND :toDate) \n" +
                        "                         THEN 1\n" +
                        "                     ELSE 0\n" +
                        "                 END)            AS approvedOnTime,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN\n" +
                        "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time > sla_finish_time AND \n" +
                        "                                 (action_current_time BETWEEN :fromDate AND :toDate) \n" +
                        "                         THEN 1\n" +
                        "                     ELSE 0\n" +
                        "                 END)            AS approvedDelayed,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN\n" +
                        "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST') \n" +
                        "                             AND current_timestamp <= sla_finish_time AND \n" +
                        "                                 action_current_time < :toDate THEN 1\n" +
                        "                     ELSE 0\n" +
                        "                 END)            AS approvingNotDelayed,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN\n" +
                        "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST')\n" +
                        "                             AND current_timestamp > sla_finish_time AND\n" +
                        "                                 action_current_time < :toDate THEN 1\n" +
                        "                     ELSE 0\n" +
                        "                 END)            AS approvingDelayed,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN proc_inst_status in  ('DELETED_BY_RU','RECALLED','RECALLING') AND action_current_time < :toDate THEN 1\n" +
                        "                     ELSE 0\n" +
                        "                 END)            AS approvalReturned,\n" +
                        "             SUM(CASE\n" +
                        "                     WHEN proc_inst_status = 'CANCEL' AND (action_current_time BETWEEN :fromDate AND :toDate) THEN 1\n" +
                        "                     ELSE 0\n" +
                        "                 END)            AS approvalCanceled \n" +
                        "      FROM report_by_group rp \n" +
                        "       WHERE ";

        // create stringBuilder
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        reportHelper.buildReportByUserFilter(filter, stringBuilder);
        stringBuilder.append(" 1=1  GROUP BY rp.service_id,rp.service_name) as b \n");
        stringBuilder.append(" where b.carryoverPreviousApproval > 0\n" +
                "   or b.duringApprovalPeriod > 0\n" +
                "   or b.approved > 0\n" +
                "   or b.approvedOnTime > 0\n" +
                "   or b.approvedDelayed > 0\n" +
                "   or b.approvingNotDelayed > 0\n" +
                "   or b.approvingDelayed > 0\n" +
                "   or b.approvalReturned > 0\n" +
                "   or b.approvalCanceled > 0 ");
        stringBuilder.append(" ) AS c LIMIT :index,:size ) as d\n");
        reportHelper.buildReportByGroupSort(filter, stringBuilder);

        // create query
        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);

        reportHelper.buildReportTaskByUserParameter(filter, query);

        query.setParameter("index", (filter.getPage() - 1) * filter.getPageSize());
        query.setParameter("size", filter.getPageSize());

        List<Tuple> result = query.getResultList();
        List<ReportProcInstByGroupDto> list = new ArrayList<>();
        result.forEach(tuple -> {
            ReportProcInstByGroupDto reportProcInstByGroupDto = new ReportProcInstByGroupDto();
            try {
                reportHelper.tupleToReportProcInstDto(tuple, reportProcInstByGroupDto);
            } catch (ParseException e) {
                throw new BusinessException(BusinessCode.INTERNAL_SERVER_ERROR);
            }

            list.add(reportProcInstByGroupDto);
        });

        return list;
    }


    private BigInteger countReportTaskByUser(ReportProcInstFilter filter) {
        String mainQuery = " (select * from (select b.*,\n" +
                "       ifnull(b.carryoverPreviousApproval, 0) + ifnull(b.duringApprovalPeriod, 0) as pending\n" +
                " from (SELECT service_id,service_name, \n" +
                "             SUM(CASE\n" +
                "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND\n" +
                "                          action_current_time < :fromDate THEN 1\n" +
                "                     ELSE 0 END) as carryoverPreviousApproval,\n" +
                "             SUM(CASE\n" +
                "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND\n" +
                "                          (action_current_time BETWEEN :fromDate AND :toDate) THEN 1\n" +
                "                     ELSE 0 END) as duringApprovalPeriod,\n" +
                "             SUM(CASE\n" +
                "                     WHEN proc_inst_status IN ('COMPLETED','CLOSED') AND (action_current_time BETWEEN :fromDate AND :toDate) THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approved,\n" +
                "             SUM(CASE\n" +
                "                     WHEN\n" +
                "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time <= sla_finish_time AND \n" +
                "                                 (action_current_time BETWEEN :fromDate AND :toDate) \n" +
                "                         THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvedOnTime,\n" +
                "             SUM(CASE\n" +
                "                     WHEN\n" +
                "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time > sla_finish_time AND \n" +
                "                                 (action_current_time BETWEEN :fromDate AND :toDate) \n" +
                "                         THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvedDelayed,\n" +
                "             SUM(CASE\n" +
                "                     WHEN\n" +
                "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST') \n" +
                "                             AND current_timestamp <= sla_finish_time AND\n" +
                "                                 action_current_time < :toDate THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvingNotDelayed,\n" +
                "             SUM(CASE\n" +
                "                     WHEN\n" +
                "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST')\n" +
                "                             AND current_timestamp > sla_finish_time AND\n" +
                "                                 action_current_time < :toDate THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvingDelayed,\n" +
                "             SUM(CASE\n" +
                "                     WHEN proc_inst_status in  ('DELETED_BY_RU','RECALLED','RECALLING') AND action_current_time < :toDate THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvalReturned,\n" +
                "             SUM(CASE\n" +
                "                     WHEN proc_inst_status = 'CANCEL' AND (action_current_time BETWEEN :fromDate AND :toDate) THEN 1\n" +
                "                     ELSE 0\n" +
                "                 END)            AS approvalCanceled \n" +
                "      FROM report_by_group rp \n" +
                "       WHERE ";

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select count(service_id) as count from ").append(mainQuery);
        reportHelper.buildReportByUserFilter(filter, stringBuilder);

        stringBuilder.append(" 1=1  GROUP BY rp.service_id,rp.service_name) as b\n");
        stringBuilder.append(" where b.carryoverPreviousApproval > 0\n" +
                "   or b.duringApprovalPeriod > 0\n" +
                "   or b.approved > 0\n" +
                "   or b.approvedOnTime > 0\n" +
                "   or b.approvedDelayed > 0\n" +
                "   or b.approvingNotDelayed > 0\n" +
                "   or b.approvingDelayed > 0\n" +
                "   or b.approvalReturned > 0\n" +
                "   or b.approvalCanceled > 0 ");
        stringBuilder.append(" ) as c) as count \n");

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);

        reportHelper.buildReportTaskByUserParameter(filter, query);

        List<Tuple> result = query.getResultList();
        return result.get(0).get("count", BigInteger.class);
    }

}
