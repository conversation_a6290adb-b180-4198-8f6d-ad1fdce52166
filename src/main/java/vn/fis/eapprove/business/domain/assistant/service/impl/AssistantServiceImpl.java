package vn.fis.eapprove.business.domain.assistant.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.assistant.entity.Assistant;
import vn.fis.eapprove.business.domain.assistant.entity.AssistantOpinion;
import vn.fis.eapprove.business.domain.assistant.repository.AssistantOpinionRepository;
import vn.fis.eapprove.business.domain.assistant.repository.AssistantRepository;
import vn.fis.eapprove.business.domain.assistant.service.AssistantService;
import vn.fis.eapprove.business.dto.AssistantOpinionDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.specification.AssistantOpinionSpecification;

import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.util.DateTimeUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Author: AnhVTN
 * Date: 01/03/2023
 */

@Service("AssistantServiceImplV1")
@Slf4j
@Transactional
public class AssistantServiceImpl implements AssistantService {
    private final AssistantRepository repository;
    private final AssistantOpinionRepository opinionRepository;
    private final AssistantOpinionSpecification assistantOpinionSpecification;
    private final ResponseUtils responseUtils;

    @Autowired
    public AssistantServiceImpl(AssistantRepository repository,
                                AssistantOpinionRepository opinionRepository,
                                AssistantOpinionSpecification assistantOpinionSpecification,
                                ResponseUtils responseUtils) {
        this.repository = repository;
        this.opinionRepository = opinionRepository;
        this.assistantOpinionSpecification = assistantOpinionSpecification;
        this.responseUtils = responseUtils;
    }

    @Override
    public List<Assistant> getListAssistantByTicketId(String ticketId) {
        return repository.getAssistantByTicketId(ticketId);
    }

    @Override
    public PageDto getAssistantsOpinion(AssistantOpinionDto request) {
        int pageNum = request.getPage() - 1;
        Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());
        List<AssistantOpinion> templates = new ArrayList<>();
        Page<AssistantOpinion> page = opinionRepository.findAll(
                assistantOpinionSpecification.filter(request),
                PageRequest.of(pageNum, request.getSize(), sort)
        );
        if (page.hasContent()) {
            page.getContent().stream().map(item -> {
                item.setFormatedCreateAt(DateTimeUtils.dateToString(item.getCreateAt(), "dd/MM/yyyy HH:mm:ss"));
                item.setFormatedUpdateAt(DateTimeUtils.dateToString(item.getUpdateAt(), "dd/MM/yyyy HH:mm:ss"));
                return item;
            }).collect(Collectors.toList());

            templates = page.getContent();
        }
        return PageDto.builder().content(templates)
                .number(page.getNumber() + 1)
                .numberOfElements(page.getNumberOfElements())
                .page(page.getNumber() + 1)
                .size(page.getSize())
                .sortBy(request.getSortBy())
                .sortBy(request.getSortType())
                .totalPages(page.getTotalPages())
                .totalElements(page.getTotalElements())
                .build();
    }

    @Override
    public List<String> getTicketIdByAssistanEmail(String email) {
        return repository.getListTicketByAssistantEmail(email);
    }

    @Override
    public List<String> getListAssistantTicket(List<String> usernames) {
        return repository.getListAssistantTicket(usernames);
    }
}
