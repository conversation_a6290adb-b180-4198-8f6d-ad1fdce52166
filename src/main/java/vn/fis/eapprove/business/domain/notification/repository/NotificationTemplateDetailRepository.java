package vn.fis.eapprove.business.domain.notification.repository;

import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.notification.entity.NotificationTemplateDetail;


import java.util.List;

/**
 * Author: AnhVTN
 * Date: 28/02/2023
 */

@Repository
public interface NotificationTemplateDetailRepository extends JpaRepository<NotificationTemplateDetail, Long>, JpaSpecificationExecutor<NotificationTemplateDetail> {

    @Query("select ntd  from NotificationTemplateDetail ntd where ntd.notificationTemplateId = :templateId")
    List<NotificationTemplateDetail> getDetailFromTemplateId(@Param("templateId") Long templateId);

    @Query("select ntd  from NotificationTemplateDetail ntd where ntd.notificationTemplateId = :templateId and ntd.type in :type")
    List<NotificationTemplateDetail> getTemplateDetailFromTemplateIdAndType(@Param("templateId") Long templateId,@Param("type") List<String> type);

    void deleteAllByNotificationTemplateId(Long templateId);
}
