package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "bpm_procdef_inherits")
@Data
public class BpmProcdefInherits {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "bpm_procdef_id")
    private Long bpmProcdefId;

    @Column(name = "proc_def_id")
    private String procDefId;

    @Column(name = "task_def_key")
    private String taskDefKey;

    @Column(name = "task_def_key_inherits")
    private String taskDefKeyInherits;

    @Column(name = "field_inherits")
    private String fieldInherits;

    @Column(name = "status")
    private String status;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "updated_user")
    private String updatedUser;

}
