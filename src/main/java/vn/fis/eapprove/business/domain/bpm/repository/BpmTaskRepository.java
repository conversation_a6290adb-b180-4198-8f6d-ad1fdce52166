package vn.fis.eapprove.business.domain.bpm.repository;

import jakarta.persistence.Tuple;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.dto.ExpireDto;
import vn.fis.eapprove.business.dto.FilterTaskOptionDto;
import vn.fis.eapprove.business.model.response.ReminderTaskProcessingResponse;


import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Generated by Speed Generator
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 */
@Repository
public interface BpmTaskRepository extends JpaRepository<BpmTask, Long>, JpaSpecificationExecutor<BpmTask> {

    BpmTask getBpmTaskByTaskId(String taskId);

    Boolean existsBpmTaskByTaskIdAndTaskStatus(String taskId, String taskStatus);

    Boolean existsByTaskProcInstIdAndTaskStatusAndTaskDefKeyIn(String procInstId, String status, List<String> listTaskKey);

    Boolean existsByTaskProcInstIdAndTaskIsFirstAndTaskStartedTimeIsNotNull(String procInstId, Boolean isFirst);

    @Query("SELECT (task) \n" +
            "FROM BpmTask task \n" +
            "WHERE task.taskProcInstId in :procInstId AND task.taskStatus in :status AND (task.taskCreatedTime BETWEEN :startTime AND :endTime) ")
    List<BpmTask> countTask(List<String> procInstId, LocalDateTime startTime, LocalDateTime endTime, String[] status);

    @Query("SELECT (task) \n" +
            "FROM BpmTask task INNER join BpmProcInst procIns on task.taskProcInstId = procIns.ticketProcInstId\n" +
            "WHERE  task.taskStatus in :status AND (task.taskCreatedTime BETWEEN :startTime AND :endTime) AND task.taskAssignee = :account")
    List<BpmTask> countTaskByChartNode(LocalDateTime startTime, LocalDateTime endTime, String[] status, String account);

    @Query("SELECT distinct task \n" +
            "FROM BpmTask task \n" +
            "WHERE task.taskProcInstId = :procInstId AND task.taskIsFirst = true ")
    List<BpmTask> getFirstTask(@Param("procInstId") String procInstId);

    @Query("select task from BpmTask task " +
            "left join ChangeAssigneeHistory ca on ca.taskId = task.taskId and task.taskAssignee = ca.toAssignee " +
            "where task.taskId in :listTaskId and task.taskStatus in :listStatus " +
            "and (" +
            "  (task.taskAssignee = :account and task.assignType is null) " +
            " or (task.assignType is true and ca.orgAssignee = :account)" +
            ") ")
    List<BpmTask> getListTaskByListTaskId(List<String> listTaskId, List<String> listStatus, String account);


    @Query("SELECT task \n" +
            "FROM BpmTask task \n" +
            "WHERE task.taskDefKey in :listTaskKey AND task.taskProcInstId = :procInstId AND task.taskStatus = :status")
    List<BpmTask> listTaskZoom(@Param("procInstId") String procInstID, @Param("listTaskKey") List<String> listTaskKey, @Param("status") String status);

    @Query("SELECT task \n" +
            "FROM BpmTask task \n" +
            "WHERE task.taskDefKey = :taskDefKey AND task.taskProcInstId = :procInstId AND task.taskStatus = 'COMPLETED'")
    List<BpmTask> listTaskCompleted(@Param("taskDefKey") String taskDefKey, @Param("procInstId") String procInstId);

    @Modifying
    @Query("UPDATE BpmTask task SET task.taskStatus = 'DELETED_BY_EDIT' WHERE task.taskDefKey = :taskDefKey AND task.taskProcInstId = :procInstId AND task.taskStatus = 'COMPLETED'")
    void deleteTaskByEdit(@Param("taskDefKey") String taskDefKey, @Param("procInstId") String procInstId);

    @Query("SELECT distinct task " +
            "FROM BpmTask task " +
            "WHERE task.taskProcInstId = :procInstId AND task.taskDefKey in :listKey order by task.id desc ")
    List<BpmTask> listRuTask(@Param("procInstId") String procInstId, @Param("listKey") List<String> listKey);

    @Query("SELECT DISTINCT task.taskDefKey " +
            "FROM BpmTask task " +
            "WHERE task.taskProcInstId = :procInstId " +
            "AND task.taskStatus <> 'DELETED_BY_RU' AND task.taskDefKey IN :taskKeys")
    Set<String> getAffectedKeys(@Param("procInstId") String procInstId, @Param("taskKeys") List<String> taskKeys);

    @Query("SELECT distinct task " +
            "FROM BpmTask task " +
            "WHERE task.taskProcInstId = :procInstId" +
            " and task.taskStatus in :listStatus and task.taskDefKey in :listKey")
    List<BpmTask> getTaskByTasksAndStatus(@Param("procInstId") String procInstId,
                                          @Param("listKey") List<String> listKey,
                                          @Param("listStatus") List<String> listStatus);

    List<BpmTask> getBpmTaskByTaskIdIn(List<String> listId);

    List<BpmTask> getBpmTaskByTaskProcInstIdAndTaskStatusIn(String procIntsId, List<String> listStatus);

    @Query("select a from BpmTask a " +
            "where a.taskProcInstId = :procInstId " +
            "and a.taskStatus in :listStatus " +
            "and (a.assignType is null or a.assignType is false)")
    List<BpmTask> getBpmTaskByTaskProcInstIdAndTaskStatusInAndAssignTypeFalse(String procInstId, List<String> listStatus);

    List<BpmTask> getBpmTaskByTaskProcInstId(String procInstId);

    List<BpmTask> getBpmTaskByTaskProcInstIdAndTaskStatus(String procInstId, String status);

    List<BpmTask> getBpmTaskByTaskStatusAndTaskProcInstIdIn(String active, List<String> procInstId);

    List<BpmTask> getBpmTaskByTaskStatusInAndTaskProcInstIdIn(List<String> active, List<String> procInstId);

    @Query("select new vn.fis.eapprove.business.dto.FilterTaskOptionDto(bt.taskProcInstId, bt.taskId, bt.taskAssignee, bt.taskName) from BpmTask bt " +
            " where bt.taskStatus in (:active) and bt.taskProcInstId in (:procInstId)")
    List<FilterTaskOptionDto> getFilterTaskOptions(List<String> active, List<String> procInstId);

    List<BpmTask> getBpmTaskByTaskProcInstIdIn(List<String> procInstId);

    @Query("SELECT a FROM BpmTask a "
            + "WHERE a.taskDefKey = :taskDefKey AND a.taskProcInstId = :procInstId")
    List<BpmTask> getBpmTaskInfoByTaskDefKeyAndProcInstId(@Param("taskDefKey") String taskDefKey, @Param("procInstId") String procInstId);

    @Query("SELECT a FROM BpmTask a "
            + "WHERE a.taskProcInstId = :procInstId AND a.taskStatus = 'ACTIVE'")
    List<BpmTask> loadActiveTask(@Param("procInstId") String procInstId);

    @Query(value = "select b from BpmTask b where b.taskAssignee = :user and b.taskType = :type and b.taskStatus ='active'")
    List<BpmTask> loadActiveTaskByType(@Param("type") String type, @Param("user") String user);

    BpmTask findBpmTaskByTaskDefKeyAndTaskProcInstIdAndTaskStatus(String taskKey, String procInstId, String status);

    List<BpmTask> findBpmTaskByTaskDefKeyAndTaskProcInstId(String taskKey, String procInstId);

    @Modifying
    @Transactional
    @Query("UPDATE BpmTask b SET b.taskStartedTime = :time WHERE b.taskId = :id")
    void startPhase(@Param("time") LocalDateTime time, @Param("id") String id);

    @Modifying
    @Query("UPDATE BpmTask b SET b.taskStatus = 'DELETED_BY_RU' WHERE b.taskProcInstId = :id")
    void deleteByRuTicket(@Param("id") String id);

    @Modifying
    @Query("UPDATE BpmTask b SET b.taskAssignee = :user WHERE b.taskId = :id")
    void claimTask(@Param("user") String user, @Param("id") String id);

    @Query("SELECT a.taskStatus,  COUNT(distinct a.id)" +
            "FROM BpmTask a JOIN BpmProcInst b ON a.taskProcInstId = b.ticketProcInstId " +
            "JOIN ServicePackage c ON b.serviceId = c.id " +
            "JOIN BpmProcdef d ON c.processId = d.id " +
            "JOIN BpmTaskUser t ON b.ticketProcInstId = t.procInstId " +
            "WHERE lower(t.userName) = lower(:username) AND lower(a.taskType) = lower(:type) " +
            "AND (a.taskStatus in ('ACTIVE','PROCESSING','RE_CREATED_BY_RU','DELETED_BY_RU','WAIT')) " +
            "AND b.ticketStatus not in ('DRAFT','CANCEL')" +
            "AND (COALESCE(:users, NULL) IS NULL OR b.createdUser IN :users)" +
            "AND (COALESCE(:services, NULL) IS NULL OR c.serviceName IN :services)" +
            "GROUP BY a.taskStatus")
    List<Object[]> countTask(@Param("username") String username, @Param("type") String type, @Param("users") List<String> users, @Param("services") List<String> services);

    @Query("SELECT COUNT(distinct a.id)" +
            "FROM BpmTask a JOIN BpmProcInst b ON a.taskProcInstId = b.ticketProcInstId " +
            "JOIN ServicePackage c ON b.serviceId = c.id " +
            "JOIN BpmProcdef d ON c.processId = d.id " +
            "WHERE lower(a.taskAssignee) = lower(:username) AND lower(a.taskType) = lower(:type) " +
            "AND b.ticketStatus = 'CANCEL' " +
            "AND (COALESCE(:users, NULL) IS NULL OR b.ticketStartUserId IN :users)")
    Long countCancel(@Param("username") String username, @Param("type") String type, @Param("users") List<String> users);

    @Query("SELECT b.taskType,  COUNT(b.taskId)\n" +
            "FROM BpmTask b\n" +
            "WHERE b.taskAssignee = :username\n" +
            "GROUP BY b.taskType")
    List<Object[]> CountType(@Param("username") String username);

    @Query("SELECT b.ticketProcInstId, b.ticketTitle, b.ticketStartUserId, d.procDefId, s.serviceName, b.ticketId \n" +
            "FROM BpmProcInst b \n" +
            "JOIN BpmProcdef d on d.procDefId = b.ticketProcDefId \n" +
            "JOIN ServicePackage s on s.id = b.serviceId \n" +
            "WHERE b.ticketProcInstId in :procInstId"
    )
    List<Object[]> listAdditional(@Param("procInstId") List<String> procInstId);

    @Query("SELECT t.taskAssignee, COUNT(t.taskId)\n" +
            "FROM BpmTask t \n" +
            "WHERE t.taskAssignee in :listUser " +
            "AND t.taskStatus in ('active', 'processing')\n" +
            "GROUP BY t.taskAssignee"
    )
    List<Object[]> getMinTaskUser(@Param("listUser") List<String> listUser);

    @Query("SELECT DISTINCT b.createdUser, s.serviceName\n" +
            "FROM BpmTask t \n" +
            "JOIN BpmProcInst b on b.ticketProcInstId = t.taskProcInstId \n" +
            "JOIN BpmTaskUser u on u.taskId = t.taskId \n" +
            "JOIN ServicePackage s on s.id = b.serviceId \n" +
            "WHERE t.taskStatus in :status \n" +
            "AND t.taskType in :listType \n" +
            "AND LOWER(u.userName) =  LOWER(:user)"
    )
    List<Object[]> getServicesAndUser(@Param("status") List<String> status,
                                      @Param("listType") List<String> listType,
                                      @Param("user") String user);

    @Query("SELECT DISTINCT b.createdUser, s.serviceName\n" +
            "FROM BpmTask t \n" +
            "JOIN BpmProcInst b on b.ticketProcInstId = t.taskProcInstId \n" +
            "JOIN BpmTaskUser u on u.taskId = t.taskId \n" +
            "JOIN ServicePackage s on s.id = b.serviceId \n" +
            "WHERE t.taskStatus = :status\n" +
            "AND t.taskType in :listType \n" +
            "AND LOWER(u.userName) =  LOWER(:user) "
    )
    List<Object[]> getServicesAndUser(@Param("status") String status,
                                      @Param("listType") List<String> listType,
                                      @Param("user") String user);

    @Query("SELECT DISTINCT b.createdUser, s.serviceName\n" +
            "FROM BpmTask t \n" +
            "JOIN BpmProcInst b on b.ticketProcInstId = t.taskProcInstId \n" +
            "JOIN ServicePackage s on s.id = b.serviceId \n" +
            "JOIN BpmTaskUser u on u.taskId = t.taskId \n" +
            "JOIN BpmShared p on p.procInstId = b.ticketProcInstId \n" +
            "WHERE LOWER(p.sharedUser) = LOWER(:user) AND t.taskType in :listType " +
            "AND LOWER(u.userName) =  LOWER(:user) " +
            "AND b.ticketStatus not in ('CLOSED','COMPLETED','DRAFT') "
    )
    List<Object[]> getServicesAndUserShared(
            @Param("listType") List<String> listType,
            @Param("user") String user);

    @Query("SELECT b.ticketStartUserId\n" +
            "FROM BpmTask t \n" +
            "JOIN BpmProcInst b on b.ticketProcInstId = t.taskProcInstId \n" +
            "JOIN BpmProcdef d on d.procDefId = b.ticketProcDefId \n" +
            "JOIN ServicePackage s on s.id = b.serviceId \n" +
            "WHERE b.ticketStatus in :status\n" +
            "AND t.taskAssignee = :user AND t.taskType in :listType"
    )
    List<String> getUsers(@Param("status") List<String> status,
                          @Param("listType") List<String> listType,
                          @Param("user") String user);

    List<BpmTask> findBpmTaskByTaskDefKeyAndTaskProcDefId(String taskDefKey, String taskProcDefId);

    List<BpmTask> findBpmTasksByTaskProcInstIdAndTaskDefKeyAndTaskStatusIn(String taskProcInstId, String taskDefKey, List<String> taskStatus);

    List<BpmTask> findBpmTaskByTaskAssigneeAndTaskStatus(String assignee, String status);

    @Query("select bt.taskId,bt.taskName,bt.taskAssignee," +
            "case when bt.taskStartedTime is null then bt.slaResponse else bt.slaFinish end, " +
            "case when bt.taskStartedTime is null then bt.taskCreatedTime else bt.taskStartedTime end, " +
            "case when bt.taskStartedTime is null then bt.slaResponseTime else bt.slaFinishTime end, " +
            "case when bt.taskStartedTime is null then 1 else 2 end," +
            "bpi.priority, bpi.ticketId, bpi.ticketTitle, bt.taskProcDefId, bt.taskDefKey " +
            "from BpmTask bt " +
            "inner join BpmProcInst bpi on bt.taskProcInstId = bpi.ticketProcInstId " +
            "inner join ServicePackage sp on bpi.serviceId = sp.id " +
            "where sp.idOrgChart = :chartId and (lower(bt.taskName) like lower(concat('%',:search,'%')) or lower(bpi.ticketTitle) like lower(concat('%',:search,'%'))) and lower(bt.taskAssignee) in :assignee  and bt.taskStatus in ('ACTIVE','PROCESSING') " +
            "and ((-1 in :lsStatus) or ( 1 in :lsStatus and bt.taskStartedTime is null) or ( 2 in :lsStatus and bt.taskStartedTime is not null and bt.taskFinishedTime is null)) " +
            "and (('-1' in :lsPriority) or (lower(bpi.priority) in :lsPriority))")
    Page<Object[]> findBpmTaskActiveCallCenter(@Param("chartId") Long chartId, @Param("search") String search, @Param("assignee") List<String> assignee, @Param("lsStatus") List<Integer> lsStatus, @Param("lsPriority") List<String> lsPriority, Pageable pageable);

    @Query("SELECT DISTINCT b.ticketStartUserId, s.serviceName " +
            "FROM BpmTask t " +
            "JOIN BpmProcInst b on b.ticketProcInstId = t.taskProcInstId " +
            "JOIN ServicePackage s on s.id = b.serviceId " +
            "JOIN BpmVariables v on v.taskId = t.taskId " +
            "WHERE v.isDraft = 1 " +
            "AND LOWER(t.taskAssignee) = :user AND t.taskType in :listType"
    )
    List<Object[]> getServicesAndUserByDraftStatus(@Param("listType") List<String> listType, @Param("user") String user);

    @Modifying
    @Query("UPDATE BpmTask SET taskStatus = 'CANCEL' "
            + "WHERE taskProcInstId = :procInstId AND taskStatus IN ('ACTIVE', 'PROCESSING','RE_CREATED_BY_RU')")
    void cancelActiveTasks(@Param("procInstId") String procInstId);

    @Query("SELECT a FROM BpmTask a WHERE a.taskProcInstId IN (:procInstIds) AND a.taskStatus IN (:status)")
    List<BpmTask> getAllTasksByStatus(@Param("procInstIds") List<String> procInstIds, @Param("status") List<String> status);

    @Query("SELECT a.taskAssignee FROM BpmTask a WHERE a.taskProcInstId = :procInstId")
    List<String> getAllUserAssigneeByProcInstId(@Param("procInstId") String procInstId);

    @Query("SELECT a.taskAssignee FROM BpmTask a WHERE a.taskProcInstId = :procInstId AND a.taskDefKey IN (:taskDefKeys)")
    List<String> getAllUserAssigneeByTaskDefKeys(@Param("procInstId") String procInstId, @Param("taskDefKeys") List<String> taskDefKeys);

    @Query("SELECT a.taskAssignee FROM BpmTask a WHERE a.taskProcInstId = :procInstId AND a.taskStatus IN (:status)")
    List<String> getAllUserAssigneeByStatus(@Param("procInstId") String procInstId, @Param("status") List<String> status);

    @Query("SELECT distinct b.userName FROM BpmTask a " +
            " JOIN BpmTaskUser b on a.id = b.bpmTaskId " +
            " WHERE a.taskProcInstId = :procInstId AND a.taskStatus IN (:status)")
    List<String> getAllTaskUserByStatus(@Param("procInstId") String procInstId, @Param("status") List<String> status);

    @Query("SELECT distinct b.userName " +
            "FROM BpmTask a " +
            "INNER JOIN BpmTaskUser b on a.id = b.bpmTaskId " +
            "WHERE a.taskProcInstId = :procInstId AND a.taskDefKey IN (:taskDefKeys) AND a.taskStatus IN (:status)")
    List<String> getAllUserAssigneeByTaskDefKeyAndStatus(@Param("procInstId") String procInstId, @Param("taskDefKeys") List<String> taskDefKeys, @Param("status") List<String> status);

    @Modifying
    @Query("UPDATE BpmTask SET taskAssignee = :assignee, assignType = :assignType "
            + "WHERE taskProcInstId = :procInstId AND taskDefKey = :taskDefKey AND taskAssignee = :oldAssignee")
    void updateOldTaskToNewAssignee(@Param("procInstId") String procInstId,
                                    @Param("taskDefKey") String taskDefKey,
                                    @Param("oldAssignee") String oldAssignee,
                                    @Param("assignee") String assignee,
                                    @Param("assignType") boolean assignType);

    @Query("select bt.taskAssignee from BpmTask bt where bt.taskProcInstId =:taskProcInstId")
    List<String> getAllAssigneeOfTicket(@Param("taskProcInstId") String taskProcInstId);

    @Query("select new vn.fis.eapprove.business.dto.ExpireDto(b.ticketId,bt.taskAssignee,bt.slaResponseTime,bt.slaFinishTime) from BpmTask bt JOIN BpmProcInst b on b.ticketProcInstId = bt.taskProcInstId where bt.taskStatus =:status")
    List<ExpireDto> findBpmTaskByTaskStatus(String status, Pageable pageable);

    @Query("SELECT COUNT(a) FROM BpmTask a "
            + "WHERE a.taskStatus IN (:status) "
            + "AND a.taskProcInstId = :procInstId "
            + "AND a.id != :bpmTaskId")
    Long countRemainTask(@Param("procInstId") String procInstId,
                         @Param("bpmTaskId") Long bpmTaskId,
                         @Param("status") List<String> status);

    @Query("Select distinct t.taskStatus as taskStatus from BpmTask t INNER JOIN BpmProcInst p ON p.ticketProcInstId= t.taskProcInstId where t.taskStatus not in ('AGREE_TO_RECALL', 'DELETED_BY_RU')")
    List<String> listTaskStatus();

    @Query("select distinct sp from BpmTask sp " +
            "JOIN BpmProcInst b on sp.taskProcInstId = b.ticketProcInstId")
    Page<BpmTask> getTask(Pageable pageable);

    BpmTask findBpmTaskByTaskProcInstIdAndTaskDefKeyAndAssignType(String procInstId, String taskDefKey, Boolean assignType);

    @Query("select t from BpmTask t where t.taskAssignee = :account ")
    List<BpmTask> listTaskAssignee(String account);

    @Query("SELECT new vn.fis.eapprove.business.model.response.ReminderTaskProcessingResponse(bt.id,bp.priorityId, " +
            " CASE" +
            "           WHEN (select max(id) from ChangeAssigneeHistory where procInstId = bp.ticketProcInstId) is not null " +
            "               THEN (select toAssignee from ChangeAssigneeHistory where id = (select max(id) from ChangeAssigneeHistory where procInstId = bp.ticketProcInstId)) " +
            "           ELSE bt.taskAssignee " +
            "   END as assigne , bt.taskDefKey , bp.ticketProcInstId,bp.ticketId,bt.taskCreatedTime,bt.slaFinishTime) " +
            "FROM BpmTask bt JOIN BpmProcInst bp on bt.bpmProcInst.ticketProcInstId = bp.ticketProcInstId " +
            "WHERE UPPER(bp.ticketStatus) IN ('OPENED','PROCESSING') " +
            "AND bt.taskStatus IN ('PROCESSING','ACTIVE') " +
            "AND bp.priorityId IS NOT NULL " +
            "AND bt.slaFinishTime IS NOT NULL " +
            "AND bt.slaFinishTime > :date")
    List<ReminderTaskProcessingResponse> getListReminderTaskProcessing(@Param("date") LocalDateTime date);

//    @Query("Select count(t) from BpmTask t where t.taskAssignee = : ")
//    List<Integer> countBpmProinstTask(String username);

    int countByTaskAssigneeAndTaskType(String username, String type);

    @Query(nativeQuery = true, value = "select * from bpm_task bt where bt.created_time between STR_TO_DATE(:fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(:toDate, '%Y-%m-%d %H:%i:%s')")
    List<BpmTask> getTaskByDate(@Param("fromDate") String fromDate, @Param("toDate") String toDate);

    @Query(nativeQuery = true, value = "select * from bpm_task bt where bt.proc_inst_id = :procInstId and bt.status in :status")
    List<BpmTask> findBpmTaskReturnByProcInstIdAndStatus(@Param("procInstId") String procInstId,
                                                       @Param("status") List<String> status);

    @Query(nativeQuery = true, value = "select * from bpm_task bt where bt.proc_inst_id = :procInstId")
    List<BpmTask> findBpmTaskByProcInstId(@Param("procInstId") String procInstId);

    @Query(nativeQuery = true, value = "select * from bpm_task bt where bt.proc_inst_id = :procInstId and bt.status in  ('ACTIVE', 'PROCESSING')")
    List<BpmTask> findBpmTaskActiveByProcInstId(@Param("procInstId") String procInstId);

    @Query(value = "select task.id from bpm_task task " +
            "left join change_assignee_history ca on ca.task_id = task.task_id and task.assignee = ca.to_assignee " +
            "where (" +
            "  (task.assignee in (:users) and task.assign_type is null) " +
            " or (task.assign_type is true and ca.org_assignee in (:users)) " +
            " or task.action_user in (:users) " +
            ") ", nativeQuery = true)
    List<Long> getListTaskByListUser(List<String> users);

    @Query("select bt.id as id, bt.taskDefKey as taskDefKey, bt.taskStatus as taskStatus from BpmTask bt where bt.taskProcInstId = :procInstId")
    List<Tuple> getTaskInfoByProcInstId(String procInstId);
}
