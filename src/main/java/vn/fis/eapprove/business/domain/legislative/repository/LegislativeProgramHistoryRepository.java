package vn.fis.eapprove.business.domain.legislative.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.legislative.model.entity.LegislativeProgramHistory;

import java.util.List;

@Repository
public interface LegislativeProgramHistoryRepository extends JpaRepository<LegislativeProgramHistory, Long> {

    List<LegislativeProgramHistory> findLegislativeProgramHistoryByLegislativeIdAndVersionIsNotNullOrderById(Long legislativeId);

    @Query("select max(version) from LegislativeProgramHistory where legislativeId = :legislativeId")
    Integer getMaxVersionByLegislativeId(Long legislativeId);
}
