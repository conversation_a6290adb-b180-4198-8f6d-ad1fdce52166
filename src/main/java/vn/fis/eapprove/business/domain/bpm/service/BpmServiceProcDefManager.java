package vn.fis.eapprove.business.domain.bpm.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.domain.bpm.entity.BpmServiceProcDef;
import vn.fis.eapprove.business.domain.bpm.repository.BpmServiceProcDefRepository;


import java.util.List;

@Service("BpmServiceProcDefManagerV1")
public class BpmServiceProcDefManager {

    @Autowired
    private BpmServiceProcDefRepository bpmServiceProcDefRepository;

    public List<BpmServiceProcDef> listByServiceId(List<Long> serviceId) {
        List<BpmServiceProcDef> bpmServiceProcDefList = bpmServiceProcDefRepository.getBpmServiceProcDefsByServiceIdIn(serviceId);
        return bpmServiceProcDefList;
    }

    public List<BpmServiceProcDef> listByprocDefId(Long procDefId) {
        List<BpmServiceProcDef> bpmServiceProcDefList = bpmServiceProcDefRepository.getBpmServiceProcDefByProcDefId(procDefId);
        return bpmServiceProcDefList;
    }
}
