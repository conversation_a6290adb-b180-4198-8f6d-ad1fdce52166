package vn.fis.eapprove.business.domain.assistant.repository;

import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.assistant.entity.Assistant;


import java.util.List;

/**
 * Author: AnhVTN
 * Date: 01/03/2023
 */

@Repository
public interface AssistantRepository extends JpaRepository<Assistant, Long>, JpaSpecificationExecutor<Assistant> {
    List<Assistant> getAssistantByTicketId(String ticketId);

    @Query(value = "select a.ticketId from Assistant a where a.assistantEmail =:email")
    List<String> getListTicketByAssistantEmail(@Param("email") String email);

    @Query(value = "select pb.ticketId " +
            "from BpmProcInst pb " +
            "join BpmShared bs " +
            "on bs.procInstId = pb.ticketProcInstId " +
            "and (bs.type = 'FOLLOWED' and bs.sharedUser = bs.createdUser and bs.createdUser = :email)")
    List<String> getListTicketShareByAssistantEmail(@Param("email") String email);

    @Query(value = "select a.assistantEmail from Assistant a where a.ticketId =:ticketId")
    List<String> getAssistantEmailByTicketId(@Param("ticketId") String ticketId);

    @Query(value = "select distinct a.ticket_id from assistant a " +
            "join bpm_procinst bp on cast(a.ticket_id as char) = bp.id " +
            "join bpm_shared bs on bs.PROC_INST_ID = bp.proc_inst_id " +
            "where a.assistant_email = :email and bs.type='FOLLOWED' " +
            "and bp.status != 'CLOSED' and bp.status != 'CANCEL' ", nativeQuery = true)
    List<String> getListShareTicket(@Param("email") String email);

    List<Assistant> findAllByTicketId(String ticketId);

    int countByAssistantEmail(String username);

    @Query("select distinct a.assistantEmail " +
            "from Assistant a " +
            "join BpmProcInst bp on cast(bp.ticketId as string) = a.ticketId " +
            "where a.assistantEmail in (:accounts) " +
            "and bp.ticketStatus in ('OPENED', 'PROCESSING', 'ADDITIONAL_REQUEST', 'DELETED_BY_RU')")
    List<String> getListAssistantTicket(List<String> accounts);

    List<Assistant> getAssistantByTicketIdAndAssistantEmail(String ticketId, String username);
}
