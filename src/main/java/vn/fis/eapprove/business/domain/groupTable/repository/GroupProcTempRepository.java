package vn.fis.eapprove.business.domain.groupTable.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import vn.fis.eapprove.business.domain.groupTable.entity.GroupTableProTemp;


import java.util.List;

public interface GroupProcTempRepository extends JpaRepository<GroupTableProTemp, Long>, JpaSpecificationExecutor<GroupTableProTemp> {
    List<GroupTableProTemp> findGroupTableProTempByFormKeyIn(List<String> formKey);

    void deleteByProDefId(String proDefId);

    //    List<GroupTableProTemp> findGroupTableProTempByProDefId(String proDefId);
    List<GroupTableProTemp> getGroupTableProTempsByFormKey(String formKey);

    List<GroupTableProTemp> getGroupTableProTempsByFormKeyIn(List<String> formKey);

    List<GroupTableProTemp> getGroupTableProTempsByProDefId(String procDefId);

    List<GroupTableProTemp> findByProDefId(String proDefId);
}
