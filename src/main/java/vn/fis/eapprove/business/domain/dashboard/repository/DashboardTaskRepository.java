package vn.fis.eapprove.business.domain.dashboard.repository;

import jakarta.persistence.Tuple;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.dashboard.entity.DashboardTask;

import java.util.List;
import java.util.Set;

@Repository
public interface DashboardTaskRepository extends JpaRepository<DashboardTask, Long> {

    @Modifying
    @Transactional
    @Query(nativeQuery = true, value = "delete from dashboard_task where task_id in :taskId")
    int deleteByTaskId(@Param("taskId") List<String> taskId);

    DashboardTask getDashboardTaskByTaskId(String taskId);

    @Query(value = "select b.* " +
            "from (SELECT dt.assignee as username, " +
            "             dt.assignee_full_name as fullName," +
            "             dt.assignee_staff_code as staffCode," +
            "             SUM(CASE " +
            "                     WHEN task_status IN ('PROCESSING', 'ACTIVE') " +
            "                         AND current_timestamp <= sla_finish_time " +
            "                         THEN 1 " +
            "                     ELSE 0 " +
            "                 END) AS approvingNotDelayed, " +
            "             SUM(CASE " +
            "                     WHEN task_status IN ('PROCESSING', 'ACTIVE') " +
            "                         AND current_timestamp > sla_finish_time " +
            "                         THEN 1 " +
            "                     ELSE 0 " +
            "                 END) AS approvingDelayed " +
            " " +
            "      FROM dashboard_task dt " +
            "      WHERE 1 = 1" +
            "      AND dt.assignee_chart_id = :chartId " +
            "      AND dt.assignee_chart_node_id in (:chartNodeIds) " +
            "      AND dt.assignee in (:usernames) " +
            "      AND dt.created_time > :fromDate " +
            "      AND (:toDate is null or dt.created_time < :toDate) " +
            "      AND (:taskType is null or :taskType = '-1' or dt.task_type = :taskType) " +
            "      group by dt.assignee," +
            "               dt.assignee_full_name," +
            "               dt.assignee_staff_code" +
            ") as b " +
            "WHERE b.approvingNotDelayed > 0 " +
            "   or b.approvingDelayed > 0", nativeQuery = true)
    List<Tuple> getChartBarEmployee(@Param("chartId") Long chartId,
                                    @Param("chartNodeIds") List<Long> chartNodeIds,
                                    @Param("usernames") Set<String> usernames,
                                    @Param("fromDate") String fromDate,
                                    @Param("toDate") String toDate,
                                    @Param("taskType") String taskType
    );

    @Query(value = "select priority_id, priority_name, count(priority_id) as count   " +
            "from dashboard_task   " +
            "where 1 = 1   " +
            "  and assignee = :username   " +
            "  and created_time >= :fromDate   " +
            "  and (:toDate is null or created_time < :toDate)   " +
            "  and (:taskType is null or :taskType = '-1' or task_type = :taskType)   " +
            "group by priority_id, priority_name", nativeQuery = true)
    List<Tuple> getChartPiePriority(@Param("username") String username,
                                    @Param("fromDate") String fromDate,
                                    @Param("toDate") String toDate,
                                    @Param("taskType") String taskType);

    @Query(value = "select b.* " +
            "from (SELECT dt.assignee as username, " +
            "             dt.assignee_full_name as fullName," +
            "             dt.assignee_staff_code as staffCode," +
            "             SUM(CASE " +
            "                     WHEN task_status IN ('PROCESSING', 'ACTIVE') " +
            "                         THEN 1 " +
            "                     ELSE 0 " +
            "                 END) AS processing, " +
            "             SUM(CASE " +
            "                     WHEN task_status NOT IN ('PROCESSING', 'ACTIVE') " +
            "                         THEN 1 " +
            "                     ELSE 0 " +
            "                 END) AS otherStatus " +
            " " +
            "      FROM dashboard_task dt " +
            "      WHERE 1 = 1" +
            "      AND dt.assignee_chart_id = :chartId " +
            "      AND dt.assignee_chart_node_id in (:chartNodeIds) " +
            "      AND dt.assignee in (:usernames) " +
            "      AND dt.created_time > :fromDate " +
            "      AND (:toDate is null or dt.created_time < :toDate) " +
            "      AND (:taskType is null or :taskType = '-1' or dt.task_type = :taskType)   " +
            "      group by dt.assignee," +
            "               dt.assignee_full_name," +
            "               dt.assignee_staff_code" +
            ") as b " +
            "WHERE b.processing > 0 " +
            "   or b.otherStatus > 0", nativeQuery = true)
    List<Tuple> getTableEmployeeWorkload(@Param("chartId") Long chartId,
                                         @Param("chartNodeIds") List<Long> chartNodeIds,
                                         @Param("usernames") Set<String> usernames,
                                         @Param("fromDate") String fromDate,
                                         @Param("toDate") String toDate,
                                         @Param("taskType") String taskType
    );

    @Query(value = "select b.* " +
            "from (SELECT dt.service_id as serviceId, " +
            "             dt.service_name as serviceName," +
            "             SUM(CASE " +
            "                     WHEN task_status IN (:taskStatus) " +
            "                         THEN 1 " +
            "                     ELSE 0 " +
            "                 END) AS processing, " +
            "             SUM(CASE " +
            "                     WHEN task_status NOT IN (:taskStatus) " +
            "                         THEN 1 " +
            "                     ELSE 0 " +
            "                 END) AS otherStatus " +
            " " +
            "      FROM dashboard_task dt " +
            "      WHERE 1 = 1" +
            "      AND dt.assignee_chart_id = :chartId " +
            "      AND dt.assignee_chart_node_id in (:chartNodeIds) " +
            "      AND dt.assignee in (:usernames) " +
            "      AND dt.created_time > :fromDate " +
            "      AND (:toDate is null or dt.created_time < :toDate) " +
            "      AND (:taskType is null or :taskType = '-1' or dt.task_type = :taskType) " +
            "      group by dt.service_id," +
            "               dt.service_name" +
            ") as b " +
            "WHERE b.processing > 0 " +
            "   or b.otherStatus > 0", nativeQuery = true)
    List<Tuple> getTableProposalStatus(@Param("chartId") Long chartId,
                                       @Param("chartNodeIds") List<Long> chartNodeIds,
                                       @Param("usernames") Set<String> usernames,
                                       @Param("fromDate") String fromDate,
                                       @Param("toDate") String toDate,
                                       @Param("taskStatus") List<String> taskStatus,
                                       @Param("taskType") String taskType
                                       );
}
