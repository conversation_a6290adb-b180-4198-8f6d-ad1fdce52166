package vn.fis.eapprove.business.domain.manageApi.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.manageApi.entity.ManageShareTicket;


import java.time.LocalDate;
import java.util.List;

@Repository
public interface ManageShareTicketRepository extends JpaRepository<ManageShareTicket, Long>, JpaSpecificationExecutor<ManageShareTicket> {
    ManageShareTicket getManageShareTicketById(Long id);

    ManageShareTicket getManageShareTicketByName(String name);
    ManageShareTicket getManageShareTicketByNameAndStatus(String name, String status);

    @Query("select distinct st from ManageShareTicket st " +
            "                  join ManageShareTicketDetail sd1 on sd1.manageShareTicketId = st.id " +
            "                  join ManageShareTicketDetail sd2 on sd2.manageShareTicketId = st.id " +
            "where sd1.type = 'service' and sd1.value in (:listService) " +
            " and sd2.type = 'shareUser' and sd2.value in (:listShareUser) " +
            " and st.status = 'active' " +
            " and (:manageShareTicketId is null or st.id <> :manageShareTicketId) " +
            " and ( " +
            "    ( :toDate is null and (st.toDate is null or :fromDate < st.toDate) ) " +
            "    or ( st.toDate is null and st.fromDate < :toDate ) " +
            "    or ( :fromDate between st.fromDate and st.toDate ) " +
            "    or ( :toDate between st.fromDate and st.toDate ) " +
            " )")
    List<ManageShareTicket> validateServiceAndDateAndShareUser(List<String> listService, LocalDate fromDate, LocalDate toDate, Long manageShareTicketId, List<String> listShareUser);

    @Query("select st from ManageShareTicket st " +
            " join ManageShareTicketDetail sd1 on sd1.manageShareTicketId = st.id " +
//            " join ManageShareTicketDetail sd2 on sd2.manageShareTicketId = st.id " +
            " where st.status = 'active' " +
            " and sd1.type = 'service' and sd1.value = :serviceId " +
//            " and sd2.type = 'companyCode' and sd2.value = :companyCode " +
            " and st.fromDate <= :createdDate " +
            " and (st.toDate is null or st.toDate >= :createdDate)")
    List<ManageShareTicket> getByServiceIdAndCompanyCode(String serviceId, LocalDate createdDate);
}
