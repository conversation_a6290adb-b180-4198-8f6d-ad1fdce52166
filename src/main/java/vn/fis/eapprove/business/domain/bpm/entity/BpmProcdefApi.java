package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "bpm_procdef_api")
public class BpmProcdefApi {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "proc_def_id")
    private String procDefId;

    @Column(name = "task_def_key", nullable = false)
    private String taskDefKey;

    @Column(name = "action_id")
    private Long actionId;

    @Column(name = "api_id")
    private Long apiId;

    @Column(name = "header")
    private String header;

    @Column(name = "body")
    private String body;

    @Column(name = "response")
    private String response;

    @Column(name = "continue_on_error")
    private Integer continueOnError;

    @Column(name = "status")
    private Long status;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "updated_user")
    private String updatedUser;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "bpm_procdef_id")
    private Long bpmProcdefId;

    @Column(name = "call_order")
    private String callOrder;

    @Column(name = "success_condition")
    private String successCondition;

    @Column(name = "call_condition")
    private String callCondition;

    @Column(name = "description")
    private String description;
}
