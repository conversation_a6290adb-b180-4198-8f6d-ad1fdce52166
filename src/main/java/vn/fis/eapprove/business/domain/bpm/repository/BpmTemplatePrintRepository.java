package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrint;
import vn.fis.eapprove.business.model.response.ChildResponse;
import vn.fis.eapprove.business.model.response.TemplatePrintApplyResponse;


import jakarta.persistence.Tuple;
import java.util.List;

@Repository
public interface BpmTemplatePrintRepository extends JpaRepository<BpmTemplatePrint, Long>, JpaSpecificationExecutor<BpmTemplatePrint> {

    @Query(value = "SELECT bpm FROM BpmTemplatePrint as bpm WHERE bpm.isDeleted = false AND bpm.id = :id ")
    BpmTemplatePrint getById(@Param("id") Long id);

    @Query(value = "SELECT bpm FROM BpmTemplatePrint as bpm WHERE bpm.isDeleted = false AND lower(bpm.name) LIKE lower(concat('%',:name,'%'))")
    List<BpmTemplatePrint> getByName(@Param("name") String name);

    @Query(value = "SELECT bpm FROM BpmTemplatePrint as bpm WHERE bpm.isDeleted = false AND bpm.name = :name")
    List<BpmTemplatePrint> getByNameExist(@Param("name") String name);

    @Query(value = "SELECT bpm FROM BpmTemplatePrint as bpm WHERE bpm.createdDate is not null AND bpm.name = :name and bpm.printType = 0 and bpm.isDeleted = false")
    BpmTemplatePrint getByName1(String name);

    @Query(value = "SELECT bpm FROM BpmTemplatePrint as bpm WHERE bpm.name = :name ")
    BpmTemplatePrint getBpmByName(String name);

    @Query(value = "SELECT bpm FROM BpmTemplatePrint as bpm WHERE bpm.name = :name and bpm.printType = 0 and bpm.isDeleted = false ")
    BpmTemplatePrint getBpmTemplatePrintByName(String name);

    @Query(value = "SELECT distinct bpm2 FROM BpmTpSignZone as bpm " +
            "JOIN BpmTemplatePrint bpm2 ON bpm2.id = bpm.bpmTemplatePrintId " +
            "WHERE bpm.procInstId = :procInstId and bpm2.procDefId = :procDefId and bpm.taskDefKey in :taskKey")
    List<BpmTemplatePrint> getApprovalPrintPhase(@Param("procDefId") String procDefId, @Param("procInstId") String procInstId, @Param("taskKey") List<String> taskKey);

    @Query(value = "SELECT distinct bpm2 FROM BpmTpTask as bpm " +
            "JOIN BpmTemplatePrint bpm2 ON bpm2.id = bpm.bpmTemplatePrintId " +
            "WHERE bpm.procDefId = :procDefId and bpm.taskDefKey in :taskKey")
    List<BpmTemplatePrint> getExecutionPrintPhase(@Param("procDefId") String procDefId, @Param("taskKey") List<String> taskKey);

    @Query(value = "select bpmtemp.id as id," +
            "       bpmtemp.name as name," +
            "       bpmtemp.processName as processName," +
            "       bpmtemp.content as content," +
            "       bpmtemp.pdfContent as pdfContent," +
            "       bpmtemp.printType as printType," +
            "       bpmtask.status as status," +
            "       cf.id as cf_id," +
            "       cf.conditionText as conditionText," +
            "       cf.uploadWords as uploadWords," +
            "       cf.uploadWordsChange as uploadWordsChange," +
            "       cf.htmlId as htmlId" +
            " from BpmTpTask bpmtask" +
            "         join BpmTemplatePrint bpmtemp" +
            "              on bpmtask.bpmTemplatePrintId = bpmtemp.id and bpmtemp.isDeleted = false" +
            "         left join FileCondition cf on bpmtask.bpmTemplatePrintId = cf.bpmTemplatePrintId" +
            " where bpmtask.procDefId = :procDefId" +
            "  and bpmtask.taskDefKey in (:taskKey)")
    List<Tuple> getExecutionPrintPhaseNew(@Param("procDefId") String procDefId, @Param("taskKey") List<String> taskKey);

    @Query(value = "SELECT distinct bpm FROM BpmTemplatePrint as bpm " +
            "JOIN BpmTpTask bpm3 ON bpm.id = bpm3.bpmTemplatePrintId " +
            "WHERE bpm.procDefId = :procDefId and bpm3.taskDefKey in :taskKey")
    List<BpmTemplatePrint> getSignZonePrint(@Param("procDefId") String procDefId, @Param("taskKey") List<String> taskKey);

    @Query(value = "SELECT distinct bpm FROM BpmTemplatePrint as bpm where bpm.isDeleted= false and bpm.printType = 0")
    List<BpmTemplatePrint> listProcessUsed();

    @Query(value = "SELECT bpm FROM BpmTemplatePrint as bpm " +
            "WHERE bpm.createdDate is not null " +
            "AND bpm.procDefId = :procDefId " +
            "AND bpm.printType = 0 AND bpm.isDeleted = false")
    List<BpmTemplatePrint> getByProcDefId(String procDefId);

    BpmTemplatePrint findBpmTemplatePrintBySpecialParentIdAndSpecialCompanyCodeAndSpecialParentServiceId(Long parentId, String companyCode, Long parentServiceId);

    @Query("SELECT new vn.fis.eapprove.business.model.response.ChildResponse(t.specialParentId,t.id,t.specialCompanyCode,'','') FROM BpmTemplatePrint t " +
            "where t.specialParentId in (:parentIds) " +
            "AND t.specialFlow = true")
    List<ChildResponse> findSpecialFlowChildDataByParentIds(List<Long> parentIds);

    BpmTemplatePrint getBpmTemplatePrintById(Long id);

    @Query("select btp from BpmTemplatePrint btp where btp.id <> :id and btp.name = :name  and btp.printType = 0 and btp.isDeleted = false")
    List<BpmTemplatePrint> checkExistByName(@Param("id") Long id, @Param("name") String name);

    @Query("select new vn.fis.eapprove.business.model.response.TemplatePrintApplyResponse(bp.procDefId,btp.name) " +
            "from BpmTemplatePrint btp left join BpmProcdef bp on bp.procDefId = btp.procDefId" +
            " where bp.id in (:ids) and btp.printType = 0 " )
    List<TemplatePrintApplyResponse> getTemplatePrintApplyProcdef(List<Long> ids);

    @Query("select btp.procDefId from BpmTemplatePrint btp where btp.printType = :printType and btp.procDefId <> :procDefId and btp.isDeleted = false")
    List<String> getProcDefIdByPrintType(@Param("printType") Integer printType, @Param("procDefId") String procDefId);
}
