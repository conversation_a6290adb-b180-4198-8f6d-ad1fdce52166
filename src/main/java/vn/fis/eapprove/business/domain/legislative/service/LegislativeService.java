package vn.fis.eapprove.business.domain.legislative.service;

import vn.fis.eapprove.business.domain.legislative.model.entity.LegislativeProgramHistory;
import vn.fis.eapprove.business.domain.legislative.model.request.LegislativeDashboardRequest;
import vn.fis.eapprove.business.domain.legislative.model.request.ReportRequest;
import vn.fis.eapprove.business.domain.legislative.model.response.LegislativePercentChartResponse;
import vn.fis.eapprove.business.domain.legislative.model.response.RankTicketResponse;
import vn.fis.eapprove.business.domain.legislative.model.response.ReportResponse;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.domain.legislative.model.mapper.LegislativeCreateModel;
import vn.fis.eapprove.business.domain.legislative.model.request.LegislativeSearchRequest;
import vn.fis.eapprove.business.domain.legislative.model.request.LegislativeTicketRequest;
import vn.fis.eapprove.business.model.response.InitialLegislativeResponse;
import vn.fis.eapprove.business.domain.legislative.model.response.LegislativeSearchResponse;

import java.util.List;


public interface LegislativeService {
    Long createUpdate(LegislativeCreateModel request);
    String updateStatus(Long id, String status);
    void cancel(Long id);
    LegislativeCreateModel getById(Long id);
    PageDto search(LegislativeSearchRequest request);
    List<LegislativeSearchResponse> searchFilter(LegislativeSearchRequest request);
    List<LegislativeProgramHistory> getHistory(Long legislativeId);
    List<LegislativeSearchResponse> getLegislativeActive(Boolean isLoadAll);

    void updateActionApi(LegislativeTicketRequest request);

    InitialLegislativeResponse loadInit();

    // dashboard
    List<LegislativePercentChartResponse> getPercentChart(LegislativeDashboardRequest request);
    List<RankTicketResponse> getRankTickets(LegislativeDashboardRequest request);
    PageDto getReport(ReportRequest request);
    List<ReportResponse> getFilterReport(ReportRequest request);
}
