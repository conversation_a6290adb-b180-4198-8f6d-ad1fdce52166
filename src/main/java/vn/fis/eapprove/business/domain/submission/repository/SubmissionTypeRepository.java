package vn.fis.eapprove.business.domain.submission.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.submission.entity.SubmissionType;
import vn.fis.eapprove.business.model.response.ServiceApplyResponse;


import java.util.List;
import java.util.Optional;

@Repository
public interface SubmissionTypeRepository extends JpaRepository<SubmissionType, Long>, JpaSpecificationExecutor<SubmissionType> {
    @Query("SELECT c FROM SubmissionType c WHERE LOWER(c.typeName) = LOWER(:nameSubmissionType) AND UPPER(c.typeName) = UPPER(:nameSubmissionType) AND c.createdDate is not null")
    List<SubmissionType> CheckExistName(String nameSubmissionType);

    Optional<SubmissionType> findTopByTypeNameLikeOrderByIdDesc(String submissionTypeName);

    SubmissionType getSubmissionTypeById(Long id);

    @Query("SELECT c.typeName from SubmissionType c where c.status = 1")
    List<String> getAllSubmissionTypeName();

    @Query("SELECT new vn.fis.eapprove.business.model.response.ServiceApplyResponse(s.id,s.serviceName) " +
            "from ServicePackage s join SubmissionType st on s.submissionType = st.id " +
            "where st.id in (:ids)")
    List<ServiceApplyResponse> getAllServiceApply(List<Long> ids);

}
