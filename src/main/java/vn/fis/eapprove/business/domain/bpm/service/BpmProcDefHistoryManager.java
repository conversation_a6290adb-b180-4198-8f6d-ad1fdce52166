package vn.fis.eapprove.business.domain.bpm.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import vn.fis.eapprove.business.config.GsonAdapterConfig;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.api.entity.ApiManagement;
import vn.fis.eapprove.business.domain.api.repository.ApiManagementRepository;
import vn.fis.eapprove.business.domain.bpm.entity.*;
import vn.fis.eapprove.business.domain.bpm.repository.*;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.repository.ShareUserRepository;
import vn.fis.eapprove.business.dto.BpmOwnerDto;
import vn.fis.eapprove.business.dto.BpmProcdefApiDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.ProcDefHistoryRequest;
import vn.fis.eapprove.business.model.response.BpmProcDefHistoryResponse;
import vn.fis.eapprove.business.model.response.ProcDefHistoryDetailResponse;
import vn.fis.eapprove.business.specification.BpmProcDefHistorySpecification;

import vn.fis.eapprove.business.tenant.manager.CamundaEngineService;

import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ShareUserTypeEnum;
import vn.fis.spro.common.model.response.ChartInfoRoleResponse;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service("BpmProcDefHistoryManagerV1")
@Transactional
public class BpmProcDefHistoryManager {

    @Autowired
    private BpmProcDefHistorySpecification bpmProcDefHistorySpecification;
    @Autowired
    private Common common;
    @Autowired
    private BpmProcDefHistoryRepository bpmProcDefHistoryRepository;
    @Autowired
    private CamundaEngineService camundaEngineService;
    @Autowired
    private BpmOwnerProcessRepository bpmOwnerProcessRepository;
    @Autowired
    private ResponseUtils responseUtils;
    @Autowired
    private BpmProcdefInheritsRepository bpmProcdefInheritsRepository;
    @Autowired
    private BpmProcdefNotificationRepository bpmProcdefNotificationRepository;
    @Autowired
    private BpmProcdefApiRepository bpmProcdefApiRepository;
    @Autowired
    private ApiManagementRepository apiManagementRepository;
    @Autowired
    private ShareUserRepository shareUserRepository;
    @Autowired
    private PermissionDataManagementRepository permissionDataManagementRepository;
    @Autowired
    private BpmProcdefViewFileApiRepository bpmProcdefViewFileApiRepository;
    @Autowired
    private BpmProcdefRepository bpmProcdefRepository;
    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private BpmTemplatePrintRepository bpmTemplatePrintRepository;
    @Autowired
    private BpmTpTaskRepository bpmTpTaskRepository;
    @Autowired
    private BpmOwnerProcessManager bpmOwnerProcessManager;

    public BpmProcDefHistoryManager(BpmProcDefHistorySpecification bpmProcDefHistorySpecification) {
        this.bpmProcDefHistorySpecification = bpmProcDefHistorySpecification;
    }

    public PageDto getAll(ProcDefHistoryRequest request) {

        Map<String, Object> mapData = bpmProcDefHistorySpecification.getAllHistory(request);
        List<BpmProcDefHistoryResponse> result = (List<BpmProcDefHistoryResponse>) mapData.get("data");
        Long totalItems = (Long) mapData.get("count");
        Integer totalPage = common.getPageCount(totalItems, request.getLimit());

        return PageDto.builder()
                .content(result)
                .number(request.getPage())
                .numberOfElements(request.getLimit())
                .page(request.getPage())
                .size(request.getLimit())
                .totalPages(totalPage)
                .totalElements(totalItems)
                .build();
    }

    public List<BpmProcDefHistoryResponse> getAllFilter(ProcDefHistoryRequest request) {
        return bpmProcDefHistorySpecification.getAllFilter(request);
    }

    public ProcDefHistoryDetailResponse getDetailHistory(Long id) {
        ProcDefHistoryDetailResponse response = new ProcDefHistoryDetailResponse();

        BpmProcDefHistory data = bpmProcDefHistoryRepository.getBpmProcDefHistoryById(id);
        if (data == null) {
            throw new RuntimeException("Không tìm thấy bản ghi lịch sử.");
        }
        Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();

        List<String> cancelTasks = ObjectUtils.toObject(data.getCancelTasks(), new TypeReference<>() {
        });
        List<String> hideInfoTasks = ObjectUtils.toObject(data.getHideInfoTasks(), new TypeReference<>() {
        });
        List<String> showInfoTasks = ObjectUtils.toObject(data.getShowInfoTaks(), new TypeReference<>() {
        });
        List<String> changeImplementValue = ObjectUtils.toObject(data.getChangeImplementerValue(), new TypeReference<>() {
        });
        List<String> showInputTaskDefKeys = ObjectUtils.toObject(data.getShowInputTaskDefKeys(), new TypeReference<>() {
        });
        List<String> hideRuTasks = ObjectUtils.toObject(data.getHideRuTasks(), new TypeReference<>() {
        });
        List<String> hideInheritTasks = ObjectUtils.toObject(data.getHideInheritTasks(), new TypeReference<>() {
        });
        List<String> hideCommentTasks = ObjectUtils.toObject(data.getHideCommentTasks(), new TypeReference<>() {
        });
        List<String> hideDownloadTasks = ObjectUtils.toObject(data.getHideDownloadTasks(), new TypeReference<>() {
        });
        List<Integer> listHideTicketValue = ObjectUtils.toObject(data.getHideRelatedTicketValue(), new TypeReference<>() {
        });
        List<Integer> listAuthorityOnTicket = ObjectUtils.toObject(data.getAuthorityOnTicketValue(), new TypeReference<>() {
        });
        List<String> listAuthorityOnTicketStep = ObjectUtils.toObject(data.getAuthorityOnTicketStep(), new TypeReference<>() {
        });

        response.setId(data.getId());
        response.setProcDefId(data.getProcDefId());
        response.setName(data.getName());
        response.setDescription(data.getDescription());
        response.setKey(data.getKey());
        response.setDeploymentId(data.getDeploymentId());
        response.setResourceName(data.getResourceName());
        response.setBytes(data.getBytes());
        response.setFileXml(camundaEngineService.getXML(data.getProcDefId()));
        response.setPrioritized(data.getPrioritized());
        response.setUserCreated(data.getUserCreated());
        response.setStatus(data.getStatus());

        //owner
        List<BpmOwnerProcess> bpmOwnerProcessList = g.fromJson(data.getProcessOwner(), new TypeReference<List<BpmOwnerProcess>>(){}.getType());
        List<String> usernames = bpmOwnerProcessList.stream().map(BpmOwnerProcess::getIdUser).collect(Collectors.toList());
        List<BpmOwnerDto> ownerDtos = new ArrayList<>();
        // get user information from customer-service: /chart/getUserByEmail
        if (!ValidationUtils.isNullOrEmpty(usernames)) {
            List<ChartInfoRoleResponse> chartInfoRoleResponses = responseUtils.getUserByEmail(usernames);
            if (!ValidationUtils.isNullOrEmpty(chartInfoRoleResponses)) {
                chartInfoRoleResponses.forEach(e -> {
                    BpmOwnerDto bpmOwnerDto = new BpmOwnerDto(e.getId(), e.getUsername());
                    ownerDtos.add(bpmOwnerDto);
                });
            }
        }
        response.setOwners(ownerDtos);
        response.setAutoClose(data.getAutoClose() != null ? String.valueOf(data.getAutoClose()) : "");
        response.setAutoCancel(data.getAutoCancel() != null ? String.valueOf(data.getAutoCancel()) : "");
        response.setStepByStepResultForCreate(data.getStepByStepResultForCreate());
        response.setInFormTo(data.getInFormTo());
        response.setLocation(data.getLocation());
        response.setUpdate(data.getUpdate());
        response.setRequestUpdate(data.getRequestUpdate());
        response.setCreateNewAndDouble(data.getCreateNewAndDouble());
        response.setAutoInherits(data.getAutoInherits());
        response.setBpmProcdefInherits(bpmProcdefInheritsRepository.getBpmProcdefInheritsInfoHistory(data.getProcDefId()));
        response.setBpmProcdefNotifications(bpmProcdefNotificationRepository.findBpmProcdefNotificationsByProcDefIdAndStatus(data.getProcDefId(),"1"));
        List<BpmProcdefApi> bpmProcdefApis = bpmProcdefApiRepository.findBpmProcdefApisByProcDefId(data.getProcDefId());
        List<BpmProcdefApiDto> bpmProcDefDetailDTOList = new ArrayList<>();
        for (BpmProcdefApi bpmProcdefApi : bpmProcdefApis) {
            ApiManagement apiManagement = apiManagementRepository.getApiManagementById(bpmProcdefApi.getApiId());
            BpmProcdefApiDto apiDto = new BpmProcdefApiDto(bpmProcdefApi.getId(), bpmProcdefApi.getProcDefId(), bpmProcdefApi.getTaskDefKey(), bpmProcdefApi.getActionId(), bpmProcdefApi.getApiId(), bpmProcdefApi.getHeader(), bpmProcdefApi.getBody(), bpmProcdefApi.getStatus(), bpmProcdefApi.getCreatedUser(), bpmProcdefApi.getCreatedTime(), bpmProcdefApi.getUpdatedUser(), bpmProcdefApi.getUpdatedTime(),
                    apiManagement.getUrl(), apiManagement.getMethod(), bpmProcdefApi.getCallOrder(), bpmProcdefApi.getSuccessCondition(), bpmProcdefApi.getResponse(), bpmProcdefApi.getContinueOnError(), bpmProcdefApi.getCallCondition(),false, bpmProcdefApi.getDescription());
            bpmProcDefDetailDTOList.add(apiDto);
        }
        response.setBpmProcdefApiDtos(bpmProcDefDetailDTOList);
        response.setPriorityId(data.getPriorityId());
        response.setCancel(data.getCancel());
        response.setIsAssistant(data.getIsAssistant());
        response.setIsEditAssistant(data.getIsEditAssistant());
        response.setIsAutoCancel(data.getIsAutoCancel());
        response.setHideInfo(data.getHideInfo());
        response.setShowInfo(data.getShowInfo());
        response.setAdditionalRequest(data.getAdditionalRequest());
        response.setCancelTasks(data.getCancelTasks());

        response.setListCancelTasks(cancelTasks);
        response.setListShowInfoTasks(showInfoTasks);
        response.setListHideInfoTasks(hideInfoTasks);

        List<SharedUser> sharedUsers = g.fromJson(data.getShareWith(), new TypeReference<List<SharedUser>>(){}.getType());
        List<String> listEmail = new ArrayList<>();
        if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
            listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(data.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
        }
        response.setShareWith(listEmail);
        response.setListHideRelatedTicketValue(listHideTicketValue);
        response.setHideRelatedTicket(data.getHideRelatedTicket());
        response.setListChangeImplementerValue(changeImplementValue);
        response.setListAuthorityOnTicketValue(listAuthorityOnTicket);
        response.setAuthorityOnTicket(data.getAuthorityOnTicket());
        response.setListAuthorityOnTicketStep(listAuthorityOnTicketStep);
        response.setOffNotification(data.getOffNotification());
        List<PermissionDataManagement> permissionDataManagements = g.fromJson(data.getApplyFor(), new TypeReference<List<PermissionDataManagement>>(){}.getType());
        if (!ValidationUtils.isNullOrEmpty(permissionDataManagements)) {
            List<String> companyCodes = permissionDataManagements.stream().map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
            response.setApplyFor(companyCodes);
        }
        response.setRecall(data.getRecall());
        response.setShowInputTask(data.getShowInputTask());
        response.setLstShowInputTaskDefKeys(showInputTaskDefKeys);
        response.setListHideRuTasks(hideRuTasks);
        response.setHideInherit(data.getHideInherit());
        response.setListHideInheritTasks(hideInheritTasks);
        response.setHideComment(data.getHideComment());
        response.setListHideCommentTasks(hideCommentTasks);
        response.setHideDownload(data.getHideDownload());
        response.setListHideDownloadTasks(hideDownloadTasks);
        response.setHideShareTicket(data.getHideShareTicket());
        List<BpmProcdefViewFileApi> bpmProcdefViewFileApis = bpmProcdefViewFileApiRepository.getAllByBpmProcdefIdAndProcDefId(data.getOrgProcessId(), data.getProcDefId());
        response.setBpmProcdefViewFileApis(bpmProcdefViewFileApis);
        response.setAutoCompleteTask(data.getAutoCompleteTask());
        response.setDisableApprovedTicket(data.getDisableApprovedTicket());
        response.setListDisabledApprovedTicketTasks(data.getDisabledApprovedTicketTasks());
        response.setWarningApprovedTicket(data.getWarningApprovedTicket());
        response.setListWarningApprovedTicketTasks(data.getWarningApprovedTicketTasks());

        return response;
    }

    public void restoreHistory(Long id, String username) {
        BpmProcDefHistory history = bpmProcDefHistoryRepository.getBpmProcDefHistoryById(id);
        if (history == null) {
            throw new RuntimeException("Bản ghi lịch sử không tồn tại.");
        }
        BpmProcdef bpmProcDef = bpmProcdefRepository.getBpmProcDefById(history.getOrgProcessId());
        if (bpmProcDef == null) {
            throw new RuntimeException("Quy trình không tồn tại.");
        }
        String oldProcDefId = bpmProcDef.getProcDefId();
        // check exist name
        List<BpmProcdef> listExist = bpmProcdefRepository.checkExistByName(history.getOrgProcessId(), history.getName());
        if (!ValidationUtils.isNullOrEmpty(listExist)) {
            throw new RuntimeException("Tên bản ghi khôi phục trùng với bản ghi đã tồn tại.");
        }

        // save procDef
        bpmProcDef.setProcDefId(history.getProcDefId());
        bpmProcDef.setName(history.getName());
        bpmProcDef.setProcessType(history.getProcessType());
        bpmProcDef.setDescription(history.getDescription());
        bpmProcDef.setKey(history.getKey());
        bpmProcDef.setDeploymentId(history.getDeploymentId());
        bpmProcDef.setResourceName(history.getResourceName());
        bpmProcDef.setBytes(history.getBytes());
        bpmProcDef.setPrioritized(history.getPrioritized());
        bpmProcDef.setUserUpdate(username);
        bpmProcDef.setUpdatedDate(LocalDateTime.now());
        bpmProcDef.setAutoClose(history.getAutoClose());
        bpmProcDef.setAutoCancel(history.getAutoCancel());
        bpmProcDef.setStatus(history.getStatus());
        bpmProcDef.setStepByStepResultForCreate(history.getStepByStepResultForCreate());
        bpmProcDef.setInFormTo(history.getInFormTo());
        bpmProcDef.setLocation(history.getLocation());
        bpmProcDef.setUpdate(history.getUpdate());
        bpmProcDef.setRequestUpdate(history.getRequestUpdate());
        bpmProcDef.setHideRuTasks(history.getHideRuTasks());
        bpmProcDef.setCreateNewAndDouble(history.getCreateNewAndDouble());
        bpmProcDef.setAutoInherits(history.getAutoInherits());
        bpmProcDef.setOffNotification(history.getOffNotification());
        bpmProcDef.setParentsId(history.getParentsId());
        bpmProcDef.setServiceCount(history.getServiceCount());
        bpmProcDef.setPriorityId(history.getPriorityId());
        bpmProcDef.setCancel(history.getCancel());
        bpmProcDef.setShowInfo(history.getShowInfo());
        bpmProcDef.setHideInfo(history.getHideInfo());
        bpmProcDef.setIsAssistant(history.getIsAssistant());
        bpmProcDef.setIsEditAssistant(history.getIsEditAssistant());
        bpmProcDef.setHideInfoTasks(history.getHideInfoTasks());
        bpmProcDef.setShowInfoTaks(history.getShowInfoTaks());
        bpmProcDef.setIsAutoCancel(history.getIsAutoCancel());
        bpmProcDef.setAdditionalRequest(history.getAdditionalRequest());
        bpmProcDef.setCancelTasks(history.getCancelTasks());
        bpmProcDef.setHideRelatedTicket(history.getHideRelatedTicket());
        bpmProcDef.setHideRelatedTicketValue(history.getHideRelatedTicketValue());
        bpmProcDef.setAuthorityOnTicket(history.getAuthorityOnTicket());
        bpmProcDef.setAuthorityOnTicketValue(history.getAuthorityOnTicketValue());
        bpmProcDef.setAuthorityOnTicketStep(history.getAuthorityOnTicketStep());
        bpmProcDef.setChangeImplementerValue(history.getChangeImplementerValue());
        bpmProcDef.setRecall(history.getRecall());
        bpmProcDef.setShowInputTask(history.getShowInputTask());
        bpmProcDef.setShowInputTaskDefKeys(history.getShowInputTaskDefKeys());
        bpmProcDef.setHideInherit(history.getHideInherit());
        bpmProcDef.setHideInheritTasks(history.getHideInheritTasks());
        bpmProcDef.setHideComment(history.getHideComment());
        bpmProcDef.setHideCommentTasks(history.getHideCommentTasks());
//        bpmProcDef.setSpecialFlow(history.getSpecialFlow());
//        bpmProcDef.setSpecialParentId(history.getSpecialParentId());
//        bpmProcDef.setSpecialCompanyCode(history.getSpecialCompanyCode());
//        bpmProcDef.setSpecialFormKey(history.getSpecialFormKey());
//        bpmProcDef.setSpecialParentServiceId(history.getSpecialParentServiceId());
        bpmProcDef.setHideDownload(history.getHideDownload());
        bpmProcDef.setHideDownloadTasks(history.getHideDownloadTasks());
        bpmProcDef.setHideShareTicket(history.getHideShareTicket());
        bpmProcDef.setAutoCompleteTask(history.getAutoCompleteTask());
        bpmProcDef.setWarningApprovedTicket(history.getWarningApprovedTicket());
        bpmProcDef.setWarningApprovedTicketTasks(history.getWarningApprovedTicketTasks());
        bpmProcDef.setDisableApprovedTicket(history.getDisableApprovedTicket());
        bpmProcDef.setDisabledApprovedTicketTasks(history.getDisabledApprovedTicketTasks());
        bpmProcdefRepository.save(bpmProcDef);

        Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();
        // owner
        List<BpmOwnerProcess> bpmOwnerProcessList = g.fromJson(history.getProcessOwner(), new TypeReference<List<BpmOwnerProcess>>(){}.getType());
        List<BpmOwnerProcess> bpmOwnerProcessListNew = new ArrayList<>();
        if (!ValidationUtils.isNullOrEmpty(bpmOwnerProcessList)) {
            bpmOwnerProcessManager.deleteBpmOwnerProcessByProcDefId(id);
            for (BpmOwnerProcess owner : bpmOwnerProcessList) {
                BpmOwnerProcess bpmOwnerProcess = new BpmOwnerProcess();
                bpmOwnerProcess.setProcDefId(owner.getProcDefId());
                bpmOwnerProcess.setIdUser(owner.getIdUser());
                bpmOwnerProcessListNew.add(bpmOwnerProcess);
            }
            bpmOwnerProcessManager.saveAll(bpmOwnerProcessListNew);
        }

        // share user
        shareUserRepository.deleteAllByReferenceIdAndReferenceType(bpmProcDef.getId(), ShareUserTypeEnum.PROCESS.type);
        List<SharedUser> sharedUsers = g.fromJson(history.getShareWith(), new TypeReference<List<SharedUser>>(){}.getType());
        if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
            List<SharedUser> sharedUsersNew = new ArrayList<>();
            for (SharedUser sharedUser : sharedUsers) {
                SharedUser sharedNew = new SharedUser();
                sharedNew.setReferenceId(bpmProcDef.getId());
                sharedNew.setReferenceType(ShareUserTypeEnum.PROCESS.type);
                sharedNew.setEmail(sharedUser.getEmail());
                sharedUsersNew.add(sharedNew);
            }
            shareUserRepository.saveAll(sharedUsersNew);
        }

        // permission
        List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(bpmProcDef.getId(), PermissionDataConstants.Type.BPM_PROCDEF.code);
        if (!ValidationUtils.isNullOrEmpty(oldData)) {
            permissionDataManagementRepository.deleteAll(oldData);
        }
        List<PermissionDataManagement> perHistories = g.fromJson(history.getApplyFor(), new TypeReference<List<PermissionDataManagement>>(){}.getType());
        if (!ValidationUtils.isNullOrEmpty(perHistories)) {
            List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
            for (PermissionDataManagement perHistory : perHistories) {
                PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                permissionDataManagement.setTypeId(bpmProcDef.getId());
                permissionDataManagement.setTypeName(PermissionDataConstants.Type.BPM_PROCDEF.code);
                permissionDataManagement.setCompanyCode(perHistory.getCompanyCode());
                permissionDataManagement.setCreatedUser(username);
                permissionDataManagement.setCreatedTime(LocalDateTime.now());
                permissionDataManagements.add(permissionDataManagement);
            }
            permissionDataManagementRepository.saveAll(permissionDataManagements);
        }

        // Update procDefId mtk
        List<BpmTemplatePrint> lstTemplatePrint = bpmTemplatePrintRepository.getByProcDefId(oldProcDefId);
        if (!ValidationUtils.isNullOrEmpty(lstTemplatePrint)) {
            for (BpmTemplatePrint bpmTemplatePrint : lstTemplatePrint) {
                bpmTemplatePrint.setProcDefId(history.getProcDefId());

                // Update procDefId bpmTpTask
                List<BpmTpTask> lstBpmTpTask = bpmTpTaskRepository.getByTemplatePrintIdAndProcDefId(bpmTemplatePrint.getId(), oldProcDefId);
                if (!ValidationUtils.isNullOrEmpty(lstBpmTpTask)) {
                    for (BpmTpTask bpmTask : lstBpmTpTask) {
                        bpmTask.setProcDefId(history.getProcDefId());
                    }
                    bpmTpTaskRepository.saveAll(lstBpmTpTask);
                }
            }
            bpmTemplatePrintRepository.saveAll(lstTemplatePrint);
        }

        // save old history
        List<BpmProcDefHistory> listOldHistory = bpmProcDefHistoryRepository.findByOrgProcessId(bpmProcDef.getId());
        for (BpmProcDefHistory oldHistory : listOldHistory) {
            oldHistory.setStatusHistory(false);
        }
        bpmProcDefHistoryRepository.saveAll(listOldHistory);

        // create new version history
        int version = Math.toIntExact(bpmProcDefHistoryRepository.countByOrgProcessId(history.getOrgProcessId()));
        modelMapper.getConfiguration().setDeepCopyEnabled(true);
        BpmProcDefHistory newHistory = modelMapper.map(history, BpmProcDefHistory.class);
        newHistory.setId(null);
        newHistory.setStatusHistory(true);
        newHistory.setVersion("V" + version);
        newHistory.setCreatedDate(LocalDateTime.now());
        newHistory.setUserCreated(username);
        newHistory.setContentEdit("Khôi phục từ phiên bản " + history.getVersion());
        bpmProcDefHistoryRepository.save(newHistory);
    }
}
