package vn.fis.eapprove.business.domain.legislative.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.legislative.model.entity.LegislativeProgramDetail;

import java.util.List;

@Repository
public interface LegislativeProgramDetailRepository extends JpaRepository<LegislativeProgramDetail, Long>, JpaSpecificationExecutor<LegislativeProgramDetail> {

    List<LegislativeProgramDetail> findLegislativeProgramDetailByLegislativeId(Long legislativeId);

    List<LegislativeProgramDetail> findLegislativeProgramDetailByLegislativeIdAndType(Long legislativeId, String type);

    LegislativeProgramDetail findLegislativeProgramDetailByLegislativeIdAndTypeAndTaskDefKey(Long legislativeId, String type, String taskDefKey);

    List<LegislativeProgramDetail> findLegislativeProgramDetailByType(String type);

    @Query("select lp from LegislativeProgramDetail lp where lp.legislativeId in (:legislativeIds) and lp.type = :type and lp.status <> 'DEACTIVE'")
    List<LegislativeProgramDetail> findLegislativeProgramDetailByLegislativeIdInAndType(List<Long> legislativeIds, String type);
}
