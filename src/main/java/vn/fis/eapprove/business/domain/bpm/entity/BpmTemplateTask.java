package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Data;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.*;
import java.util.Date;

@Data
@Entity
@Transactional
@Table(name = "bpm_template_task")
public class BpmTemplateTask {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;
    @Column(name = "Task_id")
    private Long taskId;
    @Column(name = "Template_id")
    private Long templateId;
    @Column(name = "created_date")
    private Date createdDate;
}
