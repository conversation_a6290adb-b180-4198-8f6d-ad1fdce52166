package vn.fis.eapprove.business.domain.task.entity;

import lombok.Data;

import jakarta.persistence.*;

@Data
@Entity
@Table(name = "task_setting_config")
public class TaskSettingConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "expired_task_value_normal")
    private int expiredTaskValueNormal;

    @Column(name = "expired_task_value_warning")
    private int expiredTaskValueWarning;

    @Column(name = "expired_task_value_danger")
    private int expiredTaskValueDanger;

    @Column(name = "expired_hour_value_normal")
    private int expiredHourValueNormal;

    @Column(name = "expired_hour_value_warning")
    private int expiredHourValueWarning;

    @Column(name = "expired_hour_value_danger")
    private int expiredHourValueDanger;

    @Column(name = "email")
    private String email;
}
