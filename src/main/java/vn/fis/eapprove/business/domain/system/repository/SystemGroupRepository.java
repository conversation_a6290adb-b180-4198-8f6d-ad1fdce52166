package vn.fis.eapprove.business.domain.system.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.api.entity.ApiLog;
import vn.fis.eapprove.business.domain.system.entity.SystemGroup;


import java.util.List;

/**
 * Author: PhucVM
 * Date: 29/11/2022
 */
@Repository
public interface SystemGroupRepository extends JpaRepository<SystemGroup, Long>, JpaSpecificationExecutor<ApiLog> {
    List<SystemGroup> findAllByParent(Long parent);
    List<SystemGroup> findAllByParentIn(List<Long> parents);

    SystemGroup findTopByGroupTypeAndNameAndParent(String groupType,String groupName,Long parent);
    SystemGroup findTopByGroupTypeAndNameAndIdNotAndParent(String groupType,String groupName,Long id,Long parent);

    @Query("SELECT s FROM SystemGroup s " +
            "WHERE s.status in (:status) " +
            "AND s.groupType = :type " +
            "AND (lower(s.createdUser) like %:search% " +
            "OR lower(s.modifiedUser) like %:search% " +
            "OR lower(s.companyCode) like %:search% " +
            "OR lower(s.companyName) like %:search% " +
            "OR lower(s.name) like %:search% " +
            "OR lower(s.description) like %:search%)")
    List<SystemGroup> findAllByGroupType(@Param("type") String type, @Param("search") String search, @Param("status") List<Integer> status);


    List<SystemGroup> findAllByIdIn(List<Long> ids);

    @Query("SELECT COUNT(e) > 0 FROM SystemGroup e WHERE e.parent IN :parent AND e.groupValue IN :groupValue AND e.status = 1")
    Boolean existsByParentInAndGroupValueInAndStatusIsTrue(@Param("parent") List<Long> parent, @Param("groupValue") List<String> groupValue);

    @Query(value = "select sg.group_value " +
            "from system_group sg " +
            "where username = :username " +
            "and group_type = :type "+
            "and (select 1 from system_group sg2 " +
            "where id = sg.parent and NOW() between sg2.from_date and sg2.to_date " +
            "and sg2.status = 1) = 1",nativeQuery = true)
    List<Long> getGroupIdByGroupTypeAndUsername(String type, String username);

    List<SystemGroup> findSystemGroupByGroupTypeAndUsernameAndGroupValue(String groupType, String username, String groupValue);
}
