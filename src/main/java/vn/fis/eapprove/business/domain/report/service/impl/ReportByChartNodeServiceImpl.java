package vn.fis.eapprove.business.domain.report.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcInstRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTaskRepository;
import vn.fis.eapprove.business.domain.changeAssignee.repository.ChangeAssigneeHistoryRepository;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;
import vn.fis.eapprove.business.domain.priority.repository.PriorityManagementRepository;
import vn.fis.eapprove.business.domain.report.entity.ReportByChartNode;
import vn.fis.eapprove.business.domain.report.repository.ReportByChartNodeRepo;
import vn.fis.eapprove.business.domain.report.service.ReportByChartNodeService;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.repository.ServicePackageRepository;
import vn.fis.eapprove.business.dto.ReportByChartNodeDto;
import vn.fis.eapprove.business.mapper.IReportByChartNodeMapper;
import vn.fis.eapprove.business.model.response.UserInfoByUsername;
import vn.fis.eapprove.business.tenant.manager.impl.CustomerServiceImpl;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Transactional
public class ReportByChartNodeServiceImpl implements ReportByChartNodeService {

    private final ReportByChartNodeRepo reportByChartNodeRepo;
    private final IReportByChartNodeMapper mapper;
    private final BpmTaskRepository bpmTaskRepository;
    private final CustomerServiceImpl customerService;
    private final BpmProcInstRepository bpmProcInstRepository;
    private final ServicePackageRepository servicePackageRepository;
    private final PriorityManagementRepository priorityManagementRepository;
    private final ChangeAssigneeHistoryRepository changeAssigneeHistory;

    @Override
    public void createReportByChartNode(String taskId) {
        try {
            System.out.println("TicketId: " + taskId + "------------------create------report by chart node--------------------------------");

            if (!this.isTaskExist(taskId)) {
                this.insert(taskId);
                System.out.println("TicketId: " + taskId + "------------------insert------report by chart node------------ok--------------------");

            } else {
                this.update(taskId);
                System.out.println("TicketId: " + taskId + "------------------update------report by chart node-------------ok-------------------");

            }
        } catch (Exception e) {
            System.out.println("TicketId: " + taskId + "------------------error--------------------------------");
            e.printStackTrace();
        }
    }


    @Override
    public ReportByChartNode insert(String taskId) {
        ReportByChartNodeDto reportByChartNodeDto = this.mapToReportByChartNode(taskId);
        if (reportByChartNodeDto == null || ValidationUtils.isNullOrEmpty(reportByChartNodeDto.getAssigneeChartNodeId())) {
            return null;
        }
        ReportByChartNode reportByChartNode = mapper.to(reportByChartNodeDto);
        return reportByChartNodeRepo.save(reportByChartNode);
    }

    @Override
    public ReportByChartNode update(String taskId) {

        ReportByChartNodeDto reportByChartNodeDto = this.mapToReportByChartNode(taskId);
        if (reportByChartNodeDto == null || ValidationUtils.isNullOrEmpty(reportByChartNodeDto.getAssigneeChartNodeId())) {
            return null;
        }
        ReportByChartNode existed = this.findByTaskId(reportByChartNodeDto.getTaskId());
        ReportByChartNode update = mapper.merge(existed, reportByChartNodeDto);
        return reportByChartNodeRepo.save(update);
    }

    @Override
    public ReportByChartNode findByTaskId(String taskId) {
        return reportByChartNodeRepo.findByTaskId(taskId);
    }

    @Override
    public boolean isTaskExist(String taskId) {
        ReportByChartNode existed = reportByChartNodeRepo.findByTaskId(taskId);
        return existed != null;
    }

    @Override
    public void syncReportByChartNode(String fromDate, String toDate) {
        List<String> taskIds = getTaskIds(fromDate, toDate);
        for (String taskId : taskIds) {
            createReportByChartNode(taskId);
        }
    }

    @Override
    public List<String> getListUserFilter(List<String> usernames) {
        return reportByChartNodeRepo.getListUserFilter(usernames);
    }

    public List<String> getTaskIds(String fromDate, String toDate) {
        List<BpmTask> bpmTasks = bpmTaskRepository.getTaskByDate(fromDate, toDate);
        List<String> taskIds = new ArrayList<>();
        for (BpmTask e : bpmTasks) {
            taskIds.add(e.getTaskId());
        }
        return taskIds;
    }

    public ReportByChartNodeDto mapToReportByChartNode(String bpmTaskId) {

        ReportByChartNodeDto reportByChartNodeDto = new ReportByChartNodeDto();
        BpmTask bpmTask = bpmTaskRepository.getBpmTaskByTaskId(bpmTaskId);
        if (bpmTask == null) {
            return null;
        }

        BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByProcInstId(bpmTask.getTaskProcInstId());
        if (bpmProcInst == null) {
            return null;
        }

        reportByChartNodeDto.setTaskId(bpmTask.getTaskId());
        reportByChartNodeDto.setTaskName(bpmTask.getTaskName());
        reportByChartNodeDto.setTaskType(bpmTask.getTaskType());
        reportByChartNodeDto.setTaskStatus(bpmTask.getTaskStatus());
        reportByChartNodeDto.setProcInstId(bpmTask.getTaskProcInstId());
        if (bpmProcInst.getServiceId() != null) {
            Optional<ServicePackage> servicePackage = servicePackageRepository.findById(bpmProcInst.getServiceId());
            if (servicePackage.isPresent()) {
                reportByChartNodeDto.setServiceId(bpmProcInst.getServiceId());
                reportByChartNodeDto.setServiceName(servicePackage.get().getServiceName());
                reportByChartNodeDto.setSubmissionType(servicePackage.get().getSubmissionType());
                reportByChartNodeDto.setMasterParentId(servicePackage.get().getMasterParentId());
            }
        }
        reportByChartNodeDto.setRequestCode(bpmProcInst.getRequestCode());
        reportByChartNodeDto.setTitle(bpmProcInst.getTicketTitle());
        reportByChartNodeDto.setPriorityId(bpmProcInst.getPriorityId());
        if (bpmProcInst.getPriorityId() != null) {
            Optional<PriorityManagement> priorityManagement = priorityManagementRepository.findById(bpmProcInst.getPriorityId());
            if (priorityManagement.isPresent()) {
                reportByChartNodeDto.setPriorityName(priorityManagement.get().getName());
            } else {
                reportByChartNodeDto.setPriorityName("DEFAULT");
            }
        }
        reportByChartNodeDto.setLocationId(bpmProcInst.getLocationId());
        reportByChartNodeDto.setCreatedTime(bpmTask.getTaskCreatedTime());
        reportByChartNodeDto.setSlaFinishTime(bpmTask.getSlaFinishTime());
        reportByChartNodeDto.setFinishedTime(bpmTask.getTaskFinishedTime());
        reportByChartNodeDto.setStartedTime(bpmTask.getTaskStartedTime());
        reportByChartNodeDto.setCancelReason(bpmProcInst.getCancelReason());
        reportByChartNodeDto.setTicketId(bpmProcInst.getTicketId());
        reportByChartNodeDto.setProcDefId(bpmProcInst.getTicketProcDefId());
        UserInfoByUsername createdUser = customerService.getUserInfoByUsername(bpmProcInst.getCreatedUser());
        if (createdUser == null) {
            return null;
        }
        reportByChartNodeDto.setCreatedUser(bpmProcInst.getCreatedUser());
        reportByChartNodeDto.setCreatedUserStatus(createdUser.getStatus());
        reportByChartNodeDto.setCreatedUserFullName(createdUser.getFullName());
        reportByChartNodeDto.setCreatedUserChartId(bpmProcInst.getChartId());
        reportByChartNodeDto.setCreatedUserChartShortName(createdUser.getChartShortName());
        reportByChartNodeDto.setCreatedUserChartNodeId(createdUser.getChartNodeId());
        reportByChartNodeDto.setCreatedUserChartNodeName(createdUser.getChartNodeName());
        reportByChartNodeDto.setCreatedUserChartNodeCode(createdUser.getChartNodeCode());
        reportByChartNodeDto.setCreatedUserTitleName(createdUser.getTitleName());
        reportByChartNodeDto.setCreatedUserStaffCode(createdUser.getStaffCode());
        reportByChartNodeDto.setCreatedUserManagerLevel(createdUser.getManagerLevel());
        reportByChartNodeDto.setCreatedUserEmail(createdUser.getEmail());
        reportByChartNodeDto.setCreatedUserDirectManager(createdUser.getDirectManager());
        // nếu là ủy quyền thì lấy assignee là người tạo task -> lấy theo ng thực hiện task
        if (!ValidationUtils.isNullOrEmpty(bpmTask.getActionUser())) {
            reportByChartNodeDto.setAssignee(bpmTask.getActionUser());
            if (setAssigneeInfo(bpmTask.getActionUser(), reportByChartNodeDto)) return null;
        } else if (Boolean.TRUE.equals(bpmTask.getAssignType())) {
            String orgAssignee = changeAssigneeHistory.getAssigneeOrgByTaskId(bpmTask.getTaskId());
            reportByChartNodeDto.setAssignee(orgAssignee);
            if (setAssigneeInfo(orgAssignee, reportByChartNodeDto)) return null;
        } else {
            reportByChartNodeDto.setAssignee(bpmTask.getTaskAssignee());
            if (setAssigneeInfo(bpmTask.getTaskAssignee(), reportByChartNodeDto)) return null;
        }
//        LocalDateTime finishTime = bpmTask.getTaskFinishedTime();
//        finishTime = (finishTime == null) ? LocalDateTime.now() : finishTime;
//        LocalDateTime estimateTime = bpmTask.getSlaFinishTime();
//        reportByChartNodeDto.setIsExpire(!finishTime.isBefore(estimateTime));
        return reportByChartNodeDto;
    }

    private boolean setAssigneeInfo(String username, ReportByChartNodeDto reportByChartNodeDto) {
        UserInfoByUsername assigneeInfo = customerService.getUserInfoByUsername(username);
        if (assigneeInfo == null) {
            return true;
        }
        reportByChartNodeDto.setAssigneeChartNodeId(assigneeInfo.getChartNodeId().toString());
        reportByChartNodeDto.setAssigneeChartNodeCode(assigneeInfo.getChartNodeCode());
        reportByChartNodeDto.setAssigneeChartNodeName(assigneeInfo.getChartNodeName());
        reportByChartNodeDto.setAssigneeChartId(assigneeInfo.getChartId());
        reportByChartNodeDto.setAssigneeChartShortName(assigneeInfo.getChartShortName());
        reportByChartNodeDto.setAssigneeFullName(assigneeInfo.getFullName());
        reportByChartNodeDto.setAssigneeStaffCode(assigneeInfo.getStaffCode());
        reportByChartNodeDto.setAssigneeTitleName(assigneeInfo.getTitleName());
        reportByChartNodeDto.setAssigneeEmail(assigneeInfo.getEmail());
        reportByChartNodeDto.setAssigneeStaffCode(assigneeInfo.getStaffCode());
        reportByChartNodeDto.setAssigneeManagerLevel(assigneeInfo.getManagerLevel());
        reportByChartNodeDto.setAssigneeDirectManager(assigneeInfo.getDirectManager());
        reportByChartNodeDto.setAssigneeStatus(assigneeInfo.getStatus());
        return false;
    }
}
