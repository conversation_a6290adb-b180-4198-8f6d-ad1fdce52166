package vn.fis.eapprove.business.domain.changeAssignee.service;



import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.changeAssignee.entity.ChangeAssigneeHistory;

import java.util.List;

/**
 * Author: PhucVM
 * Date: 22/04/2023
 */
public interface ChangeAssigneeHistoryService {

    ChangeAssigneeHistory save(ChangeAssigneeHistory entity);

    List<ChangeAssigneeHistory> saveAll(List<ChangeAssigneeHistory> entities);

    ChangeAssigneeHistory save(BpmProcInst bpmProcInst, BpmTask bpmTask, String fromAssignee, String toAssignee, String orgAssigneeTitle, Integer assignType, Long assignTicketId);

    List<ChangeAssigneeHistory> getLatestChangeAssignees(Long bpmProcinstId, String procInstId, Long bpmTaskId, String taskId, String fromAssignee, String toAssignee);

    List<ChangeAssigneeHistory> getLatestChangeByToAssignee(Long bpmProcinstId);

    List<ChangeAssigneeHistory> getLatestChangeByToAssignee(Long bpmProcinstId, String taskId);

    ChangeAssigneeHistory getFirstChangeAssignee(Long bpmProcinstId, String taskId);

    List<ChangeAssigneeHistory> findAllByBpmProcinstIdAndTaskIdAndOrgAssignee(Long bpmProcInstId, String taskId, String orgAssignee);

    void deleteAll(List<ChangeAssigneeHistory> entities);
}
