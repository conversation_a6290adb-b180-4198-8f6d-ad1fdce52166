package vn.fis.eapprove.business.domain.report.service.impl;

import vn.fis.eapprove.security.CredentialHelper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import vn.fis.eapprove.business.constant.Constant;
import vn.fis.eapprove.business.domain.report.service.ReportProcInstServiceNew;
import vn.fis.eapprove.business.dto.filter.ReportProcInstFilter;
import vn.fis.eapprove.business.dto.report.ReportProcInstByChartNodeDto;
import vn.fis.eapprove.business.dto.report.ReportProcInstByGroupDto;
import vn.fis.eapprove.business.exception.report.BusinessCode;
import vn.fis.eapprove.business.exception.report.BusinessException;
import vn.fis.eapprove.business.model.response.ReportProcInstByChartNodeResponse;
import vn.fis.eapprove.business.model.response.ReportProcInstByGroupResponse;
import vn.fis.eapprove.business.model.response.UserInfoByUsername;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.utils.ReportHelperNew;
import vn.fis.spro.common.util.ListCompare;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.Tuple;
import java.math.BigInteger;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.*;


@Service
@AllArgsConstructor
public class ReportProcInstServiceImplNew implements ReportProcInstServiceNew {

    private EntityManager entityManager;

    private ReportHelperNew reportHelperNew;

    private CustomerService customerService;

    private final CredentialHelper credentialHelper;

    @Override
    @SuppressWarnings("unchecked")
    public ReportProcInstByGroupResponse getReportProcInstByGroup(ReportProcInstFilter filter) {

        String status = null;
        String username;

        LocalDate date = LocalDate.parse(filter.getToDate());
        String newDate = date.plusDays(1).toString();
        filter.setToDate(newDate);

        try {
            username = credentialHelper.getJWTPayload().getUsername();
        } catch (Exception e) {
            throw new BusinessException(BusinessCode.TOKEN_INVALID);
        }

        if (!ValidationUtils.isNullOrEmpty(filter.getUserStatus()) && filter.getUserStatus().size() == 1) {
            status = filter.getUserStatus().get(0);
        }

        Set<String> users = new HashSet<>(customerService.getUserDefault(username, status));
        users.add(username);
        filter.getDefaultUser().addAll(users);

        if (filter.getUserStatus().size() == 1 && filter.getUserStatus().get(0).equalsIgnoreCase(Constant.INACTIVE)) {
            filter.getDefaultUser().remove(username);
        }

        List<Long> chartNodes = customerService.getChartNodeIdsByUsernames(filter.getDefaultUser());
        filter.setDefaultChartNodeId(chartNodes);

        ReportProcInstByGroupResponse reportProcInstByGroupResponse = new ReportProcInstByGroupResponse();

        List<ReportProcInstByGroupDto> list = getReportProcInstByGroupList(filter);

        BigInteger total = countReportProcInstByGroup(filter);

        reportProcInstByGroupResponse.setList(list);
        reportProcInstByGroupResponse.setTotal(total);
        return reportProcInstByGroupResponse;
    }


    @Override
    public ReportProcInstByGroupResponse getReportTaskByUser(ReportProcInstFilter filter) {
        String status = null;

        String username;

        LocalDate date = LocalDate.parse(filter.getToDate());
        String newDate = date.plusDays(1).toString();
        filter.setToDate(newDate);

        try {
            username = credentialHelper.getJWTPayload().getUsername();
        } catch (Exception e) {
            throw new BusinessException(BusinessCode.TOKEN_INVALID);
        }

        if (!ValidationUtils.isNullOrEmpty(filter.getUserStatus()) && filter.getUserStatus().size() == 1) {
            status = filter.getUserStatus().get(0);
        }

        Set<String> users = new HashSet<>(customerService.getUserDefault(username, status));
        users.add(username);
        filter.getDefaultUser().addAll(users);

        if (filter.getUserStatus().size() == 1 && filter.getUserStatus().get(0).equalsIgnoreCase("inactive")) {
            filter.getDefaultUser().remove(username);
        }

        List<Long> chartNodes = customerService.getChartNodeIdsByUsernames(filter.getDefaultUser());
        filter.setDefaultChartNodeId(chartNodes);

        ReportProcInstByGroupResponse reportProcInstByGroupResponse = new ReportProcInstByGroupResponse();

        List<ReportProcInstByGroupDto> list = getReportTaskByUserList(filter);

        BigInteger total = countReportTaskByUser(filter);

        reportProcInstByGroupResponse.setList(list);
        reportProcInstByGroupResponse.setTotal(total);

        return reportProcInstByGroupResponse;
    }

    @Override
    @SuppressWarnings("unchecked")
    public ReportProcInstByChartNodeResponse getReportProcInstByChartNode(ReportProcInstFilter filter) {
        String status = null;

        String username;
        LocalDate date = LocalDate.parse(filter.getToDate());
        String newDate = date.plusDays(1).toString();
        filter.setToDate(newDate);

        try {
            username = credentialHelper.getJWTPayload().getUsername();
        } catch (Exception e) {
            throw new BusinessException(BusinessCode.TOKEN_INVALID);
        }

        if (!ValidationUtils.isNullOrEmpty(filter.getUserStatus()) && filter.getUserStatus().size() == 1) {
            status = filter.getUserStatus().get(0);
        }

        Set<String> users = new HashSet<>(customerService.getUserDefault(username, status));
        users.add(username);
        filter.getDefaultUser().addAll(users);

        if (filter.getUserStatus().size() == 1 && filter.getUserStatus().get(0).equalsIgnoreCase("inactive")) {
            filter.getDefaultUser().remove(username);
        }

        List<Long> chartNodes = customerService.getChartNodeIdsByUsernames(filter.getDefaultUser());
        filter.setDefaultChartNodeId(chartNodes);

        ReportProcInstByChartNodeResponse response = new ReportProcInstByChartNodeResponse();
        Map<String, List<ReportProcInstByChartNodeDto>> resultMap = new HashMap<>();

        List<ReportProcInstByChartNodeDto> listUserChartNode = getReportProcInstByChartNodeList(filter);
        List<UserInfoByUsername> userInfoByUsernames = customerService.getInfoByListUser(filter.getDefaultUser());
        mapperUserInfoToReportByChartNode(userInfoByUsernames, listUserChartNode);

        for (ReportProcInstByChartNodeDto dto : listUserChartNode) {
            if ((ObjectUtils.isEmpty(filter.getDefaultChartNodeId()) && ObjectUtils.isEmpty(dto.getChartNodeId()) || ListCompare.containsCommonElement(filter.getDefaultChartNodeId(), dto.getChartNodeId()))) {
                for (String chartNodeCode : dto.getChartNodeCode()) {
                    resultMap.computeIfAbsent(chartNodeCode, k -> new ArrayList<>()).add(dto);
                }
            }
        }
        response.setResult(resultMap);
        response.setTotal(BigInteger.valueOf(resultMap.size()));
        return response;
    }

    private static void mapperUserInfoToReportByChartNode(List<UserInfoByUsername> userInfoByUsernames, List<ReportProcInstByChartNodeDto> listUserChartNode) {
        Map<String, UserInfoByUsername> userInfoByUsernameMap = new HashMap<>();
        for (UserInfoByUsername userInfo : userInfoByUsernames) {
            userInfoByUsernameMap.put(userInfo.getUsername(), userInfo);
        }
        for (ReportProcInstByChartNodeDto e : listUserChartNode) {
            UserInfoByUsername userInfo = userInfoByUsernameMap.get(e.getAssignee());
            if (userInfo != null) {
                e.setAssigneeFullName(userInfo.getFullName());
                e.setUserTitleName(userInfo.getTitleName());
                e.setChartShortName(userInfo.getChartShortName());
                e.setChartNodeName(userInfo.getChartNodeName());
                e.setChartId(userInfo.getChartId());
                e.setStaffCode(userInfo.getStaffCode());
            }
        }
    }

    private List<ReportProcInstByGroupDto> getReportProcInstByGroupList(ReportProcInstFilter filter) {

        String mainQuery =
                "select d.* from (select * from (select b.*, " +
                        "       ifnull(b.carryoverPreviousApproval, 0) + ifnull(b.duringApprovalPeriod, 0) as pending " +
                        " from  " +
                        " (with history as (select v.ticket_id, MAX(version_time) as version_time  " +
                        "                 from (SELECT ticket_id, MAX(version_time) as version_time  " +
                        "                       FROM report_by_group_new " +
                        "                       where version_time between :fromDate and :toDate  " +
                        "                       GROUP BY ticket_id  " +
                        "                       union all  " +
                        "                       SELECT ticket_id, MAX(version_time) as version_time  " +
                        "                       FROM report_by_group_new  " +
                        "                       where version_time < :fromDate " +
                        "                       GROUP BY ticket_id) as v " +
                        "                 group by ticket_id)  " +
                        "SELECT service_id, service_name,  " +
                        "             SUM(CASE " +
                        "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND " +
                        "                          rp.version_time < :fromDate THEN 1 " +
                        "                     ELSE 0 END) as carryoverPreviousApproval, " +
                        "             SUM(CASE " +
                        "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND " +
                        "                          (rp.version_time BETWEEN :fromDate AND :toDate) THEN 1 " +
                        "                     ELSE 0 END) as duringApprovalPeriod, " +
                        "             SUM(CASE " +
                        "                     WHEN proc_inst_status IN ('COMPLETED','CLOSED') AND (rp.version_time BETWEEN :fromDate AND :toDate) THEN 1 " +
                        "                     ELSE 0 " +
                        "                 END)            AS approved, " +
                        "             SUM(CASE " +
                        "                     WHEN " +
                        "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time <= sla_finish_time AND  " +
                        "                                 (rp.version_time BETWEEN :fromDate AND :toDate)  " +
                        "                         THEN 1 " +
                        "                     ELSE 0 " +
                        "                 END)            AS approvedOnTime, " +
                        "             SUM(CASE " +
                        "                     WHEN " +
                        "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time > sla_finish_time AND  " +
                        "                                 (rp.version_time BETWEEN :fromDate AND :toDate)  " +
                        "                         THEN 1 " +
                        "                     ELSE 0 " +
                        "                 END)            AS approvedDelayed, " +
                        "             SUM(CASE " +
                        "                     WHEN " +
                        "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST')  " +
                        "                             AND current_timestamp <= sla_finish_time AND " +
                        "                                 rp.version_time <= :toDate THEN 1 " +
                        "                     ELSE 0 " +
                        "                 END)            AS approvingNotDelayed, " +
                        "             SUM(CASE " +
                        "                     WHEN " +
                        "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST') " +
                        "                             AND current_timestamp > sla_finish_time AND " +
                        "                                 rp.version_time <= :toDate THEN 1 " +
                        "                     ELSE 0 " +
                        "                 END)            AS approvingDelayed, " +
                        "             SUM(CASE " +
                        "                     WHEN proc_inst_status in  ('DELETED_BY_RU','RECALLED','RECALLING') AND rp.version_time <= :toDate THEN 1 " +
                        "                     ELSE 0 " +
                        "                 END)            AS approvalReturned, " +
                        "             SUM(CASE " +
                        "                     WHEN proc_inst_status = 'CANCEL' AND (rp.version_time BETWEEN :fromDate AND :toDate) THEN 1 " +
                        "                     ELSE 0 " +
                        "                 END)            AS approvalCanceled  " +
                        "      FROM report_by_group_new rp join history h on rp.ticket_id = h.ticket_id and rp.version_time = h.version_time   " +
                        "       WHERE rp.created_user in :defaultUser AND rp.chart_node_id in :defaultChartNodeId AND ";

        // create stringBuilder
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        reportHelperNew.buildReportByGroupFilter(filter, stringBuilder);
        stringBuilder.append(" 1=1  GROUP BY rp.service_id,rp.service_name) as b  ");
        stringBuilder.append(" where b.carryoverPreviousApproval > 0 " +
                "   or b.duringApprovalPeriod > 0 " +
                "   or b.approved > 0 " +
                "   or b.approvedOnTime > 0 " +
                "   or b.approvedDelayed > 0 " +
                "   or b.approvingNotDelayed > 0 " +
                "   or b.approvingDelayed > 0 " +
                "   or b.approvalReturned > 0 " +
                "   or b.approvalCanceled > 0 ");
        stringBuilder.append(" ) AS c LIMIT :index,:size ) as d ");
        reportHelperNew.buildReportByGroupSort(filter, stringBuilder);

        // create query
        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);

        reportHelperNew.buildReportByGroupParameter(filter, query);
        query.setParameter("index", (filter.getPage() - 1) * filter.getPageSize());
        query.setParameter("size", filter.getPageSize());

        List<Tuple> result = query.getResultList();
        List<ReportProcInstByGroupDto> list = new ArrayList<>();
        result.forEach(tuple -> {
            ReportProcInstByGroupDto reportProcInstByGroupDto = new ReportProcInstByGroupDto();
            try {
                reportHelperNew.tupleToReportProcInstDto(tuple, reportProcInstByGroupDto);
            } catch (ParseException e) {
                throw new BusinessException(BusinessCode.INTERNAL_SERVER_ERROR);
            }

            list.add(reportProcInstByGroupDto);
        });

        return list;
    }


    private BigInteger countReportProcInstByGroup(ReportProcInstFilter filter) {

        String mainQuery = " (select * from (select b.*, " +
                "       ifnull(b.carryoverPreviousApproval, 0) + ifnull(b.duringApprovalPeriod, 0) as pending " +
                " from  " +
                " (with history as (select v.ticket_id, MAX(version_time) as version_time " +
                "                 from (SELECT ticket_id, MAX(version_time) as version_time " +
                "                       FROM report_by_group_new " +
                "                       where version_time between :fromDate and :toDate " +
                "                       GROUP BY ticket_id " +
                "                       union all " +
                "                       SELECT ticket_id, MAX(version_time) as version_time " +
                "                       FROM report_by_group_new " +
                "                       where version_time < :fromDate " +
                "                       GROUP BY ticket_id) as v " +
                "                 group by ticket_id)  " +
                "SELECT service_id,service_name,  " +
                "             SUM(CASE " +
                "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND " +
                "                          rp.version_time < :fromDate THEN 1 " +
                "                     ELSE 0 END) as carryoverPreviousApproval, " +
                "             SUM(CASE " +
                "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND " +
                "                          (rp.version_time BETWEEN :fromDate AND :toDate) THEN 1 " +
                "                     ELSE 0 END) as duringApprovalPeriod, " +
                "             SUM(CASE " +
                "                     WHEN proc_inst_status IN ('COMPLETED','CLOSED') AND (rp.version_time BETWEEN :fromDate AND :toDate) THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approved, " +
                "             SUM(CASE " +
                "                     WHEN " +
                "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time <= sla_finish_time AND  " +
                "                                 (rp.version_time BETWEEN :fromDate AND :toDate)  " +
                "                         THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvedOnTime, " +
                "             SUM(CASE " +
                "                     WHEN " +
                "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time > sla_finish_time AND  " +
                "                                 (rp.version_time BETWEEN :fromDate AND :toDate)  " +
                "                         THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvedDelayed, " +
                "             SUM(CASE " +
                "                     WHEN " +
                "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST')  " +
                "                             AND current_timestamp <= sla_finish_time AND " +
                "                                 rp.version_time < :toDate THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvingNotDelayed, " +
                "             SUM(CASE " +
                "                     WHEN " +
                "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST') " +
                "                             AND current_timestamp > sla_finish_time AND " +
                "                                 rp.version_time < :toDate THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvingDelayed, " +
                "             SUM(CASE " +
                "                     WHEN proc_inst_status in  ('DELETED_BY_RU','RECALLED','RECALLING') AND rp.version_time < :toDate THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvalReturned, " +
                "             SUM(CASE " +
                "                     WHEN proc_inst_status = 'CANCEL' AND (rp.version_time BETWEEN :fromDate AND :toDate) THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvalCanceled  " +
                "      FROM report_by_group_new rp join history h on rp.ticket_id = h.ticket_id and rp.version_time = h.version_time   " +
                "       WHERE rp.created_user in :defaultUser AND rp.chart_node_id in :defaultChartNodeId AND ";

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select count(service_id) as count from ").append(mainQuery);
        reportHelperNew.buildReportByGroupFilter(filter, stringBuilder);

        stringBuilder.append(" 1=1  GROUP BY rp.service_id,rp.service_name) as b ");
        stringBuilder.append(" where b.carryoverPreviousApproval > 0 " +
                "   or b.duringApprovalPeriod > 0 " +
                "   or b.approved > 0 " +
                "   or b.approvedOnTime > 0 " +
                "   or b.approvedDelayed > 0 " +
                "   or b.approvingNotDelayed > 0 " +
                "   or b.approvingDelayed > 0 " +
                "   or b.approvalReturned > 0 " +
                "   or b.approvalCanceled > 0 ");
        stringBuilder.append(" ) as c) as count  ");

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);

        reportHelperNew.buildReportByGroupParameter(filter, query);

        List<Tuple> result = query.getResultList();
        Long count = (Long) result.get(0).get("count");
        return BigInteger.valueOf(count);
    }

    private List<ReportProcInstByChartNodeDto> getReportProcInstByChartNodeList(ReportProcInstFilter filter) {
        StringBuilder stringBuilder = new StringBuilder();
        String subQuery =
                " WITH information_user as (select distinct rc.assignee as assignee_task,assignee_chart_id, " +
                        "       assignee_chart_node_id,assignee_chart_node_code  " +
                        "from report_by_chart_node_new rc where assignee in :defaultUser and assignee_chart_node_id in :defaultChartNodeId  ";
        stringBuilder.append(subQuery);
        if (!ValidationUtils.isNullOrEmpty(filter.getChartNodeId())) {
            stringBuilder.append(" and assignee_chart_node_id in :chartNodeId  ");
        }
        stringBuilder.append(" ) ");
        String mainQuery = " select b.*, " +
                "       information_user.*, " +
                "       ifnull(b.carryoverPreviousApproval, " +
                "              0) + ifnull(b.duringApprovalPeriod,0) as pending " +
                " from  " +
                "(with history as (select v.task_id, MAX(version_time) as version_time " +
                "                 from (SELECT task_id, MAX(version_time) as version_time " +
                "                       FROM report_by_chart_node_new " +
                "                       where version_time between :fromDate and :toDate " +
                "                       GROUP BY task_id " +
                "                       union all " +
                "                       SELECT task_id, MAX(version_time) as version_time " +
                "                       FROM report_by_chart_node_new " +
                "                       where version_time < :fromDate " +
                "                       GROUP BY task_id) as v " +
                "                 group by task_id)  " +
                "SELECT assignee, " +
                "SUM(CASE " +
                "                     WHEN rc.task_status IN ('PROCESSING', 'ACTIVE') AND rc.version_time < :fromDate  THEN 1 " +
                "                     ELSE 0 " +
                "                 END) as carryoverPreviousApproval,  " +
                "             SUM(CASE " +
                "                     WHEN task_status IN ('PROCESSING','ACTIVE') AND rc.version_time between :fromDate AND :toDate THEN 1 " +
                "                     ELSE 0 END) as duringApprovalPeriod, " +
                "             SUM(CASE " +
                "                     WHEN task_status = 'COMPLETED' AND rc.version_time between :fromDate AND :toDate THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approved, " +
                "             SUM(CASE " +
                "                     WHEN " +
                "                                 task_status = 'COMPLETED'  " +
                "                             AND finished_time <= sla_finish_time AND rc.version_time between :fromDate AND :toDate THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvedOnTime, " +
                "             SUM(CASE " +
                "                     WHEN " +
                "                                 task_status = 'COMPLETED' " +
                "                             AND finished_time > sla_finish_time AND rc.version_time between :fromDate AND :toDate THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvedDelayed, " +
                "             SUM(CASE " +
                "                     WHEN " +
                "                                 task_status IN ('PROCESSING','ACTIVE')  " +
                "                             AND current_timestamp <= sla_finish_time AND rc.version_time <= :toDate THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvingNotDelayed, " +
                "             SUM(CASE " +
                "                     WHEN " +
                "                                 task_status IN ('PROCESSING','ACTIVE') " +
                "                             AND current_timestamp > sla_finish_time AND rc.version_time <= :toDate THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvingDelayed, " +
                "             SUM(CASE " +
                "                     WHEN task_status in ('DELETED_BY_RU','AGREE_TO_RECALL') AND rc.version_time <= :toDate THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvalReturned, " +
                "             SUM(CASE " +
                "                     WHEN task_status = 'CANCEL' AND rc.version_time between :fromDate AND :toDate THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvalCanceled " +
                "      FROM report_by_chart_node_new rc join history h on rc.task_id = h.task_id and rc.version_time = h.version_time " +
                "      WHERE rc.assignee in :defaultUser AND rc.assignee_chart_node_id in :defaultChartNodeId AND  ";


        stringBuilder.append(mainQuery);
        reportHelperNew.buildReportTaskByChartNodeFilter(filter, stringBuilder);
        stringBuilder.append(" 1=1  " +
                "      GROUP BY assignee) as b " +
                "     JOIN information_user on information_user.assignee_task = b.assignee  " +
                " WHERE "
        );
        stringBuilder.append(" b.carryoverPreviousApproval > 0 " +
                "   or b.duringApprovalPeriod > 0 " +
                "   or b.approved > 0 " +
                "   or b.approvedOnTime > 0 " +
                "   or b.approvedDelayed > 0 " +
                "   or b.approvingNotDelayed > 0 " +
                "   or b.approvingDelayed > 0 " +
                "   or b.approvalReturned > 0 " +
                "   or b.approvalCanceled > 0 AND ");
        stringBuilder.append(" 1=1");
        reportHelperNew.buildReportByGroupSort(filter, stringBuilder);
        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);

        reportHelperNew.buildReportTaskByChartNodeParameter(filter, query);

        List<Tuple> result = query.getResultList();
        List<ReportProcInstByChartNodeDto> list = new ArrayList<>();
        result.forEach(tuple -> {
            ReportProcInstByChartNodeDto reportProcInstByChartNodeDto = new ReportProcInstByChartNodeDto();
            try {
                reportHelperNew.tupleToReportProcInstDto(tuple, reportProcInstByChartNodeDto);
            } catch (ParseException e) {
                throw new BusinessException(BusinessCode.INTERNAL_SERVER_ERROR);
            }

            list.add(reportProcInstByChartNodeDto);
        });
        return list;
    }

    private List<ReportProcInstByGroupDto> getReportTaskByUserList(ReportProcInstFilter filter) {

        String mainQuery =
                "select d.* from (select * from (select b.*, " +
                        "       ifnull(b.carryoverPreviousApproval, 0) + ifnull(b.duringApprovalPeriod, 0) as pending " +
                        " from " +
                        " (with history as (select v.ticket_id, MAX(version_time) as version_time " +
                        "                 from (SELECT ticket_id, MAX(version_time) as version_time " +
                        "                       FROM report_by_group_new " +
                        "                       where version_time between :fromDate and :toDate " +
                        "                       GROUP BY ticket_id " +
                        "                       union all " +
                        "                       SELECT ticket_id, MAX(version_time) as version_time " +
                        "                       FROM report_by_group_new " +
                        "                       where version_time < :fromDate " +
                        "                       GROUP BY ticket_id) as v " +
                        "                 group by ticket_id)  " +
                        "SELECT service_id,service_name,  " +
                        "             SUM(CASE " +
                        "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND " +
                        "                          rp.version_time < :fromDate THEN 1 " +
                        "                     ELSE 0 END) as carryoverPreviousApproval, " +
                        "             SUM(CASE " +
                        "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND " +
                        "                          (rp.version_time BETWEEN :fromDate AND :toDate) THEN 1 " +
                        "                     ELSE 0 END) as duringApprovalPeriod, " +
                        "             SUM(CASE " +
                        "                     WHEN proc_inst_status IN ('COMPLETED','CLOSED') AND (rp.version_time BETWEEN :fromDate AND :toDate) THEN 1 " +
                        "                     ELSE 0 " +
                        "                 END)            AS approved, " +
                        "             SUM(CASE " +
                        "                     WHEN " +
                        "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time <= sla_finish_time AND  " +
                        "                                 (rp.version_time BETWEEN :fromDate AND :toDate)  " +
                        "                         THEN 1 " +
                        "                     ELSE 0 " +
                        "                 END)            AS approvedOnTime, " +
                        "             SUM(CASE " +
                        "                     WHEN " +
                        "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time > sla_finish_time AND  " +
                        "                                 (rp.version_time BETWEEN :fromDate AND :toDate)  " +
                        "                         THEN 1 " +
                        "                     ELSE 0 " +
                        "                 END)            AS approvedDelayed, " +
                        "             SUM(CASE " +
                        "                     WHEN " +
                        "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST')  " +
                        "                             AND current_timestamp <= sla_finish_time AND  " +
                        "                                 rp.version_time <= :toDate THEN 1 " +
                        "                     ELSE 0 " +
                        "                 END)            AS approvingNotDelayed, " +
                        "             SUM(CASE " +
                        "                     WHEN " +
                        "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST') " +
                        "                             AND current_timestamp > sla_finish_time AND " +
                        "                                 rp.version_time <= :toDate THEN 1 " +
                        "                     ELSE 0 " +
                        "                 END)            AS approvingDelayed, " +
                        "             SUM(CASE " +
                        "                     WHEN proc_inst_status in  ('DELETED_BY_RU','RECALLED','RECALLING') AND rp.version_time <= :toDate THEN 1 " +
                        "                     ELSE 0 " +
                        "                 END)            AS approvalReturned, " +
                        "             SUM(CASE " +
                        "                     WHEN proc_inst_status = 'CANCEL' AND (rp.version_time BETWEEN :fromDate AND :toDate) THEN 1 " +
                        "                     ELSE 0 " +
                        "                 END)            AS approvalCanceled  " +
                        "      FROM report_by_group_new rp join history h on rp.ticket_id = h.ticket_id and rp.version_time = h.version_time  " +
                        "       WHERE ";

        // create stringBuilder
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        reportHelperNew.buildReportByUserFilter(filter, stringBuilder);
        stringBuilder.append(" 1=1  GROUP BY rp.service_id,rp.service_name) as b  ");
        stringBuilder.append(" where b.carryoverPreviousApproval > 0 " +
                "   or b.duringApprovalPeriod > 0 " +
                "   or b.approved > 0 " +
                "   or b.approvedOnTime > 0 " +
                "   or b.approvedDelayed > 0 " +
                "   or b.approvingNotDelayed > 0 " +
                "   or b.approvingDelayed > 0 " +
                "   or b.approvalReturned > 0 " +
                "   or b.approvalCanceled > 0 ");
        stringBuilder.append(" ) AS c LIMIT :index,:size ) as d ");
        reportHelperNew.buildReportByGroupSort(filter, stringBuilder);

        // create query
        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);

        reportHelperNew.buildReportTaskByUserParameter(filter, query);

        query.setParameter("index", (filter.getPage() - 1) * filter.getPageSize());
        query.setParameter("size", filter.getPageSize());

        List<Tuple> result = query.getResultList();
        List<ReportProcInstByGroupDto> list = new ArrayList<>();
        result.forEach(tuple -> {
            ReportProcInstByGroupDto reportProcInstByGroupDto = new ReportProcInstByGroupDto();
            try {
                reportHelperNew.tupleToReportProcInstDto(tuple, reportProcInstByGroupDto);
            } catch (ParseException e) {
                throw new BusinessException(BusinessCode.INTERNAL_SERVER_ERROR);
            }

            list.add(reportProcInstByGroupDto);
        });

        return list;
    }


    private BigInteger countReportTaskByUser(ReportProcInstFilter filter) {
        String mainQuery = " (select * from (select b.*, " +
                "       ifnull(b.carryoverPreviousApproval, 0) + ifnull(b.duringApprovalPeriod, 0) as pending " +
                " from " +
                " (with history as (select v.ticket_id, MAX(version_time) as version_time " +
                "                 from (SELECT ticket_id, MAX(version_time) as version_time " +
                "                       FROM report_by_group_new " +
                "                       where version_time between :fromDate and :toDate " +
                "                       GROUP BY ticket_id " +
                "                       union all " +
                "                       SELECT ticket_id, MAX(version_time) as version_time " +
                "                       FROM report_by_group_new " +
                "                       where version_time < :fromDate " +
                "                       GROUP BY ticket_id) as v " +
                "                 group by ticket_id)  " +
                "SELECT service_id,service_name,  " +
                "             SUM(CASE " +
                "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND " +
                "                          rp.version_time < :fromDate THEN 1 " +
                "                     ELSE 0 END) as carryoverPreviousApproval, " +
                "             SUM(CASE " +
                "                     WHEN proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND " +
                "                          (rp.version_time BETWEEN :fromDate AND :toDate) THEN 1 " +
                "                     ELSE 0 END) as duringApprovalPeriod, " +
                "             SUM(CASE " +
                "                     WHEN proc_inst_status IN ('COMPLETED','CLOSED') AND (rp.version_time BETWEEN :fromDate AND :toDate) THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approved, " +
                "             SUM(CASE " +
                "                     WHEN " +
                "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time <= sla_finish_time AND  " +
                "                                 (rp.version_time BETWEEN :fromDate AND :toDate)  " +
                "                         THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvedOnTime, " +
                "             SUM(CASE " +
                "                     WHEN " +
                "                                 proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time > sla_finish_time AND  " +
                "                                 (rp.version_time BETWEEN :fromDate AND :toDate)  " +
                "                         THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvedDelayed, " +
                "             SUM(CASE " +
                "                     WHEN " +
                "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST')  " +
                "                             AND current_timestamp <= sla_finish_time AND " +
                "                                 rp.version_time < :toDate THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvingNotDelayed, " +
                "             SUM(CASE " +
                "                     WHEN " +
                "                                 proc_inst_status IN ('OPENED','PROCESSING','ADDITIONAL_REQUEST') " +
                "                             AND current_timestamp > sla_finish_time AND " +
                "                                 rp.version_time < :toDate THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvingDelayed, " +
                "             SUM(CASE " +
                "                     WHEN proc_inst_status in  ('DELETED_BY_RU','RECALLED','RECALLING') AND rp.version_time < :toDate THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvalReturned, " +
                "             SUM(CASE " +
                "                     WHEN proc_inst_status = 'CANCEL' AND (rp.version_time BETWEEN :fromDate AND :toDate) THEN 1 " +
                "                     ELSE 0 " +
                "                 END)            AS approvalCanceled  " +
                "      FROM report_by_group_new rp join history h on rp.ticket_id = h.ticket_id and rp.version_time = h.version_time  " +
                "       WHERE ";

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select count(service_id) as count from ").append(mainQuery);
        reportHelperNew.buildReportByUserFilter(filter, stringBuilder);

        stringBuilder.append(" 1=1  GROUP BY rp.service_id,rp.service_name) as b ");
        stringBuilder.append(" where b.carryoverPreviousApproval > 0 " +
                "   or b.duringApprovalPeriod > 0 " +
                "   or b.approved > 0 " +
                "   or b.approvedOnTime > 0 " +
                "   or b.approvedDelayed > 0 " +
                "   or b.approvingNotDelayed > 0 " +
                "   or b.approvingDelayed > 0 " +
                "   or b.approvalReturned > 0 " +
                "   or b.approvalCanceled > 0 ");
        stringBuilder.append(" ) as c) as count  ");

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);

        reportHelperNew.buildReportTaskByUserParameter(filter, query);

        List<Tuple> result = query.getResultList();
        Long count = result.get(0).get("count", Long.class);
        return BigInteger.valueOf(count);

    }

}
