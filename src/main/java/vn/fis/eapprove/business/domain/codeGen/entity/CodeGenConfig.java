package vn.fis.eapprove.business.domain.codeGen.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;

import jakarta.persistence.*;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Author: AnhVTN
 * Date: 30/03/2023
 */

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@Entity
@Table(name = "code_gen_config")
public class CodeGenConfig {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "code", length = 100, nullable = true)
    private String code;
    @Column(name = "description", length = 500, nullable = true)
    private String description;
    @Column(name = "structor_code", length = 100, nullable = true)
    private String structorCode;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_at", nullable = true)
    private Date createdAt;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_at", nullable = true)
    private Date updatedAt;
    @Column(name = "user_create", nullable = true)
    private String userCreate;

    @Column(name = "user_update")
    private String userUpdate;

    @Column(name = "status")
    private String status;

    @Transient
    private List<CodeGenStruct> lstCodeGenStruct;

    @OneToMany(mappedBy = "codeGenConfig")
    @JsonIgnore
    private Set<PermissionDataManagement> permissionDataManagements;
    @Column(name = "company_code")
    private String companyCode;
    @Column(name = "company_name")
    private String companyName;
}
