package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Data;

import jakarta.persistence.*;

@Data
@Entity
@Table(name = "bpm_tp_task")
public class BpmTpTask {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "TASK_DEF_KEY")
    private String taskDefKey;

    @Column(name = "PROC_DEF_ID")
    private String procDefId;

    @Column(name = "FORM_KEY")
    private String formKey;
    //0: execution
    //1: approval
    @Column(name = "TASK_TYPE")
    private int taskType = 0;

    @Column(name = "BPM_TEMPLATE_PRINT_ID")
    private Long bpmTemplatePrintId;

    @Column(name = "STATUS")
    private String status;
}
