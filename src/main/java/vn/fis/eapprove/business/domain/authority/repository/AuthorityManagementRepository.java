package vn.fis.eapprove.business.domain.authority.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.authority.entity.AuthorityManagement;


import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @project business-process-service
 * @created 3/13/2023 - 4:47 PM
 */

@Repository
public interface AuthorityManagementRepository extends JpaRepository<AuthorityManagement, Long> {
    List<AuthorityManagement> findAuthorityManagementByFromAccountAndToAccount(String fromAccount, String toAccount);

    @Query(value = "select a.fromAccount from AuthorityManagement a where a.ticketId =:ticketId and a.toAccount IN (:lstToAccounts)")
    List<String> findAuthorityManagementsByTicketIdAndToAccountIn(@Param("ticketId") Long ticketId, @Param("lstToAccounts") List<String> lstToAccounts);

    List<AuthorityManagement> findByTicketIdAndTypeAndTaskDefKeyOrderByCreateAtDesc(Long ticketId, Integer type, String taskDefKey);

    List<AuthorityManagement> findByTicketIdAndTypeOrderByCreateAtDesc(Long ticketId, Integer type);

    @Query(value = "select a.ticketId from AuthorityManagement a where a.type = 3")
    List<Long> getTicketId();

    @Query(value = "select a from AuthorityManagement a where a.ticketId = :ticketId and a.type = :type")
    AuthorityManagement getByTicketId(Long ticketId, Integer type);

    @Modifying
    @Query(value = "update AuthorityManagement a set a.fromAccount = :fromAccount,a.toAccount = :toAccount, a.userUpdate = :account,a.updateAt = :time where a.ticketId = :id and a.type = :type")
    void updateAuthority(Long id, String fromAccount, String toAccount, String account, Date time, Integer type);

}
