package vn.fis.eapprove.business.domain.priority.service;


import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;

import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import vn.fis.eapprove.business.domain.authority.service.AuthService;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcInstRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcdefRepository;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagementHistory;
import vn.fis.eapprove.business.domain.priority.repository.PriorityHistoryRepository;
import vn.fis.eapprove.business.domain.priority.repository.PriorityManagementRepository;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.repository.ShareUserRepository;
import vn.fis.eapprove.business.domain.sharedUser.service.ShareUserManager;
import vn.fis.eapprove.business.domain.system.repository.SystemGroupRepository;
import vn.fis.eapprove.business.dto.PageSearchDto;
import vn.fis.eapprove.business.dto.SearchPriorityDto;
import vn.fis.eapprove.business.exception.ErrorMessage;
import vn.fis.eapprove.business.model.request.PriorityRequest;
import vn.fis.eapprove.business.model.response.NameAndCodeCompanyResponse;
import vn.fis.eapprove.business.model.response.PriorityResponse;
import vn.fis.eapprove.business.specification.PrioritySpecification;

import vn.fis.eapprove.business.tenant.manager.CustomerService;

import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.business.utils.TimeUtils;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.MapKeyEnum;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ShareUserTypeEnum;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static vn.fis.eapprove.business.constant.Constant.*;

@Slf4j
@Service
@Transactional
public class PriorityManager {

    @Autowired
    ResponseUtils responseUtils;
    @Autowired
    ModelMapper modelMapper;
    @Autowired
    PriorityManagementRepository priorityManagementRepository;
    @Autowired
    CredentialHelper credentialHelper;
    @Autowired
    BpmProcdefRepository bpmProcdefRepository;
    @Autowired
    PrioritySpecification prioritySpecification;
    @Autowired
    BpmProcInstRepository bpmProcInstRepository;
    @Autowired
    RestTemplate restTemplate;
    @Autowired
    SproProperties sproProperties;
    @Autowired
    ShareUserManager shareUserManager;
    @Autowired
    ShareUserRepository shareUserRepository;
    @Autowired
    CustomerService customerService;
    @Autowired
    private PermissionDataManagementRepository permissionDataManagementRepository;

    @Autowired
    MessageSource messageSource;

    @Autowired
    private AuthService authService;
    @Autowired
    private SystemGroupRepository systemGroupRepository;
    @Autowired
    private PriorityHistoryRepository priorityHistoryRepository;


    public List<PriorityResponse> getPriority(String search) {
        try {
            List<PriorityManagement> priorityManagements = priorityManagementRepository.getListPriority(search);
            List<PriorityResponse> listRp = priorityManagements.stream().map(x -> modelMapper.map(x, PriorityResponse.class)).collect(Collectors.toList());
            return listRp;

        } catch (Exception e) {
            log.error("Error getChartInfoRole {}", e.getMessage());
            return null;
        }
    }

    public PageSearchDto search(SearchPriorityDto criteria) {
        try {
            int pageNum = criteria.getPage() - 1;
            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());

            String username = credentialHelper.getJWTPayload().getUsername();
            // Lấy list companyCode cấu hình QL vai trò người dùng
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.PRIORITY_MANAGEMENT.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                criteria.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            Page<PriorityManagement> page = priorityManagementRepository.findAll(
                    prioritySpecification.filter(criteria, lstCompanyCode, username),
                    PageRequest.of(pageNum, criteria.getLimit(),
                            sort));

            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.PRIORITY_MANAGEMENT.code);

            // Lấy list share user
            List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceType(ShareUserTypeEnum.PRIORITY.type);

            // Lấy list locationIds active
            List<Long> lstProcInsts = bpmProcInstRepository.getAllPriorityIds();
            List<Long> lstProcDefs = bpmProcdefRepository.getAllPriorityIds();
            List<Long> lstActive = new ArrayList<>();
            lstActive.addAll(lstProcInsts);
            lstActive.addAll(lstProcDefs);
            lstActive = lstActive.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());

            List<PriorityResponse> priorityResponses = new ArrayList<>();
            for (PriorityManagement priorityManagement : page.getContent()) {
                Integer status = 0;
                if (lstActive.contains(priorityManagement.getId())) {
                    status = 1;
                }
//                Integer isActive = 0;
//                if (lstProcDefs.contains(priorityManagement.getId())) {
//                    isActive = 1;
//                }

                List<String> listEmail = new ArrayList<>();

                if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                    listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(priorityManagement.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
                }

                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(priorityManagement.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());

                PriorityResponse priorityResponse = PriorityResponse.builder()
                        .id(priorityManagement.getId())
                        .code(priorityManagement.getCode())
                        .name(priorityManagement.getName())
                        .color(priorityManagement.getColor())
                        .alertTimeComplete(priorityManagement.getAlertTimeComplete())
                        .shareWith(listEmail)
//                        .status(priorityManagement.getStatus())
                        .status(status)
                        .slaValue(priorityManagement.getSlaValue())
                        .createdUser(priorityManagement.getCreatedUser())
                        .createdDate(TimeUtils.localDateTimeToString(priorityManagement.getCreatedDate(), LOCALFORMAT))
                        .modifiedUser(priorityManagement.getModifiedUser() == null ? "" : priorityManagement.getModifiedUser())
                        .modifiedDate(priorityManagement.getModifiedDate() == null ? "" : TimeUtils.localDateTimeToString(priorityManagement.getModifiedDate(), LOCALFORMAT))
                        .description(priorityManagement.getDescription())
                        .reminderType(priorityManagement.getReminderType())
                        .reminderBeingType(priorityManagement.getReminderBeingType())
                        .reminderTime(priorityManagement.getReminderTime())
                        .reminderBeingTime(priorityManagement.getReminderBeingTime())
                        .reminderValue(priorityManagement.getReminderValue())
                        .reminderBeingValue(priorityManagement.getReminderBeingValue())
                        .configReminder(priorityManagement.getConfigReminder())
                        .applyFor(companyCodes)
                        .companyCode(priorityManagement.getCompanyCode())
                        .companyName(priorityManagement.getCompanyName())
                        .activeStatus(priorityManagement.getActiveStatus() == null ? 0 : priorityManagement.getActiveStatus())
                        .build();
                priorityResponses.add(priorityResponse);

            }
            return PageSearchDto.builder().content(priorityResponses)
                    .number(page.getNumber() + 1)
                    .numberOfElements(page.getNumberOfElements())
                    .page(page.getNumber() + 1)
                    .limit(page.getSize())
                    .totalPages(page.getTotalPages())
                    .totalElements(page.getTotalElements())
                    .sortBy(criteria.getSortBy())
                    .sortType(criteria.getSortType())
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    public List<PriorityResponse> searchFilter(SearchPriorityDto criteria) {
        try {
//            Integer pageNum = criteria.getPage() - 1;
//            Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());

            String username = credentialHelper.getJWTPayload().getUsername();
            // Lấy list companyCode cấu hình QL vai trò người dùng
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.PRIORITY_MANAGEMENT.tableName, username);
            // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
            if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
                criteria.setLstGroupPermissionId(lstGroupPermissionId);
                // get list company code role admin member
                List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
                criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
            }

            List<PriorityManagement> page = priorityManagementRepository.findAll(
                    prioritySpecification.filter(criteria, lstCompanyCode, username)
//                    , PageRequest.of(pageNum, criteria.getLimit(), sort)
            );

            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.PRIORITY_MANAGEMENT.code);

            // Lấy list share user
            List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceType(ShareUserTypeEnum.PRIORITY.type);

            // Lấy list locationIds active
            List<Long> lstProcInsts = bpmProcInstRepository.getAllPriorityIds();
            List<Long> lstProcDefs = bpmProcdefRepository.getAllPriorityIds();
            List<Long> lstActive = new ArrayList<>();
            lstActive.addAll(lstProcInsts);
            lstActive.addAll(lstProcDefs);
            lstActive = lstActive.stream().filter(e -> e != null).distinct().collect(Collectors.toList());

            List<PriorityResponse> priorityResponses = new ArrayList<>();
            for (PriorityManagement priorityManagement : page) {
//                List<BpmProcInst> bpmProcInsts = bpmProcInstRepository.getAllPriorityOnBpmProcInst(priorityManagement.getId());
//                List<BpmProcdef> bpmProcdefs = bpmProcdefRepository.getAllPriorityOnBpmProdef(priorityManagement.getId());
//                List<Long> listPriorityBpmProcDef = bpmProcdefs.stream().map(BpmProcdef::getPriorityId).collect(Collectors.toList());
//                List<Long> listPriorityBpmProcInst = bpmProcInsts.stream().map(BpmProcInst::getPriorityId).collect(Collectors.toList());
//                if (listPriorityBpmProcDef.contains(priorityManagement.getId()) || listPriorityBpmProcInst.contains(priorityManagement.getId())) {
//                    priorityManagement.setStatus(1);
//                }
                Integer status = 0;
                if (lstActive.contains(priorityManagement.getId())) {
                    status = 1;
                }

                List<String> listEmail = new ArrayList<>();
                ;
                if (!ValidationUtils.isNullOrEmpty(sharedUsers)) {
                    listEmail = sharedUsers.stream().filter(res -> res.getReferenceId().equals(priorityManagement.getId())).map(SharedUser::getEmail).collect(Collectors.toList());
                }

                List<String> companyCodes = permissionDataManagements.stream().filter(data -> data.getTypeId().equals(priorityManagement.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());

                PriorityResponse priorityResponse = PriorityResponse.builder()
                        .id(priorityManagement.getId())
                        .code(priorityManagement.getCode())
                        .name(priorityManagement.getName())
                        .color(priorityManagement.getColor())
                        .alertTimeComplete(priorityManagement.getAlertTimeComplete())
                        .shareWith(listEmail)
//                        .status(priorityManagement.getStatus())
                        .status(status)
                        .slaValue(priorityManagement.getSlaValue())
                        .createdUser(priorityManagement.getCreatedUser())
                        .createdDate(TimeUtils.localDateTimeToString(priorityManagement.getCreatedDate(), LOCALFORMAT))
                        .modifiedUser(priorityManagement.getModifiedUser() == null ? "" : priorityManagement.getModifiedUser())
                        .modifiedDate(priorityManagement.getModifiedDate() == null ? "" : TimeUtils.localDateTimeToString(priorityManagement.getModifiedDate(), LOCALFORMAT))
                        .description(priorityManagement.getDescription())
                        .reminderType(priorityManagement.getReminderType())
                        .reminderBeingType(priorityManagement.getReminderBeingType())
                        .reminderTime(priorityManagement.getReminderTime())
                        .reminderBeingTime(priorityManagement.getReminderBeingTime())
                        .reminderValue(priorityManagement.getReminderValue())
                        .reminderBeingValue(priorityManagement.getReminderBeingValue())
                        .configReminder(priorityManagement.getConfigReminder())
                        .applyFor(companyCodes)
                        .companyCode(priorityManagement.getCompanyCode())
                        .companyName(priorityManagement.getCompanyName())
                        .activeStatus(priorityManagement.getActiveStatus() == null ? 0 : priorityManagement.getActiveStatus())
                        .build();
                priorityResponses.add(priorityResponse);

            }
            return priorityResponses;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    public String createPriority(PriorityRequest priorityRequest) {
        try {
            PriorityManagement priorityManagement = new PriorityManagement();
            priorityManagement.setCode(getCode(credentialHelper.getJWTPayload().getEmail()));
            priorityManagement.setColor(priorityRequest.getColor());
            priorityManagement.setAlertTimeComplete(priorityRequest.getAlertTimeComplete());
            priorityManagement.setStatus((priorityRequest.getStatus() != null) ? priorityRequest.getStatus() : 0);
            priorityManagement.setName(priorityRequest.getName());
            priorityManagement.setSlaValue(priorityRequest.getSlaValue());
            priorityManagement.setCreatedDate(LocalDateTime.now());
            priorityManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            priorityManagement.setDescription(priorityRequest.getDescription());

            priorityManagement.setReminderBeingTime(priorityRequest.getReminderBeingTime());
            priorityManagement.setReminderTime(priorityRequest.getReminderTime());
            priorityManagement.setReminderBeingType(priorityRequest.getReminderBeingType());
            priorityManagement.setReminderType(priorityRequest.getReminderType());
            priorityManagement.setReminderValue(priorityRequest.getReminderValue());
            priorityManagement.setReminderBeingValue(priorityRequest.getReminderBeingValue());
            priorityManagement.setConfigReminder(priorityRequest.getConfigReminder());
            priorityManagement.setActiveStatus(0);
            List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
            for (NameAndCodeCompanyResponse response : listCompanyCodeAndName) {
                priorityManagement.setCompanyCode(response.getCompanyCode());
                priorityManagement.setCompanyName(response.getCompanyName());
            }
            PriorityManagementHistory priorityManagementHistory = modelMapper.map(priorityManagement, PriorityManagementHistory.class);

            PriorityManagement newPriorityManagement = priorityManagementRepository.save(priorityManagement);

            priorityManagementHistory.setPriorityId(newPriorityManagement.getId());
            priorityManagementHistory.setVersion(1);
            priorityHistoryRepository.save(priorityManagementHistory);

            // Lưu phân quyền dữ liệu
            if (!ValidationUtils.isNullOrEmpty(priorityRequest.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : priorityRequest.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(priorityManagement.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.PRIORITY_MANAGEMENT.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                permissionDataManagementRepository.saveAll(permissionDataManagements);
            }

            if (!ValidationUtils.isNullOrEmpty(priorityRequest.getShareWith())) {
                List<SharedUser> sharedUsers = new ArrayList<>();
                for (String shareWith : priorityRequest.getShareWith()) {
                    SharedUser sharedUser = new SharedUser();
                    sharedUser.setReferenceId(priorityManagement.getId());
                    sharedUser.setReferenceType(ShareUserTypeEnum.PRIORITY.type);
                    sharedUser.setEmail(shareWith);
                    sharedUsers.add(sharedUser);
                }
                shareUserRepository.saveAll(sharedUsers);
            }

            return MESSAGE_SUCCESS;

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return MESSAGE_FAIL;
        }
    }

    public String updatePriority(PriorityRequest priorityRequest) {
        try {

            PriorityManagement priorityManagement = priorityManagementRepository.findById(priorityRequest.getId()).get();
            priorityManagement.setDescription(priorityRequest.getDescription());
            priorityManagement.setColor(priorityRequest.getColor());
            priorityManagement.setName(priorityRequest.getName());
            priorityManagement.setCode(getCode(credentialHelper.getJWTPayload().getEmail()));
            priorityManagement.setAlertTimeComplete(priorityRequest.getAlertTimeComplete());
            priorityManagement.setSlaValue(priorityRequest.getSlaValue());
            priorityManagement.setModifiedDate(LocalDateTime.now());
            priorityManagement.setModifiedUser(credentialHelper.getJWTPayload().getUsername());

            priorityManagement.setReminderBeingTime(priorityRequest.getReminderBeingTime());
            priorityManagement.setReminderTime(priorityRequest.getReminderTime());
            priorityManagement.setReminderBeingType(priorityRequest.getReminderBeingType());
            priorityManagement.setReminderType(priorityRequest.getReminderType());
            priorityManagement.setReminderValue(priorityRequest.getReminderValue());
            priorityManagement.setReminderBeingValue(priorityRequest.getReminderBeingValue());
            priorityManagement.setConfigReminder(priorityRequest.getConfigReminder());

            // share user
            shareUserRepository.deleteAllByReferenceIdAndReferenceType(priorityManagement.getId(), ShareUserTypeEnum.PRIORITY.type);
            if (!ValidationUtils.isNullOrEmpty(priorityRequest.getShareWith())) {
                List<SharedUser> sharedUsers = new ArrayList<>();
                for (String shareWith : priorityRequest.getShareWith()) {
                    SharedUser sharedUser = new SharedUser();
                    sharedUser.setReferenceId(priorityManagement.getId());
                    sharedUser.setReferenceType(ShareUserTypeEnum.PRIORITY.type);
                    sharedUser.setEmail(shareWith);
                    sharedUsers.add(sharedUser);
                }
                shareUserRepository.saveAll(sharedUsers);
            }

            // Lưu phân quyền dữ liệu
            // Xóa data cũ
            List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(priorityManagement.getId(), PermissionDataConstants.Type.PRIORITY_MANAGEMENT.code);
            if (!ValidationUtils.isNullOrEmpty(oldData)) {
                permissionDataManagementRepository.deleteAll(oldData);
            }
            if (!ValidationUtils.isNullOrEmpty(priorityRequest.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : priorityRequest.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(priorityManagement.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.PRIORITY_MANAGEMENT.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                permissionDataManagementRepository.saveAll(permissionDataManagements);
            }

            PriorityManagement newPriorityManagement = priorityManagementRepository.save(priorityManagement);

            // update version history
            PriorityManagementHistory priorityManagementHistory = modelMapper.map(newPriorityManagement, PriorityManagementHistory.class);
            priorityManagementHistory.setPriorityId(newPriorityManagement.getId());
            priorityManagementHistory.setVersion(priorityHistoryRepository.findByPriorityId(newPriorityManagement.getId()).size() + 1);
            priorityHistoryRepository.save(priorityManagementHistory);

            return MESSAGE_SUCCESS;

        } catch (Exception e) {
            e.printStackTrace();
            return MESSAGE_FAIL;
        }
    }

    public int deletePriority(List<Long> listId) {
        try {
            for (Long idPriority : listId) {
                priorityManagementRepository.deleteById(idPriority);
            }
            return SUCCESS;

        } catch (Exception e) {
            log.info("Delete failed");
            return FAILED;
        }
    }

    public boolean CheckExistName(String namePriority) {
        try {
            List<PriorityManagement> groupSystems = priorityManagementRepository.CheckExistName(namePriority);
            if (groupSystems.size() > 0) {
                return true;
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public Boolean checkNameExits(String namePriority, Long id) {
        if (id != null) {
            PriorityManagement priorityManagement = priorityManagementRepository.findById(id).get();
            if (!namePriority.equalsIgnoreCase(priorityManagement.getName())) {
                if (CheckExistName(namePriority)) {
                    return true;
                }
            }

        } else {
            if (CheckExistName(namePriority)) {
                return true;
            }
        }
        return false;
    }


    public String getCode(String email) {
        try {
            String token = credentialHelper.getJWTToken();
//            //String realm = credentialHelper.getRealm();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("key_role", "public");
//            //headers.set("realm", realm);
            headers.set("Authorization", "Bearer " + token);
            HttpEntity<?> requestEntity = new HttpEntity<>(headers);
            String url = UriComponentsBuilder.fromHttpUrl(sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key) + "/userInfo/getCodePriorityByChartId").queryParam("email", email).encode().toUriString();
            ResponseEntity<ResponseDto> responseCustomer = restTemplate.exchange(url,
                    HttpMethod.GET, requestEntity, new ParameterizedTypeReference<>() {
                    });
            ResponseDto responseDto = responseCustomer.getBody();
            String chartDtoList = String.valueOf(responseDto.getData());
            return chartDtoList;
        } catch (Exception e) {
            log.error("Error getCode {}", e.getMessage());
            return null;
        }
    }

    public List<String> getAlluserInfo() {
        try {
            List<String> response = customerService.listAllUserInfo();
            return response;
        } catch (Exception e) {
            throw new ErrorMessage("");
        }
    }

    public Optional<PriorityManagement> getById(Long id) {
        return priorityManagementRepository.findById(id);
    }

    public List<PriorityManagement> getAllPriotity() {
        return priorityManagementRepository.findAll();
    }

    public List<PriorityManagement> findByStatus(Integer status) {
        return priorityManagementRepository.findAllByActiveStatus(status);
    }

    public Object getAllSystemGroup()  {
        List<Map<String, Object>> lstResponse = new ArrayList<>();

        SearchPriorityDto criteria = new SearchPriorityDto();
        criteria.setPage(1);
        criteria.setLimit(999999);
        criteria.setSortBy("id");
        criteria.setSortType("DESC");
        int pageNum = criteria.getPage() - 1;
        Sort sort = responseUtils.getSort(criteria.getSortBy(), criteria.getSortType());

        String username = credentialHelper.getJWTPayload().getUsername();
        // Lấy list companyCode cấu hình QL vai trò người dùng
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(username);

        // Phân quyền theo nhóm
        List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.PRIORITY_MANAGEMENT.tableName, username);
        // có phân quyền theo nhóm - ưu tiên quyền theo nhóm
        if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId)) {
            criteria.setLstGroupPermissionId(lstGroupPermissionId);
            // get list company code role admin member
            List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(username);
            criteria.setListCompanyCodeMemberAdmin(lstCompanyCodeMemberAdmin);
        }

        Page<PriorityManagement> page = priorityManagementRepository.findAll(
                prioritySpecification.filter(criteria, lstCompanyCode, username),
                PageRequest.of(pageNum, criteria.getLimit(), sort));
        List<PriorityManagement> priorityManagers = page.getContent();
        for (PriorityManagement priorityManagement : priorityManagers) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", priorityManagement.getId());
            map.put("name", priorityManagement.getName());
            map.put("companyCode", priorityManagement.getCompanyCode());
            map.put("companyName", priorityManagement.getCompanyName());
            lstResponse.add(map);
        }

        return lstResponse;
    }

    public ResponseDto<?> activePriority(List<Long> id, Boolean isActive) {
        if (!isActive) {
            if (!priorityManagementRepository.checkPriorityExistInProcdef(id)) {
                priorityManagementRepository.updateStatusById(id, 0);
                return ResponseDto.builder().message(messageSource.getMessage("message.priority-management.activePriority.success", null, Locale.getDefault())).build();
            }
            return ResponseDto.builder().message(messageSource.getMessage("message.priority-management.activePriority.fail", null, Locale.getDefault())).build();
        } else {
            priorityManagementRepository.updateStatusById(id, 1);
            return ResponseDto.builder().message(messageSource.getMessage("message.priority-management.activePriority.active", null, Locale.getDefault())).build();

        }
    }
}
