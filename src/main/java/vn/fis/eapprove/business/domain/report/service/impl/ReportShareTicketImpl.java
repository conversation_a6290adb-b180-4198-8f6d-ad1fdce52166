package vn.fis.eapprove.business.domain.report.service.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.Tuple;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.domain.report.service.ReportShareTicketService;
import vn.fis.eapprove.business.model.request.ReportShareTicketRequest;
import vn.fis.eapprove.business.model.response.ReportShareTicketResponse;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class ReportShareTicketImpl implements ReportShareTicketService {

    private EntityManager entityManager;

    @Override
    public List<ReportShareTicketResponse> getReportShareTicket(ReportShareTicketRequest request) {
        List<ReportShareTicketResponse> lstResponse = new ArrayList<>();
        EntityManager em = null;
        try {
            em = entityManager.getEntityManagerFactory().createEntityManager();
            String mainQuery = "select distinct share.name    as name, " +
                    "                detailService.name       as serviceName, " +
                    "                detailShareUser.name     as shareUserName, " +
                    "                detailAssignee.name      as assigneeName, " +
                    "                detailChartNodeCode.name as chartNodeName, " +
                    "                detailCompanyCode.name   as companyName, " +
                    "                detailCreatedUser.name   as createdUserName, " +
                    "                share.status             as status " +
                    " from manage_share_ticket share " +
                    "         join manage_share_ticket_detail detailService " +
                    "              on share.id = detailService.manage_share_ticket_id and detailService.type = 'service' " +
                    "         join manage_share_ticket_detail detailShareUser " +
                    "              on share.id = detailShareUser.manage_share_ticket_id and detailShareUser.type = 'shareUser' " +
                    "         left join manage_share_ticket_detail detailAssignee " +
                    "                   on share.id = detailAssignee.manage_share_ticket_id and detailAssignee.type = 'assignee' " +
                    "         left join manage_share_ticket_detail detailChartNodeCode " +
                    "                   on share.id = detailChartNodeCode.manage_share_ticket_id and " +
                    "                      detailChartNodeCode.type = 'chartNodeCode' " +
                    "         left join manage_share_ticket_detail detailCompanyCode " +
                    "                   on share.id = detailCompanyCode.manage_share_ticket_id and detailCompanyCode.type = 'companyCode' " +
                    "         left join manage_share_ticket_detail detailCreatedUser " +
                    "                   on share.id = detailCreatedUser.manage_share_ticket_id and detailCreatedUser.type = 'createdUser' " +
                    " where 1 = 1 ";

            StringBuilder queryBuilder = new StringBuilder();
            queryBuilder.append(mainQuery);
            if (!ValidationUtils.isNullOrEmpty(request.getListServiceId())) {
                queryBuilder.append(" and detailService.value in (:listServiceId) ");
            }
            if (!ValidationUtils.isNullOrEmpty(request.getListShareUser())) {
                queryBuilder.append(" and detailShareUser.value in (:listShareUser) ");
            }
            if (!ValidationUtils.isNullOrEmpty(request.getListAssignee())) {
                queryBuilder.append(" and detailAssignee.value in (:listAssignee) ");
            }
            if (!ValidationUtils.isNullOrEmpty(request.getListChartNodeCode())) {
                queryBuilder.append(" and detailChartNodeCode.value in (:listChartNodeCode) ");
            }
            if (!ValidationUtils.isNullOrEmpty(request.getListCompanyCode())) {
                queryBuilder.append(" and detailCompanyCode.value in (:listCompanyCode) ");
            }
            if (!ValidationUtils.isNullOrEmpty(request.getListCreatedUser())) {
                queryBuilder.append(" and detailCreatedUser.value in (:listCreatedUser) ");
            }
            if (!ValidationUtils.isNullOrEmpty(request.getFromDate())) {
                queryBuilder.append(" and share.created_date >= :fromDate ");
            }
            if (!ValidationUtils.isNullOrEmpty(request.getToDate())) {
                LocalDate date = LocalDate.parse(request.getToDate());
                String newToDate = date.plusDays(1).toString();
                request.setToDate(newToDate);
                queryBuilder.append(" and share.created_date < :toDate ");
            }

            // create query
            Query query = em.createNativeQuery(queryBuilder.toString(), Tuple.class);
            if (!ValidationUtils.isNullOrEmpty(request.getListServiceId())) {
                query.setParameter("listServiceId", request.getListServiceId());
            }
            if (!ValidationUtils.isNullOrEmpty(request.getListShareUser())) {
                query.setParameter("listShareUser", request.getListShareUser());
            }
            if (!ValidationUtils.isNullOrEmpty(request.getListAssignee())) {
                query.setParameter("listAssignee", request.getListAssignee());
            }
            if (!ValidationUtils.isNullOrEmpty(request.getListChartNodeCode())) {
                query.setParameter("listChartNodeCode", request.getListChartNodeCode());
            }
            if (!ValidationUtils.isNullOrEmpty(request.getListCompanyCode())) {
                query.setParameter("listCompanyCode", request.getListCompanyCode());
            }
            if (!ValidationUtils.isNullOrEmpty(request.getListCreatedUser())) {
                query.setParameter("listCreatedUser", request.getListCreatedUser());
            }
            if (!ValidationUtils.isNullOrEmpty(request.getFromDate())) {
                query.setParameter("fromDate", request.getFromDate());
            }
            if (!ValidationUtils.isNullOrEmpty(request.getToDate())) {
                query.setParameter("toDate", request.getToDate());
            }

            List<Tuple> resultList = query.getResultList();
            resultList.forEach(tuple -> {
                lstResponse.add(tupleToResponse(tuple));
            });

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (em != null) {
                em.close();
            }
        }

        return lstResponse;
    }

    private ReportShareTicketResponse tupleToResponse(Tuple tuple) {
        ReportShareTicketResponse response = new ReportShareTicketResponse();
        response.setName(tuple.get("name", String.class));
        response.setServiceName(tuple.get("serviceName", String.class));
        response.setShareUserName(tuple.get("shareUserName", String.class));
        response.setAssigneeName(tuple.get("assigneeName") == null ? "" : tuple.get("assigneeName", String.class));
        response.setChartNodeName(tuple.get("chartNodeName") == null ? "" : tuple.get("chartNodeName", String.class));
        response.setCompanyName(tuple.get("companyName") == null ? "" : tuple.get("companyName", String.class));
        response.setCreatedUserName(tuple.get("createdUserName") == null ? "" : tuple.get("createdUserName", String.class));
        response.setStatus(tuple.get("status", String.class));
        return response;
    }
}
