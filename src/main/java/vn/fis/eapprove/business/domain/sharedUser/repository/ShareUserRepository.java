package vn.fis.eapprove.business.domain.sharedUser.repository;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;


import java.util.List;

public interface ShareUserRepository extends JpaRepository<SharedUser, Long>, JpaSpecificationExecutor<SharedUser> {

    SharedUser findAllByReferenceIdAndReferenceType(Long id, String type);

    List<SharedUser> getSharedUsersByReferenceType(String type);

    List<SharedUser> getSharedUsersByReferenceIdAndReferenceType(Long id, String type);

    List<SharedUser> getSharedUsersByReferenceIdInAndReferenceType(List<Long> id, String type);

    List<SharedUser> getSharedUsersByReferenceTypeAndEmailIn(String type, List<String> email);

    void deleteAllByReferenceIdAndReferenceType(Long id, String type);

    @Query("select a.email from SharedUser a where a.referenceType = :type and a.referenceId = :referenceId")
    List<String> getShareUserByReferenceId(String type, Long referenceId);

    List<SharedUser> getSharedUsersByReferenceIdAndReferenceTypeAndAddition(Long id, String type, String addition);
}
