package vn.fis.eapprove.business.domain.notification.repository;


import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.notification.entity.Notification;


@Repository
public interface NotificationRepository extends JpaRepository<Notification, Long> {
    Notification getAllByEmail(String email);

    @Query("UPDATE Notification n set n.status = 1 where n.id in(:ids)")
    void readNotificationByIds(@Param("ids") Long[] ids);

    @Query("UPDATE Notification n set n.status = 2 where n.id in(:ids)")
    void deleteNotificationByIds(@Param("ids") Long[] ids);
}
