package vn.fis.eapprove.business.domain.report.service;


import vn.fis.eapprove.business.dto.filter.ReportProcInstFilter;
import vn.fis.eapprove.business.model.response.ReportProcInstByChartNodeResponse;
import vn.fis.eapprove.business.model.response.ReportProcInstByGroupResponse;

public interface ReportProcInstService {
    ReportProcInstByGroupResponse getReportProcInstByGroup(ReportProcInstFilter filter, String username);

    ReportProcInstByGroupResponse getReportTaskByUser(ReportProcInstFilter filter, String username);

    ReportProcInstByChartNodeResponse getReportProcInstByChartNode(ReportProcInstFilter filter, String username);

}