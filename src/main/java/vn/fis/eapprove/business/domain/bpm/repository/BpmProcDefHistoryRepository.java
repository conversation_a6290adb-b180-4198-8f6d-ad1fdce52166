package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcDefHistory;


import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface BpmProcDefHistoryRepository extends JpaRepository<BpmProcDefHistory, Long>, JpaSpecificationExecutor<BpmProcDefHistory> {
    @Query("select count(bh.id) from BpmProcDefHistory bh where bh.orgProcessId = :orgProcessId")
    Long countByOrgProcessId(Long orgProcessId);

    List<BpmProcDefHistory> findByOrgProcessId(Long orgProcessId);

    @Query(value = "select bh.* from bpm_procdef_history bh where bh.proc_def_id = :procDefId order by bh.id desc limit 1", nativeQuery = true)
    BpmProcDefHistory findByProcDefId(String procDefId);

    @Query(value = "select bh.* from bpm_procdef_history bh " +
            " where bh.org_process_id = :processId" +
            " and bh.created_date > :createdTime " +
            " order by bh.id asc limit 1", nativeQuery = true)
    BpmProcDefHistory findVer0ByProcessIdAndTicketCreatedTime(Long processId, LocalDateTime createdTime);

    BpmProcDefHistory getBpmProcDefHistoryById(Long id);
}
