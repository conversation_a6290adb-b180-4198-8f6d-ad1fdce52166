package vn.fis.eapprove.business.domain.bpm.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@Entity
@Table(name = "bpm_history")
public class BpmHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "TICKET_ID")
    private Long ticketId;

    @Column(name = "PROC_INST_ID")
    private String procInstId;

    @Column(name = "TASK_INST_ID")
    private String taskInstId;

    @Column(name = "FROM_TASK")
    private String fromTask;

    @Column(name = "TO_TASK")
    private String toTask;

    @Column(name = "FROM_TASK_KEY")
    private String fromTaskKey;

    @Column(name = "TO_TASK_KEY")
    private String toTaskKey;

    @Column(name = "RECEIVED_TIME")
    private LocalDateTime receivedTime;

    @Column(name = "CREATED_TIME")
    private Date createdTime;

    @Column(name = "ACTION")
    private String action;

    @Column(name = "TASK_DEF_KEY")
    private String taskDefKey;

    @Column(name = "ACTION_USER")
    private String actionUser;

    @Column(name = "NOTE")
    private String note;

    @Column(name = "AFFECTED_TASK")
    private String affectedTask;

    @Column(name = "TASK_TYPE")
    private String taskType;

    @Column(name = "OLD_TASK_INST_ID")
    private String oldTaskId;

    @Column(name = "OLD_PROCINST_ID")
    private String oldProcInstId;

    @Column(name = "ATTACH_FILES")
    private String attachFiles;

    @Column(name = "ATTACH_FILES_NAME")
    private String attachFilesName;

    @Column(name = "ATTACH_FILES_SIZE")
    private String attachFilesSize;

    @Column(name = "TASK_ASSIGNEE")
    private String taskAssignee;

    @Column(name = "OLD_DEFAULT_FIELD")
    private String oldDefaultField;

    @Column(name = "ACTION_USER_INFO")
    private String actionUserInfo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TICKET_ID", insertable = false, updatable = false)
    @JsonIgnore
    private BpmProcInst bpmProcInst;
}
