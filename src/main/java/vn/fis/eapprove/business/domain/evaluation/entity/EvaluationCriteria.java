package vn.fis.eapprove.business.domain.evaluation.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Entity
@Table(name = "evaluation_criteria")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@NamedEntityGraph(
        name = "EvaluationCriteria.evaluationDepartments",
        attributeNodes = {
                @NamedAttributeNode("evaluationDepartments")
        }
)
public class EvaluationCriteria {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "review_item")
    private String reviewItem;

    @Column(name = "name")
    private String name;

    @Column(name = "description")
    private String description;

    @Column(name = "status")
    private String status;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "updated_user")
    private String updatedUser;

    @OneToMany(mappedBy = "evaluationCriteria")
    @JsonIgnore
    private Set<EvaluationDepartment> evaluationDepartments;

    @Transient
    private List<String> lstDepartments;

    @OneToMany(mappedBy = "evaluationCriteria")
    @JsonIgnore
    private Set<PermissionDataManagement> permissionDataManagements;
    @Column(name = "company_code")
    private String companyCode;
    @Column(name = "company_name")
    private String companyName;

}
