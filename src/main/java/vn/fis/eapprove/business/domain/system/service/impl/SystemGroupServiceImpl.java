package vn.fis.eapprove.business.domain.system.service.impl;

import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.MessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.api.service.ApiManager;
import vn.fis.eapprove.business.domain.authority.service.AuthService;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcdefManager;
import vn.fis.eapprove.business.domain.bpm.service.BpmTemplatePrintManager;
import vn.fis.eapprove.business.domain.codeGen.service.impl.CodeGenConfigServiceImpl;
import vn.fis.eapprove.business.domain.evaluation.service.impl.EvaluationCriteriaServiceImpl;
import vn.fis.eapprove.business.domain.notification.service.impl.NotificationTemplateServiceImpl;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.priority.service.PriorityManager;
import vn.fis.eapprove.business.domain.servicePackage.service.ServicePackageManager;
import vn.fis.eapprove.business.domain.submission.service.SubmissionTypeManager;
import vn.fis.eapprove.business.domain.system.entity.SystemGroup;
import vn.fis.eapprove.business.domain.system.entity.SystemGroupHistory;
import vn.fis.eapprove.business.domain.system.repository.SystemGroupHistoryRepository;
import vn.fis.eapprove.business.domain.system.repository.SystemGroupRepository;
import vn.fis.eapprove.business.domain.system.service.SystemGroupService;
import vn.fis.eapprove.business.domain.template.service.TemplateManager;
import vn.fis.eapprove.business.dto.AccountInfoDto;
import vn.fis.eapprove.business.model.request.AddSystemGroupRequest;
import vn.fis.eapprove.business.model.request.SystemGroupField;
import vn.fis.eapprove.business.model.request.SystemGroupRequest;
import vn.fis.eapprove.business.model.response.NameAndCodeCompanyResponse;
import vn.fis.eapprove.business.model.response.SystemGroupResponse;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.eapprove.business.model.request.SystemGroupSearch;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class SystemGroupServiceImpl implements SystemGroupService {
    private final SystemGroupRepository systemGroupRepository;
    private final CredentialHelper credentialHelper;
    private final PermissionDataManagementRepository permissionDataManagementRepository;
    private final TemplateManager templateManager;
    private final CustomerService customerService;
    private final BpmProcdefManager bpmProcdefManager;
    private final ServicePackageManager servicePackageManager;
    private final BpmTemplatePrintManager bpmTemplatePrintManager;
    //    private final MasterDataManager masterDataManager;
    private final PriorityManager priorityManager;
    private final NotificationTemplateServiceImpl notificationTemplateServiceImpl;
    private final ApiManager apiManager;
    private final SubmissionTypeManager submissionTypeManager;
    private final EvaluationCriteriaServiceImpl evaluationCriteriaServiceImpl;
    private final CodeGenConfigServiceImpl codeGenConfigServiceImpl;
    private final MessageSource messageSource;
    private final SystemGroupHistoryRepository systemGroupHistoryRepository;
    private final AuthService authService;

    public SystemGroupServiceImpl(SystemGroupRepository systemGroupRepository,
                                  CredentialHelper credentialHelper,
                                  PermissionDataManagementRepository permissionDataManagementRepository,
                                  TemplateManager templateManager,
                                  BpmProcdefManager bpmProcdefManager,
                                  ServicePackageManager servicePackageManager,
                                  BpmTemplatePrintManager bpmTemplatePrintManager,
                                  CustomerService customerService,
//                                  MasterDataManager masterDataManager,
                                  PriorityManager priorityManager,
                                  NotificationTemplateServiceImpl notificationTemplateServiceImpl,
                                  ApiManager apiManager,
                                  SubmissionTypeManager submissionTypeManager,
                                  EvaluationCriteriaServiceImpl evaluationCriteriaServiceImpl,
                                  CodeGenConfigServiceImpl codeGenConfigServiceImpl,
                                  MessageSource messageSource,
                                  SystemGroupHistoryRepository systemGroupHistoryRepository,
                                  AuthService authService) {
        this.systemGroupRepository = systemGroupRepository;
        this.credentialHelper = credentialHelper;
        this.permissionDataManagementRepository = permissionDataManagementRepository;
        this.templateManager = templateManager;
        this.customerService = customerService;
        this.bpmProcdefManager = bpmProcdefManager;
        this.servicePackageManager = servicePackageManager;
        this.bpmTemplatePrintManager = bpmTemplatePrintManager;
//        this.masterDataManager = masterDataManager;
        this.priorityManager = priorityManager;
        this.notificationTemplateServiceImpl = notificationTemplateServiceImpl;
        this.apiManager = apiManager;
        this.submissionTypeManager = submissionTypeManager;
        this.evaluationCriteriaServiceImpl = evaluationCriteriaServiceImpl;
        this.codeGenConfigServiceImpl = codeGenConfigServiceImpl;
        this.messageSource = messageSource;
        this.systemGroupHistoryRepository = systemGroupHistoryRepository;
        this.authService = authService;
    }

    @Override
    public ResponseEntity<?> createUpdate(SystemGroupRequest request)  {
        List<SystemGroup> updateList = new ArrayList<>();
        if (checkDuplicate(request.getGroupType(), request.getName(), request.getId())) {
            return ResponseEntity.badRequest().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.systemGroup.duplicateName", null, Locale.getDefault())).build());
        }
        //History
        SystemGroupHistory history = new SystemGroupHistory();
        Set<String> newGroupValue = new HashSet<>();
        Set<String> deleteGroupValue = new HashSet<>();
        Set<String> newUser = new HashSet<>();
        Set<String> deleteUser = new HashSet<>();
        Map<Long, Object> appList = new HashMap<>();
        StringBuilder changeHistory = new StringBuilder();

        //List danh mục tương ứng
        ResponseEntity<Object> allData = findDataByTableName(request.getGroupType(), null);
        if (allData.getBody() instanceof ArrayList) {
            ((ArrayList<?>) allData.getBody()).forEach(i -> {
                        Map<String, Object> child = (Map) i;
                        if (!ValidationUtils.isNullOrEmpty(child.get("id")))
                            appList.put(Long.valueOf(child.get("id").toString()), child.get("name"));
                    }
            );
        }

        List<Long> removeList= new ArrayList<>();

        Set<String> saveGroupList = new HashSet<>();
        Set<String> saveUserList = new HashSet<>();
        Set<String> oldGroupList = new HashSet<>();
        Set<String> oldUserList = new HashSet<>();
        convertToEntity(request, null, updateList, null, null, changeHistory, appList, history,
                newGroupValue, deleteGroupValue, newUser, deleteUser,
                removeList
        );

        //Remove old system group
        if (!removeList.isEmpty()) {
            systemGroupRepository.deleteAllById(removeList);
        }

        //Get old list update delete - (history
        updateList.forEach(i->{
            if(i.getParent() != null) {
                if(i.getId()==null) {
                    saveGroupList.add(i.getGroupValue());
                    saveUserList.add(i.getUsername());
                }else {
                    oldGroupList.add(i.getGroupValue());
                    oldUserList.add(i.getUsername());
                }
            }
        });

        systemGroupRepository.saveAll(updateList);

        //Handle history
        Set<String> finalDeleteGroupValue = deleteGroupValue;
        newGroupValue = newGroupValue.stream().filter(i->!oldGroupList.contains(i) && saveGroupList.contains(i) && !finalDeleteGroupValue.contains(i)).collect(Collectors.toSet());
        Set<String> finalDeleteUser = deleteUser;
        newUser = newUser.stream().filter(i->!oldUserList.contains(i) && saveUserList.contains(i) && !finalDeleteUser.contains(i)).collect(Collectors.toSet());
        deleteGroupValue = deleteGroupValue.stream().filter(i->!saveGroupList.contains(i) && !oldGroupList.contains(i)).collect(Collectors.toSet());
        deleteUser = deleteUser.stream().filter(i->!saveUserList.contains(i) && !oldUserList.contains(i)).collect(Collectors.toSet());


        //Save history
        handleCreateHistory(request, changeHistory, appList, history, newGroupValue, deleteGroupValue, newUser, deleteUser);
        if (!ValidationUtils.isNullOrEmpty(changeHistory)) {
            String saveData = changeHistory.toString();
            List<AccountInfoDto> allUser = authService.getAccountByRoleAndUsername();
            Set<String> finalNewUser = newUser;
            Set<String> finalDeleteUser1 = deleteUser;
            allUser = allUser.stream().filter(i-> finalNewUser.contains(i.getUsername()) || finalDeleteUser1.contains(i.getUsername())).collect(Collectors.toList());
            for(AccountInfoDto account : allUser){
                saveData = saveData.replaceAll(account.getUsername(),account.getUsername()+" - "+account.getFullname()+" - "+account.getTitleName());
            }
            history.setDescription(saveData);
            systemGroupHistoryRepository.save(history);
        }

        return ResponseEntity.ok(null);
    }


    private void convertToEntity(SystemGroupRequest request, SystemGroup parent, List<SystemGroup> updateList,
                                 SystemGroupField systemGroupField, String username, StringBuilder changeHistory,
                                 Map<Long, Object> appList,
                                 SystemGroupHistory history,
                                 Set<String> newGroupValue,
                                 Set<String> deleteGroupValue,
                                 Set<String> newUser,
                                 Set<String> deleteUser,
                                 List<Long> removeList
    )  {
        SystemGroup systemGroup = new SystemGroup();
        List<SystemGroup> childrenList = new ArrayList<>();
        List<SystemGroup> addList = new ArrayList<>();


        boolean isNew = false;

        //Find exist
        if (request.getId() != null && parent == null) {
            systemGroup = systemGroupRepository.findById(request.getId()).orElse(new SystemGroup());
            systemGroup.setModifiedDate(LocalDateTime.now());
            systemGroup.setModifiedUser(credentialHelper.getJWTPayload().getUsername());
            //Get child
            if (!ValidationUtils.isNullOrEmpty(systemGroup.getId())) {
                childrenList = systemGroupRepository.findAllByParent(systemGroup.getId());
                history.setParent(systemGroup.getId());
            }
        } else {
            systemGroup.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            systemGroup.setCreatedDate(LocalDateTime.now());
            isNew = true;
        }

        //Save parent or new child()
        if (systemGroup.getId() == null && parent == null) { //Save parent
            systemGroup = systemGroupRepository.save(systemGroup);
            history.setParent(systemGroup.getId());
        } else if (systemGroupField != null && parent != null) { // new child()
            systemGroup.setParent(parent.getId());
            systemGroup.setGroupField(systemGroupField.getGroupField());
            systemGroup.setGroupType(systemGroupField.getGroupType());
            systemGroup.setGroupValue(systemGroupField.getGroupValue());
            systemGroup.setUsername(username);

            newGroupValue.add(systemGroupField.getGroupValue());
            newUser.add(username);

        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
        LocalDateTime fromDate = LocalDateTime.parse(request.getFromDate(), formatter);
        LocalDateTime toDate = LocalDateTime.parse(request.getToDate(), formatter);

        //Process common field History before update in parent
        if (parent == null) {
            history.setCreatedDate(LocalDateTime.now());
            history.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            history.setGroupType(request.getGroupType());
            history.setVersion(systemGroupHistoryRepository.findVersionByParentAndGroupType(history.getParent(), history.getGroupType()) + 1);
            if (!isNew) {
                if (!request.getName().equals(systemGroup.getName())) {
                    changeHistory.append(messageSource.getMessage("message.systemGroup.changeName",
                            new Object[]{systemGroup.getName(), request.getName()},
                            Locale.getDefault())).append("\n");
                }
                if (!request.getDescription().equals(systemGroup.getDescription())) {
                    changeHistory.append(messageSource.getMessage("message.systemGroup.changeDescription",
                            new Object[]{systemGroup.getDescription(), request.getDescription()},
                            Locale.getDefault())).append("\n");
                }
                if (
                        !fromDate.equals(systemGroup.getFromDate())
                                || !toDate.equals(systemGroup.getToDate())) {
                    changeHistory.append(messageSource.getMessage("message.systemGroup.changeTime",
                            new Object[]{
                                    formatter.format(systemGroup.getFromDate()) + " - " + formatter.format(systemGroup.getToDate()),
                                    request.getFromDate() + " - " + request.getToDate()
                            },
                            Locale.getDefault())).append("\n");
                }
            }
        }
        //Process History before update

        //Save common field child and parent
        if(request.getId() == null) {
            List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
            if (!ValidationUtils.isNullOrEmpty(listCompanyCodeAndName) && !listCompanyCodeAndName.isEmpty()) {
                systemGroup.setCompanyCode(listCompanyCodeAndName.get(0).getCompanyCode());
                systemGroup.setCompanyName(listCompanyCodeAndName.get(0).getCompanyName());
            }
        }
        systemGroup.setName(request.getName());
        systemGroup.setFromDate(fromDate);
        systemGroup.setToDate(toDate);
        systemGroup.setStatus(request.getStatus());
        systemGroup.setDescription(request.getDescription());
        systemGroup.setGroupType(request.getGroupType());
        //Save common field child and parent


        //Handle input list (Create child)
        if (parent == null && !ValidationUtils.isNullOrEmpty(request.getListUserName()) && !ValidationUtils.isNullOrEmpty(request.getListField())) {
            for (String name : request.getListUserName()) {
                for (SystemGroupField field : request.getListField()) {
                    if (childrenList.isEmpty()) {
                        convertToEntity(request, systemGroup, addList, field, name, changeHistory, appList, history,
                                newGroupValue, deleteGroupValue, newUser, deleteUser,removeList);
                    } else {
                        Optional<SystemGroup> optionalItem = childrenList.stream().filter(i ->
                                i.getUsername() != null && i.getUsername().equals(name)
                                        && i.getGroupField() != null && i.getGroupField().equals(field.getGroupField())
                                        && i.getGroupValue() != null && i.getGroupValue().equals(field.getGroupValue())
                                        && i.getGroupType() != null && i.getGroupType().equals(field.getGroupType())
                        ).findFirst();
                        if (ValidationUtils.isNullOrEmpty(optionalItem.orElse(null))) {
                            convertToEntity(request, systemGroup, addList, field, name, changeHistory, appList,
                                    history,
                                    newGroupValue, deleteGroupValue, newUser, deleteUser,removeList);
                        } else {
                            childrenList.remove(optionalItem.get());
                            SystemGroup update = optionalItem.get();
                            update.setModifiedUser(credentialHelper.getJWTPayload().getUsername());
                            update.setModifiedDate(LocalDateTime.now());
                            updateList.add(update);
                        }

                    }
                }
            }

            // Lưu phân quyền dữ liệu
            if (!ValidationUtils.isNullOrEmpty(request.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                List<PermissionDataManagement> oldDatas = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(systemGroup.getId(), PermissionDataConstants.Type.SYSTEM_GROUP.code);
                ;
                for (String data : request.getApplyFor()) {
                    Optional<PermissionDataManagement> oldData = oldDatas.stream().filter(i -> i.getCompanyCode().equals(data)).findFirst();
                    if (!ValidationUtils.isNullOrEmpty(oldData.orElse(null))) {
                        oldDatas.remove(oldData.get());
                    } else {
                        PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                        permissionDataManagement.setTypeId(systemGroup.getId());
                        permissionDataManagement.setTypeName(PermissionDataConstants.Type.SYSTEM_GROUP.code);
                        permissionDataManagement.setCompanyCode(data);
                        permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                        permissionDataManagement.setCreatedTime(LocalDateTime.now());
                        permissionDataManagements.add(permissionDataManagement);
                    }
                }
                if (!oldDatas.isEmpty())
                    permissionDataManagementRepository.deleteAllById(oldDatas.stream().map(PermissionDataManagement::getId).collect(Collectors.toList()));
                permissionDataManagementRepository.saveAll(permissionDataManagements);
            }

        }

        //Add in loop
        updateList.add(systemGroup);
        updateList.addAll(addList);
        removeList.addAll(childrenList.stream().map(i -> {
            deleteGroupValue.add(i.getGroupValue());
            deleteUser.add(i.getUsername());
            return i.getId();
        }).toList());

    }

    private void handleCreateHistory(SystemGroupRequest request, StringBuilder changeHistory,
                                     Map<Long, Object> appList,
                                     SystemGroupHistory history,
                                     Set<String> newGroupValue,
                                     Set<String> deleteGroupValue,
                                     Set<String> newUser,
                                     Set<String> deleteUser
    ) {
        Set<String> ignoreUser = new HashSet<>(newUser);
        Set<String> ignoreGroupValue = new HashSet<>(newGroupValue);
        ignoreUser.retainAll(deleteUser);
        ignoreGroupValue.retainAll(deleteGroupValue);

        if (request.getId() == null) {
            changeHistory.append(messageSource.getMessage("message.systemGroup.createNew", null, Locale.getDefault())).append("\n");
            history.setVersion(1L);
        } else {
            newGroupValue.forEach(i -> {
                if (!ignoreGroupValue.contains(i)) {
                    changeHistory.append(messageSource.getMessage("message.systemGroup.addList",
                            new Object[]{appList.get(Long.parseLong(i))},
                            Locale.getDefault())).append("\n");
                }
            });
            deleteGroupValue.forEach(i -> {
                if (!ignoreGroupValue.contains(i)) {
                    changeHistory.append(messageSource.getMessage("message.systemGroup.deleteList",
                            new Object[]{appList.get(Long.parseLong(i))},
                            Locale.getDefault())).append("\n");
                }
            });
            newUser.forEach(i -> {
                if (!ignoreUser.contains(i)) {
                    changeHistory.append(messageSource.getMessage("message.systemGroup.addUser",
                            new Object[]{i}, Locale.getDefault())).append("\n");
                }
            });
            deleteUser.forEach(i -> {
                if (!ignoreUser.contains(i)) {
                    changeHistory.append(messageSource.getMessage("message.systemGroup.deleteUser",
                            new Object[]{i}, Locale.getDefault())).append("\n");
                }
            });
        }
    }

    @Override
    public ResponseEntity<SystemGroupResponse> findAllById(Long id)  {
        SystemGroupResponse systemGroupResponse = new SystemGroupResponse();
        SystemGroup parent = systemGroupRepository.findById(id).orElse(null);
        if (parent != null) {
            List<SystemGroup> child = systemGroupRepository.findAllByParent(parent.getId());
            systemGroupResponse.setParent(parent);
            systemGroupResponse.setChild(child);

            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(parent.getId(), PermissionDataConstants.Type.SYSTEM_GROUP.code);
            systemGroupResponse.setApplyFor(permissionDataManagements.stream().map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList()));

            return ResponseEntity.ok(systemGroupResponse);
        }
        return null;
    }

    @Override
    public ResponseEntity<List<SystemGroupResponse>> findByGroupType(SystemGroupSearch request)  {
        List<SystemGroup> allData = systemGroupRepository.findAllByGroupType(request.getGroupType(), request.getSearch(), request.getStatus());
        List<SystemGroup> parentList = allData.stream().filter(i -> i.getParent() == null).collect(Collectors.toList());
        List<SystemGroup> childList = allData.stream().filter(i -> i.getParent() != null).collect(Collectors.toList());

        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(credentialHelper.getJWTPayload().getUsername());

        List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.
                getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.SYSTEM_GROUP.code);

        List<SystemGroupResponse> result = new ArrayList<>();
        parentList.forEach(i -> {
            List<String> applyFor = permissionDataManagements.stream().filter(p -> p.getTypeId().equals(i.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
            List<String> isExist = applyFor.stream().filter(lstCompanyCode::contains).collect(Collectors.toList());
            if(applyFor.contains(CommonConstants.FILTER_SELECT_ALL) || lstCompanyCode.contains(CommonConstants.FILTER_SELECT_ALL) || !isExist.isEmpty()){
                SystemGroupResponse systemGroupResponse = new SystemGroupResponse();
                List<SystemGroup> child = childList.stream().filter(c -> c.getParent().equals(i.getId())).collect(Collectors.toList());
                systemGroupResponse.setParent(i);
                systemGroupResponse.setChild(child);
                systemGroupResponse.setId(i.getId());

                // Lấy list phân quyền dữ liệu
                systemGroupResponse.setApplyFor(applyFor);

                result.add(systemGroupResponse);
            }

        });

        return ResponseEntity.ok(result);
    }

    @Override
    public List<SystemGroup> findByGroupTypeFilter(SystemGroupSearch request)  {
        List<SystemGroup> allData = systemGroupRepository.findAllByGroupType(request.getGroupType(), request.getSearch(), request.getStatus());
        List<SystemGroup> parentList = allData.stream().filter(i -> i.getParent() == null).collect(Collectors.toList());

        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(credentialHelper.getJWTPayload().getUsername());

        List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.
                getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.SYSTEM_GROUP.code);

        List<SystemGroup> result = new ArrayList<>();
        parentList.forEach(i -> {
            List<String> applyFor = permissionDataManagements.stream().filter(p -> p.getTypeId().equals(i.getId())).map(PermissionDataManagement::getCompanyCode).collect(Collectors.toList());
            List<String> isExist = applyFor.stream().filter(lstCompanyCode::contains).collect(Collectors.toList());
            if(applyFor.contains(CommonConstants.FILTER_SELECT_ALL) || lstCompanyCode.contains(CommonConstants.FILTER_SELECT_ALL) || !isExist.isEmpty()){
                result.add(i);
            }

        });

        return result;
    }

    @Override
    public ResponseEntity<Object> findDataByTableName(String tableName, Object filter)  {
        Object result = null;
        switch (tableName.toUpperCase()) {
            case "TEMPLATE_MANAGER": // Biểu mẫu
                result = templateManager.getAllSystemGroup();
                break;
            case "BPM_PROC_DEF": // Quy trình
                result = bpmProcdefManager.getAllSystemGroup();
                break;
            case "SERVICE_PACKAGE": // Dịch vụ
                result = servicePackageManager.getAllSystemGroup();
                break;
            case "BPM_TEMPLATE_PRINT": // Mẫu trình ký
                result = bpmTemplatePrintManager.getAllSystemGroup();
                break;
            case "CHART": // Sdtc
                result = customerService.getAllSystemGroupChart("import");
                break;
            case "CUSTOM_FLOW": // luồng duyệt tùy biến
                result = customerService.getAllSystemGroupChart("manual");
                break;
//            case "MASTER_DATA":
//                result = masterDataManager.getAllSystemGroup();
//                break;
            case "PRIORITY_MANAGEMENT": // Độ ưu tiên
                result = priorityManager.getAllSystemGroup();
                break;
            case "NOTIFICATION_TEMPLATE": // Mẫu thông báo
                result = notificationTemplateServiceImpl.getAllSystemGroup();
                break;
            case "API_MANAGEMENT":
                result = apiManager.getAllSystemGroup();
                break;
            case "SUBMISSION_TYPE": // Loại tờ trình
                result = submissionTypeManager.getAllSystemGroup();
                break;
            case "EVALUATION_CRITERIA": // Danh mục soát xét
                result = evaluationCriteriaServiceImpl.getAllSystemGroup();
                break;
            case "CODE_GEN_CONFIG": // Cấu hình sinh mã tự động
                result = codeGenConfigServiceImpl.getAllSystemGroup();
                break;
            case "APPROVED_BUDGET": // Quản lý hạn mức tài chính
                result = customerService.getAllSystemGroupBudget();
                break;
            case "SYSTEM_CONFIG": // Quản lý cấu hình hệ thống
                result = customerService.getAllSystemGroupSystemConfig();
                break;
            case "SYSTEM_AUTHORIZATION": // Quản lý nhóm quyền chức năng
                result = authService.getAllSystemAuthorizationGroup();
                break;
            case "MANAGE_USER_ROLES": // Quản lý phân quyền dữ liệu
                result = authService.getAllPermissionGroup();
                break;
            case "POSITION": // Quản lý phân quyền dữ liệu
                result = customerService.getAllPosition();
                break;
            case "JOB_TITLE": // Quản lý phân quyền dữ liệu
                result = customerService.getAllJobTitle();
                break;
            default:
                break;

        }

        return ResponseEntity.ok(result);
    }

    @Override
    public ResponseEntity<Object> getUserGroup()  {

        return null;
    }

    @Override
    public ResponseEntity<?> addSystemGroup(AddSystemGroupRequest request)  {
        String username = credentialHelper.getJWTPayload().getUsername();
        //Check dup
        Boolean isDup = systemGroupRepository.existsByParentInAndGroupValueInAndStatusIsTrue(
                request.getParentId(),
                Arrays.asList(request.getGroupValue(), "-1"));
        if (isDup) {
            return ResponseEntity.badRequest().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.systemGroup.duplicate", null, Locale.getDefault())).build());
        }

        //List danh mục tương ứng
        //Handle history
        List<SystemGroupHistory> histories = new ArrayList<>();
        ResponseEntity<Object> allData = findDataByTableName(request.getGroupType(), null);
        Map<Long, Object> appList = new HashMap<>();
        if (allData.getBody() instanceof ArrayList) {
            ((ArrayList<?>) allData.getBody()).forEach(i -> {
                        Map<String, Object> child = (Map) i;
                        if(child.get("id") !=null) {
                            appList.put(Long.valueOf(child.get("id").toString()), child.get("name"));
                        }
                    }
            );
        }

        //Handle history

        List<SystemGroup> systemGroups = systemGroupRepository.findAllByIdIn(request.getParentId());
        List<SystemGroup> addList = new ArrayList<>();
        if (systemGroups != null && !systemGroups.isEmpty()) {
            systemGroups.forEach(item -> {
                SystemGroup systemGroup = item.clone();
                systemGroup.setGroupType(request.getGroupType());
                systemGroup.setGroupField(request.getGroupField());
                systemGroup.setGroupValue(request.getGroupValue());
                systemGroup.setCreatedDate(LocalDateTime.now());
                systemGroup.setModifiedDate(null);
                systemGroup.setCreatedUser(username);
                systemGroup.setModifiedUser(null);
                systemGroup.setGroupValue(request.getGroupValue());
                systemGroup.setParent(item.getId());
                systemGroup.setId(null);
                addList.add(systemGroup);

                //Handle history
                SystemGroupHistory history = new SystemGroupHistory();
                history.setParent(item.getId());
                history.setCreatedDate(LocalDateTime.now());
                history.setCreatedUser(username);
                history.setGroupType(request.getGroupType());
                history.setVersion(systemGroupHistoryRepository.findVersionByParentAndGroupType(history.getParent(), history.getGroupType()) + 1);
                history.setDescription(messageSource.getMessage("message.systemGroup.addList",
                        new Object[]{appList.get(Long.parseLong(request.getGroupValue()))},
                        Locale.getDefault()));
                histories.add(history);
            });
            systemGroupRepository.saveAll(addList);
            systemGroupHistoryRepository.saveAll(histories);
            return ResponseEntity.ok().body(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code)
                    .message("Thành công").build());
        } else
            return ResponseEntity.badRequest().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.systemGroup.groupNotFound", null, Locale.getDefault())).build());
    }

    @Override
    public Boolean update(List<Long> ids, Integer status)  {
        if (status == null) {
            status = 0;
        }
        List<SystemGroup> parents = systemGroupRepository.findAllById(ids);
        if (!parents.isEmpty()) {
            List<SystemGroup> systemGroup = systemGroupRepository.findAllByParentIn(parents.stream().map(SystemGroup::getId).collect(Collectors.toList()));
            parents.addAll(systemGroup);
        }
        Integer finalStatus = status;
        List<SystemGroupHistory> histories = new ArrayList<>();
        parents.forEach(systemGroup1 -> {
            systemGroup1.setStatus(finalStatus);
            if(finalStatus == 1 && systemGroup1.getToDate().isBefore(LocalDateTime.now())) {
                systemGroup1.setFromDate(LocalDateTime.now());
                systemGroup1.setToDate(LocalDateTime.now().plusDays(1));
            }

            SystemGroupHistory history = new SystemGroupHistory();
            history.setParent(systemGroup1.getId());
            history.setCreatedDate(LocalDateTime.now());
            history.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            history.setGroupType(systemGroup1.getGroupType());
            history.setVersion(systemGroupHistoryRepository.findVersionByParentAndGroupType(history.getParent(), history.getGroupType()) + 1);
            String message = (finalStatus == 1 ? "Kích hoạt" : "Vô hiệu hóa") + " "+systemGroup1.getName();
            history.setDescription(message);
            histories.add(history);
        });



        systemGroupHistoryRepository.saveAll(histories);
        systemGroupRepository.saveAll(parents);
        return true;
    }

    @Override
    public ResponseEntity<Boolean> checkDuplicateName(String tableName, String name, Long id) {
        return ResponseEntity.ok(checkDuplicate(tableName, name, id));
    }

    private Boolean checkDuplicate(String tableName, String name, Long id) {
        SystemGroup systemGroup = null;
        if (id == null) {
            systemGroup = systemGroupRepository.findTopByGroupTypeAndNameAndParent(tableName, name, null);
        } else {
            systemGroup = systemGroupRepository.findTopByGroupTypeAndNameAndIdNotAndParent(tableName, name, id, null);
        }
        return systemGroup != null;
    }

    @Override
    public List<Long> getListIdSystemGroup(String groupType, String username) {
        return systemGroupRepository.getGroupIdByGroupTypeAndUsername(groupType, username);
    }

    @Override
    public Boolean checkIdInSystemGroup(String groupType, String username, String groupValue) {
        List<SystemGroup> systemGroups = systemGroupRepository.findSystemGroupByGroupTypeAndUsernameAndGroupValue(groupType, username, groupValue);
        return !ValidationUtils.isNullOrEmpty(systemGroups);
    }
}
