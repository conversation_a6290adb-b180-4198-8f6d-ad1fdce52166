package vn.fis.eapprove.business.domain.bpm.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.bpm.entity.BpmOwnerProcess;
import vn.fis.eapprove.business.domain.bpm.repository.BpmOwnerProcessRepository;


import java.util.ArrayList;
import java.util.List;

@Service("BpmOwnerProcessManagerV1")
@Slf4j
@Transactional
public class BpmOwnerProcessManager {

    @Autowired
    private BpmOwnerProcessRepository bpmOwnerProcessRepository;

    public void save(BpmOwnerProcess bpmOwnerProcess) {
        bpmOwnerProcessRepository.save(bpmOwnerProcess);
    }

    public void deleteBpmOwnerProcessByProcDefId(Long id) {
        bpmOwnerProcessRepository.deleteBpmOwnerProcessByProcDefId(id);
    }

    public List<BpmOwnerProcess> bpmOwnerProcessByProcDefId(Long procDefId) {
        try {
            return bpmOwnerProcessRepository.getBpmOwnerProcessByProcDefId(procDefId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public List<BpmOwnerProcess> getAllByProcDefIdIn(List<Long> procDefId) {
        try {
            return bpmOwnerProcessRepository.getAllByProcDefIdIn(procDefId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public List<String> getProcessOwnersByProcDefId(String procDefId) {
        try {
            List<BpmOwnerProcess> listQuery = bpmOwnerProcessRepository.getListOwnerByProcDefId(procDefId);
            List<String> listUser = new ArrayList<>();
            for (BpmOwnerProcess user : listQuery) {
                listUser.add(user.getIdUser());
            }
            return listUser;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public void saveAll(List<BpmOwnerProcess> bpmOwnerProcessList) {
        bpmOwnerProcessRepository.saveAll(bpmOwnerProcessList);
    }

    public List<BpmOwnerProcess> getAllByBpmProcInstId(Long bpmProcInstId) {
        return bpmOwnerProcessRepository.getAllByBpmProcInstId(bpmProcInstId);
    }
}
