package vn.fis.eapprove.business.domain.sequence.service;


import vn.fis.eapprove.business.domain.sequence.entity.SequenceData;

public interface SequenceDataManager {
    public SequenceData getSequenceDataInfo(String sequenceName);

    public SequenceData createNew(String sequenceName);

    public SequenceData update(SequenceData sequenceData);

    public Long getNextval(String sequenceName);

    public Long getCurVal(String sequenceName);

    public String generateIDBySequenceTemplate(String sequenceTemplate);

    public Integer redisAtomicInteger();

}
