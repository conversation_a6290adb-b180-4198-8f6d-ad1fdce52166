package vn.fis.eapprove.business.domain.bpm.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "bpm_ru_history")
public class BpmRuHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_", nullable = false)
    private Long id;

    @Column(name = "PROC_INST_ID")
    private String procInstId;

    @Column(name = "UPDATE_TASK_ID")
    private String updateTaskId;

    @Column(name = "CURRENT_TASK_ID")
    private String currentTaskId;

    @JsonProperty("OLD_RU_DATA")
    private String oldRuData;

    @Column(name = "RU_TIME")
    private Date ruTime;

    @Column(name = "RU_ACTION_USER")
    private String ruActionUser;

    @Column(name = "AFFECTED_TASK")
    private String affectedTask;
}
