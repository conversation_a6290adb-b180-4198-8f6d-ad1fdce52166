package vn.fis.eapprove.business.domain.report.service;


import vn.fis.eapprove.business.dto.filter.ReportProcInstFilter;
import vn.fis.eapprove.business.model.response.ReportProcInstByChartNodeResponse;
import vn.fis.eapprove.business.model.response.ReportProcInstByGroupResponse;

public interface ReportProcInstServiceNew {

    ReportProcInstByGroupResponse getReportProcInstByGroup(ReportProcInstFilter filter);

    ReportProcInstByGroupResponse getReportTaskByUser(ReportProcInstFilter filter);

    ReportProcInstByChartNodeResponse getReportProcInstByChartNode(ReportProcInstFilter filter);

}