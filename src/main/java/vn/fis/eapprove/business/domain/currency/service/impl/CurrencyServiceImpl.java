package vn.fis.eapprove.business.domain.currency.service.impl;

/**
 * Author: AnhVTN
 * Date: 31/03/2023
 */

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.currency.entity.Currency;
import vn.fis.eapprove.business.domain.currency.repository.CurrencyRepository;
import vn.fis.eapprove.business.domain.currency.service.CurrencyService;
import vn.fis.eapprove.business.dto.CurrencyFilterDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.specification.CurrencySpecification;

import vn.fis.eapprove.business.utils.ResponseUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service("CurrencyServiceImplV1")
@Transactional
public class CurrencyServiceImpl implements CurrencyService {
    private final CurrencyRepository currencyRepository;
    private final ResponseUtils responseUtils;
    private final CurrencySpecification currencySpecification;

    @Autowired
    public CurrencyServiceImpl(CurrencyRepository currencyRepository, ResponseUtils responseUtils, CurrencySpecification currencySpecification) {
        this.currencyRepository = currencyRepository;
        this.responseUtils = responseUtils;
        this.currencySpecification = currencySpecification;
    }

    @Override
    public Currency saveCurrency(Currency currency) throws Exception {

        return currencyRepository.save(currency);

    }

    @Override
    public Currency findById(Long id) {
        return currencyRepository.findById(id).orElse(null);
    }

    @Override
    public void deleteByIds(List<Long> ids) {
        currencyRepository.deleteAllById(ids);
    }

    @Override
    public PageDto getCurrencys(CurrencyFilterDto request) {
        int pageNum = request.getPage() - 1;
        Sort sort = responseUtils.getSort(request.getSortBy(), request.getSortType());
        List<Currency> templates = new ArrayList<>();
        Page<Currency> page = currencyRepository.findAll(currencySpecification.filter(request), PageRequest.of(pageNum, request.getSize(), sort));
        if (page.hasContent()) {
            templates = page.getContent();
        }
        return PageDto.builder().content(templates)
                .number(page.getNumber() + 1)
                .numberOfElements(page.getNumberOfElements())
                .page(page.getNumber() + 1)
                .size(page.getSize())
                .sortBy(request.getSortBy())
                .sortBy(request.getSortType())
                .totalPages(page.getTotalPages())
                .totalElements(page.getTotalElements())
                .build();
    }

    @Override
    public List<Currency> getCurrencysFilter(CurrencyFilterDto request) {
        return currencyRepository.findAll(currencySpecification.filter(request));
    }

    @Override
    public boolean isExistCode(String code) {
        return currencyRepository.findCurrencyByCode(code) != null;
    }

    @Override
    public boolean isUpdateChangeCode(Long id, String code) {
        Currency currency = currencyRepository.findById(id).orElse(null);
        return currency != null && currency.getCode().equalsIgnoreCase(code);
    }

    @Override
    public boolean isNullCode(String code) {
        return code.trim().isEmpty();
    }

    @Override
    public boolean checkNameExist(String name) {
        List<Currency> currencies;
        currencies = currencyRepository.findAll();
        for (Currency currency : currencies) {
            if (currency.getName().equalsIgnoreCase(name)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean checkAnotherNameExist(String name) {
        List<Currency> currencies;
        currencies = currencyRepository.findAll();
        for (Currency currency : currencies) {
            if (currency.getAnotherName().equalsIgnoreCase(name)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean checkNameExistForUpdate(Long id, String name) {
        List<Currency> currencies;
        currencies = currencyRepository.findAll();
        Currency currencyExist = currencyRepository.findById(id).orElseThrow();
        currencies.remove(currencyExist);
        for (Currency currency : currencies) {
            if (currency.getName().equalsIgnoreCase(name)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean checkAnotherNameExistForUpdate(Long id, String name) {
        List<Currency> currencies;
        currencies = currencyRepository.findAll();
        Currency currencyExist = currencyRepository.findById(id).orElseThrow();
        currencies.remove(currencyExist);
        for (Currency currency : currencies) {
            if (currency.getAnotherName().equalsIgnoreCase(name)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean checkCodeExistForUpdate(Long id, String code) {
        List<Currency> currencies;
        currencies = currencyRepository.findAll();
        Currency currencyExist = currencyRepository.findById(id).orElseThrow();
        currencies.remove(currencyExist);
        for (Currency currency : currencies) {
            if (currency.getCode().equalsIgnoreCase(code)) {
                return true;
            }
        }
        return false;
    }
}
