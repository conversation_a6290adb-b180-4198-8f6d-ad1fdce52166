package vn.fis.eapprove.business.domain.location.entity;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Set;

@Entity
@Table(name = "location_management")
@Data
public class LocationManagement {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "abbreviations")
    private String abbreviations;

    @Column(name = "location_name")
    private String locationName;

//    @Column(name = "chart_id")
//    private String chartId;

    @Column(name = "address")
    private String address;

    @Column(name = "working_time_code")
    private String workingTimeCode;

    @Column(name = "working_time_name")
    private String workingTimeName;

    @Column(name = "status")
    private String status;

//    @Column(name = "company_name")
//    private String companyName;

//    @Column(name = "type")
//    private String type;

    @Column(name = "created_user")
    private String createdUser;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "modified_user")
    private String modifiedUser;

    @Column(name = "modified_date")
    private LocalDateTime modifiedDate;

    @Column(name = "company_name")
    private String companyName;

    @Column(name = "company_code")
    private String companyCode;

    @OneToMany(mappedBy = "locationManagement")
    @JsonIgnore
    private Set<PermissionDataManagement> permissionDataManagements;

}
