package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefNotification;


import java.util.List;

@Repository
public interface BpmProcdefNotificationRepository extends JpaRepository<BpmProcdefNotification, Long>, JpaSpecificationExecutor<BpmProcdefNotification> {

    @Modifying(clearAutomatically = true, flushAutomatically = true)
    @Query("DELETE FROM BpmProcdefNotification "
            + "WHERE bpmProcdefId = :bpmProcdefId ")
    void deleteAllBpmProcdefNotificationById(@Param("bpmProcdefId") Long bpmProcdefId);

    List<BpmProcdefNotification> findBpmProcdefNotificationsByTaskDefKeyNotification(String taskDefKeyNotification);

    @Query("SELECT c FROM BpmProcdefNotification c WHERE c.bpmProcdefId =:bpmProcdefId and c.status = '1'")
    List<BpmProcdefNotification> getBpmProcdefNotificationsInfo(@Param("bpmProcdefId") Long bpmProcdefId);

    //    @EntityGraph(value = "BpmProcdefNotification.lstNotifications")
    List<BpmProcdefNotification> findBpmProcdefNotificationsByBpmProcdefIdAndStatus(Long bpmProcdefId, String status);

    List<BpmProcdefNotification> findBpmProcdefNotificationsByStatusAndNotificationTemplateIdIn(String status, List<Long> lstNotificationTemplateId);

    //    @EntityGraph(value = "BpmProcdefNotification.lstNotifications")
    List<BpmProcdefNotification> findBpmProcdefNotificationsByBpmProcdefIdAndTaskDefKeyNotificationAndStatus(Long bpmProcdefId, String taskDefKeyNotification, String status);
    List<BpmProcdefNotification> findBpmProcdefNotificationsByBpmProcdefId(Long bpmProcdefId);

    List<BpmProcdefNotification> findBpmProcdefNotificationsByProcDefIdAndStatus(String procDefId, String status);

}
