package vn.fis.eapprove.business.domain.manageApi.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.manageApi.entity.ManageShareTicketDetail;


import java.util.List;

@Repository
public interface ManageShareTicketDetailRepository extends JpaRepository<ManageShareTicketDetail, Long>, JpaSpecificationExecutor<ManageShareTicketDetail> {
    List<ManageShareTicketDetail> findByManageShareTicketId(Long id);

    List<ManageShareTicketDetail> findManageShareTicketDetailsByManageShareTicketIdAndType(Long id, String type);
}
