package vn.fis.eapprove.business.domain.evaluation.service;


import vn.fis.eapprove.business.domain.evaluation.entity.EvaluationCriteria;
import vn.fis.eapprove.business.dto.ChartNodeDto;
import vn.fis.eapprove.business.dto.EvaluationCriteriaDto;
import vn.fis.eapprove.business.dto.EvaluationCriteriaSearchDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.EvaluationCriteriaRequest;
import vn.fis.eapprove.business.model.response.EvaluationCriteriaResponse;


import java.util.List;

public interface EvaluationCriteriaService {

    void saveAll(EvaluationCriteriaRequest request) throws Exception;

    void deleteByIds(EvaluationCriteriaRequest request) throws Exception;

    PageDto search(EvaluationCriteriaDto request) ;

    List<EvaluationCriteriaResponse> searchFilter(EvaluationCriteriaDto request) ;

    List<EvaluationCriteria> getAllCriteriaByReviewAndDepartment(EvaluationCriteriaSearchDto request);

    List<ChartNodeDto> getAllDepartmentByReview(String companyCode);

    List<EvaluationCriteria> getAllCriteria(EvaluationCriteriaDto request);

    List<EvaluationCriteria> getAllCriteriaByPredicate(EvaluationCriteriaDto request);

    Object getAllSystemGroup() ;
}
