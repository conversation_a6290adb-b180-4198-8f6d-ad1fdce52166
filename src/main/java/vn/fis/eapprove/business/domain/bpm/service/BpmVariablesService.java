package vn.fis.eapprove.business.domain.bpm.service;

import vn.fis.eapprove.business.domain.bpm.entity.BpmVariables;
import vn.fis.eapprove.business.model.request.VariableValueDto;


import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Author: PhucVM
 * Date: 06/03/2023
 */
public interface BpmVariablesService {

    void deleteVariables(String procInstId, String taskId, Set<String> variables);

    List<BpmVariables> saveAll(List<BpmVariables> variables);

    void saveVariables(String procInstId, String taskId, Map<String, VariableValueDto> variables, boolean isDraft);

    String uploadVarFile(VariableValueDto varDto);

    String saveFileToMinIO(String encodedBase64, String fileName, Date date);

    String getAdditionalValue(VariableValueDto valueDto);
}
