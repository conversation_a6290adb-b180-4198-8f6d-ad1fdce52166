package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vn.fis.eapprove.business.domain.bpm.entity.BpmHistory;
import vn.fis.eapprove.business.dto.report.DetailTicketCancelProjection;


import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface BpmHistoryRepository extends JpaRepository<BpmHistory, Long>, JpaSpecificationExecutor<BpmHistory> {

    @Query("SELECT h FROM BpmHistory h WHERE h.procInstId = :procId")
    List<BpmHistory> getHistoryuByProcess(@Param("procId") String procId);

    @Query("SELECT history \n" +
            "FROM BpmHistory history \n" +
            "WHERE history.action = 'RequestUpdate' AND history.createdTime > :firstDateOfTerm")
    List<BpmHistory> listRuHistory(@Param("firstDateOfTerm") Date firstDateOfTerm);

    List<BpmHistory> getBpmHistoryByAction(String action);

    @Query("SELECT history FROM BpmHistory  history where history.action ='RE_CREATED_BY_RU' and history.taskInstId = :taskId")
    BpmHistory getBpmHistoryRuByTaskId(String taskId);

    @Query(nativeQuery = true,value = "SELECT bh.action_user from bpm_history bh\n" +
            "WHERE bh.action = 'CREATE_TASK'\n" +
            " AND   bh.task_inst_id = :taskId")
    String getAssigneeOrgByTaskId(String taskId);

    @Query("SELECT h FROM BpmHistory h "
            + "WHERE h.taskDefKey = :taskDefKey "
            + "AND (h.action = 'CREATE_TASK' or h.action='RE_CREATED_BY_RU')"
            + "AND (h.actionUser IS NULL OR h.actionUser = '') "
            + "AND h.ticketId = :ticketId")
    List<BpmHistory> getAllByTaskDefKey(@Param("ticketId") Long ticketId, @Param("taskDefKey") String taskDefKey);

    @Query("SELECT DISTINCT h.action FROM BpmHistory h WHERE h.ticketId = :ticketId " +
            " and h.action not in ('GET_REQUEST_UPDATE', 'AFFECTED_BY_RU') ") // DXG_ERP_TH-36234 - bỏ  GET_REQUEST_UPDATE, AFFECTED_BY_RU
    List<String> getAllAction(@Param("ticketId") Long ticketId);

    @Query("SELECT DISTINCT h.actionUser FROM BpmHistory h WHERE h.ticketId = :ticketId")
    List<String> getAllActionUser(@Param("ticketId") Long ticketId);

    @Query("SELECT h.oldProcInstId FROM BpmHistory h WHERE h.procInstId = :procInstId and h.action = 'CREATE_TICKET' and h.ticketId = :ticketId")
    String getOldProcInstId(@Param("procInstId") String procInstId, @Param("ticketId") Long ticketId);

    @Query("SELECT h.oldTaskId FROM BpmHistory h " +
            "WHERE h.ticketId = :ticketId " +
            "and h.taskDefKey = :taskDefKey " +
            "and h.oldProcInstId = :oldProcInstId " +
            "and h.taskAssignee = :actionUser " +
            "and h.action = 'AFFECTED_BY_RU'")
    String getOldTaskIdAffected(@Param("ticketId") Long ticketId, @Param("taskDefKey") String taskDefKey, @Param("oldProcInstId") String oldProcInsId, @Param("actionUser") String actionUser);

    @Query("SELECT h.oldDefaultField FROM BpmHistory h " +
            "WHERE h.ticketId = :ticketId " +
            "AND h.oldProcInstId = :procInstId " +
            "AND h.action = 'CREATE_TICKET'")
    String getOldTicketDefaultField(@Param("ticketId") Long ticketId, @Param("procInstId") String procInstId);

    List<BpmHistory> findBpmHistoriesByActionAndProcInstIdOrderByIdDesc(String action, String procInstId);

    @Query("SELECT h FROM BpmHistory h " +
            "WHERE h.procInstId = :procInstId " +
            "AND h.action in ('RECALLED', 'GET_REQUEST_UPDATE') " +
            "ORDER BY h.id desc")
    List<BpmHistory> getBpmHistoriesByProcInstIdAndActionIn(@Param("procInstId") String procInstId);

    List<BpmHistory> findAllByProcInstIdAndAttachFilesNotNullOrderByIdDesc(String procInstId);

    List<BpmHistory> findAllByTicketIdAndAttachFilesNotNullAndActionNotInOrderByIdDesc(Long ticketId, Collection<String> action);

    @Query("SELECT DISTINCT h.procInstId FROM BpmHistory h " +
            "WHERE h.ticketId = :ticketId " +
            "AND h.taskInstId = :taskInstId")
    String getProcInstIdByTicketIdAndTaskInstId(@Param("ticketId") Long ticketId, @Param("taskInstId") String taskInstId);

    @Query(nativeQuery = true, value = "SELECT bh.proc_inst_id as procInstId" +
            ",bh.created_time as createdTime,bh.action_user as cancelUser,bh.note as cancelReason " +
            "FROM bpm_history bh WHERE bh.action = 'CANCEL_TICKET' AND proc_inst_id = :procInstId " +
            "AND created_time = (SELECT MAX(created_time)\n" +
            "                      FROM bpm_history\n" +
            "                      WHERE action = 'CANCEL_TICKET'\n" +
            "                        AND proc_inst_id = :procInstId)")
    DetailTicketCancelProjection getDetailTicketCancel(@Param("procInstId") String procInstId);

    BpmHistory getBpmHistoryByTaskInstIdAndAction(String taskInstId, String action);

    @Query(value = "select * from bpm_history  " +
            " where proc_inst_id = :procInstId " +
            " and task_def_key = :taskDefKey " +
            " and action_user = :assignee " +
            " and action = 'COMPLETE_TASK' " +
            " order by id desc limit 1", nativeQuery = true)
    BpmHistory getBpmHistoryMissingSign(String procInstId, String taskDefKey, String assignee);
}
