package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTicketFilter;


import java.util.List;

@Repository
public interface BpmTicketFilterRepository extends JpaRepository<BpmTicketFilter, Long>, JpaSpecificationExecutor<BpmTicketFilter> {

    BpmTicketFilter getBpmTicketFilterById(long id);
    BpmTicketFilter findByNameAndCreatedUserAndType(String name, String createdUser, String type);

    @Query("select count(tf.id) from BpmTicketFilter tf where tf.type = :type and tf.status = 'pin' and tf.createdUser = :createdUser and tf.id <> :id")
    Long countPinByTypeAndCreatedUser(Long id, String type, String createdUser);

    @Query("select tf from BpmTicketFilter tf where tf.type = :type and tf.status = 'pin' and tf.createdUser = :createdUser ")
    List<BpmTicketFilter> getPinByTypeAndCreatedUser(String createdUser, String type);

    @Query("select tf from BpmTicketFilter tf where tf.createdUser = :createdUser " +
            " and (:search is null or tf.name like concat('%', :search, '%')) " +
            " and tf.type = :type " +
            " order by tf.status asc, tf.updatedDate desc")
    List<BpmTicketFilter> findByCreatedUserAndType(String createdUser, String search, String type);

    Boolean existsBpmTicketFilterByNameAndTypeAndCreatedUser(String name, String type, String createdUser);
}
