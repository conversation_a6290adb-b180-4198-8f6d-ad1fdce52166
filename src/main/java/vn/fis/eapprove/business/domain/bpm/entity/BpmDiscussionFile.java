package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Data;

import jakarta.persistence.*;

@Data
@Entity
@Table(name = "bpm_discussion_file")
public class BpmDiscussionFile {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "DISCUSSION_ID")
    private Long discussionId;

    @Column(name = "DOWNLOAD_URL")
    private String downloadUrl;

    @Column(name = "FILES_PATH")
    private String filePath;

    @Column(name = "FILES_NAME")
    private String fileName;

    @Column(name = "file_size")
    private String fileSize;
}
