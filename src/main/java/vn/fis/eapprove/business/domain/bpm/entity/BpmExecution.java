package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Data
@Entity
@Table(name = "bpm_execution")
public class BpmExecution {

    @Id
    @Column(name = "ID_")
    private String id;

    @Column(name = "PROC_INST_ID_")
    private String procInstId;

    @Column(name = "PARENT_ID_")
    private String parentId;

    @Column(name = "PROC_DEF_ID_")
    private String procDefId;

    @Column(name = "ACT_ID_")
    private String actId;

    @Column(name = "ACT_INST_ID_")
    private String actInstId;

    @Column(name = "IS_ACTIVE_")
    private int isActive;

    @Column(name = "IS_CONCURRENT_")
    private int isConcurrent;

    @Column(name = "SEQUENCE_COUNTER_")
    private Long sequenceCounter;

    @Column(name = "TENANT_ID_")
    private String tenantId;
}
