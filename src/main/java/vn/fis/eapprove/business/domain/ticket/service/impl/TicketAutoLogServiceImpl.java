package vn.fis.eapprove.business.domain.ticket.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.ticket.entity.TicketAutoLog;
import vn.fis.eapprove.business.domain.ticket.repository.TicketAutoLogRepository;
import vn.fis.eapprove.business.domain.ticket.service.TicketAutoLogService;
import vn.fis.spro.common.constants.AutoTicketConstant;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
@Transactional

public class TicketAutoLogServiceImpl implements TicketAutoLogService {

    @Autowired
    private TicketAutoLogRepository ticketAutoLogRepository;

    @Override
    public void saveLog(Long ticketId, String type, Boolean checkResult) {
        try {
            TicketAutoLog ticketAutoLog = new TicketAutoLog();
            ticketAutoLog.setTicketId(ticketId);
            ticketAutoLog.setType(type);
            ticketAutoLog.setProcessTime(LocalDateTime.now());
            if (checkResult = true) {
                ticketAutoLog.setState(AutoTicketConstant.State.SUCCESS.code);
            } else {
                ticketAutoLog.setState(AutoTicketConstant.State.FAIL.code);
            }
            ticketAutoLogRepository.save(ticketAutoLog);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return;
        }

    }

    @Override
    public void saveAll(List<TicketAutoLog> ticketAutoLogList) {
        ticketAutoLogRepository.saveAll(ticketAutoLogList);
    }
}
