package vn.fis.eapprove.business.domain.notification.entity;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;

import jakarta.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * Author: AnhVTN
 * Date: 28/02/2023
 */
@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Entity
@Table(name = "notification_template_detail")
public class NotificationTemplateDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Column(name = "notification_template_id", nullable = false)
    private Long notificationTemplateId;

    @Size(max = 1000)
    @Column(name = "title", length = 1000)
    private String title;

    @Lob
    @Column(name = "content")
    private String content;

    @Size(max = 50)
    @Column(name = "source_type", length = 50)
    private String sourceType;

    @Size(max = 10)
    @Column(name = "type", length = 10)
    private String type;

    @Column(name = "create_at")
    private Date createAt;

    @Column(name = "update_at")
    private Date updateAt;

    @Size(max = 50)
    @Column(name = "user_create", length = 50)
    private String userCreate;

    @Size(max = 50)
    @Column(name = "user_update", length = 50)
    private String userUpdate;

    @Size(max = 4000)
    @Column(name = "file_icon", length = 4000)
    private String fileIcon;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        NotificationTemplateDetail that = (NotificationTemplateDetail) o;
        return id != null && Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}