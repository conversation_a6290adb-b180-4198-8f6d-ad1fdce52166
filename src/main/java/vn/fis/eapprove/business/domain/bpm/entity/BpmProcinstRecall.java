package vn.fis.eapprove.business.domain.bpm.entity;

import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 12/05/2023
 */
@Getter
@Setter
@Entity
@Table(name = "bpm_procinst_recall")
public class BpmProcinstRecall {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "BPM_PROCINST_ID")
    private Long bpmProcinstId;

    @Column(name = "PROC_INST_ID")
    private String procInstId;

    @Column(name = "RECALL_USER")
    private String recallUser;

    @Column(name = "RECALL_TIME")
    private LocalDateTime recallTime;

    @Column(name = "REASON")
    private String reason;

    @Column(name = "ATTACH_FILE")
    private String attachFile;

    @Column(name = "ATTACH_FILE_NAME")
    private String attachFileName;

    @Column(name = "ATTACH_FILE_SIZE")
    private String attachFileSize;
}
