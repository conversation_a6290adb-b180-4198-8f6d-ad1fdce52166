package vn.fis.eapprove.business.domain.bpm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefInherits;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefLegislativeStatusConfig;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefViewFileApi;

import java.util.List;

@Repository
public interface BpmProcdefLegislativeConfigRepository extends JpaRepository<BpmProcdefLegislativeStatusConfig, Long>, JpaSpecificationExecutor<BpmProcdefInherits> {

    List<BpmProcdefLegislativeStatusConfig> getAllByBpmProcdefIdAndProcDefId(Long bpmProcdefId,String procDefId);

    List<BpmProcdefLegislativeStatusConfig> getAllByBpmProcdefId(Long bpmProcdefId);
}
