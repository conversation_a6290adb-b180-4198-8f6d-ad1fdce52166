package vn.fis.eapprove.business;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import vn.fis.spro.common.CommonConfig;
import vn.fis.spro.file.FileManagerConfig;

@SpringBootApplication(exclude = {RedisRepositoriesAutoConfiguration.class})
@Import({CommonConfig.class, FileManagerConfig.class})
@ComponentScan("vn.fis.eapprove")
public class BusinessProcessServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(BusinessProcessServiceApplication.class, args);
    }

}
