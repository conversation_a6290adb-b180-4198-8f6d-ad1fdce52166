package vn.fis.eapprove.business.dto.report;

import lombok.Data;
import lombok.experimental.FieldDefaults;
import vn.fis.eapprove.business.model.response.UserInfoByUsername;

import java.time.LocalDateTime;

@Data
@FieldDefaults(level = lombok.AccessLevel.PRIVATE)
public class DetailTicketCancelResponse {
    String procInstId;
    LocalDateTime createdTime;
    String cancelReason;
    UserInfoByUsername cancelUserInfo;
}
