package vn.fis.eapprove.business.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class ReportByChartNodeDto {

    private Long id;

    private String taskId;

    private String procInstId;

    private String taskType;

    private String taskStatus;

    private Long serviceId;

    private String serviceName;

    private Long submissionType;

    private Long masterParentId;

    private String requestCode;

    private String title;

    private Long priorityId;

    private String priorityName;

    private String createdUser;

    private String createdUserStatus;

    private String createdUserFullName;

    private Long createdUserChartId;

    private String createdUserChartShortName;

    private Long createdUserChartNodeId;

    private String createdUserChartNodeName;

    private String createdUserChartNodeCode;

    private String createdUserTitleName;

    private String createdUserStaffCode;

    private String createdUserManagerLevel;

    private String createdUserEmail;

    private String createdUserDirectManager;

    private String assignee;

    private String assigneeFullName;

    private Long assigneeChartId;

    private String assigneeChartShortName;

    private String assigneeManagerLevel;

    private String assigneeChartNodeId;

    private String assigneeChartNodeName;

    private String assigneeChartNodeCode;

    private String assigneeTitleName;

    private String assigneeStaffCode;

    private String assigneeEmail;

    private String assigneeDirectManager;

    private Long locationId;

    private LocalDateTime createdTime;

    private LocalDateTime slaFinishTime;

    private LocalDateTime finishedTime;

    private Boolean isExpire;

    private LocalDateTime startedTime;

    private String taskName;

    private String cancelReason;

    private Long ticketId;

    private String procDefId;

    private String assigneeStatus;

    private LocalDate versionTime;
}
