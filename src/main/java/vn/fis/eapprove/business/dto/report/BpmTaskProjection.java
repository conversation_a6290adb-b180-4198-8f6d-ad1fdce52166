package vn.fis.eapprove.business.dto.report;

import java.time.LocalDateTime;

public interface BpmTaskProjection {
    Long getId();

    String getTaskId();

    String getProcInstId();

    String getProcDefId();

    String getName();

    Integer getStatus();

    String getPriority();

    String getAssignee();

    LocalDateTime getCreatedTime();

    LocalDateTime getStartedTime();

    Double getSlaFinish();

    Double getSlaResponse();

    LocalDateTime getFinishedTime();

    LocalDateTime getSlaFinishTime();

    String getType();

    String getCreatedUser();

    Boolean getAssignType();
}
