package vn.fis.eapprove.business.dto;

import lombok.*;
import lombok.experimental.SuperBuilder;
import vn.fis.eapprove.business.model.response.ChildResponse;
import vn.fis.eapprove.business.model.response.ChildTemplateResponse;
import vn.fis.eapprove.business.model.response.ServiceNamePopUpResponse;

import java.util.List;

/**
 * Generated by Speed Generator
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
public class BpmProcdefDto extends BaseDto {

    private Long id;
    private String procDefId;
    private List<String> listProcDefId;
    private String name;
    private Integer processType;
    private String deploymentId;
    private String prioritized;
    private Long priorityId;
    private Long parentsId;
    private Long serviceId;
    private List<Long> listServiceName;
    private List<ServiceNamePopUpResponse> serviceNamePopUp;
    private Long serviceCount;
    private String description;
    private String key;
    private String resourceName;
    private List<String> listTemplateName;
    private String status;
    private List<String> listStatus;
    private List<BpmOwnerDto> owners;
    private String createdDate;
    private String userCreated;
    private String updatedDate;
    private String userUpdate;
    private List<BpmProcDefFilterDto> listDateFilter;
    private List<String> shareUser;

    private Boolean specialFlow;
    private Boolean isShared;
    private String companyCode;
    private String companyName;
    private List<String> listCompanyCode;
    private List<String> listCompanyCodeFilter;
    private List<String> listCompanyName;
    private List<String> listUserUpdate;
    private List<String> listName;
    private List<String> listDescription;
    private List<String> listResourceName;
    private List<String> listUserCreated;
    private List<Long> lstGroupPermissionId;
    private String specialCompanyCode;
    private String specialFormKey;
    private List<ChildTemplateResponse> templates;
    private List<ChildResponse> child;
    private List<String> printApply;
    private List<String> listCompanyCodeMemberAdmin;
}