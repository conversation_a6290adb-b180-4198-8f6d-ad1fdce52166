package vn.fis.eapprove.business.dto.filter;

import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDate;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@FieldDefaults(level = lombok.AccessLevel.PRIVATE)
public class ReportProcInstFilter {
    private Long procInstId;
    private List<String> serviceName;
    private List<Long> submissionType;
    private List<String> taskType;
    private List<Long> priorityId;
    private Long chartId;
    private List<Long> chartNodeId;
    private List<Long> defaultChartNodeId;
    private String username;
    private List<String> userStatus;
    private List<String> directManager;
    private Set<String> createdUser = new HashSet<>();
    private Set<String> defaultUser = new HashSet<>();
    private List<String> taskStatus;
    private Long isExpire;
    private List<Long> locationId;
    private String fromDate;
    private String toDate;
    private Integer page;
    private Integer pageSize;
    private String reportType;
    private String staffCode;
    private List<Long> masterParentId;
    private List<String> assignee;
    private List<String> procInstStatus;
    private String search;
    private String sortBy;
    private String sortType;
    private Long serviceId;
    private Long ticketId;
    private Set<String> userNames;
    private String chartNodeCode;

//    public String getToDate() {
//        if (this.toDate.isEmpty()) {
//            return "";
//        }
//        LocalDate date = LocalDate.parse(toDate);
//        LocalDate newDate = date.plusDays(1);
//        return newDate.toString();
//    }
}
