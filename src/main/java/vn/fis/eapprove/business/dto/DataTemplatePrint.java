package vn.fis.eapprove.business.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTpTask;


import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataTemplatePrint {
    private Long id;
    private String name;
    private String processName;
    private String descr;
    private String procDefId;
    private List<String> shareWith;
    private String uploadWords;
    private String uploadWordsChange;
    private List<BpmTpTask> bpmTpTaskList;
    private List<HistoryChangeDto> historyChangeDtoList;
    private List<FileConditionDto> fileConditionList;
    private List<String> applyFor;
    private List<Long> htmlConfig;
    private String configType;
}
