package vn.fis.eapprove.business.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.time.LocalDateTime;

/**
// * A DTO for the {@link BpmAdditionalRequest} entity
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BpmAdditionalRequestDto {
    private Long id;
    private String taskId;
    private String procInstId;
    private String startUserId;
    private String contentRequest;
    private int autoReturn;
    private Boolean isAuto;
    private LocalDateTime reminderDate;
    private LocalDateTime expiredDate;
    private LocalDateTime createdDate;
    private String createdUser;
    private LocalDateTime modifiedDate;
    private String modifiedUser;
}