package vn.fis.eapprove.business.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Author: AnhVTN
 * Date: 30/03/2023
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CodeGenConfigDto extends BaseFilterDto {
    private String code;
    private List<String> systemConfigCode;
    private List<String> listCompanyCode;
    private List<String> status;

    //Add Filter
    private List<String> listCode;
    private List<String> listUserCreate;
    private List<String> listUserUpdate;
    private List<String> listDescription;
    private List<String> listStructorCode;

    private List<Long> lstGroupPermissionId;
    private List<String> listCompanyCodeMemberAdmin;
}
