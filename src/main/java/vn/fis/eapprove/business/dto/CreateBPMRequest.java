package vn.fis.eapprove.business.dto;

import lombok.Data;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefLegislativeStatusConfig;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefLegislativeTicketConfig;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefViewFileApi;


import java.util.List;

@Data
public class CreateBPMRequest {
    private Long id;
    private String deploymentId;
    private String fileName;
    private String name;
    private String description;
    private String prioritized;
    private List<String> owners;
    private Double autoClose;
    private Double autoCancel;
    private Boolean stepByStepResultForCreate;
    private Boolean inFormTo;
    private Boolean location;
    private Integer processType;
    private String procDefId;

    private Boolean update;
    private Boolean requestUpdate;
    private Boolean autoInherits;
    private Boolean offNotification;
    private Boolean createNewAndDouble;
    private List<String> templateId;
    private List<BpmProcdefApiDto> bpmProcdefApiList;
    private List<BpmProcdefInheritsDTO> bpmProcdefInherits;
    private List<BpmProcdefNotificationDTO> bpmProcdefNotifications;
    private Long priorityId;

    private Boolean cancel;
    private Boolean additionalRequest;
    private List<String> cancelTasks;
    private List<String> shareWith;
    private Boolean hideRelatedTicket;
    private List<String> hideRelatedTicketValue;
    private Boolean showInfo;
    private Boolean hideInfo;
    private Boolean isAssistant;
    private Boolean isEditAssistant;
    private Boolean isAutoCancel;
    private List<String> showInfoTasks;
    private List<String> hideInfoTasks;

    //    private Boolean changeImplementer;
    private List<String> changeImplementerValue;

    private Boolean authorityOnTicket;
    private List<String> authorityOnTicketValue;
    private List<String> authorityOnTicketStep;
    private List<String> applyFor;
    private Boolean recall;
    private Boolean showInputTask;
    private List<String> showInputTaskDefKeys;
    private List<String> hideRuTasks;

    private Boolean hideInherit;
    private List<String> hideInheritTasks;

    private Boolean hideComment;
    private List<String> hideCommentTasks;

    private Boolean hideDownload;
    private List<String> hideDownloadTasks;

    private Boolean hideShareTicket;

    private List<BpmProcdefViewFileApi> viewFileApi;

    // auto complete
    private Boolean autoCompleteTask;

    private Boolean disableApprovedTicket;
    private Boolean warningApprovedTicket;
    private String warningApprovedTicketTasks;
    private String disabledApprovedTicketTasks;

    // change setting object
    private Boolean isChangeActionApi;
    private Boolean isChangeInherits;
    private Boolean isChangeNotification;
    private Boolean isChangeViewFileApi;
    private Boolean legislativeRequirement;

    //Legislative
    private List<BpmProcdefLegislativeStatusConfig> configLegislativeByStatus;
    private List<BpmProcdefLegislativeTicketConfig> configLegislativeTicket;
}
