package vn.fis.eapprove.business.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ServicePackageDto {

    private Long id;
    private Long parentId;
    private String serviceName;
    private String color;
    private String icon;
    private Integer serviceType;
    private Long processId;
    private String description;
    private String note;
    private String url;
    private Integer positionPackage;
    private Long idOrgChart;
    private Boolean notShowingWebsite;
    private Boolean notShowingMoblie;
    private String hide_name;
    private String visible_name;
    private Boolean deleted;
    private Long masterParentId;
    private List<ServicePackageDto> lsChild = new ArrayList<>();
}
