package vn.fis.eapprove.business.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class VariableInstDto {
    private String id;
    private String procDefKey;
    private String procDefId;
    private String procInstId;
    private String actInstId;
    private String taskId;
    private String type;
    private String value1;
    private String value2;
    private String executionId;
    private String name;
    private String varDouble;
    private String varLong;
    private String createdTime;
    private String status;
}
