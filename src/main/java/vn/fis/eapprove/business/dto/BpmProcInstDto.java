package vn.fis.eapprove.business.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Generated by Speed Generator
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BpmProcInstDto {
    private String id;
    private String procInstId;
    private String title;
    private String procDefKey;
    private String procDefId;
    private LocalDateTime createTime;
    private LocalDateTime startedTime;
}