package vn.fis.eapprove.business.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import vn.fis.spro.common.model.request.DateFilterDto;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class LoadTicketDto extends BaseDtoTemp {

    private Long id;
    private String ticketId;
    private String ticketTitle;
    private String ticketCreatedUser;
    private String ticketProcDefId;
    private String ticketCreatedTime;
    private String ticketSla;
    private String ticketEndTime;
    private String ticketClosedTime;
    private String ticketStartedTime;
    private String ticketCanceledTime;
    private String ticketStatus;
    private String procServiceName;
    private String ticketStartUserId;
    private Double ticketRating;
    private String comment;
    private List<String> listOwner;
    private List<String> listService;
    private List<String> listStatus;
    private List<String> listDate;
    private List<String> listRating;
    private String user;
    private Map<String, Object> ruData;
    private Long serviceId;
//    private List<TicketDateFilterDto> listDateFilter;
    private String taskName;
    private List<BpmTaskDto> ticketTaskDtoList;
    private Boolean ownerProcess = true;
    private Boolean isJoinBpmTask = false;
    private String searchBy;

    private String companyCode;
    private String name;
    private String chartNodeName;

    //Search tên công ty
    private List<String> chartNames;
    //Search mã công ty
    private List<String> chartCodes;
    //Search phòng ban
    private List<String> chartNodeNames;
    //Search độ ưu tiên
    private List<String> taskPriority;

    private List<String> listRequestCode;
    private List<String> listTicketTitle;
    private List<String> listProcServiceName;
    private List<String> listTicketTaskDtoList;
    private List<String> listTicketStatus;
    private List<String> listCompanyCode;
    private List<String> listId;
    private List<String> listChartName;
    private List<String> listCancelReason;
    private List<String> listChartNodeName;
    private List<String> listTicketId;
    private List<String> listActionUser;
    private List<String> listSharedUser;
    private List<String> listProcTitle;
    private List<String> listServiceName;
    private List<String> listTaskPriority;
    private List<String> listTaskStatus;
    private List<String> listTaskName;
    private List<String> listTicketTaskDtoTaskAssignee;
    private List<String> listTaskNameExt;
    private List<String> listOrgAssignee;
    private List<String> listActionUserCompleteTask;
    private List<String> listCancelUser;
    private List<String> listCancelActionUser;
    private List<String> listTaskNameMyApproval;
    private List<String> listTicketStartUserId2;
    private List<String> listTicketStartUserId;
    private List<String> listTaskDefKey;
    private List<String> listCompanyName;

    private List<String> listCreatedUser;
    private List<String> listModifiedUser;

    private List<DateFilterDto> listDateFilter;

    private List<String> listDescription;

    private Boolean handingOverWork;

    private String searchRoot;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public void setTicketId(Long ticketId) {
        this.ticketId = ticketId.toString();
    }

}
