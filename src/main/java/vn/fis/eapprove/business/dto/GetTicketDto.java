package vn.fis.eapprove.business.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Author: PhucVM
 * Date: 04/12/2022
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetTicketDto {
    private Long serviceId;
    private List<Long> listServiceId;
    private String status;
    private List<String> listStatus;
    private List<String> listTicketId;
    private List<String> listProcinstId;
    private List<String> listCompleteTicketId;
    private String username;

}
