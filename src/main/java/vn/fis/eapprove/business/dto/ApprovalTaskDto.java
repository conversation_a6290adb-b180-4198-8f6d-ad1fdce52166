package vn.fis.eapprove.business.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Author: PhucVM
 * Date: 04/10/2022
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApprovalTaskDto {

    private String taskKey;
    private String taskName;
    private String formKey;
    private Long printId;
    private String taskType;
    private boolean isMultiInstance;
    private boolean isUsed;
    private String collection;
    private String assignee;
    private String candidateUsers;
    private String elementType;
    private List<String> listTaskAfter;
    private List<String> listTaskBefore;
    private List<String> listVariable;
}
