package vn.fis.eapprove.business.dto;

import lombok.*;
import vn.fis.eapprove.business.domain.bpm.entity.*;
import vn.fis.eapprove.business.model.response.ServiceNamePopUpResponse;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Builder
public class ProDefDetailDto {
    private String id;
    private String procDefId;
    private String name;
    private List<ServiceNamePopUpResponse> serviceNamePopUp;
    private String serviceCount;
    private String serviceName;
    private String description;
    private String key;
    private String deploymentId;
    private String resourceName;
    private byte[] bytes;
    private String fileXml;
    private String prioritized;
    private String userCreated;
    private String status;
    private List<BpmOwnerDto> owners;
    private String createdDate;
    private String createdUser;
    private String updatedDate;
    private String userUpdate;
    private String autoClose;
    private String autoCancel;
    private List<String> blockLocation;
    private Boolean stepByStepResultForCreate;
    private Boolean inFormTo;
    private Boolean location;
    private Boolean update;
    private Boolean requestUpdate;
    private Boolean createNewAndDouble;
    private Boolean autoInherits;
    private List<BpmProcdefInherits> bpmProcdefInherits;
    private List<BpmProcdefNotification> bpmProcdefNotifications;

    private List<BpmProcdefApiDto> bpmProcdefApiDtos;

    private Long priorityId;
    private Boolean cancel;
    private Boolean isAssistant;
    private Boolean isEditAssistant;
    private Boolean isAutoCancel;
    private Boolean hideInfo;
    private Boolean showInfo;
    private Boolean additionalRequest;
    private String cancelTasks;
    private List<String> listCancelTasks;
    private List<String> listShowInfoTasks;
    private List<String> listHideInfoTasks;
    private List<String> shareWith;
    private List<Integer> listHideRelatedTicketValue;
    private Boolean hideRelatedTicket;

    //    private Boolean changeImplementer;
    private List<String> listChangeImplementerValue;

    private List<Integer> listAuthorityOnTicketValue;
    private Boolean authorityOnTicket;
    private List<String> listAuthorityOnTicketStep;
    private Boolean offNotification;
    private List<String> applyFor;
    private Boolean recall;
    private Boolean showInputTask;
    private List<String> lstShowInputTaskDefKeys;
    private List<String> listHideRuTasks;

    private Boolean hideInherit;
    private List<String> listHideInheritTasks;

    private Boolean hideComment;
    private List<String> listHideCommentTasks;

    // Ẩn download file trình ký
    private Boolean hideDownload;
    private List<String> listHideDownloadTasks;

    // Ẩn nút Chia sẻ
    private Boolean hideShareTicket;

    private List<BpmProcdefViewFileApi> bpmProcdefViewFileApis;

    // Special clone procDef
    private Boolean specialFlow;
    private Long specialParentId;
    private String specialCompanyCode;
    private List<String> listSpecialFormKey;

    // auto complete
    private Boolean autoCompleteTask;

    private Boolean disableApprovedTicket;
    private Boolean warningApprovedTicket;
    private String listWarningApprovedTicketTasks;
    private String listDisabledApprovedTicketTasks;
    private Boolean legislativeRequirement;

    private List<BpmProcdefLegislativeStatusConfig> configLegislativeByStatus;
    private List<BpmProcdefLegislativeTicketConfig> configLegislativeTicket;
}




