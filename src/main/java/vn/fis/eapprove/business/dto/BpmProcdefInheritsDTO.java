package vn.fis.eapprove.business.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BpmProcdefInheritsDTO {
    private Long id;
    private Long notificationTemplateId;
    private Long bpmProcdefId;
    private String procDefId;
    private String taskDefKey;
    private String taskDefKeyInherits;
    private String fieldInherits;
    private String status;
    private LocalDateTime createdTime;
    private String createdUser;
    private LocalDateTime updatedTime;
    private String updatedUser;
    private Boolean isNew;
}
