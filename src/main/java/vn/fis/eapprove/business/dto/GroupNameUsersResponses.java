package vn.fis.eapprove.business.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * vn.fis.spro.customer.dto.GroupNameUsersResponses
 * Created by ThaiTuanHiep - <EMAIL>
 * 7/4/2022 4:28 PM
 */

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class GroupNameUsersResponses {
    private Long groupId;
    private String groupName;
    private Set<String> users;
    private List<EmailUserDto> emails;
}

