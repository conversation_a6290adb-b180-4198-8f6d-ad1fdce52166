package vn.fis.eapprove.business.dto.report;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DetailTaskDto {
    private String taskId;
    private String requestCode;
    private String taskType;
    private String taskName;
    private String priority;
    private LocalDateTime createdTime;
    private LocalDateTime startedTime;
    private LocalDateTime finishedTime;
    private LocalDateTime slaFinishTime;
    private String status;
    private String procInstId;
    private String procDefId;
    private String assignee;
    private String createdUser;
    private String createdUserFullName;
    private String createdUserChartNodeName;
    private String createdUserChartShortName;
    private String createdUserTitleName;
    private String assigneeFullName;
    private String assigneeChartNodeName;
    private List<String> assigneeChartNodeCode;
    private String assigneeChartShortName;
    private String assigneeTitleName;
    private String assigneeDirectManager;
    private String assigneeStatus;
    private Long assigneeLocationId;
    private String assigneeStaffCode;
    private Long ticketId;
    private String isExpire;
    private Long elapsedTime;
    private Double delayTime;
}
