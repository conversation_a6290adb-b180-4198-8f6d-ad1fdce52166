package vn.fis.eapprove.business.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties
public class EvaluationCriteriaDto extends BaseFilterDto {
    private String status;
    private List<Integer> lstDepartments;
    private List<String> lstCompanyCode;

    //Add filter
//    private List<String> listReviewItem;
    private List<String> listName;
    private List<String> listUpdatedUser;

    private List<Long> lstGroupPermissionId;
    private List<String> listCompanyCodeMemberAdmin;
}
