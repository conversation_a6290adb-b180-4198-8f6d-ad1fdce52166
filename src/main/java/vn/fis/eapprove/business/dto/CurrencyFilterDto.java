package vn.fis.eapprove.business.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import vn.fis.spro.common.model.request.BaseFilterDto;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
public class CurrencyFilterDto extends BaseFilterDto {
    private List<String> listCode;
    private List<String> listName;
    private List<String> listAnotherName;
    private List<String> listRoundingRules;
    private List<String> listUserCreate;
    private List<String> listUserUpdated;
}
