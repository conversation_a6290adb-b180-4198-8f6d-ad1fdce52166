package vn.fis.eapprove.business.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class TaskInfoDto {
    private String taskDefKey;
    private Long taskId;
    private String orgAssignee;
    private String toAssignee;
    private String taskStatus;
    private String actionUser;
    private LocalDateTime taskFinishedTime;
    private LocalDateTime taskCreatedTime;
    private Boolean assignTask;
    private List<String> lstCandidateUser;
}
