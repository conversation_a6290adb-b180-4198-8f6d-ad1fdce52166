package vn.fis.eapprove.business.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssignDto extends BaseFilterDto {
    private List<String> assignUser;
    private List<String> assignedUser;
    private List<String> createdUser;
    private List<String> updatedUser;
    private List<Integer> status;
    private String userLogin;
    private List<String> lstCompanyCode;
    private Boolean hasPermission = true;

    //Add filter
    private List<String> listRequestCode;
    private List<String> listAssignName;
    private List<String> listAssignUser;
    private List<String> listAssignedUser;
    private List<String> listServiceId;
    private List<String> listNewRequestCode;
    private List<String> listUpdatedUser;
}
