package vn.fis.eapprove.business.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ExpireDto {
    private Long id;

    private String taskAssignee;
    private LocalDateTime slaResponseTime;
    private LocalDateTime slaFinishTime;

    public ExpireDto(Long id, String taskAssignee, LocalDateTime slaResponseTime, LocalDateTime slaFinishTime) {
        this.id = id;
        this.taskAssignee = taskAssignee;
        this.slaResponseTime = slaResponseTime;
        this.slaFinishTime = slaFinishTime;
    }
}
