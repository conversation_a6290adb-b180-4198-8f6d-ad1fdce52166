package vn.fis.eapprove.business.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: PhucVM
 * Date: 16/03/2023
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
public class BpmTpSignZoneDto {

    private Long id;
    private Long bpmTemplatePrintId;
    private String taskDefKey;
    private String email;
    private String signedFile;
    private String signedFileName;
}
