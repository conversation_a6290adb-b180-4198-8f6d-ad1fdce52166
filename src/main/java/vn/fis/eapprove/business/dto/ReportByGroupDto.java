package vn.fis.eapprove.business.dto;

import lombok.Data;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class ReportByGroupDto {
        private Long serviceId;
        private String serviceName;
        private Long submissionType;
        private Long masterParentId;
        private String procInstId;
        private String title;
        private String requestCode;
        private Long priorityId;
        private String priorityName;
        private String procInstStatus;
        private Double slaFinish;
        private LocalDateTime startedTime;
        private LocalDateTime finishedTime;
        private String createdUser;
        private Long locationId;
        private LocalDateTime createdTime;
        private LocalDateTime actionCurrentTime;
        private String createdUserFullName;
        private String chartNodeName;
        private String chartNodeCode;
        private Long chartNodeId;
        private String chartShortName;
        private Long chartId;
        private String userTitleName;
        private String directManager;
        private String staffCode;
        private String userStatus;
        private Boolean isExpire;
        private String managerLevel;
        private String email;
        private Long ticketId;
        private String procDefId;
        private LocalDateTime slaFinishTime;
        private String assignee;
        private String assigneeChartNodeId;
        private String assigneeChartId;
        private String taskType;
        private String assigneeStatus;
        private LocalDate versionTime;
}
