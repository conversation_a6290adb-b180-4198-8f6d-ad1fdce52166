package vn.fis.eapprove.business.dto.report;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
public class ReportProcInstByChartNodeDto extends ReportProcInstDto {
    String assignee;
    String staffCode;
    List<String> chartNodeCode;
    String chartNodeName;
    String assigneeFullName;
    String userTitleName;
    String chartShortName;
    List<Long> chartNodeId;
    Long chartId;
}
