package vn.fis.eapprove.business.dto;

import lombok.Data;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTaskUser;


import java.time.LocalDateTime;
import java.util.Set;

@Data
public class BpmTaskCallApiDto extends BaseDto {
    private Long id;
    private String taskId;
    private String taskExecutionId;
    private String taskProcInstId;
    private String taskProcDefId;
    private String taskCaseInstId;
    private String taskDefKey;
    private String taskName;
    private Integer signStatus;
    private String taskPriority;
    private String taskAssignee;
    private LocalDateTime taskCreatedTime;
    private LocalDateTime taskStartedTime;
    private Double slaFinish;
    private Double slaResponse;
    private LocalDateTime taskFinishedTime;
    private LocalDateTime slaResponseTime;
    private LocalDateTime slaFinishTime;
    private Long responseDuration;
    private Long finishDuration;
    private Double taskDoneTime;
    private String taskType;
    private String taskCreatedUser;
    private String taskStatus;
    private boolean taskIsFirst;
    private Boolean assignType;
    private String actionUser;
    private BpmProcInst bpmProcInst;
    private Set<BpmTaskUser> bpmTaskUsers;
}