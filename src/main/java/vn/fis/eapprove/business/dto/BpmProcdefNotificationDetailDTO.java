package vn.fis.eapprove.business.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BpmProcdefNotificationDetailDTO {
    private Long id;
    private Long bpmProcdefNotificationId;
    private Long notificationTemplateId;
    private String actionCode;
    private String status;
    private Boolean isNew;
}
