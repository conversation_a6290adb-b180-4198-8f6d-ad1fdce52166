package vn.fis.eapprove.business.dto;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class ChartNodeDto {
    private Long id;
    private Long chartId;
    private String code;
    private String name;
    private Integer level;
    private String type;
    private Long nodeParentId;
    private String description;
    private String color;
    private boolean colorApplied;
    private Long chartLinkId;
    private String chartLinkName;
    private Long nodeLinkId;
    private String nodeLinkName;
    private LocalDate createdDate;
    private String createdUser;
    private LocalDate modifiedDate;
    private String modifiedUser;
    private Long totalStaff;
    private Long totalManager;
    private List<ChartNodeDto> chartNodeDtoList;
}