package vn.fis.eapprove.business.dto;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class ChartDto {
    private Long id;
    private String code;
    private String companyCode;
    private String name;
    private String type;
    private String description;
    private String status;
    private Integer userTotal;
    private Integer linkTotal;
    private String version;
    private LocalDate createdDate;
    private String createdUser;
    private LocalDate modifiedDate;
    private String modifiedUser;
    private List<ChartSharedUserDto> chartSharedUserDtoList;
    private List<ChartNodeDto> chartNodeDtoList;
    private List<String> userInfoName;
    private List<String> chartLinkName;
}