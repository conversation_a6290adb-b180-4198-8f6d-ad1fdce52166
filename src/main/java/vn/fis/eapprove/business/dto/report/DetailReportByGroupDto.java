package vn.fis.eapprove.business.dto.report;

import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.List;

@Data
@FieldDefaults(level = lombok.AccessLevel.PRIVATE)
public class DetailReportByGroupDto {
    String procInstId;
    String procInstName;
    String serviceName;
    String taskStatus;
    String taskType;
    String taskId;
    Long elapsedTime;
    Double delayTime;
    String priority;
    String reasonCancel;
    Long countReturned;
    String assignee;
    String createdUser;
    String assigneeFullName;
    List<String> assigneeChartNodeName;
    List<String> assigneeChartNodeCode;
    String assigneeChartShortName;
    List<String> assigneeTitleName;
    String createdUserFullName;
    String createdUserChartNodeName;
    String createdUserChartNodeCode;
    String createdUserChartShortName;
    String createdUserTitleName;
    String taskName;
    LocalDateTime createdTime;
    LocalDateTime finishedTime;
    LocalDateTime slaFinishTime;
    LocalDateTime startedTime;
    String requestCode;
    Long ticketId;
    Long chartId;
    String isExpire;
    String procDefId;
    List<Long> chartNodeIds;
}
