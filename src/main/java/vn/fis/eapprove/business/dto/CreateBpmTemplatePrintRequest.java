package vn.fis.eapprove.business.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.model.request.BpmTaskPrintRequest;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateBpmTemplatePrintRequest {
    Long id;
    List<BpmTaskPrintRequest> taskKey;
    List<FileConditionRequest> fileConditionRequests;
    String procDefId;
    String processName;
    String name;
    Long processId;
    String description;
    int type;
    List<String> shareWith;
    Map<String, String> stringMap;
    String jsonHistory;
    List<String> applyFor;
    List<Long> htmlConfig;
    List<FileConditionRequest> htmlConditionRequests;
    String configType;
}
