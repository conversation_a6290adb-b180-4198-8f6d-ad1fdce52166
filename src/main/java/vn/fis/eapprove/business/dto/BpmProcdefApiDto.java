package vn.fis.eapprove.business.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BpmProcdefApiDto {

    private Long id;
    private String procDefId;
    private String taskDefKey;
    private Long actionId;
    private Long apiId;
    private String header;
    private String body;
    private Long status;
    private String createdUser;
    private LocalDateTime createdTime;
    private String updatedUser;
    private LocalDateTime updatedTime;
    private String url;
    private String method;
    private String callOrder;
    private String successCondition;
    private String response;
    private Integer continueOnError;
    private String callCondition;
    private Boolean isDelete;
    private String description;
}
