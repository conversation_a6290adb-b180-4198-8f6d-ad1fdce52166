package vn.fis.eapprove.business.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * Generated by Speed Generator
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
public class BpmTaskDto extends BaseDto {
    private Long ticketId;
    private String taskId;
    private String taskDefKey;
    private String taskName;
    private String taskPriority;
    private String taskAssignee;
    private String taskCreatedTime;
    private String taskStartedTime;
    private Double taskSla;
    private String taskFinishedTime;
    private Double taskDoneTime;
    private String taskStatus;
    private String procInstId;
    private List<String> affected;
    private List<String> listStatus;
    private List<Map<String, Object>> listVariables;
    private Map<String, Object> responseTime;
    private Map<String, Object> finishTime;
    private String newTaskId;
    private Integer startPermission;
}