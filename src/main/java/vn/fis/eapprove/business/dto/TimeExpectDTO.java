package vn.fis.eapprove.business.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TimeExpectDTO {
    private String ticketProcInstId;
    private LocalDateTime ruTime;
    private String createdUser;
    private Double autoCancel;
    private Long ticketId;
    private Double autoClose;
    private LocalDateTime ticketFinishTime;
    private Double slaFinish;

}
