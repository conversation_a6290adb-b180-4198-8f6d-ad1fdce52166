package vn.fis.eapprove.business.dto;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
public class WorkingTimeDto {

    private Long id;
    private String name;
    private String description;
    private String timezone;
    private String startTime;
    private String saturdayWorking;
    private String sundayWorking;
    private Double morningWorking;
    private Double lunchBreak;
    private Double afternoonWorking;
    private String vacation;
    private LocalDateTime createdDate;
    private String createdUser;
    private LocalDate modifiedDate;
    private String modifiedUser;
    private String workScheduleCode;
    private String shareWith;
}
