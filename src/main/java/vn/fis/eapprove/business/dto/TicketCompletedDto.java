package vn.fis.eapprove.business.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketCompletedDto {
    private Long ticketId;
    private String ticketTitle;
    private String requestCode;
    private String appCode;
    private String systemCode;
    private String actionUser;
    private LocalDateTime taskFinishedTime;
    private Long serviceId;
    private String procDefId;
    private String startKey;
    private String createdUser;
}
