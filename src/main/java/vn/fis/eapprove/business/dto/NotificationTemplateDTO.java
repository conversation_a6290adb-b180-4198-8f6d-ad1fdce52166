package vn.fis.eapprove.business.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.domain.notification.entity.NotificationTemplateDetail;


import java.util.List;

/**
 * Author: AnhVTN
 * Date: 05/01/2023
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NotificationTemplateDTO {
    private Long id;
    private String type;
    private String title;
    private String actionCode;
    private String notificationObject;
    private String sourceType;
    private String content;
    private String shareWith;
    private String userCreate;
    private String createAt;
    private List<NotificationTemplateDetail> notifTmpDetail;
    private String fileIcon;
    private List<String> applyFor;
}
