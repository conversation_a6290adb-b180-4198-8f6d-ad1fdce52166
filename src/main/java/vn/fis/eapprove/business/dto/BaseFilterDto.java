package vn.fis.eapprove.business.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import vn.fis.spro.common.model.request.DateFilterDto;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
public class BaseFilterDto extends BaseDto{
    private List<String> listCompanyCode;
    private List<String> listCompanyCodeFilter;
    private List<String> listCompanyName;

    private List<String> listCreatedUser;
    private List<String> listModifiedUser;

    private List<DateFilterDto> listDateFilter;

    private List<String> listSharedUser;
    private List<String> listDescription;
}
