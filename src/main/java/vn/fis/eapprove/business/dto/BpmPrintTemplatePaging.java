package vn.fis.eapprove.business.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
public class BpmPrintTemplatePaging extends BaseDto {
    private Long id;
    private String header;
    private String footer;
    private String createdUser;
    private Date createdDate;
    private String updatedUser;
    private Date updatedDate;

}
