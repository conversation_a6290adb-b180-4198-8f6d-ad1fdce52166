package vn.fis.eapprove.business.dto;

import lombok.Data;

import java.util.List;

@Data
public class SearchPrintDto extends BaseFilterDto{
    private Boolean isShared;
    private List<String> sharedWith;
    private List<String> firstStep;
    private List<String> signStep;
    private List<String> name;
    private List<String> updatedUser;
    private List<String> procDefName;
    private List<String> createdUser;
    private Boolean specialFlow;
    private List<Long> lstGroupPermissionId;
    private List<Boolean> listStatus;
    private List<String> listCompanyCodeMemberAdmin;
}
