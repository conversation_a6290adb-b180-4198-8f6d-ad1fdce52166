package vn.fis.eapprove.business.dto.report;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Getter
@Setter
@FieldDefaults(level = lombok.AccessLevel.PRIVATE)
public class UserTitleDto implements Serializable {
    String username;
    String titleName;
    String chartNodeName;
    String chartNodeCode;
    Long chartNodeId;
    Long chartId;
}
