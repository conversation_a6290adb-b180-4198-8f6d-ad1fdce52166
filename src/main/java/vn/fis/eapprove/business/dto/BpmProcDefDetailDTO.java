package vn.fis.eapprove.business.dto;


import lombok.*;
import vn.fis.eapprove.business.model.response.ServiceNamePopUpResponse;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Builder
public class BpmProcDefDetailDTO {
    private String id;
    private String procDefId;
    private String name;
    private List<ServiceNamePopUpResponse> serviceNamePopUp;
    private String serviceCount;
    private String serviceName;
    private String description;
    private String key;
    private String deploymentId;
    private String resourceName;
    private byte[] bytes;
    private String fileXml;
    private String prioritized;
    private String userCreated;
    private String status;
    private List<BpmOwnerDto> owners;
    private String createdDate;
    private String createdUser;
    private String updatedDate;
    private String userUpdate;
    private String autoClose;
    private String autoCancel;
    private List<String> blockLocation;
    private Boolean stepByStepResultForCreate;
    private Boolean inFormTo;
    private Boolean location;
    private Boolean update;
    private Boolean requestUpdate;
    private Boolean createNewAndDouble;
}
