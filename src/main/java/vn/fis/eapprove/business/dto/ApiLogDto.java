package vn.fis.eapprove.business.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.spro.common.model.request.DateFilterDto;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiLogDto extends BaseSearchDto{
    private String fromDate;
    private String toDate;

    private List<String> listMethod;
    private List<String> listApiName;
    private List<String> listUrl;
    private List<String> listHeader;
    private List<String> listRequestBody;
    private List<DateFilterDto> listDateFilter;
    private List<String> listResponseStatus;
    private List<String> listResponseData;
    private List<String> listApiType;
    private List<String> listRequestCode;
    private List<String> listTicketName;
    private List<String> listTaskDefKey;
    private List<String> listAppCode;
}
