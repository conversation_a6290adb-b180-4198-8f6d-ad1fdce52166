package vn.fis.eapprove.business.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BpmProcdefNotificationDTO {
    private Long id;
    private Long bpmProcdefId;
    private String procDefId;
    private String actionCode;
    private String notificationObject;
    private Long notificationTemplateId;
    private String taskDefKey;
    private String taskDefKeyNotification;
    private String fieldNotification;
    private String status;
    private LocalDateTime createdTime;
    private String createdUser;
    private LocalDateTime updatedTime;
    private String updatedUser;
    private Boolean addMoreConfig;
    private Boolean offNotification;
    private Boolean isNew;
}
