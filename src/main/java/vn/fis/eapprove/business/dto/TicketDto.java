package vn.fis.eapprove.business.dto;

import lombok.Data;
import vn.fis.spro.common.util.DateTimeUtils;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * Author: PhucVM
 * Date: 11/11/2022
 */
@Data
public class TicketDto {

    private Long ticketId;
    private String ticketTitle;
    private String procDefId;
    private String procInstId;
    private Long serviceId;
    private String serviceName;
    private String ticketStatus;
    private Date createdTime;
    private Date startedTime;
    private Date endTime;
    private Date calceledTime;
    private Date closedTime;
    private Date finishTime;
    private Date editTime;
    private String comment;
    private String cancelReason;
    private Long autoClose;

    // additional info
    private String taskNames;

    public TicketDto(Long ticketId, String ticketTitle, String procDefId, String procInstId, Long serviceId, String serviceName, String ticketStatus,
                     LocalDateTime createdTime, LocalDateTime startedTime, LocalDateTime endTime, LocalDateTime calceledTime, LocalDateTime closedTime,
                     LocalDateTime finishTime, LocalDateTime editTime, String comment, String cancelReason, Long autoClose) {
        this.ticketId = ticketId;
        this.ticketTitle = ticketTitle;
        this.procDefId = procDefId;
        this.procInstId = procInstId;
        this.serviceId = serviceId;
        this.serviceName = serviceName;
        this.ticketStatus = ticketStatus;
        this.createdTime = DateTimeUtils.localDateTimeToDate(createdTime);
        this.startedTime = DateTimeUtils.localDateTimeToDate(startedTime);
        this.endTime = DateTimeUtils.localDateTimeToDate(endTime);
        this.calceledTime = DateTimeUtils.localDateTimeToDate(calceledTime);
        this.closedTime = DateTimeUtils.localDateTimeToDate(closedTime);
        this.finishTime = DateTimeUtils.localDateTimeToDate(finishTime);
        this.editTime = DateTimeUtils.localDateTimeToDate(editTime);
        this.comment = comment;
        this.cancelReason = cancelReason;
        this.autoClose = autoClose;
    }
}
