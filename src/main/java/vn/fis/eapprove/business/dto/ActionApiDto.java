package vn.fis.eapprove.business.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Author: PhucVM
 * Date: 04/12/2022
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActionApiDto implements Serializable {

    private static final long serialVersionUID = 9084482964096388701L;

    private Long bpmProcdefApiId;
    private String exHeader;
    private String exBody;
    private String callOrder;
    private String successCondition;
    private String exSuccessCondition;
    private String url;
    private String method;
    private String header;
    private String body;
    private Long authenApiId;
    private String authenUrl;
    private String authenMethod;
    private String authenHeader;
    private String authenBody;
    private String tokenAttribute;
    private Long baseUrlId;
    private String baseUrl;
    private String response;
    private Integer returnResponse;
    private String exResponse;
    private String errorAttribute;
    private Integer continueOnError;
    private Integer exContinueOnError;
    private String callCondition;
}
