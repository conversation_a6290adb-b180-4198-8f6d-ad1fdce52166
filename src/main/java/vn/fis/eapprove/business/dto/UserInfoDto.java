package vn.fis.eapprove.business.dto;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Data
public class UserInfoDto {
    private Long id;
    private Long chartNodeId;
    private String chartNodeName;
    private Long chartId;
    private Long locationId;
    private String staffCode;
    private String firstname;
    private String lastname;
    private String fullName;
    private String username;
    private String name;
    private String title;
    private String email;
    private List<String> emailList;
    private List<String> usernames;
    private List<Long> idList;
    private String managerLevel;
    private String phone;
    private String status;
    private String handoverStatus;
    private boolean available;
    private Map<String, Object> addition;
    private String directManager;
    private String viceManager;
    private String description;
    private String createdUser;
    private LocalDate createdDate;
    private LocalDate modifiedDate;
    private String modifiedUser;
    private Integer hasSecretary;
    private Long importChartId;

    //interactive
    private List<String> activeUser;
    private List<String> inactiveUser;
    private List<Long> activeNode;
    private List<Long> inactiveNode;

    private List<InteractiveUserDto> interactiveUserDtos;
    private List<InteractiveNodeDto> interactiveNodeDtos;

    private String positionCode;
    private String positionName;
    private String titleCode;
    private String titleName;
    private Integer concurrently;

    private List<Long> chartNodeIdSecretary;

}