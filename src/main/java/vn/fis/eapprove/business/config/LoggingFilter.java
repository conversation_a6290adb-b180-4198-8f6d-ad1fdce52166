package vn.fis.eapprove.business.config;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StreamUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.util.ValidationUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.util.*;
import java.util.zip.GZIPInputStream;
import java.util.zip.ZipException;

/**
 * vn.fis.eapprove.business.config.LoggingFilter.java
 * TungHuynh
 * <NAME_EMAIL>
 * Date 11/05/2019 9:47 AM
 * extends class này nếu cần ghi log API
 */
@Slf4j
@Component
public class LoggingFilter extends OncePerRequestFilter {

    public static final String[] AUTH_WHITELIST = {
            "/v2/api-docs", "/swagger-resources/**", "/favicon.ico",
            "/swagger-ui.html", "/webjars/**", "/error", "/csrf",
            "/vendor/altair/**"};
    private final static String TRANS_ID = "request_id";
    private final List<MediaType> VISIBLE_TYPES = Arrays.asList(
            MediaType.valueOf("text/*"),
            MediaType.APPLICATION_FORM_URLENCODED,
            MediaType.APPLICATION_JSON,
            MediaType.APPLICATION_XML,
            MediaType.valueOf("application/*+json"),
            MediaType.valueOf("application/*+xml"),
            MediaType.MULTIPART_FORM_DATA
    );
    @Autowired
    private CredentialHelper credentialHelper;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        //Logging Request
        try {
            long startTime = System.currentTimeMillis();
            String uuid;
            String uuidHeader = request.getHeader(TRANS_ID);
            if (StringUtil.stringIsNullOrEmty(uuidHeader)) {
                uuid = UUID.randomUUID().toString();
            } else {
                uuid = uuidHeader;
            }
            MDC.put(TRANS_ID, uuid);
            request.setAttribute("startTime", startTime);
            request.setAttribute(TRANS_ID, uuid);
            response.setHeader(TRANS_ID, uuid);
            log.info("BEGIN " + request.getRequestURI());

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        if (isAsyncDispatch(request)) {
            filterChain.doFilter(request, response);
        } else {
            doFilterWrapped(wrapRequest(request), wrapResponse(response), filterChain);
        }
        try {
            Long elapsedTimeMs = (Long) request.getAttribute("elapsedTimeMs");
            if (elapsedTimeMs == null) {
                long startTime = (long) request.getAttribute("startTime");
                elapsedTimeMs = System.currentTimeMillis() - startTime;
            }
            if (response.getStatus() != 200) {
                log.info("END " + request.getRequestURI() + " in " + elapsedTimeMs + " ms" + " | http-status-error " + response.getStatus() + " | Error " + response.getHeader("Error"));
            } else {
                log.info("END " + request.getRequestURI() + " in " + elapsedTimeMs + " ms");
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    protected void doFilterWrapped(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response, FilterChain filterChain) throws ServletException, IOException {
        long start = new Date().getTime();
        try {
            filterChain.doFilter(request, response);
        } finally {
            afterRequest(request, response, start);
            response.copyBodyToResponse();
        }
    }

    protected void afterRequest(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response, long start) {
        LogApiDTO logApiDTO = new LogApiDTO();
        try {
//            AccessToken accessToken = credentialHelper.getAccessToken(request.getHeader("authorization").replace("Bearer ", ""));
            logApiDTO.setUserName(credentialHelper.getJWTPayload().getEmail());
        } catch (Exception e) {
        }
        logApiDTO.setProcessTime(String.valueOf(new Date().getTime() - start));
        String queryString = request.getQueryString();
        if (HttpMethod.OPTIONS.matches(request.getMethod())) {
            return;
        }

        //Ignore swagger-ui
        Optional visible = Arrays.stream(AUTH_WHITELIST)
                .filter(item -> new AntPathMatcher().match(item, request.getRequestURI()))
                .findFirst();
        if (visible.isPresent()) {
            return;
        }

        logApiDTO.setMethod(request.getMethod());
        logApiDTO.setUri(queryString == null ? request.getRequestURI() : (request.getRequestURI() + "?" + queryString));
        StringBuilder reqHeader = new StringBuilder();
        Collections.list(request.getHeaderNames()).forEach(headerName ->
                Collections.list(request.getHeaders(headerName))
                        .forEach(headerValue -> {
                                    if (!"cookie".equalsIgnoreCase(headerName))
                                        reqHeader.append(String.format("%s:%s|", headerName, headerValue));
                                }
                        ));
        logApiDTO.setRequestHeader(reqHeader.toString());
        String reqPairs = request.getHeader(TRANS_ID);
        if (StringUtil.stringIsNullOrEmty(reqPairs)) {
            reqPairs = StringUtil.nvl(request.getAttribute(TRANS_ID), "");
        }
        logApiDTO.setRequestPair(reqPairs);
        String reqBody = "";
        byte[] reqContent = request.getContentAsByteArray();
        if (reqContent.length > 0) {
            reqBody = logContent(reqContent, request.getContentType(), request.getCharacterEncoding(), request.getRemoteAddr());
            if (logApiDTO.getUri().contains("/login")) {
                reqBody = reqBody.replaceAll("\"password\"(\\s*:\\s*)\"([^\"]*)\"", "\"password\"$1\"******\"");
            }
            reqBody = reqBody.replaceAll("\"data:(\\w+\\/\\w+);base(\\d{2}),([\\w=\\/+\\\\]+)\"", "\"data:$1;base$2,[not show data file]\"");
            //reqBody = reqBody.replaceAll("\"data:(\\w+\\/\\w+);base(\\d{2}),([\\w=\\/+]+)\"", "\"data:$1;base$2,[not show data file]\"");
            if (reqBody.length() > 2000) {
                reqBody = reqBody.substring(0, 1999) + " [...too long (2000)]";
            }
        }
        logApiDTO.setRequestBody(reqBody);
        logApiDTO.setResponseStatus(String.valueOf(response.getStatus()));
        String respBody = "";
        // phucvm3: check compressed response gzip
        if (isCompressedResponse(response)) {
            try {
                respBody = getCompressedResponseData(response);
            } catch (ZipException ex) {
                respBody = null;
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }
        if (StringUtil.stringIsNullOrEmty(respBody)) {
            byte[] respContent = response.getContentAsByteArray();
            if (respContent.length > 0) {
                respBody = logContent(respContent, response.getContentType(), response.getCharacterEncoding(), request.getRemoteAddr());
            }
        }

        if (!ValidationUtils.isNullOrEmpty(respBody)) {
            respBody = respBody.replaceAll("\"data:(\\w+\\/\\w+);base(\\d{2}),([\\w=\\/+\\\\]+)\"", "\"data:$1;base$2,[not show data file]\"");
            if (respBody.length() > 2000) {
                respBody = respBody.substring(0, 1999) + " [...too long (2000)]";
            }
        }

        logApiDTO.setResponseBody(respBody);
        loggingDebug(logApiDTO);
    }

    private void loggingDebug(LogApiDTO item) {
        StringBuilder content = new StringBuilder("LOG API - ");
        content.append("Uri:" + item.getUri() + "|");
        content.append("ProcessTime:" + item.getProcessTime() + "|");
        content.append("SessionId:" + item.getSessionId() + "|");
        content.append("UserName:" + item.getUserName() + "|");
        content.append("UserId:" + item.getUserId() + "|");
        content.append("RequestPair:" + item.getRequestPair() + "|");
        content.append("Method:" + item.getMethod() + "|");
        content.append("ResponseStatus:" + item.getResponseStatus());
        log.debug(content.toString());
        log.debug("LOG API - RequestHeader:" + item.getRequestHeader());
        String reqBody;
        if (item.getRequestBody() != null && item.getRequestBody().length() > 1000) {
            reqBody = item.getRequestBody().substring(0, 999) + " [...too long (1000)]";
        } else {
            reqBody = item.getRequestBody();
        }
        log.debug("LOG API - RequestBody:" + reqBody);
        String respBody;
        if (item.getResponseBody() != null && item.getResponseBody().length() > 1000) {
            respBody = item.getResponseBody().substring(0, 999) + " [...too long (1000)]";
        } else {
            respBody = item.getResponseBody();
        }
        log.debug("LOG API - ResponseBody:" + respBody);

    }

    private boolean isCompressedResponse(ContentCachingResponseWrapper response) {
        if (response != null) {
            String gzip = response.getHeader(HttpHeaders.CONTENT_ENCODING);
            return !ValidationUtils.isNullOrEmpty(gzip) && gzip.contains("gzip");
        }
        return false;
    }

    private String getCompressedResponseData(ContentCachingResponseWrapper response) throws Exception {
        String contentType = response.getContentType();
        String characterEncoding = response.getCharacterEncoding();

        MediaType mediaType = MediaType.valueOf(contentType);
        boolean visible = VISIBLE_TYPES.stream().anyMatch(visibleType -> visibleType.includes(mediaType));
        if (visible) {
            try (final InputStream compressedInputStream = response.getContentInputStream()) {
                try (final InputStream gzipInputStream = new GZIPInputStream(compressedInputStream)) {
                    return StreamUtils.copyToString(gzipInputStream, Charset.forName(characterEncoding));
                }
            } catch (Exception ex) {
                throw ex;
            }
        }

        return null;
    }

    private String logContent(byte[] content, String contentType, String contentEncoding, String prefix) {
        MediaType mediaType = MediaType.valueOf(contentType);
        boolean visible = VISIBLE_TYPES.stream().anyMatch(visibleType -> visibleType.includes(mediaType));
        if (visible) {
            try {
                return new String(content, contentEncoding);
            } catch (UnsupportedEncodingException e) {
                log.info(String.format("logContent1: %s [%s bytes content]", prefix, content.length));
            }
        } else {
            log.info(String.format("logContent2: %s [%s bytes content]", prefix, content.length));
        }
        return "";
    }

    private ContentCachingRequestWrapper wrapRequest(HttpServletRequest request) {
        if (request instanceof ContentCachingRequestWrapper) {
            return (ContentCachingRequestWrapper) request;
        } else {
            return new ContentCachingRequestWrapper(request);
        }
    }

    private ContentCachingResponseWrapper wrapResponse(HttpServletResponse response) {
        if (response instanceof ContentCachingResponseWrapper) {
            return (ContentCachingResponseWrapper) response;
        } else {
            return new ContentCachingResponseWrapper(response);
        }
    }

    @Data
    class LogApiDTO {
        private String requestId;
        private String method;
        private String uri;
        private String requestHeader;
        private String requestBody;
        private String responseStatus;
        private String responseBody;
        private String processTime;
        private Date createTime;
        private Integer sessionId;
        private Integer userId;
        private String userName;
        private String requestPair;
    }

}
