//package vn.fis.eapprove.business.config;
//
//import camundajar.impl.com.google.gson.JsonNull;
//import camundajar.impl.com.google.gson.JsonObject;
//
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import org.keycloak.adapters.springsecurity.authentication.KeycloakCookieBasedRedirect;
//import org.springframework.security.core.AuthenticationException;
//import org.springframework.security.web.authentication.AuthenticationFailureHandler;
//
//
//import java.io.IOException;
//import java.io.PrintWriter;
//
//public class CustomKeycloakAuthenticationFailureHandler implements AuthenticationFailureHandler {
//
//    public CustomKeycloakAuthenticationFailureHandler() {
//    }
//
//    @Override
////    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) throws IOException, ServletException {
////        if (!response.isCommitted()) {
////            if (KeycloakCookieBasedRedirect.getRedirectUrlFromCookie((javax.servlet.http.HttpServletRequest) request) != null) {
////                response.addCookie(KeycloakCookieBasedRedirect.createCookieFromRedirectUrl((String) null));
////            }
////
////            // Set the response status to 401 (Unauthorized)
////            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
////
////            // Create a JSON response object
////            JsonObject jsonResponse = new JsonObject();
////            jsonResponse.addProperty("code", 401);
////            jsonResponse.addProperty("message", "Lỗi authentication");
////            jsonResponse.add("data", JsonNull.INSTANCE);
////
////            // Set the response content type to JSON
////            response.setContentType("application/json");
////
////            // Write the JSON response to the output stream
////            try (PrintWriter out = response.getWriter()) {
////                out.print(jsonResponse.toString());
////                out.flush();
////            }
////        } else if (200 <= response.getStatus() && response.getStatus() < 300) {
////            throw new RuntimeException("Success response was committed while authentication failed!", exception);
////        }
////    }
//}
