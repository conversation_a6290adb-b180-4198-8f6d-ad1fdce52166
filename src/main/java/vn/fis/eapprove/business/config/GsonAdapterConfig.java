package vn.fis.eapprove.business.config;
import com.google.gson.*;
import java.lang.reflect.Type;
import java.time.LocalDateTime;

public class GsonAdapterConfig implements JsonSerializer<Object>, JsonDeserializer<Object> {

    @Override
    public JsonElement serialize(Object src, Type typeOfSrc, JsonSerializationContext context) {
        // Trả về null nếu gặp type object khi serialize
        return null;
    }

    @Override
    public LocalDateTime deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        // Tr<PERSON> về null nếu gặp type object khi deserialize
        return null;
    }
}

