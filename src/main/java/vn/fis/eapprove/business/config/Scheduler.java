package vn.fis.eapprove.business.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.assign.service.AssignManager;

@Component
public class Scheduler {

    @Autowired
    private AssignManager assignManager;

    @Scheduled(cron = "0 0 * * * ?")
    public void updateStatusAssignManager() {
        assignManager.updateStatusAssignManager();
    }
}