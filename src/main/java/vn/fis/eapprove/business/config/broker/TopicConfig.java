//package vn.fis.eapprove.business.config.broker;
//
//import org.apache.kafka.clients.CommonClientConfigs;
//import org.apache.kafka.clients.admin.AdminClientConfig;
//import org.apache.kafka.clients.admin.NewTopic;
//import org.apache.kafka.common.config.SaslConfigs;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
//import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.kafka.config.TopicBuilder;
//import org.springframework.kafka.core.KafkaAdmin;
//import vn.fis.spro.common.SproProperties;
//import vn.fis.spro.common.constants.TopicConstants;
//
//import java.util.HashMap;
//import java.util.Map;
//
////@Configuration
////@ConditionalOnBean(KafkaConfig.class)
//public class TopicConfig {
//
//    @Autowired
//    private KafkaProperties kafkaProperties;
//
//    @Autowired
//    private SproProperties sproProperties;
//
//    @Value("${spring.kafka.authentication.enable}")
//    private boolean kafkaAuthenticationEnable;
//
//    @Value("${spring.kafka.authentication.username}")
//    private String kafkaAuthenticationUsername;
//
//    @Value("${spring.kafka.authentication.password}")
//    private String kafkaAuthenticationPassword;
//
//    @Bean
//    public KafkaAdmin kafkaAdmin() {
//        Map<String, Object> configs = new HashMap<>();
//        configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaProperties.getBootstrapServers());
//        if (kafkaAuthenticationEnable) {
//            configs.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
//            configs.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"" + kafkaAuthenticationUsername + "\" password=\"" + kafkaAuthenticationPassword + "\";");
//            configs.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");
//        }
//        return new KafkaAdmin(configs);
//    }
//
//    @Bean
//    public NewTopic topicImportMasterData() {
//        return TopicBuilder.name(TopicConstants.TOPIC_MASTER_DATA_IMPORT).partitions(sproProperties.getKafka().getTopicPartitions()).replicas(sproProperties.getKafka().getTopicReplicas()).build();
//    }
//
//    @Bean
//    public NewTopic topicActionApiAfter() {
//        return TopicBuilder.name(TopicConstants.TOPIC_ACTION_API_AFTER)
//                .partitions(sproProperties.getKafka().getTopicPartitions())
//                .replicas(sproProperties.getKafka().getTopicReplicas())
//                .build();
//    }
//}
