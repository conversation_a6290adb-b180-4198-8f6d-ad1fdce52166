package vn.fis.eapprove.business.config;

import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import vn.fis.eapprove.business.interceptor.RestTemplateHeaderInterceptor;
import vn.fis.eapprove.business.interceptor.RestTemplateLoggingInterceptor;

import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

@Configuration
@EnableAsync
@EnableScheduling
@ComponentScan(basePackages = {"vn.fis.eapprove.business.config"})
public class ApplicationConfig implements WebMvcConfigurer {

    @Autowired
    public ApplicationConfig(RestTemplate restTemplate,
                             RestTemplateHeaderInterceptor restTemplateHeaderInterceptor,
                             RestTemplateLoggingInterceptor restTemplateLoggingInterceptor) throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        restTemplate.setInterceptors(List.of(restTemplateHeaderInterceptor, restTemplateLoggingInterceptor));
    }

    @Bean
    public ModelMapper modelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
        return modelMapper;
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE") // Cho phép các phương thức yêu cầu được gọi từ phía client
                .allowedHeaders("*"); // Cho phép tất cả các header được sử dụng trong yêu cầu
    }
}
