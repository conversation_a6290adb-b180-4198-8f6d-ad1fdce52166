package vn.fis.eapprove.business.exception.report;

import org.springframework.http.HttpStatus;

public class BusinessCode {
    public static final ResponseStatus SUCCESS =
            new ResponseStatus("SUCCESS", "success", HttpStatus.OK);
    public static final ResponseStatus INTERNAL_SERVER_ERROR =
            new ResponseStatus("INTERNAL_SERVER_ERROR", "Something went wrong", HttpStatus.INTERNAL_SERVER_ERROR);

    public static final ResponseStatus NOT_EXIST =
            new ResponseStatus("ID_NOT_EXIST", "id not exist", HttpStatus.INTERNAL_SERVER_ERROR);

    public static final ResponseStatus TOKEN_INVALID =
            new ResponseStatus("TOKEN_INVALID", "Token invalid", HttpStatus.UNAUTHORIZED);

    private BusinessCode() {
    }
}
