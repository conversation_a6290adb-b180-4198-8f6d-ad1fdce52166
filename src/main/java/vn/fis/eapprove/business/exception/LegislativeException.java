package vn.fis.eapprove.business.exception;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;
import vn.fis.eapprove.business.exception.rest.response.ErrorResponse;

@Getter
@Setter
@Builder
public class LegislativeException extends RuntimeException {

    private static final long serialVersionUID = 1138067521792343348L;
    private final String code;
    private final String message;
    private final HttpStatus status;
    private final Object detailError;
    private final Throwable rootCause;

    public LegislativeException(ErrorResponse errorResponse) {
        this(errorResponse.getCode(), errorResponse.getMessage(), errorResponse.getHttpStatus());
    }

    public LegislativeException(String errorCode, String message, HttpStatus status) {
        this(errorCode, message, status, null, null);
    }

    public LegislativeException(String errorCode, String message, HttpStatus status, Object detail) {
        this(errorCode, message, status, detail, null);
    }

    public LegislativeException(String errorCode, String message, HttpStatus status, Object detail,
                                Throwable cause) {
        this.code = errorCode;
        this.message = message;
        this.status = status;
        this.detailError = detail;
        this.rootCause = cause;
    }
}
