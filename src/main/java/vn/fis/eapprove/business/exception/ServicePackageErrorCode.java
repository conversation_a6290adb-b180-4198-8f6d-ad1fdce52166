package vn.fis.eapprove.business.exception;

import org.springframework.http.HttpStatus;
import vn.fis.eapprove.business.exception.rest.response.ErrorResponse;

public final class ServicePackageErrorCode {

    public static final ErrorResponse NAME_MAX_LENGTH = new ErrorResponse("000410",
            "Tên dịch vụ vượt quá 100 ký tự", HttpStatus.BAD_REQUEST);

    public static final ErrorResponse SERVICE_PACKAGE_NOT_EXIST = new ErrorResponse("000411",
            "Service package not exist", HttpStatus.NOT_FOUND);

    public static final ErrorResponse EVENT_NOT_FOUND = new ErrorResponse("000522",
            "Event not found", HttpStatus.NOT_FOUND);
    public static final ErrorResponse FOOD_NOT_FOUND = new ErrorResponse("000523",
            "Food not found", HttpStatus.NOT_FOUND);
}

