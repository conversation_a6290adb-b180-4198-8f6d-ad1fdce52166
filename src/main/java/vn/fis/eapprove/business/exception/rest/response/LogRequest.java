package vn.fis.eapprove.business.exception.rest.response;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.http.HttpMethod;

import java.util.UUID;

@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class LogRequest {
    private UUID id;
    private HttpMethod method;
    private String uri;
    private String payload;
}
