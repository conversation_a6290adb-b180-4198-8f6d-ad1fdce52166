package vn.fis.eapprove.business.exception;

import org.springframework.http.HttpStatus;
import vn.fis.eapprove.business.exception.rest.response.ErrorResponse;

/**
 * Default error code will be defined here.
 */
public final class DefaultErrorCode {

    public static final ErrorResponse DEFAULT_INTERNAL_SERVER_ERROR = new ErrorResponse("000500",
            "Internal Server Error", HttpStatus.INTERNAL_SERVER_ERROR);
    public static final ErrorResponse DEFAULT_SERVICE_UNAVAILABLE = new ErrorResponse("000503",
            "Service Unavailable", HttpStatus.SERVICE_UNAVAILABLE);
    public static final ErrorResponse DEFAULT_BAD_REQUEST = new ErrorResponse("000400",
            "Bad Request", HttpStatus.BAD_REQUEST);
    public static final ErrorResponse DEFAULT_FORBIDDEN = new ErrorResponse("000403",
            "Forbidden", HttpStatus.FORBIDDEN);
    public static final ErrorResponse DEFAULT_NOT_FOUND = new ErrorResponse("000404",
            "Item not found", HttpStatus.NOT_FOUND);
    public static final ErrorResponse AUTHENTICATION_REQUIRED = new ErrorResponse("000510",
            "This request is required authentication", HttpStatus.UNAUTHORIZED);
    public static final ErrorResponse AUTHENTICATION_FAILED = new ErrorResponse("000511",
            "Authentication failed", HttpStatus.UNAUTHORIZED);
    public static final ErrorResponse TOKEN_INVALID = new ErrorResponse("000512",
            "Token Invalid", HttpStatus.UNAUTHORIZED);
    public static final ErrorResponse API_SEND_MAIL_FAILED = new ErrorResponse("000514",
            "Error on call api send mail", HttpStatus.INTERNAL_SERVER_ERROR);
    public static final ErrorResponse HASH_INVALID_OR_EXPIRED = new ErrorResponse("000520",
            "Hash invalid or expired", HttpStatus.BAD_REQUEST);
    private DefaultErrorCode() {
        throw new IllegalStateException("Utility class");
    }
}
