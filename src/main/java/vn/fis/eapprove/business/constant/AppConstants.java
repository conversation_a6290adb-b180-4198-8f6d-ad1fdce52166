package vn.fis.eapprove.business.constant;

import vn.fis.eapprove.business.domain.task.entity.TaskAction;


import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public interface AppConstants {

    String TOPIC_NAME_EMAIL = "topic.email";
    String GROUP_ID_EMAIL = "group.email";

    String TOPIC_NAME_NOTIFICATION = "topic.notification";
    String GROUP_ID_NOTIFICATION = "group.notification";

    String TOPIC_CONTACT_MAIL = "topic.contact";
    String TOPIC_VERIFIED_MAIL = "topic.verified";
    String GROUP_EMAIL = "group.contact";

    interface Cache {
        Map<String, TaskAction> ACTION_CODE_TO_TASK_ACTION = new ConcurrentHashMap<String, vn.fis.eapprove.business.domain.task.entity.TaskAction>();
        Map<Long, String> AUTHEN_API_ID_TO_TOKEN = new ConcurrentHashMap<>();
    }

    interface BpmnNodeType {
        String END_EVENT = "endEvent";
    }

    interface AdditionalValueKey {
        String TYPE = "type";
        String VALUE = "value";
    }

    interface AdditionalVarType {
        String DATE = "DATE";
        String FILE = "FILE";
    }

    interface RecallStatus {
        String AGREE = "AGREE";
        String DISAGREE = "DISAGREE";
    }
}
