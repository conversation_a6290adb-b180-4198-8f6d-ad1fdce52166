package vn.fis.eapprove.business.constant;

public enum ExcelEnum {
    PROCESSING("processing"),
    COMPLETED("completed"),
    ERROR("error"),
    ACTIVE("active"),
    DEACTIVE("deactive"),

    IS_SPECIAL_CHAR("không chứa ký tự đặc biệt"),
    VALIDATION("validation"),
    DUPLICATE("không được có dữ liệu trùng"),
    NULL("null"),
    IS_WHITE_SPACE("dữ liệu không được chứa khoảng trắng"),

    ID_IS_EXIST("ID đã tồn tại"),
    NAME_IS_EXIST("Name đã tồn tại"),
    EXCEL_FILE_ERROR("File excel không hợp lệ"),
    ID_VALIDATION("ID không hợp lệ"),
    FILE_EMPTY("File không có dữ liệu"),
    FILE_DUPLICATE("<PERSON><PERSON> nhiều dòng trùng dữ liệu. <PERSON><PERSON> lòng kiểm tra và thử lại"),

    KEY_IS_SPECIAL_CHAR("Key không chứa ký tự đặc biệt"),

    KEY_IS_WHITE_SPACE("Key không chứa khoảng trắng"),

    KEY_IS_VIETNAMESE("Key không chứa tiếng việt");

    public String status;

    private ExcelEnum(String status) {
        this.status = status;
    }
}
