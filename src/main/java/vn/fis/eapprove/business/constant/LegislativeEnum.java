package vn.fis.eapprove.business.constant;

public class LegislativeEnum {
    public enum LegislativeDetailType {
        PROCESS_USER("PROCESS_USER", "<PERSON>ư<PERSON>i thực hiện quản lý kế hoạch"),
        PROCESS_DETAIL("PROCESS_DETAIL", "Chi tiết nhiệm vụ"),
        FILE("FILE", "Văn bản bàn giao");

        public final String code;
        public final String detail;

        LegislativeDetailType(String code, String detail) {
            this.code = code;
            this.detail = detail;
        }
    }

    public enum LegislativeStatus {
        WAITING("WAITING", "Chờ tới lượt"),
        RESEARCHING("RESEARCHING", "<PERSON><PERSON> nghi<PERSON> c<PERSON>, đề xuất đưa vào Chương trình lập pháp"),
        PROPOSING_POLICY("PROPOSING_POLICY", "Lập đề xuất chính sách (Đ<PERSON><PERSON> với trường hợp phải lập ch<PERSON>h sách)"),
        POLICY_APPRAISED("POLICY_APPRAISED", "Đ<PERSON> gửi thẩm định ch<PERSON> sách (Đối với trường hợp phải lập chính sách)"),
        POLICY_APPRAISAL_SUBMITTED("POLICY_APPRAISAL_SUBMITTED", "Đã thẩm định chính sách (Đối với trường hợp phải lập chính sách)"),
        POLICY_SUBMITTED("POLICY_SUBMITTED", "Đã trình Chính phủ (Đối với trường hợp phải lập chính sách)"),
        POLICY_APPROVED("POLICY_APPROVED", "Đã thông qua chính sách (Đối với trường hợp phải lập chính sách)"),
        DRAFTING("DRAFTING", "Đang soạn thảo dự thảo"),
        DRAFT_APPRAISED("DRAFT_APPRAISED", "Đã gửi thẩm định dự thảo"),
        DRAFT_APPRAISAL_SUBMITTED("DRAFT_APPRAISAL_SUBMITTED", "Đã thẩm định dự thảo"),
        PREPARING_GOV_SUBMISSION("PREPARING_GOV_SUBMISSION", "Đang hoàn thiện hồ sơ trình Chính phủ"),
        SUBMITTING_TO_GOV("SUBMITTING_TO_GOV", "Đang trình Chính phủ"),
        PREPARING_ASSEMBLY_SUBMISSION("PREPARING_ASSEMBLY_SUBMISSION", "Đang hoàn thiện hồ sơ trình Quốc hội"),
        SUBMITTING_FOR_REVIEW("SUBMITTING_FOR_REVIEW", "Đã gửi thẩm tra"),
        REVIEW_COMPLETED("REVIEW_COMPLETED", "Đã thẩm tra dự thảo"),
        UNDER_ASSEMBLY_REVIEW("UNDER_ASSEMBLY_REVIEW", "Đang trình Quốc hội/UBTVQH xem xét, thông qua"),
        ASSEMBLY_APPROVED("ASSEMBLY_APPROVED", "Quốc hội/UBTVQH đã thông qua");

        public final String code;
        public final String detail;

        LegislativeStatus(String code, String detail) {
            this.code = code;
            this.detail = detail;
        }
    }

    public enum LegislativeProgramType {
        PROCESSING("law", "Luật"),
        COMPLETED("circular", "Thông tư"),
        CANCEL("decree", "Nghị định");

        public final String code;
        public final String detail;

        LegislativeProgramType(String code, String detail) {
            this.code = code;
            this.detail = detail;
        }
    }

    public enum LegislativeActivityStatus {
        PROCESSING("PROCESSING", "Đang xử lý"),
        COMPLETED("COMPLETED", "Hoàn thành"),
        CANCEL("CANCEL", "Hủy"),
        DEACTIVE("DEACTIVE", "Vô hiệu hóa"),
        ;

        public final String code;
        public final String detail;

        LegislativeActivityStatus(String code, String detail) {
            this.code = code;
            this.detail = detail;
        }

        public static String getDetailByCode(String code) {
            for (LegislativeActivityStatus status : LegislativeActivityStatus.values()) {
                if (status.code.equalsIgnoreCase(code)) {
                    return status.detail;
                }
            }
            return null;
        }
    }

    public enum LegislativeDetailStatus {
        PROCESSING("PROCESSING", "Đang hoạt động"),
        COMPLETED("COMPLETED", "Hoàn thành"),
        CANCEL("CANCEL", "Hủy"),
        DEACTIVE("DEACTIVE", "Vô hiệu hóa"),
        WAITING("WAITING", "Chờ tới lượt");

        public final String code;
        public final String detail;

        LegislativeDetailStatus(String code, String detail) {
            this.code = code;
            this.detail = detail;
        }
    }

    public enum LegislativeMessage {
        SUCCESS("success", "Thành công"),
        VALID_UPDATE_STATUS_CANCEL("valid001", "Phiếu có trạng thái khác huỷ, không được vô hiệu hoá"),
        VALID_UPDATE_STATUS_NO_TICKET("valid002", "Nhiệm vụ không được gán với phiếu, không được vô hiệu hoá"),
        VALID_UPDATE_STATUS_DEACTIVE("valid003", "Trạng thái nhiệm vụ khác vô hiệu hoá, không được kích hoạt"),
        CANCEL_TICKET_WITH_PROGRAM("cancel01", "Huỷ phiếu theo nhiệm vụ"),
        ;
        public final String code;
        public final String message;

        LegislativeMessage(String code, String message) {
            this.code = code;
            this.message = message;
        }

        public static String getMessageByCode(String code) {
            for (LegislativeMessage status : LegislativeMessage.values()) {
                if (status.code.equalsIgnoreCase(code)) {
                    return status.message;
                }
            }
            return null;
        }
    }
}
