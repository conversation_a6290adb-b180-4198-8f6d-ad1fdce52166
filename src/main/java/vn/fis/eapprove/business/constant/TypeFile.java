package vn.fis.eapprove.business.constant;

import java.util.stream.Stream;

public enum TypeFile {
    DOCX(".docx");

    private String typeOfFile;

    TypeFile(String typeOfFile) {
        this.typeOfFile = typeOfFile;
    }

    public static Stream<TypeFile> stream() {
        return Stream.of(TypeFile.values());
    }

    public String getTypeOfFile() {
        return typeOfFile;
    }

    public void setTypeOfFile(String typeOfFile) {
        this.typeOfFile = typeOfFile;
    }

    // standard getters and setters

}

