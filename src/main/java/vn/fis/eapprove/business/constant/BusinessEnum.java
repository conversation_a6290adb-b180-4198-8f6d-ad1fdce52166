package vn.fis.eapprove.business.constant;

public class BusinessEnum {
    public enum AdditionStatusRequest {
        NEEDS_UPDATE(0, "additional"),
        UPDATED(1, "additionalCompleted"),
        COMPLETED_NO_UPDATE(2, "completedNoUpdate"),
        NONE(-1, "none")
        ;

        public final int code;
        public final String value;

        AdditionStatusRequest(int code, String value) {
            this.code = code;
            this.value = value;
        }
    }
}
