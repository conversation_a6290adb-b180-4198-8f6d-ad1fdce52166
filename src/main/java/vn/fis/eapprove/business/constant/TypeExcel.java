package vn.fis.eapprove.business.constant;

import java.util.stream.Stream;

public enum TypeExcel {
    XLSX(".xlsx"),
    XLS(".xls");

    private String typeOfExcel;

    TypeExcel(String typeOfExcel) {
        this.typeOfExcel = typeOfExcel;
    }

    public static Stream<TypeExcel> stream() {
        return Stream.of(TypeExcel.values());
    }

    public String getTypeOfExcel() {
        return typeOfExcel;
    }

    public void setTypeOfExcel(String typeOfExcel) {
        this.typeOfExcel = typeOfExcel;
    }
}
