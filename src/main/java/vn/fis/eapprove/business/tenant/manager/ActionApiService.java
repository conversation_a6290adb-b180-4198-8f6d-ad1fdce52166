package vn.fis.eapprove.business.tenant.manager;

import org.springframework.http.HttpHeaders;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import vn.fis.eapprove.business.domain.task.entity.TaskAction;
import vn.fis.eapprove.business.dto.ActionApiDto;
import vn.fis.eapprove.business.model.ActionApiContext;
import vn.fis.eapprove.business.model.ActionApiExecuteResult;
import vn.fis.eapprove.business.model.ActionApiResult;
import vn.fis.eapprove.business.model.request.VariableValueDto;


import java.util.List;
import java.util.Map;

/**
 * Author: PhucVM
 * Date: 04/12/2022
 */
public interface ActionApiService {

    /**
     * Begin handle call action-apis
     *
     * @param actionCode Task action code (CREATE_TICKET, UPDATE_TICKET, START_TASK...)
     * @param procDefId  Process-definition-id to determine task-def-key (when missing - is null)
     * @param taskDefKey Current process task-def-key
     * @param procInstId Current process-instance-id
     * @param variables  Variables data map
     * @return Context object
     */
    ActionApiContext beginHandleActionApi(String actionCode, String procDefId, String taskDefKey, String procInstId, Map<String, Object> variables);

    ActionApiContext beginHandleActionApi(String actionCode, String procDefId, String taskDefKey, String procInstId, String actionUser, Map<String, Object> variables);

    void endHandleActionApi(ActionApiContext context);

    void endHandleActionApi(ActionApiContext context, Map<String, Object> variables);

    void endHandleActionApi(ActionApiContext context, Long bpmProcInstId, String procInstId, Map<String, Object> variables);

    void endHandleActionApiWithActionUser(ActionApiContext context, Long bpmProcInstId, String procInstId, String actionUser, Map<String, Object> variables);

    TaskAction getTaskActionFromActionCode(String actionCode);

    boolean callApi(ActionApiExecuteResult executeResult, boolean isLogAfter, boolean isCallAfter);

    HttpHeaders getDefaultHeaders(ActionApiDto actionApi, Long bpmProcinstId, String procInstId, boolean logAfter);

    List<String> getAllParamsPlaceHolder(String source, boolean excludeSpecialFunction);

    public List<String> parsePlaceHolderByRegex(String source, String regex);

    /**
     * Convert object in multi-type to result map
     *
     * @param data Multi-type data object
     * @return A result map variables
     */
    Map<String, Object> createVariablesMap(Object data);

    Map<String, Object> createVariablesMap(Object... data);

    void sendLogAfter(String procInstId, Long bpmProcInstId);

    Map<String, VariableValueDto> getTicketVariables(String procInstId);

    List<ActionApiResult> getActionApiResults(ActionApiContext actionApiContext);

    Map<String, VariableValueDto> getVariableFromResult(List<ActionApiExecuteResult> actionApiExecuteResults);

    Map<String, VariableValueDto> getVariableFromResult(ActionApiExecuteResult actionApiExecuteResult);

    void setActionApiResponse(ActionApiContext actionApiContext, Map<String, Object> responseData);
}
