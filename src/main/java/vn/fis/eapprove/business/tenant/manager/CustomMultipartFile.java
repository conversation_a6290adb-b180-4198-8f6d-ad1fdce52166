package vn.fis.eapprove.business.tenant.manager;

import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

public class CustomMultipartFile implements MultipartFile {
    private final byte[] fileContent;

    private String fileName;

    private String contentType;

    private File file;

    private FileOutputStream fileOutputStream;

    public CustomMultipartFile(byte[] fileData, String name, String path) {
        this.fileContent = fileData;
        this.fileName = name;
        file = new File(path + fileName);

    }

    @Override
    public void transferTo(File dest) {
        try {
            fileOutputStream = new FileOutputStream(dest);
            fileOutputStream.write(fileContent);
        } catch (Exception e) {

        }
    }

    public void clearOutStreams() {
        try {
            if (null != fileOutputStream) {
                fileOutputStream.flush();
                fileOutputStream.close();
                file.deleteOnExit();
            }
        } catch (Exception e) {

        }
    }

    public File getFile() {
        return file;
    }

    @Override
    public String getName() {
        return null;
    }

    @Override
    public String getOriginalFilename() {
        return null;
    }

    @Override
    public String getContentType() {
        return null;
    }

    @Override
    public boolean isEmpty() {
        return false;
    }

    @Override
    public long getSize() {
        return 0;
    }

    @Override
    public byte[] getBytes() {
        try {
            return fileContent;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public InputStream getInputStream() {
        try {
            return new ByteArrayInputStream(fileContent);
        } catch (Exception e) {
            return null;
        }
    }
}
