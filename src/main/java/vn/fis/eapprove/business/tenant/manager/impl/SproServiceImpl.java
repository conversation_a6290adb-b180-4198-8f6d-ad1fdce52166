package vn.fis.eapprove.business.tenant.manager.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.model.request.WorkFlowRequest;
import vn.fis.eapprove.business.tenant.manager.CamundaEngineService;
import vn.fis.eapprove.business.tenant.manager.SproService;
import vn.fis.spro.common.camunda.SproFlow;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: PhucVM
 * Date: 16/09/2022
 */
@Slf4j
@Service
public class SproServiceImpl implements SproService {

    private final CamundaEngineService camundaEngineService;

    @Autowired
    public SproServiceImpl(CamundaEngineService camundaEngineService) {
        this.camundaEngineService = camundaEngineService;
    }

    @Override
    public List<SproFlow> getWorkFlow(WorkFlowRequest request) {
        long startTime = System.currentTimeMillis();
        List<SproFlow> sproFlows = new ArrayList<>();
        if (request == null) {
            log.info("Get work flow ==> the request object is null");
            return sproFlows;
        }

        String procDefId = request.getProcDefId();
        String procInstId = request.getProcInstId();
        String fromNodeId = request.getFromNodeId();

        log.info("Starting get work flow ==> procDefId={}, procInstId={}, fromNodeId={}", procDefId, procInstId, fromNodeId);
        try {
            // 1 - Get all nodes from diagram
            sproFlows.addAll(camundaEngineService.getSproFlows(request));

            // 2 - Get tasks info from DB
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get work flow ==> procDefId={}, procInstId={}, fromNodeId={} in {} ms ==> total={}", procDefId, procInstId, fromNodeId, System.currentTimeMillis() - startTime, sproFlows.size());
        }

        return sproFlows;
    }

    @Override
    public List<SproFlow> getAllWorkFlow(WorkFlowRequest request) {
        long startTime = System.currentTimeMillis();
        List<SproFlow> sproFlows = new ArrayList<>();
        if (request == null) {
            log.info("Get work flow ==> the request object is null");
            return sproFlows;
        }

        String procDefId = request.getProcDefId();
        String procInstId = request.getProcInstId();
        String fromNodeId = request.getFromNodeId();

        log.info("Starting get work flow ==> procDefId={}, procInstId={}, fromNodeId={}", procDefId, procInstId, fromNodeId);
        try {
            // 1 - Get all nodes from diagram
            sproFlows.addAll(camundaEngineService.getAllSproFlows(request));

            // 2 - Get tasks info from DB
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get work flow ==> procDefId={}, procInstId={}, fromNodeId={} in {} ms ==> total={}", procDefId, procInstId, fromNodeId, System.currentTimeMillis() - startTime, sproFlows.size());
        }

        return sproFlows;
    }
}
