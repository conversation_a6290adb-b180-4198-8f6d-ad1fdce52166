package vn.fis.eapprove.business.tenant.manager.impl;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.constant.Constant;
import vn.fis.eapprove.business.dto.*;
import vn.fis.eapprove.business.dto.filter.UserInfoRequest;
import vn.fis.eapprove.business.model.AccountModel;
import vn.fis.eapprove.business.model.request.*;
import vn.fis.eapprove.business.model.response.*;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.MapKeyEnum;
import vn.fis.spro.common.helper.RestHelper;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.model.request.ChartRequest;
import vn.fis.spro.common.model.response.ChartInfoRoleResponse;
import vn.fis.spro.common.model.response.UserGroupInfoResponse;
import vn.fis.spro.common.model.response.UserInfoResponse;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author: PhucVM
 * Date: 11/11/2022
 */
@Service
@Slf4j
public class CustomerServiceImpl implements CustomerService {

    private final SproProperties sproProperties;
    private final RestHelper restHelper;
    private final CredentialHelper credentialHelper;
    private String customerServiceHost;
    @Value("${app.superAdmin.username}")
    private String usernameBasicAuth;
    @Value("${app.superAdmin.password}")
    private String password;
    @Value("${app.superAdmin.realm}")
    private String realm;


    @Autowired
    public CustomerServiceImpl(SproProperties sproProperties,
                               RestHelper restHelper,
                               CredentialHelper credentialHelper) {
        this.sproProperties = sproProperties;
        this.restHelper = restHelper;
        this.credentialHelper = credentialHelper;
    }

    @PostConstruct
    public void init() {
        customerServiceHost = sproProperties.getServiceUrls().get(MapKeyEnum.CUSTOMER.key);
        if (customerServiceHost != null && customerServiceHost.endsWith(CommonConstants.PATH_SEPARATOR)) {
            customerServiceHost = customerServiceHost.substring(0, customerServiceHost.length() - 1);
        }
    }

    private String getFullUrl(String uri) {
        return customerServiceHost + uri;
    }

    private long getEndTimeInMs(long startTime) {
        return System.currentTimeMillis() - startTime;
    }

    @Override
    public HttpHeaders getHeaders()  {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("key_role", Constant.KEY_ROLE_PUBLIC);

        try {
            //headers.set("realm", credentialHelper.getRealm());
            String token = credentialHelper.getJWTToken();
            if (!ValidationUtils.isNullOrEmpty(token)) {
                headers.setBearerAuth(token);
            }
        } catch (Exception e) {
            //headers.set("realm", realm);
            headers.setBasicAuth(usernameBasicAuth, password);
        }
        return headers;
    }

    @Override
    public CustomerInfoDto getCustomerInfo(String email) {
        long startTime = System.currentTimeMillis();
        CustomerInfoDto customerInfoDto = null;

        log.info("Starting get customer info, email={}", email);

        String uri = "/customerInfo/getCustomerInfo?email={email}";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("email", email);

            // call service
            ResponseDto<CustomerInfoDto> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                customerInfoDto = responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get customer info, email={} in {} ms", email, getEndTimeInMs(startTime));
        }

        return customerInfoDto;
    }

    public List<String> getUserEmails() {
        long startTime = System.currentTimeMillis();
        List<String> listUserEmail = new ArrayList<>();
        log.info("Starting get users email");

        String uri = "/userInfo/getUserEmailByTenant";
        try {
            ResponseDto<List<String>> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    null);

            if (responseData != null) {
                listUserEmail = responseData.getData();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            log.info("Finished get users email in {} ms", getEndTimeInMs(startTime));
        }

        return listUserEmail;
    }

    @Override
    public List<String> getCandidateUsersByGroup(String group) {
        long startTime = System.currentTimeMillis();
        List<String> users = new ArrayList<>();

        log.info("Starting get candidate users, group={}", group);

        String uri = "/userInfo/getAutoAssign/{group}";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("group", group);

            // call service
            ResponseDto<List<String>> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                users.addAll(responseData.getData());
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get candidate users, group={} in {} ms", group, getEndTimeInMs(startTime));
        }

        return users;
    }

    @Override
    public List<String> listAllUserInfo() {
        long startTime = System.currentTimeMillis();

        log.info("Starting get user info");

        String uri = "/userInfo/getAllAccount";
        try {
            // call service
            ResponseDto<List<String>> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return (List<String>) responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get user info in {} ms", getEndTimeInMs(startTime));
        }

        return null;
    }

    public String getUserTitle(String username) {
        long startTime = System.currentTimeMillis();

        log.info("Starting get user title");

        String uri = "/userTitle/getUserTitle?username={username}";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("username", username);

            // call service
            ResponseDto<String> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    }, params);

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get user title in {} ms", getEndTimeInMs(startTime));
        }

        return null;
    }


    @Override
    public List<ChartNodeDto> getChartNodeByChartId(Long chartId) {
        long startTime = System.currentTimeMillis();

        log.info("Starting get chartNode by chartId, chartId = {}", chartId);

        String uri = "/chart-node/getChartNodeByChartId/?chartId={chartId}";

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("chartId", chartId);
            ResponseDto<List<ChartNodeDto>> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    }, params);

            if (responseData != null) {
                return (List<ChartNodeDto>) responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get user info in {} ms", getEndTimeInMs(startTime));
        }

        return null;
    }

    @Override
    public List<ChartNodeDto> getAllDepartmentByCodes(ChartNodeDtoRequest params) {
        long startTime = System.currentTimeMillis();

        log.info("Starting getAllDepartmentByCodes, lstCodes={}, lstIds={}", params.getLstCodes(), params.getLstIds());

        String uri = "/chart-node/getCharNodeByCodes";

        try {
            // call service
            ResponseDto<List<ChartNodeDto>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    params,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished getAllDepartmentByCodes {} ms", getEndTimeInMs(startTime));
        }

        return null;
    }


    @Override
    public UserInfoResponse getUserInfo(String username) {
        long startTime = System.currentTimeMillis();

        log.info("Starting get user info, username={}", username);

        String uri = "/userInfo/getByUsername?username={username}";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("username", username);

            // call service
            ResponseDto<UserInfoResponse> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get user info, username={} in {} ms", username, getEndTimeInMs(startTime));
        }

        return null;
    }

    @Override
    public List<UserInfoResponse> getUserInfoList(List<String> emails) {
        long startTime = System.currentTimeMillis();

        log.info("Starting get user info, email={}", emails);


        String uri = "/userInfo/getByEmails";

        // call service
        try {
            NotifyUserRequest request = new NotifyUserRequest();
            request.setUserEmails(emails);

            // call service
            ResponseDto<List<UserInfoResponse>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    request,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get user info list, email={} in {} ms", emails, getEndTimeInMs(startTime));
        }

        return null;
    }

    @Override
    public List<UserGroupInfoResponse> getUserGroupInfoList(List<Long> groupIds) {
        long startTime = System.currentTimeMillis();

        log.info("Starting get user group info, groupIds={}", groupIds);
        String uri = "/groupUser/getUserGroupInfo";
        try {
            NotifyGroupUserRequest request = new NotifyGroupUserRequest();
            request.setGroupIds(groupIds);

            // call service
            ResponseDto<List<UserGroupInfoResponse>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    request,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get user group info, groupIds={} in {} ms", groupIds, getEndTimeInMs(startTime));
        }

        return null;
    }

    @Override
    public UserInfoDto getEmailByUser(String username) {
        long startTime = System.currentTimeMillis();
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("username", username);
            String uri = "/userInfo/getEmailByUsername?username={username}";
            // call service
            ResponseDto<UserInfoDto> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get time response, username={} in {} ms", username, getEndTimeInMs(startTime));
        }
        return null;
    }


    @Override
    public List<Map<String, Object>> listTimeToFeedback(List<ListTimeExpectRequest> listTimeExpectRequests) {
        long startTime = System.currentTimeMillis();
        try {
            String uri = "/working-time/getListTimeResponse";
            // call service
            ResponseDto<List<Map<String, Object>>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    listTimeExpectRequests,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get listTimeToFeedback in {} ms", getEndTimeInMs(startTime));
        }
        return null;
    }

    @Override
    public Integer checkStartPhase(String procDefId, String taskDefKey) {
        long startTime = System.currentTimeMillis();

        log.info("Starting check start phase: procDefId={}, taskDefKey={}", procDefId, taskDefKey);

        String uri = "/userInfo/checkStartPhase?procDefId={procDefId}&taskDefKey={taskDefKey}";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("procDefId", procDefId);
            params.put("taskDefKey", taskDefKey);

            // call service
            ResponseDto<Integer> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished check start phase: procDefId={}, taskDefKey={} in {} ms", procDefId, taskDefKey, getEndTimeInMs(startTime));
        }

        return 0;
    }

    @Override
    public List<ChartInfoRoleResponse> getUserByEmail(List<String> username) {
        long startTime = System.currentTimeMillis();
        log.info("Starting get user by username");

        String uri = "/chart/getUserByEmail";
        try {
            ChartRequest request = new ChartRequest();
            request.setUsername(username);

            // call service
            ResponseDto<List<ChartInfoRoleResponse>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    request,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get user by email in {} ms", getEndTimeInMs(startTime));
        }

        return new ArrayList<>();
    }

    @Override
    public List<ApiManagementShareWithRequest> listShareWith(ApiManagementShareWithRequest apiManagementShareWithRequest) {
        long startTime = System.currentTimeMillis();
        List<ApiManagementShareWithRequest> listShareWith = new ArrayList<>();

        log.info("Starting get users, user={}", apiManagementShareWithRequest.getEmail());

        String uri = "/userInfo/listShareWith";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("email", apiManagementShareWithRequest.getEmail());

            // call service
            ResponseDto<List<ApiManagementShareWithRequest>> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                listShareWith.addAll(responseData.getData());
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get users, user={} in {} ms", apiManagementShareWithRequest.getEmail(), getEndTimeInMs(startTime));
        }

        return listShareWith;
    }

    @Override
    public String[] getAssistantByChartId(String email, String createUser) {
        long startTime = System.currentTimeMillis();
        log.info("Starting get assistant by chartId");

        String uri = "/userInfo/getAssistantByChartNode?email={email}&createUser={createUser}";
        log.info("getAssistantByChartId URL", uri);
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("email", email);
            params.put("createUser", createUser);
            // call service
            ResponseDto<String[]> responseData = restHelper.getAndGetBody(
                    getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get assistant by chartId  in {} ms", getEndTimeInMs(startTime));
        }

        return new String[]{};
    }

    @Override
    public List<TimeExpectResponse> listTimeExpect(List<ListTimeExpectRequest> listTimeExpectRequests) {
        long startTime = System.currentTimeMillis();
        try {
            String uri = "/working-time/getListTimeExpect";
            // call service
            ResponseDto<List<TimeExpectResponse>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    listTimeExpectRequests,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get listTimeToFeedback in {} ms", getEndTimeInMs(startTime));
        }
        return null;
    }

    @Override
    public List<ChartDto> getAllChartId() {
        long startTime = System.currentTimeMillis();
        log.info("Starting get user by email");

        String uri = "/chart/getAll";
        try {


            // call service
            ResponseDto<List<ChartDto>> responseData = restHelper.getAndGetBody(
                    getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get user by email in {} ms", getEndTimeInMs(startTime));
        }

        return new ArrayList<>();
    }

    @Override
    public Long getLocationIdByUserName(String userName) {
        long startTime = System.currentTimeMillis();
        log.info("Starting get assistant by chartId");

        String uri = "/userInfo/getLocationIdByUserName?userName={userName}";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("userName", userName);
            // call service
            ResponseDto<Long> responseData = restHelper.getAndGetBody(
                    getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get user info id by username  in {} ms", getEndTimeInMs(startTime));
        }
        return null;
    }

    @Override
    public List<AccountModel> getAccountByUsernames(List<String> usernames) {
        long startTime = System.currentTimeMillis();
        List<AccountModel> accounts = new ArrayList<>();

        log.info("Starting get accounts by usernames={}", usernames);

        String uri = "/userInfo/getByUserNames";
        try {
            Map<String, Object> body = new HashMap<>();
            body.put("userNames", usernames);

            // call service
            ResponseDto<List<AccountModel>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    body,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                accounts.addAll(responseData.getData());
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get accounts by usernames={} in {} ms", usernames, getEndTimeInMs(startTime));
        }

        return accounts;
    }

    @Override
    public List<AccountModel> getUserTaskInfoByUserNames(List<String> usernames) {
        long startTime = System.currentTimeMillis();
        List<AccountModel> accounts = new ArrayList<>();

        log.info("Starting getUserTaskInfoByUserNames ={}", usernames);

        String uri = "/userInfo/getUserTaskInfoByUserNames";
        try {
            Map<String, Object> body = new HashMap<>();
            body.put("userNames", usernames);

            // call service
            ResponseDto<List<AccountModel>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    body,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                accounts.addAll(responseData.getData());
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished getUserTaskInfoByUserNames ={} in {} ms", usernames, getEndTimeInMs(startTime));
        }

        return accounts;
    }

    @Override
    public List<UserInfoDto> getUserInfoByChartIdAndCharNodeIds(ChartNodeDtoRequest request) {
        long startTime = System.currentTimeMillis();

        log.info("Starting get accounts by usernames={}", request);

        String uri = "/userInfo/getUserInfoByChartIdAndCharNodeIds";
        try {

            // call service
            ResponseDto<List<UserInfoDto>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    request,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get accounts by usernames={} in {} ms", request, getEndTimeInMs(startTime));
        }

        return null;
    }

    @Override
    public Map<String, AccountModel> getAccountMapByUsernames(List<String> usernames) {
        List<AccountModel> accounts = getAccountByUsernames(usernames);
        return accounts.stream().collect(Collectors.toMap(AccountModel::getUsername, Function.identity(), (first, second) -> second));
    }

    @Override
    public List<Map<String, String>> getAccountProcInst(Long chartNodeId) {
        long startTime = System.currentTimeMillis();
        log.info("Starting get assistant by chartId");

        String uri = "/userInfo/getUsernameByChartNode?chartNodeId={chartNodeId}";
        log.info("getUsernameByChartNode URL", uri);
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("chartNodeId", chartNodeId);
            // call service
            ResponseDto<List<Map<String, String>>> responseData = restHelper.getAndGetBody(
                    getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get assistant by chartId  in {} ms", getEndTimeInMs(startTime));
        }
        return null;
    }

    @Override
    public List<ChartDto> getLstChartByUsername(String username) {
        long startTime = System.currentTimeMillis();

        log.info("Starting get chart by usernames={}", username);

        String uri = "/chart/getChartActiveByUsername?username={username}";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("username", username);
            // call service
            ResponseDto<List<ChartDto>> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get chart by usernames={} in {} ms", username, getEndTimeInMs(startTime));
        }

        return null;
    }

    @Override
    public String getConfigSlaShift(String username) {
        long startTime = System.currentTimeMillis();

        log.info("Starting get value config sla shift .....");
        String uri = "/systemConfig/findValueByCodeAndInfo?code={code}&info={info}&username={username}";

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("code", "config_sla_shift");
            params.put("info", "system");
            params.put("username", username);

            // call service
            ResponseDto<String> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get value config sla shift in {} ms", getEndTimeInMs(startTime));
        }
        return null;
    }

    @Override
    public UserInfoResponse getCharNodeIds(String username) {
        long startTime = System.currentTimeMillis();

        log.info("Starting get user info, username={}", username);

        String uri = "/userInfo/getChartNodeIdsByUsername?username={username}";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("username", username);

            // call service
            ResponseDto<UserInfoResponse> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get user info, username={} in {} ms", username, getEndTimeInMs(startTime));
        }
        return null;
    }

    @Override
    public List<UserTitleResponse> getUserTitleByUserName(String username) {
        long startTime = System.currentTimeMillis();

        log.info("Starting get user title, username={}", username);

        String uri = "/userTitle/getTitleByUsername?username={username}";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("username", username);

            // call service
            ResponseDto<List<UserTitleResponse>> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get user title, username={} in {} ms", username, getEndTimeInMs(startTime));
        }
        return null;
    }

    @Override
    public UserInfoDto changeStatusHandOver(String username) {
        long startTime = System.currentTimeMillis();
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setUsername(username);
        log.info("Starting get accounts by usernames={}", username);

        String uri = "/userInfo/changeStatusHandOver";
        try {

            // call service
            ResponseDto<UserInfoDto> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    userInfoDto,
                    new ParameterizedTypeReference<>() {
                    });
            return responseData.getData();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get accounts by usernames={} in {} ms", username, getEndTimeInMs(startTime));
        }

        return null;
    }

    @Override
    public List<Map<String, Object>> findCompanyCodeByUsername(String username) {
        long startTime = System.currentTimeMillis();
        List<Map<String, Object>> mapResponse = null;
        String url = "/chart/findCompanyCodeByUsername?username=" + username;
        log.info("Starting call customer-service url: {}", url);
        try {
            HttpHeaders httpHeaders = getHeaders();

            mapResponse = restHelper.getAndGetBody(
                    getFullUrl(url),
                    httpHeaders,
                    new ParameterizedTypeReference<>() {
                    });

            return mapResponse;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            log.info("Finished call customer-service in {} ms", getEndTimeInMs(startTime));
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> getAllByCompanyCodes(String username) {
        long startTime = System.currentTimeMillis();
        List<Map<String, Object>> mapResponse = null;
        String url = "/userInfo/getAllByCompanyCodes?username=" + username;
        log.info("Starting call customer-service url: {}", url);
        try {
            HttpHeaders httpHeaders = getHeaders();

            mapResponse = restHelper.getAndGetBody(
                    getFullUrl(url),
                    httpHeaders,
                    new ParameterizedTypeReference<>() {
                    });

            return mapResponse;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            log.info("Finished call customer-service in {} ms", getEndTimeInMs(startTime));
        }
        return null;
    }

    @Override
    public List<String> listChildCompanyCodeByParentCode(List<String> parentCodes) {
        long startTime = System.currentTimeMillis();

        log.info("Starting listChildCompanyCodeByParentCode, parentId = {}", parentCodes);

        String uri = "/chart-node/listChildCompanyCodeByParentCode";

        try {
            Map<String, Object> body = new HashMap<>();
            body.put("lstCodes", parentCodes);

            // call service
            ResponseDto<List<String>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    body,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished listChildCompanyCodeByParentCode in {} ms", getEndTimeInMs(startTime));
        }

        return null;
    }

    @Override
    public List<NameAndCodeCompanyResponse> responseCompanyCodeAndName(String username) {
        long startTime = System.currentTimeMillis();

        log.info("Starting get companyCode and CompanyName, username = {}", username);

        String uri = "/chart/getCompanyCodeAndNameByUsername?username=" + username;

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("username", username);
            ResponseDto<List<NameAndCodeCompanyResponse>> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    }, params);

            if (responseData != null) {
                return (List<NameAndCodeCompanyResponse>) responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get companyCode and CompanyName in {} ms", getEndTimeInMs(startTime));
        }

        return null;
    }

    @Override
    public List<NameAndCodeCompanyResponse> responseCompanyCodeAndNames(List<String> username) {
        long startTime = System.currentTimeMillis();

        log.info("Starting get companyCode and CompanyName, username = {}", username);

        String uri = "/chart/getCompanyCodeAndNameByUsernames";

        Map<String, Object> params = new HashMap<>();
        params.put("usernames", username);
        try {
            ResponseDto<List<NameAndCodeCompanyResponse>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    params,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get companyCode and CompanyName in {} ms", getEndTimeInMs(startTime));
        }

        return null;
    }

    @Override
    public List<NameAndCodeCompanyResponse> findAllCompanyCodeAndNameByCompanyCode(List<String> companyCode) {
        long startTime = System.currentTimeMillis();

        String uri = "/chart/findAllCompanyCodeAndNameByCompanyCode";

        Map<String, Object> params = new HashMap<>();
        params.put("listCompanyCode", companyCode);
        try {
            ResponseDto<List<NameAndCodeCompanyResponse>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    params,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get companyCode and CompanyName in {} ms", getEndTimeInMs(startTime));
        }

        return null;
    }

    @Override
    public UserInfoByUsername getUserInfoByUsername(String username) {
        long startTime = System.currentTimeMillis();

        String uri = "/userInfo/getUserByUserName?username={username}";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("username", username);

            // call service
            ResponseDto<UserInfoByUsername> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get user title, username={} in {} ms", username, getEndTimeInMs(startTime));
        }
        return null;
    }

    @Override
    public AssigneeInfoResponse getAssigneeInfo(String username) {
        long startTime = System.currentTimeMillis();

        String uri = "/userInfo/getAssigneeInfo?username={username}";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("username", username);
            // call service
            ResponseDto<AssigneeInfoResponse> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get user title, username={} in {} ms", username, getEndTimeInMs(startTime));
        }
        return null;
    }

    @Override
    public List<String> getUserDefault(String username,String status) {
        long startTime = System.currentTimeMillis();
        log.info("Starting get user default, username={}", username);
        String uri = "/userInfo/getUserDefaultByChart?username={username}&status={status}";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("username", username);
            params.put("status", status);
            // call service
            ResponseDto<List<String>> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get user default, username={} in {} ms", username, getEndTimeInMs(startTime));
        }

        return Collections.emptyList();
    }

    @Override
    public Object getAllSystemGroupChart(String type) {
        long startTime = System.currentTimeMillis();
        String uri = "/chart/getAllSystemGroup?type={type}";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("type", type);

            // call service
            ResponseDto<Object> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished getAllSystemGroup default in {} ms", getEndTimeInMs(startTime));
        }

        return Collections.emptyList();
    }

    @Override
    public Object getAllSystemGroupBudget() {
        long startTime = System.currentTimeMillis();
        String uri = "/approvedBudget/getAllSystemGroup";
        try {
            // call service
            ResponseDto<Object> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished getAllSystemGroup default in {} ms", getEndTimeInMs(startTime));
        }

        return Collections.emptyList();
    }

    @Override
    public Object getAllSystemGroupSystemConfig() {
        long startTime = System.currentTimeMillis();
        String uri = "/systemConfig/getAllSystemGroup";
        try {
            // call service
            ResponseDto<Object> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished getAllSystemGroup default in {} ms", getEndTimeInMs(startTime));
        }

        return Collections.emptyList();
    }
    @Override
    public Object getAllPosition() {
        long startTime = System.currentTimeMillis();
        String uri = "/position/getAllSystemGroup";
        try {
            // call service
            ResponseDto<Object> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished getAllSystemGroup default in {} ms", getEndTimeInMs(startTime));
        }

        return Collections.emptyList();
    }
    @Override
    public Object getAllJobTitle() {
        long startTime = System.currentTimeMillis();
        String uri = "/jobTitle/getAllSystemGroup";
        try {
            // call service
            ResponseDto<Object> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished getAllSystemGroup default in {} ms", getEndTimeInMs(startTime));
        }

        return Collections.emptyList();
    }

    @Override
    public List<Long> getChartNodeIdsByUsernames(Set<String> username) {
        long startTime = System.currentTimeMillis();
        log.info("Starting get char node id default, username={}", username);
        String uri = "/userInfo/getChartNodeByListUser";
        try {
            UserInfoRequest request = new UserInfoRequest();
            request.setUsername(username);

            // call service
            ResponseDto<List<Long>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(), request,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get user default, username={} in {} ms", username, getEndTimeInMs(startTime));
        }

        return Collections.emptyList();
    }

    @Override
    public List<String> getListUsernameActive(List<String> lstUsername) {
        long startTime = System.currentTimeMillis();
        String uri = "/userInfo/getListUsernameActive";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("userNames", lstUsername);
            // call service
            ResponseDto<List<String>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(), params,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished getListUsernameActive default in {} ms", getEndTimeInMs(startTime));
        }

        return Collections.emptyList();
    }

    public AssigneeInfoDto getAssigneeInfoByListUser(Set<String> usernames) {
        long startTime = System.currentTimeMillis();
        log.info("Starting get user default, username={}", usernames);
        String uri = "/userInfo/getInfoAssigneeByListUser";
        try {
            UserInfoRequest request = new UserInfoRequest();
            request.setUsername(usernames);

            // call service
            ResponseDto<AssigneeInfoDto> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(), request,

                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished getListUsernameActive default in {} ms", getEndTimeInMs(startTime));
        }
        return null;
    }


    public List<UserInfoByUsername> getInfoByListUser(Set<String> usernames) {
        log.info("Starting get user default, username={}", usernames);
        String uri = "/userInfo/getInfoByListUser";
        try {
            UserInfoRequest request = new UserInfoRequest();
            request.setUsername(usernames);

            // call service
            ResponseDto<List<UserInfoByUsername>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(), request,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return null;
    }


    public List<Map<String, Object>> getCompanyCodeByChartNodeIdIn(List<String> chartNodeIds) {
        String uri = "/chart/getCompanyCodeByChartNodeIdIn";
        try {
            Map<String, Object> request = new HashMap<>();
            request.put("chartNodeIds", chartNodeIds);

            // call service
            ResponseDto<List<Map<String, Object>>> responseData = restHelper.postAndGetBody(
                    getFullUrl(uri),
                    getHeaders(),
                    request,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return null;
    }

    @Override
    public LocalDateTime getTimeSla(long timeRes, LocalDateTime startDate, String emailAssignee) {
        long startTime = System.currentTimeMillis();

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("timeCreate", startDate.toString());
            params.put("timeRes", timeRes);
            params.put("emailAssignee", emailAssignee);
            String uri = "/working-time/getTimeSla?timeCreate={timeCreate}&timeRes={timeRes}&emailAssignee={emailAssignee}";
            // call service
            LocalDateTime responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    },
                    params);

            if (responseData != null) {
                return responseData;
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished getTimeSla, timeRes={}, startDate={}, emailAssignee={} in {} ms", emailAssignee, startDate, emailAssignee, getEndTimeInMs(startTime));
        }
        return null;
    }

    @Override
    public String getDefaultSignatureByUsername(String username) {
        long startTime = System.currentTimeMillis();
        try {
            String uri = "/v1/signature/getDefaultSignature/" + username;
            // call service
            Map<String, Object> responseData = restHelper.getAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.get("data").toString();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished getDefaultSignature, username={} in {} ms", username, getEndTimeInMs(startTime));
        }
        return null;
    }

    public void savePermissionHistory(PermissionHistoryRequest request) {
        String uri = "/permission-history/save";
        try {
            ResponseDto<List<NameAndCodeCompanyResponse>> responseData = restHelper.postAndGetBody(
                    getFullUrl(uri),
                    getHeaders(),
                    request,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                log.info("savePermissionHistory responseData: {}", responseData);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    @Override
    public Map<String, String> getMapNameShareTicketDetail(List<String> sharedUsers,
                                                           List<String> companyCodes,
                                                           List<String> chartNodeCodes,
                                                           List<String> createdUsers,
                                                           List<String> assignees
    ) {
        String uri = "/chart/getMapNameShareTicket";
        try {
            Map<String, Object> request = new HashMap<>();
            request.put("sharedUsers", sharedUsers);
            request.put("companyCodes", companyCodes);
            request.put("chartNodeCodes", chartNodeCodes);
            request.put("createdUsers", createdUsers);
            request.put("assignees", assignees);

            // call service
            ResponseDto<Map<String, String>> responseData = restHelper.postAndGetBody(
                    getFullUrl(uri),
                    getHeaders(),
                    request,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return null;
    }

}

