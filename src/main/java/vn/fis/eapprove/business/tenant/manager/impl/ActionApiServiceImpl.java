package vn.fis.eapprove.business.tenant.manager.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import vn.fis.eapprove.business.config.GsonAdapterConfig;
import vn.fis.eapprove.business.constant.AppConstants;
import vn.fis.eapprove.business.domain.api.entity.ApiLog;
import vn.fis.eapprove.business.domain.api.repository.ApiManagementRepository;
import vn.fis.eapprove.business.domain.api.service.ApiLogService;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef;
import vn.fis.eapprove.business.domain.bpm.service.*;
import vn.fis.eapprove.business.domain.task.entity.TaskAction;
import vn.fis.eapprove.business.dto.ActionApiDto;
import vn.fis.eapprove.business.dto.AdditionApiInfoDto;
import vn.fis.eapprove.business.model.*;
import vn.fis.eapprove.business.model.request.VariableValueDto;
import vn.fis.eapprove.business.model.response.ActionApiResponse;
import vn.fis.eapprove.business.tenant.manager.ActHiVarInstManager;
import vn.fis.eapprove.business.tenant.manager.ActionApiService;
import vn.fis.eapprove.business.tenant.manager.CamundaEngineService;
import vn.fis.eapprove.business.utils.CamundaUtils;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.eapprove.security.config.BasicAuthConfig;
import vn.fis.spro.common.camunda.Variable;
import vn.fis.spro.common.camunda.VariableModification;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.TaskActionConstants;
import vn.fis.spro.common.helper.RedisHelper;
import vn.fis.spro.common.helper.RestHelper;
import vn.fis.spro.common.util.HttpUtils;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static vn.fis.eapprove.business.constant.Constant.FORMAT_DATE_TIME_2;
import static vn.fis.eapprove.business.constant.Constant.GET_ALL_JSON_ARRAY;
import static vn.fis.eapprove.business.utils.TimeUtils.localDateTimeToString;

/**
 * Author: PhucVM
 * Date: 04/12/2022
 */
@Service
@Slf4j
@SuppressWarnings({"unchecked", "rawtypes"})
public class ActionApiServiceImpl implements ActionApiService {

    private static final String[] SUCCESS_CONDITION_LIST = {
            "IGNORE"
    };
    private final CamundaEngineService camundaEngineService;
    private final BpmProcdefApiManager bpmProcdefApiManager;
    private final BpmProcInstManager bpmProcInstManager;
    private final BpmProcdefManager bpmProcdefManager;
    private final RestHelper restHelper;
    private final Common common;
    private final RedisHelper redisHelper;
    private final ActHiVarInstManager actHiVarInstManager;
    private final BpmVariablesService bpmVariablesService;
    private final ApiLogService apiLogService;
    private final ApiManagementRepository apiManagementRepository;
    private final BasicAuthConfig basicAuthConfig;

    private final BpmService bpmService;
    @Value("${action-api.always-allow-post-process}")
    private boolean isAlwaysAllowPostProcess;
    @Value("${job.apiLogTask.enable}")
    private boolean apiLogTaskEnable;
    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Value("${app.superAdmin.account}")
    private String appSuperAdminAccount;

    @Autowired
    CredentialHelper credentialHelper;

    @Value("${api-after.call-kafka.enable}")
    private boolean apiCallKafka;

    @Value("${spring.kafka.consumer.topic.action-api-after}")
    private String topicActionApiAfter;

    @Autowired
    public ActionApiServiceImpl(CamundaEngineService camundaEngineService,
                                BpmProcdefApiManager bpmProcdefApiManager,
                                @Lazy BpmProcInstManager bpmProcInstManager,
                                BpmProcdefManager bpmProcdefManager,
                                RestHelper restHelper,
                                Common common,
                                RedisHelper redisHelper,
                                ActHiVarInstManager actHiVarInstManager,
                                BpmVariablesService bpmVariablesService,
                                ApiLogService apiLogService,
                                ApiManagementRepository apiManagementRepository, BasicAuthConfig basicAuthConfig,
                                BpmService bpmService) {
        this.camundaEngineService = camundaEngineService;
        this.bpmProcdefApiManager = bpmProcdefApiManager;
        this.bpmProcInstManager = bpmProcInstManager;
        this.bpmProcdefManager = bpmProcdefManager;
        this.restHelper = restHelper;
        this.common = common;
        this.redisHelper = redisHelper;
        this.actHiVarInstManager = actHiVarInstManager;
        this.bpmVariablesService = bpmVariablesService;
        this.apiLogService = apiLogService;
        this.apiManagementRepository = apiManagementRepository;
        this.basicAuthConfig = basicAuthConfig;
        this.bpmService = bpmService;
    }

    @Override
    public ActionApiContext beginHandleActionApi(String actionCode, String procDefId, String taskDefKey, String procInstId, Map<String, Object> variables) {
        return beginHandleActionApi(actionCode, procDefId, taskDefKey, procInstId, null, variables);
    }

    @Override
    public ActionApiContext beginHandleActionApi(String actionCode, String procDefId, String taskDefKey, String procInstId, String actionUser, Map<String, Object> variables) {
        if (ValidationUtils.isNullOrEmpty(actionCode) || ValidationUtils.isNullOrEmpty(procDefId)) {
            return null;
        }

        // if task-def-key is null ==> action from start, must find id from BPMN
        if (ValidationUtils.isNullOrEmpty(taskDefKey)) {
            BpmnModelInstance modelInstance = camundaEngineService.getModelInstance(procDefId);
            StartEvent startEvent = CamundaUtils.getStartEvent(modelInstance);
            taskDefKey = startEvent != null ? startEvent.getId() : null;
        }

        // get task-action from action-code
        TaskAction taskAction = getTaskActionFromActionCode(actionCode);
        if (taskAction == null) {
            return null;
        }

        // get bpm_procinst
        BpmProcInst bpmProcInst = null;
        if (!ValidationUtils.isNullOrEmpty(procInstId)) {
            bpmProcInst = bpmProcInstManager.findBpmProcInstByTicketProcInstId(procInstId);
        }

        Long bpmProcinstId = bpmProcInst != null ? bpmProcInst.getTicketId() : null;

        // init context
        ActionApiContext context = ActionApiContext.builder()
                .procDefId(procDefId)
                .taskDefKey(taskDefKey)
                .procInstId(procInstId)
                .actionCode(actionCode)
                .bpmProcInst(bpmProcInst)
                .allowPostProcess(true)
                .build();

        // get list of action-api info
        List<ActionApiDto> actionApis = null;
        if (bpmProcdefApiManager.isProcDefIdExists(procDefId)) {
            actionApis = bpmProcdefApiManager.getBpmProcdefApiInfo(procDefId, taskDefKey, taskAction.getId());
        } else if (bpmProcInst != null) {
            // find current procDefId by serviceId
            BpmProcdef bpmProcdef = bpmProcdefManager.findByServiceId(bpmProcInst.getServiceId());
            if (bpmProcdef != null) {
                actionApis = bpmProcdefApiManager.getBpmProcdefApiInfo(bpmProcdef.getProcDefId(), taskDefKey, taskAction.getId());
            }
        } else if (variables.get("serviceIdApi") != null) {
            // Không có thì lấy từ optVariable
            Long serviceId = Long.valueOf(variables.get("serviceIdApi").toString());
            BpmProcdef bpmProcdef = bpmProcdefManager.findByServiceId(serviceId);
            if (bpmProcdef != null) {
                actionApis = bpmProcdefApiManager.getBpmProcdefApiInfo(bpmProcdef.getProcDefId(), taskDefKey, taskAction.getId());
            }
        } else {
            log.info("action-api not found, procInstId: {},  procDeftId: {}, actionCode: {} " , procInstId, procDefId, actionCode);
        }
        String account;
        if (actionUser != null) {
            account = actionUser;
        } else {
            try {
                account = credentialHelper.getJWTPayload().getUsername();
            } catch (Exception e) {
                account = appSuperAdminAccount;
            }
        }
        if (!ValidationUtils.isNullOrEmpty(actionApis)) {
            // split list of action-apis to 2 list BEFORE and AFTER
            String finalAccount = account;
            Map<Boolean, List<ActionApiExecuteResult>> partitions = actionApis.stream()
                    // check call api condition
                    .filter(actionApiDto -> {
                        String callCondition = actionApiDto.getCallCondition();
                        if (ValidationUtils.isNullOrEmpty(callCondition)) {
                            return true;
                        }
                        Boolean evaluationResult = common.evalExpressionByMVEL(callCondition, variables, Boolean.class);
                        return evaluationResult != null && evaluationResult;
                    })
                    .map(e -> new ActionApiExecuteResult(e, bpmProcinstId, procInstId, variables, finalAccount))
                    .collect(Collectors.partitioningBy(e -> ValidationUtils.isNullOrEmpty(e.getActionApi().getCallOrder())
                            || e.getActionApi().getCallOrder().equalsIgnoreCase(TaskActionConstants.CallOrder.BEFORE)));

            List<ActionApiExecuteResult> apiExecuteBeforeActionResults = new ArrayList<>(partitions.get(true));
            List<ActionApiExecuteResult> apiExecuteAfterActionResults = new ArrayList<>(partitions.get(false));

            // add list to context bean
            context.setApiExecuteBeforeActionResults(apiExecuteBeforeActionResults);
            context.setApiExecuteAfterActionResults(apiExecuteAfterActionResults);
        }

        // decide to send log before or after when create ticket
        boolean isLogAfter = bpmProcinstId == null && (actionCode.equalsIgnoreCase(TaskActionConstants.Action.CREATE_TICKET.code) || actionCode.equalsIgnoreCase(TaskActionConstants.Action.CREATE_DRAFT_TICKET.code));

        // start execute list of apis BEFORE
        if (!ValidationUtils.isNullOrEmpty(context.getApiExecuteBeforeActionResults())) {
            context.getApiExecuteBeforeActionResults().forEach(e -> {
                boolean callResult = callApi(e, isLogAfter, false);
                e.setCalled(true);
                e.setSuccess(callResult);
            });

            // determine allow post process
            context.setAllowPostProcess(isAllowPostProcess(context.getApiExecuteBeforeActionResults()));
        }

        return context;
    }

    /**
     * Handle call async apis async (put in the end of action method)
     */
    @Override
    public void endHandleActionApi(ActionApiContext context) {
        endHandleActionApi(context, null);
    }

    /**
     * Handle call async apis async with variables
     */
    @Override
    public void endHandleActionApi(ActionApiContext context, Map<String, Object> variables) {
        endHandleActionApi(context, null, null, variables);
    }

    @Override
    public void endHandleActionApi(ActionApiContext context, Long bpmProcInstId, String procInstId, Map<String, Object> variables) {
        // enqueue api excute AFTER action to task scheduler
        if (context != null && !ValidationUtils.isNullOrEmpty(context.getApiExecuteAfterActionResults())) {
            log.debug("DEBUG action api AFTER: context = {}", context);
            context.getApiExecuteAfterActionResults().forEach(e -> {
                if (e != null) {
                    if (!ValidationUtils.isNullOrEmpty(variables)) {
                        // convert value double -> long
                        variables.forEach((key, value) -> {
                            if (value instanceof Double) {
                                variables.put(key, ((Double) value).longValue());
                            }
                        });

                        e.setVariables(variables);
                    }
                    if (bpmProcInstId != null && !bpmProcInstId.equals(0L)) {
                        e.setBpmProcinstId(bpmProcInstId);
                    }
                    if (!ValidationUtils.isNullOrEmpty(procInstId)) {
                        e.setProcInstId(procInstId);
                    }
                    String account;
                    try {
                        account = credentialHelper.getJWTPayload().getUsername();
                    } catch (Exception ex) {
                        account = appSuperAdminAccount;
                    }
                    e.setUsername(account);

                    // redis
//                    redisHelper.enqueue(CommonConstants.QueueName.ACTION_API_QUEUE, e);

                    // kafka
                    if (apiCallKafka) {
                        kafkaTemplate.send(topicActionApiAfter, e);
                    } else {
                        callApi(e, false, true);
                    }
                }
            });
        }
    }

    @Override
    public void endHandleActionApiWithActionUser(ActionApiContext context, Long bpmProcInstId, String procInstId, String actionUser, Map<String, Object> variables) {
        // enqueue api excute AFTER action to task scheduler
        if (context != null && !ValidationUtils.isNullOrEmpty(context.getApiExecuteAfterActionResults())) {
            log.debug("DEBUG action api AFTER: context = {}", context);
            context.getApiExecuteAfterActionResults().forEach(e -> {
                if (e != null) {
                    if (!ValidationUtils.isNullOrEmpty(variables)) {
                        // convert value double -> long
                        variables.forEach((key, value) -> {
                            if (value instanceof Double) {
                                variables.put(key, ((Double) value).longValue());
                            }
                        });

                        e.setVariables(variables);
                    }
                    if (bpmProcInstId != null && !bpmProcInstId.equals(0L)) {
                        e.setBpmProcinstId(bpmProcInstId);
                    }
                    if (!ValidationUtils.isNullOrEmpty(procInstId)) {
                        e.setProcInstId(procInstId);
                    }
                    String account = actionUser == null ? appSuperAdminAccount : actionUser;

                    e.setUsername(account);

                    // redis
//                    redisHelper.enqueue(CommonConstants.QueueName.ACTION_API_QUEUE, e);

                    // kafka
                    if (apiCallKafka) {
                        kafkaTemplate.send(topicActionApiAfter, e);
                    } else {
                        callApi(e, false, true);
                    }
                }
            });
        }
    }

    /**
     * Determine allow continue process or not
     */
    private boolean isAllowPostProcess(List<ActionApiExecuteResult> apiBeforeExecuteResults) {
        if (!ValidationUtils.isNullOrEmpty(apiBeforeExecuteResults) && !isAlwaysAllowPostProcess) {
            ActionApiExecuteResult executeResult = apiBeforeExecuteResults.stream()
                    .filter(e -> !e.isSuccess() && !e.isContinueOnError())
                    .findAny()
                    .orElse(null);
            return executeResult == null; // nothing fail
        }

        return true;
    }

    @Override
    public TaskAction getTaskActionFromActionCode(String actionCode) {
        if (!ValidationUtils.isNullOrEmpty(actionCode)) {
            return AppConstants.Cache.ACTION_CODE_TO_TASK_ACTION.get(actionCode);
        }

        return null;
    }

    public static Map<String, Object> convertHeaders(HttpHeaders headers) {
        Map<String, Object> headersMap = new HashMap<>();

        headers.forEach((key, valueList) -> {
            if (valueList.size() == 1) {
                headersMap.put(key, valueList.get(0));
            } else {
                headersMap.put(key, valueList);
            }
        });

        return headersMap;
    }

    @Override
    public boolean callApi(ActionApiExecuteResult executeResult, boolean isLogAfter, boolean isCallAfter) {
        if (executeResult == null) {
            return false;
        }

        ActionApiDto actionApi = executeResult.getActionApi();
        if (actionApi == null) {
            return false;
        }
        long startTime = System.currentTimeMillis();

        Map<String, Object> variables = executeResult.getVariables();
        Long bpmProcinstId = executeResult.getBpmProcinstId();
        String procInstId = executeResult.getProcInstId();

        String url = buildUrl(actionApi.getBaseUrl(), actionApi.getUrl());
        String method = actionApi.getMethod();
        String header = !ValidationUtils.isNullOrEmpty(actionApi.getExHeader()) ? actionApi.getExHeader() : actionApi.getHeader();
        String body = !ValidationUtils.isNullOrEmpty(actionApi.getExBody()) ? actionApi.getExBody() : actionApi.getBody();
        Integer continueOnError = !ValidationUtils.isNullOrEmpty(actionApi.getExContinueOnError()) ? actionApi.getExContinueOnError() : actionApi.getContinueOnError();
        Integer returnResponse = actionApi.getReturnResponse() != null ? actionApi.getReturnResponse() : CommonConstants.IntToBoolean.FALSE;

        // set back execute result
        executeResult.getActionApiResult().setUrl(url);
        executeResult.setContinueOnError(continueOnError == null || continueOnError.equals(CommonConstants.IntToBoolean.TRUE));
        executeResult.setReturnResponse(returnResponse.equals(CommonConstants.IntToBoolean.TRUE));

        HttpHeaders headers = new HttpHeaders();
        //Push thêm header
        headers.add("username", executeResult.getUsername());

        Object requestBody = null;
        ResponseEntity<Object> responseEntity = null;

        //Xứ lý call phân hệ thì tự động lấy authen tương ứng
        String callSubsystem = null;
        if (url.contains("/call-service-sap"))
            callSubsystem = "sap";
        else if (url.contains("/call-service-cons"))
            callSubsystem = "cons";
        else if (url.contains("/call-service-ihrp"))
            callSubsystem = "ihrp";
        else if (url.contains("/call-service-admin"))
            callSubsystem = "admin";
        //
        log.info("Starting call action-api {}: url={}", actionApi.getCallOrder(), url);
        try {
            /* BEGIN: parse config header */
            HttpHeaders configHeaders = parseRequestHeaders(header, variables);
            if (ValidationUtils.isNullOrEmpty(configHeaders)) {
                headers.addAll(getDefaultHeaders(actionApi, bpmProcinstId, procInstId, isLogAfter));
            } else {
                headers.addAll(configHeaders);
                headers.addAll(getAdditionalHeaders(actionApi, bpmProcinstId, procInstId, isLogAfter));
            }
            /* END: parse config header */

            if (!ValidationUtils.isNullOrEmpty(body)) {
                requestBody = parseBody(body, variables);
            }
            log.debug("DEBUG action api method= {}", method.toUpperCase());
            log.debug("DEBUG action api header= {}", headers);
            log.debug("DEBUG action api body= {}", requestBody);

            //Xứ lý call phân hệ thì tự động lấy authen tương ứng
            if (callSubsystem != null) {
                responseEntity = (ResponseEntity<Object>) bpmService.callBpmSubSystem(url,
                        method.toUpperCase(),
                        requestBody instanceof HashMap ? (Map<String, Object>) requestBody : new HashMap<>(),
                        convertHeaders(headers),
                        callSubsystem,
                        false,
                        variables,
                        true);
            } else {
                if(!headers.containsKey(HttpHeaders.AUTHORIZATION)){
                    headers.setBasicAuth(basicAuthConfig.getUsers().get(0).getUsername(), basicAuthConfig.getUsers().get(0).getPassword());
                }
                responseEntity = restHelper.exchange(url,
                        HttpMethod.valueOf(method.toUpperCase()),
                        headers,
                        requestBody,
                        new ParameterizedTypeReference<>() {
                        },
                        variables);
            }
            log.debug("DEBUG action api responseEntity= {}", responseEntity);

            boolean responseState = isResponseSuccess(responseEntity, actionApi, executeResult);

            // update variables
            updateVariable(executeResult, isCallAfter);

            return responseState;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            String msgSubSystem = "";
            if(callSubsystem != null){
                msgSubSystem = "Gọi hệ thống "+callSubsystem.toUpperCase()+" thất bại :";
            }

            // parse config response
            String message = msgSubSystem + ObjectUtils.writeValueAsString(ex.getMessage());
            Object responseBody = ObjectUtils.readValue(message);
            log.debug("Log action-api responseBody {} ", responseBody);
            executeResult.setResponse(responseBody);
            executeResult.getActionApiResult().setMessage(common.getMessage("message.ticket.errorApi", new Object[]{url}));

            //Log authen header da log
            if(responseBody == null || !responseBody.toString().contains("Unauthorized"))
                logError(url, method, headers, requestBody, responseEntity, ex);
        } finally {
            log.info("Finished call action-api {}: url={} in {} ms", actionApi.getCallOrder(), url, System.currentTimeMillis() - startTime);
        }

        return false;
    }

    private void updateVariable(ActionApiExecuteResult executeResult, boolean isCallAfter) {
//        if (!isCallAfter) {
//            return;
//        }

        String procInstId = executeResult.getProcInstId();
        Map<String, VariableValueDto> variables = getVariableFromResult(executeResult);
        if (!ValidationUtils.isNullOrEmpty(variables)) {
            Set<String> variableNameSet = variables.keySet();

            // delete old variable
            bpmVariablesService.deleteVariables(procInstId, null, variableNameSet);

            // insert variable
            bpmVariablesService.saveVariables(procInstId, null, variables, false);

            // update camunda
            Map<String, Variable> camundaVarMap = new HashMap<>();
            variables.forEach((k, v) -> camundaVarMap.put(k, new Variable(v.getType(), v.getValue())));

            VariableModification variableModification = new VariableModification();
            variableModification.setModifications(camundaVarMap);
            camundaEngineService.updateProcessVariables(procInstId, variableModification);
        }
    }

    private String buildUrl(String baseUrl, String url) {
        if (!ValidationUtils.isNullOrEmpty(baseUrl)) {
            baseUrl = baseUrl.replaceAll(CommonConstants.PATH_SEPARATOR + "+$", "") + CommonConstants.PATH_SEPARATOR;

            if (!ValidationUtils.isNullOrEmpty(url)) {
                url = url.replaceAll("^" + CommonConstants.PATH_SEPARATOR + "+", "");
                return baseUrl + url;
            } else {
                return baseUrl;
            }
        }

        return url;
    }

    private void logError(String url, String method, HttpHeaders headers, Object requestBody, ResponseEntity<Object> responseEntity, Exception exception) {
        try {
            String message = exception.getMessage();

            // check is not really error or already saved log on rest's logging interceptor
            if ((!ValidationUtils.isNullOrEmpty(message) && message.toLowerCase().matches("^\\d+.*"))
                    || (responseEntity != null && responseEntity.getStatusCode().equals(HttpStatus.OK))) {
                return;
            }

            String responseData = ObjectUtils.writeValueAsString(message);
            ApiLog apiLog = ApiLog.builder()
                    .url(url)
                    .method(method)
                    .header(HttpUtils.handleNullableJson(ObjectUtils.toJson(headers)))
                    .requestBody(HttpUtils.handleInvalidJson(HttpUtils.handleNullableJson(ObjectUtils.toJson(requestBody))))
                    .requestTime(LocalDateTime.now())
                    .responseTime(LocalDateTime.now())
                    .responseStatus("ERROR")
                    .responseData(responseData)
                    .apiType(CommonConstants.ApiType.TASK_ACTION)
                    .build();

            apiLogService.setAdditionalInfo(apiLog, headers);
            if (!apiLogTaskEnable) return;
            redisHelper.enqueue(CommonConstants.QueueName.API_LOG_QUEUE, apiLog);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    private boolean isResponseSuccess(ResponseEntity<Object> responseEntity, @NonNull ActionApiDto actionApi, @NonNull ActionApiExecuteResult executeResult) {
        String successCondition = !ValidationUtils.isNullOrEmpty(actionApi.getExSuccessCondition()) ? actionApi.getExSuccessCondition() : actionApi.getSuccessCondition();

        if (responseEntity != null) {
            Object responseBody = restHelper.getResponseBody(responseEntity);
            executeResult.setResponse(responseBody);

            // parse config response
            parseResponseBody(actionApi, executeResult, responseBody);

            if (ValidationUtils.isNullOrEmpty(successCondition)
                    || Arrays.stream(SUCCESS_CONDITION_LIST)
                    .filter(successCondition::equalsIgnoreCase)
                    .findAny()
                    .orElse(null) != null) {
                return true;
            } else {
                HttpStatus httpStatus = (HttpStatus) responseEntity.getStatusCode();

                // check status 200 - OK
                if (successCondition.equals(String.valueOf(HttpStatus.OK.value()))) {
                    return httpStatus.equals(HttpStatus.OK);
                }

                // check successful conditional by MVEL expression
                return responseBody != null && common.evalExpressionByMVEL(successCondition, responseBody, Boolean.class);
            }
        }

        return false;
    }

    private void parseResponseBody(@NonNull ActionApiDto actionApi, @NonNull ActionApiExecuteResult executeResult, Object responseBody) {
        if (responseBody != null) {
            String responseConfig = !ValidationUtils.isNullOrEmpty(actionApi.getExResponse()) ? actionApi.getExResponse() : actionApi.getResponse();
            String errorAttribute = actionApi.getErrorAttribute();
            if (!ValidationUtils.isNullOrEmpty(responseConfig) && responseBody instanceof Map) {
                executeResult.getActionApiResult().setResultData(parseBody(responseConfig, (Map) responseBody));
                executeResult.getActionApiResult().setMessage(common.evalExpressionByMVEL(errorAttribute, responseBody, String.class));
            }
        }
    }

    private HttpHeaders parseRequestHeaders(String header, Map<String, Object> variables) {
        HttpHeaders configHeaders = null;
        if (!ValidationUtils.isNullOrEmpty(header)) {
            try {
                header = replaceVariable(header, variables, false);
                configHeaders = HttpUtils.getHttpHeadersFromJson(header);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }

        return configHeaders;
    }

    @Nullable
    private Object parseBody(String body, Map<String, Object> variables) {
        if (!ValidationUtils.isNullOrEmpty(body)) {
            try {
                body = replaceVariable(body, variables, true);
                Object bodyObj = ObjectUtils.toObject(body, new TypeReference<>() {
                });

                return bodyObj != null ? bodyObj : body;
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }

        return null;
    }

    private String replaceVariable(String input, Map<String, Object> variables, boolean setNullUnknowValue) {
        if (ValidationUtils.isNullOrEmpty(variables)) {
            return input;
        }

        // normalize json string
//        input = ObjectUtils.minifyJson(input);

        List<String> placeHolders = getAllParamsPlaceHolder(input, true);
        if (ValidationUtils.isNullOrEmpty(placeHolders)
                && !input.toLowerCase().contains(TaskActionConstants.Function.JSON_ARRAY.toLowerCase() + "(")
                && !input.toLowerCase().contains(TaskActionConstants.Function.JSON_TO_ARRAY.toLowerCase() + "(")
        ) {
            return input;
        }

        Map<String, Object> functionVarMap = new HashMap<>();

        // replace all normal param, json function param
        input = placeHolders.stream()
                .reduce(input, (replaceString, placeHolder) -> {
                    String mapKey = placeHolder.replaceAll("\\{{2}|}{2}", "");

                    // check map key is functional
                    ActionApiParam actionApiParam = getFunctionalParam(mapKey, TaskActionConstants.Function.JSON);

                    Object valueObj;
                    if (actionApiParam != null) {
                        valueObj = getJsonValueFromPath(actionApiParam, variables, functionVarMap);
                    } else {
                        valueObj = variables.get(mapKey);
                    }

                    if (valueObj != null) {
                        return replaceString.replaceAll(Pattern.quote(placeHolder), valueObj.toString());
                    }

                    if (setNullUnknowValue && !ValidationUtils.isNullOrEmpty(placeHolder)) {
                        String nullSt = "null";
                        if (replaceString.indexOf("\"" + placeHolder + "\"") > 0) {
                            return replaceString.replaceAll(Pattern.quote("\"" + placeHolder + "\""), nullSt);
                        }

                        return replaceString.replaceAll(Pattern.quote(placeHolder), nullSt);
                    }

                    return replaceString;
                });

        // replace json_array function param
        if (input != null && input.toLowerCase().contains(TaskActionConstants.Function.JSON_ARRAY.toLowerCase() + "(")) {
            Map<String, Object> jsonMap = ObjectUtils.toObject(input, new TypeReference<>() {
            });
            if (jsonMap == null) {
                return input;
            }

            Map<String, String> jsonArrayConfigMap = getJsonArrayConfigMap(jsonMap, new HashMap<>());
            if (ValidationUtils.isNullOrEmpty(jsonArrayConfigMap)) {
                return input;
            }

            jsonArrayConfigMap.forEach((attribute, config) -> {
                if (!ValidationUtils.isNullOrEmpty(jsonMap.get(attribute))) {
                    if (config.contains(TaskActionConstants.Function.JSON_TO_ARRAY.toLowerCase() + "(")) {
                        jsonMap.put(attribute, getJsonToListResult(config, variables, functionVarMap));
                    } else {
                        jsonMap.put(attribute, getJsonArrayResultList(config, variables, functionVarMap));
                    }
                } else {
                    jsonMap.entrySet().stream().peek(entry -> {
                        findKeyMapAndPutData(entry, attribute, config, variables, functionVarMap);
                    }).collect(Collectors.toList());
                }
            });

            input = ObjectUtils.writeValueAsString(jsonMap);
        }

        if (input != null && input.toLowerCase().contains(TaskActionConstants.Function.JSON_TO_ARRAY.toLowerCase() + "(")) {
            Map<String, Object> jsonMap = ObjectUtils.toObject(input, new TypeReference<>() {
            });
            if (jsonMap == null) {
                return input;
            }

            Map<String, String> jsonArrayConfigMap = getJsonArrayConfigMap(jsonMap, new HashMap<>());
            if (ValidationUtils.isNullOrEmpty(jsonArrayConfigMap)) {
                return input;
            }

            jsonArrayConfigMap.forEach((attribute, config) -> {
                if (!ValidationUtils.isNullOrEmpty(jsonMap.get(attribute))) {
                    jsonMap.put(attribute, getJsonToListResult(config, variables, functionVarMap));
                }
            });

            input = ObjectUtils.writeValueAsString(jsonMap);
        }

        return input;
    }

    private void findKeyMapAndPutData(Map.Entry<String, Object> input, String keyFind, String config, @NonNull Map<String, Object> variables, @NonNull Map<String, Object> functionVarMap) {
        Object value = input.getValue();

        if (value instanceof Map) {
            Map<String, Object> mapValue = (Map<String, Object>) value;

            // Trường hợp tìm thấy key trong map hiện tại
            if (mapValue.containsKey(keyFind)) {
                mapValue.put(keyFind, getJsonArrayResultList(config, variables, functionVarMap));
            } else {
                // Trường hợp chưa tìm thấy key, thực hiện đệ quy trên từng entry trong map
                mapValue.entrySet().forEach(entry -> findKeyMapAndPutData(entry, keyFind, config, variables, functionVarMap));
            }
        } else if (value instanceof List) {
            List<Object> listValue = (List<Object>) value;

            // Trường hợp giá trị là một ArrayList
            for (int i = 0; i < listValue.size(); i++) {
                Object listItem = listValue.get(i);
                if (listItem instanceof Map) {
                    findKeyMapAndPutData(new AbstractMap.SimpleEntry<>(String.valueOf(i), listItem), keyFind, config, variables, functionVarMap);
                }
            }
        }
    }

    @NonNull
    private List<Object> getJsonArrayResultList(String config, @NonNull Map<String, Object> variables, @NonNull Map<String, Object> functionVarMap) {
        List<Object> result = new ArrayList<>();
        ActionApiParam actionApiParam = getFunctionalParam(config, TaskActionConstants.Function.JSON_ARRAY);
        if (actionApiParam == null) {
            return result;
        }

        // parse template to object map
        Object templateObject = ObjectUtils.toObject(actionApiParam.getTemplate(), Object.class);
        if (templateObject == null) {
            return result;
        }

        // get object from variable
        Object variableObject = getFunctionVariableValue(actionApiParam.getVarName(), variables, functionVarMap);
        if (variableObject == null) {
            return result;
        }

        // get oject from attribute
        Object data = null;
        if (actionApiParam.getAttribute() != null) {
            if (actionApiParam.getAttribute().isEmpty() || actionApiParam.getAttribute().equalsIgnoreCase(actionApiParam.getVarName())) {
                data = variableObject;
            } else {
                data = common.evalExpressionByMVEL(actionApiParam.getAttribute(), variableObject, Object.class);
            }
        }

        if (data == null) {
            return result;
        }

        // parse result
        if (data instanceof List) {
            List<Object> dataList = (List<Object>) data;
            dataList.forEach(e -> result.add(createArrayObject(e, templateObject, variables, functionVarMap)));
        } else {
            result.add(createArrayObject(data, templateObject, variables, functionVarMap));
        }

        return result;
    }

    @NonNull
    private List<Object> getJsonToListResult(String config, @NonNull Map<String, Object> variables, @NonNull Map<String, Object> functionVarMap) {
        List<Object> result = new ArrayList<>();
        ActionApiParam actionApiParam = getFunctionalParam(config, TaskActionConstants.Function.JSON_TO_ARRAY);
        if (actionApiParam == null) {
            return result;
        }

        // parse template to object map
        Object templateObject = ObjectUtils.toObject(actionApiParam.getTemplate(), Object.class);
        if (templateObject == null) {
            return result;
        }

        // get object from variable
        Object variableObject = getFunctionVariableValue(actionApiParam.getVarName(), variables, functionVarMap);
        if (variableObject == null) {
            return result;
        }

        // get oject from attribute
        Object data = null;
        if (actionApiParam.getAttribute() != null) {
            if (actionApiParam.getAttribute().isEmpty() || actionApiParam.getAttribute().equalsIgnoreCase(actionApiParam.getVarName())) {
                data = variableObject;
            } else {
                data = common.evalExpressionByMVEL(actionApiParam.getAttribute(), variableObject, Object.class);
            }
        }

        if (data == null) {
            return result;
        }

        // parse result
        if (data instanceof List) {
            List<Object> dataList = (List<Object>) data;
            result.addAll(dataList);
        } else {
            result.add(createArrayObject(data, templateObject, variables, functionVarMap));
        }

        return result;
    }

    private Object createArrayObject(Object data, @NonNull Object templateObject, @NonNull Map<String, Object> variables, @NonNull Map<String, Object> functionVarMap) {
        if (data == null) {
            return templateObject;
        }
        if (templateObject.toString().contains(GET_ALL_JSON_ARRAY)) {
            Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();
            return g.toJson(data);
        }
        if (templateObject instanceof String) {
            return getAttributeData(data, templateObject.toString(), variables, functionVarMap);
        } else if (templateObject instanceof Map) {
            Map<String, Object> templateMap = (Map<String, Object>) templateObject;
            Map<String, Object> resultMap = new HashMap<>();
            templateMap.forEach((k, v) -> {
                if (v instanceof String) {
                    resultMap.put(k, getAttributeData(data, v.toString(), variables, functionVarMap));
                } else {
                    resultMap.put(k, v);
                }
            });

            return resultMap;
        }

        return templateObject;
    }

    private Object getAttributeData(@NonNull Object data, String key, @NonNull Map<String, Object> variables, @NonNull Map<String, Object> functionVarMap) {
        String jsonArrayParamKey = getJsonArrayParamKey(key);
        if (!ValidationUtils.isNullOrEmpty(jsonArrayParamKey)) {
            return common.evalExpressionByMVEL(jsonArrayParamKey, data, Object.class);
        } else if (isJsonArrayFunction(key)) {
            return getJsonArrayResultList(key, variables, functionVarMap);
        }

        return key;
    }

    private boolean isJsonArrayFunction(String input) {
        return !ValidationUtils.isNullOrEmpty(input) && input.toLowerCase().startsWith(TaskActionConstants.Function.JSON_ARRAY.toLowerCase() + "(");
    }

    private String getJsonArrayParamKey(String param) {
        if (!ValidationUtils.isNullOrEmpty(param)) {
            Pattern pattern = Pattern.compile("\\$\\{(.+)}");
            Matcher matcher = pattern.matcher(param);
            if (matcher.find()) {
                return matcher.group(1);
            }
        }

        return null;
    }

    @NonNull
    private Map<String, String> getJsonArrayConfigMap(Map<String, Object> jsonMap, @Nullable Map<String, String> jsonArrayConfigMap) {
//        Map<String, String> jsonArrayConfigMap = new HashMap<>();
        if (!ValidationUtils.isNullOrEmpty(jsonMap)) {
            jsonMap.forEach((k, v) -> {
                if (v instanceof String) {
                    String config = v.toString();
                    if (config.toLowerCase().startsWith(TaskActionConstants.Function.JSON_ARRAY.toLowerCase())
                            || config.toLowerCase().startsWith(TaskActionConstants.Function.JSON_TO_ARRAY.toLowerCase())) {
                        jsonArrayConfigMap.put(k, config);
                    }
                } else if (v instanceof HashMap) {
                    getJsonArrayConfigMap((Map<String, Object>) v, jsonArrayConfigMap);
//                    if (!ValidationUtils.isNullOrEmpty(result)) {
//                        jsonArrayConfigMap.putAll(result);
//                    }
                } else if (v instanceof ArrayList) {
                    ((ArrayList<?>) v).stream().forEach(i -> {
                        getJsonArrayConfigMap((Map<String, Object>) i, jsonArrayConfigMap);
//                        if(i instanceof String){
//                           jsonArrayConfigMap.put(k, ((String) i).toString());
//                        } else if(i instanceof HashMap){
//                            getJsonArrayConfigMap((Map<String, Object>) i);
//                        }
                    });

                }
            });
        }

        return jsonArrayConfigMap;
    }

    /**
     * Parse special function param
     */
    @Nullable
    private ActionApiParam getFunctionalParam(String key, String functionName) {
        if (!ValidationUtils.isNullOrEmpty(key)) {
            Pattern pattern = Pattern.compile("^(?i)(" + functionName + ")\\((.+?)\\)\\.*([\\w.\\[\\]]*)$");
            Matcher matcher = pattern.matcher(key);
            if (matcher.find()) {
                ActionApiParam actionApiParam = new ActionApiParam(matcher.group(1), matcher.group(2), matcher.group(3));

                // set json_array param
                if (actionApiParam.getFunction().equalsIgnoreCase(TaskActionConstants.Function.JSON_ARRAY)
                        || actionApiParam.getFunction().equalsIgnoreCase(TaskActionConstants.Function.JSON_TO_ARRAY)
                ) {
                    try {
                        // split param by var-name
                        String[] paramArray = actionApiParam.getVarName().split(",", 3);
                        actionApiParam.setVarName(paramArray[0] != null ? paramArray[0].trim() : actionApiParam.getVarName());
                        actionApiParam.setAttribute(paramArray[1] != null ? paramArray[1].trim() : actionApiParam.getAttribute());
                        if (paramArray.length > 2)
                            actionApiParam.setTemplate(paramArray[2] != null ? paramArray[2].trim() : actionApiParam.getTemplate());
                        else {
                            Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();
                            actionApiParam.setTemplate(g.toJson("\"" + GET_ALL_JSON_ARRAY + "\""));
                        }
                    } catch (Exception ex) {
                        Gson g = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new GsonAdapterConfig())
                    .registerTypeAdapter(LocalDate.class, new GsonAdapterConfig())
                    .create();
                        actionApiParam.setTemplate(g.toJson("\"" + GET_ALL_JSON_ARRAY + "\""));
                        log.error(ex.getMessage(), ex);
                    }
                }

                return actionApiParam;
            }
        }

        return null;
    }

    @Nullable
    private Object getJsonValueFromPath(@NonNull ActionApiParam actionApiParam, @NonNull Map<String, Object> variables, @NonNull Map<String, Object> functionVarMap) {
        String varName = actionApiParam.getVarName();

        // check existed in function variable map
        Object value = getFunctionVariableValue(varName, variables, functionVarMap);

        if (value != null) {
            if (ValidationUtils.isNullOrEmpty(actionApiParam.getAttribute())) { // if doesn't specific attribute then return full variable object
                return value;
            }

            return common.evalExpressionByMVEL(actionApiParam.getAttribute(), value, Object.class);
        }

        return null;
    }

    private Object getFunctionVariableValue(String varName, @NonNull Map<String, Object> variables, @NonNull Map<String, Object> functionVarMap) {
        if (!ValidationUtils.isNullOrEmpty(varName)) {
            Object value = functionVarMap.get(varName);
            if (value == null) {
                value = variables.get(varName);
                if (value instanceof String) {
                    value = ObjectUtils.toObject(value.toString(), Object.class);
                }

                functionVarMap.put(varName, value);
            }

            return value;
        }

        return null;
    }

    @Override
    public List<String> getAllParamsPlaceHolder(String source, boolean excludeSpecialFunction) {
        String regex = "\\{\\{.*?}}";
        if (excludeSpecialFunction) {
            regex = "(?i)\\{\\{(?!" + TaskActionConstants.Function.JSON_ARRAY + ").*?}}";
        }

        return parsePlaceHolderByRegex(source, regex);
    }

    @Override
    public List<String> parsePlaceHolderByRegex(String source, String regex) {
        List<String> placeHolders = new ArrayList<>();
        if (!ValidationUtils.isNullOrEmpty(source) && !ValidationUtils.isNullOrEmpty(regex)) {
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(source);
            while (matcher.find()) {
                String property = matcher.group();
                property = !ValidationUtils.isNullOrEmpty(property) ? property.trim() : property;
                placeHolders.add(property);
            }
        }

        return placeHolders;
    }

    @Override
    public HttpHeaders getDefaultHeaders(ActionApiDto actionApi, Long bpmProcinstId, String procInstId, boolean isLogAfter) {
        HttpHeaders headers = HttpUtils.getDefaultApplicationJsonHeaders();
        headers.addAll(getAdditionalHeaders(actionApi, bpmProcinstId, procInstId, isLogAfter));


        return headers;
    }

    private HttpHeaders getAdditionalHeaders(ActionApiDto actionApi, Long bpmProcinstId, String procInstId, boolean isLogAfter) {
        HttpHeaders headers = new HttpHeaders();
        headers.add(CommonConstants.ApiHeader.API_TYPE, CommonConstants.ApiType.TASK_ACTION);
        headers.add(CommonConstants.ApiHeader.ADDITIONAL_INFO, ObjectUtils.toJson(createAdditionApiInfoDto(actionApi, bpmProcinstId, procInstId, isLogAfter)));

        return headers;
    }

    private AdditionApiInfoDto createAdditionApiInfoDto(ActionApiDto actionApi, Long bpmProcinstId, String procInstId, boolean isLogAfter) {
//        String baseUrl = "https://bds-staging.truesight.asia/";
        String baseUrlAuthenApi = "";
        AdditionApiInfoDto additionApiInfoDto = new AdditionApiInfoDto();
        additionApiInfoDto.setBpmProcdefApiId(actionApi.getBpmProcdefApiId());
        additionApiInfoDto.setBpmProcinstId(bpmProcinstId);
        additionApiInfoDto.setAuthenApiId(actionApi.getAuthenApiId());
        if (actionApi.getAuthenApiId() != null) {
            baseUrlAuthenApi = apiManagementRepository.findBaseUrlAuthenApi(actionApi.getAuthenApiId());
        }
//        additionApiInfoDto.setAuthenUrl(baseUrl+actionApi.getAuthenUrl());
        additionApiInfoDto.setAuthenUrl(baseUrlAuthenApi + actionApi.getAuthenUrl());
        additionApiInfoDto.setAuthenHeader(actionApi.getAuthenHeader());
        additionApiInfoDto.setAuthenMethod(actionApi.getAuthenMethod());
        additionApiInfoDto.setAuthenBody(actionApi.getAuthenBody());
        additionApiInfoDto.setTokenAttribute(actionApi.getTokenAttribute());
        additionApiInfoDto.setLogAfter(isLogAfter);
        additionApiInfoDto.setProcInstId(procInstId);

        return additionApiInfoDto;
    }

    @Override
    public Map<String, Object> createVariablesMap(Object data) {
        Map<String, Object> variableMap = new HashMap<>();

        if (data == null) {
            return variableMap;
        }

        if (data instanceof Map) {
            variableMap.putAll(extractFromMap((Map<?, ?>) data));
        } else {
            Map<String, Object> convertedDataMap = ObjectUtils.convertObject(data, new TypeReference<>() {
            });
            if (convertedDataMap != null) {
                variableMap.putAll(convertedDataMap);
            }
        }

        return variableMap;
    }

    @NonNull
    private Map<String, Object> extractFromMap(Map<?, ?> map) {
        Map<String, Object> variableMap = new HashMap<>();
        if (!ValidationUtils.isNullOrEmpty(map)) {
            map.forEach((key, value) -> {
                String mapKey = (String) key;
                Object valueMap = value;
                if (valueMap instanceof VariableValueDto) {
                    valueMap = getAdditionalValue((VariableValueDto) valueMap);
                    if (valueMap instanceof VariableValueDto && ((VariableValueDto) valueMap).getValue() instanceof String) {
                        ((VariableValueDto) valueMap).setValue(((VariableValueDto) valueMap).getValue().toString().replaceAll("\n", "<br/>"));
                    } else if (valueMap instanceof String) {
                        valueMap = valueMap.toString().replaceAll("\n", "<br/>");
                    }
                    variableMap.put(mapKey, valueMap);
                } else if (value != null) {
                    valueMap = valueMap.toString().replaceAll("\n", "<br/>");
                    variableMap.put(mapKey, valueMap);
                }
            });
        }

        return variableMap;
    }

    /**
     * Check and get additional value like DATE...
     */
    private Object getAdditionalValue(@NonNull VariableValueDto valueDto) {
        String additionalValue = bpmVariablesService.getAdditionalValue(valueDto);
        return additionalValue != null ? additionalValue : valueDto.getValue();
    }

    @Override
    public Map<String, Object> createVariablesMap(Object... data) {
        Map<String, Object> variableMap = new HashMap<>();
        if (data != null) {
            Arrays.stream(data).forEach(e -> variableMap.putAll(createVariablesMap(e)));
        }
        // add actionTime
        variableMap.put("actionTime", localDateTimeToString(LocalDateTime.now(), FORMAT_DATE_TIME_2));
        return variableMap;
    }

    @Override
    public void sendLogAfter(String procInstId, Long bpmProcInstId) {
        if (!ValidationUtils.isNullOrEmpty(procInstId)) {
            ActionApiLogAfterBean logAfterBean = new ActionApiLogAfterBean(procInstId, bpmProcInstId);
            redisHelper.enqueue(CommonConstants.QueueName.LOG_AFTER_QUEUE, logAfterBean);
        }
    }

    @Override
    public Map<String, VariableValueDto> getTicketVariables(String procInstId) {
        Map<String, VariableValueDto> variableMap = new HashMap<>();
        if (ValidationUtils.isNullOrEmpty(procInstId)) {
            return variableMap;
        }

        List<Map<String, VariableValueDto>> variableList = actHiVarInstManager.getVariByTicketConvertToVariableValueDto(procInstId);
        if (!ValidationUtils.isNullOrEmpty(variableList)) {
            variableList.forEach(variableMap::putAll);
        }

        return variableMap;
    }

    @Override
    public List<ActionApiResult> getActionApiResults(ActionApiContext actionApiContext) {
        List<ActionApiResult> actionApiResults = new ArrayList<>();
        if (actionApiContext != null && !ValidationUtils.isNullOrEmpty(actionApiContext.getApiExecuteBeforeActionResults())) {
            actionApiContext.getApiExecuteBeforeActionResults().forEach(e -> {
                if (e.isReturnResponse()) {
                    actionApiResults.add(e.getActionApiResult());
                }
            });
        }

        return actionApiResults;
    }

    @Override
    public Map<String, VariableValueDto> getVariableFromResult(List<ActionApiExecuteResult> actionApiExecuteResults) {
        Map<String, VariableValueDto> variables = new HashMap<>();
        if (ValidationUtils.isNullOrEmpty(actionApiExecuteResults)) {
            return variables;
        }

        actionApiExecuteResults.forEach(e -> variables.putAll(getVariableFromResult(e)));

        return variables;
    }

    @Override
    public Map<String, VariableValueDto> getVariableFromResult(ActionApiExecuteResult actionApiExecuteResult) {
        Map<String, VariableValueDto> variables = new HashMap<>();
        if (actionApiExecuteResult == null) {
            return variables;
        }

        ActionApiResult actionApiResult = actionApiExecuteResult.getActionApiResult();
        if (actionApiResult != null) {
            if (actionApiResult.getResultData() instanceof Map) {
                Map<String, Object> resultMap = (Map<String, Object>) actionApiResult.getResultData();
                resultMap.forEach((k, v) -> {
                    if (v instanceof VariableValueDto) {
                        variables.put(k, (VariableValueDto) v);
                    } else {
                        VariableValueDto valueDto = new VariableValueDto();

                        //Update lại bảng variable khi dạng tbl trả về kiểu Array<String> =>Convert về dúng dạng json của biểu mẫu
                        if (k.indexOf("tbl_") != -1 && v instanceof ArrayList && ((ArrayList<?>) v).size() > 0) {
                            valueDto.setType(CommonConstants.VarType.JSON);
                            valueDto.setValue(((ArrayList<?>) v).get(0));
                        } else {
                            valueDto.setType(StringUtils.capitalize(CommonConstants.VarType.STRING.toLowerCase()));
                            valueDto.setValue(v);
                        }

                        variables.put(k, valueDto);
                    }
                });
            }
        }

        return variables;
    }

    @Override
    public void setActionApiResponse(ActionApiContext actionApiContext, @NonNull Map<String, Object> responseData) {
        Map<String, Object> actionApiResponse = ObjectUtils.convertObject(new ActionApiResponse(getActionApiResults(actionApiContext)), new TypeReference<>() {
        });

        if (actionApiResponse != null) {
            responseData.putAll(actionApiResponse);
        }
    }
}
