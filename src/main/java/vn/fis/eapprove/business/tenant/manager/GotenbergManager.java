package vn.fis.eapprove.business.tenant.manager;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.constants.MapKeyEnum;
import vn.fis.spro.common.helper.RestHelper;
import vn.fis.spro.common.util.ValidationUtils;

import java.io.InputStream;
import java.net.URL;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class GotenbergManager {
    private final SproProperties sproProperties;

    private RestHelper restHelper;

    private final CredentialHelper credentialHelper;

    public GotenbergManager(SproProperties sproProperties, RestHelper restHelper, CredentialHelper credentialHelper) {
        this.sproProperties = sproProperties;
        this.restHelper = restHelper;
        this.credentialHelper = credentialHelper;
    }

    public ResponseEntity<byte[]> htmlToPdf(MultipartFile file) throws Exception {
        String url = sproProperties.getServiceUrls().get(MapKeyEnum.PDF_SERVICE.key) + "/forms/chromium/convert/html";
//        String url = "https://uat-api-eapp.datxanh.com.vn/forms/chromium/convert/html";

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(credentialHelper.getJWTToken());
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        // Sử dụng ByteArrayResource thay vì InputStreamResource
        ByteArrayResource byteArrayResource = new ByteArrayResource(file.getBytes()) {
            @Override
            public String getFilename() {
                return file.getOriginalFilename(); // Đảm bảo gửi tên tệp chính xác
            }
        };

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("files", byteArrayResource);

        ResponseEntity<byte[]> responseEntity = restHelper.exchange(url,
                HttpMethod.POST,
                headers,body,
                new ParameterizedTypeReference<>() {});

        log.info("htmlToPdf DONE {}", responseEntity);
        return responseEntity;
    }

    public byte[] htmlStringToPdf(String htmlString, String headerString, String footerString) {
        String url = sproProperties.getServiceUrls().get(MapKeyEnum.PDF_SERVICE.key) + "/forms/chromium/convert/html";
//        String url = "https://uat-api-eapp.datxanh.com.vn/forms/chromium/convert/html";

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(credentialHelper.getJWTToken());
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            ByteArrayResource byteArrayResource = new ByteArrayResource(htmlString.getBytes()) {
                @Override
                public String getFilename() {
                    return "index.html";
                }
            };
            body.add("files", byteArrayResource);

            if (!ValidationUtils.isNullOrEmpty(headerString)) {
                if (headerString.contains("http")) {
                    Pattern pattern = Pattern.compile("<img[^>]+src=[\"'](http[^\"']+)[\"']", Pattern.CASE_INSENSITIVE);
                    Matcher matcher = pattern.matcher(headerString);
                    while (matcher.find()) {
                        String imageUrl = matcher.group(1);
                        String base64 = getBase64FromUrl(imageUrl);
                        headerString = headerString.replace(imageUrl, "data:image/png;base64," + base64);
                    }
                }
                body.add("files", new ByteArrayResource(headerString.getBytes()) {
                    @Override
                    public String getFilename() {
                        return "header.html";
                    }
                });
            }
            if (!ValidationUtils.isNullOrEmpty(footerString)) {
                if (footerString.contains("http")) {
                    Pattern pattern = Pattern.compile("<img[^>]+src=[\"'](http[^\"']+)[\"']", Pattern.CASE_INSENSITIVE);
                    Matcher matcher = pattern.matcher(footerString);
                    while (matcher.find()) {
                        String imageUrl = matcher.group(1);
                        String base64 = getBase64FromUrl(imageUrl);
                        footerString = footerString.replace(imageUrl, "data:image/png;base64," + base64);
                    }
                }
                body.add("files", new ByteArrayResource(footerString.getBytes()) {
                    @Override
                    public String getFilename() {
                        return "footer.html";
                    }
                });
            }

            ResponseEntity<byte[]> responseEntity = restHelper.exchange(url,
                    HttpMethod.POST,
                    headers, body,
                    new ParameterizedTypeReference<>() {});

            if (responseEntity != null) {
                log.info("htmlStringToPdf success");
                return responseEntity.getBody();
            }

            return null;
        } catch (Exception e) {
            log.error("htmlStringToPdf error", e);
            return null;
        }
    }

    public byte[] htmlStringToPdfTest(String htmlString, String headerString, String footerString) {
//        String url = sproProperties.getServiceUrls().get(MapKeyEnum.PDF_SERVICE.key) + "/forms/chromium/convert/html";
        String url = "https://uat-api-eapp.datxanh.com.vn/forms/chromium/convert/html";

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(credentialHelper.getJWTToken());
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            ByteArrayResource byteArrayResource = new ByteArrayResource(htmlString.getBytes()) {
                @Override
                public String getFilename() {
                    return "index.html";
                }
            };
            body.add("files", byteArrayResource);

            if (!ValidationUtils.isNullOrEmpty(headerString)) {
                if (headerString.contains("http")) {
                    Pattern pattern = Pattern.compile("<img[^>]+src=[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE);
                    Matcher matcher = pattern.matcher(headerString);
                    while (matcher.find()) {
                        String imageUrl = matcher.group(1);
                        String base64 = getBase64FromUrl(imageUrl);
                        headerString = headerString.replace(imageUrl, "data:image/png;base64," + base64);
                        System.out.println(imageUrl);
                    }
                }
                ByteArrayResource headerResource = new ByteArrayResource(headerString.getBytes()) {
                    @Override
                    public String getFilename() {
                        return "header.html";
                    }
                };
                body.add("files", headerResource);
            }
            if (!ValidationUtils.isNullOrEmpty(footerString)) {
                ByteArrayResource footerResource = new ByteArrayResource(footerString.getBytes()) {
                    @Override
                    public String getFilename() {return "footer.html";}
                };
                body.add("files", footerResource);
            }

            ResponseEntity<byte[]> responseEntity = restHelper.exchange(url,
                    HttpMethod.POST,
                    headers, body,
                    new ParameterizedTypeReference<>() {});

            if (responseEntity != null) {
                log.info("htmlStringToPdf success");
                return responseEntity.getBody();
            }

            return null;
        } catch (Exception e) {
            log.error("htmlStringToPdf error", e);
            return null;
        }
    }

    private static String getBase64FromUrl(String imageUrl) throws Exception {
        try (InputStream in = new URL(imageUrl).openStream()) {
            byte[] bytes = in.readAllBytes();
            return Base64.getEncoder().encodeToString(bytes);
        }
    }
}
