package vn.fis.eapprove.business.tenant.manager.impl;

import vn.fis.eapprove.security.CredentialHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import vn.fis.eapprove.business.domain.bpm.repository.BpmHistoryRepository;
import vn.fis.eapprove.business.dto.filter.ReportProcInstFilter;
import vn.fis.eapprove.business.dto.filter.RequestDetailTaskReturned;
import vn.fis.eapprove.business.dto.report.*;
import vn.fis.eapprove.business.exception.report.BusinessCode;
import vn.fis.eapprove.business.exception.report.BusinessException;
import vn.fis.eapprove.business.model.response.DetailReportByGroupResponse;
import vn.fis.eapprove.business.model.response.UserInfoByUsername;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.tenant.manager.DetailReportServiceNew;
import vn.fis.eapprove.business.utils.ReportHelperNew;
import vn.fis.eapprove.business.utils.TimeUtils;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.Tuple;
import java.math.BigInteger;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
@Slf4j
public class DetailReportServiceImplNew implements DetailReportServiceNew {

    private final EntityManager entityManager;

    private final ReportHelperNew reportHelper;

    private final BpmHistoryRepository bpmHistoryRepository;

    private final CustomerService customerService;

    private final CredentialHelper credentialHelper;

    @Override
    public DetailReportByGroupResponse getDetailReportByChartNode(ReportProcInstFilter filter) {
        String status = null;

        String username;
        try {
            username = credentialHelper.getJWTPayload().getUsername();
        } catch (Exception e) {
            throw new BusinessException(BusinessCode.TOKEN_INVALID);
        }
        LocalDate date = LocalDate.parse(filter.getToDate());
        String newDate = date.plusDays(1).toString();
        filter.setToDate(newDate);

        if (!ValidationUtils.isNullOrEmpty(filter.getUserStatus()) && filter.getUserStatus().size() == 1) {
            status = filter.getUserStatus().get(0);
        }
        Set<String> users = new HashSet<>(customerService.getUserDefault(username, status));
        users.add(username);
        filter.getDefaultUser().addAll(users);
        if (filter.getUserStatus().size() == 1 && filter.getUserStatus().get(0).equalsIgnoreCase("inactive")) {
            filter.getDefaultUser().remove(username);
        }
        List<Long> chartNodes = customerService.getChartNodeIdsByUsernames(filter.getDefaultUser());
        filter.setDefaultChartNodeId(chartNodes);

        DetailReportByGroupResponse detailReportByGroupResponse = new DetailReportByGroupResponse();
        try {
            List<DetailReportByGroupDto> detailReportByGroupDtos = getListDetailReportByChartNode(filter);
            detailReportByGroupResponse.setList(detailReportByGroupDtos);
            detailReportByGroupResponse.setTotal(countDetailProcInstByChartNode(filter).longValue());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return detailReportByGroupResponse;
    }

    @Override
    public DetailReportByGroupResponse getDetailReportByUser(ReportProcInstFilter filter) {
        String status = null;

        String username;
        try {
            username = credentialHelper.getJWTPayload().getUsername();
        } catch (Exception e) {
            throw new BusinessException(BusinessCode.TOKEN_INVALID);
        }
        LocalDate date = LocalDate.parse(filter.getToDate());
        String newDate = date.plusDays(1).toString();
        filter.setToDate(newDate);

        if (!ValidationUtils.isNullOrEmpty(filter.getUserStatus()) && filter.getUserStatus().size() == 1) {
            status = filter.getUserStatus().get(0);
        }

        Set<String> users = new HashSet<>(customerService.getUserDefault(username, status));
        users.add(username);
        filter.getDefaultUser().addAll(users);

        if (filter.getUserStatus().size() == 1 && filter.getUserStatus().get(0).equalsIgnoreCase("inactive")) {
            filter.getDefaultUser().remove(username);
        }

        List<Long> chartNodes = customerService.getChartNodeIdsByUsernames(filter.getDefaultUser());
        filter.setDefaultChartNodeId(chartNodes);

        DetailReportByGroupResponse detailReportByGroupResponse = new DetailReportByGroupResponse();
        List<DetailReportByGroupDto> detailReportByGroupDtos = getListDetailReportByUser(filter);
        detailReportByGroupResponse.setList(detailReportByGroupDtos);
        detailReportByGroupResponse.setTotal(countDetailProcInstByUser(filter).longValue());

        return detailReportByGroupResponse;
    }


    @Override
    public DetailReportByGroupResponse getDetailReportByGroup(ReportProcInstFilter filter) {
        String status = null;

        String username;
        try {
            username = credentialHelper.getJWTPayload().getUsername();
        } catch (Exception e) {
            throw new BusinessException(BusinessCode.TOKEN_INVALID);
        }
        LocalDate date = LocalDate.parse(filter.getToDate());
        String newDate = date.plusDays(1).toString();
        filter.setToDate(newDate);

        if (!ValidationUtils.isNullOrEmpty(filter.getUserStatus()) && filter.getUserStatus().size() == 1) {
            status = filter.getUserStatus().get(0);
        }

        Set<String> users = new HashSet<>(customerService.getUserDefault(username, status));
        users.add(username);
        filter.getDefaultUser().addAll(users);

        if (filter.getUserStatus().size() == 1 && filter.getUserStatus().get(0).equalsIgnoreCase("inactive")) {
            filter.getDefaultUser().remove(username);
        }

        List<Long> chartNodes = customerService.getChartNodeIdsByUsernames(filter.getDefaultUser());
        filter.setDefaultChartNodeId(chartNodes);

        DetailReportByGroupResponse detailReportByGroupResponse = new DetailReportByGroupResponse();

        List<DetailReportByGroupDto> detailReportByGroupDtos = getListDetailReportByGroup(filter);

        detailReportByGroupResponse.setList(detailReportByGroupDtos);
        detailReportByGroupResponse.setTotal(countDetailProcInstByGroup(filter).longValue());

        return detailReportByGroupResponse;
    }

    @Override
    public List<DetailTaskDto> getDetailTaskByProcInstId(ReportProcInstFilter filter) {

        log.info("to date: {}", filter.getToDate());

        return getListDetailTask(filter.getTicketId(), filter.getFromDate(), filter.getToDate());
    }

    @Override
    public List<DetailTaskReturnedDto> getDetailTaskReturned(RequestDetailTaskReturned request) {
        String mainQuery = "select distinct bh.task_inst_id as task_inst_id, bh.note as ly_do_tra_ve, bh.created_time as thoi_gian_tra_ve,bh.action_user as nguoi_tra_ve,rp.created_user as nguoi_bi_tra_ve\n" +
                "from bpm_history bh\n" +
                "join report_by_group_new rp on bh.ticket_id = rp.ticket_id\n" +
                "where bh.action IN ('REQUEST_UPDATE','AFFECTED_BY_RU') AND \n";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        if (!ObjectUtils.isEmpty(request.getTaskInstId())) {
            stringBuilder.append(" bh.task_inst_id = :taskInstId AND ");
        }
        if (!ObjectUtils.isEmpty(request.getProcInstId())) {
            stringBuilder.append(" bh.proc_inst_id = :procInstId AND ");
        }
        stringBuilder.append(" 1 = 1 \n " +
                " order by bh.created_time asc ");
        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);

        if (!ObjectUtils.isEmpty(request.getTaskInstId())) {
            query.setParameter("taskInstId", request.getTaskInstId());
        }
        if (!ObjectUtils.isEmpty(request.getProcInstId())) {
            query.setParameter("procInstId", request.getProcInstId());
        }
        List<Tuple> result = query.getResultList();
        List<DetailTaskReturnedDto> detailTaskReturnedDtos = new ArrayList<>();

        result.forEach(tuple -> {
            String taskInstId = tuple.get("task_inst_id") == null ? null : String.valueOf(tuple.get("task_inst_id"));
            String reasonReturned = tuple.get("ly_do_tra_ve") == null ? null : String.valueOf(tuple.get("ly_do_tra_ve"));
            String actionUser = tuple.get("nguoi_tra_ve") == null ? null : String.valueOf(tuple.get("nguoi_tra_ve"));
            String createdUser = tuple.get("nguoi_bi_tra_ve") == null ? null : String.valueOf(tuple.get("nguoi_bi_tra_ve"));

            DetailTaskReturnedDto detailTaskReturnedDto = new DetailTaskReturnedDto();
            LocalDateTime timeReturned = tuple.get("thoi_gian_tra_ve") == null ? null : TimeUtils.stringToLocalDateTime(tuple.get("thoi_gian_tra_ve").toString(), TimeUtils.FORMAT_DATE_DD_MM_YYYY);
            detailTaskReturnedDto.setTimeReturned(timeReturned);
            detailTaskReturnedDto.setReasonReturned(reasonReturned);
            detailTaskReturnedDto.setTaskInstId(taskInstId);
            detailTaskReturnedDto.setReturner(actionUser);
            detailTaskReturnedDto.setReturnee(createdUser);

            UserInfoByUsername returnerInfo = customerService.getUserInfoByUsername(actionUser);
            UserInfoByUsername returneeInfo = customerService.getUserInfoByUsername(createdUser);

            detailTaskReturnedDto.setReturnerInfo(returnerInfo);
            detailTaskReturnedDto.setReturneeInfo(returneeInfo);
            detailTaskReturnedDtos.add(detailTaskReturnedDto);
        });
        return detailTaskReturnedDtos;
    }

    @Override
    public DetailTicketCancelResponse getDetailTicketCancel(String procInstId) {
        DetailTicketCancelResponse response = new DetailTicketCancelResponse();
        DetailTicketCancelProjection detailTicketCancelProjection = bpmHistoryRepository.getDetailTicketCancel(procInstId);
        UserInfoByUsername assigneeInfoResponse = customerService.getUserInfoByUsername(detailTicketCancelProjection.getCancelUser());
        response.setCancelReason(detailTicketCancelProjection.getCancelReason());
        response.setCreatedTime(detailTicketCancelProjection.getCreatedTime());
        response.setProcInstId(detailTicketCancelProjection.getProcInstId());
        response.setCancelUserInfo(assigneeInfoResponse);
        return response;
    }

    private List<DetailTaskDto> getListDetailTask(Long ticketId, String fromDate, String toDate) {
        log.info("getListDetailTask ticketId: {}, fromDate: {}, toDate: {}", ticketId, fromDate, toDate);
        LocalDate date = LocalDate.parse(toDate);
        String newDate = date.plusDays(1).toString();
        String mainQuery =
                "with history as (select v.task_id, MAX(version_time) as version_time\n" +
                        "                 from (SELECT task_id, MAX(version_time) as version_time\n" +
                        "                       FROM report_by_chart_node_new\n" +
                        "                       where version_time between :fromDate and :toDate\n" +
                        "                       GROUP BY task_id\n" +
                        "                       union all\n" +
                        "                       SELECT task_id, MAX(version_time) as version_time\n" +
                        "                       FROM report_by_chart_node_new\n" +
                        "                       where version_time < :fromDate\n" +
                        "                       GROUP BY task_id) as v\n" +
                        "                 group by task_id) \n" +
                        " select rc.task_id            as task_id,\n" +
                        "       rc.task_type                     as type,\n" +
                        "       rc.task_name                     as name,\n" +
                        "       rc.created_time,\n" +
                        "       rc.started_time,\n" +
                        "       rc.task_status                   as status,\n" +
                        "       rc.proc_inst_id,\n" +
                        "       rc.proc_def_id,\n" +
                        "       rc.priority_name as priority,\n" +
                        "       rc.request_code ,\n" +
                        "       rc.assignee_full_name,\n" +
                        "       rc.assignee_chart_short_name     as assignee_chart_short_name,\n" +
                        "       rc.assignee                      as assignee,\n" +
                        "       TIMESTAMPDIFF(HOUR, rc.created_time,rc.sla_finish_time) AS elapsed_time,\n" +
                        "       TIMESTAMPDIFF(HOUR,ifnull(rc.finished_time,current_timestamp), rc.sla_finish_time) AS delay_time,\n" +
                        "       rc.sla_finish_time                as sla_finish_time,\n" +
                        "       rc.finished_time                  as finished_time,\n" +
                        "       rc.assignee_chart_node_name      as assignee_chart_node_name,\n" +
                        "       rc.assignee_title_name           as assignee_title_name,\n" +
                        "       rc.assignee_direct_manager       as assignee_direct_manager,\n" +
                        "       rc.assignee_status               as assignee_status,\n" +
                        "       rc.assignee_staff_code,\n" +
                        "       rc.created_user                  as created_user,\n" +
                        "       rc.created_user_full_name,\n" +
                        "       rc.created_user_chart_node_name  as created_user_chart_node_name,\n" +
                        "       rc.created_user_chart_short_name as created_user_chart_short_name,\n" +
                        "       rc.created_user_title_name       as created_user_title_name,\n" +
                        "       rc.is_expire                     AS is_expire,\n" +
                        "       rc.ticket_id                     as ticketId\n" +
                        "from report_by_chart_node_new rc join history h on rc.task_id = h.task_id and rc.version_time = h.version_time\n" +
                        "where rc.ticket_id = :ticketId order by rc.created_time asc";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);
        query.setParameter("ticketId", ticketId);
        query.setParameter("fromDate", fromDate);
        query.setParameter("toDate", newDate);
        List<Tuple> result = query.getResultList();
        List<DetailTaskDto> detailTaskDtos = new ArrayList<>();
        reportHelper.mapTupleToDetailTaskDto(result, detailTaskDtos);
        return detailTaskDtos;
    }


    private List<DetailReportByGroupDto> getListDetailReportByGroup(ReportProcInstFilter filter) {
        String mainQuery = " WITH canceled_tickets AS (SELECT bh.proc_inst_id AS proc_inst_id, bh.note AS ly_do_huy, bh.created_time AS thoi_gian_huy\n" +
                "                          FROM bpm_history bh\n" +
                "                          WHERE bh.action = 'CANCEL_TICKET'),\n" +
                " approval_returned as (select bh.proc_inst_id AS proc_inst_id,\n" +
                "                                  bh.note         AS ly_do_tra_ve,\n" +
                "                                  bh.created_time AS thoi_gian_tra_ve\n" +
                "                           FROM bpm_history bh\n" +
                "                           WHERE bh.action = 'REQUEST_UPDATE'), \n" +
                " history as (select v.ticket_id, MAX(version_time) as version_time\n" +
                "                 from (SELECT ticket_id, MAX(version_time) as version_time\n" +
                "                       FROM report_by_group_new\n" +
                "                       where version_time between :fromDate and :toDate\n" +
                "                       GROUP BY ticket_id\n" +
                "                       union all\n" +
                "                       SELECT ticket_id, MAX(version_time) as version_time\n" +
                "                       FROM report_by_group_new\n" +
                "                       where version_time < :fromDate\n" +
                "                       GROUP BY ticket_id) as v\n" +
                "                 group by ticket_id) \n" +
                " select d.* from ( select rp.proc_inst_id                                                                           as proc_inst_id,\n" +
                "       rp.title                                                                                    as proc_inst_name,\n" +
                "       rp.service_name,\n" +
                "       rp.ticket_id,\n" +
                "       rp.proc_inst_status                                                                         as proc_inst_status,\n" +
                "       rp.request_code                                                                             as request_code,\n" +
                "       TIMESTAMPDIFF(HOUR, rp.created_time,rp.sla_finish_time) AS elapsed_time,\n" +
                "       TIMESTAMPDIFF(HOUR,ifnull(rp.finished_time,current_timestamp), rp.sla_finish_time) AS delay_time,\n" +
                "       rp.priority_name                                                                            as priority,\n" +
                "       rp.created_user                                                                             AS created_user,\n" +
                "       rp.created_user_full_name,\n" +
                "       rp.chart_node_name,\n" +
                "       rp.chart_node_code,\n" +
                "       rp.chart_short_name,\n" +
                "       rp.created_time,\n" +
                "       rp.finished_time,\n" +
                "       rp.sla_finish_time,\n" +
                "       rp.user_title_name,\n" +
                "       rp.is_expire as is_expire,\n" +
                "       count_proc_inst_returned.pr                                          as so_lan_tra_ve,\n" +
                "       bhc.ly_do_huy                                                                               as ly_do_huy_phieu,\n" +
                "       bhc.thoi_gian_huy                                                                           as thoi_gian_huy_phieu, \n" +
                "       ar.ly_do_tra_ve                                                      as ly_do_tra_ve,\n" +
                "       ar.thoi_gian_tra_ve                                                  as thoi_gian_tra_ve \n" +
                "from report_by_group_new rp\n" +
                "         JOIN history h on rp.version_time = h.version_time and rp.ticket_id = h.ticket_id \n" +
                "         LEFT JOIN canceled_tickets bhc ON rp.proc_inst_id = bhc.proc_inst_id\n" +
                "         LEFT JOIN approval_returned ar on rp.proc_inst_id = ar.proc_inst_id\n" +
                "         LEFT JOIN (select proc_inst_id, count(proc_inst_id) as pr\n" +
                "                    from approval_returned \n" +
                "                    group by proc_inst_id) as count_proc_inst_returned \n" +
                "                   on rp.proc_inst_id = count_proc_inst_returned.proc_inst_id \n" +
                "WHERE rp.created_user in :defaultUser AND rp.chart_node_id in :defaultChartNodeId AND  ";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        reportHelper.buildDetailReportByGroupFilter(filter, stringBuilder);
        stringBuilder.append(" 1 = 1 ");

        stringBuilder.append(" LIMIT :page, :pageSize) as d \n ");
        reportHelper.buildDetailReportByGroupSort(filter, stringBuilder);

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);
        reportHelper.buildDetailReportByGroupParam(filter, query);
        query.setParameter("page", (filter.getPage() - 1) * filter.getPageSize());
        query.setParameter("pageSize", filter.getPageSize());

        List<Tuple> result = query.getResultList();
        List<DetailReportByGroupDto> detailReportByGroupDtos = new ArrayList<>();

        reportHelper.mapTupleToDetailReportByGroup(result, detailReportByGroupDtos);
        return detailReportByGroupDtos;
    }

    private BigInteger countDetailProcInstByGroup(ReportProcInstFilter filter) {
        String mainQuery = " select count(*) as count from ( WITH canceled_tickets AS (SELECT bh.proc_inst_id AS proc_inst_id, bh.note AS ly_do_huy, bh.created_time AS thoi_gian_huy\n" +
                "                          FROM bpm_history bh\n" +
                "                          WHERE bh.action = 'CANCEL_TICKET'), \n" +
                " approval_returned as (select bh.proc_inst_id AS proc_inst_id,\n" +
                "                                  bh.note         AS ly_do_tra_ve,\n" +
                "                                  bh.created_time AS thoi_gian_tra_ve\n" +
                "                           FROM bpm_history bh\n" +
                "                           WHERE bh.action = 'REQUEST_UPDATE'), \n" +
                " history as (select v.ticket_id, MAX(version_time) as version_time\n" +
                "                 from (SELECT ticket_id, MAX(version_time) as version_time\n" +
                "                       FROM report_by_group_new\n" +
                "                       where version_time between :fromDate and :toDate\n" +
                "                       GROUP BY ticket_id\n" +
                "                       union all\n" +
                "                       SELECT ticket_id, MAX(version_time) as version_time\n" +
                "                       FROM report_by_group_new\n" +
                "                       where version_time < :fromDate\n" +
                "                       GROUP BY ticket_id) as v\n" +
                "                 group by ticket_id) \n" +
                "  select rp.proc_inst_id                                                                           as proc_inst_id,\n" +
                "       rp.title                                                                                    as proc_inst_name,\n" +
                "       rp.service_name,\n" +
                "       rp.ticket_id,\n" +
                "       rp.proc_inst_status                                                                         as proc_inst_status,\n" +
                "       rp.request_code                                                                             as request_code,\n" +
                "       TIMESTAMPDIFF(HOUR, rp.created_time,rp.sla_finish_time) AS elapsed_time,\n" +
                "       TIMESTAMPDIFF(HOUR,ifnull(rp.finished_time,current_timestamp), rp.sla_finish_time) AS delay_time,\n" +
                "       rp.priority_name                                                                            as priority,\n" +
                "       rp.created_user                                                                             AS created_user,\n" +
                "       rp.created_user_full_name,\n" +
                "       rp.chart_node_name,\n" +
                "       rp.chart_node_code,\n" +
                "       rp.chart_short_name,\n" +
                "       rp.created_time,\n" +
                "       rp.finished_time,\n" +
                "       rp.sla_finish_time,\n" +
                "       rp.user_title_name,\n" +
                "       rp.is_expire as is_expire,\n" +
                "       count_proc_inst_returned.pr                                          as so_lan_tra_ve,\n" +
                "       bhc.ly_do_huy                                                                               as ly_do_huy_phieu,\n" +
                "       bhc.thoi_gian_huy                                                                           as thoi_gian_huy_phieu, \n" +
                "       ar.ly_do_tra_ve                                                      as ly_do_tra_ve,\n" +
                "       ar.thoi_gian_tra_ve                                                  as thoi_gian_tra_ve \n" +
                "from report_by_group_new rp\n" +
                "         JOIN history h on rp.version_time = h.version_time and rp.ticket_id = h.ticket_id \n" +
                "         LEFT JOIN canceled_tickets bhc ON rp.proc_inst_id = bhc.proc_inst_id\n" +
                "         LEFT JOIN approval_returned ar on rp.proc_inst_id = ar.proc_inst_id\n" +
                "         LEFT JOIN (select proc_inst_id, count(proc_inst_id) as pr\n" +
                "                    from approval_returned \n" +
                "                    group by proc_inst_id) as count_proc_inst_returned \n" +
                "                   on rp.proc_inst_id = count_proc_inst_returned.proc_inst_id \n" +
                "WHERE rp.created_user in :defaultUser AND rp.chart_node_id in :defaultChartNodeId AND ";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        reportHelper.buildDetailReportByGroupFilter(filter, stringBuilder);
        stringBuilder.append(" 1 = 1) as a ");

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);
        reportHelper.buildDetailReportByGroupParam(filter, query);

        List<Tuple> result = query.getResultList();
        Long count = (Long) result.get(0).get("count");
        return BigInteger.valueOf(count);
    }

    private List<DetailReportByGroupDto> getListDetailReportByChartNode(ReportProcInstFilter filter) throws ParseException {
        String mainQuery = " WITH returned_task AS (SELECT bh.task_inst_id AS task_inst_id,\n" +
                "                              bh.note         AS ly_do_tra_ve,\n" +
                "                              bh.created_time AS thoi_gian_tra_ve\n" +
                "                       FROM bpm_history bh\n" +
                "                       WHERE bh.action IN ('REQUEST_UPDATE', 'AFFECTED_BY_RU')), \n" +
                "  history as (select v.task_id, MAX(version_time) as version_time\n" +
                "                 from (SELECT task_id, MAX(version_time) as version_time\n" +
                "                       FROM report_by_chart_node_new\n" +
                "                       where version_time between :fromDate and :toDate\n" +
                "                       GROUP BY task_id\n" +
                "                       union all\n" +
                "                       SELECT task_id, MAX(version_time) as version_time\n" +
                "                       FROM report_by_chart_node_new\n" +
                "                       where version_time < :fromDate\n" +
                "                       GROUP BY task_id) as v\n" +
                "                 group by task_id) \n" +
                "select d.* from (select proc_inst_id                      as proc_inst_id,\n" +
                "       title                             as proc_inst_name,\n" +
                "       service_name,\n" +
                "       task_type                         as task_type,\n" +
                "       task_status                       as task_status,\n" +
                "       rc.task_id                           as task_id,\n" +
                "       TIMESTAMPDIFF(HOUR, rc.created_time,rc.sla_finish_time) AS elapsed_time,\n" +
                "       TIMESTAMPDIFF(HOUR,ifnull(rc.finished_time,current_timestamp), rc.sla_finish_time) AS delay_time,\n" +
                "       priority_name                     as priority,\n" +
                "       rc.request_code                   as request_code,\n" +
                "       count_task_returned.ctr           as so_lan_tra_ve,\n" +
                "       assignee                          AS assignee,\n" +
                "       rc.assignee_full_name             as assignee_full_name,\n" +
                "       rc.assignee_chart_short_name      as assignee_chart_short_name,\n" +
                "       rc.assignee_status                as assignee_status,\n" +
                "       rc.assignee_staff_code            as assignee_staff_code,\n" +
                "       rc.assignee_title_name            as assignee_title_name,\n" +
                "       rc.assignee_chart_node_id         as assignee_chart_node_id,\n" +
                "       rc.assignee_chart_node_name       as assignee_chart_node_name,\n" +
                "       rc.assignee_chart_node_code       as assignee_chart_node_code,\n" +
                "       rc.assignee_chart_id              as assignee_chart_id,\n" +
                "       rc.created_user                   as created_user,\n" +
                "       rc.created_user_chart_short_name  as created_user_chart_short_name,\n" +
                "       rc.created_user_full_name         as created_user_full_name,\n" +
                "       rc.created_user_title_name        as created_user_title_name,\n" +
                "       rc.created_user_chart_node_id     as created_user_chart_node_id,\n" +
                "       rc.created_user_chart_node_code   as created_user_chart_node_code,\n" +
                "       rc.created_user_chart_node_name   as created_user_chart_node_name,\n" +
                "       rc.created_user_chart_id          as created_user_chart_id,\n" +
                "       rc.task_name                      as taskName,\n" +
                "       rc.created_time                   as task_created_time,\n" +
                "       rc.sla_finish_time                as sla_finish_time,\n" +
                "       rc.finished_time                  as finished_time,\n" +
                "       rc.started_time                   as started_time,\n" +
                "       rc.cancel_reason                  as ly_do_huy,\n" +
                "       rc.is_expire                      as is_expire,\n" +
                "       rc.proc_def_id                    as proc_def_id,\n" +
                "       rc.ticket_id                      as ticket_id\n" +
                "from report_by_chart_node_new rc\n" +
                "         JOIN history h on rc.task_id = h.task_id and rc.version_time = h.version_time \n" +
                "         LEFT JOIN (select task_inst_id, count(task_inst_id) as ctr\n" +
                "                    from returned_task\n" +
                "                    group by task_inst_id) as count_task_returned\n" +
                "                   on rc.task_id = count_task_returned.task_inst_id" +
                " WHERE rc.assignee in :defaultUser AND rc.assignee_chart_node_id in :defaultChartNodeId AND ";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        reportHelper.buildDetailReportByChartNodeFilter(filter, stringBuilder);
        stringBuilder.append(" 1 = 1 \n ");

        stringBuilder.append("\n LIMIT :page, :pageSize) as d \n");
        reportHelper.buildDetailReportByChartNodeSort(filter, stringBuilder);

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);

        reportHelper.buildDetailReportByChartNodeParam(filter, query);

        query.setParameter("page", (filter.getPage() - 1) * filter.getPageSize());
        query.setParameter("pageSize", filter.getPageSize());

        List<Tuple> result = query.getResultList();
        List<DetailReportByGroupDto> detailReportByGroupDtos = new ArrayList<>();
        reportHelper.mapTupleToDetailReportByChartNode(result, detailReportByGroupDtos);
        return detailReportByGroupDtos;
    }


    private BigInteger countDetailProcInstByChartNode(ReportProcInstFilter filter) {
        String mainQuery = " (WITH returned_task AS (SELECT bh.task_inst_id AS task_inst_id,\n" +
                "                              bh.note         AS ly_do_tra_ve,\n" +
                "                              bh.created_time AS thoi_gian_tra_ve\n" +
                "                       FROM bpm_history bh\n" +
                "                       WHERE bh.action IN ('REQUEST_UPDATE', 'AFFECTED_BY_RU')), \n" +
                "  history as (select v.task_id, MAX(version_time) as version_time\n" +
                "                 from (SELECT task_id, MAX(version_time) as version_time\n" +
                "                       FROM report_by_chart_node_new\n" +
                "                       where version_time between :fromDate and :toDate\n" +
                "                       GROUP BY task_id\n" +
                "                       union all\n" +
                "                       SELECT task_id, MAX(version_time) as version_time\n" +
                "                       FROM report_by_chart_node_new\n" +
                "                       where version_time < :fromDate\n" +
                "                       GROUP BY task_id) as v\n" +
                "                 group by task_id) \n" +
                " select proc_inst_id                     as proc_inst_id,\n" +
                "       title                             as proc_inst_name,\n" +
                "       service_name,\n" +
                "       task_type                         as task_type,\n" +
                "       task_status                       as task_status,\n" +
                "       rc.task_id                           as task_id,\n" +
                "       TIMESTAMPDIFF(HOUR, rc.created_time,rc.sla_finish_time) AS elapsed_time,\n" +
                "       TIMESTAMPDIFF(HOUR,ifnull(rc.finished_time,current_timestamp), rc.sla_finish_time) AS delay_time,\n" +
                "       priority_name                     as priority,\n" +
                "       rc.request_code                   as request_code,\n" +
                "       count_task_returned.ctr           as so_lan_tra_ve,\n" +
                "       assignee                          AS assignee,\n" +
                "       rc.assignee_full_name             as assignee_full_name,\n" +
                "       rc.assignee_chart_short_name      as assignee_chart_short_name,\n" +
                "       rc.assignee_status                as assignee_status,\n" +
                "       rc.assignee_staff_code            as assignee_staff_code,\n" +
                "       rc.assignee_title_name            as assignee_title_name,\n" +
                "       rc.assignee_chart_node_id         as assignee_chart_node_id,\n" +
                "       rc.assignee_chart_node_name       as assignee_chart_node_name,\n" +
                "       rc.assignee_chart_node_code       as assignee_chart_node_code,\n" +
                "       rc.assignee_chart_id              as assignee_chart_id,\n" +
                "       rc.created_user                   as created_user,\n" +
                "       rc.created_user_chart_short_name  as created_user_chart_short_name,\n" +
                "       rc.created_user_full_name         as created_user_full_name,\n" +
                "       rc.created_user_title_name        as created_user_title_name,\n" +
                "       rc.created_user_chart_node_id     as created_user_chart_node_id,\n" +
                "       rc.created_user_chart_node_code   as created_user_chart_node_code,\n" +
                "       rc.created_user_chart_node_name   as created_user_chart_node_name,\n" +
                "       rc.created_user_chart_id          as created_user_chart_id,\n" +
                "       rc.task_name                      as taskName,\n" +
                "       rc.created_time                   as task_created_time,\n" +
                "       rc.sla_finish_time                as sla_finish_time,\n" +
                "       rc.finished_time                  as finished_time,\n" +
                "       rc.started_time                   as started_time,\n" +
                "       rc.cancel_reason                  as ly_do_huy,\n" +
                "       rc.is_expire                      as is_expire,\n" +
                "       rc.proc_def_id                    as proc_def_id,\n" +
                "       rc.ticket_id                      as ticket_id\n" +
                "from report_by_chart_node_new rc\n" +
                "         JOIN history h on rc.task_id = h.task_id and rc.version_time = h.version_time \n" +
                "         LEFT JOIN (select task_inst_id, count(task_inst_id) as ctr\n" +
                "                    from returned_task\n" +
                "                    group by task_inst_id) as count_task_returned\n" +
                "                   on rc.task_id = count_task_returned.task_inst_id" +
                " WHERE rc.assignee in :defaultUser AND rc.assignee_chart_node_id in :defaultChartNodeId AND ";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select count(*) as count FROM ").append(mainQuery);
        reportHelper.buildDetailReportByChartNodeFilter(filter, stringBuilder);
        stringBuilder.append(" 1 = 1 ) as a \n ");

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);
        reportHelper.buildDetailReportByChartNodeParam(filter, query);
        List<Tuple> result = query.getResultList();
        Long count = (Long) result.get(0).get("count");
        return BigInteger.valueOf(count);
    }

//    @NotNull
//    private List<DetailReportByGroupDto> getListDetailReportByUser(ReportProcInstFilter filter) throws ParseException {
//        Set<DetailReportByGroupDto> response = new HashSet<>();
//        List<DetailReportByGroupDto> detailReportByGroupDtos = getListDetailReportByChartNode(filter);
//        for (DetailReportByGroupDto e : detailReportByGroupDtos) {
//            ReportByGroup reportByGroup = reportByGroupRepository.findByTicketId(e.getTicketId());
//            DetailReportByGroupDto detailReportByGroupDto = new DetailReportByGroupDto();
//            detailReportByGroupDto.setTicketId(reportByGroup.getTicketId());
//            detailReportByGroupDto.setRequestCode(reportByGroup.getRequestCode());
//            detailReportByGroupDto.setProcInstId(reportByGroup.getProcInstId());
//            detailReportByGroupDto.setProcInstName(reportByGroup.getTitle());
//            detailReportByGroupDto.setServiceName(reportByGroup.getServiceName());
//            detailReportByGroupDto.setTaskStatus(reportByGroup.getProcInstStatus());
//            detailReportByGroupDto.setPriority(reportByGroup.getPriorityName());
//            detailReportByGroupDto.setCreatedUser(reportByGroup.getCreatedUser());
//            detailReportByGroupDto.setCreatedUserFullName(reportByGroup.getCreatedUserFullName());
//            detailReportByGroupDto.setCreatedUserChartNodeName(reportByGroup.getChartNodeName());
//            detailReportByGroupDto.setCreatedUserChartShortName(reportByGroup.getChartShortName());
//            detailReportByGroupDto.setCreatedUserTitleName(reportByGroup.getUserTitleName());
//            detailReportByGroupDto.setCreatedTime(reportByGroup.getCreatedTime());
//            detailReportByGroupDto.setFinishedTime(reportByGroup.getFinishedTime());
//            detailReportByGroupDto.setProcDefId(reportByGroup.getProcDefId());
//            detailReportByGroupDto.setIsExpire(reportByGroup.getIsExpire().toString());
//            Long elapsedTime = ChronoUnit.HOURS.between(reportByGroup.getCreatedTime(), LocalDateTime.now());
//            detailReportByGroupDto.setElapsedTime(elapsedTime);
//            LocalDateTime finishTime = reportByGroup.getFinishedTime();
//            finishTime = (finishTime == null) ? LocalDateTime.now() : finishTime;
//            LocalDateTime estimateTime = reportByGroup.getCreatedTime().plusHours(Math.round(reportByGroup.getSlaFinish()));
//            Long delayTime = ChronoUnit.HOURS.between(finishTime, estimateTime);
//            detailReportByGroupDto.setDelayTime(Double.valueOf(delayTime));
//            response.add(detailReportByGroupDto);
//        }
//        List<DetailReportByGroupDto> listResponse = new ArrayList<>(response);
//        return listResponse;
//    }

//    private Long countDetailReportByUser(ReportProcInstFilter filter) throws ParseException {
//        filter.setPageSize(999999999);
//        List<DetailReportByGroupDto> detailReportByGroupDtos = getListDetailReportByChartNode(filter);
//        return Long.valueOf(detailReportByGroupDtos.size());
//    }

    private List<DetailReportByGroupDto> getListDetailReportByUser(ReportProcInstFilter filter) {
        String mainQuery = " WITH canceled_tickets AS (SELECT bh.proc_inst_id AS proc_inst_id, bh.note AS ly_do_huy, bh.created_time AS thoi_gian_huy\n" +
                "                          FROM bpm_history bh\n" +
                "                          WHERE bh.action = 'CANCEL_TICKET'),\n" +
                " approval_returned as (select bh.proc_inst_id AS proc_inst_id,\n" +
                "                                  bh.note         AS ly_do_tra_ve,\n" +
                "                                  bh.created_time AS thoi_gian_tra_ve\n" +
                "                           FROM bpm_history bh\n" +
                "                           WHERE bh.action = 'REQUEST_UPDATE'), \n" +
                " history as (select v.ticket_id, MAX(version_time) as version_time\n" +
                "                 from (SELECT ticket_id, MAX(version_time) as version_time\n" +
                "                       FROM report_by_group_new\n" +
                "                       where version_time between :fromDate and :toDate\n" +
                "                       GROUP BY ticket_id\n" +
                "                       union all\n" +
                "                       SELECT ticket_id, MAX(version_time) as version_time\n" +
                "                       FROM report_by_group_new\n" +
                "                       where version_time < :fromDate\n" +
                "                       GROUP BY ticket_id) as v\n" +
                "                 group by ticket_id) \n" +
                " select d.* from (" +
                " select rp.proc_inst_id                                                                           as proc_inst_id,\n" +
                "       rp.title                                                                                    as proc_inst_name,\n" +
                "       rp.service_name,\n" +
                "       rp.ticket_id,\n" +
                "       rp.proc_inst_status                                                                         as proc_inst_status,\n" +
                "       rp.request_code                                                                             as request_code,\n" +
                "       TIMESTAMPDIFF(HOUR, rp.created_time,rp.sla_finish_time) AS elapsed_time,\n" +
                "       TIMESTAMPDIFF(HOUR,ifnull(rp.finished_time,current_timestamp), rp.sla_finish_time) AS delay_time,\n" +
                "       rp.priority_name                                                                            as priority,\n" +
                "       rp.created_user                                                                             AS created_user,\n" +
                "       rp.sla_finish_time                                                                          AS sla_finish_time,\n" +
                "       rp.created_user_full_name,\n" +
                "       rp.chart_node_name,\n" +
                "       rp.chart_node_code,\n" +
                "       rp.chart_short_name,\n" +
                "       rp.created_time,\n" +
                "       rp.finished_time,\n" +
                "       rp.user_title_name,\n" +
                "       rp.is_expire as is_expire,\n" +
                "       count_proc_inst_returned.pr                                          as so_lan_tra_ve,\n" +
                "       bhc.ly_do_huy                                                                               as ly_do_huy_phieu,\n" +
                "       bhc.thoi_gian_huy                                                                           as thoi_gian_huy_phieu, \n" +
                "       ar.ly_do_tra_ve                                                      as ly_do_tra_ve,\n" +
                "       ar.thoi_gian_tra_ve                                                  as thoi_gian_tra_ve \n" +
                "      FROM report_by_group_new rp " +
                "         JOIN history h on rp.ticket_id = h.ticket_id and rp.version_time = h.version_time \n" +
                "         LEFT JOIN canceled_tickets bhc ON rp.proc_inst_id = bhc.proc_inst_id\n" +
                "         LEFT JOIN approval_returned ar on rp.proc_inst_id = ar.proc_inst_id\n" +
                "         LEFT JOIN (select proc_inst_id, count(proc_inst_id) as pr\n" +
                "                    from approval_returned \n" +
                "                    group by proc_inst_id) as count_proc_inst_returned \n" +
                "                   on rp.proc_inst_id = count_proc_inst_returned.proc_inst_id \n" +
                "WHERE ";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        reportHelper.buildDetailReportByUserFilter(filter, stringBuilder);
        stringBuilder.append(" 1 = 1 ");

        stringBuilder.append(" LIMIT :page, :pageSize) as d \n ");
        reportHelper.buildDetailReportByGroupSort(filter, stringBuilder);

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);
        reportHelper.buildDetailReportByUserParam(filter, query);
        query.setParameter("page", (filter.getPage() - 1) * filter.getPageSize());
        query.setParameter("pageSize", filter.getPageSize());

        List<Tuple> result = query.getResultList();
        List<DetailReportByGroupDto> detailReportByGroupDtos = new ArrayList<>();

        reportHelper.mapTupleToDetailReportByGroup(result, detailReportByGroupDtos);
        return detailReportByGroupDtos;
    }

    private BigInteger countDetailProcInstByUser(ReportProcInstFilter filter) {
        String mainQuery = " select count(*) as count from (" +
                " WITH canceled_tickets AS (SELECT bh.proc_inst_id AS proc_inst_id, bh.note AS ly_do_huy, bh.created_time AS thoi_gian_huy\n" +
                "                          FROM bpm_history bh\n" +
                "                          WHERE bh.action = 'CANCEL_TICKET'), \n" +
                " history as (select v.ticket_id, MAX(version_time) as version_time\n" +
                "                 from (SELECT ticket_id, MAX(version_time) as version_time\n" +
                "                       FROM report_by_group_new\n" +
                "                       where version_time between :fromDate and :toDate\n" +
                "                       GROUP BY ticket_id\n" +
                "                       union all\n" +
                "                       SELECT ticket_id, MAX(version_time) as version_time\n" +
                "                       FROM report_by_group_new\n" +
                "                       where version_time < :fromDate\n" +
                "                       GROUP BY ticket_id) as v\n" +
                "                 group by ticket_id) \n" +
                "select rp.proc_inst_id                                                                             as proc_inst_id,\n" +
                "       rp.title                                                                                    as proc_inst_name,\n" +
                "       rp.service_name,\n" +
                "       rp.proc_inst_status                                                                         as proc_inst_status,\n" +
                "       rp.request_code                                                                             as request_code,\n" +
                "       TIMESTAMPDIFF(HOUR, rp.created_time,rp.sla_finish_time) AS elapsed_time,\n" +
                "       TIMESTAMPDIFF(HOUR,ifnull(rp.finished_time,current_timestamp), rp.sla_finish_time) AS delay_time,\n" +
                "       rp.priority_name                                                                            as priority,\n" +
                "       rp.created_user                                                                             AS created_user,\n" +
                "       rp.created_user_full_name,\n" +
                "       rp.chart_node_name,\n" +
                "       rp.chart_node_code,\n" +
                "       rp.chart_short_name,\n" +
                "       rp.user_title_name,\n" +
                "       count_proc_inst_cancel.pc                                                                   as so_lan_huy_phieu,\n" +
                "       bhc.ly_do_huy                                                                               as ly_do_huy_phieu,\n" +
                "       bhc.thoi_gian_huy                                                                           as thoi_gian_huy_phieu\n" +
                "from report_by_group_new rp\n" +
                "         JOIN history h ON rp.ticket_id = h.ticket_id and rp.version_time = h.version_time \n" +
                "         LEFT JOIN canceled_tickets bhc ON rp.proc_inst_id = bhc.proc_inst_id\n" +
                "         LEFT JOIN (select proc_inst_id, count(proc_inst_id) as pc\n" +
                "                    from canceled_tickets\n" +
                "                    group by proc_inst_id) as count_proc_inst_cancel\n" +
                "                   on rp.proc_inst_id = count_proc_inst_cancel.proc_inst_id \n" +
                "WHERE ";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        reportHelper.buildDetailReportByUserFilter(filter, stringBuilder);
        stringBuilder.append(" 1 = 1) as a ");

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);
        reportHelper.buildDetailReportByUserParam(filter, query);

        List<Tuple> result = query.getResultList();
        Long count = (Long) result.get(0).get("count");
        return BigInteger.valueOf(count);
    }
}