package vn.fis.eapprove.business.tenant;


import vn.fis.eapprove.business.dto.filter.ReportProcInstFilter;
import vn.fis.eapprove.business.dto.filter.RequestDetailTaskReturned;
import vn.fis.eapprove.business.dto.report.DetailTaskDto;
import vn.fis.eapprove.business.dto.report.DetailTaskReturnedDto;
import vn.fis.eapprove.business.dto.report.DetailTicketCancelResponse;
import vn.fis.eapprove.business.model.response.DetailReportByGroupResponse;

import java.util.List;

public interface DetailReportService {

    DetailReportByGroupResponse getDetailReportByChartNode(ReportProcInstFilter filter, String username);

    DetailReportByGroupResponse getDetailReportByUser(ReportProcInstFilter filter, String username);

    DetailReportByGroupResponse getDetailReportByGroup(ReportProcInstFilter filter, String username);

    List<DetailTaskDto> getDetailTaskByProcInstId(Long ticketId);

    List<DetailTaskReturnedDto> getDetailTaskReturned(RequestDetailTaskReturned requestDetailTaskReturned);

    DetailTicketCancelResponse getDetailTicketCancel(String procInstId);

}
