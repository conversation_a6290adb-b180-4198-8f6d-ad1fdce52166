package vn.fis.eapprove.business.tenant.manager;

import java.util.Queue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * Author: PhucVM
 * Date: 10/08/2022
 */
public class QueueManager {

    public static Queue<Object> QUEUE_LOG_CALL_API = null;

    public static void initQueueLogCallApi() {
        if (QUEUE_LOG_CALL_API == null) {
            QUEUE_LOG_CALL_API = new LinkedBlockingQueue<>();
        }
    }
}
