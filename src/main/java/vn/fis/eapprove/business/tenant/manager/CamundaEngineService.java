package vn.fis.eapprove.business.tenant.manager;

import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import vn.fis.eapprove.business.model.request.CompleteTaskDto;
import vn.fis.eapprove.business.model.request.StartProcessInstanceDto;
import vn.fis.eapprove.business.model.request.WorkFlowRequest;
import vn.fis.spro.common.camunda.IdentityLink;
import vn.fis.spro.common.camunda.SequenceFlowData;
import vn.fis.spro.common.camunda.SproFlow;
import vn.fis.spro.common.camunda.VariableModification;

import java.util.List;
import java.util.Map;

/**
 * Author: PhucVM
 * Date: 27/08/2022
 */
public interface CamundaEngineService {

    /**
     * <b>GET /engine-rest/process-definition/{id}/xml</b>
     *
     * @param procDefId The id of the process definition
     * @return A string of BPMN 2.0 XML
     */
    String getXML(String procDefId);

    /**
     * <b>GET /engine-rest/process-definition/{id}/xml</b>
     *
     * @param procDefId The id of the process definition
     * @return Response entity of BPMN 2.0 XML
     */
    ResponseEntity<Map<String, String>> getXMLEntity(String procDefId);

    /**
     * <b>GET /engine-rest/process-definition/{id}/xml</b>
     *
     * @param procDefId The id of the process definition
     * @return BPMN model instance
     */
    BpmnModelInstance getModelInstance(String procDefId);

    /**
     * <b>GET /engine-rest/task/{id}/identity-links</b>
     *
     * @param taskId The id of the task
     * @return List of identity links
     */
    List<IdentityLink> getIdentityLinks(String taskId);

    /**
     * <b>POST /task/{id}/identity-links/delete</b>
     *
     * @param taskId The id of the task
     * @return This method returns no content.
     */
    void deleteIdentityLink(String taskId, String userId, String groupId, String type);

    /**
     * <b>POST /task/{id}/identity-links</b>
     *
     * @param taskId The id of the task
     * @return This method returns no content.
     */
    void addIdentityLink(String taskId, String userId, String groupId, String type);

    /**
     * <b>POST /bpmn/work-flow</b>
     *
     * @param request Request object
     * @return List of SPRO flow
     */
    List<SproFlow> getSproFlows(WorkFlowRequest request);
    List<SproFlow> getAllSproFlows(WorkFlowRequest request);

    /**
     * <b>GET /engine-rest/variable-instance</b>
     *
     * @param processInstanceIdIn Only include variable instances which belong to one of the passed and comma-separated process instance ids
     * @return A JSON array of variable instance objects
     */
    String getVariableInstances(String processInstanceIdIn);

    /**
     * <b>POST /engine-rest/deployment/create</b><br>
     * A multipart form submit
     *
     * @param resource File's resource
     * @return A JSON object corresponding to the DeploymentWithDefinitions interface in the engine
     */
    Map<String, Object> createDeployment(Resource resource) throws Exception;

    /**
     * <b>POST /actHiVarInst/getByType/{procInstId}</b>
     *
     * @param procInstId The id of the process instance.
     * @param types      List of data types.
     * @return A list of map String to Object response data.
     */
    List<Map<String, Object>> getVariablesByTypes(String procInstId, List<String> types);

    /**
     * <b>POST /actHiVarInst/getByName/{procInstId}/name</b>
     *
     * @param procInstId The id of the process instance.
     * @param names      List of variable names.
     * @return A list of map String to Object response data.
     */
    List<Map<String, Object>> getVariablesByNames(String procInstId, List<String> names);

    /**
     * <b>GET /engine-rest/process-definition</b>
     *
     * @param deploymentId Filter by the deployment the id belongs to.
     * @return A JSON array of process definition objects.
     */
    List<Map<String, Object>> getProcessDefinitionByDeploymentId(String deploymentId);

    /**
     * <b>POST /engine-rest/process-definition/{procDefId}/submit-form</b><br>
     * Starts a process instance using a set of process variables and the business key.
     *
     * @param procDefId The id of the process definition to submit the form for.
     * @param request   A JSON object.
     * @return A JSON object corresponding to the ProcessInstance interface in the engine.
     */
    Map<String, Object> submitForm(String procDefId, StartProcessInstanceDto request) throws Exception;

    /**
     * <b>GET /engine-rest/task/{id}</b>
     *
     * @param id The id of the task to be retrieved.
     * @return A JSON object corresponding to the Task interface in the engine.
     */
    Map<String, Object> getTaskById(String id);

    /**
     * <b>POST /engine-rest/task/{id}/complete</b><br>
     * Completes a task and updates process variables.
     *
     * @param taskId    The id of the task to complete.
     * @param variables A JSON object containing variable key-value pairs. Each key is a variable name and each value a JSON variable value object.
     * @return This method returns no content.
     */
    void completeTask(String taskId, CompleteTaskDto variables) throws Exception;

    /**
     * <b>POST /engine-rest/task/{id}/claim</b><br>
     * Claims a task for a specific user.
     *
     * @param taskId The id of the task to claim.
     * @param userId The id of the user that claims the task. If userId is empty the task is unclaimed.
     * @return This method returns no content.
     */
    void claimTask(String taskId, String userId);

    /**
     * <b>POST /engine-rest/task/{id}/assignee</b><br>
     * Changes the assignee of a task to a specific user.
     *
     * @param taskId The id of the task to set the assignee for.
     * @param userId The id of the user that will be the assignee of the task. If userId is empty the task is unassigned.
     * @return This method returns no content.
     */
    void setTaskAssignee(String taskId, String userId);

    /**
     * <b>GET /actRuTask/ByProcessInstId/{procInstId}</b>
     * Get current runtime tasks
     *
     * @param procInstId The id of the process instance.
     * @return A list of tasks.
     */
    List<Map<String, Object>> getCurrentRuntimeTasks(String procInstId);

    /**
     * <b>POST /engine-rest/process-instance/{id}/modification</b><br>
     * Submits a list of modification instructions to change a process instance's execution state.
     *
     * @param id   The id of the process instance to modify.
     * @param body A JSON object.
     * @return This method returns no content.
     */
    void modifyProcessInstance(String id, Map<String, Object> body);

    /**
     * <b>DELETE /engine-rest/process-instance/{id}</b><br>
     * Deletes a running process instance by id.
     *
     * @param id The id of the process instance to modify.
     * @return This method returns no content.
     */
    void deleteProcessInstance(String id);

    /**
     * <b>POST /engine-rest/process-instance/{id}/variables</b><br>
     * Updates or deletes the variables of a process instance by id.
     *
     * @param id         The id of the process instance to set variables for.
     * @param updateData Modification data.
     * @return This method returns no content.
     */
    void updateProcessVariables(String id, VariableModification updateData);

    /**
     * <b>POST /engine-rest/task/{id}/variables</b><br>
     * Updates or deletes the variables visible from the task.
     *
     * @param id         The id of the task to set variables for.
     * @param updateData Modification data.
     * @return This method returns no content.
     */
    void updateTaskVariables(String id, VariableModification updateData);

    /**
     * <b>GET /actHiProcInst/actHiProcInstById/{procInstId}</b>
     *
     * @param procInstId The id of the process instance.
     * @return A JSON object.
     */
    Map<String, Object> getHiProcInstById(String procInstId);

    /**
     * <b>GET /bpmn/calc-proc-inst-expressions?procDefId={procDefId}&procInstId={procInstId}</b>
     *
     * @param procDefId  The id of the process definition to calculate.
     * @param procInstId The id of the process instance.
     * @return A map of evaluated expressions value
     */
    Map<String, SequenceFlowData> calcProcInstExpressions(String procDefId, String procInstId);

    HttpHeaders getHttpHeadersMultiPartForm();

//    HttpHeaders getHttpHeadersWithToken();
}
