package vn.fis.eapprove.business.tenant.manager.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.constant.Constant;
import vn.fis.eapprove.business.dto.MDResponseDto;
import vn.fis.eapprove.business.model.request.MasterDataImportRequest;
import vn.fis.eapprove.business.tenant.manager.MasterDataService;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.MapKeyEnum;
import vn.fis.spro.common.helper.RestHelper;

import jakarta.annotation.PostConstruct;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class MasterDataServiceImpl implements MasterDataService {

    private final SproProperties sproProperties;
    private final RestHelper restHelper;
    private String mdServiceHost;
    @Value("${app.superAdmin.username}")
    private String usernameBasicAuth;
    @Value("${app.superAdmin.password}")
    private String password;
    @Value("${app.superAdmin.realm}")
    private String realm;

    private final CredentialHelper credentialHelper;

    @Autowired
    public MasterDataServiceImpl(SproProperties sproProperties,
                                 RestHelper restHelper, CredentialHelper credentialHelper) {
        this.sproProperties = sproProperties;
        this.restHelper = restHelper;
        this.credentialHelper = credentialHelper;
    }

    @PostConstruct
    public void init() {
        mdServiceHost = sproProperties.getServiceUrls().get(MapKeyEnum.MD_SERVICE.key);
        if (mdServiceHost != null && mdServiceHost.endsWith(CommonConstants.PATH_SEPARATOR)) {
            mdServiceHost = mdServiceHost.substring(0, mdServiceHost.length() - 1);
        }
    }

    private long getEndTimeInMs(long startTime) {
        return System.currentTimeMillis() - startTime;
    }

    @Override
    public HttpHeaders getHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("key_role", Constant.KEY_ROLE_PUBLIC);

        try {
            //headers.set("realm", credentialHelper.getRealm());
            String token = credentialHelper.getJWTToken();
            if (!ValidationUtils.isNullOrEmpty(token)) {
                headers.setBearerAuth(token);
            }
        } catch (Exception e) {
            //headers.set("realm", realm);
            headers.setBasicAuth(usernameBasicAuth, password);
        }
        return headers;
    }

    private String getFullUrl(String uri) {
        return mdServiceHost + uri;
    }

    public List<Map<String, Object>> getLoadTemplateLocation(Long locationId) {
        long startTime = System.currentTimeMillis();

        MasterDataImportRequest request = new MasterDataImportRequest();
        request.setMasterDataId(999L);
        Map<String, Object> filter = new HashMap<>();
        filter.put("condition", "and");
        filter.put("begin", "code");
        filter.put("operator", "=");
        filter.put("end", locationId.toString());
        List<Map<String, Object>> lstFilter = new ArrayList<>();
        lstFilter.add(filter);
        request.setConditionFilter(lstFilter);

        String uri = "/masterData/getLoadTemplate";

        try {
            // call service
            MDResponseDto<List<Map<String, Object>>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    request,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished getLoadTemplateLocation {} ms", getEndTimeInMs(startTime));
        }

        return null;
    };

    public List<Map<String, Object>> getListApprovalSession() {
        long startTime = System.currentTimeMillis();

        MasterDataImportRequest request = new MasterDataImportRequest();
        request.setMasterDataId(998L);

        String uri = "/masterData/getLoadTemplate";

        try {
            MDResponseDto<List<Map<String, Object>>> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    getHeaders(),
                    request,
                    new ParameterizedTypeReference<>() {
                    });

            if (responseData != null) {
                return responseData.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished getLoadTemplateLocation {} ms", getEndTimeInMs(startTime));
        }

        return null;
    };
}
