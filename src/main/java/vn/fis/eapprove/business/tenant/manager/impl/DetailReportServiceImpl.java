package vn.fis.eapprove.business.tenant.manager.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import vn.fis.eapprove.business.domain.bpm.repository.BpmHistoryRepository;
import vn.fis.eapprove.business.dto.filter.ReportProcInstFilter;
import vn.fis.eapprove.business.dto.filter.RequestDetailTaskReturned;
import vn.fis.eapprove.business.dto.report.*;
import vn.fis.eapprove.business.model.response.DetailReportByGroupResponse;
import vn.fis.eapprove.business.model.response.UserInfoByUsername;
import vn.fis.eapprove.business.tenant.DetailReportService;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.utils.ReportHelper;
import vn.fis.eapprove.business.utils.TimeUtils;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.Tuple;
import java.math.BigInteger;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class DetailReportServiceImpl implements DetailReportService {

    private final EntityManager entityManager;

    private final ReportHelper reportHelper;

    private final BpmHistoryRepository bpmHistoryRepository;

    private final CustomerService customerService;

    @Override
    public DetailReportByGroupResponse getDetailReportByChartNode(ReportProcInstFilter filter,String username) {
        String status = null;
        if (!ValidationUtils.isNullOrEmpty(filter.getUserStatus()) && filter.getUserStatus().size() == 1) {
            status = filter.getUserStatus().get(0);
        }
        Set<String> users = new HashSet<>(customerService.getUserDefault(username,status));
        users.add(username);
        filter.getDefaultUser().addAll(users);
        if(filter.getUserStatus().size() == 1 && filter.getUserStatus().get(0).equalsIgnoreCase("inactive")){
            filter.getDefaultUser().remove(username);
        }
        List<Long> chartNodes = customerService.getChartNodeIdsByUsernames(filter.getDefaultUser());
        filter.setDefaultChartNodeId(chartNodes);

        DetailReportByGroupResponse detailReportByGroupResponse = new DetailReportByGroupResponse();
        try {
            List<DetailReportByGroupDto> detailReportByGroupDtos = getListDetailReportByChartNode(filter);
            detailReportByGroupResponse.setList(detailReportByGroupDtos);
            detailReportByGroupResponse.setTotal(countDetailProcInstByChartNode(filter).longValue());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return detailReportByGroupResponse;
    }

    @Override
    public DetailReportByGroupResponse getDetailReportByUser(ReportProcInstFilter filter,String username) {
        String status = null;
        if (!ValidationUtils.isNullOrEmpty(filter.getUserStatus()) && filter.getUserStatus().size() == 1) {
            status = filter.getUserStatus().get(0);
        }
        Set<String> users = new HashSet<>(customerService.getUserDefault(username,status));
        users.add(username);
        filter.getDefaultUser().addAll(users);
        if(filter.getUserStatus().size() == 1 && filter.getUserStatus().get(0).equalsIgnoreCase("inactive")){
            filter.getDefaultUser().remove(username);
        }
        List<Long> chartNodes = customerService.getChartNodeIdsByUsernames(filter.getDefaultUser());
        filter.setDefaultChartNodeId(chartNodes);

        DetailReportByGroupResponse detailReportByGroupResponse = new DetailReportByGroupResponse();
        List<DetailReportByGroupDto> detailReportByGroupDtos = getListDetailReportByUser(filter);
        detailReportByGroupResponse.setList(detailReportByGroupDtos);
        detailReportByGroupResponse.setTotal(countDetailProcInstByUser(filter).longValue());
        return detailReportByGroupResponse;
    }


    @Override
    public DetailReportByGroupResponse getDetailReportByGroup(ReportProcInstFilter filter,String username) {
        String status = null;
        if (!ValidationUtils.isNullOrEmpty(filter.getUserStatus()) && filter.getUserStatus().size() == 1) {
            status = filter.getUserStatus().get(0);
        }
        Set<String> users = new HashSet<>(customerService.getUserDefault(username,status));
        users.add(username);
        filter.getDefaultUser().addAll(users);
        if(filter.getUserStatus().size() == 1 && filter.getUserStatus().get(0).equalsIgnoreCase("inactive")){
            filter.getDefaultUser().remove(username);
        }
        List<Long> chartNodes = customerService.getChartNodeIdsByUsernames(filter.getDefaultUser());
        filter.setDefaultChartNodeId(chartNodes);

        DetailReportByGroupResponse detailReportByGroupResponse = new DetailReportByGroupResponse();

        List<DetailReportByGroupDto> detailReportByGroupDtos = getListDetailReportByGroup(filter);
        detailReportByGroupResponse.setList(detailReportByGroupDtos);
        detailReportByGroupResponse.setTotal(countDetailProcInstByGroup(filter).longValue());
        return detailReportByGroupResponse;
    }

    @Override
    public List<DetailTaskDto> getDetailTaskByProcInstId(Long ticketId) {
        return getListDetailTask(ticketId);
    }

    @Override
    public List<DetailTaskReturnedDto> getDetailTaskReturned(RequestDetailTaskReturned request) {
        String mainQuery = "select bh.task_inst_id as task_inst_id, bh.note as ly_do_tra_ve, bh.created_time as thoi_gian_tra_ve,bh.action_user as nguoi_tra_ve,rp.created_user as nguoi_bi_tra_ve " +
                "from bpm_history bh " +
                "join report_by_group rp on bh.ticket_id = rp.ticket_id " +
                "where bh.action IN ('REQUEST_UPDATE','AFFECTED_BY_RU') AND  ";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        if (!ObjectUtils.isEmpty(request.getTaskInstId())) {
            stringBuilder.append(" bh.task_inst_id = :taskInstId AND ");
        }
        if (!ObjectUtils.isEmpty(request.getProcInstId())) {
            stringBuilder.append(" bh.proc_inst_id = :procInstId AND ");
        }
        stringBuilder.append(" 1 = 1   " +
                " order by bh.created_time asc ");
        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);

        if (!ObjectUtils.isEmpty(request.getTaskInstId())) {
            query.setParameter("taskInstId", request.getTaskInstId());
        }
        if (!ObjectUtils.isEmpty(request.getProcInstId())) {
            query.setParameter("procInstId", request.getProcInstId());
        }
        List<Tuple> result = query.getResultList();
        List<DetailTaskReturnedDto> detailTaskReturnedDtos = new ArrayList<>();

        result.forEach(tuple -> {
            String taskInstId = tuple.get("task_inst_id") == null ? null : String.valueOf(tuple.get("task_inst_id"));
            String reasonReturned = tuple.get("ly_do_tra_ve") == null ? null : String.valueOf(tuple.get("ly_do_tra_ve"));
            String actionUser = tuple.get("nguoi_tra_ve") == null ? null : String.valueOf(tuple.get("nguoi_tra_ve"));
            String createdUser = tuple.get("nguoi_bi_tra_ve") == null ? null : String.valueOf(tuple.get("nguoi_bi_tra_ve"));

            DetailTaskReturnedDto detailTaskReturnedDto = new DetailTaskReturnedDto();
            LocalDateTime timeReturned = tuple.get("thoi_gian_tra_ve") == null ? null : TimeUtils.stringToLocalDateTime(tuple.get("thoi_gian_tra_ve").toString(), TimeUtils.FORMAT_DATE_DD_MM_YYYY);
            detailTaskReturnedDto.setTimeReturned(timeReturned);
            detailTaskReturnedDto.setReasonReturned(reasonReturned);
            detailTaskReturnedDto.setTaskInstId(taskInstId);
            detailTaskReturnedDto.setReturner(actionUser);
            detailTaskReturnedDto.setReturnee(createdUser);
            UserInfoByUsername returnerInfo = customerService.getUserInfoByUsername(actionUser);
            UserInfoByUsername returneeInfo = customerService.getUserInfoByUsername(createdUser);
            detailTaskReturnedDto.setReturnerInfo(returnerInfo);
            detailTaskReturnedDto.setReturneeInfo(returneeInfo);
            detailTaskReturnedDtos.add(detailTaskReturnedDto);
        });
        return detailTaskReturnedDtos;
    }

    @Override
    public DetailTicketCancelResponse getDetailTicketCancel(String procInstId) {

        DetailTicketCancelResponse response = new DetailTicketCancelResponse();

        DetailTicketCancelProjection detailTicketCancelProjection = bpmHistoryRepository.getDetailTicketCancel(procInstId);

        UserInfoByUsername assigneeInfoResponse = customerService.getUserInfoByUsername(detailTicketCancelProjection.getCancelUser());

        response.setCancelReason(detailTicketCancelProjection.getCancelReason());
        response.setCreatedTime(detailTicketCancelProjection.getCreatedTime());
        response.setProcInstId(detailTicketCancelProjection.getProcInstId());
        response.setCancelUserInfo(assigneeInfoResponse);

        return response;
    }

    private List<DetailTaskDto> getListDetailTask(Long ticketId) {
        String mainQuery = " select rc.task_id            as task_id, " +
                "       rc.task_type                     as type, " +
                "       rc.task_name                     as name, " +
                "       rc.created_time, " +
                "       rc.started_time, " +
                "       rc.task_status                   as status, " +
                "       rc.proc_inst_id, " +
                "       rc.proc_def_id, " +
                "       rc.priority_name as priority, " +
                "       rc.request_code , " +
                "       rc.assignee_full_name, " +
                "       rc.assignee_chart_short_name     as assignee_chart_short_name, " +
                "       rc.assignee                      as assignee, " +
                "       TIMESTAMPDIFF(HOUR, rc.created_time,rc.sla_finish_time) AS elapsed_time, " +
                "       TIMESTAMPDIFF(HOUR,ifnull(rc.finished_time,current_timestamp), rc.sla_finish_time) AS delay_time, " +
                "       rc.sla_finish_time                as sla_finish_time, " +
                "       rc.finished_time                  as finished_time, " +
                "       rc.assignee_chart_node_name      as assignee_chart_node_name, " +
                "       rc.assignee_title_name           as assignee_title_name, " +
                "       rc.assignee_direct_manager       as assignee_direct_manager, " +
                "       rc.assignee_status               as assignee_status, " +
                "       rc.assignee_staff_code, " +
                "       rc.created_user                  as created_user, " +
                "       rc.created_user_full_name, " +
                "       rc.created_user_chart_node_name  as created_user_chart_node_name, " +
                "       rc.created_user_chart_short_name as created_user_chart_short_name, " +
                "       rc.created_user_title_name       as created_user_title_name, " +
                "       rc.is_expire                     AS is_expire, " +
                "       rc.ticket_id                     as ticketId " +
                "from report_by_chart_node rc " +
                "where rc.ticket_id = :ticketId order by rc.created_time asc";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);
        query.setParameter("ticketId", ticketId);
        List<Tuple> result = query.getResultList();
        List<DetailTaskDto> detailTaskDtos = new ArrayList<>();
        reportHelper.mapTupleToDetailTaskDto(result, detailTaskDtos);
        return detailTaskDtos;
    }


    private List<DetailReportByGroupDto> getListDetailReportByGroup(ReportProcInstFilter filter) {
        String mainQuery = " WITH canceled_tickets AS (SELECT bh.proc_inst_id AS proc_inst_id, bh.note AS ly_do_huy, bh.created_time AS thoi_gian_huy " +
                "                          FROM bpm_history bh " +
                "                          WHERE bh.action = 'CANCEL_TICKET'), " +
                " approval_returned as (select bh.proc_inst_id AS proc_inst_id, " +
                "                                  bh.note         AS ly_do_tra_ve, " +
                "                                  bh.created_time AS thoi_gian_tra_ve " +
                "                           FROM bpm_history bh " +
                "                           WHERE bh.action = 'REQUEST_UPDATE')  " +
                " select d.* from ( select rp.proc_inst_id                                                                           as proc_inst_id, " +
                "       rp.title                                                                                    as proc_inst_name, " +
                "       rp.service_name, " +
                "       rp.ticket_id, " +
                "       rp.proc_inst_status                                                                         as proc_inst_status, " +
                "       rp.request_code                                                                             as request_code, " +
                "       TIMESTAMPDIFF(HOUR, rp.created_time,rp.sla_finish_time) AS elapsed_time, " +
                "       TIMESTAMPDIFF(HOUR,ifnull(rp.finished_time,current_timestamp), rp.sla_finish_time) AS delay_time, " +
                "       rp.priority_name                                                                            as priority, " +
                "       rp.created_user                                                                             AS created_user, " +
                "       rp.created_user_full_name, " +
                "       rp.chart_node_name, " +
                "       rp.chart_node_code, " +
                "       rp.chart_short_name, " +
                "       rp.created_time, " +
                "       rp.finished_time, " +
                "       rp.sla_finish_time, " +
                "       rp.user_title_name, " +
                "       rp.is_expire as is_expire, " +
                "       count_proc_inst_returned.pr                                          as so_lan_tra_ve, " +
                "       bhc.ly_do_huy                                                                               as ly_do_huy_phieu, " +
                "       bhc.thoi_gian_huy                                                                           as thoi_gian_huy_phieu,  " +
                "       ar.ly_do_tra_ve                                                      as ly_do_tra_ve, " +
                "       ar.thoi_gian_tra_ve                                                  as thoi_gian_tra_ve  " +
                "from report_by_group rp " +
                "         LEFT JOIN canceled_tickets bhc ON rp.proc_inst_id = bhc.proc_inst_id " +
                "         LEFT JOIN approval_returned ar on rp.proc_inst_id = ar.proc_inst_id " +
                "         LEFT JOIN (select proc_inst_id, count(proc_inst_id) as pr " +
                "                    from approval_returned  " +
                "                    group by proc_inst_id) as count_proc_inst_returned  " +
                "                   on rp.proc_inst_id = count_proc_inst_returned.proc_inst_id  " +
                "WHERE rp.created_user in :defaultUser AND rp.chart_node_id in :defaultChartNodeId AND  ";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        reportHelper.buildDetailReportByGroupFilter(filter, stringBuilder);
        stringBuilder.append(" 1 = 1 ");

        stringBuilder.append(" LIMIT :page, :pageSize) as d   ");
        reportHelper.buildDetailReportByGroupSort(filter, stringBuilder);

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);
        reportHelper.buildDetailReportByGroupParam(filter, query);
        query.setParameter("page", (filter.getPage() - 1) * filter.getPageSize());
        query.setParameter("pageSize", filter.getPageSize());

        List<Tuple> result = query.getResultList();
        List<DetailReportByGroupDto> detailReportByGroupDtos = new ArrayList<>();

        reportHelper.mapTupleToDetailReportByGroup(result, detailReportByGroupDtos);
        return detailReportByGroupDtos;
    }

    private BigInteger countDetailProcInstByGroup(ReportProcInstFilter filter) {
        String mainQuery = " select count(*) as count from ( WITH canceled_tickets AS (SELECT bh.proc_inst_id AS proc_inst_id, bh.note AS ly_do_huy, bh.created_time AS thoi_gian_huy " +
                "                          FROM bpm_history bh " +
                "                          WHERE bh.action = 'CANCEL_TICKET') " +
                "select rp.proc_inst_id                                                                             as proc_inst_id, " +
                "       rp.title                                                                                    as proc_inst_name, " +
                "       rp.service_name, " +
                "       rp.proc_inst_status                                                                         as proc_inst_status, " +
                "       rp.request_code                                                                             as request_code, " +
                "       TIMESTAMPDIFF(HOUR, rp.created_time,rp.sla_finish_time) AS elapsed_time, " +
                "       TIMESTAMPDIFF(HOUR,ifnull(rp.finished_time,current_timestamp), rp.sla_finish_time) AS delay_time, " +
                "       rp.priority_name                                                                            as priority, " +
                "       rp.created_user                                                                             AS created_user, " +
                "       rp.created_user_full_name, " +
                "       rp.chart_node_name, " +
                "       rp.sla_finish_time, " +
                "       rp.finished_time, " +
                "       rp.chart_node_code, " +
                "       rp.chart_short_name, " +
                "       rp.user_title_name, " +
                "       count_proc_inst_cancel.pc                                                                   as so_lan_huy_phieu, " +
                "       bhc.ly_do_huy                                                                               as ly_do_huy_phieu, " +
                "       bhc.thoi_gian_huy                                                                           as thoi_gian_huy_phieu " +
                "from report_by_group rp " +
                "         LEFT JOIN canceled_tickets bhc ON rp.proc_inst_id = bhc.proc_inst_id " +
                "         LEFT JOIN (select proc_inst_id, count(proc_inst_id) as pc " +
                "                    from canceled_tickets " +
                "                    group by proc_inst_id) as count_proc_inst_cancel " +
                "                   on rp.proc_inst_id = count_proc_inst_cancel.proc_inst_id  " +
                "WHERE rp.created_user in :defaultUser AND rp.chart_node_id in :defaultChartNodeId AND ";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        reportHelper.buildDetailReportByGroupFilter(filter, stringBuilder);
        stringBuilder.append(" 1 = 1) as a ");

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);
        reportHelper.buildDetailReportByGroupParam(filter, query);

        List<Tuple> result = query.getResultList();
        return result.get(0).get("count", BigInteger.class);
    }

    private List<DetailReportByGroupDto> getListDetailReportByChartNode(ReportProcInstFilter filter) throws ParseException {
        String mainQuery = " WITH returned_task AS (SELECT bh.task_inst_id AS task_inst_id, " +
                "                              bh.note         AS ly_do_tra_ve, " +
                "                              bh.created_time AS thoi_gian_tra_ve " +
                "                       FROM bpm_history bh " +
                "                       WHERE bh.action IN ('REQUEST_UPDATE', 'AFFECTED_BY_RU')) " +
                "select d.* from (select proc_inst_id                      as proc_inst_id, " +
                "       title                             as proc_inst_name, " +
                "       service_name, " +
                "       task_type                         as task_type, " +
                "       task_status                       as task_status, " +
                "       task_id                           as task_id, " +
                "       TIMESTAMPDIFF(HOUR, rc.created_time,rc.sla_finish_time) AS elapsed_time, " +
                "       TIMESTAMPDIFF(HOUR,ifnull(rc.finished_time,current_timestamp), rc.sla_finish_time) AS delay_time, " +
                "       priority_name                     as priority, " +
                "       rc.request_code                   as request_code, " +
                "       count_task_returned.ctr           as so_lan_tra_ve, " +
                "       assignee                          AS assignee, " +
                "       rc.assignee_full_name             as assignee_full_name, " +
                "       rc.assignee_chart_short_name      as assignee_chart_short_name, " +
                "       rc.assignee_status                as assignee_status, " +
                "       rc.assignee_staff_code            as assignee_staff_code, " +
                "       rc.assignee_title_name            as assignee_title_name, " +
                "       rc.assignee_chart_node_id         as assignee_chart_node_id, " +
                "       rc.assignee_chart_node_name       as assignee_chart_node_name, " +
                "       rc.assignee_chart_node_code       as assignee_chart_node_code, " +
                "       rc.assignee_chart_id              as assignee_chart_id, " +
                "       rc.created_user                   as created_user, " +
                "       rc.created_user_chart_short_name  as created_user_chart_short_name, " +
                "       rc.created_user_full_name         as created_user_full_name, " +
                "       rc.created_user_title_name        as created_user_title_name, " +
                "       rc.created_user_chart_node_id     as created_user_chart_node_id, " +
                "       rc.created_user_chart_node_code   as created_user_chart_node_code, " +
                "       rc.created_user_chart_node_name   as created_user_chart_node_name, " +
                "       rc.created_user_chart_id          as created_user_chart_id, " +
                "       rc.task_name                      as taskName, " +
                "       rc.created_time                   as task_created_time, " +
                "       rc.sla_finish_time                as sla_finish_time, " +
                "       rc.finished_time                  as finished_time, " +
                "       rc.started_time                   as started_time, " +
                "       rc.cancel_reason                  as ly_do_huy, " +
                "       rc.is_expire                      as is_expire, " +
                "       rc.proc_def_id                    as proc_def_id, " +
                "       rc.ticket_id                      as ticket_id " +
                "from report_by_chart_node rc " +
                "         LEFT JOIN (select task_inst_id, count(task_inst_id) as ctr " +
                "                    from returned_task " +
                "                    group by task_inst_id) as count_task_returned " +
                "                   on rc.task_id = count_task_returned.task_inst_id" +
                " WHERE rc.assignee in :defaultUser AND rc.assignee_chart_node_id in :defaultChartNodeId AND ";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        reportHelper.buildDetailReportByChartNodeFilter(filter, stringBuilder);
        stringBuilder.append(" 1 = 1   ");

        stringBuilder.append("  LIMIT :page, :pageSize) as d  ");
        reportHelper.buildDetailReportByChartNodeSort(filter, stringBuilder);

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);

        reportHelper.buildDetailReportByChartNodeParam(filter, query);

        query.setParameter("page", (filter.getPage() - 1) * filter.getPageSize());
        query.setParameter("pageSize", filter.getPageSize());

        List<Tuple> result = query.getResultList();
        List<DetailReportByGroupDto> detailReportByGroupDtos = new ArrayList<>();
        reportHelper.mapTupleToDetailReportByChartNode(result, detailReportByGroupDtos);
        return detailReportByGroupDtos;
    }


    private BigInteger countDetailProcInstByChartNode(ReportProcInstFilter filter) {
        String mainQuery = " (WITH returned_task AS (SELECT bh.task_inst_id AS task_inst_id, " +
                "                              bh.note         AS ly_do_tra_ve, " +
                "                              bh.created_time AS thoi_gian_tra_ve " +
                "                       FROM bpm_history bh " +
                "                       WHERE bh.action IN ('REQUEST_UPDATE', 'AFFECTED_BY_RU')) " +
                "select proc_inst_id                      as proc_inst_id, " +
                "       title                             as proc_inst_name, " +
                "       service_name, " +
                "       task_type                         as task_type, " +
                "       task_status                       as task_status, " +
                "       task_id                           as task_id, " +
                "       TIMESTAMPDIFF(HOUR, rc.created_time,rc.sla_finish_time) AS elapsed_time, " +
                "       TIMESTAMPDIFF(HOUR,ifnull(rc.finished_time,current_timestamp), rc.sla_finish_time) AS delay_time, " +
                "       priority_name                     as priority, " +
                "       rc.request_code                   as request_code, " +
                "       count_task_returned.ctr           as so_lan_tra_ve, " +
                "       assignee                          AS assignee, " +
                "       rc.assignee_full_name             as assignee_full_name, " +
                "       rc.assignee_chart_short_name      as assignee_chart_short_name, " +
                "       rc.assignee_status                as assignee_status, " +
                "       rc.assignee_staff_code            as assignee_staff_code, " +
                "       rc.assignee_title_name            as assignee_title_name, " +
                "       rc.assignee_chart_node_id         as assignee_chart_node_id, " +
                "       rc.assignee_chart_node_name       as assignee_chart_node_name, " +
                "       rc.assignee_chart_node_code       as assignee_chart_node_code, " +
                "       rc.assignee_chart_id              as assignee_chart_id, " +
                "       rc.created_user                   as created_user, " +
                "       rc.created_user_chart_short_name  as created_user_chart_short_name, " +
                "       rc.created_user_full_name         as created_user_full_name, " +
                "       rc.created_user_title_name        as created_user_title_name, " +
                "       rc.created_user_chart_node_id     as created_user_chart_node_id, " +
                "       rc.created_user_chart_node_code   as created_user_chart_node_code, " +
                "       rc.created_user_chart_node_name   as created_user_chart_node_name, " +
                "       rc.created_user_chart_id          as created_user_chart_id, " +
                "       rc.task_name                      as taskName, " +
                "       rc.created_time                   as task_created_time, " +
                "       rc.cancel_reason                  as ly_do_huy, " +
                "       rc.is_expire                      as is_expire, " +
                "       rc.proc_def_id                    as proc_def_id, " +
                "       rc.ticket_id                      as ticket_id " +
                "from report_by_chart_node rc " +
                "         LEFT JOIN (select task_inst_id, count(task_inst_id) as ctr " +
                "                    from returned_task " +
                "                    group by task_inst_id) as count_task_returned " +
                "                   on rc.task_id = count_task_returned.task_inst_id" +
                " WHERE rc.assignee in :defaultUser AND rc.assignee_chart_node_id in :defaultChartNodeId AND ";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select count(*) as count FROM ").append(mainQuery);
        reportHelper.buildDetailReportByChartNodeFilter(filter, stringBuilder);
        stringBuilder.append(" 1 = 1 ) as a   ");

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);
        reportHelper.buildDetailReportByChartNodeParam(filter, query);
        List<Tuple> result = query.getResultList();
        return result.get(0).get("count", BigInteger.class);
    }

//    @NotNull
//    private List<DetailReportByGroupDto> getListDetailReportByUser(ReportProcInstFilter filter) throws ParseException {
//        Set<DetailReportByGroupDto> response = new HashSet<>();
//        List<DetailReportByGroupDto> detailReportByGroupDtos = getListDetailReportByChartNode(filter);
//        for (DetailReportByGroupDto e : detailReportByGroupDtos) {
//            ReportByGroup reportByGroup = reportByGroupRepository.findByTicketId(e.getTicketId());
//            DetailReportByGroupDto detailReportByGroupDto = new DetailReportByGroupDto();
//            detailReportByGroupDto.setTicketId(reportByGroup.getTicketId());
//            detailReportByGroupDto.setRequestCode(reportByGroup.getRequestCode());
//            detailReportByGroupDto.setProcInstId(reportByGroup.getProcInstId());
//            detailReportByGroupDto.setProcInstName(reportByGroup.getTitle());
//            detailReportByGroupDto.setServiceName(reportByGroup.getServiceName());
//            detailReportByGroupDto.setTaskStatus(reportByGroup.getProcInstStatus());
//            detailReportByGroupDto.setPriority(reportByGroup.getPriorityName());
//            detailReportByGroupDto.setCreatedUser(reportByGroup.getCreatedUser());
//            detailReportByGroupDto.setCreatedUserFullName(reportByGroup.getCreatedUserFullName());
//            detailReportByGroupDto.setCreatedUserChartNodeName(reportByGroup.getChartNodeName());
//            detailReportByGroupDto.setCreatedUserChartShortName(reportByGroup.getChartShortName());
//            detailReportByGroupDto.setCreatedUserTitleName(reportByGroup.getUserTitleName());
//            detailReportByGroupDto.setCreatedTime(reportByGroup.getCreatedTime());
//            detailReportByGroupDto.setFinishedTime(reportByGroup.getFinishedTime());
//            detailReportByGroupDto.setProcDefId(reportByGroup.getProcDefId());
//            detailReportByGroupDto.setIsExpire(reportByGroup.getIsExpire().toString());
//            Long elapsedTime = ChronoUnit.HOURS.between(reportByGroup.getCreatedTime(), LocalDateTime.now());
//            detailReportByGroupDto.setElapsedTime(elapsedTime);
//            LocalDateTime finishTime = reportByGroup.getFinishedTime();
//            finishTime = (finishTime == null) ? LocalDateTime.now() : finishTime;
//            LocalDateTime estimateTime = reportByGroup.getCreatedTime().plusHours(Math.round(reportByGroup.getSlaFinish()));
//            Long delayTime = ChronoUnit.HOURS.between(finishTime, estimateTime);
//            detailReportByGroupDto.setDelayTime(Double.valueOf(delayTime));
//            response.add(detailReportByGroupDto);
//        }
//        List<DetailReportByGroupDto> listResponse = new ArrayList<>(response);
//        return listResponse;
//    }

//    private Long countDetailReportByUser(ReportProcInstFilter filter) throws ParseException {
//        filter.setPageSize(999999999);
//        List<DetailReportByGroupDto> detailReportByGroupDtos = getListDetailReportByChartNode(filter);
//        return Long.valueOf(detailReportByGroupDtos.size());
//    }

    private List<DetailReportByGroupDto> getListDetailReportByUser(ReportProcInstFilter filter) {
        String mainQuery = " WITH canceled_tickets AS (SELECT bh.proc_inst_id AS proc_inst_id, bh.note AS ly_do_huy, bh.created_time AS thoi_gian_huy " +
                "                          FROM bpm_history bh " +
                "                          WHERE bh.action = 'CANCEL_TICKET'), " +
                " approval_returned as (select bh.proc_inst_id AS proc_inst_id, " +
                "                                  bh.note         AS ly_do_tra_ve, " +
                "                                  bh.created_time AS thoi_gian_tra_ve " +
                "                           FROM bpm_history bh " +
                "                           WHERE bh.action = 'REQUEST_UPDATE')  " +
                " select d.* from ( select rp.proc_inst_id                                                                           as proc_inst_id, " +
                "       rp.title                                                                                    as proc_inst_name, " +
                "       rp.service_name, " +
                "       rp.ticket_id, " +
                "       rp.proc_inst_status                                                                         as proc_inst_status, " +
                "       rp.request_code                                                                             as request_code, " +
                "       TIMESTAMPDIFF(HOUR, rp.created_time,rp.sla_finish_time) AS elapsed_time, " +
                "       TIMESTAMPDIFF(HOUR,ifnull(rp.finished_time,current_timestamp), rp.sla_finish_time) AS delay_time, " +
                "       rp.priority_name                                                                            as priority, " +
                "       rp.created_user                                                                             AS created_user, " +
                "       rp.sla_finish_time                                                                          AS sla_finish_time, " +
                "       rp.created_user_full_name, " +
                "       rp.chart_node_name, " +
                "       rp.chart_node_code, " +
                "       rp.chart_short_name, " +
                "       rp.created_time, " +
                "       rp.finished_time, " +
                "       rp.user_title_name, " +
                "       rp.is_expire as is_expire, " +
                "       count_proc_inst_returned.pr                                          as so_lan_tra_ve, " +
                "       bhc.ly_do_huy                                                                               as ly_do_huy_phieu, " +
                "       bhc.thoi_gian_huy                                                                           as thoi_gian_huy_phieu,  " +
                "       ar.ly_do_tra_ve                                                      as ly_do_tra_ve, " +
                "       ar.thoi_gian_tra_ve                                                  as thoi_gian_tra_ve  " +
                "from report_by_group rp " +
                "         LEFT JOIN canceled_tickets bhc ON rp.proc_inst_id = bhc.proc_inst_id " +
                "         LEFT JOIN approval_returned ar on rp.proc_inst_id = ar.proc_inst_id " +
                "         LEFT JOIN (select proc_inst_id, count(proc_inst_id) as pr " +
                "                    from approval_returned  " +
                "                    group by proc_inst_id) as count_proc_inst_returned  " +
                "                   on rp.proc_inst_id = count_proc_inst_returned.proc_inst_id  " +
                "WHERE ";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        reportHelper.buildDetailReportByUserFilter(filter, stringBuilder);
        stringBuilder.append(" 1 = 1 ");

        stringBuilder.append(" LIMIT :page, :pageSize) as d   ");
        reportHelper.buildDetailReportByGroupSort(filter, stringBuilder);

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);
        reportHelper.buildDetailReportByUserParam(filter, query);
        query.setParameter("page", (filter.getPage() - 1) * filter.getPageSize());
        query.setParameter("pageSize", filter.getPageSize());

        List<Tuple> result = query.getResultList();
        List<DetailReportByGroupDto> detailReportByGroupDtos = new ArrayList<>();

        reportHelper.mapTupleToDetailReportByGroup(result, detailReportByGroupDtos);
        return detailReportByGroupDtos;
    }

    private BigInteger countDetailProcInstByUser(ReportProcInstFilter filter) {
        String mainQuery = " select count(*) as count from ( WITH canceled_tickets AS (SELECT bh.proc_inst_id AS proc_inst_id, bh.note AS ly_do_huy, bh.created_time AS thoi_gian_huy " +
                "                          FROM bpm_history bh " +
                "                          WHERE bh.action = 'CANCEL_TICKET') " +
                "select rp.proc_inst_id                                                                             as proc_inst_id, " +
                "       rp.title                                                                                    as proc_inst_name, " +
                "       rp.service_name, " +
                "       rp.proc_inst_status                                                                         as proc_inst_status, " +
                "       rp.request_code                                                                             as request_code, " +
                "       TIMESTAMPDIFF(HOUR, rp.created_time,rp.sla_finish_time) AS elapsed_time, " +
                "       TIMESTAMPDIFF(HOUR,ifnull(rp.finished_time,current_timestamp), rp.sla_finish_time) AS delay_time, " +
                "       rp.priority_name                                                                            as priority, " +
                "       rp.created_user                                                                             AS created_user, " +
                "       rp.created_user_full_name, " +
                "       rp.chart_node_name, " +
                "       rp.chart_node_code, " +
                "       rp.chart_short_name, " +
                "       rp.user_title_name, " +
                "       count_proc_inst_cancel.pc                                                                   as so_lan_huy_phieu, " +
                "       bhc.ly_do_huy                                                                               as ly_do_huy_phieu, " +
                "       bhc.thoi_gian_huy                                                                           as thoi_gian_huy_phieu " +
                "from report_by_group rp " +
                "         LEFT JOIN canceled_tickets bhc ON rp.proc_inst_id = bhc.proc_inst_id " +
                "         LEFT JOIN (select proc_inst_id, count(proc_inst_id) as pc " +
                "                    from canceled_tickets " +
                "                    group by proc_inst_id) as count_proc_inst_cancel " +
                "                   on rp.proc_inst_id = count_proc_inst_cancel.proc_inst_id  " +
                "WHERE ";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(mainQuery);
        reportHelper.buildDetailReportByUserFilter(filter, stringBuilder);
        stringBuilder.append(" 1 = 1) as a ");

        Query query = entityManager.createNativeQuery(stringBuilder.toString(), Tuple.class);
        reportHelper.buildDetailReportByUserParam(filter, query);

        List<Tuple> result = query.getResultList();
        return result.get(0).get("count", BigInteger.class);
    }
}