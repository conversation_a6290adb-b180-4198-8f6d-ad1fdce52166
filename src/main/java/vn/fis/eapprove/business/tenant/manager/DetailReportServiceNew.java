package vn.fis.eapprove.business.tenant.manager;


import vn.fis.eapprove.business.dto.filter.ReportProcInstFilter;
import vn.fis.eapprove.business.dto.filter.RequestDetailTaskReturned;
import vn.fis.eapprove.business.dto.report.DetailTaskDto;
import vn.fis.eapprove.business.dto.report.DetailTaskReturnedDto;
import vn.fis.eapprove.business.dto.report.DetailTicketCancelResponse;
import vn.fis.eapprove.business.model.response.DetailReportByGroupResponse;

import java.util.List;

public interface DetailReportServiceNew {

    DetailReportByGroupResponse getDetailReportByChartNode(ReportProcInstFilter filter);

    DetailReportByGroupResponse getDetailReportByUser(ReportProcInstFilter filter);

    DetailReportByGroupResponse getDetailReportByGroup(ReportProcInstFilter filter);

    List<DetailTaskDto> getDetailTaskByProcInstId(ReportProcInstFilter filter);

    List<DetailTaskReturnedDto> getDetailTaskReturned(RequestDetailTaskReturned requestDetailTaskReturned);

    DetailTicketCancelResponse getDetailTicketCancel(String procInstId);

}
