package vn.fis.eapprove.business.tenant.manager;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.assistant.entity.Assistant;
import vn.fis.eapprove.business.domain.assistant.repository.AssistantRepository;
import vn.fis.eapprove.business.domain.authority.entity.AuthorityManagement;
import vn.fis.eapprove.business.domain.authority.repository.AuthorityManagementRepository;
import vn.fis.eapprove.business.domain.authority.service.AuthorityManagementService;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcInstRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTaskRepository;
import vn.fis.eapprove.business.domain.bpm.service.BpmHistoryManager;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcInstManager;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcdefNotificationService;
import vn.fis.eapprove.business.domain.bpm.service.BpmTaskManager;
import vn.fis.eapprove.business.domain.changeAssignee.entity.ChangeAssigneeHistory;
import vn.fis.eapprove.business.domain.changeAssignee.service.ChangeAssigneeHistoryService;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.AccountModel;
import vn.fis.eapprove.business.model.request.HandingOverWorkRequest;
import vn.fis.eapprove.business.model.request.HandingOverWorkSearchRequest;
import vn.fis.eapprove.business.model.request.HistoryDto;
import vn.fis.eapprove.business.model.response.HandingOverWorkResponse;
import vn.fis.eapprove.business.model.response.MyTaskResponse;
import vn.fis.eapprove.business.producer.ReportProducer;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.SymbolEnum;
import vn.fis.spro.common.constants.ProcInstConstants;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.*;

import static vn.fis.spro.common.constants.ProcInstConstants.Notifications.*;

@Slf4j
@Service
@Transactional
public class HandingOverWorkManager {
    @Autowired
    CredentialHelper credentialHelper;
    @Autowired
    BpmProcInstRepository bpmProcInstRepository;
    @Autowired
    BpmTaskManager taskManager;
    @Autowired
    AuthorityManagementService authorityManagementService;
    @Autowired
    BpmTaskManager bpmTaskManager;
    @Autowired
    AuthorityManagementRepository authorityManagementRepository;
    @Autowired
    BpmProcdefNotificationService bpmProcdefNotificationService;
    @Autowired
    private AssistantRepository assistantRepository;
    @Autowired
    private BpmProcInstManager bpmProcInstManager;
    @Autowired
    private Common common;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private BpmTaskRepository bpmTaskRepository;
    @Autowired
    private BpmHistoryManager bpmHistoryManager;
    @Autowired
    private ChangeAssigneeHistoryService changeAssigneeHistoryService;
    @Autowired
    private ReportProducer reportProducer;

    @Value("${spring.kafka.consumer.topic.insert-report-by-group}")
    private String topicInsertReportByGroup;
    @Value("${spring.kafka.consumer.topic.insert-report-by-chart-node}")
    private String topicInsertReportByChartNode;

    public Boolean process(HandingOverWorkRequest handingOverWorkRequests) {
        try {
            String account = credentialHelper.getJWTPayload().getUsername();
            List<AccountModel> lstAccountInfo = customerService.getUserTaskInfoByUserNames(Arrays.asList(handingOverWorkRequests.getNewUser(), account, handingOverWorkRequests.getOldUser()));
            AccountModel newUserInfo = lstAccountInfo.stream().filter(e -> e.getUsername().equalsIgnoreCase(handingOverWorkRequests.getNewUser())).findFirst().orElse(null);
            StringBuilder note = new StringBuilder("Bàn giao công việc cho " + handingOverWorkRequests.getNewUser());
            if (newUserInfo != null) {
                note.append(" - ").append(newUserInfo.getFirstname()).append(SymbolEnum.SPACE.value).append(newUserInfo.getLastname()).append(" - ").append(newUserInfo.getFinalTitle());
            }

            if (handingOverWorkRequests.getType().equals("0")) {
                List<BpmProcInst> bpmProcInst = bpmProcInstRepository.findBpmProcInstByticketIdIn(handingOverWorkRequests.getTicketId());
                List<BpmProcInst> bpmProcInstList = new ArrayList<>();
                List<String> listUser = new ArrayList<>();
                for (BpmProcInst procInst : bpmProcInst) {
                    if (procInst.getTicketStatus().equals("COMPLETED")) {
                        throw new RuntimeException(common.getMessage("message.ticket.completed"));
                    } else {
                        authorityManagementService.saveAuthorityByTicket(procInst.getTicketId().toString(), procInst.getCreatedUser(), handingOverWorkRequests.getNewUser());
                        procInst.setTicketStartUserId(handingOverWorkRequests.getNewUser());
                        bpmProcInstList.add(procInst);
                        listUser.add(procInst.getTicketStartUserId());

                        // save lịch sử
                        saveTicketHistory(procInst.getTicketId(), procInst.getTicketProcInstId(), null, null, null, account, HANDOVER_MY_TICKET.code, note.toString(), lstAccountInfo);
                    }
                }
                bpmProcInstRepository.saveAll(bpmProcInstList);
                for (BpmProcInst procInst : bpmProcInst) {
                    bpmProcdefNotificationService.addNotificationsByConfig(null, HANDOVER_MY_TICKET.code, new HashMap<>()
                            , procInst.getTicketId(), true, listUser, HANDOVER_MY_TICKET.code, account);
                    reportProducer.sendKafka(procInst.getTicketId(), topicInsertReportByGroup);
                }
            } else if (handingOverWorkRequests.getType().equals("3")) {
                List<Assistant> assistantList = assistantRepository.getAssistantByTicketIdAndAssistantEmail(String.valueOf(handingOverWorkRequests.getTicketId().get(0)), handingOverWorkRequests.getOldUser());
                List<Assistant> list = new ArrayList<>();
                List<String> listUser = new ArrayList<>();
                for (Assistant assistant : assistantList) {
                    authorityManagementService.saveAuthorityByAssistance(assistant.getTicketId(), assistant.getAssistantEmail(), handingOverWorkRequests.getNewUser());
                    assistant.setAssistantEmail(handingOverWorkRequests.getNewUser());
                    list.add(assistant);
                    listUser.add(assistant.getAssistantEmail());
                }
                assistantRepository.saveAll(list);
                for (Assistant assistant : assistantList) {
                    bpmProcdefNotificationService.addNotificationsByConfig(null, HANDOVER_MY_ASSISTANT.code, new HashMap<>()
                            , Long.parseLong(assistant.getTicketId()), true, listUser, HANDOVER_MY_ASSISTANT.code, account);
                }
            } else {
                List<Object[]> listChangeRequest = bpmProcInstRepository.getBpmTask(handingOverWorkRequests.getTicketId());
                List<String> listUser = new ArrayList<>();
                for (Object[] changeRequest : listChangeRequest) {
                    Map<String, Object> body = new HashMap<>();
                    body.put("ticketId", changeRequest[4].toString());
                    body.put("id", changeRequest[0].toString());
                    body.put("taskId", changeRequest[1].toString());
                    body.put("taskDefKey", changeRequest[2].toString());
                    body.put("email", handingOverWorkRequests.getNewUser());
                    body.put("title", newUserInfo != null ? newUserInfo.getFinalTitle() : "");
                    body.put("reason", "");
//                    if (handingOverWorkRequests.getOldUser().equalsIgnoreCase(changeRequest[3].toString())) {
                    List<ChangeAssigneeHistory> changeAssigneeHistories = changeAssigneeHistoryService.getLatestChangeByToAssignee((Long) changeRequest[0], changeRequest[1].toString());

                    if ((!ValidationUtils.isNullOrEmpty(changeAssigneeHistories) && changeAssigneeHistories.get(0).getOrgAssignee().equalsIgnoreCase(changeRequest[3].toString()))
                            || handingOverWorkRequests.getOldUser().equalsIgnoreCase(changeRequest[3].toString()))
                    {
                        if (((changeRequest[5].toString().equals("EXECUTION") && handingOverWorkRequests.getType().equals("1"))
                                || (changeRequest[5].toString().equals("APPROVAL") && handingOverWorkRequests.getType().equals("2")))
                                && !changeRequest[6].toString().equals("COMPLETED")
                                && !changeRequest[6].toString().equals("CANCEL")
                        ) {
                            taskManager.changeUserHandover(body, handingOverWorkRequests.getNewUser(), 3, handingOverWorkRequests.getOldUser());
                            authorityManagementService.saveAuthorityByTask(
                                    changeRequest[0].toString(),
                                    handingOverWorkRequests.getOldUser(),
                                    handingOverWorkRequests.getNewUser(),
                                    changeRequest[1].toString(),
                                    changeRequest[2].toString()
                            );
                            listUser.add(handingOverWorkRequests.getNewUser());
                            bpmProcdefNotificationService.addNotificationsByConfig(null, HANDOVER_MY_TASK.code, new HashMap<>()
                                    , handingOverWorkRequests.getTicketId().get(0), true, listUser, HANDOVER_MY_TASK.code, account);

                            // save lịch sử
                            String taskInstId = changeRequest[7].toString();
                            String taskDefKey = changeRequest[2].toString();
                            String taskType = changeRequest[5].toString();
                            String procInstId = changeRequest[4].toString();

                            Long ticketIdHis = Long.parseLong(changeRequest[0].toString());
                            saveTicketHistory(ticketIdHis, procInstId,
                                    taskInstId, taskDefKey, taskType,
                                    account, HANDOVER_MY_TASK.code, note.toString(), lstAccountInfo);
                        }
//                        else if (changeRequest[5].toString().equals("APPROVAL") && handingOverWorkRequests.getType().equals("2")) {
//                            if (!changeRequest[6].toString().equals("COMPLETED") && !changeRequest[6].toString().equals("CANCEL")) {
//                                taskManager.changeUserHandover(body, handingOverWorkRequests.getNewUser(), 3, handingOverWorkRequests.getOldUser());
//                                authorityManagementService.saveAuthorityByTask(changeRequest[0].toString(), handingOverWorkRequests.getOldUser(), handingOverWorkRequests.getNewUser(), changeRequest[1].toString(), changeRequest[2].toString());
//                                listUser.add(handingOverWorkRequests.getNewUser());
//                                bpmProcdefNotificationService.addNotificationsByConfig(null, HANDOVER_MY_TASK.code, new HashMap<>()
//                                        , handingOverWorkRequests.getTicketId().get(0), true, listUser, HANDOVER_MY_TASK.code);
//
//                                // save lịch sử
//                                String taskInstId = changeRequest[7].toString();
//                                String taskDefKey = changeRequest[2].toString();
//                                String taskType = changeRequest[5].toString();
//                                String procInstId = changeRequest[4].toString();
//
//                                Long ticketIdHis = Long.parseLong(changeRequest[0].toString());
//                                saveTicketHistory(ticketIdHis, procInstId,
//                                        taskInstId, taskDefKey, taskType,
//                                        account, HANDOVER_MY_TASK.code, note.toString(), lstAccountInfo);
//
//                            }
//                        }
                    }

//                    }
//                        Long ticketId = Long.parseLong(changeRequest[0].toString());
//                        saveTicketHistory(ticketId, procInstId,
//                                taskInstId, taskDefKey, taskType,
//                                account, HANDOVER_MY_TASK.code, note.toString(), lstAccountInfo);
                    }

                    List<BpmTask> listTaskReport = new ArrayList<>();
                    for (Long ticketId : handingOverWorkRequests.getTicketId()) {

                        BpmProcInst bpmProcInst = bpmProcInstRepository.getBpmProcInstByTicketId(ticketId);
                        List<BpmTask> listTask = bpmTaskRepository.getBpmTaskByTaskProcInstId(bpmProcInst.getTicketProcInstId());
                        listTaskReport.addAll(listTask);
                        reportProducer.sendKafka(bpmProcInst.getTicketId(), topicInsertReportByGroup);

                    }
                    for (BpmTask task : listTaskReport) {
                        reportProducer.sendKafka(task.getTaskId(), topicInsertReportByChartNode);

                    }
                }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public PageDto search(HandingOverWorkSearchRequest handingOverWorkSearchRequest) {
        try {
            PageDto pageDto = null;
            List<HandingOverWorkResponse> handingOverWorkResponses = new ArrayList<>();
            if (!ValidationUtils.isNullOrEmpty(handingOverWorkSearchRequest.getLoadTicketDto())) {
                handingOverWorkSearchRequest.getLoadTicketDto().setHandingOverWork(true);
                pageDto = bpmProcInstManager.search(handingOverWorkSearchRequest.getLoadTicketDto(), handingOverWorkSearchRequest.getLoadTicketDto().getUser());
                List<Map<String, Object>> myTicketResponse = pageDto.getContent();
                for (Map<String, Object> objectMap : myTicketResponse) {
                    HandingOverWorkResponse handingOverWorkResponse = new HandingOverWorkResponse();
                    handingOverWorkResponse.setTicketId(Long.parseLong(objectMap.get("id").toString()));
                    handingOverWorkResponse.setProcDefId(objectMap.get("ticketProcDefId").toString());
                    handingOverWorkResponse.setProcInstId(objectMap.get("ticketId").toString());
                    handingOverWorkResponse.setTitle(objectMap.get("ticketTitle").toString());
                    handingOverWorkResponse.setStatusTicket(objectMap.get("ticketStatus").toString());
                    handingOverWorkResponse.setTicketCreatedTime((Date) objectMap.get("ticketCreatedTime"));
                    handingOverWorkResponse.setServiceName(ValidationUtils.isNullOrEmpty(objectMap.get("procServiceName")) ? "" : objectMap.get("procServiceName").toString());
                    handingOverWorkResponse.setRequestCode(ValidationUtils.isNullOrEmpty(objectMap.get("requestCode")) ? "" : objectMap.get("requestCode").toString());
                    List<AuthorityManagement> authorityManagement = authorityManagementRepository.findByTicketIdAndTypeOrderByCreateAtDesc(Long.parseLong(objectMap.get("id").toString()), 2);

                    for (AuthorityManagement management : authorityManagement) {
                        handingOverWorkResponse.setUserAction(management.getUserCreate());
                        handingOverWorkResponse.setTimeAction(management.getCreateAt());

                        if (handingOverWorkSearchRequest.getLoadTicketDto().getUser().equals(management.getToAccount())) {
                            handingOverWorkResponse.setStatus(false);
                            handingOverWorkResponse.setUserTranfer(null);
                            break;
                        }
                        if (handingOverWorkSearchRequest.getLoadTicketDto().getUser().equals(management.getFromAccount())) {
                            handingOverWorkResponse.setStatus(true);
                            handingOverWorkResponse.setUserTranfer(authorityManagement.get(0).getToAccount());
                            break;
                        }
                    }


                    handingOverWorkResponses.add(handingOverWorkResponse);
                }

                // check user
                boolean checkUser = bpmProcInstManager.checkUser(handingOverWorkSearchRequest.getLoadTicketDto().getUser());
                if (checkUser) {
                    customerService.changeStatusHandOver(handingOverWorkSearchRequest.getLoadTicketDto().getUser());
                }

                pageDto = PageDto.builder()
                        .content(handingOverWorkResponses)
                        .number(handingOverWorkSearchRequest.getLoadTicketDto().getPage())
                        .numberOfElements(handingOverWorkSearchRequest.getLoadTicketDto().getPage())
                        .page(handingOverWorkSearchRequest.getLoadTicketDto().getPage())
                        .size(handingOverWorkSearchRequest.getLoadTicketDto().getLimit())
                        .totalPages(pageDto.getTotalPages())
                        .totalElements(pageDto.getTotalElements())
                        .build();
            } else if (!ValidationUtils.isNullOrEmpty(handingOverWorkSearchRequest.getMyTaskRequest())) {
                pageDto = bpmTaskManager.myTask(handingOverWorkSearchRequest.getMyTaskRequest(), handingOverWorkSearchRequest.getMyTaskRequest().getUser());
                List<MyTaskResponse> myTaskResponses = pageDto.getContent();
                for (MyTaskResponse myTaskResponse : myTaskResponses) {
                    HandingOverWorkResponse handingOverWorkResponse = new HandingOverWorkResponse();
                    handingOverWorkResponse.setTicketId(myTaskResponse.getTicketId());
                    handingOverWorkResponse.setProcDefId(myTaskResponse.getTicketProcDefId());
                    handingOverWorkResponse.setProcInstId(myTaskResponse.getProcInstId());
                    handingOverWorkResponse.setTitle(myTaskResponse.getProcTitle());

                    // ticketStatus = RECALLING check riêng
                    handingOverWorkResponse.setStatusTicket(myTaskResponse.getTicketStatus().equalsIgnoreCase(ProcInstConstants.Status.RECALLING.code)
                            ? myTaskResponse.getTicketStatus()
                            : myTaskResponse.getTaskStatus());

                    handingOverWorkResponse.setServiceName(myTaskResponse.getServiceName());
                    handingOverWorkResponse.setTicketCreatedTime(myTaskResponse.getTicketCreatedTime());
                    if (!ValidationUtils.isNullOrEmpty(myTaskResponse.getRequestCode())) {
                        handingOverWorkResponse.setRequestCode(myTaskResponse.getRequestCode());
                    }

                    List<AuthorityManagement> authorityManagement = authorityManagementRepository.findByTicketIdAndTypeAndTaskDefKeyOrderByCreateAtDesc(myTaskResponse.getTicketId(), 3, myTaskResponse.getTaskDefKey());
                    for (AuthorityManagement management : authorityManagement) {
                        handingOverWorkResponse.setUserAction(management.getUserCreate());
                        handingOverWorkResponse.setTimeAction(management.getCreateAt());
                        if (handingOverWorkSearchRequest.getMyTaskRequest().getUser().equals(management.getToAccount())) {
                            handingOverWorkResponse.setStatus(false);
                            handingOverWorkResponse.setUserTranfer(null);
                        }
                        if (handingOverWorkSearchRequest.getMyTaskRequest().getUser().equals(management.getFromAccount())) {
                            handingOverWorkResponse.setStatus(true);
                            handingOverWorkResponse.setUserTranfer(authorityManagement.get(0).getToAccount());
                        }
                    }

                    handingOverWorkResponses.add(handingOverWorkResponse);
                }
                // check user
                boolean checkUser = bpmProcInstManager.checkUser(handingOverWorkSearchRequest.getMyTaskRequest().getUser());
                if (checkUser) {
                    customerService.changeStatusHandOver(handingOverWorkSearchRequest.getMyTaskRequest().getUser());
                }

                pageDto = PageDto.builder()
                        .content(handingOverWorkResponses)
                        .number(handingOverWorkSearchRequest.getMyTaskRequest().getPage())
                        .numberOfElements(handingOverWorkSearchRequest.getMyTaskRequest().getPage())
                        .page(handingOverWorkSearchRequest.getMyTaskRequest().getPage())
                        .size(handingOverWorkSearchRequest.getMyTaskRequest().getLimit())
                        .totalPages(pageDto.getTotalPages())
                        .totalElements(pageDto.getTotalElements())
                        .build();
            } else if (!ValidationUtils.isNullOrEmpty(handingOverWorkSearchRequest.getLoadAssistantTicketDto())) {

                pageDto = bpmProcInstManager.searchByListAssistantEmail(handingOverWorkSearchRequest.getLoadAssistantTicketDto());
                List<Map<String, Object>> assistanceResponses = pageDto.getContent();
                for (Map<String, Object> assistance : assistanceResponses) {
                    HandingOverWorkResponse handingOverWorkResponse = new HandingOverWorkResponse();
                    handingOverWorkResponse.setTicketId(Long.valueOf(assistance.get("id").toString()));
                    handingOverWorkResponse.setProcDefId(assistance.get("ticketProcDefId").toString());
                    handingOverWorkResponse.setProcInstId(assistance.get("ticketId").toString());
                    handingOverWorkResponse.setTitle(assistance.get("ticketTitle").toString());
                    handingOverWorkResponse.setStatusTicket(assistance.get("ticketStatus").toString());
                    handingOverWorkResponse.setServiceName(assistance.get("procServiceName").toString());
                    handingOverWorkResponse.setTicketCreatedTime((Date) assistance.get("ticketCreatedTime"));
                    if (!ValidationUtils.isNullOrEmpty(assistance.get("requestCode"))) {
                        handingOverWorkResponse.setRequestCode(assistance.get("requestCode").toString());
                    }
                    List<AuthorityManagement> authorityManagement = authorityManagementRepository.findByTicketIdAndTypeOrderByCreateAtDesc(Long.valueOf(assistance.get("id").toString()), 4);
                    for (AuthorityManagement managementAssistance : authorityManagement) {
                        handingOverWorkResponse.setUserAction(managementAssistance.getUserCreate());
                        handingOverWorkResponse.setTimeAction(managementAssistance.getCreateAt());
                        if (handingOverWorkSearchRequest.getLoadAssistantTicketDto().getUser().equals(managementAssistance.getToAccount())) {
                            handingOverWorkResponse.setStatus(false);
                            handingOverWorkResponse.setUserTranfer(null);
                            break;
                        }
                        if (handingOverWorkSearchRequest.getLoadAssistantTicketDto().getUser().equals(managementAssistance.getFromAccount())) {
                            handingOverWorkResponse.setStatus(true);
                            handingOverWorkResponse.setUserTranfer(authorityManagement.get(0).getToAccount());
                            break;
                        }
                    }

                    handingOverWorkResponses.add(handingOverWorkResponse);
                }
                pageDto = PageDto.builder()
                        .content(handingOverWorkResponses)
                        .number(handingOverWorkSearchRequest.getLoadAssistantTicketDto().getPage())
                        .numberOfElements(handingOverWorkSearchRequest.getLoadAssistantTicketDto().getPage())
                        .page(handingOverWorkSearchRequest.getLoadAssistantTicketDto().getPage())
                        .size(handingOverWorkSearchRequest.getLoadAssistantTicketDto().getLimit())
                        .totalPages(pageDto.getTotalPages())
                        .totalElements(pageDto.getTotalElements())
                        .build();
            }
            return pageDto;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    private void saveTicketHistory(Long ticketId,
                                   String procInstId,
                                   String taskInstId,
                                   String taskDefKey,
                                   String taskType,
                                   String account,
                                   String actionCode,
                                   String note,
                                   List<AccountModel> lstAccountInfo
    ) {
        HistoryDto hisDto = new HistoryDto();
        hisDto.setTicketId(ticketId);
        hisDto.setActionUser(account);
        hisDto.setAction(actionCode);
        hisDto.setFromTask(null);
        hisDto.setProcInstId(procInstId);
        hisDto.setNote(note);
        hisDto.setTaskInstId(taskInstId);
        hisDto.setTaskDefKey(taskDefKey);
        hisDto.setTaskType(taskType);

        // get action_user_info
        if (!ValidationUtils.isNullOrEmpty(lstAccountInfo)) {
            Map<String, Object> actionUserInfo = new HashMap<>();
            AccountModel accountInfo = lstAccountInfo.stream().filter(e -> e.getUsername().equalsIgnoreCase(account)).findFirst().orElse(null);
            if (!ValidationUtils.isNullOrEmpty(accountInfo)) {
                actionUserInfo.put("fullName", accountInfo.getLastname() + SymbolEnum.SPACE.value + accountInfo.getFirstname());
                actionUserInfo.put("userTitle", accountInfo.getFinalTitle());
                hisDto.setActionUserInfo(ObjectUtils.writeValueAsString(actionUserInfo));
            }
        }
        bpmHistoryManager.saveHistory(hisDto);
    }

}
