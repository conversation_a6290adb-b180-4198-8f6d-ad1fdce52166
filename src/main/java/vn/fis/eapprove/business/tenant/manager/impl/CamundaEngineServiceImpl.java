package vn.fis.eapprove.business.tenant.manager.impl;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import vn.fis.eapprove.business.model.request.CompleteTaskDto;
import vn.fis.eapprove.business.model.request.StartProcessInstanceDto;
import vn.fis.eapprove.business.model.request.WorkFlowRequest;
import vn.fis.eapprove.business.tenant.manager.ActHiVarInstManager;
import vn.fis.eapprove.business.tenant.manager.CamundaEngineService;
import vn.fis.eapprove.business.utils.FileUtils;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.camunda.IdentityLink;
import vn.fis.spro.common.camunda.SequenceFlowData;
import vn.fis.spro.common.camunda.SproFlow;
import vn.fis.spro.common.camunda.VariableModification;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.MapKeyEnum;
import vn.fis.spro.common.helper.RestHelper;
import vn.fis.spro.common.util.ValidationUtils;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: PhucVM
 * Date: 27/08/2022
 */
@Service
@Slf4j
@SuppressWarnings({"unchecked"})
public class CamundaEngineServiceImpl implements CamundaEngineService {

    private final SproProperties sproProperties;
    private final RestHelper restHelper;
    private final ActHiVarInstManager actHiVarInstManager;

    private String camundaEngineHost;

    @Autowired
    public CamundaEngineServiceImpl(SproProperties sproProperties,
                                    RestHelper restHelper,
                                    @Lazy ActHiVarInstManager actHiVarInstManager) {
        this.sproProperties = sproProperties;
        this.restHelper = restHelper;
        this.actHiVarInstManager = actHiVarInstManager;
    }

    @PostConstruct
    public void init() {
        camundaEngineHost = sproProperties.getServiceUrls().get(MapKeyEnum.BPM_SERVICE.key);
        if (camundaEngineHost != null && camundaEngineHost.endsWith(CommonConstants.PATH_SEPARATOR)) {
            camundaEngineHost = camundaEngineHost.substring(0, camundaEngineHost.length() - 1);
        }
    }

    @Override
    public String getXML(String procDefId) {
        try {
            ResponseEntity<Map<String, String>> responseEntity = getXMLEntity(procDefId);

            // get response body
            Map<String, String> responseBody = restHelper.getResponseBody(responseEntity);
            if (responseBody != null) {
                return responseBody.get("bpmn20Xml");
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return null;
    }

    @Override
    public ResponseEntity<Map<String, String>> getXMLEntity(String procDefId) {
        long startTime = System.currentTimeMillis();
        log.info("Starting get XML for process-definition-id {}", procDefId);
        String method = "/process-definition/{id}/xml";
        try {
            String url = restHelper.buildUrl(camundaEngineHost, getEngineRestUri(method), getIdParams("id", procDefId), null);

            // call service
            return restHelper.get(url,
                    null,
                    new ParameterizedTypeReference<>() {
                    });
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get XML for process-definition-id {} in {} ms", procDefId, getEndTimeInMs(startTime));
        }

        return null;
    }

    @Override
    public BpmnModelInstance getModelInstance(String procDefId) {
        long startTime = System.currentTimeMillis();
        log.info("Starting get model instance ==> procDefId={}", procDefId);
        try {
            String xml = getXML(procDefId);
            try (InputStream is = FileUtils.getInputStreamFromString(xml)) {
                return Bpmn.readModelFromStream(is);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get model instance ==> procDefId={} in {} ms", procDefId, getEndTimeInMs(startTime));
        }

        return null;
    }

    @Override
    public List<IdentityLink> getIdentityLinks(String taskId) {
        long startTime = System.currentTimeMillis();
        List<IdentityLink> identityLinks = new ArrayList<>();
        log.info("Starting get identity links for task-id {}", taskId);
        String method = "/task/{id}/identity-links";
        try {
            String url = restHelper.buildUrl(camundaEngineHost, getEngineRestUri(method), getIdParams("id", taskId), null);

            // call service
            ResponseEntity<List<IdentityLink>> responseEntity = restHelper.get(url,
                    null,
                    new ParameterizedTypeReference<>() {
                    });

            // get response body
            List<IdentityLink> responseBody = restHelper.getResponseBody(responseEntity);
            if (responseBody != null) {
                identityLinks.addAll(responseBody);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get identity links for task-id {} in {} ms ==> total={}", taskId, getEndTimeInMs(startTime), identityLinks.size());
        }

        return identityLinks;
    }

    @Override
    public void deleteIdentityLink(String taskId, String userId, String groupId, String type) {
        long startTime = System.currentTimeMillis();
        log.info("Starting delete identity links for task-id {}", taskId);
        String method = "/task/{id}/identity-links/delete";
        Map<String, Object> params = new HashMap<>();

        params.put("userId", userId);
        params.put("groupId", groupId);
        params.put("type", type);

        try {
            String url = restHelper.buildUrl(camundaEngineHost, getEngineRestUri(method), getIdParams("id", taskId), null);
            // call service
            restHelper.post(url,
                    null,
                    params,
                    new ParameterizedTypeReference<>() {
                    });
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished delete identity links for task-id {} in {} ms", taskId, getEndTimeInMs(startTime));
        }
    }

    @Override
    public void addIdentityLink(String taskId, String userId, String groupId, String type) {
        long startTime = System.currentTimeMillis();
        log.info("Starting add identity links for task-id {}", taskId);
        String method = "/task/{id}/identity-links";
        Map<String, Object> params = new HashMap<>();

        params.put("userId", userId);
        params.put("groupId", groupId);
        params.put("type", type);

        try {
            String url = restHelper.buildUrl(camundaEngineHost, getEngineRestUri(method), getIdParams("id", taskId), null);
            // call service
            restHelper.post(url,
                    null,
                    params,
                    new ParameterizedTypeReference<>() {
                    });
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished add identity links for task-id {} in {} ms", taskId, getEndTimeInMs(startTime));
        }
    }

    @Override
    public List<SproFlow> getSproFlows(WorkFlowRequest request) {
        long startTime = System.currentTimeMillis();
        List<SproFlow> sproFlows = new ArrayList<>();
        if (request == null) {
            log.info("Get SPRO flow ==> the request object is null");
            return sproFlows;
        }

        String procDefId = request.getProcDefId();
        String procInstId = request.getProcInstId();
        String fromNodeId = request.getFromNodeId();

        log.info("Starting get SPRO flow ==> procDefId={}, procInstId={}, fromNodeId={}", procDefId, procInstId, fromNodeId);
        String method = "/bpmn/work-flow";
        try {
            // get all variable
            request.setVariables(actHiVarInstManager.getVariByTicketAndConvertToMapStringObject(request.getProcInstId()));
            String url = restHelper.buildUrl(camundaEngineHost, method, null, null);

            // call service
            List<SproFlow> response = restHelper.postAndGetBody(url,
                    null,
                    request,
                    new ParameterizedTypeReference<>() {
                    });

            // get response body
//            List<SproFlow> responseBody = restHelper.getResponseBody(responseEntity);
            if (response != null) {
                sproFlows.addAll(response);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get SPRO flow ==> procDefId={}, procInstId={}, fromNodeId={} in {} ms ==> total={}", procDefId, procInstId, fromNodeId, getEndTimeInMs(startTime), sproFlows.size());
        }

        return sproFlows;
    }

    @Override
    public List<SproFlow> getAllSproFlows(WorkFlowRequest request) {
        long startTime = System.currentTimeMillis();
        List<SproFlow> sproFlows = new ArrayList<>();
        if (request == null) {
            log.info("Get SPRO flow ==> the request object is null");
            return sproFlows;
        }

        String procDefId = request.getProcDefId();
        String procInstId = request.getProcInstId();
        String fromNodeId = request.getFromNodeId();

        log.info("Starting get SPRO flow ==> procDefId={}, procInstId={}, fromNodeId={}", procDefId, procInstId, fromNodeId);
        String method = "/bpmn/all-work-flow";
        try {
            String url = restHelper.buildUrl(camundaEngineHost, method, null, null);

            // call service
            List<SproFlow> response = restHelper.postAndGetBody(url,
                    null,
                    request,
                    new ParameterizedTypeReference<>() {
                    });

            // get response body
//            List<SproFlow> responseBody = restHelper.getResponseBody(responseEntity);
            if (response != null) {
                sproFlows.addAll(response);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get SPRO flow ==> procDefId={}, procInstId={}, fromNodeId={} in {} ms ==> total={}", procDefId, procInstId, fromNodeId, getEndTimeInMs(startTime), sproFlows.size());
        }

        return sproFlows;
    }

    @Override
    public String getVariableInstances(String processInstanceIdIn) {
        log.info("Starting get variable instances ==> processInstanceIdIn={}", processInstanceIdIn);
        long startTime = System.currentTimeMillis();
        String result = null;

        String method = "/variable-instance?processInstanceIdIn={processInstanceIdIn}&deserializeValues=false";
        try {
            // call service
            result = restHelper.getAndGetBody(getFullUrlEngineRest(method),
                    null,
                    new ParameterizedTypeReference<>() {
                    },
                    getIdParams("processInstanceIdIn", processInstanceIdIn));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get variable instances ==> processInstanceIdIn={} in {} ms", processInstanceIdIn, getEndTimeInMs(startTime));
        }

        return result;
    }

    @Override
    public Map<String, Object> createDeployment(Resource resource) throws Exception {
        long startTime = System.currentTimeMillis();
        Map<String, Object> dataMap = new HashMap<>();
        if (resource == null) {
            log.info("Create deployment ==> resource is null");
            return dataMap;
        }

        log.info("Starting create deployment, BPMN file: {}", resource.getFilename());

        String method = "/deployment/create";
        try {
            MultiValueMap<String, Object> requestBody = new LinkedMultiValueMap<>();
            requestBody.add("file", resource);

            // call service
            dataMap.putAll(restHelper.postAndGetBody(getFullUrlEngineRest(method),
                    getHttpHeadersMultiPartForm(),
                    requestBody,
                    new ParameterizedTypeReference<Map<String, Object>>() {
                    }));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            throw ex;
        } finally {
            log.info("Finished create deployment, BPMN file: {} in {} ms", resource.getFilename(), getEndTimeInMs(startTime));
        }

        return dataMap;
    }

    @Override
    public List<Map<String, Object>> getVariablesByTypes(String procInstId, List<String> types) {
        long startTime = System.currentTimeMillis();
        List<Map<String, Object>> dataList = new ArrayList<>();
        log.info("Starting get variables by types ==> procInstId={}, types={}", procInstId, types);

        String uri = "/actHiVarInst/getByType/{procInstId}";
        try {
            // call service
            Map<String, Object> dataMap = restHelper.postAndGetBody(getFullUrl(uri),
                    null,
                    types,
                    new ParameterizedTypeReference<>() {
                    },
                    getIdParams("procInstId", procInstId));

            dataList.addAll(getDataListFromResponseMap(dataMap));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get variables by types ==> procInstId={}, types={} in {} ms", procInstId, types, getEndTimeInMs(startTime));
        }

        return dataList;
    }

    @Override
    public List<Map<String, Object>> getVariablesByNames(String procInstId, List<String> names) {
        long startTime = System.currentTimeMillis();
        List<Map<String, Object>> dataList = new ArrayList<>();
        log.info("Starting get variables by names ==> procInstId={}, names={}", procInstId, names);

        String uri = "/actHiVarInst/getByName/{procInstId}/name";
        try {
            // call service
            Map<String, Object> dataMap = restHelper.postAndGetBody(getFullUrl(uri),
                    null,
                    names,
                    new ParameterizedTypeReference<>() {
                    },
                    getIdParams("procInstId", procInstId));

            dataList.addAll(getDataListFromResponseMap(dataMap));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get variables by names ==> procInstId={}, names={} in {} ms", procInstId, names, getEndTimeInMs(startTime));
        }

        return dataList;
    }

    @Override
    public List<Map<String, Object>> getProcessDefinitionByDeploymentId(String deploymentId) {
        long startTime = System.currentTimeMillis();
        List<Map<String, Object>> dataList = new ArrayList<>();

        log.info("Starting get process definition ==> deploymentId={}", deploymentId);

        String method = "/process-definition?deploymentId={deploymentId}";
        try {
            // call service
            dataList.addAll(restHelper.getAndGetBody(getFullUrlEngineRest(method),
                    null,
                    new ParameterizedTypeReference<List<Map<String, Object>>>() {
                    },
                    getIdParams("deploymentId", deploymentId)));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get process definition ==> deploymentId={} in {} ms", deploymentId, getEndTimeInMs(startTime));
        }

        return dataList;
    }

    @Override
    public Map<String, Object> submitForm(String procDefId, StartProcessInstanceDto request) throws Exception {
        long startTime = System.currentTimeMillis();
        Map<String, Object> dataMap = new HashMap<>();

        if (request == null) {
            log.info("Submit process-definition form ==> procDefId={}, request object is null", procDefId);
            return dataMap;
        }

        log.info("Starting submit process-definition form ==> procDefId={}", procDefId);

        String method = "/process-definition/{procDefId}/submit-form";
        try {
            // call service
            dataMap.putAll(restHelper.postAndGetBody(getFullUrlEngineRest(method),
                    null,
                    request,
                    new ParameterizedTypeReference<Map<String, Object>>() {
                    },
                    getIdParams("procDefId", procDefId)));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            throw ex;
        } finally {
            log.info("Finished submit process-definition form ==> procDefId={} in {} ms", procDefId, getEndTimeInMs(startTime));
        }

        return dataMap;
    }

    @Override
    public Map<String, Object> getTaskById(String id) {
        long startTime = System.currentTimeMillis();
        Map<String, Object> dataMap = new HashMap<>();

        log.info("Starting get task ==> id={}", id);

        String method = "/task/{id}";
        try {
            // call service
            dataMap.putAll(restHelper.getAndGetBody(getFullUrlEngineRest(method),
                    null,
                    new ParameterizedTypeReference<Map<String, Object>>() {
                    },
                    getIdParams("id", id)));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get task ==> id={} in {} ms", id, getEndTimeInMs(startTime));
        }

        return dataMap;
    }

    @Override
    public void completeTask(String taskId, CompleteTaskDto variables) throws Exception {
        long startTime = System.currentTimeMillis();
        log.info("Starting complete task ==> id={}", taskId);

        String method = "/task/{id}/complete";
        try {
            // call service
            restHelper.post(getFullUrlEngineRest(method),
                    null,
                    variables,
                    new ParameterizedTypeReference<>() {
                    },
                    getIdParams("id", taskId));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            throw ex;
        } finally {
            log.info("Finished complete task ==> id={} in {} ms", taskId, getEndTimeInMs(startTime));
        }
    }

    @Override
    public void claimTask(String taskId, String userId) {
        long startTime = System.currentTimeMillis();
        log.info("Starting claim task ==> taskId={}", taskId);

        String method = "/task/{id}/claim";
        try {
            Map<String, Object> body = new HashMap<>();
            body.put("userId", userId);

            // call service
            restHelper.post(getFullUrlEngineRest(method),
                    null,
                    body,
                    new ParameterizedTypeReference<>() {
                    },
                    getIdParams("id", taskId));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished claim task ==> taskId={} in {} ms", taskId, getEndTimeInMs(startTime));
        }
    }

    @Override
    public void setTaskAssignee(String taskId, String userId) {
        long startTime = System.currentTimeMillis();
        log.info("Starting set assignee task ==> taskId={}", taskId);

        String method = "/task/{id}/assignee";
        try {
            Map<String, Object> body = new HashMap<>();
            body.put("userId", userId);

            // call service
            restHelper.post(getFullUrlEngineRest(method),
                    null,
                    body,
                    new ParameterizedTypeReference<>() {
                    },
                    getIdParams("id", taskId));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished set assignee task ==> taskId={} in {} ms", taskId, getEndTimeInMs(startTime));
        }
    }

    @Override
    public List<Map<String, Object>> getCurrentRuntimeTasks(String procInstId) {
        long startTime = System.currentTimeMillis();
        List<Map<String, Object>> dataList = new ArrayList<>();
        log.info("Starting get runtime tasks ==> procInstId={}", procInstId);

        String uri = "/actRuTask/ByProcessInstId/{procInstId}";
        try {
            // call service
            Map<String, Object> dataMap = restHelper.getAndGetBody(getFullUrl(uri),
                    null,
                    new ParameterizedTypeReference<>() {
                    },
                    getIdParams("procInstId", procInstId));

            dataList.addAll(getDataListFromResponseMap(dataMap));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get runtime tasks ==> procInstId={} in {} ms", procInstId, getEndTimeInMs(startTime));
        }

        return dataList;
    }

    @Override
    public void modifyProcessInstance(String id, Map<String, Object> body) {
        long startTime = System.currentTimeMillis();
        log.info("Starting modify process instance ==> id={}", id);

        String method = "/process-instance/{id}/modification";
        try {
            // call service
            restHelper.post(getFullUrlEngineRest(method),
                    null,
                    body,
                    new ParameterizedTypeReference<>() {
                    },
                    getIdParams("id", id));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished modify process instance ==> id={} in {} ms", id, getEndTimeInMs(startTime));
        }
    }

    @Override
    public void deleteProcessInstance(String id) {
        long startTime = System.currentTimeMillis();
        log.info("Starting delete process instance ==> id={}", id);

        String method = "/process-instance/{id}";
        try {
            // call service
            restHelper.exchange(getFullUrlEngineRest(method),
                    HttpMethod.DELETE,
                    null,
                    null,
                    new ParameterizedTypeReference<>() {
                    },
                    getIdParams("id", id));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished delete process instance ==> id={} in {} ms", id, getEndTimeInMs(startTime));
        }
    }

    @Override
    public void updateProcessVariables(String id, VariableModification updateData) {
        long startTime = System.currentTimeMillis();
        log.info("Starting update process instance variables ==> id={}", id);

        String method = "/process-instance/{id}/variables";
        try {
            // call service
            restHelper.post(getFullUrlEngineRest(method),
                    null,
                    updateData,
                    new ParameterizedTypeReference<>() {
                    },
                    getIdParams("id", id));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished update process instance variables ==> id={} in {} ms", id, getEndTimeInMs(startTime));
        }
    }

    @Override
    public void updateTaskVariables(String id, VariableModification updateData) {
        long startTime = System.currentTimeMillis();
        log.info("Starting update task variables ==> id={}", id);

        String method = "/task/{id}/variables";
        try {
            // call service
            restHelper.post(getFullUrlEngineRest(method),
                    null,
                    updateData,
                    new ParameterizedTypeReference<>() {
                    },
                    getIdParams("id", id));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished update task variables ==> id={} in {} ms", id, getEndTimeInMs(startTime));
        }
    }

    @Override
    public Map<String, Object> getHiProcInstById(String procInstId) {
        long startTime = System.currentTimeMillis();
        Map<String, Object> dataMap = new HashMap<>();

        log.info("Starting get history process instance ==> procInstId={}", procInstId);

        String uri = "/actHiProcInst/actHiProcInstById/{procInstId}";
        try {
            // call service
            dataMap.putAll(restHelper.getAndGetBody(getFullUrl(uri),
                    null,
                    new ParameterizedTypeReference<Map<String, Object>>() {
                    },
                    getIdParams("procInstId", procInstId)));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get history process instance ==> procInstId={} in {} ms", procInstId, getEndTimeInMs(startTime));
        }

        return dataMap;
    }

    @Override
    public Map<String, SequenceFlowData> calcProcInstExpressions(String procDefId, String procInstId) {
        long startTime = System.currentTimeMillis();
        log.info("Starting calculate process-instance expressions, procDefId={}, procInstId={}", procDefId, procInstId);

        String uri = "/bpmn/calc-proc-inst-expressions";
        try {
            WorkFlowRequest request = new WorkFlowRequest();
            request.setProcDefId(procDefId);
            request.setProcInstId(procInstId);
            request.setVariables(actHiVarInstManager.getVariByTicketAndConvertToMapStringObject(request.getProcInstId()));

            // call service
            Map<String, SequenceFlowData> responseData = restHelper.postAndGetBody(getFullUrl(uri),
                    null,
                    request,
                    new ParameterizedTypeReference<>() {
                    });

            return responseData;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished update task in {} ms", getEndTimeInMs(startTime));
        }

        return null;
    }

    @Override
    public HttpHeaders getHttpHeadersMultiPartForm() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        return headers;
    }

//    @Override
//    public HttpHeaders getHttpHeadersWithToken() {
//        return redirectApiUtils.getHeaders( credentialHelper.getJWTToken());
//    }

    private List<Map<String, Object>> getDataListFromResponseMap(Map<String, Object> responseDataMap) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        if (responseDataMap != null) {
            dataList.addAll((List<Map<String, Object>>) responseDataMap.get(CommonConstants.DATA_KEY));
        }

        return dataList;
    }

    private Map<String, Object> getDataMapFromResponseMap(Map<String, Object> responseDataMap) {
        Map<String, Object> dataMap = new HashMap<>();
        if (responseDataMap != null) {
            dataMap.putAll((Map<String, Object>) responseDataMap.get(CommonConstants.DATA_KEY));
        }

        return dataMap;
    }

    private Map<String, Object> getIdParams(String idKey, String id) {
        Map<String, Object> params = new HashMap<>();
        params.put(idKey, id);

        return params;
    }

    private String getFullUrl(String uri) {
        return camundaEngineHost + uri;
    }

    private String getFullUrlEngineRest(String method) {
        return camundaEngineHost + getEngineRestUri(method);
    }

    private String getEngineRestUri(String method) {
        if (!ValidationUtils.isNullOrEmpty(method)) {
            return "/engine-rest"
                    + (!method.startsWith(CommonConstants.PATH_SEPARATOR) ? CommonConstants.PATH_SEPARATOR : "")
                    + method;
        }

        return method;
    }

    private long getEndTimeInMs(long startTime) {
        return System.currentTimeMillis() - startTime;
    }
}
