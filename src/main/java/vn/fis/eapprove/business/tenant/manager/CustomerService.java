package vn.fis.eapprove.business.tenant.manager;

import org.springframework.http.HttpHeaders;
import vn.fis.eapprove.business.dto.*;
import vn.fis.eapprove.business.model.AccountModel;
import vn.fis.eapprove.business.model.request.ApiManagementShareWithRequest;
import vn.fis.eapprove.business.model.request.ChartNodeDtoRequest;
import vn.fis.eapprove.business.model.request.ListTimeExpectRequest;
import vn.fis.eapprove.business.model.request.PermissionHistoryRequest;
import vn.fis.eapprove.business.model.response.*;
import vn.fis.spro.common.model.response.ChartInfoRoleResponse;
import vn.fis.spro.common.model.response.UserGroupInfoResponse;
import vn.fis.spro.common.model.response.UserInfoResponse;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Author: PhucVM
 * Date: 11/11/2022
 */
public interface CustomerService {

    HttpHeaders getHeaders() ;

    CustomerInfoDto getCustomerInfo(String email);

    List<String> getUserEmails();

    List<String> getCandidateUsersByGroup(String group);

    List<String> listAllUserInfo();

    List<ChartNodeDto> getChartNodeByChartId(Long chartId);

    List<ApiManagementShareWithRequest> listShareWith(ApiManagementShareWithRequest apiManagementShareWithRequest);

    List<ChartNodeDto> getAllDepartmentByCodes(ChartNodeDtoRequest params);

    UserInfoResponse getUserInfo(String username);

    List<UserInfoResponse> getUserInfoList(List<String> emails);

    List<UserGroupInfoResponse> getUserGroupInfoList(List<Long> groupIds);

    Integer checkStartPhase(String procDefId, String taskDefKey);

    List<ChartInfoRoleResponse> getUserByEmail(List<String> username);

    LocalDateTime getTimeSla(long timeRes, LocalDateTime startDate, String emailAssignee);

    UserInfoDto getEmailByUser(String username);

    String[] getAssistantByChartId(String email, String createUser);

    List<Map<String, Object>> listTimeToFeedback(List<ListTimeExpectRequest> listTimeExpectRequests);

    List<TimeExpectResponse> listTimeExpect(List<ListTimeExpectRequest> listTimeExpectRequests);

    List<ChartDto> getAllChartId();

    Long getLocationIdByUserName(String userName);

    List<AccountModel> getAccountByUsernames(List<String> usernames);

    List<AccountModel> getUserTaskInfoByUserNames(List<String> usernames);

    List<UserInfoDto> getUserInfoByChartIdAndCharNodeIds(ChartNodeDtoRequest request);

    Map<String, AccountModel> getAccountMapByUsernames(List<String> usernames);


    List<Map<String, String>> getAccountProcInst(Long chartNodeId);

    String getUserTitle(String username);

    List<ChartDto> getLstChartByUsername(String username);

    String getConfigSlaShift(String username);

    UserInfoResponse getCharNodeIds(String username);

    List<UserTitleResponse> getUserTitleByUserName(String username);

    UserInfoDto changeStatusHandOver(String username);

    List<Map<String, Object>> findCompanyCodeByUsername(String username);
    List<Map<String, Object>> getAllByCompanyCodes(String username);

    List<String> listChildCompanyCodeByParentCode(List<String> parentCodes);

    List<NameAndCodeCompanyResponse> responseCompanyCodeAndName(String username);

    List<NameAndCodeCompanyResponse> responseCompanyCodeAndNames(List<String> username);

    List<NameAndCodeCompanyResponse> findAllCompanyCodeAndNameByCompanyCode(List<String> companyCode);

    UserInfoByUsername getUserInfoByUsername(String username);

    AssigneeInfoResponse getAssigneeInfo(String username);

    List<String> getUserDefault(String username, String status);

    // System group
    Object getAllSystemGroupChart(String type);

    Object getAllSystemGroupBudget();

    Object getAllSystemGroupSystemConfig();

    Object getAllPosition();

    Object getAllJobTitle();

    List<Long> getChartNodeIdsByUsernames(Set<String> usernames);

    List<String> getListUsernameActive(List<String> lstUsername);

    AssigneeInfoDto getAssigneeInfoByListUser(Set<String> usernames);

    List<UserInfoByUsername> getInfoByListUser(Set<String> usernames);

    List<Map<String, Object>> getCompanyCodeByChartNodeIdIn(List<String> chartNodeIds);

    String getDefaultSignatureByUsername(String username);

    void savePermissionHistory(PermissionHistoryRequest request);

    Map<String, String> getMapNameShareTicketDetail(List<String> sharedUsers,
                                                    List<String> companyCodes,
                                                    List<String> chartNodeCodes,
                                                    List<String> createdUsers,
                                                    List<String> assignees);

}
