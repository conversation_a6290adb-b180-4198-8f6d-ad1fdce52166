package vn.fis.eapprove.business.tenant.manager;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.fis.spro.common.constants.FilterDataEnum;

import java.util.List;

@Service
public class BusinessManager {
    @Autowired
    BusinessService businessService;

    public <T> Object getFilterData(List<T> allData, FilterDataEnum tableName) throws IllegalAccessException {
        return businessService.getFilterData(allData,tableName);
    }
}
