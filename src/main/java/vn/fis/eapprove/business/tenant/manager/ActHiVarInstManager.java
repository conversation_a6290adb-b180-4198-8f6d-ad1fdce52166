package vn.fis.eapprove.business.tenant.manager;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.entity.BpmVariables;
import vn.fis.eapprove.business.domain.bpm.repository.BpmHistoryRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTaskRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmVariablesRepository;
import vn.fis.eapprove.business.domain.bpm.service.BpmHistoryManager;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcInstManager;
import vn.fis.eapprove.business.domain.bpm.service.BpmTpSignZoneManager;
import vn.fis.eapprove.business.dto.BpmTpSignZoneDto;
import vn.fis.eapprove.business.model.request.ActHiVarInstRequest;
import vn.fis.eapprove.business.model.request.CompleteTaskDto;
import vn.fis.eapprove.business.model.request.VariableValueDto;
import vn.fis.eapprove.business.model.response.TicketDefaultResponse;
import vn.fis.eapprove.business.model.response.VariablesResponse;
import vn.fis.eapprove.business.specification.BpmVariablesSpecification;

import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class ActHiVarInstManager {

    @Autowired
    private BpmVariablesRepository bpmVariablesRepo;
    @Autowired
    private BpmTaskRepository bpmTaskRepository;
    @Autowired
    private BpmVariablesSpecification variSpecification;
    @Autowired
    private ResponseUtils responseUtils;
    @Autowired
    private BpmHistoryManager bpmHistoryManager;
    @Autowired
    @Lazy
    private BpmProcInstManager bpmProcInstManager;
    @Autowired
    private BpmHistoryRepository bpmHistoryRepository;
    @Autowired
    private BpmTpSignZoneManager bpmTpSignZoneManager;

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getVariableByType(String procInstId, List<String> type) {
        List<BpmVariables> variables = bpmVariablesRepo.findAll(variSpecification.filterByProcInstId(procInstId));
        return getResponseVariables(variables);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getVariableByName(String procInstId, List<String> name) {
        List<BpmVariables> variables = bpmVariablesRepo.findAll(variSpecification.filterByName(name, procInstId, false));
        return getResponseVariables(variables);
    }

    public List<Map<String, Object>> getResponseVariables(List<BpmVariables> variables) {
        if (!variables.isEmpty()) {
            return variables.stream().map(x -> {
                Map<String, Object> mapData = new HashMap<>();
                mapData.put("name", x.getName());
                mapData.put("taskId", x.getTaskId());
                mapData.put("type", x.getType());
                mapData.put("additionalVal", x.getAdditionalVal());
                switch (x.getType().toUpperCase()) {
                    case "STRING":
                        mapData.put("value", x.getStringVal());
                        break;
                    case "DOUBLE":
                        mapData.put("value", x.getDoubleVal());
                        break;
                    case "LONG":
                    case "INTEGER":
                        mapData.put("value", x.getLongVal());
                        break;
                    case "JSON":
                        mapData.put("value", x.getJsonVal());
                        break;
                    case "FILE":
                        mapData.put("value", x.getDownloadUrl());
                        break;
                }
                return mapData;
            }).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    public List<Map<String, VariableValueDto>> convertResponseVariablesToVariableValueDto(List<BpmVariables> variables) {
        if (!variables.isEmpty()) {
            return variables.stream().map(x -> {
                Map<String, VariableValueDto> mapData = new HashMap<>();
                VariableValueDto variableValueDto = new VariableValueDto();
                variableValueDto.setType(StringUtils.capitalize(x.getType() == null ? "" : x.getType().trim().toLowerCase()));

                // (phucvm3) additional values
                if (!ValidationUtils.isNullOrEmpty(x.getAdditionalVal()) && ObjectUtils.isValidJSON(x.getAdditionalVal())) {
                    variableValueDto.setValueInfo(ObjectUtils.toObject(x.getAdditionalVal(), new TypeReference<>() {
                    }));
                }

                switch (x.getType().toUpperCase()) {
                    case "STRING":
                        variableValueDto.setValue(x.getStringVal());
                        break;
                    case "DOUBLE":
                        variableValueDto.setValue(x.getDoubleVal());
                        break;
                    case "LONG":
                    case "INTEGER":
                        variableValueDto.setValue(x.getLongVal());
                        break;
                    case "JSON":
                        variableValueDto.setValue(x.getJsonVal());
                        break;
                    case "FILE":
                        variableValueDto.setValue(x.getDownloadUrl());
                        Map<String, Object> valueInfo = new HashMap<>();
                        valueInfo.put("filename", x.getDownloadUrl());
                        valueInfo.put("mimeType", "");

                        // (phucvm3) additional values
                        if (!ValidationUtils.isNullOrEmpty(variableValueDto.getValueInfo())) {
                            variableValueDto.getValueInfo().putAll(valueInfo);
                        } else {
                            variableValueDto.setValueInfo(valueInfo);
                        }

                        break;
                }
                mapData.put(x.getName(), variableValueDto);
                return mapData;
            }).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    public Map<String, VariableValueDto> convertResponseVariablesToMap(List<BpmVariables> variables) {
        Map<String, VariableValueDto> variableValueDtoMap = new HashMap<>();
        if (!variables.isEmpty()) {
            variables.forEach(x -> {
                VariableValueDto variableValueDto = new VariableValueDto();
                variableValueDto.setType(StringUtils.capitalize(x.getType() == null ? "" : x.getType().trim().toLowerCase()));

                // (phucvm3) additional values
                if (!ValidationUtils.isNullOrEmpty(x.getAdditionalVal()) && ObjectUtils.isValidJSON(x.getAdditionalVal())) {
                    variableValueDto.setValueInfo(ObjectUtils.toObject(x.getAdditionalVal(), new TypeReference<>() {
                    }));
                }

                switch (x.getType().toUpperCase()) {
                    case "STRING":
                        variableValueDto.setValue(x.getStringVal());
                        break;
                    case "DOUBLE":
                        variableValueDto.setValue(x.getDoubleVal());
                        break;
                    case "LONG":
                    case "INTEGER":
                        variableValueDto.setValue(x.getLongVal());
                        break;
                    case "JSON":
                        variableValueDto.setValue(x.getJsonVal());
                        break;
                    case "FILE":
                        variableValueDto.setValue(x.getDownloadUrl());
                        Map<String, Object> valueInfo = new HashMap<>();
                        valueInfo.put("filename", x.getDownloadUrl());
                        valueInfo.put("mimeType", "");

                        // (phucvm3) additional values
                        if (!ValidationUtils.isNullOrEmpty(variableValueDto.getValueInfo())) {
                            variableValueDto.getValueInfo().putAll(valueInfo);
                        } else {
                            variableValueDto.setValueInfo(valueInfo);
                        }

                        break;
                }
                variableValueDtoMap.put(x.getName(), variableValueDto);
            });
        }
        return variableValueDtoMap;
    }

    public Map<String, Object> convertResponseVariablesToMapStringObject(List<BpmVariables> variables) {
        Map<String, Object> variableValueDtoMap = new HashMap<>();
        if (!variables.isEmpty()) {
            variables.forEach(x -> {
                switch (x.getType().toUpperCase()) {
                    case "STRING":
                        variableValueDtoMap.put(x.getName(), x.getStringVal());
                        break;
                    case "DOUBLE":
                        variableValueDtoMap.put(x.getName(), x.getDoubleVal());
                        break;
                    case "LONG":
                    case "INTEGER":
                        variableValueDtoMap.put(x.getName(), x.getLongVal());
                        break;
                    case "JSON":
                        variableValueDtoMap.put(x.getName(), x.getJsonVal());
                        break;
                    case "FILE":
                        variableValueDtoMap.put(x.getName(), x.getDownloadUrl());
                        break;
                }
            });
        }
        return variableValueDtoMap;
    }

    public List<Map<String, Object>> getVariByName(List<String> listName, String procInstId, Boolean isRu) {
        try {
            List<BpmVariables> variables = bpmVariablesRepo.findAll(variSpecification.filterByName(listName, procInstId, isRu));
            return getResponseVariables(variables);
        } catch (Exception e) {
            return null;
        }
    }

    public List<Map<String, Object>> getVariByTicket(String procInstId) {
        try {
            List<BpmVariables> variables = bpmVariablesRepo.findAll(variSpecification.filterByProcInstId(procInstId));
            return getResponseVariables(variables);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public List<Map<String, VariableValueDto>> getVariByTicketConvertToVariableValueDto(String procInstId) {
        try {
            List<BpmVariables> variables = bpmVariablesRepo.findAll(variSpecification.filterByProcInstId(procInstId, false));
            return convertResponseVariablesToVariableValueDto(variables);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Map<String, VariableValueDto> getVariByTicketAndConvertToMap(String procInstId) {
        try {
            List<BpmVariables> variables = bpmVariablesRepo.findAll(variSpecification.filterByProcInstId(procInstId, false));
            return convertResponseVariablesToMap(variables);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Map<String, Object> getVariByTicketAndConvertToMapStringObject(String procInstId) {
        try {
            List<BpmVariables> variables = bpmVariablesRepo.findAll(variSpecification.filterByProcInstId(procInstId, false));
            return convertResponseVariablesToMapStringObject(variables);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Map<String, Object> getVariByTask(String taskId, String type) {
        try {
            BpmTask task = bpmTaskRepository.getBpmTaskByTaskId(taskId);
            Map<String, Object> mapFinal = new HashMap<>();
            List<String> listSignTask = new ArrayList<>();
            List<String> listTaskKey = new ArrayList<>();
            List<String> listOutKey = new ArrayList<>();
            List<String> startEvent = new ArrayList<>();
            Boolean isStart = false;
            //------------------------------------------------//
            Document doc = responseUtils.getXml(task.getTaskProcDefId());
            NodeList nodes = doc.getElementsByTagName("bpmn:userTask");
            NodeList nodeGateway = doc.getElementsByTagName("bpmn:parallelGateway");
            NodeList nodeSequence = doc.getElementsByTagName("bpmn:sequenceFlow");
            NodeList nodeStart = doc.getElementsByTagName("bpmn:startEvent");
            if (type.equalsIgnoreCase("input")) {
                for (int i = 0; i < nodes.getLength(); i++) {
                    Element nodeElement = (Element) nodes.item(i);
                    String taskKey = nodeElement.getAttributes().getNamedItem("id").getNodeValue();
                    if (taskKey.equals(task.getTaskDefKey())) {
                        NodeList nodeIn = nodeElement.getElementsByTagName("bpmn:incoming");
                        for (int j = 0; j < nodeIn.getLength(); j++) {
                            Element eleIn = (Element) nodeIn.item(j);
                            listOutKey.add(eleIn.getTextContent());
                        }
                    }
                }
                getInTaskKey(listOutKey, nodes, nodeGateway, nodeSequence, nodeStart, listTaskKey, startEvent);
                listTaskKey.add(task.getTaskDefKey());
            } else {
                getOutTaskKey(listOutKey, nodes, nodeGateway, nodeSequence, nodeStart, listSignTask, startEvent);
                listTaskKey.add(task.getTaskDefKey());
            }
            if (!startEvent.isEmpty()) {
                isStart = true;
            }
            //--------------------------------------------//
            List<BpmVariables> variables = bpmVariablesRepo.findAll(variSpecification.filterByInput(task.getTaskProcInstId(), listTaskKey, isStart));
            if (!variables.isEmpty()) {
                mapFinal.put("listVariables", getResponseVariables(variables));
            }
            return mapFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public void getInTaskKey(List<String> listOutKey, NodeList nodes, NodeList nodeGateway,
                             NodeList nodeSequence, NodeList nodeStart, List<String> listTaskKey, List<String> listStart) {
        try {
            Element elStart = (Element) nodeStart.item(0);
            String startKey = elStart.getAttributes().getNamedItem("id").getNodeValue();
            if (listOutKey.contains(startKey)) {
                listOutKey.remove(startKey);
                listStart.add(startKey);
            }
            for (int i = 0; i < nodes.getLength(); i++) {
                Element nodeElement = (Element) nodes.item(i);
                String taskKey = nodeElement.getAttributes().getNamedItem("id").getNodeValue();
                if (listOutKey.contains(taskKey)) {
                    listOutKey.remove(taskKey);
                    listTaskKey.add(taskKey);
                    if (!listOutKey.isEmpty()) {
                        getInTaskKey(listOutKey, nodes, nodeGateway, nodeSequence, nodeStart, listTaskKey, listStart);
                    }
                }
            }
            for (int i = 0; i < nodeSequence.getLength(); i++) {
                Element element = (Element) nodeSequence.item(i);
                String seqKey = element.getAttributes().getNamedItem("id").getNodeValue();
                String tarRef = element.getAttributes().getNamedItem("targetRef").getNodeValue();
                String sourceRef = element.getAttributes().getNamedItem("sourceRef").getNodeValue();
                if (listOutKey.contains(seqKey) || listOutKey.contains(tarRef)) {
                    listOutKey.remove(seqKey);
                    listOutKey.remove(tarRef);
                    listOutKey.add(sourceRef);
                    getInTaskKey(listOutKey, nodes, nodeGateway, nodeSequence, nodeStart, listTaskKey, listStart);
                }
            }
            for (int i = 0; i < nodeGateway.getLength(); i++) {
                Element nodeElement = (Element) nodeGateway.item(i);
                String taskKey = nodeElement.getAttributes().getNamedItem("id").getNodeValue();
                if (listOutKey.contains(taskKey)) {
                    NodeList gateOut = nodeElement.getElementsByTagName("bpmn:incoming");
                    listOutKey.remove(taskKey);
                    for (int j = 0; j < gateOut.getLength(); j++) {
                        Element addedKey = (Element) gateOut.item(j);
                        String addKeyStr = addedKey.getTextContent();
                        listOutKey.add(addKeyStr);
                    }
                    getInTaskKey(listOutKey, nodes, nodeGateway, nodeSequence, nodeStart, listTaskKey, listStart);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void getOutTaskKey(List<String> listOutKey, NodeList nodes, NodeList nodeGateway,
                              NodeList nodeSequence, NodeList nodeStart, List<String> listTaskKey, List<String> listStart) {
        try {
            Element elStart = (Element) nodeStart.item(0);
            String startKey = elStart.getAttributes().getNamedItem("id").getNodeValue();
            if (listOutKey.contains(startKey)) {
                listOutKey.remove(startKey);
                listStart.add(startKey);
            }
            for (int i = 0; i < nodes.getLength(); i++) {
                Element nodeElement = (Element) nodes.item(i);
                String taskKey = nodeElement.getAttributes().getNamedItem("id").getNodeValue();
                if (listOutKey.contains(taskKey)) {
                    listOutKey.remove(taskKey);
                    listTaskKey.add(taskKey);
                    if (!listOutKey.isEmpty()) {
                        getOutTaskKey(listOutKey, nodes, nodeGateway, nodeSequence, nodeStart, listTaskKey, listStart);
                    }
                }
            }
            for (int i = 0; i < nodeSequence.getLength(); i++) {
                Element element = (Element) nodeSequence.item(i);
                String seqKey = element.getAttributes().getNamedItem("id").getNodeValue();
                String tarRef = element.getAttributes().getNamedItem("targetRef").getNodeValue();
                String sourceRef = element.getAttributes().getNamedItem("sourceRef").getNodeValue();
                if (listOutKey.contains(seqKey) || listOutKey.contains(sourceRef)) {
                    listOutKey.remove(seqKey);
                    listOutKey.remove(sourceRef);
                    listOutKey.add(tarRef);
                    getOutTaskKey(listOutKey, nodes, nodeGateway, nodeSequence, nodeStart, listTaskKey, listStart);
                }
            }
            for (int i = 0; i < nodeGateway.getLength(); i++) {
                Element nodeElement = (Element) nodeGateway.item(i);
                String taskKey = nodeElement.getAttributes().getNamedItem("id").getNodeValue();
                if (listOutKey.contains(taskKey)) {
                    NodeList gateOut = nodeElement.getElementsByTagName("bpmn:outgoing");
                    listOutKey.remove(taskKey);
                    for (int j = 0; j < gateOut.getLength(); j++) {
                        Element addedKey = (Element) gateOut.item(j);
                        String addKeyStr = addedKey.getTextContent();
                        listOutKey.add(addKeyStr);
                    }
                    getOutTaskKey(listOutKey, nodes, nodeGateway, nodeSequence, nodeStart, listTaskKey, listStart);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public CompleteTaskDto getCompleteTaskVari(String procInstId, String taskId) {
        try {
            CompleteTaskDto completeTaskDto = new CompleteTaskDto();
            Map<String, VariableValueDto> mapVari = new HashMap<>();
            List<BpmVariables> listVariables =  bpmVariablesRepo.findAll(variSpecification.filterComplete(procInstId, taskId));
            if (!listVariables.isEmpty()) {
                for (BpmVariables variable : listVariables) {
                    VariableValueDto valueDto = new VariableValueDto();
                    switch (variable.getType().toLowerCase()) {
                        case "integer":
                            valueDto.setType("integer");
                            valueDto.setValue(variable.getLongVal());
                            break;
                        case "long":
                            valueDto.setType("long");
                            valueDto.setValue(variable.getLongVal());
                            break;
                        case "double":
                            valueDto.setType("double");
                            valueDto.setValue(variable.getDoubleVal());
                            break;
                        case "float":
                            valueDto.setType("float");
                            valueDto.setValue(variable.getDoubleVal());
                            break;
                        case "string":
                            valueDto.setType("string");
                            valueDto.setValue(variable.getStringVal());
                            break;
                    }
                    mapVari.put(variable.getName(), valueDto);
                }
            }
            completeTaskDto.setVariables(mapVari);
            completeTaskDto.setWithVariablesInReturn(false);
            return completeTaskDto;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return new CompleteTaskDto();
        }
    }

    public Map<String, Object> getByIncomingTask(String procInstId, String incomingTaskKey) {
        try {
            Map<String, Object> mapFinal = new HashMap<>();
            List<BpmVariables> variables = bpmVariablesRepo.getAllByIncomingTaskDefKey(procInstId, incomingTaskKey);
            if (!variables.isEmpty()) {
                mapFinal.put("listVariables", getResponseVariables(variables));
            }

            return mapFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public VariablesResponse listVariablesByRu(ActHiVarInstRequest request) {
        try {
            VariablesResponse variablesResponse = new VariablesResponse();
            if (request.isStart) {
                List<BpmVariables> currentVariablesProcInst = bpmVariablesRepo.getListVariablesByProcInstId(request.getProcInstId());
                List<BpmVariables> oldVariablesProcInst = bpmVariablesRepo.getListVariablesByProcInstId(request.getOldProcInstId());
                List<Map<String, Object>> currentProcInst = getResponseVariables(currentVariablesProcInst);
                List<Map<String, Object>> oldProcInst = getResponseVariables(oldVariablesProcInst);
                variablesResponse.setCurrentProcInst(currentProcInst);
                variablesResponse.setOldProcInst(oldProcInst);

                if (!ValidationUtils.isNullOrEmpty(request.getTicketId())) {
                    TicketDefaultResponse newDefaultField = bpmProcInstManager.getDefaultByProcInstId(request.ticketId);
                    variablesResponse.setNewDefaultField(newDefaultField);
                }

                TicketDefaultResponse oldDefaultField = bpmHistoryManager.getOldTicketDefaultField(request.ticketId, request.oldProcInstId);
                variablesResponse.setOldDefaultField(oldDefaultField);
            } else {
                List<BpmVariables> currentVariablesTask = bpmVariablesRepo.getListVariablesByTaskId(request.getTaskId());
                List<BpmVariables> oldVariablesTask = bpmVariablesRepo.getListVariablesByTaskId(request.getOldTaskId());
                List<Map<String, Object>> currentTask = getResponseVariables(currentVariablesTask);
                List<Map<String, Object>> oldTask = getResponseVariables(oldVariablesTask);
                variablesResponse.setCurrentTask(currentTask);
                variablesResponse.setOldTask(oldTask);

                request.setOldProcInstId(bpmHistoryRepository.getProcInstIdByTicketIdAndTaskInstId(request.getTicketId(), request.getOldTaskId()));
                request.setProcInstId(bpmHistoryRepository.getProcInstIdByTicketIdAndTaskInstId(request.getTicketId(), request.getTaskId()));
            }

            if (!ValidationUtils.isNullOrEmpty(request.getTicketId())) {
                BpmProcInst bpmProcInst = bpmProcInstManager.findById(request.getTicketId());
                List<BpmTpSignZoneDto> oldDto = bpmTpSignZoneManager.findAll(request.getOldProcInstId(), bpmProcInst.getTicketStartActId());
                List<BpmTpSignZoneDto> newDto = bpmTpSignZoneManager.findAll(request.getProcInstId(), bpmProcInst.getTicketStartActId());
                if (!ValidationUtils.isNullOrEmpty(oldDto)) {
                    variablesResponse.setOldSignedFile(oldDto.get(0).getSignedFile());
                    variablesResponse.setOldSignedFileName(oldDto.get(0).getSignedFileName());
                }
                if (!ValidationUtils.isNullOrEmpty(newDto)) {
                    variablesResponse.setNewSignedFile(newDto.get(0).getSignedFile());
                    variablesResponse.setNewSignedFileName(newDto.get(0).getSignedFileName());
                }
            }

            return variablesResponse;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }

    }

    public CompleteTaskDto getAutoCompleteVariable(String procInstId, String taskId, String oldTaskDefKey, String newTaskDefKey,
                                                   Boolean oldTaskIsMultiInstance, Boolean newTaskIsMultiInstance, String account
    ) {
        try {
            CompleteTaskDto completeTaskDto = new CompleteTaskDto();
            Map<String, VariableValueDto> mapVari = new HashMap<>();
            List<BpmVariables> listVariables = bpmVariablesRepo.findAll(variSpecification.filterComplete(procInstId, taskId));
            if (!listVariables.isEmpty()) {
                for (BpmVariables variable : listVariables) {
                    VariableValueDto valueDto = new VariableValueDto();
                    Map<String, Object> valueInfo = variable.getAdditionalVal() != null ? ObjectUtils.toObject(variable.getAdditionalVal(), new TypeReference<>() {}) : null;
                    switch (variable.getType().toLowerCase()) {
                        case "integer":
                            valueDto.setType("integer");
                            valueDto.setValue(variable.getLongVal());
                            valueDto.setValueInfo(valueInfo);
                            break;
                        case "long":
                            valueDto.setType("long");
                            valueDto.setValue(variable.getLongVal());
                            valueDto.setValueInfo(valueInfo);
                            break;
                        case "double":
                            valueDto.setType("double");
                            valueDto.setValue(variable.getDoubleVal());
                            valueDto.setValueInfo(valueInfo);
                            break;
                        case "float":
                            valueDto.setType("float");
                            valueDto.setValue(variable.getDoubleVal());
                            valueDto.setValueInfo(valueInfo);
                            break;
                        case "string":
                            valueDto.setType("string");
                            valueDto.setValue(variable.getStringVal());
                            valueDto.setValueInfo(valueInfo);
                            break;
                        case "file":
                            valueDto.setType("file");
                            valueDto.setValue(variable.getDownloadUrl());
                            valueDto.setValueInfo(valueInfo);
                            break;
                        case "json":
                            valueDto.setType("json");
                            valueDto.setValue(variable.getJsonVal());
                            valueDto.setValueInfo(valueInfo);
                            break;
                    }
                    String name = replaceNewVariableName(variable.getName(), oldTaskDefKey, newTaskDefKey, oldTaskIsMultiInstance, newTaskIsMultiInstance, account);
                    mapVari.put(name, valueDto);
                }
            }
            completeTaskDto.setVariables(mapVari);
            completeTaskDto.setWithVariablesInReturn(false);
            return completeTaskDto;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return new CompleteTaskDto();
        }
    }

    private String replaceNewVariableName(String oldName, String oldTaskDefKey, String newTaskDefKey, Boolean oldTaskIsMultiInstance, Boolean newTaskIsMultiInstance, String account) {
        String newName;
        if (oldTaskIsMultiInstance && !newTaskIsMultiInstance) {
            String replaceString = "zoom_" + account + "_" + oldTaskDefKey;
            newName = StringUtils.replace(oldName, replaceString, newTaskDefKey);
        } else if (!oldTaskIsMultiInstance && newTaskIsMultiInstance) {
            String replaceString = "zoom_" + account + "_" + newTaskDefKey;
            newName = StringUtils.replace(oldName, oldTaskDefKey, replaceString);
        } else {
            newName = StringUtils.replace(oldName, oldTaskDefKey, newTaskDefKey);
        }

        return newName;
    }
}
