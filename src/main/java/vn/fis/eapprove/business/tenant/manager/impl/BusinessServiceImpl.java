package vn.fis.eapprove.business.tenant.manager.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.tenant.manager.BusinessService;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.helper.RestHelper;

import java.lang.reflect.Field;
import java.util.*;

@Service
@Slf4j
public class BusinessServiceImpl implements BusinessService {

    private final RestHelper restHelper;

    public BusinessServiceImpl(RestHelper restHelper) {
        this.restHelper = restHelper;
    }

    private long getEndTimeInMs(long startTime) {
        return System.currentTimeMillis() - startTime;
    }
    @Override
    public <T> Object   getFilterData(List<T> allData, FilterDataEnum tableName) throws IllegalAccessException {
        Map<String, Set<Object>> result = new HashMap<>();
        List<String> excludeList = tableName!= null ? getExcludeList(tableName) : new ArrayList<>();
        if (!allData.isEmpty()) {
            for (T data : allData) {
                if (data instanceof HashMap) {
                    processMapData((Map<?, ?>) data,excludeList,result);
                } else {
                    processObjectData(data,excludeList,result);
                }
            }
        }
        return result;
    }

    private Object convertStringToList(Object input) {
        try {
            if (input instanceof String && ((String) input).contains("[")) {
                String cleanString = ((String) input).replaceAll("\\\"\\]|\\[\\\"|\\[|\\]", "");

                String[] stringArray = cleanString.split(",");

                List<String> result = new ArrayList<>();
                for (String str : stringArray) {
                    result.add(str.trim().replaceAll("\"", ""));
                }
                return result;
            }
            return input;
        }catch (Exception e){
            return input;
        }
    }
    private void processFieldValue(String fieldName, Object value, Map<String, Set<Object>> result) {
        value = convertStringToList(value);
        if (value instanceof List) {
            result.computeIfAbsent(fieldName, k -> new HashSet<>()).addAll((Collection<?>) value);
        } else {
            result.computeIfAbsent(fieldName, k -> new HashSet<>()).add(value);
        }
    }


    private void processObjectData(Object data, List<String> excludeList, Map<String, Set<Object>> result) throws IllegalAccessException {
        List<Field> validFields = new ArrayList<>();
        Field[] fields = data.getClass().getDeclaredFields();

        for (Field field : fields) {
            if (!excludeList.contains(field.getName())) {
                field.setAccessible(true);
                Object value = field.get(data);

                // Lấy ra danh sách tên hợp lệ
                if (value instanceof String || value instanceof Number || value instanceof List) {
                    validFields.add(field);
                }
            }
        }

        for (Field validField : validFields) {
            validField.setAccessible(true);
            String fieldName = validField.getName();
            Object value = validField.get(data);
            processFieldValue(fieldName, value, result);
        }
    }
    private void processMapData(Map<?, ?> data, List<String> excludeList, Map<String, Set<Object>> result) {
        List<String> validFields = new ArrayList<>();

        for (Map.Entry<?, ?> entry : data.entrySet()) {
            if (!excludeList.contains(entry.getKey().toString())) {
                Object value = entry.getValue();

                // Lấy ra danh sách tên hợp lệ
                if (value instanceof String || value instanceof Number || value instanceof List) {
                    validFields.add(entry.getKey().toString());
                }
            }
        }

        for (String validField : validFields) {
            Object value = data.get(validField);
            processFieldValue(validField, value, result);
        }
    }

    public List<String> getExcludeList(FilterDataEnum tableName) {
        List<String> defaultExclude = new ArrayList<>(Arrays.asList("id", "body", "header", "procDefId","specialCompanyCode",
                "serialVersionUID","serviceCount","specialParentId","createdDate","updatedDate"));
        List<String> excludeList = new ArrayList<>();

        switch (tableName) {
            case TEMPLATE_MANAGER:
                excludeList = Arrays.asList("template");
                break;
            case BPM_PROC_DEF:
                excludeList = Arrays.asList("specialFormKey","showInputTaskDefKeys","hideInheritTasks",
                        "deploymentId","autoClose","prioritized","authorityOnTicketStep","hideCommentTasks"
                        ,"hideRuTasks","showInputTaskDefKeys","changeImplementerValue","hideInfoTasks","specialParentServiceId",
                        "hideDownloadTasks","authorityOnTicketValue","hideRelatedTicketValue","priorityId","showInfoTaks","processType","autoCancel","serviceNamePopUp","key");
                break;
//            case API_MANAGEMENT:
//                break;
//            case BPM_TEMPLATE_PRINT:
//                excludeList = Arrays.asList("historyChange","pdfContent");
//                break;
//            case SERVICE_PACKAGE:
//                excludeList = Arrays.asList("positionPackage","masterParentId","hideChart","visibleChart","visibleGroup","icon","parentCompanyCode","hideName","parentId","specialParentId","visibleName");
//                break;
            default:
                break;
        }
        defaultExclude.addAll(new ArrayList<>(excludeList));
        return defaultExclude;
    }

    public List<String> getAcceptList(FilterDataEnum tableName) {
        List<String> acceptList = new ArrayList<>();

        switch (tableName) {
            case TEMPLATE_MANAGER:
                acceptList = Arrays.asList("urlName", "modifiedUser", "templateName", "createdUser");
                break;
            case BPM_PROC_DEF:
                acceptList = Arrays.asList("userUpdate", "resourceName", "name", "userCreated");
                break;
            case API_MANAGEMENT:
                acceptList = Arrays.asList("name", "url", "updatedUser", "createdUser", "description");
                break;
            case BPM_TEMPLATE_PRINT:
                acceptList = Arrays.asList("processName", "updatedUser", "createdUser");
                break;
            case SERVICE_PACKAGE:
                acceptList = Arrays.asList("positionPackage", "masterParentId", "hideChart", "visibleChart", "visibleGroup", "icon", "parentCompanyCode", "hideName", "parentId", "specialParentId", "visibleName");
                break;
            default:
                break;
        }
        return acceptList;
    }
}
