package vn.fis.eapprove.business.tenant.manager;

import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fis.eapprove.business.domain.authority.service.AuthService;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.repository.ShareUserRepository;
import vn.fis.eapprove.business.domain.system.repository.SystemGroupRepository;
import vn.fis.eapprove.business.model.request.MasterDataRequest;
import vn.fis.eapprove.business.model.response.MasterDataPermitResponse;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ShareUserTypeEnum;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class MasterDataManager {
    @Autowired
    ResponseUtils responseUtils;

    @Autowired
    ModelMapper modelMapper;

    @Autowired
    Common common;

    @Autowired
    CustomerService customerService;

    @Autowired
    private ShareUserRepository shareUserRepository;
    @Autowired
    private PermissionDataManagementRepository permissionDataManagementRepository;
    @Autowired
    private AuthService authService;
    @Autowired
    private SystemGroupRepository systemGroupRepository;

    public Boolean savePermitMD(MasterDataRequest masterDataRequest, String account) {
        try {

            // Lưu phân quyền dữ liệu
            List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(masterDataRequest.getId(), PermissionDataConstants.Type.MASTER_DATA.code);
            if (!ValidationUtils.isNullOrEmpty(oldData)) {
                permissionDataManagementRepository.deleteAll(oldData);
            }
            // Lưu phân quyền dữ liệu
            if (!ValidationUtils.isNullOrEmpty(masterDataRequest.getApplyFor())) {
                List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                for (String data : masterDataRequest.getApplyFor()) {
                    PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                    permissionDataManagement.setTypeId(masterDataRequest.getId());
                    permissionDataManagement.setTypeName(PermissionDataConstants.Type.MASTER_DATA.code);
                    permissionDataManagement.setCompanyCode(data);
                    permissionDataManagement.setCreatedUser(account);
                    permissionDataManagement.setCreatedTime(LocalDateTime.now());
                    permissionDataManagements.add(permissionDataManagement);
                }

                permissionDataManagementRepository.saveAll(permissionDataManagements);
            }

            shareUserRepository.deleteAllByReferenceIdAndReferenceType(masterDataRequest.getId(), ShareUserTypeEnum.MASTERDATA.type);
            // Share user
            if (!ValidationUtils.isNullOrEmpty(masterDataRequest.getShareWith())) {
                List<SharedUser> sharedUsers = new ArrayList<>();
                for (String shareWith : masterDataRequest.getShareWith()) {
                    SharedUser sharedUser = new SharedUser();
                    sharedUser.setReferenceId(masterDataRequest.getId());
                    sharedUser.setReferenceType(ShareUserTypeEnum.MASTERDATA.type);
                    sharedUser.setEmail(shareWith);
                    sharedUsers.add(sharedUser);
                }
                shareUserRepository.saveAll(sharedUsers);
            }

            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public MasterDataPermitResponse getPermitMD(MasterDataRequest masterDataRequest, String account) {
        try {
            MasterDataPermitResponse response = new MasterDataPermitResponse();
            //List companyCode
            List<String> lstCompanyCode = authService.getListCompanyCodeByUser(account);
            response.setCompanyCodes(lstCompanyCode);

            // Phân quyền theo nhóm
            List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.MASTER_DATA.tableName, account);
            response.setGroupPermitId(lstGroupPermissionId);

            // Lấy list phân quyền dữ liệu
            List<PermissionDataManagement> permissionDataManagements = permissionDataManagementRepository.getPermissionDataManagementsByTypeName(PermissionDataConstants.Type.MASTER_DATA.code);
            permissionDataManagements = permissionDataManagements.stream().filter(i -> i.getTypeId().equals(masterDataRequest.getId())).collect(Collectors.toList());
            for (PermissionDataManagement e : permissionDataManagements) {
                e.setCreatedTime(null);
            }
            response.setPermissionDataManagements(permissionDataManagements);

            // Lấy list shareUser
            List<SharedUser> sharedUsers = shareUserRepository.getSharedUsersByReferenceIdAndReferenceType(masterDataRequest.getId(), ShareUserTypeEnum.MASTERDATA.type);
            response.setSharedUsers(sharedUsers);
            return response;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public MasterDataPermitResponse getMDPermit(String account) {
        MasterDataPermitResponse response = new MasterDataPermitResponse();
        // list companyCode
        List<String> lstCompanyCode = authService.getListCompanyCodeByUser(account);
        response.setCompanyCodes(lstCompanyCode);
        response.setGroupPermitId(new ArrayList<>());
        // Phân quyền theo nhóm
        List<Long> lstGroupPermissionId = systemGroupRepository.getGroupIdByGroupTypeAndUsername(FilterDataEnum.MASTER_DATA.tableName, account);
        // có phân quyền theo nhóm - ưu tiên quyền theo nhóm - trừ super admin
        if (!ValidationUtils.isNullOrEmpty(lstGroupPermissionId) && !lstCompanyCode.contains(CommonConstants.FILTER_SELECT_ALL)) {
            response.setGroupPermitId(lstGroupPermissionId);
            // get list company code role admin member
            List<String> lstCompanyCodeMemberAdmin = authService.getListCompanyCodeMemberAdminByUser(account);
            response.setCompanyCodes(lstCompanyCodeMemberAdmin);
        }
        return response;
    }
}
