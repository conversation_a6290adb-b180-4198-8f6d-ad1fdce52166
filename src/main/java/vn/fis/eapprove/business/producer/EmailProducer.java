package vn.fis.eapprove.business.producer;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.constant.AppConstants;
import vn.fis.eapprove.business.model.request.MailVerifiedRequest;


@Component
public class EmailProducer {

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    public void sendEmail(MailVerifiedRequest email) {
        kafkaTemplate.send(AppConstants.TOPIC_VERIFIED_MAIL, email);
    }
}
