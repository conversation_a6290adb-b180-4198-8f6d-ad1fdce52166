package vn.fis.eapprove.business.producer;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.constant.AppConstants;
import vn.fis.spro.common.model.Notification;

@Component
public class PushNotificationProducer {

    @Autowired
    KafkaTemplate<String, Object> kafkaTemplate;

    public void pushNotification(Notification notification) {
        kafkaTemplate.send(AppConstants.TOPIC_NAME_EMAIL, notification);
    }
}
