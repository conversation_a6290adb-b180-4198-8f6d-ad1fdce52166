package vn.fis.eapprove.business.producer;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.util.UUID;
@Component
@Slf4j
@AllArgsConstructor
public class ReportProducer {
    private KafkaTemplate<String, Object> kafkaTemplate;

    public <T> void sendKafka(T request, String topic) {
        String uuid = UUID.randomUUID().toString();
        kafkaTemplate.send(MessageBuilder.withPayload(request)
                .setHeader(KafkaHeaders.TOPIC, topic)
                .setHeader("messageId", uuid)
                .build());
    }
}
