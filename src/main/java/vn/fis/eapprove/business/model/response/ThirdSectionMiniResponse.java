package vn.fis.eapprove.business.model.response;

import lombok.Data;

@Data
public class ThirdSectionMiniResponse {
    private String user;
    private Long backlog;
    private Long newTask;
    private Long requestUpdate;

    public ThirdSectionMiniResponse(String user, Long backlog, Long newTask) {
        this.user = user;
        this.backlog = backlog;
        this.newTask = newTask;
    }

    public ThirdSectionMiniResponse(String user, Long requestUpdate) {
        this.user = user;
        this.requestUpdate = requestUpdate;
    }
}
