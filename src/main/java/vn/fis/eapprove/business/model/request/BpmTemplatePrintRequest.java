package vn.fis.eapprove.business.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTpSignZone;


import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BpmTemplatePrintRequest {

    List<BpmTpSignZone> bpmTpSignZoneList;
    private Map<String, VariableValueDto> variables;//Truyền thêm biến nếu muốn
    private String templateName;
    private String uploadWordsChange;//Tên của file word trên s3
    private String printType;//pdf => nếu muốn file là pdf, docx nếu muốn file là docx
    private Map<String, Object> createVariables;

    // cấu hình ký html
    private Long fileConditionId;
    private String htmlContent;
    private String headerContent;
    private String footerContent;
}
