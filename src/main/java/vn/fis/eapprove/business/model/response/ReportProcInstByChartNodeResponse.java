package vn.fis.eapprove.business.model.response;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import vn.fis.eapprove.business.dto.report.ReportProcInstByChartNodeDto;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;


@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ReportProcInstByChartNodeResponse {
    Map<String, List<ReportProcInstByChartNodeDto>> result;
    BigInteger total;
}
