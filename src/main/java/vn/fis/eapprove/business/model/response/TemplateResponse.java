package vn.fis.eapprove.business.model.response;

import lombok.Data;
import vn.fis.eapprove.business.dto.BpmProcdefDto;
import vn.fis.eapprove.business.utils.DateToLocalDateTime;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class TemplateResponse {
    private Long id;
    private String templateName;
    private String description;
    private Integer status;
    //    private List<Map<String,String>> template;
    private String template;
    private Date createdDate;
    private String createdUser;
    private Date modifiedDate;
    private String modifiedUser;
    private String urlName;
    private String nodeId;
    private String assignee;
    private String taskName;
    private String zoomCollection;
    private List<String> listZoom;
    private List<String> listTaskName;
    private List<String> listTaskAssignee;
    private List<BpmProcdefDto> bpmProcdefDtos;
    private List<String> shareWith;
    private List<String> applyFor;
    private Boolean specialFlow;
    private Long specialParentId;
    private String specialCompanyCode;
    private Boolean isProcDefJoin;
    private String companyCode;
    private String companyName;
    private List<ChildResponse> child;

    public TemplateResponse() {
    }

    public TemplateResponse(long id, String templateName, String description,
                            Integer status, String template, LocalDateTime createdDate,
                            String createdUser, LocalDateTime modifiedDate, String modifiedUser,
                            String urlName,String companyCode,String companyName,String specialCompanyCode) {
        this.id = id;
        this.templateName = templateName;
        this.description = description;
        this.status = status;
        this.template = template;
        if (createdDate != null) {
            this.createdDate = DateToLocalDateTime.convertToDate(createdDate);
        }
        this.createdUser = createdUser;
        if (modifiedDate != null) {
            this.modifiedDate = DateToLocalDateTime.convertToDate(modifiedDate);
        }
        this.modifiedUser = modifiedUser;
        this.urlName = urlName;
        this.companyCode = companyCode;
        this.companyName = companyName;
        this.specialCompanyCode = specialCompanyCode;
    }
}

