package vn.fis.eapprove.business.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: AnhVTN
 * Date: 01/03/2023
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AsisstantOpinionUpdate {
    private Long id;
    private String url;
    private String name;
    private String opinion;
    private Long status;
    private String assistantEmail;
    private String ticketId;
}
