package vn.fis.eapprove.business.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;


import java.util.List;

/**
 * Author: PhucVM
 * Date: 04/12/2022
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActionApiContext {

    private String procDefId;
    private String taskDefKey;
    private String procInstId;
    private String actionCode;
    private BpmProcInst bpmProcInst;
    private boolean allowPostProcess = true;
    private List<ActionApiExecuteResult> apiExecuteBeforeActionResults; // list of apis execute BEFORE process
    private List<ActionApiExecuteResult> apiExecuteAfterActionResults; // list of apis execute AFTER process
}
