package vn.fis.eapprove.business.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ManageShareTicketResponse {
    private Long id;
    private String name;
    private String description;
    private String fromDate;
    private String toDate;
    private String createdDate;
    private String createdUser;
    private String updatedDate;
    private String updatedUser;
    private String companyCode;
    private String companyName;
    private String status;
}
