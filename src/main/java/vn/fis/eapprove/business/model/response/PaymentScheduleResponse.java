package vn.fis.eapprove.business.model.response;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

@Builder
@Data
public class PaymentScheduleResponse {
    private Long id;

    private String codeTicket;

    private String contractNumber;

    private String nameProject;

    private String paymentInstallment;

    private Long procInstId;

    private String paymentAmount;

    private LocalDateTime paymentDeadline;

    private String approvedPaymentAmount;

    private String actualSpending;

    private String amountPaid;

    private String createDate;

    private String createUser;

    private String reason;

    private String frequency;

    private String paidAt;

    private String companyCode;

    private String companyName;

    private int status;

}
