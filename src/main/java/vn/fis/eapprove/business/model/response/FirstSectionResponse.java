package vn.fis.eapprove.business.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FirstSectionResponse {
    private FirstSectionMiniResponse backlogTask;
    private FirstSectionMiniResponse newTask;
    private FirstSectionMiniResponse returnTask;

    public FirstSectionResponse(Long backlogTotal, Long backlogLate, Long backlogPunctual, Long backlogCancel, Long backlogRu, Long backlogOutput,
                                Long newTotal, Long newLate, Long newPunctual, Long newCancel, Long newRu, Long newOutput,
                                Long ruTotal, Long ruLate, Long ruPunctual, Long ruCancel, Long ruRu, Long ruOutput
    ) {
        FirstSectionMiniResponse mapBacklog = new FirstSectionMiniResponse();
        FirstSectionMiniResponse mapNew = new FirstSectionMiniResponse();
        FirstSectionMiniResponse mapReturn = new FirstSectionMiniResponse();

        mapBacklog.setTotal(backlogTotal);
        mapBacklog.setLate(backlogLate);
        mapBacklog.setPunctual(backlogPunctual);
        mapBacklog.setCancel(backlogCancel);
        mapBacklog.setRequestUpdate(backlogRu);
        mapBacklog.setOutputBacklog(backlogOutput);

        mapNew.setTotal(newTotal);
        mapNew.setLate(newLate);
        mapNew.setPunctual(newPunctual);
        mapNew.setCancel(newCancel);
        mapNew.setRequestUpdate(newRu);
        mapNew.setOutputBacklog(newOutput);

        mapReturn.setTotal(ruTotal);
        mapReturn.setLate(ruLate);
        mapReturn.setPunctual(ruPunctual);
        mapReturn.setCancel(ruCancel);
        mapReturn.setRequestUpdate(ruRu);
        mapReturn.setOutputBacklog(ruOutput);

        this.backlogTask = mapBacklog;
        this.newTask = mapNew;
        this.returnTask = mapReturn;
    }
}
