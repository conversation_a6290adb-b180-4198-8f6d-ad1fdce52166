package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class CreateShareTicketRequest {
    private Long id;
    private String name;
    private String description;
    private String status;
    private String fromDate;
    private String toDate;
    private List<String> listAssignee;
    private List<String> listServiceId;
    private List<String> listChartNodeCode;
    private List<String> listCompanyCode;
    private List<String> listCreatedUser;
    private List<String> listShareUser;
    private List<String> applyFor;
}
