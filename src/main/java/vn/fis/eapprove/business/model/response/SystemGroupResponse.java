package vn.fis.eapprove.business.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.domain.system.entity.SystemGroup;


import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SystemGroupResponse {
    private List<String> applyFor;
    private SystemGroup parent;
    private List<SystemGroup> child;
    private Long id;
}
