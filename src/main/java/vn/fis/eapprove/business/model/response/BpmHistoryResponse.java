package vn.fis.eapprove.business.model.response;

import lombok.Data;
import vn.fis.spro.common.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Data
public class BpmHistoryResponse {
    private Long id;
    private Long ticketId;
    private String procInstId;
    private String taskInstId;
    private String fromTask;
    private String toTask;
    private String fromTaskName;
    private String toTaskName;
    private Date time;
    private String action;
    private String actionUser;
    private String taskDefKey;
    private String note;
    private String affectedTask;
    private String taskType;
    private String oldTaskId;
    private String oldProcInstId;
    private String attachFiles;
    private String attachFilesName;
    private String attachFilesSize;
    private String taskAssignee;
    private Date receivedTime;
    private Object userInfo;

    public BpmHistoryResponse(Long id, Long ticketId, String procInstId, String taskInstId, String fromTask,
                              String toTask, String fromTaskName, String toTaskName, Date time, String action,
                              String actionUser, String taskDefKey, String note, String affectedTask, String taskType,
                              String oldTaskId, String oldProcInstId, String attachFiles,String attachFilesName,String attachFilesSize, String taskAssignee,
                              LocalDateTime receivedTime, String actionUserInfo) {
        this.id = id;
        this.ticketId = ticketId;
        this.procInstId = procInstId;
        this.taskInstId = taskInstId;
        this.fromTask = fromTask;
        this.toTask = toTask;
        this.fromTaskName = fromTaskName;
        this.toTaskName = toTaskName;
        this.time = time;
        this.actionUser = actionUser;
        this.taskDefKey = taskDefKey;
        this.note = note;
        this.affectedTask = affectedTask;
        this.taskType = taskType;
        this.oldTaskId = oldTaskId;
        this.oldProcInstId = oldProcInstId;
        this.attachFiles = attachFiles;
        this.attachFilesName = attachFilesName;
        this.attachFilesSize = attachFilesSize;
        this.taskAssignee = taskAssignee;
        if (receivedTime != null) {
            this.receivedTime = Date.from(receivedTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (actionUserInfo != null) {
            this.userInfo = ObjectUtils.readValue(actionUserInfo);
        }

        String convertedAction = action;

        if (action != null) {
            switch (action) {
                case "RequestUpdate":
                    convertedAction = "Trả về";
                    break;
                case "GetRequestUpdate":
                    convertedAction = "Bị trả về";
                    break;
                case "ChangeImplementer":
                    convertedAction = "Đổi người thực hiện";
                    break;
                case "StartTask":
                    convertedAction = "Bắt đầu phê duyệt";
                    break;
                case "CompleteTask":
                    convertedAction = "Phê duyệt";
                    break;
                case "CreateTask":
                    convertedAction = "Tạo mới bước";
                    break;
                case "DELETED_BY_RU":
                    convertedAction = "Xóa bởi trả về";
                    break;
                case "CreateTicket":
                    convertedAction = "Tạo mới phiếu";
                    break;
                case "DraftTask":
                    convertedAction = "Dự thảo công việc";
                    break;
                case "Rating":
                    convertedAction = "Đánh giá";
                    break;
                case "UpdateTicket":
                    convertedAction = "Cập nhập";
                    break;
                default:
                    break;
            }
        }

        this.action = convertedAction;
    }
}
