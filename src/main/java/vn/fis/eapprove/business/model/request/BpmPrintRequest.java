package vn.fis.eapprove.business.model.request;

import java.util.List;

public class BpmPrintRequest {
    String procDefId;
    String name;
    String description;
    List<BpmPrintItemRequest> listItem;


    public BpmPrintRequest() {

    }

    public BpmPrintRequest(String procDefId, String name, String description, List<BpmPrintItemRequest> listItem) {
        this.name = name;
        this.description = description;
        this.listItem = listItem;
        this.procDefId = procDefId;
    }


    public String getProcDefId() {
        return procDefId;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    public String getName() {
        return name;
    }


    public void setName(String name) {
        this.name = name;
    }


    public String getDescription() {
        return description;
    }


    public void setDescription(String description) {
        this.description = description;
    }


    public List<BpmPrintItemRequest> getListItem() {
        return listItem;
    }


    public void setListItem(List<BpmPrintItemRequest> listItem) {
        this.listItem = listItem;
    }


}
