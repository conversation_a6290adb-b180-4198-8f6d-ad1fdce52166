package vn.fis.eapprove.business.model.response;

import lombok.Data;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTpSignZone;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTpTask;


import java.util.Date;
import java.util.List;

@Data
public class BpmPrintPhaseResponse {
    private Long id;
    private String name;
    private String procDefName;
    private String descr;
    private String procDefId;
    private List<BpmTpTask> bpmPrintPhaseTasks;
    private List<BpmTpSignZone> bpmPrintSignZones;
    private String firstStep;
    private List<String> signStep;
    private List<String> shareWith;
    private Boolean isDelete;
    private String content;
    private String pdfContent;
    private String createdUser;
    private Date createdDate;
    private String updatedUser;
    private Date updatedDate;
    private String companyCode;
    private String companyName;
    private Boolean specialFlow;
    private Long specialParentId;
    private String specialCompanyCode;
    private Long specialParentServiceId;
    private List<ChildResponse> child;
    private Boolean status;
    private String configType;
}
