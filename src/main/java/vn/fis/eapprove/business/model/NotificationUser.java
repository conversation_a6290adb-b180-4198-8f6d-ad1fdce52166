package vn.fis.eapprove.business.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.model.request.VariableValueDto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class NotificationUser implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long bpmProcdefId;
    private String nextTaskDefKey;
    private Map<String, VariableValueDto> variables;
    private Long ticketId;
    private Boolean isGetOldVariable;
    private List<String> lstCustomerEmails;
    private String actionCode;
    private String emailExe;
}
