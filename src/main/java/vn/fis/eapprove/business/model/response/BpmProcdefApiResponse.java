package vn.fis.eapprove.business.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BpmProcdefApiResponse {

    private Long id;
    private String procDefId;
    private String taskDefKey;
    private Integer actionId;
    private Long apiId;
    private String header;
    private String body;
    private Boolean status;
    private String createdUser;
    private LocalDateTime createdTime;
    private String updatedUser;
    private LocalDateTime updatedTime;
}
