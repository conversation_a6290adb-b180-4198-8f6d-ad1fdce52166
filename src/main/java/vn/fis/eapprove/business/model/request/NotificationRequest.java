package vn.fis.eapprove.business.model.request;

import lombok.*;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@FieldDefaults(level = AccessLevel.PRIVATE)
public class NotificationRequest {
    private String id;
    private long receiveTime;
    private String receiver;
    private String type;
    private Object payload;
    private Object extraInfo;
    private String title;
}