package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class AssignRequest {
    private Long id;
    private String assignName;
    private String assignUser;
    private String assignedUser;
    private Long ticketId;
    private List<Long> serviceRange;
    private LocalDate startDate;
    private LocalDate endDate;
    private Integer status;
    private Integer effect;
    private String description;
    private String fileName;
    private String newTicket;
    private Boolean ticketAssign;
    private Integer type;
    private String assignTitle;
    private String assignDuty;
    private String assignCompanyName;
    private String assignDecision;
    private String assignedTitle;
    private String assignStorage;
    private Long serviceId;
    private List<String> authorityConditions;
    private String requestCode;
    private String newRequestCode;
    private String createdUser;

}
