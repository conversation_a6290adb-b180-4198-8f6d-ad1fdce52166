package vn.fis.eapprove.business.model.response;

import lombok.Data;

import java.util.Date;

@Data
public class HandingOverWorkResponse {
    Long ticketId;
    String requestCode;
    String title;
    String serviceName;
    String statusTicket;
    String userTranfer;
    Boolean status;
    String procDefId;
    String procInstId;
    String userAction;
    Date timeAction;
    Date ticketCreatedTime;
}
