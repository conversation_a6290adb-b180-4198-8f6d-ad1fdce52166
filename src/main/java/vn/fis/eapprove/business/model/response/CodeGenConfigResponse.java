package vn.fis.eapprove.business.model.response;

import lombok.Data;
import vn.fis.eapprove.business.domain.codeGen.entity.CodeGenStruct;


import java.util.Date;
import java.util.List;

@Data
public class CodeGenConfigResponse {
    private Long id;
    private String code;
    private String description;
    private String structorCode;
    private Date createdAt;
    private Date updatedAt;
    private String userCreate;
    private String status;
    private String userUpdate;
    private List<CodeGenStruct> lstCodeGenStruct;
    private List<String> applyFor;
    private String companyCode;
    private String companyName;
}
