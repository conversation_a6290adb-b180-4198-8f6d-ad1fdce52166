package vn.fis.eapprove.business.model.request;

import lombok.Data;
import vn.fis.eapprove.business.dto.BaseDtoTemp;
import vn.fis.spro.common.model.request.DateFilterDto;

import java.util.List;

@Data
public class BpmTicketFilterSearchRequest extends BaseDtoTemp {
    private String search;
    private String username;
    private String type;
    private Long filterId;
    private List<DateFilterDto> listDateFilter;
    private List<String> listTicketTitle;
    private List<String> listCreatedUser;
    private List<String> listCancelUser;
    private List<String> listRequestCode;
    private List<String> listServiceName;
    private List<String> listTicketTaskDtoList;
    private List<String> listTicketTaskDtoTaskAssignee;
    private List<String> listTicketStatus;
    private List<String> listCompanyCode;
    private List<String> listChartNodeName;
}
