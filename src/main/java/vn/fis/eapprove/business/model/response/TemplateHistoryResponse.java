package vn.fis.eapprove.business.model.response;

import lombok.Data;

import java.time.LocalDateTime;


@Data
public class TemplateHistoryResponse {

    private Long id;


    private Long templateId;


    private String version;


    private Boolean statusHistory;


    private String contentEdit;


    private String urlName;


    private String templateName;

    private String template;


    private LocalDateTime createdDate;


    private String createdUser;
    private Long inputId;

    public TemplateHistoryResponse(Long id, Long templateId, String version, Boolean statusHistory, String contentEdit, String urlName, String templateName, String template, LocalDateTime createdDate, String createdUser) {
        this.id = id;
        this.templateId = templateId;
        this.version = version;
        this.statusHistory = statusHistory;
        this.contentEdit = contentEdit;
        this.urlName = urlName;
        this.templateName = templateName;
        this.template = template;
        this.createdDate = createdDate;
        this.createdUser = createdUser;
    }

    public TemplateHistoryResponse(Long id, Long templateId,String urlName) {
        this.id = id;
        this.templateId = templateId;
        this.urlName = urlName;
    }
    public TemplateHistoryResponse(Long id, Long templateId,String urlName,Long inputId) {
        this.id = id;
        this.templateId = templateId;
        this.urlName = urlName;
        this.inputId = inputId;
    }
}
