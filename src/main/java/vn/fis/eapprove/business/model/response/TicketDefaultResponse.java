package vn.fis.eapprove.business.model.response;

import lombok.Data;

import java.util.List;


@Data
public class TicketDefaultResponse {
    private List<Long> linkProcInstId;
    private LocationManagementResponse locationManagement;
    private boolean receiveMail;
    private Long priorityId;
    private List<NotifyUserResponse> notifyUsers;
    private List<Long> notifyGroups;
    private Boolean isAssistant;
    private String chartNodeName;
    private String companyCode;
    private Long chartId;
    private Long chartNodeId;
    private String chartNodeCode;
    private Long legislativeId;
}
