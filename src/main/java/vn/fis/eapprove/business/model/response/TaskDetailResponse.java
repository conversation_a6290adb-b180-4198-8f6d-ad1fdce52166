package vn.fis.eapprove.business.model.response;

import lombok.Data;
import vn.fis.eapprove.business.dto.SignedFileDto;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class TaskDetailResponse {

    private Long id;
    private String taskId;
    private String taskDefKey;
    private String taskName;
    private String taskPriority;
    private String taskAssignee;
    private Date taskCreatedTime;
    private Date taskStartedTime;
    private Double taskSla;
    private Date taskFinishedTime;
    private Date taskDoneTime;
    private String taskStatus;
    private String procInstId;
    private String endKey;
    private String procDefId;
    private String taskType;
    private List<String> affected;
    private List<String> listStatus;
    private List<Map<String, Object>> listVariables;
    private Map<String, Object> responseTime;
    private Map<String, Object> finishTime;
    private String newTaskId;
    private Integer startPermission;
    private String newStatus;
    private Long printId;
    private Long ticketId;
    private List<Map<String, Object>> listSignForm;
    private Boolean editPermission;
    private String ticketProcDefId;
    private String fullName;
    private List<String> taskUsers;
    private Long priorityId;
    private String color;
    private List<SignedFileDto> signedFiles;
    private Long newId;
    private List<Map<String, Object>> draftVariables;
    private Map<String, Object> taskActionUser;
    private Map<String, Object> taskOrgAssignee;

    public String getTaskProcDefId() {
        return procDefId;
    }

    public void setTaskProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    public Date getTaskCreatedTime() {
        return taskCreatedTime;
    }

    public void setTaskCreatedTime(LocalDateTime taskCreatedTime) {
        if (taskCreatedTime != null) {
            this.taskCreatedTime = Date.from(taskCreatedTime.atZone(ZoneId.systemDefault()).toInstant());
        } else {
            this.taskCreatedTime = null;
        }
    }

    public Date getTaskStartedTime() {
        return taskStartedTime;
    }

    public void setTaskStartedTime(LocalDateTime taskStartedTime) {
        if (taskStartedTime != null) {
            this.taskStartedTime = Date.from(taskStartedTime.atZone(ZoneId.systemDefault()).toInstant());
        } else {
            this.taskStartedTime = null;
        }
    }

    public Date getTaskFinishedTime() {
        return taskFinishedTime;
    }

    public void setTaskFinishedTime(LocalDateTime taskFinishedTime) {
        if (taskFinishedTime != null) {
            this.taskFinishedTime = Date.from(taskFinishedTime.atZone(ZoneId.systemDefault()).toInstant());
        } else {
            this.taskFinishedTime = null;
        }
    }

    public Date getTaskDoneTime() {
        return taskDoneTime;
    }

    public void setTaskDoneTime(LocalDateTime taskDoneTime) {
        if (taskDoneTime != null) {
            this.taskDoneTime = Date.from(taskDoneTime.atZone(ZoneId.systemDefault()).toInstant());
        } else {
            this.taskDoneTime = null;
        }
    }
}
