package vn.fis.eapprove.business.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApiManagementResponse {
    private Long id;

    private String name;
    private String url;

    private String method;
    private String header;
    private String body;
    private String tokenAttribute;
    private Integer status;
    private Long authenApiId;
    private Long baseUrlId;
    private String description;
    private String createdUser;
    private String createdTime;
    private String updatedUser;
    private String updatedTime;
    private String type;

    private String response;

    private Integer returnResponse;

    private String errorAttribute;

    private String successCondition;

    private Integer continueOnError;

    private List<String> shareWith;

    private Boolean isDeleted;

    private List<String> applyFor;
    private String companyCode;
    private String companyName;
    private List<String> procDefApplyName;

}
