package vn.fis.eapprove.business.model.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SystemGroupRequest {
    private Long id;
    private String name;
    private List<String> listUserName;
    private List<String> applyFor;
    private String fromDate;
    private String toDate;
    private List<SystemGroupField> listField;
    private String groupType;
    private Integer status;
    private String companyCode;
    private String companyName;
    private String description;
}
