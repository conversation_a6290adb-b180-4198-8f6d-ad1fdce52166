package vn.fis.eapprove.business.model.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.model.ActionApiResult;

import java.io.Serializable;
import java.util.List;

/**
 * Author: PhucVM
 * Date: 05/03/2023
 */
@Data
@NoArgsConstructor
public class ActionApiResponse implements Serializable {

    private static final long serialVersionUID = 96302912173328204L;

    private List<ActionApiResult> actionApiResults;

    public ActionApiResponse(List<ActionApiResult> actionApiResults) {
        this.actionApiResults = actionApiResults;
    }
}
