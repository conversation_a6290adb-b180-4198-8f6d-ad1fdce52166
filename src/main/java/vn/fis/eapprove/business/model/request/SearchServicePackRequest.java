package vn.fis.eapprove.business.model.request;

import lombok.Data;
import vn.fis.eapprove.business.dto.BaseFilterDto;

import java.util.List;

@Data
public class SearchServicePackRequest extends BaseFilterDto {

    private String serviceName;
    private String processName;
    private Boolean isExactServiceType = false;
    private Long chartId;
    private List<Integer> serviceType;
    private Boolean showWeb;
    private Boolean admin;
    private List<String> listStatus;
    private List<String> createdUser;
    private List<String> modifiedUser;
    private String fromDate;
    private String toDate;
    private Boolean visibleAllUser;
    private List<String> applyFor;
    private Boolean specialFlow;
    private List<Integer> specialServiceType;
    private Boolean isShared;
    private List<String> sortCompanyCode;
    private List<String> listServiceName;
    private List<String> listServiceType;
    private List<String> listSpecialServiceType;
    private List<Long> lstGroupPermissionId;
    private List<String> listCompanyCodeMemberAdmin;
}
