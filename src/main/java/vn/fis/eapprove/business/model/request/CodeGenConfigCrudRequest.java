package vn.fis.eapprove.business.model.request;

/**
 * Author: AnhVTN
 * Date: 30/03/2023
 */

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.domain.codeGen.entity.CodeGenStruct;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CodeGenConfigCrudRequest {
    private Long id;
    private String description;
    private String code;
    private String userCreate;
    private String structorCode;
    private List<CodeGenStruct> lstCodeGenStruct;
    private List<String> applyFor;
    private String status;
    private Date createdAt;

}
