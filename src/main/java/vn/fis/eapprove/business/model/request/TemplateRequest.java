package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.util.List;

@Data
public class TemplateRequest extends BasePageRequest {
    private Long id;
    private String templateName;
    private String templateCode;
    private String description;
    private Integer status;
    private String template;
    private String createdDate;
    private String createdUser;
    private String modifiedDate;
    private String modifiedUser;
    private String urlName;
    private List<String> shareWith;
    private List<String> applyFor;
    private Long cloneTemplateId;
}
