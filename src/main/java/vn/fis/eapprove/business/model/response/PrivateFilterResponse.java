package vn.fis.eapprove.business.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrivateFilterResponse {
    private Long id;
    private String ticketProcInstId;
    private String requestCode;
    private String ticketTitle;
    private String ticketStatus;
    private String serviceName;
    private LocalDateTime ticketCreatedTime;
    private String createdUser;
    private String companyCode;
    private String chartNodeName;
    private String ticketPriority;
    private LocalDateTime ticketEditTime;
    private String cancelUser;
    private String procDefId;
    private String startKey;
    private Long serviceId;
    private Long chartId;
}
