package vn.fis.eapprove.business.model.response;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssigneeInfoResponse {
    String username;
    String fullName;
    Long chartId;
    String chartShortName;
    String companyCode;
    String companyName;
    String chartNodeName;
    String chartNodeCode;
    String titleName;
    Long chartNodeId;
    String status;
    String staffCode;
    String directManager;
    Long userInfoId;
    String managerLevel;
    List<String> chartNodeChild;
}
