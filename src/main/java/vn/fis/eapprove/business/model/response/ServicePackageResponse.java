package vn.fis.eapprove.business.model.response;

import lombok.Data;

import java.util.List;

@Data
public class ServicePackageResponse {
    private Long id;
    private Long parentId;
    private String serviceName;
    private String color;
    private String icon;
    private Integer serviceType;
    private Long processId;
    private String processName;
    private String url;
    private String description;
    private String note;
    ;
    private Boolean notShowingWebsite;
    private Boolean notShowingMoblie;
    private List<String> hideName;
    private List<String> visibleName;
    private List<Integer> visibleGroup;
    private List<Integer> hideGroup;
    private Integer positionPackage;
    private Long idOrgChart;
    private Long masterParentId;
    private Long countChild;
    private String procDefId;
    private String priority;
    private Long priorityId;
    private String priorityLabel;
    private String iconName;
    private String createdDate;
    private String createdUser;
    private String modifiedDate;
    private String modifiedUser;
    private String Status;
    private Boolean hideAllUser;
    private Boolean visibleAllUser;
    private Long submissionType;
    private List<String> applyFor;
    private List<String> visibleChart;
    private List<String> hideChart;

    // Special clone service
    private Boolean specialFlow;
    private Boolean specialApplyFor;
    private Long specialParentId;
    private String specialCompanyCode;
    private List<String> listParentCompanyCode;
    private String companyCode;
    private String companyName;

    private List<ChildResponse> child;

    private Boolean legislativeRequirement;
}
