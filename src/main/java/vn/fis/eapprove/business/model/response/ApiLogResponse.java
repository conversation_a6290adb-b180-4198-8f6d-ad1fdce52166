package vn.fis.eapprove.business.model.response;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class ApiLogResponse {
    private Long id;
    private String url;
    private String method;
    private String header;
    private String requestBody;
    private String requestTime;
    private String responseTime;
    private String responseStatus;
    private String responseData;
    private Long bpmProcdefApiId;
    private Long bpmProcinstId;
    private String apiType;
    private String retryTime;
    private Integer retryCount = 0;
    private String realm;
    private boolean isLogAfter;
    private String procInstId;
    private String apiName;
    private String ticketName;
    private String taskDefKey;
    private String requestCode;
}
