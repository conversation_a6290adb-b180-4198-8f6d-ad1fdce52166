package vn.fis.eapprove.business.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EvaluationCriteriaRequest {
    private Long id;
//    private String reviewItem;
    private String name;
    private String description;
    private String status;
    private List<String> lstDepartments;
    private List<String> applyFor;
}
