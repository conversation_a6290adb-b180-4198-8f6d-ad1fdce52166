package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
public class DashboardRequest {
    private Long chartId;
    private Long chartNodeId;
    private String fromDate;
    private String toDate;
    private String taskType;
    private String status;
    private Set<String> defaultUser = new HashSet<>();
    private List<Long> listChartNodeId;
    private List<String> listTicketStatus;
    private String dateRange;
}
