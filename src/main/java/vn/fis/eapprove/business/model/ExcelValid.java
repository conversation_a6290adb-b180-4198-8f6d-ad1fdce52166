package vn.fis.eapprove.business.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.formula.functions.T;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExcelValid {
    private String key;

    private List<T> validList = new ArrayList<>();

    private Boolean isValidEmpty = false;

    //Valid empty theo 1 trường khác
    private Boolean isValidEmptyByOtherField = false;
    private String isValidEmptyByOtherFieldKey;
    private String isValidEmptyByOtherFieldValue;


    private Boolean isValidMaxLength = false;
    private int maxLength;
    private Boolean isValidMinLength = false;
    private int minLength;

    //Valid Exist
    private Boolean isValidExistInDb = false;
    private List<String> listKeyInDb = new ArrayList<>(); // Auto convert

    //Valid Exist
    private Boolean isValidExistNoInDb = false;
    private List<String> listKeyNoInDb = new ArrayList<>(); // Auto convert

    //Valid trùng dòng trong excel
    private Boolean isValidExistInExcel = false;
    private List<String> keyContain = new ArrayList<>();
    private Map<String, Integer> indexKeyContainInExcel = new HashMap<>();// Auto convert

    private Boolean isWriteMessageIsCurrentCell = false;
    private int indexInExcel;

    // valid format
    private Boolean isValidFormat = false;
    private String formatRegex;

    //Valid exact list
    private Boolean isValidExactList = false;
    private List<String> listExact = new ArrayList<>();
}
