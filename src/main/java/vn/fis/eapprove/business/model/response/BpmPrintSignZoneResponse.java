package vn.fis.eapprove.business.model.response;

import lombok.Data;

import java.util.Date;

@Data
public class BpmPrintSignZoneResponse {

    private Long id;
    private String taskDefKey;
    private String procInstId;
    private String wResize;
    private String hResize;
    private Long orderSign;
    private String email;
    private Long page;
    private Long x;
    private Long y;
    private Long w;
    private Long h;
    private String firstName;
    private String lastName;
    private String position;
    private String createdUser;
    private Date createdDate;
    private String updatedUser;
    private Date updatedDate;
    private BpmSignResponse bpmSignResponse;
    private Float scale;
    private String comment;
    private String signedFile;
    private Date signedDate;
    private String signType;
    private String chartNodeLevel;
}
