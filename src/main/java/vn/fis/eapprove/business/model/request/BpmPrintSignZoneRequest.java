package vn.fis.eapprove.business.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BpmPrintSignZoneRequest {

    private String name;
    private Long bpmPrintPhaseId;
    private String procDefId;
    private String pdfContent;
    private String procInstId;
    private List<BpmSignZoneRequest> zones;
    private List<BpmTaskPrintRequest> taskKey;
    private Long ticketTempId;
    private String uploadWordsChange;
    private String username;
    private Long fileConditionId;
}
