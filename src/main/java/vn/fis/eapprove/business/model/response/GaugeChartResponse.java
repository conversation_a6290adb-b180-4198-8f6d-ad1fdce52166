package vn.fis.eapprove.business.model.response;

import lombok.Data;

@Data
public class GaugeChartResponse {

    private GaugeMiniReponse response;
    private GaugeMiniReponse finish;

    public GaugeChartResponse(Double currentRes, Double maxRes, Double currentFin, Double maxFin) {
        Double maxResDouble = (maxRes * 60);
        Double maxFinDouble = (maxFin * 60);
        Long maxResLong = Math.round(maxResDouble);
        Long maxFinLong = Math.round(maxFinDouble);
        Long currentResLong = 0l;
        Long currentFinLong = 0l;
        if (currentRes != null) {
            currentResLong = Math.round(currentRes);
        }
        if (currentFin != null) {
            currentFinLong = Math.round(currentFin);
        }
        GaugeMiniReponse gaugeRes = new GaugeMiniReponse(currentResLong, maxResLong);
        GaugeMiniReponse gaugeFin = new GaugeMiniReponse(currentFinLong, maxFinLong);
        this.response = gaugeRes;
        this.finish = gaugeFin;
    }

    public GaugeChartResponse() {

    }
}
