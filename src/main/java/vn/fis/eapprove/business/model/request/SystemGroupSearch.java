package vn.fis.eapprove.business.model.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.spro.common.model.request.BaseFilterDto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SystemGroupSearch extends BaseFilterDto {
    private String search;
    private String groupType;
    private List<Integer> status = List.of(1);
}
