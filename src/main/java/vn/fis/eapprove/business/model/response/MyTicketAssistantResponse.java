package vn.fis.eapprove.business.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MyTicketAssistantResponse {
    private Long ticketId;
    private String ticketProcInstId;
    private String ticketEndActId;
    private String ticketStartActId;
    private String ticketStatus;
    private Long serviceId;
    private String serviceName;
    private String ticketTitle;
    private String ticketStartUserId;
    private Date ticketStartedTime;
    private Date ticketCreatedTime;
    private Date ticketFinishTime;
    private Date ticketClosedTime;
    private Date ticketCanceledTime;
    private Date ticketEditTime;
    private Date ticketEndTime;
    private Double slaFinish;
    private Double slaResponse;
    private String cancelReason;
    private Double ticketRating;
    private String comment;
    private String ticketProcDefId;
    private Long submissionTypeId;
    private String typeName;
    //    private String ticketShared;
    private String ticketPriority;
    private Double autoClose;
    private String requestCode;
    private String ticketCreatedUser;
    private String companyCode;
    private Long chartId;
    private String priorityName;
    private String priorityColor;
    private String actionUser;
    private String chartName;
    private String chartNodeName;


    public MyTicketAssistantResponse(Long ticketId, String ticketProcInstId, String ticketEndActId, String ticketStartActId, String ticketStatus, Long serviceId, String serviceName, String ticketTitle,
                                     String ticketStartUserId, LocalDateTime ticketStartedTime, LocalDateTime ticketCreatedTime, LocalDateTime ticketFinishTime, LocalDateTime ticketClosedTime,
                                     LocalDateTime ticketCanceledTime, LocalDateTime ticketEditTime, LocalDateTime ticketEndTime, Double slaFinish, Double slaResponse, String cancelReason, Double ticketRating,
                                     String comment, String ticketProcDefId, Long submissionTypeId, String typeName, String ticketPriority, Double autoClose, String requestCode, String ticketCreatedUser,
                                     String companyCode, Long chartId, String priorityName,
                                     String priorityColor, String actionUser, String chartName, String chartNodeName) {
        this.ticketId = ticketId;
        this.ticketProcInstId = ticketProcInstId;
        this.ticketEndActId = ticketEndActId;
        this.ticketStartActId = ticketStartActId;
        this.ticketStatus = ticketStatus;
        this.serviceId = serviceId;
        this.serviceName = serviceName;
        this.ticketTitle = ticketTitle;
        this.ticketStartUserId = ticketStartUserId;
        if (ticketStartedTime != null) {
            this.ticketStartedTime = Date.from(ticketStartedTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketCreatedTime != null) {
            this.ticketCreatedTime = Date.from(ticketCreatedTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketFinishTime != null) {
            this.ticketFinishTime = Date.from(ticketFinishTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketClosedTime != null) {
            this.ticketClosedTime = Date.from(ticketClosedTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketCanceledTime != null) {
            this.ticketCanceledTime = Date.from(ticketCanceledTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketEditTime != null) {
            this.ticketEditTime = Date.from(ticketEditTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketEndTime != null) {
            this.ticketEndTime = Date.from(ticketEndTime.atZone(ZoneId.systemDefault()).toInstant());
        }

        this.slaFinish = slaFinish;
        this.slaResponse = slaResponse;
        this.cancelReason = cancelReason;
        this.ticketRating = ticketRating;
        this.comment = comment;
        this.ticketProcDefId = ticketProcDefId;
//        this.ticketShared = ticketShared;
        this.submissionTypeId = submissionTypeId;
        this.typeName = typeName;
        this.ticketPriority = ticketPriority;
        this.autoClose = autoClose;
        this.requestCode = requestCode;
        this.ticketCreatedUser = ticketCreatedUser;
        this.companyCode = companyCode;
        this.chartId = chartId;
        this.priorityName = priorityName;
        this.priorityColor = priorityColor;
        this.actionUser = actionUser;
        this.chartName = chartName;
        this.chartNodeName = chartNodeName;
    }
}
