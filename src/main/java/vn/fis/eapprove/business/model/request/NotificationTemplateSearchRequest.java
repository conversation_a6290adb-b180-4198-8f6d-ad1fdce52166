package vn.fis.eapprove.business.model.request;

import lombok.Data;
import vn.fis.spro.common.model.request.BaseFilterDto;

import java.util.List;

/**
 * Author: AnhVTN
 * Date: 31/03/2023
 */
@Data
public class NotificationTemplateSearchRequest extends BaseFilterDto {
    private String type;
    private String actionCode;
    private List<String> filterActionCode;
    private List<String> listCompanyCode;

    private List<String> listTitle;
    private List<String> listAction;
    private List<String> listContent;
    private List<String> listUserCreate;
    private List<String> listUserUpdate;
    private List<String> listCreateAt;
    private List<String> listUpdateAt;
    private List<Long> lstGroupPermissionId;
    private List<String> listCompanyCodeMemberAdmin;
}
