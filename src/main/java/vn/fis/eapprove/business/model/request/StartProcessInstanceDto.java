/*
 * Copyright Camunda Services GmbH and/or licensed to Camunda Services GmbH
 * under one or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information regarding copyright
 * ownership. Camunda licenses this file to you under the Apache License,
 * Version 2.0; you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package vn.fis.eapprove.business.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class StartProcessInstanceDto {

    private Map<String, VariableValueDto> variables;
    private String businessKey;
    private String caseInstanceId;
    private String priority;
    private List<ProcessInstanceModificationInstructionDto> startInstructions;
    private boolean skipCustomListeners;
    private boolean skipIoMappings;
    private boolean withVariablesInReturn = false;
    private Boolean isDraft;
    private Long serviceId;
    private Long ticketId;
    private String taskId;
    private Long locationId;
    private List<String> notifyUsers;
    private Long priorityId;
    private List<String> notifyGroups;
    private List<Long> linkProcInstId;
    private Boolean receiveMail;
    private Long chartId;
    private String appCode;
    private String[] assistantEmail;
    private Boolean isAssistant;
    private Long submissionType;
    private String requestCode;
    private String oldProcInstId;
    private String companyCode;
    private String submissionTypeName;
    private String chartNodeName;
    private String chartName;
    private String chartNodeCode;
    private Long chartNodeId;
    private String account;
    private String forceViewUrl;
    private Long cancelTicketId;
    private List<String> approvedBudgetIds;
    private BpmPrintSignZoneRequest printSignZoneRequest;
    private Long legislativeId;
}
