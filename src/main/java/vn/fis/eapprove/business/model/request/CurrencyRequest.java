package vn.fis.eapprove.business.model.request;

import lombok.Data;
import org.simpleframework.xml.core.Validate;

import jakarta.validation.constraints.NotEmpty;

/**
 * Author: AnhVTN
 * Date: 31/03/2023
 */

@Data
public class CurrencyRequest {
    private Long id;
    private String name;
    private String code;
    private String anotherName;
    private Integer roundingRules;
    private String description;
    private String userCreate;
    private String updatedUser;
}
