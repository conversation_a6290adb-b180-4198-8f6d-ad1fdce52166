package vn.fis.eapprove.business.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkFlowRequest {

    @NotBlank(message = "procDefId is mandatory")
    private String procDefId;

    private String procInstId;

    private String fromNodeId;

    private Map<String, Object> variables;
}
