package vn.fis.eapprove.business.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class HistoryFileResponse {
    private String filename;
    private String createdUser;
    private String downloadUrl;
    private String idHistory;
    private String taskDefKey;
    private String uploadTime;
    private String fileType;
    private String description;
    private String displayName;
    private Boolean isStartStep;
    private String fileSize;
}
