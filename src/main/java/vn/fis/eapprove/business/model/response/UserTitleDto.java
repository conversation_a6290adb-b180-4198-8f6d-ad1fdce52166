package vn.fis.eapprove.business.model.response;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class UserTitleDto {
    private Long id;
    private Long userInfoId;
    private Long chartNodeId;
    private String positionCode;
    private String positionName;
    private String titleCode;
    private String titleName;
    private String managerLevel;
    private Integer concurrently;
    private LocalDate concurrentlyBeginDate;
    private LocalDate concurrentlyEndDate;
    private List<Long> chartNodeIdSecretary;

}