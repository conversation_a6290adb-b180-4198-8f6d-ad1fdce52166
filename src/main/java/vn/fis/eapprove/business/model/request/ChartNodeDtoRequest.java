package vn.fis.eapprove.business.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@JsonIgnoreProperties
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChartNodeDtoRequest {
    List<String> lstCodes;
    List<Long> lstIds;
    List<Long> lstNodeIds;
    Boolean isGetChildren;
}
