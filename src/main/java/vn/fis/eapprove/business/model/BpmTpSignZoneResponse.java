package vn.fis.eapprove.business.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Author: PhucVM
 * Date: 07/02/2023
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BpmTpSignZoneResponse implements Serializable {
    private Long ticketId;
    private String requestCode;
    private String sign;
    private String position;
    private String lastName;
    private String firstName;
    private Date signedDate;
}
