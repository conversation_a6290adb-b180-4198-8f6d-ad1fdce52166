package vn.fis.eapprove.business.model.response;

import lombok.Data;
import vn.fis.eapprove.business.dto.ServicePackageDto;

import java.util.List;

@Data
public class LoadAssignResponse {
    private Long id;
    private String assignName;
    private String assignUser;
    private String assignedUser;
    private Long ticketId;
    private List<ServicePackageDto> serviceRange;
    private String startDate;
    private String endDate;
    private Integer status;
    private Integer effect;
    private String description;
    private String createdDate;
    private String createdUser;
    private String updatedDate;
    private String updatedUser;
    private String fileName;
    private String base64;
    private String newTicket;
    private String authorityConditions;
    private Long serviceId;
    private String requestCode;
    private String newRequestCode;
    private String assignTitle;
    private String titleName;
    private Integer historyStatus;
    private String companyCode;
    private String companyName;
}
