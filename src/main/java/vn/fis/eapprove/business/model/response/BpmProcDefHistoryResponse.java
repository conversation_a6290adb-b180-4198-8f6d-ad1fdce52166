package vn.fis.eapprove.business.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BpmProcDefHistoryResponse {
    private Long id;
    private String version;
    private LocalDateTime createdDate;
    private String createdUser;
    private String contentEdit;
    private Boolean statusHistory;
}
