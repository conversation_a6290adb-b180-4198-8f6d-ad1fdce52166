package vn.fis.eapprove.business.model.response;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DiscussionResponse {
    private Long id;
    private String procInstId;
    private Long typeDiscussion;
    private Long groupId;
    private Long ticketId;
    private String content;
    private String createdUser;
    private Date createdDate;
    private List<DiscussionResponse> discussionResponse;
    private List<DiscussionResponse> discussionReplys;
    private List<FileResponse> fileResponse;
    private Integer statusRequest;
}
