package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.util.List;

@Data
public class ServicePackageRequest {
    private Long id;
    private String serviceName;
    private String icon;
    private Long processId;
    private String description;
    private String note;
    private Boolean notShowingWebsite;
    private Boolean notShowingMoblie;
    private List<String> hideName;
    private List<String> visibleName;
    private List<Integer> hideGroup;
    private List<Integer> visibleGroup;
    private List<String> visibleChart;
    private List<String> hideChart;


    private Long parentId;
    private String color;
    private Integer serviceType;

    private String url;
    private Integer positionPackage;
    private List<Long> blockLocation;
    private Long idOrgChart;
    private Long masterParentId;
    private Boolean deleted;
    private List<Long> listIds;
    private Boolean hideAllUser;
    private Boolean visibleAllUser;
    private Long submissionType;

    // Phân quyền dữ liệu
    private List<String> applyFor;

    // Clone luồng đặc biệt
    private Boolean specialFlow;
    private Boolean specialApplyFor;
    private List<String> parentCompanyCode;

    //Ctr lập phap
    private Boolean legislativeRequirement;
}

