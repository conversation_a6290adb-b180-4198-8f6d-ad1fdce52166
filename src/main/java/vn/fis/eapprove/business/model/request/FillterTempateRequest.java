package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.util.List;

@Data
public class FillterTempateRequest extends BasePageRequest {
    private List<Integer> status;
    private List<String> urlName;
    private List<String> createdUser;
    private List<Integer> id;
    private List<String> procDefIds;
//    private List<TemplateDateFilterDto> listDateFilter;
    private List<String> listCompanyCode;
    private String username;
    private Boolean specialFlow;
    private Boolean isShared;

    private List<String> modifiedUser;
    private List<String> templateName;
    private List<String> companyCode;
    private List<String> companyName;
    private List<String> ignoreList;
    private List<Long> lstGroupPermissionId;
    private List<String> listCompanyCodeMemberAdmin;
}
