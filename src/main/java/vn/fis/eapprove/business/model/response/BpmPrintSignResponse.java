package vn.fis.eapprove.business.model.response;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BpmPrintSignResponse {

    private Long id;
    private String name;
    private String descr;
    private String procDefId;
    private String html;
    private String pdfContent;
    private int printType;
    private String createdUser;
    private Date createdDate;
    private String updatedUser;
    private Date updatedDate;
    private List<BpmPrintTaskReponse> tasks;
    private List<BpmPrintSignZoneResponse> signs;
    private String signedFile;
    private String storageUrl;
}
