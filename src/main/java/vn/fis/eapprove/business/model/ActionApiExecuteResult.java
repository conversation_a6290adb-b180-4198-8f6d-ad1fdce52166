package vn.fis.eapprove.business.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.dto.ActionApiDto;

import java.io.Serializable;
import java.util.Map;

/**
 * Author: PhucVM
 * Date: 05/12/2022
 */
@Data
@NoArgsConstructor
public class ActionApiExecuteResult implements Serializable {

    private static final long serialVersionUID = 6224621148863740094L;

    private ActionApiDto actionApi;
    private Long bpmProcinstId;
    private String procInstId;
    private boolean isCalled;
    private boolean isSuccess;
    private boolean continueOnError;
    private Map<String, Object> variables;
    private Object response;
    private boolean returnResponse;
    private String username;
    private ActionApiResult actionApiResult = new ActionApiResult();

    public ActionApiExecuteResult(ActionApiDto actionApi) {
        this.actionApi = actionApi;
    }

    public ActionApiExecuteResult(ActionApiDto actionApi, Long bpmProcinstId, String procInstId, Map<String, Object> variables,String username) {
        this.actionApi = actionApi;
        this.bpmProcinstId = bpmProcinstId;
        this.procInstId = procInstId;
        this.variables = variables;
        this.username = username;
    }
}
