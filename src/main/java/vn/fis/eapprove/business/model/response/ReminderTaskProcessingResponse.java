package vn.fis.eapprove.business.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ReminderTaskProcessingResponse {
    private Long id;
    private Long priorityId;
    private String assignee;
    private String taskDefKey;
    private String ticketProcInstId;
    private Long ticketId;
    private LocalDateTime createdTime;
    private LocalDateTime slaFinishTime;

}
