package vn.fis.eapprove.business.model.response;

import lombok.Data;

import java.util.Date;

@Data
public class FeatureActionDetailResponse {

    private Long id;
    private Long actionId;
    private String actionName;
    private String actionDescription;
    private Long featureId;
    private String keyRole;
    private Boolean sendNotification;
    private String mailSubject;
    private String mailContent;
    private String iconNotification;
    private String webNotification;
    private String mobileNotification;
    private Boolean saveHistory;
    private Boolean changeable;
    private Date createdDate;
    private String createdUser;
    private Date modifiedDate;
    private String modifiedUser;
}
