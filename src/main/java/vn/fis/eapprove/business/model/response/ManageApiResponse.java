package vn.fis.eapprove.business.model.response;

import lombok.Data;

import java.util.Date;

@Data
public class ManageApiResponse {
    private Long id;
    private String nameAPI;
    private String url;
    private String ticketPhase;
    private String description;
    private String ticketId;
    private String ticketName;
    private Integer status;
    private String createdUser;
    private Date createdDate;
    private String modifiedUser;
    private Date modifiedDate;
}
