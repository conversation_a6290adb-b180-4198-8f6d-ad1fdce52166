package vn.fis.eapprove.business.model.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.utils.DateToLocalDateTime;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class MyTaskResponse {

    private Long id;
    private Long ticketId;
    private String taskId;
    private String procInstId;
    private String ticketEndActId;
    private String ticketStartActId;
    private String createdUser;
    private String taskDefKey;
    private Boolean assignType;
    private String taskStatus;
    private String procTitle;
    private String taskName;
    private String serviceName;
    private Long taskPriority;
    private String startUser;
    private Date taskCreatedTime;
    private Date taskFinishedTime;
    private Date taskStartedTime;
    private Date taskCanceledTime;
    private String cancelReason;
    private Double slaFinish;
    private Double slaResponse;
    private String ticketProcDefId;
    private Date slaFinishTime;
    private long remainingTime;
    private String priorityName;
    private String priorityColor;
    private String requestCode;
    private String companyCode;
    private Double ticketRating;
    private String orgAssignee;
    private Date ticketCreatedTime;
    private String comment;
    private String actionUser;
    private Integer historyAssignType;
    private Boolean isExpired;
    private Long serviceId;
    private String currentAssignee;
    private String ticketStatus;
    private String chartNodeName;
    private String cancelActionUser;
    private List<TaskDetailResponse> ticketTaskDtoList;

//    public MyTaskResponse(Long id, Long ticketId, String taskId, String procInstId, String taskDefKey,
//                          String taskStatus, String procTitle, String taskName, String serviceName, String taskPriority,
//                          String startUser, LocalDateTime taskCreatedTime, LocalDateTime taskFinishedTime, LocalDateTime taskStartedTime,
//                          LocalDateTime taskCanceledTime, String cancelReason, Double slaFinish, Double slaResponse, String ticketProcDefId,
//                          LocalDateTime slaFinishTime, String remainingTime) {
//        this(id, )
//    }

    public MyTaskResponse(Long id, Long ticketId, String taskId, String procInstId, String ticketEndActId, String ticketStartActId, String createdUser, String taskDefKey,
                          Boolean assignType, String taskStatus, String procTitle, String taskName, String serviceName, Long taskPriority,
                          String startUser, LocalDateTime taskCreatedTime, LocalDateTime taskFinishedTime, LocalDateTime taskStartedTime,
                          LocalDateTime taskCanceledTime, String cancelReason, Double slaFinish, Double slaResponse, String ticketProcDefId,
                          LocalDateTime slaFinishTime, String remainingTime, String priorityName, String priorityColor, String requestCode,
                          String companyCode, Double ticketRating, String orgAssignee, LocalDateTime ticketCreatedTime, String comment,
                          String actionUser, Integer historyAssignType, Boolean isExpired, Long serviceId, String currentAssignee, String ticketStatus, String chartNodeName, String cancelActionUser) {
        this.id = id;
        this.ticketId = ticketId;
        this.taskId = taskId;
        this.procInstId = procInstId;
        this.ticketEndActId = ticketEndActId;
        this.ticketStartActId = ticketStartActId;
        this.createdUser = createdUser;
        this.taskDefKey = taskDefKey;
        this.assignType = assignType;
        this.taskStatus = taskStatus;
        this.procTitle = procTitle;
        this.taskName = taskName;
        this.serviceName = serviceName;
        this.taskPriority = taskPriority;
        this.startUser = startUser;
        if (taskCreatedTime != null) {
            this.taskCreatedTime = DateToLocalDateTime.convertToDate(taskCreatedTime);
        }
        if (taskFinishedTime != null) {
            this.taskFinishedTime = DateToLocalDateTime.convertToDate(taskFinishedTime);
        }
        if (taskStartedTime != null) {
            this.taskStartedTime = DateToLocalDateTime.convertToDate(taskStartedTime);
        }
        if (taskCanceledTime != null) {
            this.taskCanceledTime = DateToLocalDateTime.convertToDate(taskCanceledTime);
        }
        if (slaFinishTime != null) {
            this.slaFinishTime = DateToLocalDateTime.convertToDate(slaFinishTime);
        }
        this.cancelReason = cancelReason;
        this.slaFinish = slaFinish;
        this.slaResponse = slaResponse;
        this.ticketProcDefId = ticketProcDefId;
        LocalDateTime now = LocalDateTime.now();
        if (slaFinishTime != null) {
            if (now.isAfter(slaFinishTime)) {
                long diff = Math.abs(Duration.between(now, slaFinishTime).toMillis());
                this.remainingTime = diff * (-1);
            } else {
                long diff = Math.abs(Duration.between(slaFinishTime, now).toMillis());
                this.remainingTime = diff;
            }
        } else this.remainingTime = 0;
        this.priorityName = priorityName;
        this.priorityColor = priorityColor;
        this.requestCode = requestCode;
        this.companyCode = companyCode;
        this.ticketRating = ticketRating;
        this.orgAssignee = orgAssignee;
        if (ticketCreatedTime != null) {
            this.ticketCreatedTime = DateToLocalDateTime.convertToDate(ticketCreatedTime);
        }
        this.comment = comment;
        this.actionUser = actionUser;
        this.historyAssignType = historyAssignType;
        this.isExpired = isExpired;
        this.serviceId = serviceId;
        this.currentAssignee = currentAssignee;
        this.ticketStatus = ticketStatus;
        this.chartNodeName = chartNodeName;
        this.cancelActionUser = cancelActionUser;
    }

    public MyTaskResponse(Long id, Long ticketId, String taskId, String procInstId, String ticketEndActId, String ticketStartActId, String createdUser, String taskDefKey,
                          Boolean assignType, String taskStatus, String procTitle, String taskName, String serviceName, Long taskPriority,
                          String startUser, LocalDateTime taskCreatedTime, LocalDateTime taskFinishedTime, LocalDateTime taskStartedTime,
                          LocalDateTime taskCanceledTime, String cancelReason, Double slaFinish, Double slaResponse, String ticketProcDefId,
                          LocalDateTime slaFinishTime, String remainingTime, String priorityName, String priorityColor, String requestCode,
                          String companyCode, Double ticketRating, String orgAssignee, LocalDateTime ticketCreatedTime, String comment,
                          String actionUser, Integer historyAssignType, Boolean isExpired, Long serviceId, String currentAssignee, String ticketStatus, String chartNodeName) {
        this.id = id;
        this.ticketId = ticketId;
        this.taskId = taskId;
        this.procInstId = procInstId;
        this.ticketEndActId = ticketEndActId;
        this.ticketStartActId = ticketStartActId;
        this.createdUser = createdUser;
        this.taskDefKey = taskDefKey;
        this.assignType = assignType;
        this.taskStatus = taskStatus;
        this.procTitle = procTitle;
        this.taskName = taskName;
        this.serviceName = serviceName;
        this.taskPriority = taskPriority;
        this.startUser = startUser;
        if (taskCreatedTime != null) {
            this.taskCreatedTime = DateToLocalDateTime.convertToDate(taskCreatedTime);
        }
        if (taskFinishedTime != null) {
            this.taskFinishedTime = DateToLocalDateTime.convertToDate(taskFinishedTime);
        }
        if (taskStartedTime != null) {
            this.taskStartedTime = DateToLocalDateTime.convertToDate(taskStartedTime);
        }
        if (taskCanceledTime != null) {
            this.taskCanceledTime = DateToLocalDateTime.convertToDate(taskCanceledTime);
        }
        if (slaFinishTime != null) {
            this.slaFinishTime = DateToLocalDateTime.convertToDate(slaFinishTime);
        }
        this.cancelReason = cancelReason;
        this.slaFinish = slaFinish;
        this.slaResponse = slaResponse;
        this.ticketProcDefId = ticketProcDefId;
        LocalDateTime now = LocalDateTime.now();
        if (slaFinishTime != null) {
            if (now.isAfter(slaFinishTime)) {
                long diff = Math.abs(Duration.between(now, slaFinishTime).toMillis());
                this.remainingTime = diff * (-1);
            } else {
                long diff = Math.abs(Duration.between(slaFinishTime, now).toMillis());
                this.remainingTime = diff;
            }
        } else this.remainingTime = 0;
        this.priorityName = priorityName;
        this.priorityColor = priorityColor;
        this.requestCode = requestCode;
        this.companyCode = companyCode;
        this.ticketRating = ticketRating;
        this.orgAssignee = orgAssignee;
        if (ticketCreatedTime != null) {
            this.ticketCreatedTime = DateToLocalDateTime.convertToDate(ticketCreatedTime);
        }
        this.comment = comment;
        this.actionUser = actionUser;
        this.historyAssignType = historyAssignType;
        this.isExpired = isExpired;
        this.serviceId = serviceId;
        this.currentAssignee = currentAssignee;
        this.ticketStatus = ticketStatus;
        this.chartNodeName = chartNodeName;
    }
}
