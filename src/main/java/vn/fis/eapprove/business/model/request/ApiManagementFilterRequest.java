package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.util.List;

@Data
public class ApiManagementFilterRequest extends BasePageRequest {
    private List<Integer> listStatus;
    private List<String> listType;
    private List<String> listMethod;
    private List<String> listCompanyCode;
    private Boolean isShared;
    private List<String> createdUser;
    private List<String> updatedUser;

    private List<String> name;
    private List<String> url;
    private List<String> description;
    private List<String> shareWith;
//    private List<TicketDateFilterDto> listDateFilter;
    private List<String> companyCode;
    private List<String> companyName;

    private List<DateRequest> dateRequest;
    private List<Long> lstGroupPermissionId;
    private List<String> listCompanyCodeMemberAdmin;
}
