package vn.fis.eapprove.business.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: PhucVM
 * Date: 06/03/2023
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActionApiParam {

    private String function;
    private String varName;
    private String attribute;
    private String template;

    public ActionApiParam(String function, String varName, String attribute) {
        this.function = function;
        this.varName = varName;
        this.attribute = attribute;
    }
}
