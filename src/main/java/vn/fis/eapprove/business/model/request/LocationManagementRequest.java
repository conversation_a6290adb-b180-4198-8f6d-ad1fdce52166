package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class LocationManagementRequest {
    private Long id;
    private String abbreviations;
    private String locationName;
    private String chart_name;
    private Integer chart_id;
    private String address;
    private String workingTimeCode;
    private String fileImport;
    private String createdUser;
    private LocalDateTime createdDate;
    private String modifiedUser;
    private LocalDateTime modifiedDate;
}
