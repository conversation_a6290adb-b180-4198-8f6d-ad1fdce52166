package vn.fis.eapprove.business.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import vn.fis.spro.common.model.DateTimeFilter;

import java.util.List;

/**
 * Author: PhucVM
 * Date: 08/11/2022
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TicketAssistantFilter {

    private String tab;
    private String taskType;
    private String searchKey;
    private String assistantEmail;
    private List<String> lstTicketId;
    private List<String> users;
    private List<String> assignees;
    private List<String> status;
    private List<String> services;
    private List<DateTimeFilter> dateTimeFilters;
}
