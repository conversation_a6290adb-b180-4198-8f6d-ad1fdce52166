package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class HistoryDto extends BasePageRequest {
    private Long ticketId;
    private String procInstId;
    private String taskInstId;
    private String fromTaskKey;
    private String toTaskKey;
    private String action;
    private List<String> listAction;
    private List<String> listActionUser;
    private String note;
    private String actionUser;
    private String fromTask;
    private String toTask;
    private String taskAssignee;
    private String affectedTask;
    private String taskDefKey;
    private String taskType;
    private String oldTaskId;
    private String oldProcInstId;
    private String attachFiles;
    private String attachFilesName;
    private String attachFilesSize;
    private String oldDefaultField;
    private LocalDateTime receivedTime;
    private String actionUserInfo;
}
