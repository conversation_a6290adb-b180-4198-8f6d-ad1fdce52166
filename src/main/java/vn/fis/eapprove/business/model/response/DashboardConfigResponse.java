package vn.fis.eapprove.business.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.model.request.DashboardRequest;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DashboardConfigResponse {
    private Long id;
    private String name;
    private String username;
    private DashboardRequest filter;
    private Boolean isShow;
    private String type;
    private Boolean isClone;
}
