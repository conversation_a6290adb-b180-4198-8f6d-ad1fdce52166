package vn.fis.eapprove.business.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssigneeInfo {
    private String username;
    private String fullName;
    private String title;
    private String completedTime;
    private LocalDateTime createdTime;
    private LocalDateTime finishTime;
    private String taskStatus;
    private Long taskId;
    private String companyCode;
    private String chartShortName;
    private String chartName;
}
