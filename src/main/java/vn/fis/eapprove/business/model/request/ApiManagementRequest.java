package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.util.List;

@Data
public class ApiManagementRequest {
    List<String> shareWith;
    private Long id;
    private String name;
    private String url;
    private String method;
    private String header;
    private String body;
    private String tokenAttribute;
    private Integer status;
    private String response;
    private Integer returnResponse;
    private String errorAttribute;
    private String successCondition;
    private Integer continueOnError;
    private Long authenApiId;
    private Long baseUrlId;
    private String description;
    private String type;
    private Integer isDeleted;

    private List<String> applyFor;

}
