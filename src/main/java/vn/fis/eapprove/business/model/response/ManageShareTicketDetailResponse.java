package vn.fis.eapprove.business.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ManageShareTicketDetailResponse {
    private Long id;
    private String name;
    private String description;
    private String status;
    private String fromDate;
    private String toDate;
    private List<String> listAssignee;
    private List<Long> listServiceId;
    private List<String> listChartNodeCode;
    private List<String> listCompanyCode;
    private List<String> listCreatedUser;
    private List<String> listShareUser;
    private List<String> applyFor;
}
