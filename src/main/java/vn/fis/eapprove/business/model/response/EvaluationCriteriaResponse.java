package vn.fis.eapprove.business.model.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class EvaluationCriteriaResponse {
    private Long id;

    private String reviewItem;

    private String name;

    private String description;

    private String status;

    private LocalDateTime createdTime;

    private String createdUser;

    private LocalDateTime updatedTime;

    private String updatedUser;

    private List<String> lstDepartments;

    private List<String> applyFor;
    private String companyCode;
    private String companyName;

}
