package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class MasterDataRequest {
    private Long id;
    private Long oldId;
    private String masterName;
    private String description;
    private String status;
    private String sheet;
    private Integer filterQuantity;
    private String file;
    private String protocol;
    private String dataType;
    private String apiKey;
    private String timeMaster;
    private String url;
    private Object headerParam;
    private Object bodyContent;
    private String error;
    private String cycleMaster;
    private Object email;
    private Boolean allowSystem;
    private Boolean statusMD;
    private Integer typeMD;
    private String bodyLogin;
    private String urlLogin;
    private Boolean enterType;
    private String enterAccount;
    private String modifiedUser;
    private Date createdDate;
    private String createdUser;
    private Date modifiedDate;
    private Object listResponse;
    private List<String> shareWith;
    private List<String> applyFor;
    private List<String> primaryKey;
    private List<String> validation;
    private Object newFile;
}
