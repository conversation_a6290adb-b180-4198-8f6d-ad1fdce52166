package vn.fis.eapprove.business.model.response;

import lombok.Data;

@Data
public class LastSectionResponse {
    private String serviceName;
    private Long total;
    private Long late;
    private Long punctual;
    private Long cancel;
    private Long requestUpdate;
    private Long outputBacklog;

    public LastSectionResponse() {
    }

    public LastSectionResponse(String serviceName, Long total, Long late, Long punctual, Long cancel, Long requestUpdate, Long outputBacklog) {
        this.serviceName = serviceName;
        this.total = total;
        this.late = late;
        this.punctual = punctual;
        this.cancel = cancel;
        this.requestUpdate = requestUpdate;
        this.outputBacklog = outputBacklog;
    }
}
