package vn.fis.eapprove.business.model.response;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class NotificationTemplateResponse {
    private Long id;
    private String type;
    private String title;
    private String actionCode;
    private String notificationObject;
    private String sourceType;
    private String shareWith;
    private String content;
    private Date createAt;
    private Date updateAt;
    private String userCreate;
    private String typeNoti;
    private String userUpdate;
    private List<String> applyFor;
    private String companyCode;
    private String companyName;
    private List<String> procDefApply;
}
