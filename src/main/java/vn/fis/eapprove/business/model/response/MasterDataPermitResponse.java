package vn.fis.eapprove.business.model.response;

import lombok.Data;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;


import java.util.List;

@Data
public class MasterDataPermitResponse {
    private List<String> companyCodes;
    private List<PermissionDataManagement> permissionDataManagements;
    private List<SharedUser> sharedUsers;
    private List<Long> groupPermitId;
}
