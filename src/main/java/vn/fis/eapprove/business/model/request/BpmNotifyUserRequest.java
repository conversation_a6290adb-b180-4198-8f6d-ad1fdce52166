package vn.fis.eapprove.business.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BpmNotifyUserRequest {

    private Map<String, VariableValueDto> variables;
    private Long bpmProcdefId;
    private String nextTaskDefKey;
    private String actionCode;
    private Boolean isGetOldVariable;
    private List<String> lstCustomerEmails;
}
