package vn.fis.eapprove.business.model.response;

import lombok.Data;

import java.util.List;

@Data
public class ChildResponse {
    private Long parentId;
    private Long id;
    private String specialCompanyCode;
    private String specialCompanyName;
    private String specialFormKey;
    private List<ChildTemplateResponse> templates;

    public ChildResponse(Long parentId,Long id,String specialCompanyCode,String specialCompanyName,String specialFormKey){
        this.parentId = parentId;
        this.id = id;
        this.specialCompanyCode = specialCompanyCode;
        this.specialCompanyName = specialCompanyName;
        this.specialFormKey = specialFormKey;
    }
}

