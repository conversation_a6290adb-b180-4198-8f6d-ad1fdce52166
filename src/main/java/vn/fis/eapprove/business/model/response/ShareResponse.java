package vn.fis.eapprove.business.model.response;

import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class ShareResponse {
    private Long id;
    private String taskId;
    private String procInstId;
    private String type;
    private String sharedUser;
    private String createdUser;
    private String createdDate;
    private Boolean isDeleted;
    private String sharedUserInfo;
    private String createdUserInfo;

    public String getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        this.createdDate = formatter.format(createdDate);
    }
}
