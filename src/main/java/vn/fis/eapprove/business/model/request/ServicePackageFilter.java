package vn.fis.eapprove.business.model.request;

import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import vn.fis.eapprove.business.dto.BaseDto;


import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ServicePackageFilter extends BaseDto {
    Boolean visibleAll;
    Long id;
    String processName;
    Long chartId;
    @NotNull(message = "serviceTypes is required")
    List<Integer> serviceTypes;
    Long processId;
    String serviceName;
    Boolean notShowingWeb;
    Boolean notShowingMobile;
    @NotNull(message = "createdUser is required")
    List<String> createdUser;
    String status;
    String fromDate;
    String toDate;
    List<List<Long>> listPosition;
}
