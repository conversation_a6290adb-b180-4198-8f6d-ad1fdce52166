package vn.fis.eapprove.business.model.response;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class VariablesResponse {
    private List<Map<String, Object>> currentTask;
    private List<Map<String, Object>> oldTask;
    private List<Map<String, Object>> currentProcInst;
    private List<Map<String, Object>> oldProcInst;
    private TicketDefaultResponse newDefaultField;
    private TicketDefaultResponse oldDefaultField;
    private String oldSignedFile;
    private String oldSignedFileName;
    private String newSignedFile;
    private String newSignedFileName;
}

