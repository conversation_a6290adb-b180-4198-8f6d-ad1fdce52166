package vn.fis.eapprove.business.model.response;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class SubmissionTypeResponse {
    private Long id;
    private String typeName;
    private String companyCode;
    private List<String> departmentCreate;
    private String description;
    private List<String> shareWith;
    private int scopeApply;
    private String createdUser;
    private String createdDate;
    private String modifiedUser;
    private String modifiedDate;
    private Integer status;
    private List<String> applyFor;
    private String companyName;
    private List<String> serviceApply;
}
