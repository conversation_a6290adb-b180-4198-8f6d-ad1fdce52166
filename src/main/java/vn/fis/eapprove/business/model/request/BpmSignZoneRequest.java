package vn.fis.eapprove.business.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties
public class BpmSignZoneRequest {
    private Long id;
    private Long orderSign = Long.parseLong("0");
    private String sign;
    private String fileType;
    private Float scale;
    private String email;
    private String firstName;
    private String lastName;
    private String position;
    private String taskDefKey;
    private String wResize;
    private String hResize;
    private Long page;
    private Long x;
    private Long y;
    private Long w;
    private Long h;
    private String signType;
    private String signedFile;
    private String chartNodeLevel;
}
