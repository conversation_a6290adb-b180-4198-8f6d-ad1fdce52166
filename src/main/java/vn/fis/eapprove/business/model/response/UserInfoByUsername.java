package vn.fis.eapprove.business.model.response;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = lombok.AccessLevel.PRIVATE)
public class UserInfoByUsername {
    String username;
    String fullName;
    String chartShortName;
    String chartNodeName;
    String chartNodeCode;
    String titleName;
    Long chartNodeId;
    String status;
    String staffCode;
    String directManager;
    Long userInfoId;
    String managerLevel;
    String email;
    Long chartId;
}