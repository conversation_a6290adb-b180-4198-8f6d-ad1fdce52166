package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class PaymentScheduleRequest {

    private String codeTicket;

    private String contractNumber;

    private String nameProject;

    private List<String> paymentInstallment;

    private Long ticketId;

    private String paymentAmount;

    private LocalDateTime paymentDeadline;

    private String approvedPaymentAmount;

    private String actualAmount;

    private String amountPaid;

    private LocalDateTime createDate;

    private String createUser;

    private String reason;

    private String frequency;

    private String companyCode;

    private String companyName;
}
