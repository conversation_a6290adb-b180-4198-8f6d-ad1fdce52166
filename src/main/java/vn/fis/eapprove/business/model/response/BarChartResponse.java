package vn.fis.eapprove.business.model.response;

import lombok.Data;

@Data
public class BarChartResponse {
    private Long waitingEvaluate;
    private Long alreadyEvaluate;
    private Long autoClose;
    private Long passTermEvaluate;
    private Double avgRating;

    public BarChartResponse() {
    }

    public BarChartResponse(Long waitingEvaluate, Long alreadyEvaluate, Long autoClose, Long passTermEvaluate) {
        this.waitingEvaluate = waitingEvaluate != null ? waitingEvaluate : 0l;
        this.alreadyEvaluate = alreadyEvaluate != null ? alreadyEvaluate : 0l;
        this.autoClose = autoClose != null ? autoClose : 0l;
        this.passTermEvaluate = passTermEvaluate != null ? passTermEvaluate : 0l;
    }
}
