package vn.fis.eapprove.business.model.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@SuperBuilder
public class ListReportByServiceResponse {

    private String serviceName;

    private String account;

    private String fullName;

    private int isApproval;

    private String positionCode;

    private int isCompleted;

    private int onTimeCompleted;

    private int lateCompleted;

    private int isProcessing;

    private int onTimeProcessing;

    private int lateProcessing;

    private int isReturn;

    private int isCancel;

    private String chartNodeName;

}
