package vn.fis.eapprove.business.model.response;

import lombok.Data;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefInherits;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefViewFileApi;
import vn.fis.eapprove.business.dto.LinkedBpmProcInstDto;
import vn.fis.eapprove.business.dto.SignedFileDto;
import vn.fis.eapprove.business.model.TicketRecall;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class TicketDetailResponse {

    private Long id;
    private String ticketId;
    private String ticketTitle;
    private String ticketCreatedUser;
    private String ticketProcDefId;
    private String ticketSla;
    private String endKey;
    private String startKey;
    private Date ticketEndTime;
    private Date ticketClosedTime;
    private Date ticketStartedTime;
    private Date ticketCanceledTime;
    private Date ticketCreatedTime;
    private Date ticketFinishTime;
    private String ticketStatus;
    private String procServiceName;
    private String ticketStartUserId;
    private Double ticketRating;
    private String comment;
    private List<String> listOwner;
    private List<String> listService;
    private List<String> listStatus;
    private List<String> listDate;
    private String user;
    private List<Map<String, Object>> ruData;
    private String contentRequest;
    private Long serviceId;
    private List<TaskDetailResponse> ticketTaskDtoList;
    private Boolean hideResult;
    private Boolean informTo;
    private Boolean updatePermission;
    private List<String> hideRuTasks;
    private Boolean ruPermission;
    private Boolean newAndClone;
    private Boolean autoInherits;
    private Boolean offNotifications;
    private List<BpmProcdefInherits> bpmProcdefInherits;
    private Double slaResponse;
    private Double slaFinish;
    private Double slaResponseProcess;
    private Double slaFinishProcess;
    private Date slaResponseTimeProcess;
    private Date slaFinishTimeProcess;
    private Date slaResponseTime;
    private Date slaFinishTIme;
    private Boolean editPermission;
    private List<Map<String, Object>> listSignForm;
    private String fullName;
    private String priority;
    private Long priorityId;
    private String color;
    private Map<String, Set<String>> taskUsers;
    private List<SignedFileDto> signedFiles;
    private String endNodeId;
    private List<LinkedBpmProcInstDto> linkedBpmProcInstDto;
    private String appCode;
    private String submissionTypeName;
    private String submissionType;

    private Boolean cancel;
    private Boolean hideInfo;
    private Boolean showInfo;
    private Boolean isAssistant;
    private Boolean isEditAssistant;
    private List<String> hideInfoTasks;
    private List<String> showInfoTasks;

    private Boolean additionalRequest;
    private Boolean recall;
    private Boolean showInputTask;
    private List<String> cancelTasks;
    private List<String> showInputTaskDefKeys;
    private Long procDefId;
    private String requestCode;
    private Long chartId;
    private Long chartNodeId;

    private List<String> changeImplementerValue;
    private TicketRecall ticketRecall;
    private String companyCode;

    private List<String> authorityOnTicketValue;
    private List<String> authorityOnTicketStep;
    private Boolean authorityOnTicket;

    private Boolean hideInherit;
    private List<String> hideInheritTasks;

    private Boolean hideComment;
    private List<String> hideCommentTasks;

    private Boolean hideDownload;
    private List<String> hideDownloadTasks;

    private Boolean hideShareTicket;

    private List<BpmProcdefViewFileApi> viewFileApi;

    private String forceViewUrl;

    private List<String> notifyUsers;

    private List<Map<String, Object>> cancelDraftVariables;

    private List<String> lstOrgAssignee;

    private String approvedBudgetIds;

    private Long legislativeId;

    public Date getTicketEndTime() {
        return ticketEndTime;
    }

    public void setTicketEndTime(LocalDateTime ticketEndTime) {
        if (ticketEndTime != null) {
            this.ticketEndTime = Date.from(ticketEndTime.atZone(ZoneId.systemDefault()).toInstant());
        } else {
            this.ticketEndTime = null;
        }
    }

    public Date getTicketClosedTime() {
        return ticketClosedTime;
    }

    public void setTicketClosedTime(LocalDateTime ticketClosedTime) {
        if (ticketClosedTime != null) {
            this.ticketClosedTime = Date.from(ticketClosedTime.atZone(ZoneId.systemDefault()).toInstant());
        } else {
            this.ticketClosedTime = null;
        }
    }

    public Date getTicketStartedTime() {
        return ticketStartedTime;
    }

    public void setTicketStartedTime(LocalDateTime ticketStartedTime) {
        if (ticketStartedTime != null) {
            this.ticketStartedTime = Date.from(ticketStartedTime.atZone(ZoneId.systemDefault()).toInstant());
        } else {
            this.ticketStartedTime = null;
        }
    }

    public Date getTicketFinishTime() {
        return ticketFinishTime;
    }

    public void setTicketFinishTime(LocalDateTime ticketFinishTime) {
        if (ticketFinishTime != null) {
            this.ticketFinishTime = Date.from(ticketFinishTime.atZone(ZoneId.systemDefault()).toInstant());
        } else {
            this.ticketFinishTime = null;
        }
    }

    public Date getTicketCanceledTime() {
        return ticketCanceledTime;
    }

    public void setTicketCanceledTime(LocalDateTime ticketCanceledTime) {
        if (ticketCanceledTime != null) {
            this.ticketCanceledTime = Date.from(ticketCanceledTime.atZone(ZoneId.systemDefault()).toInstant());
        } else {
            this.ticketCanceledTime = null;
        }
    }

    public Date getTicketCreatedTime() {
        return ticketCreatedTime;
    }

    public void setTicketCreatedTime(LocalDateTime ticketCreatedTime) {
        if (ticketCreatedTime != null) {
            this.ticketCreatedTime = Date.from(ticketCreatedTime.atZone(ZoneId.systemDefault()).toInstant());
        } else {
            this.ticketCreatedTime = null;
        }
    }
}
