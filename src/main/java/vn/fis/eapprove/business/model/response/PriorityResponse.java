package vn.fis.eapprove.business.model.response;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PriorityResponse {

    private Long id;
    private String code;
    private String name;
    private String color;
    private String alertTimeComplete;
    private List<String> shareWith;
    private int status;
    private Double slaValue;
    private String createdUser;
    private String createdDate;
    private String modifiedUser;
    private String modifiedDate;
    private String description;
    private Integer reminderBeingTime;
    private Integer reminderTime;
    private Integer reminderBeingValue;
    private Integer reminderValue;
    private String reminderBeingType;
    private String reminderType;
    private Boolean configReminder;
    private List<String> applyFor;
    private String companyName;
    private String companyCode;
    private Integer activeStatus;
}
