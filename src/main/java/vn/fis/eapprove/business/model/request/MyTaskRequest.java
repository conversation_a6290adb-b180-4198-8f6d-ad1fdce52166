package vn.fis.eapprove.business.model.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.fis.spro.common.model.request.DateFilterDto;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class MyTaskRequest extends BasePageRequest {

    private List<String> listService;
    private List<String> listUser;
    private List<String> listRating;
    private List<String> taskId;
    private String taskStatus;
    private List<String> type;
    private String fromDate;
    private String toDate;
    private String user;
    private List<String> listStatus;
//    private List<TicketDateFilterDto> listDateFilter;
    private List<String> listPriority;
    private boolean isFilterChangeAssignee;
    private List<String> listOrgAssign; // filter ủy quyền
    private List<String> listAssigned; // filter được ủy quyền
    private List<String> chartIds;
    private String search;

    //Add filter
    private List<String> listRequestCode;
    private List<String> listProcTitle;
    private List<String> listServiceName;
    private List<String> listCreatedUser;
    private List<String> listCompanyCode;
    private List<String> listChartNodeName;
    private List<String> listTaskName;
    private List<String> listTaskStatus;
    private List<String> listTicketCreatedTime;
    private List<String> listTaskCreatedTime;
    private List<String> listRemainingTime;
    private List<String> listTaskPriority;
    private List<String> listOrgAssignee;
    private List<String> listAssignType;
    private List<String> listId;
    private List<String> listTicketId;
    private List<String> listTaskNameExt;
    private List<String> listTaskStartedTime;
    private List<String> listTaskFinishedTime;
    private List<String> listActionUserCompleteTask;
    private List<String> listTaskCanceledTime;
    private List<String> listCancelUser;
    private List<String> listCancelActionUser;
    private List<String> listCancelReason;
    private List<String> listActionUser;
    private List<String> listCompanyName;
    private List<String> listModifiedUser;
    private List<String> listTaskAssignee;
    private List<String> listTicketStatus;
    private List<DateFilterDto> listDateFilter;
    private List<String> listSharedUser;
    private List<String> listDescription;
    private String searchBy;
    private String searchRoot;
    private List<String> listTicketTaskDtoTaskAssignee;

}
