package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SubmissionTypeRequest {
    private Long id;
    private String typeName;
    private List<String> departmentCreate;
    private String description;
    private List<String> shareWith;
    private int scopeApply;
    private String createdUser;
    private LocalDateTime createdDate;
    private String modifiedUser;
    private LocalDateTime modifiedDate;
    private Integer status;
    private List<String> applyFor;
}
