package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class PriorityRequest {
    private Long id;
    private String color;
    private String description;
    private String name;
    private Integer status;
    private List<String> shareWith;
    private Double slaValue;
    private String alertTimeComplete;
    private String createdUser;
    private LocalDateTime createdDate;
    private LocalDateTime modifiedDate;
    private String modifiedUser;
    private Integer reminderBeingTime;
    private Integer reminderTime;
    private String reminderBeingType;
    private String reminderType;
    private Integer reminderBeingValue;
    private Integer reminderValue;
    private Boolean configReminder;
    private List<String> applyFor;
}
