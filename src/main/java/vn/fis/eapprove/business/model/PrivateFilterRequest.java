package vn.fis.eapprove.business.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PrivateFilterRequest {
    private List<String> listAssignee;
    private List<String> listCreatedUser;
    private List<String> listTicketStatus;
    private String shareStatus;
    private List<String> listTaskDefKey;
    private List<Long> listService;
    private List<Long> listChartNodeId;
    private List<Long> listChartId;
    private Boolean isAssistant;
    private Boolean isNotification;
    private Boolean isDiscussion; // cần tham vấn
    private String ticketTitle;
    private String fromDate;
    private String toDate;
    // task
    private List<String> listTaskStatus;
    private List<String> listAssignStatus;
    private List<String> listAssignedStatus;
    private List<String> listApprovalByStatus;
    private List<String> listAdditionalStatus;
    private List<String> listAdditionalUser = new ArrayList<>();
    private List<Long> listTaskPriority;
    private Boolean hasDiscussion; // có tham vấn
    private String dateType;
}
