package vn.fis.eapprove.business.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketCompletedResponse {
    private Long ticketId;
    private String ticketTitle;
    private String requestCode;
    private String appCode;
    private String systemCode;
    private List<String> listAssignee;
    private Long serviceId;
    private String url;
    private String createdUser;
}
