package vn.fis.eapprove.business.model.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Duration;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class ReportTaskResponse {

    private Long id;
    private String title;
    private String taskName;
    private String serviceName;
    private String phaseType;
    private String assignee;
    private String priority;
    private Double rating;
    private String comment;
    private String status;
    private LocalDateTime taskFinishedTime;
    private LocalDateTime slaFinishedTime;
    private LocalDateTime taskStartedTime;

    private Long actualDuration;
    private Long lateDuration;

    public ReportTaskResponse(Long id, String title, String taskName, String serviceName, String phaseType, String assignee, String priority, Double rating, String comment, String status, LocalDateTime taskFinishedTime, LocalDateTime slaFinishedTime, LocalDateTime taskStartedTime) {
        this.id = id;
        this.title = title;
        this.taskName = taskName;
        this.serviceName = serviceName;
        this.phaseType = phaseType;
        this.assignee = assignee;
        this.priority = priority;
        this.rating = rating;
        this.comment = comment;
        this.status = status;
        this.taskFinishedTime = taskFinishedTime;
        this.slaFinishedTime = slaFinishedTime;
        this.taskStartedTime = taskStartedTime;

        if (taskFinishedTime != null) {
            if (slaFinishedTime == null) {
                LocalDateTime finishedTime = taskStartedTime.plusMinutes(4 * 60);
                slaFinishedTime = finishedTime;
            }
            Long actualDuration = Duration.between(taskStartedTime, taskFinishedTime).toMinutes();
            Long lateDuration = Duration.between(slaFinishedTime, taskFinishedTime).toMinutes();
            this.actualDuration = actualDuration;
            this.lateDuration = lateDuration < 0 ? null : lateDuration;
        }
    }
}
