package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ApiLogResquest {
    private Long id;

    private String url;

    private String method;

    private String header;

    private String requestBody;

    private LocalDateTime requestTime;

    private LocalDateTime responseTime;

    private String responseStatus;

    private String responseData;

    private Long bpmProcdefApiId;

    private Long bpmProcinstId;

    private String apiType;

    private LocalDateTime retryTime;

    private Integer retryCount = 0;

    private String realm;

    private boolean isLogAfter;

    private String procInstId;
}
