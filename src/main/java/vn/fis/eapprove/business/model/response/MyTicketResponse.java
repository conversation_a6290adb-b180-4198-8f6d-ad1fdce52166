package vn.fis.eapprove.business.model.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Data
@NoArgsConstructor
public class MyTicketResponse {
    private Long ticketId;
    private String ticketProcInstId;
    private String ticketEndActId;
    private String ticketStartActId;
    private String ticketStatus;
    private Long serviceId;
    private String serviceName;
    private String ticketTitle;
    private String ticketStartUserId;
    private Date ticketStartedTime;
    private Date ticketCreatedTime;
    private Date ticketFinishTime;
    private Date ticketClosedTime;
    private Date ticketCanceledTime;
    private Date ticketEditTime;
    private Date ticketEndTime;
    private Double slaFinish;
    private Double slaResponse;
    private String cancelReason;
    private Double ticketRating;
    private String comment;
    private String ticketProcDefId;
    private Long submissionTypeId;
    private String typeName;
    //    private String ticketShared;
    private String ticketPriority;
    private Double autoClose;
    private String requestCode;
    private String ticketCreatedUser;
    private String companyCode;
    private String actionUser;
    private Long chartId;
    private String chartName;
    private String chartNodeName;
    private Long processId;
//    private Long bpmSharedId;

    public MyTicketResponse(Long ticketId, String ticketProcInstId, String ticketEndActId, String ticketStartActId, String ticketStatus, Long serviceId, String serviceName, String ticketTitle,
                            String ticketStartUserId, LocalDateTime ticketStartedTime, LocalDateTime ticketCreatedTime, LocalDateTime ticketFinishTime, LocalDateTime ticketClosedTime,
                            LocalDateTime ticketCanceledTime, LocalDateTime ticketEditTime, LocalDateTime ticketEndTime, Double slaFinish, Double slaResponse, String cancelReason, Double ticketRating,
                            String comment, String ticketProcDefId, Long submissionTypeId, String typeName, String ticketPriority, Double autoClose, String requestCode, String ticketCreatedUser,
                            String companyCode,  String actionUser,Long chartId, String chartName, String chartNodeName, Long processId) {
        this.ticketId = ticketId;
        this.ticketProcInstId = ticketProcInstId;
        this.ticketEndActId = ticketEndActId;
        this.ticketStartActId = ticketStartActId;
        this.ticketStatus = ticketStatus;
        this.serviceId = serviceId;
        this.serviceName = serviceName;
        this.ticketTitle = ticketTitle;
        this.ticketStartUserId = ticketStartUserId;
        if (ticketStartedTime != null) {
            this.ticketStartedTime = Date.from(ticketStartedTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketCreatedTime != null) {
            this.ticketCreatedTime = Date.from(ticketCreatedTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketFinishTime != null) {
            this.ticketFinishTime = Date.from(ticketFinishTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketClosedTime != null) {
            this.ticketClosedTime = Date.from(ticketClosedTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketCanceledTime != null) {
            this.ticketCanceledTime = Date.from(ticketCanceledTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketEditTime != null) {
            this.ticketEditTime = Date.from(ticketEditTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketEndTime != null) {
            this.ticketEndTime = Date.from(ticketEndTime.atZone(ZoneId.systemDefault()).toInstant());
        }

        this.slaFinish = slaFinish;
        this.slaResponse = slaResponse;
        this.cancelReason = cancelReason;
        this.ticketRating = ticketRating;
        this.comment = comment;
        this.ticketProcDefId = ticketProcDefId;
//        this.ticketShared = ticketShared;
        this.submissionTypeId = submissionTypeId;
        this.typeName = typeName;
        this.ticketPriority = ticketPriority;
        this.autoClose = autoClose;
        this.requestCode = requestCode;
        this.ticketCreatedUser = ticketCreatedUser;
        this.companyCode = companyCode;
        this.chartId = chartId;
        if (actionUser != null) this.actionUser = actionUser;
        this.chartName = chartName;
        this.chartNodeName = chartNodeName;
        this.processId = processId;
//        this.bpmSharedId = bpmSharedId;
    }

    public MyTicketResponse(Long ticketId, String ticketProcInstId, String ticketEndActId, String ticketStartActId, String ticketStatus, Long serviceId, String serviceName, String ticketTitle,
                            String ticketStartUserId, LocalDateTime ticketStartedTime, LocalDateTime ticketCreatedTime, LocalDateTime ticketFinishTime, LocalDateTime ticketClosedTime,
                            LocalDateTime ticketCanceledTime, LocalDateTime ticketEditTime, LocalDateTime ticketEndTime, Double slaFinish, Double slaResponse, String cancelReason, Double ticketRating,
                            String comment, String ticketProcDefId, Long submissionTypeId, String typeName, String ticketPriority, Double autoClose, String requestCode, String ticketCreatedUser,
                            String companyCode, Long chartId, String chartName, String chartNodeName, Long processId) {
        this.ticketId = ticketId;
        this.ticketProcInstId = ticketProcInstId;
        this.ticketEndActId = ticketEndActId;
        this.ticketStartActId = ticketStartActId;
        this.ticketStatus = ticketStatus;
        this.serviceId = serviceId;
        this.serviceName = serviceName;
        this.ticketTitle = ticketTitle;
        this.ticketStartUserId = ticketStartUserId;
        if (ticketStartedTime != null) {
            this.ticketStartedTime = Date.from(ticketStartedTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketCreatedTime != null) {
            this.ticketCreatedTime = Date.from(ticketCreatedTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketFinishTime != null) {
            this.ticketFinishTime = Date.from(ticketFinishTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketClosedTime != null) {
            this.ticketClosedTime = Date.from(ticketClosedTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketCanceledTime != null) {
            this.ticketCanceledTime = Date.from(ticketCanceledTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketEditTime != null) {
            this.ticketEditTime = Date.from(ticketEditTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        if (ticketEndTime != null) {
            this.ticketEndTime = Date.from(ticketEndTime.atZone(ZoneId.systemDefault()).toInstant());
        }

        this.slaFinish = slaFinish;
        this.slaResponse = slaResponse;
        this.cancelReason = cancelReason;
        this.ticketRating = ticketRating;
        this.comment = comment;
        this.ticketProcDefId = ticketProcDefId;
//        this.ticketShared = ticketShared;
        this.submissionTypeId = submissionTypeId;
        this.typeName = typeName;
        this.ticketPriority = ticketPriority;
        this.autoClose = autoClose;
        this.requestCode = requestCode;
        this.ticketCreatedUser = ticketCreatedUser;
        this.companyCode = companyCode;
        this.chartId = chartId;
        this.chartName = chartName;
        this.chartNodeName = chartNodeName;
        this.processId = processId;
//        this.bpmSharedId = bpmSharedId;
    }

    public MyTicketResponse(Long ticketId, String ticketProcInstId, String ticketEndActId, String ticketStatus, Long serviceId, String serviceName, String ticketTitle,
                            String ticketStartUserId, String ticketCreatedUser, LocalDateTime ticketStartedTime, LocalDateTime ticketCreatedTime, LocalDateTime ticketFinishTime,
                            LocalDateTime ticketClosedTime, LocalDateTime ticketCanceledTime, LocalDateTime ticketEditTime, LocalDateTime ticketEndTime, Double slaFinish, Double slaResponse,
                            String cancelReason, Double ticketRating, String comment, String ticketProcDefId, Long submissionTypeId, String typeName, String ticketPriority, Double autoClose, String companyCode, Long chartId, String actionUser,
                            String chartName, String chartNodeName) {
        this(ticketId, ticketProcInstId, ticketEndActId,
                "", ticketStatus, serviceId, serviceName, ticketTitle,
                ticketStartUserId, ticketStartedTime, ticketCreatedTime, ticketFinishTime,
                ticketClosedTime, ticketCanceledTime, ticketEditTime, ticketEndTime, slaFinish,
                slaResponse, cancelReason, ticketRating, comment, ticketProcDefId, submissionTypeId,
                typeName, ticketPriority, autoClose, "", ticketCreatedUser, companyCode, actionUser,chartId, chartName, chartNodeName, null);
    }
}
