package vn.fis.eapprove.business.model.request;

import lombok.Data;

import java.util.List;

@Data
public class SearchShareTicketRequest extends BasePageRequest {
    private String searchName;
    private String search;
    private String status;
    private List<String> listCompanyCode;

    // filter
    private List<String> name;
    private List<String> description;
    private List<String> companyCode;
    private List<String> companyName;
    private List<String> createdUser;
    private List<String> updatedUser;
    private List<DateRequest> dateRequest;
}
