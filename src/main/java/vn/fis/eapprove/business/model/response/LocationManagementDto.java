package vn.fis.eapprove.business.model.response;

import lombok.Data;
import vn.fis.eapprove.business.dto.WorkingTimeChoose;

import java.util.List;

@Data
public class LocationManagementDto {
    private Long id;
    private String abbreviations;
    private String locationName;
    private String address;
    private WorkingTimeChoose workingTimeChoose;
    //    private ChartChoose chartChoose;
    private Boolean isUsed = false;
    private String status;
    private String createdUser;
    private String createdDate;
    private String modifiedUser;
    private String modifiedDate;
    private List<String> applyFor;
    private String workingTimeCode;
    private String companyCode;
    private String companyName;
}
