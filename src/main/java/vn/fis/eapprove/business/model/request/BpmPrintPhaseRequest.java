package vn.fis.eapprove.business.model.request;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Data
public class BpmPrintPhaseRequest {
    Long id;
    String formKey;
    List<BpmTaskPrintRequest> taskKey;
    String procDefId;
    String form;
    //bỏ
    String processName;
    String base64;
    String name;
    String description;
    int type = 0;
    List<String> shareWith;
    MultipartFile uploadWord;

    // html
    String htmlTemplate;
    String headerTemplate;
    String footerTemplate;
    Long htmlId;
    String size;
}
