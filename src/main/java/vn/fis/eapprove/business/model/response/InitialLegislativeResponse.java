package vn.fis.eapprove.business.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.fis.eapprove.business.dto.BpmProcdefDto;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InitialLegislativeResponse {
    List<Map<String, Object>> listTicket;
    List<BpmProcdefDto> listBpmProcdef;
}
