package vn.fis.eapprove.business.mapper;

import org.mapstruct.Mapper;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef;
import vn.fis.eapprove.business.dto.BpmProcdefDto;


/**
 * Generated by Speed Generator
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 */
@Mapper(componentModel = "spring")
public interface BpmProcdefMapper {
    BpmProcdef dtoToEntity(BpmProcdefDto bpmProcdefDto);

    BpmProcdefDto entityToDto(BpmProcdef bpmProcdef);
}