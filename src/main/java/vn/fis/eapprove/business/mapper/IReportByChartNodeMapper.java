package vn.fis.eapprove.business.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import vn.fis.eapprove.business.domain.report.entity.ReportByChartNode;
import vn.fis.eapprove.business.dto.ReportByChartNodeDto;

import vn.fis.spro.common.model.IObjectMapper;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
public interface IReportByChartNodeMapper extends IObjectMapper<ReportByChartNode, ReportByChartNodeDto> {
//    @Mapping(target = "ticketId", ignore = true)
    @Mapping(target = "id", ignore = true)
    ReportByChartNode merge(@MappingTarget ReportByChartNode target, ReportByChartNodeDto source);
}
