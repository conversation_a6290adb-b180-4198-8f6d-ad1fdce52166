package vn.fis.eapprove.business.mapper;

import org.mapstruct.Mapper;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.dto.BpmProcInstDto;


/**
 * Generated by Speed Generator
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 */
@Mapper(componentModel = "spring")
public interface BpmProcInstMapper {
    BpmProcInst dtoToEntity(BpmProcInstDto bpmProcInstDto);

    BpmProcInstDto entityToDto(BpmProcInst bpmProcInst);
}