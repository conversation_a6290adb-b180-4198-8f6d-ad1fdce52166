package vn.fis.eapprove.business.mapper;


import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import vn.fis.eapprove.business.domain.report.entity.ReportByGroupNew;
import vn.fis.eapprove.business.dto.ReportByGroupDto;

import vn.fis.spro.common.model.IObjectMapper;

@Mapper(componentModel = "spring",nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
public interface IReportByGroupMapperNew extends IObjectMapper<ReportByGroupNew, ReportByGroupDto> {
    @Mapping(target = "ticketId", ignore = true)
    @Mapping(target = "id", ignore = true)
    ReportByGroupNew merge(@MappingTarget ReportByGroupNew target, ReportByGroupDto source);
}
