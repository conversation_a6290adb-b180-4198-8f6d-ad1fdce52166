package vn.fis.eapprove.business.mapper;


import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import vn.fis.eapprove.business.domain.report.entity.ReportByGroup;
import vn.fis.eapprove.business.dto.ReportByGroupDto;
import vn.fis.spro.common.model.IObjectMapper;

@Mapper(componentModel = "spring",nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
public interface IReportByGroupMapper extends IObjectMapper<ReportByGroup, ReportByGroupDto> {
    @Mapping(target = "ticketId", ignore = true)
    @Mapping(target = "id", ignore = true)
    ReportByGroup merge(@MappingTarget ReportByGroup target, ReportByGroupDto source);
}
