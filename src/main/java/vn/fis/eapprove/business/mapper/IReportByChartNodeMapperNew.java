package vn.fis.eapprove.business.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import vn.fis.eapprove.business.domain.report.entity.ReportByChartNodeNew;
import vn.fis.eapprove.business.dto.ReportByChartNodeDto;

import vn.fis.spro.common.model.IObjectMapper;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
public interface IReportByChartNodeMapperNew extends IObjectMapper<ReportByChartNodeNew, ReportByChartNodeDto> {
//    @Mapping(target = "ticketId", ignore = true)
    @Mapping(target = "id", ignore = true)
    ReportByChartNodeNew merge(@MappingTarget ReportByChartNodeNew target, ReportByChartNodeDto source);
}
