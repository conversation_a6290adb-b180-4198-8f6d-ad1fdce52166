package vn.fis.eapprove.business.mapper;

import org.mapstruct.*;
import vn.fis.eapprove.business.domain.bpm.entity.BpmAdditionalRequest;
import vn.fis.eapprove.business.dto.BpmAdditionalRequestDto;


@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface BpmAdditionalRequestMapper {
    BpmAdditionalRequest toEntity(BpmAdditionalRequestDto bpmAdditionalRequestDto);

    BpmAdditionalRequestDto toDto(BpmAdditionalRequest bpmAdditionalRequest);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    BpmAdditionalRequest partialUpdate(BpmAdditionalRequestDto bpmAdditionalRequestDto, @MappingTarget BpmAdditionalRequest bpmAdditionalRequest);
}