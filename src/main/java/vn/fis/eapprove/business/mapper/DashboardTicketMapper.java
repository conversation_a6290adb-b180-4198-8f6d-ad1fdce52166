package vn.fis.eapprove.business.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import vn.fis.eapprove.business.domain.dashboard.entity.DashboardTicket;
import vn.fis.eapprove.business.dto.DashboardTicketDto;
import vn.fis.spro.common.model.IObjectMapper;

@Mapper(componentModel = "spring",nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
public interface DashboardTicketMapper extends IObjectMapper<DashboardTicket, DashboardTicketDto> {
    @Mapping(target = "ticketId", ignore = true)
    @Mapping(target = "id", ignore = true)
    DashboardTicket merge(@MappingTarget DashboardTicket target, DashboardTicketDto source);
}
