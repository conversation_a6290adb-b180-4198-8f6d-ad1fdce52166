package vn.fis.eapprove.business.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import vn.fis.eapprove.business.domain.dashboard.entity.DashboardTask;
import vn.fis.eapprove.business.dto.DashboardTaskDto;
import vn.fis.spro.common.model.IObjectMapper;

@Mapper(componentModel = "spring",nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
public interface DashboardTaskMapper extends IObjectMapper<DashboardTask, DashboardTaskDto> {
    @Mapping(target = "id", ignore = true)
    DashboardTask merge(@MappingTarget DashboardTask target, DashboardTaskDto source);
}
