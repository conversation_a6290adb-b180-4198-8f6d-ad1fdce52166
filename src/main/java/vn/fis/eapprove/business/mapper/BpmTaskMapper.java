package vn.fis.eapprove.business.mapper;

import org.mapstruct.Mapper;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.dto.BpmTaskDto;


/**
 * Generated by Speed Generator
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 */
@Mapper(componentModel = "spring")
public interface BpmTaskMapper {
    BpmTask dtoToEntity(BpmTaskDto bpmTaskDto);

    BpmTaskDto entityToDto(BpmTask bpmTask);
}