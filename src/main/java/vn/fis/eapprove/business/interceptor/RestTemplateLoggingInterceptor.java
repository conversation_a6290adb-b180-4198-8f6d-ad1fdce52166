package vn.fis.eapprove.business.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.api.service.ApiLogService;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * Author: PhucVM
 * Date: 21/11/2022
 */
@Component
@Slf4j
public class RestTemplateLoggingInterceptor implements ClientHttpRequestInterceptor {

    private final ApiLogService apiLogService;

    @Autowired
    public RestTemplateLoggingInterceptor(ApiLogService apiLogService) {
        this.apiLogService = apiLogService;
    }

    @NonNull
    @Override
    public ClientHttpResponse intercept(@NonNull HttpRequest request, @NonNull byte[] body, ClientHttpRequestExecution execution) throws IOException {
        long startTime = System.currentTimeMillis();

        String requestBody = new String(body, StandardCharsets.UTF_8);

        // execute request
        ClientHttpResponse response = execution.execute(request, body);

        long endTime = System.currentTimeMillis();

        // push logging queue
        apiLogService.logging(request, response, requestBody, startTime, endTime);

        return response;
    }
}
