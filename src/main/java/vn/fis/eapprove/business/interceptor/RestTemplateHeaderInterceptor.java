package vn.fis.eapprove.business.interceptor;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.constant.AppConstants;
import vn.fis.eapprove.business.domain.api.service.ApiLogService;
import vn.fis.eapprove.business.dto.AdditionApiInfoDto;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.TaskActionConstants;
import vn.fis.spro.common.helper.RestHelper;
import vn.fis.spro.common.util.HttpUtils;
import vn.fis.spro.common.util.ObjectUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Author: PhucVM
 * Date: 05/12/2022
 */
@Component
@Slf4j
public class RestTemplateHeaderInterceptor implements ClientHttpRequestInterceptor {

    private static final String[] WHITE_LIST = {
            "/login"
    };
    private final RestHelper restHelper;
    private final Common common;
    private final ApiLogService apiLogService;
    @Value("${action-api.remove-additional-headers}")
    private boolean removeAdditionalHeaders;

    @Autowired
    public RestTemplateHeaderInterceptor(RestHelper restHelper,
                                         @Lazy Common common,
                                         ApiLogService apiLogService) {
        this.restHelper = restHelper;
        this.common = common;
        this.apiLogService = apiLogService;
    }

    @NonNull
    @Override
    public ClientHttpResponse intercept(@NonNull HttpRequest request, @NonNull byte[] body, @NonNull ClientHttpRequestExecution execution) throws IOException {
        long startTime = System.currentTimeMillis();
        boolean isLogging = false; // if re-execute request when renew token then must have additional log
        if (isCheckToken(request)) {
            buildTaskActionApiAuthorization(request, false);

            // remove additional headers
            Map<String, List<String>> removedHeaders = new HashMap<>();
            if (removeAdditionalHeaders) {
                removedHeaders.putAll(removeAdditionalHeaders(request));
            }

            ClientHttpResponse response = execution.execute(request, body);
            if (response.getStatusCode().equals(HttpStatus.UNAUTHORIZED)) {
                response.close();

                // put additional headers again
                request.getHeaders().putAll(removedHeaders);

                // renew token
                buildTaskActionApiAuthorization(request, true);
                isLogging = true;
            } else {
                return response;
            }
        }

        // remove additional headers
        if (removeAdditionalHeaders) {
            removeAdditionalHeaders(request);
        }

        ClientHttpResponse response = execution.execute(request, body);

        long endTime = System.currentTimeMillis();

        if (isLogging) {
            String requestBody = new String(body, StandardCharsets.UTF_8);
            apiLogService.logging(request, response, requestBody, startTime, endTime);
        }

        return response;
    }

    private void buildTaskActionApiAuthorization(HttpRequest request, boolean isRenewToken) {
        HttpHeaders httpHeaders = request.getHeaders();
        if (ValidationUtils.isNullOrEmpty(httpHeaders)) {
            return;
        }

        String addtionApiInfo = HttpUtils.getHeaderValue(httpHeaders, CommonConstants.ApiHeader.ADDITIONAL_INFO);
        AdditionApiInfoDto additionApiInfoDto = ObjectUtils.toObject(addtionApiInfo, AdditionApiInfoDto.class);
        log.debug("DEBUG action api build author additionApiInfoDto={}", additionApiInfoDto);
        if (additionApiInfoDto == null) {
            return;
        }

        Long authenApiId = additionApiInfoDto.getAuthenApiId();

        log.debug("DEBUG action api build author authenApiId={}", authenApiId);
        // get token from cache
        if (authenApiId != null) {
//            if (isRenewToken) {
            AppConstants.Cache.AUTHEN_API_ID_TO_TOKEN.remove(authenApiId);
//            }

            String token = AppConstants.Cache.AUTHEN_API_ID_TO_TOKEN.get(authenApiId);
            if (ValidationUtils.isNullOrEmpty(token)) {
                // call authen api to get token
                token = getTokenFromAuthenApi(
                        additionApiInfoDto.getAuthenUrl(),
                        additionApiInfoDto.getAuthenMethod(),
                        additionApiInfoDto.getAuthenHeader(),
                        additionApiInfoDto.getAuthenBody(),
                        additionApiInfoDto.getTokenAttribute());

                if (!ValidationUtils.isNullOrEmpty(token)) {
                    AppConstants.Cache.AUTHEN_API_ID_TO_TOKEN.put(authenApiId, token);
                }
            }

            // if token is not null then put to cache map
            if (!ValidationUtils.isNullOrEmpty(token)) {
                // replace token to header
                Map<String, List<String>> replaceHeader = new HashMap<>();
                String finalToken = token;
                httpHeaders.forEach((key, val) -> {
                    if (!ValidationUtils.isNullOrEmpty(val)) {
                        List<String> replaceVals = val.stream().map(e -> {
                            if (e.contains(TaskActionConstants.Param.TOKEN)) {
                                return e.replaceAll(Pattern.quote(TaskActionConstants.Param.TOKEN), finalToken);
                            }

                            return e;
                        }).collect(Collectors.toList());

                        replaceHeader.put(key, replaceVals);
                    }
                });

                if (!ValidationUtils.isNullOrEmpty(replaceHeader)) {
                    httpHeaders.putAll(replaceHeader);
                }
            }
        }
    }

    private boolean isCheckToken(HttpRequest request) {
        // check url
        String url = request.getURI().getPath();
        if (url != null && Arrays.stream(WHITE_LIST).filter(
                        (st -> url.toLowerCase().contains(st)))
                .findAny()
                .orElse(null) != null) {
            return false;
        }

        // check headers - catch api-type TASK_ACTION
        HttpHeaders httpHeaders = request.getHeaders();
        return !ValidationUtils.isNullOrEmpty(httpHeaders)
                && HttpUtils.getApiType(httpHeaders).equals(CommonConstants.ApiType.TASK_ACTION);
    }

    /**
     * Call authen api to get token string
     */
    private String getTokenFromAuthenApi(String url, String method, String header, String body, String tokenAttribute) {
        long startTime = System.currentTimeMillis();
        log.info("Starting get token from authen api {}", url);

        try {
            // get header
            HttpHeaders headers = new HttpHeaders();
            if (ValidationUtils.isNullOrEmpty(header)) {
                headers.addAll(HttpUtils.getDefaultApplicationJsonHeaders());
            } else {
                HttpHeaders configHeader = HttpUtils.getHttpHeadersFromJson(header);
                if (configHeader == null) {
                    configHeader = HttpUtils.getDefaultApplicationJsonHeaders();
                }
                headers.addAll(configHeader);
            }
            headers.add(CommonConstants.ApiHeader.API_TYPE, CommonConstants.ApiType.TASK_ACTION);

            // get request body
            Object requestBody = null;
            if (!ValidationUtils.isNullOrEmpty(body)) {
                requestBody = ObjectUtils.toObject(body, new TypeReference<>() {
                });
            }

            // execute api
            ResponseEntity<Object> responseEntity = restHelper.exchange(url,
                    HttpMethod.valueOf(method.toUpperCase()),
                    headers,
                    requestBody,
                    new ParameterizedTypeReference<>() {
                    });

            Object responseBody = restHelper.getResponseBody(responseEntity);
            if (responseBody != null) {
                if (responseBody instanceof String || ValidationUtils.isNullOrEmpty(tokenAttribute)) {
                    return responseBody.toString();
                }

                if (responseBody instanceof Map) {
                    return common.evalExpressionByMVEL(tokenAttribute, responseBody, String.class);
                }
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            log.info("Finished get token from authen api {} in {} ms", url, System.currentTimeMillis() - startTime);
        }

        return "";
    }

    private Map<String, List<String>> removeAdditionalHeaders(HttpRequest request) {
        Map<String, List<String>> removedMap = new HashMap<>();
        HttpHeaders httpHeaders = request.getHeaders();
        if (ValidationUtils.isNullOrEmpty(httpHeaders)) {
            return removedMap;
        }

        List<String> removeKey = Arrays.asList(
                CommonConstants.ApiHeader.API_TYPE,
                CommonConstants.ApiHeader.ADDITIONAL_INFO);

        httpHeaders.forEach((key, val) -> {
            if (removeKey.contains(key)) {
                removedMap.put(key, httpHeaders.remove(key));
            }
        });

        return removedMap;
    }
}
