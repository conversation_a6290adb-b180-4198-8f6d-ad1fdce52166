package vn.fis.eapprove.business.utils.template.tableTemplate;

import vn.fis.eapprove.security.CredentialHelper;
import lombok.Data;
import org.docx4j.XmlUtils;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.wml.ObjectFactory;
import org.docx4j.wml.P;
import org.docx4j.wml.Tbl;
import org.docx4j.wml.Tr;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrintConfigUser;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTpSignZone;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTemplatePrintConfigUserRepository;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.manager.FileManager;

import java.util.List;
import java.util.Map;

@Data
public class TableSignTemplate implements CommonTable {

    private final Tbl table;
    Tr headerRow;
    Tr dataRow;
    Tr footerRow;
    FileManager fileManager;
    CredentialHelper credentialHelper;
    WordprocessingMLPackage wordMLPackage;
    private List<Object> rows;
    private BpmTemplatePrintConfigUserRepository bpmTemplatePrintConfigUserRepository;

    public TableSignTemplate(WordprocessingMLPackage wordMLPackage, Tbl table, FileManager fileManager, CredentialHelper credentialHelper) throws Exception {
//        this.table = XmlUtils.deepCopy(table);
        this.table = table;
        this.fileManager = fileManager;
        this.credentialHelper = credentialHelper;
        this.wordMLPackage = wordMLPackage;
        rows = getAllElementFromObject(this.table, Tr.class);
        if (rows != null) {
            // có 2 hàng mới lấy header
            if (rows.size() >= 2) {
                headerRow = (Tr) rows.get(0);
            }
            //Có 3 hàng thì lấy hàng 2 vì trừ footer
            if (rows.size() >= 3) {
                dataRow = (Tr) rows.get(rows.size() - 2);
                footerRow = (Tr) rows.get(rows.size() - 1);
            } else {
                dataRow = (Tr) rows.get(rows.size() - 1);
            }
        }
    }

    public Tbl fillWithData(List<BpmTpSignZone> bpmTpSignZoneList, boolean addPageBreak, List<BpmTemplatePrintConfigUser> lstConfigUser, String bucket) throws Exception {
        Tbl localTable = table;
        if (localTable != null) {
            localTable.getContent().clear();
            if (dataRow != null && bpmTpSignZoneList != null && bpmTpSignZoneList.size() > 0) {
                Integer index = 1;
                String tmpConfig;
                for (BpmTpSignZone itemTpSignZone : bpmTpSignZoneList) {
                    if (ValidationUtils.isNullOrEmpty(itemTpSignZone.getSign())) {
                        continue;
                    }
                    Tr dataRowClone = XmlUtils.deepCopy(dataRow);
                    List<Object> paragraphs = getAllElementFromObject(dataRowClone, P.class);
                    String taskDefKey = "@" + itemTpSignZone.getTaskDefKey();
                    //Kiểm tra xem có phải bước cấu hình không
                    P paragraph = (P) paragraphs.stream().filter(paragraphsItem -> {
                        String strConfig = StringUtil.nvl(paragraphsItem, "");
                        if (strConfig.indexOf(taskDefKey) > -1) {
                            Map<String, Object> itemConfigImage = getMapObjectFromJson(strConfig.replaceAll("\\$", ""));
                            if (StringUtil.nvl(itemConfigImage.get("imageUrl"), "").equalsIgnoreCase(taskDefKey)) {
                                return true;
                            } else {
                                return false;
                            }
                        } else {
                            return false;
                        }
                    }).findAny().orElse(null);
                    //Tìm thấy thì xử lý tiếp
                    if (paragraph != null) {
                        for (Object par : paragraphs) {
                            paragraph = (P) par;
                            if (paragraph != null) {
                                // Get the font size from the run properties
                                int fontSize = 12 * 2;
                                tmpConfig = StringUtil.nvl(paragraph, "");
                                if (tmpConfig.startsWith("${index")) {
//                                    addTextToPara(new ObjectFactory(), paragraph, "TRUE", String.valueOf(index), true, fontSize, false);
                                    addTextToParaDefault(new ObjectFactory(), paragraph, "TRUE", String.valueOf(index), false);
                                } else if (tmpConfig.startsWith("${comment")) {
//                                    addTextToPara(new ObjectFactory(), paragraph, "TRUE", itemTpSignZone.getComment(), false, fontSize, false);
//                                    addTextToParaDefault(new ObjectFactory(), paragraph, "TRUE", itemTpSignZone.getComment(),false);
//                                    String comment = StringUtil.nvl(itemTpSignZone.getComment(), "").replaceAll("<[^>]+>", "").replaceAll("&nbsp;", "");
//                                    String comment = convertHtmlToString(StringUtil.nvl(itemTpSignZone.getComment(), ""));
                                    String comment = StringUtil.nvl(itemTpSignZone.getComment(), "");
                                    addTextToParaDefault(new ObjectFactory(), paragraph, "TRUE", comment, false);
                                } else if (tmpConfig.indexOf(taskDefKey) > -1) {
                                    Map<String, Object> itemConfigImage = null;
                                    itemConfigImage = getMapObjectFromJson(tmpConfig.replaceAll("\\$", ""));
                                    if (itemConfigImage != null) {
                                        Integer targetWidth = getIntValueFromMap(itemConfigImage, "targetWidth", 150);
                                        Integer targetHeight = getIntValueFromMap(itemConfigImage, "targetHeight", 150);
                                        String imageUrl = StringUtil.nvl(itemTpSignZone.getSign(), "");
                                        String signatoryName = StringUtil.nvl(itemConfigImage.get("signatoryName"), "");
                                        String position = StringUtil.nvl(itemConfigImage.get("position"), "");
                                        String orgAssigneeTitle = StringUtil.nvl(itemConfigImage.get("orgAssigneeTitle"), "");
                                        String signDate = StringUtil.nvl(itemConfigImage.get("signDate"), "");
                                        String isExternalUrl = StringUtil.nvl(itemConfigImage.get("isExternalUrl"), "false");
                                        String isClear = StringUtil.nvl(itemConfigImage.get("isClear"), "true");
                                        String addPosition = StringUtil.nvl(itemConfigImage.get("addPosition"), "true");
                                        String addSignDate = StringUtil.nvl(itemConfigImage.get("addSignDate"), "true");
                                        String type = StringUtil.nvl(itemConfigImage.get("type"), "").trim().toUpperCase();
                                        String addSignImage = StringUtil.nvl(itemConfigImage.get("addSignImage"), "true");
                                        String addSignatoryName = StringUtil.nvl(itemConfigImage.get("addSignatoryName"), "true");
                                        String addSignAction = StringUtil.nvl(itemConfigImage.get("addSignAction"), "true");
                                        if (type.isEmpty()) {
                                            if (signatoryName.isEmpty()) {//lấy tên người
                                                // full name
                                                signatoryName = StringUtil.nvl(itemTpSignZone.getLastName(), "") + " " + StringUtil.nvl(itemTpSignZone.getFirstName(), "");
                                            }

                                            if (position.isEmpty()) {//lấy chức vụ
                                                // full name
                                                position = StringUtil.nvl(itemTpSignZone.getPosition(), "");
                                            }

                                            if (!ValidationUtils.isNullOrEmpty(itemTpSignZone.getOrgAssigneeTitle())) { //lấy chức vụ orgAssignee
                                                orgAssigneeTitle = "TUQ " + StringUtil.nvl(itemTpSignZone.getOrgAssigneeTitle(), "");
                                            }

                                            if (signDate.isEmpty()) {//lấy ngày kí
                                                // full name
                                                signDate = StringUtil.nvl(StringUtil.format(itemTpSignZone.getSignedDate(), "dd/MM/yyyy HH:mm"), "");
                                            }
                                        }

                                        // check theo bpm_template_print_config_user
                                        if (!ValidationUtils.isNullOrEmpty(lstConfigUser)) {
                                            BpmTemplatePrintConfigUser configUser = lstConfigUser.stream().filter(e -> e.getUsername().equalsIgnoreCase(itemTpSignZone.getEmail())).findFirst().orElse(null);
                                            if (configUser != null) {
                                                addPosition = configUser.getAddTitle();
                                                addSignDate = configUser.getAddSignDate();
                                                addSignImage = configUser.getAddSignImage();
                                                addSignatoryName = configUser.getAddName();
                                                addSignAction = configUser.getAddSignAction();
                                            }
                                        }

                                        if (addPosition != null && addPosition.equalsIgnoreCase("FALSE")) {
                                            orgAssigneeTitle = "";
                                            position = "";
                                        }
                                        if (addSignDate != null && addSignDate.equalsIgnoreCase("FALSE")) {
                                            signDate = "";
                                        }
                                        String signAction = "";
                                        if (addSignAction != null && addSignAction.equalsIgnoreCase("true") && itemTpSignZone.getChartNodeLevel() != null) {
                                            switch (itemTpSignZone.getChartNodeLevel()) {
                                                case "0":
                                                    signAction = "Đệ trình";
                                                    break;
                                                case "1":
                                                case "2":
                                                case "3":
                                                    signAction = "Phê duyệt";
                                                    break;
                                                default:
                                                    signAction = "Xét duyệt";
                                                    break;
                                            }
                                        }

                                        try {
                                            addImageToPara(wordMLPackage, new ObjectFactory(), paragraph,
                                                    "", 0, 1, isExternalUrl,
                                                    imageUrl,
                                                    targetWidth,
                                                    targetHeight, signatoryName, position, orgAssigneeTitle, signDate, 10 * 2,
                                                    isClear, fileManager, credentialHelper, addSignImage, addSignatoryName, bucket, signAction);
                                        } catch (Exception e) {
                                            throw new RuntimeException(e);
                                        }
                                    }
                                }
                            }
                        }
                        //Add lại header nếu có
                        if (localTable.getContent().size() == 0 && headerRow != null) {
                            localTable.getContent().add(headerRow);
                        }
                        //Add hàng
                        localTable.getContent().add(dataRowClone);
                        index++;
                    }

                }
            }
            if (addPageBreak) {
                localTable.getContent().add(createPageBreak());
            }
        }
        return localTable;
    }

    public String convertHtmlToString(String htmlString) {
        // Parse the HTML string with JSoup
        Document doc = Jsoup.parse(htmlString);

        // Get the text content of the HTML as a string
        String textContent = doc.body().text();

        return textContent;
    }

}

