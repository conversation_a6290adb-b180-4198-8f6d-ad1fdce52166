package vn.fis.eapprove.business.utils.template.tableTemplate;

import jakarta.xml.bind.JAXBException;
import org.docx4j.XmlUtils;
import org.docx4j.wml.Tbl;
import org.docx4j.wml.Tr;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class TableTemplate implements CommonTable {

    private final Tbl table;

    private List<Object> rows;
    private String headerRowAsString;
    private List<String> headerRowArr;
    private String dataRow;
    private String footerRowAsString;

    public TableTemplate(Tbl table, String addFooter) throws Exception {
//        this.table = XmlUtils.deepCopy(table);
        this.table = table;
        rows = getAllElementFromObject(this.table, Tr.class);
        headerRowArr = new ArrayList<>();
        if (rows != null) {
            // có 2 hàng mới lấy header
            if (rows.size() >= 2) {
                headerRowAsString = XmlUtils.marshaltoString(prepareVariables(rows.get(0)));
                headerRowArr.add(headerRowAsString);
            }
            //Có 3 hàng thì lấy hàng 2 vì trừ footer
            if (rows.size() >= 3 && addFooter.equalsIgnoreCase("TRUE")) {
                dataRow = XmlUtils.marshaltoString(prepareVariables(rows.get(rows.size() - 2)));
                footerRowAsString = XmlUtils.marshaltoString(prepareVariables(rows.get(rows.size() - 1)));
                for (int i = 0; i < rows.size() - 2; i ++ ) {
                    headerRowAsString = XmlUtils.marshaltoString(prepareVariables(rows.get(i)));
                    headerRowArr.add(headerRowAsString);
                }
            } else if (rows.size() >= 3 && addFooter.equalsIgnoreCase("FALSE")) {
                dataRow = XmlUtils.marshaltoString(prepareVariables(rows.get(rows.size() - 1)));
                for (int i = 0; i < rows.size() - 1; i ++ ) {
                    headerRowAsString = XmlUtils.marshaltoString(prepareVariables(rows.get(i)));
                    headerRowArr.add(headerRowAsString);
                }
            } else {
                dataRow = XmlUtils.marshaltoString(prepareVariables(rows.get(rows.size() - 1)));
            }
        }
        headerRowArr = headerRowArr.stream().distinct().collect(Collectors.toList());
    }

    public Tbl fillWithData(Map<String, String> mHeader, List<Map<String, String>> lstRowData, Map<String, String> mFooter, boolean addPageBreak) throws Exception {
//        Tbl localTable = XmlUtils.deepCopy(table);
        Tbl localTable = table;
        if (localTable != null) {
            localTable.getContent().clear();

            if (mHeader != null && headerRowAsString != null && headerRowArr !=null) {
                headerRowArr.forEach(header -> {
                    try {
                        localTable.getContent().add(getFilledData(mHeader, header));
                    } catch (JAXBException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            if (dataRow != null && lstRowData != null && lstRowData.size() > 0) {
                Integer index = 1;
                for (Map<String, String> dataItem : lstRowData) {
                    dataItem.put("index", String.valueOf(index));
                    localTable.getContent().add(getFilledData(dataItem, dataRow));
                    index++;
                }
            }
            if (mFooter != null && footerRowAsString != null) {
                localTable.getContent().add(getFilledData(mFooter, footerRowAsString));
            }
            if (addPageBreak) {
                localTable.getContent().add(createPageBreak());
            }
        }
        return localTable;
    }

    public void removeRow(int rowIndex) throws JAXBException {
        List<Object> rows = getAllElementFromObject(this.table, Tr.class);
        if (rowIndex < 0 || rowIndex >= rows.size()) {
            return;
        }
        rows.remove(rowIndex);
        table.getContent().remove(rowIndex);
    }

    public void addRow(int rowIndex, Map<String, String> mRowdata) throws JAXBException {
        List<Object> rows = getAllElementFromObject(this.table, Tr.class);
        if (rowIndex < 0 || rowIndex > rows.size()) {
            return;
        }
        if (mRowdata != null && dataRow != null) {
            table.getContent().add(rowIndex, (getFilledData(mRowdata, dataRow)));
        }
    }

    private Object getFilledData(Map<String, String> mappingsData, String strFormat) throws JAXBException {
//        StrSubstitutor strSubstitutor = new StrSubstitutor(mappingsData);
//        return XmlUtils.unmarshalString(strSubstitutor.replace(strFormat));
        if (mappingsData != null && strFormat != null) {
            // Stream the values and replace '&' with '&amp;'
            mappingsData = mappingsData.entrySet().stream().collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> {
                        String value = StringUtil.nvl(entry.getValue(), "");
                        if (value.contains("&")) {
                            value = value.replaceAll("&", "&amp;");
                        }
//                        if (value.contains("\n")) {
//                            value = value.replaceAll("\\n", "&#xA;");
//                        }
                        if (ValidationUtils.isNullOrEmpty(value)) {
                            value = "@{deletedValue}";
                        }
                        return value;
                    }
            ));


            Set<String> lstProperties = mappingsData.keySet();
            for (String key : lstProperties) {
                if (key != null) {
                    strFormat = strFormat.replaceAll("\\$\\{" + key + "\\}", StringUtil.nvl(mappingsData.get(key), ""));
                }
            }
        }
        return XmlUtils.unmarshalString(strFormat);
    }
}

