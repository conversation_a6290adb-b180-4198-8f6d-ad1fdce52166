package vn.fis.eapprove.business.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.WordUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.springframework.core.io.ClassPathResource;
import vn.fis.spro.common.util.ValidationUtils;

import java.io.IOException;

/**
 * Author: PhucVM
 * Date: 03/11/2022
 */
@Slf4j
public class PDFUtils {

    /**
     * <AUTHOR>
     */
    public static float calcCordinateY(float pageHeight, float y, float imageHeight) {
        return pageHeight - y - imageHeight;
    }

    /**
     * <AUTHOR>
     */
    public static float getStringWidth(PDFont font, float fontSize, String text) throws IOException {
        return font.getStringWidth(text) * fontSize / 1000f;
    }

    /**
     * <AUTHOR>
     */
    public static float getStringHeight(PDFont font, float fontSize) throws IOException {
        return font.getFontDescriptor().getFontBoundingBox().getHeight() * fontSize / 1000f;
    }

    /**
     * <AUTHOR>
     */
    public static PDImageXObject getSignBg(PDDocument doc) throws IOException {
        return PDImageXObject.createFromByteArray(doc, new ClassPathResource("images/sign_bg.png").getInputStream().readAllBytes(), "sign_bg.png");
    }

    /**
     * <AUTHOR>
     */
    public static PDFont getFontTimes(PDDocument doc) throws IOException {
        return PDType0Font.load(doc, new ClassPathResource("fonts/times.ttf").getInputStream());
    }

    /**
     * <AUTHOR>
     */
    public static PDFont getFontTimesBold(PDDocument doc) throws IOException {
        return PDType0Font.load(doc, new ClassPathResource("fonts/timesbd.ttf").getInputStream());
    }

    /**
     * <AUTHOR>
     */
    public static String[] getWrapText(String input, int wrapLength) {
        if (!ValidationUtils.isNullOrEmpty(input)) {
            return WordUtils.wrap(input, wrapLength).split("\\r?\\n");
        }

        return new String[]{input};
    }

    /**
     * <AUTHOR>
     */
    public static int getWrapLength(String input, PDFont font, float fontSize, float zoneWidth) {
        if (!ValidationUtils.isNullOrEmpty(input)) {
            try {
                float inputWidth = getStringWidth(font, fontSize, input);
                if (inputWidth > zoneWidth) {
                    int totalChar = input.length();
                    float sizePerChar = inputWidth / totalChar;
                    return (int) (zoneWidth / sizePerChar);
                } else {
                    return (int) zoneWidth;
                }
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }

        return 0;
    }
}
