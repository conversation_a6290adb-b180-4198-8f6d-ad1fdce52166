package vn.fis.eapprove.business.utils;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import vn.fis.eapprove.business.dto.filter.ReportProcInstFilter;
import vn.fis.eapprove.business.dto.report.*;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.Query;
import jakarta.persistence.Tuple;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class ReportHelperNew {

    public void buildReportByGroupFilter(ReportProcInstFilter filter, StringBuilder stringBuilder) {

        if (!ObjectUtils.isEmpty(filter.getSearch()) && !filter.getSearch().equals("")) {
            stringBuilder.append(" rp.service_name like :search AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getMasterParentId())) {
            stringBuilder.append(" rp.master_parent_id in :masterParentId AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getServiceId())) {
            stringBuilder.append(" rp.service_id = :serviceId AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getSubmissionType())) {
            stringBuilder.append(" rp.submission_type in :submissionType AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getTaskStatus())) {
            stringBuilder.append(" rp.proc_inst_status in :taskStatus AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getPriorityId())) {
            stringBuilder.append(" rp.priority_id in :priorityId AND ");
        }
        if (filter.getChartId() != null && (!ObjectUtils.isEmpty(filter.getChartId()) && filter.getChartId() != -1) && filter.getChartId() != 0) {
            stringBuilder.append(" rp.chart_id = :chartId AND ");
        }

        if (!ObjectUtils.isEmpty(filter.getChartNodeId())) {
            stringBuilder.append(" rp.chart_node_id in :chartNodeId AND ");
        }

        if (!CollectionUtils.isEmpty(filter.getDirectManager())) {
            stringBuilder.append(" rp.direct_manager in :directManager AND ");
        }

        if (!ObjectUtils.isEmpty(filter.getSearch()) && !filter.getSearch().isEmpty()) {
            stringBuilder.append(" rp.service_name like :search AND ");
        }

        if (!CollectionUtils.isEmpty(filter.getCreatedUser())) {
            stringBuilder.append(" rp.created_user in :createdUser AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getAssignee())) {
            stringBuilder.append(" (( :assignee) = (:assignee) AND ");
            for (String e : filter.getAssignee()) {
                stringBuilder.append(" rp.assignee like ");
                stringBuilder.append(replaceChartNodeId(e));
            }
            stringBuilder.append("1=2) AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getTaskType())) {
            stringBuilder.append(" ((:taskType) = :taskType AND ");
            for (String e : filter.getTaskType()) {
                stringBuilder.append(" rp.task_type like ");
                stringBuilder.append(replaceChartNodeId(e));
            }
            stringBuilder.append(" 1=2) AND ");
        }
        if (filter.getIsExpire() != null && !ObjectUtils.isEmpty(filter.getIsExpire()) && filter.getIsExpire() != -1) {
            if (filter.getIsExpire() == 0) {
                stringBuilder.append(" (finished_time <= sla_finish_time OR current_timestamp < sla_finish_time) AND ");
            }
            if (filter.getIsExpire() == 1) {
                stringBuilder.append(" (finished_time > sla_finish_time OR ifnull(finished_time, current_timestamp) > sla_finish_time) AND ");
            }
        }

        if (!ObjectUtils.isEmpty(filter.getLocationId())) {
            stringBuilder.append(" rp.location_id in :locationId AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getProcInstStatus())) {
            stringBuilder.append(" rp.proc_inst_status in :procInstStatus AND ");
        }
    }

    public void buildReportByUserFilter(ReportProcInstFilter filter, StringBuilder stringBuilder) {

        if (!CollectionUtils.isEmpty(filter.getDefaultUser())) {
            stringBuilder.append(" ((:defaultUser) = :defaultUser AND ");
            for (String e : filter.getDefaultUser()) {
                stringBuilder.append(" rp.assignee like ");
                stringBuilder.append(replaceChartNodeId(e));
            }
            stringBuilder.append(" 1=2) AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getDefaultChartNodeId())) {
            stringBuilder.append(" ( (:defaultChartNodeId) = :defaultChartNodeId AND ");
            for (Long e : filter.getDefaultChartNodeId()) {
                stringBuilder.append(" rp.assignee_chart_node_id like ");
                stringBuilder.append(replaceChartNodeId(e.toString()));
            }
            stringBuilder.append(" 1=2) AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getAssignee())) {
            stringBuilder.append(" ((:assignee) = :assignee AND ");
            for (String e : filter.getAssignee()) {
                stringBuilder.append(" rp.assignee like ");
                stringBuilder.append(replaceChartNodeId(e));
            }
            stringBuilder.append(" 1=2) AND ");

        }
        // ------------------------------------------------
        if (!CollectionUtils.isEmpty(filter.getChartNodeId())) {
            stringBuilder.append(" ( (:chartNodeId) = :chartNodeId AND ");
            for (Long e : filter.getChartNodeId()) {
                stringBuilder.append(" rp.assignee_chart_node_id like ");
                stringBuilder.append(replaceChartNodeId(e.toString()));
            }
            stringBuilder.append(" 1=2) AND ");

        }
        if (filter.getChartId() != null && (!ObjectUtils.isEmpty(filter.getChartId()) && filter.getChartId() != -1) && filter.getChartId() != 0) {
            stringBuilder.append("  :chartId = :chartId AND ");
            stringBuilder.append(" rp.assignee_chart_id like '%").append(filter.getChartId()).append("%' AND");
        }
        if (!CollectionUtils.isEmpty(filter.getTaskType())) {
            stringBuilder.append(" ( (:taskType) = :taskType AND ");
            for (String e : filter.getTaskType()) {
                stringBuilder.append(" rp.task_type like ");
                stringBuilder.append(replaceChartNodeId(e));
            }
            stringBuilder.append(" 1=2) AND ");
        }
        // ------------------------------------------------
        if (!ObjectUtils.isEmpty(filter.getSearch()) && !filter.getSearch().equals("")) {
            stringBuilder.append(" rp.service_name like :search AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getMasterParentId())) {
            stringBuilder.append(" rp.master_parent_id in :masterParentId AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getServiceId())) {
            stringBuilder.append(" rp.service_id = :serviceId AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getSubmissionType())) {
            stringBuilder.append(" rp.submission_type in :submissionType AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getTaskStatus())) {
            stringBuilder.append(" rp.proc_inst_status in :taskStatus AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getPriorityId())) {
            stringBuilder.append(" rp.priority_id in :priorityId AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getDirectManager())) {
            stringBuilder.append(" rp.direct_manager in :directManager AND ");
        }

        if (!ObjectUtils.isEmpty(filter.getSearch()) && !filter.getSearch().isEmpty()) {
            stringBuilder.append(" rp.service_name like :search AND ");
        }

        if (!CollectionUtils.isEmpty(filter.getCreatedUser())) {
            stringBuilder.append(" rp.created_user in :createdUser AND ");
        }

        if (filter.getIsExpire() != null && !ObjectUtils.isEmpty(filter.getIsExpire()) && filter.getIsExpire() != -1) {
            if (filter.getIsExpire() == 0) {
                stringBuilder.append(" (finished_time <= sla_finish_time OR current_timestamp < sla_finish_time) AND ");
            }
            if (filter.getIsExpire() == 1) {
                stringBuilder.append(" (finished_time > sla_finish_time OR ifnull(finished_time, current_timestamp) > sla_finish_time) AND ");
            }
        }

        if (!ObjectUtils.isEmpty(filter.getLocationId())) {
            stringBuilder.append(" rp.location_id in :locationId AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getProcInstStatus())) {
            stringBuilder.append(" rp.proc_inst_status in :procInstStatus AND ");
        }
    }

    public void buildReportByGroupParameter(ReportProcInstFilter filter, Query query) {

        query.setParameter("defaultUser", filter.getDefaultUser());

        query.setParameter("defaultChartNodeId", filter.getDefaultChartNodeId());

        if (!ObjectUtils.isEmpty(filter.getMasterParentId())) {
            query.setParameter("masterParentId", filter.getMasterParentId());
        }
        if (!ObjectUtils.isEmpty(filter.getTaskType())) {
            query.setParameter("taskType", filter.getTaskType());
        }
        if (!ObjectUtils.isEmpty(filter.getServiceId())) {
            query.setParameter("serviceId", filter.getServiceId());
        }
        if (!CollectionUtils.isEmpty(filter.getSubmissionType())) {
            query.setParameter("submissionType", filter.getSubmissionType());
        }
        if (!ObjectUtils.isEmpty(filter.getPriorityId())) {
            query.setParameter("priorityId", filter.getPriorityId());
        }
        if (filter.getChartId() != null && (!ObjectUtils.isEmpty(filter.getChartId()) && filter.getChartId() != -1) && filter.getChartId() != 0) {
            query.setParameter("chartId", filter.getChartId());
        }
        if (!ObjectUtils.isEmpty(filter.getChartNodeId())) {
            query.setParameter("chartNodeId", filter.getChartNodeId());
        }
        if (!CollectionUtils.isEmpty(filter.getDirectManager())) {
            query.setParameter("directManager", filter.getDirectManager());
        }
        if (!CollectionUtils.isEmpty(filter.getCreatedUser())) {
            query.setParameter("createdUser", filter.getCreatedUser());
        }
        if (!CollectionUtils.isEmpty(filter.getAssignee())) {
            query.setParameter("assignee", filter.getAssignee());
        }
//        if (filter.getIsExpire() != null && !ObjectUtils.isEmpty(filter.getIsExpire()) && filter.getIsExpire() != -1) {
//            query.setParameter("isExpire", filter.getIsExpire());
//        }
        if (!ObjectUtils.isEmpty(filter.getLocationId())) {
            query.setParameter("locationId", filter.getLocationId());
        }
        if (!ObjectUtils.isEmpty(filter.getTaskStatus())) {
            query.setParameter("taskStatus", filter.getTaskStatus());
        }
        if (!ObjectUtils.isEmpty(filter.getSearch()) && !filter.getSearch().equals("")) {
            query.setParameter("search", "%" + filter.getSearch() + "%");
        }

        query.setParameter("fromDate", filter.getFromDate());

        query.setParameter("toDate", filter.getToDate());
    }

    public void buildReportTaskByUserParameter(ReportProcInstFilter filter, Query query) {

        query.setParameter("defaultUser", filter.getDefaultUser());

        query.setParameter("defaultChartNodeId", filter.getDefaultChartNodeId());

        if (!ObjectUtils.isEmpty(filter.getMasterParentId())) {
            query.setParameter("masterParentId", filter.getMasterParentId());
        }
        if (!ObjectUtils.isEmpty(filter.getServiceId())) {
            query.setParameter("serviceId", filter.getServiceId());
        }
        if (!CollectionUtils.isEmpty(filter.getSubmissionType())) {
            query.setParameter("submissionType", filter.getSubmissionType());
        }
        if (!ObjectUtils.isEmpty(filter.getPriorityId())) {
            query.setParameter("priorityId", filter.getPriorityId());
        }
        if (filter.getChartId() != null && (!ObjectUtils.isEmpty(filter.getChartId()) && filter.getChartId() != -1) && filter.getChartId() != 0) {
            query.setParameter("chartId", "%" + filter.getChartId().toString() + "%");
        }
        if (!ObjectUtils.isEmpty(filter.getChartNodeId())) {
            query.setParameter("chartNodeId", filter.getChartNodeId());
        }
        if (!ObjectUtils.isEmpty(filter.getTaskType())) {
            query.setParameter("taskType", filter.getTaskType());
        }
        if (!CollectionUtils.isEmpty(filter.getDirectManager())) {
            query.setParameter("directManager", filter.getDirectManager());
        }
        if (!CollectionUtils.isEmpty(filter.getCreatedUser())) {
            query.setParameter("createdUser", filter.getCreatedUser());
        }
        if (!CollectionUtils.isEmpty(filter.getAssignee())) {
            query.setParameter("assignee", filter.getAssignee());
        }
//        if (filter.getIsExpire() != null && !ObjectUtils.isEmpty(filter.getIsExpire()) && filter.getIsExpire() != -1) {
//            query.setParameter("isExpire", filter.getIsExpire());
//        }
        if (!ObjectUtils.isEmpty(filter.getLocationId())) {
            query.setParameter("locationId", filter.getLocationId());
        }
        if (!CollectionUtils.isEmpty(filter.getTaskStatus())) {
            query.setParameter("taskStatus", filter.getTaskStatus());
        }
        if (!ObjectUtils.isEmpty(filter.getSearch()) && !filter.getSearch().equals("")) {
            query.setParameter("search", "%" + filter.getSearch() + "%");
        }

        query.setParameter("fromDate", filter.getFromDate());

        query.setParameter("toDate", filter.getToDate());

    }

    public void buildReportByGroupSort(ReportProcInstFilter filter, StringBuilder stringBuilder) {
        if (!ObjectUtils.isEmpty(filter.getSortBy())) {
            switch (filter.getSortBy()) {
                case "chartNodeCode":
                    addOrderByClause(stringBuilder, "assignee_chart_node_code", filter.getSortType());
                    break;
                case "assignee":
                    addOrderByClause(stringBuilder, "assignee", filter.getSortType());
                    break;
                case "serviceId":
                    addOrderByClause(stringBuilder, "service_id", filter.getSortType());
                    break;
                case "serviceName":
                    addOrderByClause(stringBuilder, "service_name", filter.getSortType());
                    break;
                case "carryoverPreviousApproval":
                    addOrderByClause(stringBuilder, "carryoverPreviousApproval", filter.getSortType());
                    break;
                case "duringApprovalPeriod":
                    addOrderByClause(stringBuilder, "duringApprovalPeriod", filter.getSortType());
                    break;
                case "pending":
                    addOrderByClause(stringBuilder, "pending", filter.getSortType());
                    break;
                case "approved":
                    addOrderByClause(stringBuilder, "approved", filter.getSortType());
                    break;
                case "approvedOnTime":
                    addOrderByClause(stringBuilder, "approvedOnTime", filter.getSortType());
                    break;
                case "approvedDelayed":
                    addOrderByClause(stringBuilder, "approvedDelayed", filter.getSortType());
                    break;
                case "approvingNotDelayed":
                    addOrderByClause(stringBuilder, "approvingNotDelayed", filter.getSortType());
                    break;
                case "approvingDelayed":
                    addOrderByClause(stringBuilder, "approvingDelayed", filter.getSortType());
                    break;
                case "approvalReturned":
                    addOrderByClause(stringBuilder, "approvalReturned", filter.getSortType());
                    break;
                case "approvalCanceled":
                    addOrderByClause(stringBuilder, "approvalCanceled", filter.getSortType());
                    break;
                default:
                    break;
            }
        }
    }


    public void buildDetailReportByGroupSort(ReportProcInstFilter filter, StringBuilder stringBuilder) {
        if (!ObjectUtils.isEmpty(filter.getSortBy())) {
            switch (filter.getSortBy()) {
                case "requestCode":
                    addOrderByClause(stringBuilder, "request_code", filter.getSortType());
                    break;
                case "procInstName":
                    addOrderByClause(stringBuilder, "proc_inst_name", filter.getSortType());
                    break;
                case "serviceName":
                    addOrderByClause(stringBuilder, "service_name", filter.getSortType());
                    break;
                case "taskStatus":
                    addOrderByClause(stringBuilder, "proc_inst_status", filter.getSortType());
                    break;
                case "elapsedTime":
                    addOrderByClause(stringBuilder, "elapsed_time", filter.getSortType());
                    break;
                case "priority":
                    addOrderByClause(stringBuilder, "priority", filter.getSortType());
                    break;
                case "createdUserFullName":
                    addOrderByClause(stringBuilder, "created_user_full_name", filter.getSortType());
                    break;
                case "createdUserChartNodeName":
                    addOrderByClause(stringBuilder, "chart_node_name", filter.getSortType());
                    break;
                default:
                    break;
            }
        }
    }

    public void buildDetailReportByChartNodeSort(ReportProcInstFilter filter, StringBuilder stringBuilder) {

        if (!ObjectUtils.isEmpty(filter.getSortBy())) {
            switch (filter.getSortBy()) {
                case "requestCode":
                    addOrderByClause(stringBuilder, "request_code", filter.getSortType());
                    break;
                case "procInstName":
                    addOrderByClause(stringBuilder, "proc_inst_name", filter.getSortType());
                    break;
                case "serviceName":
                    addOrderByClause(stringBuilder, "service_name", filter.getSortType());
                    break;
                case "taskType":
                    addOrderByClause(stringBuilder, "task_type", filter.getSortType());
                    break;
                case "taskStatus":
                    addOrderByClause(stringBuilder, "task_status", filter.getSortType());
                    break;
                case "elapsedTime":
                    addOrderByClause(stringBuilder, "elapsed_time", filter.getSortType());
                    break;
                case "priority":
                    addOrderByClause(stringBuilder, "priority", filter.getSortType());
                    break;
                case "createdUserFullName":
                    addOrderByClause(stringBuilder, "created_user_full_name", filter.getSortType());
                    break;
                case "createdUserChartNodeName":
                    addOrderByClause(stringBuilder, "created_user_chart_node_name", filter.getSortType());
                    break;
                case "taskName":
                    addOrderByClause(stringBuilder, "task_name", filter.getSortType());
                    break;
                case "assigneeFullName":
                    addOrderByClause(stringBuilder, "assignee_full_name", filter.getSortType());
                    break;
                default:
                    break;
            }
        }
    }

    public void buildReportTaskByChartNodeFilter(ReportProcInstFilter filter, StringBuilder stringBuilder) {

        if (!ObjectUtils.isEmpty(filter.getChartNodeId())) {
//            stringBuilder.append(" ( (:chartNodeId) = :chartNodeId AND ");
//            for (Long e : filter.getChartNodeId()) {
//                stringBuilder.append(" rc.assignee_chart_node_id like  ");
//                stringBuilder.append(replaceChartNodeId(e.toString()));
//            }
//            stringBuilder.append(" 1=2) AND ");

            stringBuilder.append(" rc.assignee_chart_node_id in :chartNodeId AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getMasterParentId())) {
            stringBuilder.append(" rc.master_parent_id in :masterParentId AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getAssignee())) {
            stringBuilder.append(" rc.assignee in :assignee AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getSubmissionType())) {
            stringBuilder.append(" rc.submission_type in :submissionType AND ");
        }

        if (!ObjectUtils.isEmpty(filter.getTaskType())) {
            stringBuilder.append(" rc.task_type in :taskType AND ");
        }

        if (!ObjectUtils.isEmpty(filter.getPriorityId())) {
            stringBuilder.append(" rc.priority_id in :priorityId AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getChartId()) && filter.getChartId() != -1 && filter.getChartId() != 0) {
            stringBuilder.append(" rc.assignee_chart_id = :chartId AND ");
        }

        if (!CollectionUtils.isEmpty(filter.getTaskStatus())) {
            stringBuilder.append(" rc.task_status in :taskStatus AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getCreatedUser())) {
            stringBuilder.append(" rc.created_user in :createdUser AND ");
        }
        if (filter.getIsExpire() != null && !ObjectUtils.isEmpty(filter.getIsExpire()) && filter.getIsExpire() != -1) {
            if (filter.getIsExpire() == 0) {
                stringBuilder.append(" (finished_time <= sla_finish_time OR current_timestamp < sla_finish_time) AND ");
            }
            if (filter.getIsExpire() == 1) {
                stringBuilder.append(" (finished_time > sla_finish_time OR ifnull(finished_time, current_timestamp) > sla_finish_time) AND ");
            }
        }
        if (!ObjectUtils.isEmpty(filter.getLocationId())) {
            stringBuilder.append(" rc.location_id in :locationId AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getSearch()) && !filter.getSearch().equals("")) {
            stringBuilder.append(" (rc.assignee like :search OR rc.assignee_full_name like :search OR rc.assignee_chart_node_name like :search OR rc.assignee_staff_code like :search ) AND ");
        }
    }

    public void buildReportTaskByChartNodeParameter(ReportProcInstFilter filter, Query query) {

        query.setParameter("defaultUser", filter.getDefaultUser());

        query.setParameter("defaultChartNodeId", filter.getDefaultChartNodeId());

        if (!ObjectUtils.isEmpty(filter.getSearch()) && !filter.getSearch().equals("")) {
            query.setParameter("search", "%" + filter.getSearch() + "%");
        }
        if (!ObjectUtils.isEmpty(filter.getChartNodeId())) {
            query.setParameter("chartNodeId", filter.getChartNodeId());
        }
        if (!ObjectUtils.isEmpty(filter.getMasterParentId())) {
            query.setParameter("masterParentId", filter.getMasterParentId());
        }
        if (!ObjectUtils.isEmpty(filter.getAssignee())) {
            query.setParameter("assignee", filter.getAssignee());
        }
        if (!CollectionUtils.isEmpty(filter.getSubmissionType())) {
            query.setParameter("submissionType", filter.getSubmissionType());
        }
        if (!ObjectUtils.isEmpty(filter.getTaskType())) {
            query.setParameter("taskType", filter.getTaskType());
        }
        if (!ObjectUtils.isEmpty(filter.getPriorityId())) {
            query.setParameter("priorityId", filter.getPriorityId());
        }
        if (!ObjectUtils.isEmpty(filter.getChartId()) && filter.getChartId() != -1 && filter.getChartId() != 0) {
            query.setParameter("chartId", filter.getChartId());
        }
        if (!CollectionUtils.isEmpty(filter.getDirectManager())) {
            query.setParameter("directManager", filter.getDirectManager());
        }
        if (!CollectionUtils.isEmpty(filter.getCreatedUser())) {
            query.setParameter("createdUser", filter.getCreatedUser());
        }
        if (!CollectionUtils.isEmpty(filter.getTaskStatus())) {
            query.setParameter("taskStatus", filter.getTaskStatus());
        }
//        if (filter.getIsExpire() != null && !ObjectUtils.isEmpty(filter.getIsExpire()) && filter.getIsExpire() != -1) {
//            query.setParameter("isExpire", filter.getIsExpire());
//        }
        if (!ObjectUtils.isEmpty(filter.getLocationId())) {
            query.setParameter("locationId", filter.getLocationId());
        }

        query.setParameter("fromDate", filter.getFromDate());

        query.setParameter("toDate", filter.getToDate());
    }


    public void buildDetailReportByGroupParam(ReportProcInstFilter filter, Query query) {

        query.setParameter("defaultUser", filter.getDefaultUser());

        query.setParameter("defaultChartNodeId", filter.getDefaultChartNodeId());

        if (!ObjectUtils.isEmpty(filter.getServiceName())) {
            query.setParameter("serviceName", filter.getServiceName());
        }
        if (!CollectionUtils.isEmpty(filter.getSubmissionType())) {
            query.setParameter("submissionType", filter.getSubmissionType());
        }
        if (!ObjectUtils.isEmpty(filter.getTaskType())) {
            query.setParameter("taskType", filter.getTaskType());
        }
        if (!ObjectUtils.isEmpty(filter.getPriorityId())) {
            query.setParameter("priorityId", filter.getPriorityId());
        }
        if (filter.getChartId() != null && (!ObjectUtils.isEmpty(filter.getChartId())) && filter.getChartId() != -1 && filter.getChartId() != 0) {
            query.setParameter("chartId", filter.getChartId());
        }
        if (filter.getServiceId() != null && (!ObjectUtils.isEmpty(filter.getServiceId())) && filter.getServiceId() != -1 && filter.getServiceId() != 0) {
            query.setParameter("serviceId", filter.getServiceId());
        }
        if (!ObjectUtils.isEmpty(filter.getChartNodeId())) {
            query.setParameter("chartNodeId", filter.getChartNodeId());
        }
        if (!CollectionUtils.isEmpty(filter.getDirectManager())) {
            query.setParameter("directManager", filter.getDirectManager());
        }
        if (!CollectionUtils.isEmpty(filter.getAssignee())) {
            query.setParameter("assignee", filter.getAssignee());
        }
        if (!CollectionUtils.isEmpty(filter.getCreatedUser())) {
            query.setParameter("createdUser", filter.getCreatedUser());
        }
        if (!CollectionUtils.isEmpty(filter.getCreatedUser())) {
            query.setParameter("createdUser", filter.getCreatedUser());
        }
        if (!CollectionUtils.isEmpty(filter.getTaskStatus())) {
            query.setParameter("procInstStatus", filter.getTaskStatus());
        }
        if (!CollectionUtils.isEmpty(filter.getMasterParentId())) {
            query.setParameter("masterParentId", filter.getMasterParentId());
        }
        if (!ObjectUtils.isEmpty(filter.getLocationId())) {
            query.setParameter("locationId", filter.getLocationId());
        }
        if (!ObjectUtils.isEmpty(filter.getFromDate()) && !filter.getFromDate().equals("")) {
            query.setParameter("fromDate", filter.getFromDate());
        }
        if (!ObjectUtils.isEmpty(filter.getToDate()) && !filter.getToDate().equals("")) {
            query.setParameter("toDate", filter.getToDate());
        }
        if (!ObjectUtils.isEmpty(filter.getSearch()) && !filter.getSearch().equals("")) {
            query.setParameter("search", "%" + filter.getSearch() + "%");
        }
//        if (!ValidationUtils.isNullOrEmpty(filter.getIsExpire()) && filter.getIsExpire() != -1) {
//            query.setParameter("isExpire", filter.getIsExpire());
//        }
    }

    public void buildDetailReportByUserParam(ReportProcInstFilter filter, Query query) {

        query.setParameter("defaultUser", filter.getDefaultUser());

        query.setParameter("defaultChartNodeId", filter.getDefaultChartNodeId());

        if (!ObjectUtils.isEmpty(filter.getServiceName())) {
            query.setParameter("serviceName", filter.getServiceName());
        }
        if (!CollectionUtils.isEmpty(filter.getSubmissionType())) {
            query.setParameter("submissionType", filter.getSubmissionType());
        }
        if (!ObjectUtils.isEmpty(filter.getTaskType())) {
            query.setParameter("taskType", filter.getTaskType());
        }
        if (!ObjectUtils.isEmpty(filter.getPriorityId())) {
            query.setParameter("priorityId", filter.getPriorityId());
        }
        if (filter.getChartId() != null && (!ObjectUtils.isEmpty(filter.getChartId())) && filter.getChartId() != -1 && filter.getChartId() != 0) {
            query.setParameter("chartId", "%" + filter.getChartId().toString() + "%");
        }
        if (filter.getServiceId() != null && (!ObjectUtils.isEmpty(filter.getServiceId())) && filter.getServiceId() != -1 && filter.getServiceId() != 0) {
            query.setParameter("serviceId", filter.getServiceId());
        }
//        if (!ValidationUtils.isNullOrEmpty(filter.getIsExpire()) && filter.getIsExpire() != -1) {
//            query.setParameter("isExpire", filter.getIsExpire());
//        }
        if (!ObjectUtils.isEmpty(filter.getChartNodeId())) {
            query.setParameter("chartNodeId", filter.getChartNodeId());
        }
        if (!CollectionUtils.isEmpty(filter.getDirectManager())) {
            query.setParameter("directManager", filter.getDirectManager());
        }
        if (!CollectionUtils.isEmpty(filter.getAssignee())) {
            query.setParameter("assignee", filter.getAssignee());
        }
        if (!CollectionUtils.isEmpty(filter.getCreatedUser())) {
            query.setParameter("createdUser", filter.getCreatedUser());
        }
        if (!CollectionUtils.isEmpty(filter.getTaskStatus())) {
            query.setParameter("procInstStatus", filter.getTaskStatus());
        }
        if (!CollectionUtils.isEmpty(filter.getMasterParentId())) {
            query.setParameter("masterParentId", filter.getMasterParentId());
        }
        if (!ObjectUtils.isEmpty(filter.getLocationId())) {
            query.setParameter("locationId", filter.getLocationId());
        }
        if (!ObjectUtils.isEmpty(filter.getFromDate()) && !filter.getFromDate().isEmpty()) {
            query.setParameter("fromDate", filter.getFromDate());
        }
        if (!ObjectUtils.isEmpty(filter.getToDate()) && !filter.getToDate().isEmpty()) {
            query.setParameter("toDate", filter.getToDate());
        }
        if (!ObjectUtils.isEmpty(filter.getSearch()) && !filter.getSearch().isEmpty()) {
            query.setParameter("search", "%" + filter.getSearch() + "%");
        }

    }

    public void buildDetailReportByChartNodeParam(ReportProcInstFilter filter, Query query) {

        query.setParameter("defaultUser", filter.getDefaultUser());

        query.setParameter("defaultChartNodeId", filter.getDefaultChartNodeId());

        if (!ObjectUtils.isEmpty(filter.getSearch()) && !filter.getSearch().equals("")) {
            query.setParameter("search", "%" + filter.getSearch() + "%");
        }
        if (!ObjectUtils.isEmpty(filter.getServiceName())) {
            query.setParameter("serviceName", filter.getServiceName());
        }
        if (!ObjectUtils.isEmpty(filter.getServiceId())) {
            query.setParameter("serviceId", filter.getServiceId());
        }
        if (!ObjectUtils.isEmpty(filter.getAssignee())) {
            query.setParameter("assignee", filter.getAssignee());
        }
        if (!ObjectUtils.isEmpty(filter.getChartNodeCode())) {
            query.setParameter("assigneeChartNodeCode", filter.getChartNodeCode());
        }
        if (!CollectionUtils.isEmpty(filter.getSubmissionType())) {
            query.setParameter("submissionType", filter.getSubmissionType());
        }
        if (!CollectionUtils.isEmpty(filter.getChartNodeId())) {
            query.setParameter("assigneeChartNodeId", filter.getChartNodeId());
        }
        if (!CollectionUtils.isEmpty(filter.getMasterParentId())) {
            query.setParameter("masterParentId", filter.getMasterParentId());
        }
        if (!ObjectUtils.isEmpty(filter.getTaskType())) {
            query.setParameter("taskType", filter.getTaskType());
        }
        if (!ObjectUtils.isEmpty(filter.getPriorityId())) {
            query.setParameter("priorityId", filter.getPriorityId());
        }
        if (!ObjectUtils.isEmpty(filter.getChartId()) && filter.getChartId() != -1 && filter.getChartId() != 0) {
            query.setParameter("chartId", filter.getChartId());
        }
        if (!CollectionUtils.isEmpty(filter.getDirectManager())) {
            query.setParameter("directManager", filter.getDirectManager());
        }
        if (!CollectionUtils.isEmpty(filter.getCreatedUser())) {
            query.setParameter("createdUser", filter.getCreatedUser());
        }
        if (!CollectionUtils.isEmpty(filter.getTaskStatus())) {
            query.setParameter("taskStatus", filter.getTaskStatus());
        }
//        if (filter.getIsExpire() != null && (!ObjectUtils.isEmpty(filter.getIsExpire()) && filter.getIsExpire() != -1)) {
//            query.setParameter("isExpire", filter.getIsExpire());
//        }
        if (!ObjectUtils.isEmpty(filter.getLocationId())) {
            query.setParameter("locationId", filter.getLocationId());
        }
        if (!ObjectUtils.isEmpty(filter.getStaffCode())) {
            query.setParameter("staffCode", filter.getStaffCode());
        }
        if (!ObjectUtils.isEmpty(filter.getFromDate()) && !filter.getFromDate().isEmpty()) {
            query.setParameter("fromDate", filter.getFromDate());
        }
        if (!ObjectUtils.isEmpty(filter.getToDate()) && !filter.getToDate().isEmpty()) {
            query.setParameter("toDate", filter.getToDate());
        }
    }

    public void buildDetailReportByGroupFilter(ReportProcInstFilter filter, StringBuilder stringBuilder) {

        if (!ObjectUtils.isEmpty(filter.getAssignee())) {
            stringBuilder.append(" ((:assignee) = :assignee AND ");
            for (String e : filter.getAssignee()) {
                stringBuilder.append(" rp.assignee like ");
                stringBuilder.append(replaceChartNodeId(e));
            }
            stringBuilder.append(" 1=2) AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getTaskType())) {
            stringBuilder.append(" ((:taskType) = :taskType AND ");
            for (String e : filter.getTaskType()) {
                stringBuilder.append(" rp.task_type like ");
                stringBuilder.append(replaceChartNodeId(e));
            }
            stringBuilder.append(" 1=2) AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getSearch()) && !filter.getSearch().isEmpty()) {
            stringBuilder.append(" ( rp.title like :search OR rp.request_code like :search OR rp.service_name like :search OR rp.created_user like :search OR rp.created_user_full_name like :search) AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("carryoverPreviousApproval")) {
            stringBuilder.append(" rp.proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND rp.version_time < STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') >0 AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("duringApprovalPeriod")) {
            stringBuilder.append(" rp.proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND rp.version_time BETWEEN  STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') AND  STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("pending")) {
            stringBuilder.append(" rp.proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND rp.version_time <= STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') > 0  AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approved")) {
            stringBuilder.append(" rp.proc_inst_status IN ('COMPLETED','CLOSED') AND rp.version_time between :fromDate and :toDate AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvedOnTime")) {
            stringBuilder.append(" rp.proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time <= sla_finish_time AND rp.version_time between STR_TO_DATE(:fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(:toDate, '%Y-%m-%d %H:%i:%s') AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvedDelayed")) {
            stringBuilder.append(" rp.proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time > sla_finish_time AND rp.version_time between STR_TO_DATE(:fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(:toDate, '%Y-%m-%d %H:%i:%s') AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvingNotDelayed")) {
            stringBuilder.append(" rp.proc_inst_status IN ('OPENED','PROCESSING', 'ADDITIONAL_REQUEST') AND current_timestamp <= sla_finish_time AND rp.version_time <= STR_TO_DATE(:toDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(:fromDate, '%Y-%m-%d %H:%i:%s') > 0 AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvingDelayed")) {
            stringBuilder.append(" rp.proc_inst_status IN ('OPENED','PROCESSING', 'ADDITIONAL_REQUEST') AND current_timestamp > sla_finish_time AND rp.version_time <= STR_TO_DATE(:toDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(:fromDate, '%Y-%m-%d %H:%i:%s') > 0 AND  ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvalReturned")) {
            stringBuilder.append(" rp.proc_inst_status in ('DELETED_BY_RU','RECALLED','RECALLING') AND rp.version_time <= STR_TO_DATE(:toDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(:fromDate, '%Y-%m-%d %H:%i:%s') > 0 AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvalCanceled")) {
            stringBuilder.append(" (rp.proc_inst_status = 'CANCEL') AND rp.version_time between STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getServiceName())) {
            stringBuilder.append(" rp.service_name in :serviceName AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getServiceId())) {
            stringBuilder.append(" rp.service_id = :serviceId AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getSubmissionType())) {
            stringBuilder.append(" rp.submission_type in :submissionType AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getTaskStatus())) {
            stringBuilder.append(" rp.proc_inst_status in :procInstStatus AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getMasterParentId())) {
            stringBuilder.append(" rp.master_parent_id in :masterParentId AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getPriorityId())) {
            stringBuilder.append(" rp.priority_id in :priorityId AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getDirectManager())) {
            stringBuilder.append(" rp.direct_manager = :directManager AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getCreatedUser())) {
            stringBuilder.append(" rp.created_user in :createdUser AND ");
        }
        if (!ValidationUtils.isNullOrEmpty(filter.getIsExpire()) && filter.getIsExpire() != -1) {
            if (filter.getIsExpire() == 0) {
                stringBuilder.append(" (finished_time <= sla_finish_time OR current_timestamp < sla_finish_time) AND ");
            }
            if (filter.getIsExpire() == 1) {
                stringBuilder.append(" (finished_time > sla_finish_time OR current_timestamp > sla_finish_time) AND ");
            }
        }
        if (!ObjectUtils.isEmpty(filter.getLocationId())) {
            stringBuilder.append(" rp.location_id in :locationId AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getChartId()) && filter.getChartId() != -1 && filter.getChartId() != 0) {
            stringBuilder.append(" rp.chart_id = :chartId AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getChartNodeId())) {
            stringBuilder.append(" rp.chart_node_id in :chartNodeId AND ");
        }
    }

    public void buildDetailReportByUserFilter(ReportProcInstFilter filter, StringBuilder stringBuilder) {

        if (!ObjectUtils.isEmpty(filter.getDefaultUser())) {
            stringBuilder.append(" ((:defaultUser) = :defaultUser AND ");
            for (String e : filter.getDefaultUser()) {
                stringBuilder.append(" rp.assignee like ");
                stringBuilder.append(replaceChartNodeId(e));
            }
            stringBuilder.append(" 1=2) AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getDefaultChartNodeId())) {
            stringBuilder.append(" ((:defaultChartNodeId) = :defaultChartNodeId AND ");
            for (Long e : filter.getDefaultChartNodeId()) {
                stringBuilder.append(" rp.assignee_chart_node_id like ");
                stringBuilder.append(replaceChartNodeId(e.toString()));
            }
            stringBuilder.append(" 1=2) AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getAssignee())) {
            stringBuilder.append(" ((:assignee) = :assignee AND ");
            for (String e : filter.getAssignee()) {
                stringBuilder.append(" rp.assignee like ");
                stringBuilder.append(replaceChartNodeId(e));
            }
            stringBuilder.append(" 1=2) AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getChartNodeId())) {
            stringBuilder.append(" ((:chartNodeId) = :chartNodeId AND ");
            for (Long e : filter.getChartNodeId()) {
                stringBuilder.append(" rp.assignee_chart_node_id like ");
                stringBuilder.append(replaceChartNodeId(e.toString()));
            }
            stringBuilder.append(" 1=2) AND ");

        }
        if (filter.getChartId() != null && (!ObjectUtils.isEmpty(filter.getChartId()) && filter.getChartId() != -1) && filter.getChartId() != 0) {
            stringBuilder.append(" rp.assignee_chart_id like :chartId AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getTaskType())) {
            stringBuilder.append(" ((:taskType) = :taskType AND ");
            for (String e : filter.getTaskType()) {
                stringBuilder.append(" rp.task_type like ");
                stringBuilder.append(replaceChartNodeId(e));
            }
            stringBuilder.append(" 1=2) AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getSearch()) && !filter.getSearch().isEmpty()) {
            stringBuilder.append(" ( rp.title like :search OR rp.request_code like :search OR rp.service_name like :search OR rp.created_user like :search OR rp.created_user_full_name like :search) AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("carryoverPreviousApproval")) {
            stringBuilder.append(" rp.proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND rp.action_current_time < STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') >0 AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("duringApprovalPeriod")) {
            stringBuilder.append(" rp.proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND rp.action_current_time BETWEEN  STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') AND  STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("pending")) {
            stringBuilder.append(" rp.proc_inst_status IN ('PROCESSING','OPENED','ADDITIONAL_REQUEST','ACTIVE') AND rp.action_current_time <= STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') > 0  AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approved")) {
            stringBuilder.append(" rp.proc_inst_status IN ('COMPLETED','CLOSED') AND rp.action_current_time between STR_TO_DATE(:fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvedOnTime")) {
            stringBuilder.append(" rp.proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time <= sla_finish_time AND rp.action_current_time between STR_TO_DATE(:fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(:toDate, '%Y-%m-%d %H:%i:%s') AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvedDelayed")) {
            stringBuilder.append(" rp.proc_inst_status IN ('COMPLETED','CLOSED') AND finished_time > sla_finish_time AND rp.action_current_time between STR_TO_DATE(:fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(:toDate, '%Y-%m-%d %H:%i:%s') AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvingNotDelayed")) {
            stringBuilder.append(" rp.proc_inst_status IN ('OPENED','PROCESSING', 'ADDITIONAL_REQUEST') AND current_timestamp < sla_finish_time AND rp.action_current_time <= STR_TO_DATE(:toDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(:fromDate, '%Y-%m-%d %H:%i:%s') > 0 AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvingDelayed")) {
            stringBuilder.append(" rp.proc_inst_status IN ('OPENED','PROCESSING', 'ADDITIONAL_REQUEST') AND current_timestamp > sla_finish_time AND rp.action_current_time <= STR_TO_DATE(:toDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(:fromDate, '%Y-%m-%d %H:%i:%s') > 0 AND  ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvalReturned")) {
            stringBuilder.append(" rp.proc_inst_status in ('DELETED_BY_RU','RECALLED','RECALLING') AND rp.action_current_time <= STR_TO_DATE(:toDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(:fromDate, '%Y-%m-%d %H:%i:%s') > 0 AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvalCanceled")) {
            stringBuilder.append(" (rp.proc_inst_status = 'CANCEL') AND rp.action_current_time between STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getServiceName())) {
            stringBuilder.append(" rp.service_name in :serviceName AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getServiceId())) {
            stringBuilder.append(" rp.service_id = :serviceId AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getSubmissionType())) {
            stringBuilder.append(" rp.submission_type in :submissionType AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getTaskStatus())) {
            stringBuilder.append(" rp.proc_inst_status in :procInstStatus AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getMasterParentId())) {
            stringBuilder.append(" rp.master_parent_id in :masterParentId AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getPriorityId())) {
            stringBuilder.append(" rp.priority_id in :priorityId AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getDirectManager())) {
            stringBuilder.append(" rp.direct_manager = :directManager AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getCreatedUser())) {
            stringBuilder.append(" rp.created_user in :createdUser AND ");
        }
        if (filter.getIsExpire() != null && !ObjectUtils.isEmpty(filter.getIsExpire()) && filter.getIsExpire() != -1) {
            if (filter.getIsExpire() == 0) {
                stringBuilder.append(" (finished_time <= sla_finish_time OR current_timestamp < sla_finish_time) AND ");
            }
            if (filter.getIsExpire() == 1) {
                stringBuilder.append(" (finished_time > sla_finish_time OR ifnull(finished_time, current_timestamp) > sla_finish_time) AND ");
            }
        }
        if (!ObjectUtils.isEmpty(filter.getLocationId())) {
            stringBuilder.append(" rp.location_id in :locationId AND ");
        }
    }


    public void buildDetailReportByChartNodeFilter(ReportProcInstFilter filter, StringBuilder stringBuilder) {
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("carryoverPreviousApproval")) {
            stringBuilder.append(" rc.task_status IN ('PROCESSING','ACTIVE') AND rc.version_time < STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') > 0 AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("duringApprovalPeriod")) {
            stringBuilder.append(" rc.task_status IN ('PROCESSING','ACTIVE') AND " +
                    "                          rc.version_time BETWEEN STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("pending")) {
            stringBuilder.append(" rc.task_status IN ('PROCESSING','ACTIVE') AND rc.version_time <= STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') > 0 AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approved")) {
            stringBuilder.append(" rc.task_status = 'COMPLETED' AND rc.version_time between STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvedOnTime")) {
            stringBuilder.append(" rc.task_status = 'COMPLETED' AND finished_time <= sla_finish_time AND rc.version_time between STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvedDelayed")) {
            stringBuilder.append(" rc.task_status = 'COMPLETED' AND finished_time > sla_finish_time AND rc.version_time between STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvingNotDelayed")) {
            stringBuilder.append(" rc.task_status IN ('PROCESSING','ACTIVE') AND current_timestamp < sla_finish_time AND rc.version_time <= STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') > 0 AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvingDelayed")) {
            stringBuilder.append(" rc.task_status IN ('PROCESSING','ACTIVE') AND current_timestamp > sla_finish_time AND rc.version_time <= STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') > 0 AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvalReturned")) {
            stringBuilder.append(" rc.task_status in ('DELETED_BY_RU','AGREE_TO_RECALL') AND rc.version_time <= STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') > 0 AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getReportType()) && filter.getReportType().equals("approvalCanceled")) {
            stringBuilder.append(" rc.task_status = 'CANCEL' AND rc.version_time between STR_TO_DATE( :fromDate, '%Y-%m-%d %H:%i:%s') AND STR_TO_DATE( :toDate, '%Y-%m-%d %H:%i:%s') AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getSearch()) && !filter.getSearch().isEmpty()) {
            stringBuilder.append(" (rc.title like :search OR rc.service_name like :search OR rc.request_code like :search OR rc.assignee = :search OR rc.assignee_full_name like :search OR rc.assignee_chart_node_name like :search OR rc.assignee_staff_code like :search ) AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getServiceName())) {
            stringBuilder.append(" rc.service_name in :serviceName AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getChartNodeCode())) {
            stringBuilder.append(" rc.assignee_chart_node_code = :assigneeChartNodeCode AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getChartNodeId())) {
            stringBuilder.append(" rc.assignee_chart_node_id in :assigneeChartNodeId AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getServiceId())) {
            stringBuilder.append(" rc.service_id = :serviceId AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getAssignee())) {
            stringBuilder.append(" rc.assignee in :assignee AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getSubmissionType())) {
            stringBuilder.append(" rc.submission_type in :submissionType AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getTaskType())) {
            stringBuilder.append(" rc.task_type in :taskType AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getPriorityId())) {
            stringBuilder.append(" rc.priority_id in :priorityId AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getDirectManager())) {
            stringBuilder.append(" rc.direct_manager in :directManager AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getCreatedUser())) {
            stringBuilder.append(" rc.created_user in :createdUser AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getStaffCode())) {
            stringBuilder.append(" rc.assignee_staff_code = :staffCode AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getTaskStatus())) {
            stringBuilder.append(" rc.task_status in :taskStatus AND ");
        }
        if (!CollectionUtils.isEmpty(filter.getMasterParentId())) {
            stringBuilder.append(" rc.master_parent_id in :masterParentId AND ");
        }
        if (filter.getIsExpire() != null && !ObjectUtils.isEmpty(filter.getIsExpire()) && filter.getIsExpire() != -1) {
            if (filter.getIsExpire() == 0) {
                stringBuilder.append(" (finished_time <= sla_finish_time OR current_timestamp < sla_finish_time) AND ");
            }
            if (filter.getIsExpire() == 1) {
                stringBuilder.append(" (finished_time > sla_finish_time OR ifnull(finished_time, current_timestamp) > sla_finish_time) AND ");
            }
        }
        if (!ObjectUtils.isEmpty(filter.getLocationId())) {
            stringBuilder.append(" rc.location_id in :locationId AND ");
        }
        if (!ObjectUtils.isEmpty(filter.getChartId()) && filter.getChartId() != -1 && filter.getChartId() != 0) {
            stringBuilder.append(" rc.assignee_chart_id = :chartId AND ");
        }
    }


    public void mapTupleToDetailReportByGroup(List<Tuple> result, List<DetailReportByGroupDto> detailReportByGroupDtos) {
        result.forEach(tuple -> {
            String procInstName = tuple.get("proc_inst_name") == null ? null : String.valueOf(tuple.get("proc_inst_name"));
            String requestCode = tuple.get("request_code") == null ? null : String.valueOf(tuple.get("request_code"));
            String procInstId = tuple.get("proc_inst_id") == null ? null : String.valueOf(tuple.get("proc_inst_id"));
            String serviceName = tuple.get("service_name") == null ? null : String.valueOf(tuple.get("service_name"));
            String taskStatus = tuple.get("proc_inst_status") == null ? null : String.valueOf(tuple.get("proc_inst_status"));
            Long elapsedTime = tuple.get("elapsed_time") == null ? null : Long.valueOf(tuple.get("elapsed_time").toString());
            Long ticketId = tuple.get("ticket_id") == null ? null : Long.valueOf(tuple.get("ticket_id").toString());
            Double delayTime = tuple.get("delay_time") == null ? null : Double.valueOf(tuple.get("delay_time").toString());
            String priority = tuple.get("priority") == null ? null : String.valueOf(tuple.get("priority"));
            String reasonCancel = tuple.get("ly_do_huy_phieu") == null ? null : String.valueOf(tuple.get("ly_do_huy_phieu"));
            String reasonReturned = tuple.get("ly_do_tra_ve") == null ? null : String.valueOf(tuple.get("ly_do_tra_ve"));
            Long countReturned = tuple.get("so_lan_tra_ve") == null ? null : Long.valueOf(tuple.get("so_lan_tra_ve").toString());
            String createdUser = tuple.get("created_user") == null ? null : String.valueOf(tuple.get("created_user"));
            String createdUserFullName = tuple.get("created_user_full_name") == null ? null : String.valueOf(tuple.get("created_user_full_name"));
            String createdUserChartNodeName = tuple.get("chart_node_name") == null ? null : String.valueOf(tuple.get("chart_node_name"));
            String createdUserChartNodeCode = tuple.get("chart_node_code") == null ? null : String.valueOf(tuple.get("chart_node_code"));
            String createdUserChartShortName = tuple.get("chart_short_name") == null ? null : String.valueOf(tuple.get("chart_short_name"));
            String createdUserTitleName = tuple.get("user_title_name") == null ? null : String.valueOf(tuple.get("user_title_name"));
//            String isExpire = tuple.get("is_expire") == null ? null : String.valueOf(tuple.get("is_expire"));

            DetailReportByGroupDto detailReportByGroupDto = new DetailReportByGroupDto();

            LocalDateTime createdTime = tuple.get("created_time") == null ? null : TimeUtils.stringToLocalDateTime(tuple.get("created_time").toString(), TimeUtils.FORMAT_DATE_DD_MM_YYYY);
            LocalDateTime finishedTime = tuple.get("finished_time") == null ? null : TimeUtils.stringToLocalDateTime(tuple.get("finished_time").toString(), TimeUtils.FORMAT_DATE_DD_MM_YYYY);
            LocalDateTime slaFinishTime = tuple.get("sla_finish_time") == null ? null : TimeUtils.stringToLocalDateTime(tuple.get("sla_finish_time").toString(), TimeUtils.FORMAT_DATE_DD_MM_YYYY);
            detailReportByGroupDto.setTicketId(ticketId);
            detailReportByGroupDto.setCreatedTime(createdTime);
            detailReportByGroupDto.setFinishedTime(finishedTime);
            detailReportByGroupDto.setSlaFinishTime(slaFinishTime);
            detailReportByGroupDto.setProcInstId(procInstId);
            detailReportByGroupDto.setProcInstName(procInstName);
            detailReportByGroupDto.setRequestCode(requestCode);
            detailReportByGroupDto.setServiceName(serviceName);
            detailReportByGroupDto.setTaskStatus(taskStatus);
            detailReportByGroupDto.setElapsedTime(elapsedTime);
            detailReportByGroupDto.setDelayTime(delayTime);
            detailReportByGroupDto.setPriority(priority);
            detailReportByGroupDto.setReasonCancel(reasonCancel);
            detailReportByGroupDto.setCreatedUser(createdUser);
            detailReportByGroupDto.setCreatedUserFullName(createdUserFullName);
            detailReportByGroupDto.setCreatedUserChartNodeName(createdUserChartNodeName);
            detailReportByGroupDto.setCreatedUserChartNodeCode(createdUserChartNodeCode);
            detailReportByGroupDto.setCreatedUserChartShortName(createdUserChartShortName);
            detailReportByGroupDto.setCreatedUserTitleName(createdUserTitleName);
            detailReportByGroupDto.setCountReturned(countReturned);
//            detailReportByGroupDto.setReasonReturned(reasonReturned);
            setIsExpire(detailReportByGroupDto, slaFinishTime, finishedTime);
            detailReportByGroupDtos.add(detailReportByGroupDto);
        });
    }

    public void mapTupleToDetailReportByChartNode(List<Tuple> result, List<DetailReportByGroupDto> detailReportByGroupDtos) {
        result.forEach(tuple -> {
            String procInstName = tuple.get("proc_inst_name") == null ? null : String.valueOf(tuple.get("proc_inst_name"));
            String procInstId = tuple.get("proc_inst_id") == null ? null : String.valueOf(tuple.get("proc_inst_id"));
            String taskId = tuple.get("task_id") == null ? null : String.valueOf(tuple.get("task_id"));
            String serviceName = tuple.get("service_name") == null ? null : String.valueOf(tuple.get("service_name"));
            String taskType = tuple.get("task_type") == null ? null : String.valueOf(tuple.get("task_type"));
            String requestCode = tuple.get("request_code") == null ? null : String.valueOf(tuple.get("request_code"));
            String taskStatus = tuple.get("task_status") == null ? null : String.valueOf(tuple.get("task_status"));
            Long elapsedTime = tuple.get("elapsed_time") == null ? null : Long.valueOf(tuple.get("elapsed_time").toString());
            Double delayTime = tuple.get("delay_time") == null ? null : Double.valueOf(tuple.get("delay_time").toString());
            Long ticketId = tuple.get("ticket_id") == null ? null : Long.valueOf(tuple.get("ticket_id").toString());
            String priority = tuple.get("priority") == null ? null : String.valueOf(tuple.get("priority"));
            String reasonCancel = tuple.get("ly_do_huy") == null ? null : String.valueOf(tuple.get("ly_do_huy"));
            Long countTaskCancel = tuple.get("so_lan_tra_ve") == null ? null : Long.valueOf(tuple.get("so_lan_tra_ve").toString());
            String assignee = tuple.get("assignee") == null ? null : String.valueOf(tuple.get("assignee"));
            String createdUser = tuple.get("created_user") == null ? null : String.valueOf(tuple.get("created_user"));
            String assigneeFullName = tuple.get("assignee_full_name") == null ? null : String.valueOf(tuple.get("assignee_full_name"));
            String assigneeChartNodeId = tuple.get("assignee_chart_node_id") == null ? null : String.valueOf(String.valueOf(tuple.get("assignee_chart_node_id")));
            String[] parts = assigneeChartNodeId.split(", ");
            List<Long> assigneeChartNodeIds = Arrays.stream(parts)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());

            String assigneeChartShortName = tuple.get("assignee_chart_short_name") == null ? null : String.valueOf(tuple.get("assignee_chart_short_name"));
            List<String> assigneeChartNodeName = tuple.get("assignee_chart_node_name") == null ? null : Arrays.asList(String.valueOf(tuple.get("assignee_chart_node_name")).split(","));
            List<String> assigneeChartNodeCode = tuple.get("assignee_chart_node_code") == null ? null : Arrays.asList(String.valueOf(tuple.get("assignee_chart_node_code")).split(","));
            List<String> assigneeTitleName = tuple.get("assignee_title_name") == null ? null : Arrays.asList(String.valueOf(tuple.get("assignee_title_name")).split(","));
            String createdUserFullName = tuple.get("created_user_full_name") == null ? null : String.valueOf(tuple.get("created_user_full_name"));
            String createdUserChartNodeName = tuple.get("created_user_chart_node_name") == null ? null : String.valueOf(tuple.get("created_user_chart_node_name"));
            String createdUserChartNodeCode = tuple.get("created_user_chart_node_code") == null ? null : String.valueOf(tuple.get("created_user_chart_node_code"));
            String createdUserChartShortName = tuple.get("created_user_chart_short_name") == null ? null : String.valueOf(tuple.get("created_user_chart_short_name"));
            String createdUserTitleName = tuple.get("created_user_title_name") == null ? null : String.valueOf(tuple.get("created_user_title_name"));
//            String isExpire = tuple.get("is_expire") == null ? null : String.valueOf(tuple.get("is_expire"));
            String taskName = tuple.get("taskName") == null ? null : String.valueOf(tuple.get("taskName"));
            String procDefId = tuple.get("proc_def_id") == null ? null : String.valueOf(tuple.get("proc_def_id"));
            LocalDateTime createdTime = tuple.get("task_created_time") == null ? null : TimeUtils.stringToLocalDateTime(tuple.get("task_created_time").toString(), TimeUtils.FORMAT_DATE_DD_MM_YYYY);
            LocalDateTime finishedTime = tuple.get("finished_time") == null ? null : TimeUtils.stringToLocalDateTime(tuple.get("finished_time").toString(), TimeUtils.FORMAT_DATE_DD_MM_YYYY);
            LocalDateTime slaFinishTime = tuple.get("sla_finish_time") == null ? null : TimeUtils.stringToLocalDateTime(tuple.get("sla_finish_time").toString(), TimeUtils.FORMAT_DATE_DD_MM_YYYY);
            LocalDateTime startedTime = tuple.get("started_time") == null ? null : TimeUtils.stringToLocalDateTime(tuple.get("started_time").toString(), TimeUtils.FORMAT_DATE_DD_MM_YYYY);

            DetailReportByGroupDto detailReportByGroupDto = new DetailReportByGroupDto();
            detailReportByGroupDto.setCreatedTime(createdTime);
            detailReportByGroupDto.setFinishedTime(finishedTime);
            detailReportByGroupDto.setSlaFinishTime(slaFinishTime);
            detailReportByGroupDto.setStartedTime(startedTime);
            detailReportByGroupDto.setProcInstId(procInstId);
            detailReportByGroupDto.setTaskId(taskId);
            detailReportByGroupDto.setProcInstName(procInstName);
            detailReportByGroupDto.setServiceName(serviceName);
            detailReportByGroupDto.setTaskStatus(taskStatus);
            detailReportByGroupDto.setTaskType(taskType);
            detailReportByGroupDto.setElapsedTime(elapsedTime);
            detailReportByGroupDto.setDelayTime(delayTime);
            detailReportByGroupDto.setPriority(priority);
            detailReportByGroupDto.setReasonCancel(reasonCancel);
            detailReportByGroupDto.setCountReturned(countTaskCancel);
            detailReportByGroupDto.setCreatedUser(createdUser);
            detailReportByGroupDto.setCreatedUserFullName(createdUserFullName);
            detailReportByGroupDto.setCreatedUserChartNodeName(createdUserChartNodeName);
            detailReportByGroupDto.setCreatedUserChartNodeCode(createdUserChartNodeCode);
            detailReportByGroupDto.setCreatedUserChartShortName(createdUserChartShortName);
            detailReportByGroupDto.setCreatedUserTitleName(createdUserTitleName);
            detailReportByGroupDto.setAssignee(assignee);
            detailReportByGroupDto.setAssigneeFullName(assigneeFullName);
            detailReportByGroupDto.setTaskName(taskName);
            detailReportByGroupDto.setRequestCode(requestCode);
            setIsExpire(detailReportByGroupDto, slaFinishTime, finishedTime);
            detailReportByGroupDto.setTicketId(ticketId);
            detailReportByGroupDto.setProcDefId(procDefId);
            detailReportByGroupDto.setAssigneeChartNodeName(assigneeChartNodeName);
            detailReportByGroupDto.setAssigneeChartNodeCode(assigneeChartNodeCode);
            detailReportByGroupDto.setChartNodeIds(assigneeChartNodeIds);
            detailReportByGroupDto.setAssigneeTitleName(assigneeTitleName);
            detailReportByGroupDto.setAssigneeChartShortName(assigneeChartShortName);
            detailReportByGroupDtos.add(detailReportByGroupDto);
        });
    }

    public void mapTupleToDetailTaskDto(List<Tuple> result, List<DetailTaskDto> detailTaskDtos) {
        result.forEach(tuple -> {
            String requestCode = tuple.get("request_code") == null ? null : String.valueOf(tuple.get("request_code"));
            String taskType = tuple.get("type") == null ? null : String.valueOf(tuple.get("type"));
            String taskName = tuple.get("name") == null ? null : String.valueOf(tuple.get("name"));
            String procInstId = tuple.get("proc_inst_id") == null ? null : String.valueOf(tuple.get("proc_inst_id"));
            String priority = tuple.get("priority") == null ? null : String.valueOf(tuple.get("priority"));
            Long ticketId = tuple.get("ticketId") == null ? null : Long.valueOf(tuple.get("ticketId").toString());
            String assigneeFullName = tuple.get("assignee_full_name") == null ? null : String.valueOf(tuple.get("assignee_full_name"));
            String assigneeChartShortName = tuple.get("assignee_chart_short_name") == null ? null : String.valueOf(tuple.get("assignee_chart_short_name"));
            String assigneeChartNodeName = tuple.get("assignee_chart_node_name") == null ? null : String.valueOf(tuple.get("assignee_chart_node_name"));
            String assigneeTitleName = tuple.get("assignee_title_name") == null ? null : String.valueOf(tuple.get("assignee_title_name"));
            String assignee = tuple.get("assignee") == null ? null : String.valueOf(tuple.get("assignee"));
            String assigneeDirectManager = tuple.get("assignee_direct_manager") == null ? null : String.valueOf(tuple.get("assignee_direct_manager"));
            String assigneeStatus = tuple.get("assignee_status") == null ? null : String.valueOf(tuple.get("assignee_status"));
            String createdUser = tuple.get("created_user") == null ? null : String.valueOf(tuple.get("created_user"));
            String createdUserChartShortName = tuple.get("created_user_chart_short_name") == null ? null : String.valueOf(tuple.get("created_user_chart_short_name"));
            String createdUserFullName = tuple.get("created_user_full_name") == null ? null : String.valueOf(tuple.get("created_user_full_name"));
            String createdUserChartNodeName = tuple.get("created_user_chart_node_name") == null ? null : String.valueOf(tuple.get("created_user_chart_node_name"));
            String createdUserTitleName = tuple.get("created_user_title_name") == null ? null : String.valueOf(tuple.get("created_user_title_name"));
            String assigneeStaffCode = tuple.get("assignee_staff_code") == null ? null : String.valueOf(tuple.get("assignee_staff_code"));
            String status = tuple.get("status") == null ? null : String.valueOf(tuple.get("status"));
            String procDefId = tuple.get("proc_def_id") == null ? null : String.valueOf(tuple.get("proc_def_id"));
//            String isExpire = tuple.get("is_expire") == null ? null : String.valueOf(tuple.get("is_expire"));
            LocalDateTime finishedTime = tuple.get("finished_time") == null ? null : TimeUtils.stringToLocalDateTime(tuple.get("finished_time").toString(), TimeUtils.FORMAT_DATE_DD_MM_YYYY);
            LocalDateTime slaFinishTime = tuple.get("sla_finish_time") == null ? null : TimeUtils.stringToLocalDateTime(tuple.get("sla_finish_time").toString(), TimeUtils.FORMAT_DATE_DD_MM_YYYY);
            Long elapsedTime = tuple.get("elapsed_time") == null ? null : Long.valueOf(tuple.get("elapsed_time").toString());
            Double delayTime = tuple.get("delay_time") == null ? null : Double.valueOf(tuple.get("delay_time").toString());

            DetailTaskDto detailTaskDto = new DetailTaskDto();
            LocalDateTime createdTime = tuple.get("created_time") == null ? null : TimeUtils.stringToLocalDateTime(tuple.get("created_time").toString(), TimeUtils.FORMAT_DATE_DD_MM_YYYY);
            detailTaskDto.setCreatedTime(createdTime);
            LocalDateTime startedTime = tuple.get("started_time") == null ? null : TimeUtils.stringToLocalDateTime(tuple.get("started_time").toString(), TimeUtils.FORMAT_DATE_DD_MM_YYYY);
            detailTaskDto.setStartedTime(startedTime);
            detailTaskDto.setFinishedTime(finishedTime);
            detailTaskDto.setSlaFinishTime(slaFinishTime);
            detailTaskDto.setElapsedTime(elapsedTime);
            detailTaskDto.setDelayTime(delayTime);
            detailTaskDto.setRequestCode(requestCode);
            detailTaskDto.setPriority(priority);
            detailTaskDto.setTaskType(taskType);
            detailTaskDto.setStatus(status);
            detailTaskDto.setAssignee(assignee);
            detailTaskDto.setTaskName(taskName);
            detailTaskDto.setAssigneeFullName(assigneeFullName);
            detailTaskDto.setAssigneeChartNodeName(assigneeChartNodeName);
            detailTaskDto.setAssigneeTitleName(assigneeTitleName);
            detailTaskDto.setAssigneeChartShortName(assigneeChartShortName);
            detailTaskDto.setAssigneeDirectManager(assigneeDirectManager);
            detailTaskDto.setAssigneeStatus(assigneeStatus);
            detailTaskDto.setAssigneeStaffCode(assigneeStaffCode);
            detailTaskDto.setCreatedUser(createdUser);
            detailTaskDto.setCreatedUserFullName(createdUserFullName);
            detailTaskDto.setCreatedUserChartNodeName(createdUserChartNodeName);
            detailTaskDto.setCreatedUserChartShortName(createdUserChartShortName);
            detailTaskDto.setCreatedUserTitleName(createdUserTitleName);
            detailTaskDto.setProcInstId(procInstId);
            detailTaskDto.setTicketId(ticketId);
            detailTaskDto.setProcDefId(procDefId);
//            detailTaskDto.setIsExpire(isExpire);
            LocalDateTime currentDateTime = LocalDateTime.now();

            if(ObjectUtils.isEmpty(startedTime) && currentDateTime.isAfter(slaFinishTime)){
                detailTaskDto.setIsExpire("true");
            }
            if(ObjectUtils.isEmpty(startedTime) && currentDateTime.isBefore(slaFinishTime)){
                detailTaskDto.setIsExpire("false");
            }
            if (!ObjectUtils.isEmpty(finishedTime) && finishedTime.isAfter(slaFinishTime)) {
                detailTaskDto.setIsExpire("true");
            }
            if (ObjectUtils.isEmpty(finishedTime) && currentDateTime.isAfter(slaFinishTime)) {
                detailTaskDto.setIsExpire("true");
            }
            if (!ObjectUtils.isEmpty(finishedTime) && finishedTime.isBefore(slaFinishTime)) {
                detailTaskDto.setIsExpire("false");
            }

            detailTaskDtos.add(detailTaskDto);
        });
    }

    public void tupleToReportProcInstDto(Tuple tuple, ReportProcInstDto des) throws ParseException {
        Integer carryoverPreviousApproval = tuple.get("carryoverPreviousApproval") == null ? null : Integer.valueOf(tuple.get("carryoverPreviousApproval").toString());
        Integer duringApprovalPeriod = tuple.get("duringApprovalPeriod") == null ? null : Integer.valueOf(tuple.get("duringApprovalPeriod").toString());
        Integer pending = tuple.get("pending") == null ? null : Integer.valueOf(tuple.get("pending").toString());
        Integer approved = tuple.get("approved") == null ? null : Integer.valueOf(tuple.get("approved").toString());
        Integer approvedOnTime = tuple.get("approvedOnTime") == null ? null : Integer.valueOf(tuple.get("approvedOnTime").toString());
        Integer approvedDelayed = tuple.get("approvedDelayed") == null ? null : Integer.valueOf(tuple.get("approvedDelayed").toString());
        Integer approvingNotDelayed = tuple.get("approvingNotDelayed") == null ? null : Integer.valueOf(tuple.get("approvingNotDelayed").toString());
        Integer approvingDelayed = tuple.get("approvingDelayed") == null ? null : Integer.valueOf(tuple.get("approvingDelayed").toString());
        Integer approvalReturned = tuple.get("approvalReturned") == null ? null : Integer.valueOf(tuple.get("approvalReturned").toString());
        Integer approvalCanceled = tuple.get("approvalCanceled") == null ? null : Integer.valueOf(tuple.get("approvalCanceled").toString());

        des.setCarryoverPreviousApproval(carryoverPreviousApproval);
        des.setDuringApprovalPeriod(duringApprovalPeriod);
        des.setPending(pending);
        des.setApproved(approved);
        des.setApprovedOnTime(approvedOnTime);
        des.setApprovedDelayed(approvedDelayed);
        des.setApprovingNotDelayed(approvingNotDelayed);
        des.setApprovingDelayed(approvingDelayed);
        des.setApprovalReturned(approvalReturned);
        des.setApprovalCanceled(approvalCanceled);

        if (des instanceof ReportProcInstByUserDto) {
            setValueToReportProcInstByUserDto(tuple, (ReportProcInstByUserDto) des);
        }
        if (des instanceof ReportProcInstByGroupDto) {
            setValueToReportProcInstByGroupDto(tuple, (ReportProcInstByGroupDto) des);
        }
        if (des instanceof ReportProcInstByChartNodeDto) {
            setValueToReportProcInstByUserChartNodeDto(tuple, (ReportProcInstByChartNodeDto) des);
        }
    }

    private void setValueToReportProcInstByUserDto(Tuple tuple, ReportProcInstByUserDto dto) {
        String createdUser = tuple.get("created_user") == null ? null : String.valueOf(tuple.get("created_user"));
        String serviceName = tuple.get("service_name") == null ? null : String.valueOf(tuple.get("service_name"));
        Long serviceId = tuple.get("service_id") == null ? null : Long.valueOf(tuple.get("service_id").toString());
        dto.setCreatedUser(createdUser);
        dto.setServiceName(serviceName);
        dto.setServiceId(serviceId);
    }

    private void setValueToReportProcInstByGroupDto(Tuple tuple, ReportProcInstByGroupDto dto) {
        String serviceName = tuple.get("service_name") == null ? null : String.valueOf(tuple.get("service_name"));
        Long serviceId = tuple.get("service_id") == null ? null : Long.valueOf(tuple.get("service_id").toString());
        dto.setServiceName(serviceName);
        dto.setServiceId(serviceId);
    }

    private void setValueToReportProcInstByUserChartNodeDto(Tuple tuple, ReportProcInstByChartNodeDto dto) {
        String assignee = tuple.get("assignee") == null ? null : String.valueOf(tuple.get("assignee"));
//        String assigneeFullName = tuple.get("assignee_full_name") == null ? null : String.valueOf(tuple.get("assignee_full_name"));
//        String staffCode = tuple.get("assignee_staff_code") == null ? null : String.valueOf(tuple.get("assignee_staff_code"));
//        String chartShortName = tuple.get("assignee_chart_short_name") == null ? null : String.valueOf(tuple.get("assignee_chart_short_name"));
        Long chartId = tuple.get("assignee_chart_id") == null ? null : Long.valueOf(tuple.get("assignee_chart_id").toString());

        String chartNodeId = tuple.get("assignee_chart_node_id") == null ? null : String.valueOf(String.valueOf(tuple.get("assignee_chart_node_id")));
        String[] parts = chartNodeId.split(", ");
        List<Long> chartNodeIds = Arrays.stream(parts)
                .map(Long::valueOf)
                .collect(Collectors.toList());

        List<String> chartNodeCode = tuple.get("assignee_chart_node_code") == null ? null : Arrays.asList(String.valueOf(tuple.get("assignee_chart_node_code")).split(","));
        dto.setAssignee(assignee);
        dto.setChartNodeCode(chartNodeCode);
        dto.setChartId(chartId);
        dto.setChartNodeId(chartNodeIds);
    }

    public static String replaceChartNodeId(String inputString) {
        return "'%" + inputString + "%'" + " " + "OR" + " ";
    }

    private static void addOrderByClause(StringBuilder stringBuilder, String column, String sortType) {
        stringBuilder.append(" ORDER BY ").append(column);
        if (!ObjectUtils.isEmpty(sortType) && sortType.equalsIgnoreCase("DESC")) {
            stringBuilder.append(" DESC ");
        } else {
            stringBuilder.append(" ASC");
        }
    }

    public static boolean setIsExpire(DetailReportByGroupDto detailReportByGroupDto, LocalDateTime slaFinishTime, LocalDateTime finishedTime) {
        LocalDateTime currentDateTime = LocalDateTime.now();
        if (!ValidationUtils.isNullOrEmpty(finishedTime) && finishedTime.isAfter(slaFinishTime)) {
            detailReportByGroupDto.setIsExpire("true");
            return true;
        }
        if (ValidationUtils.isNullOrEmpty(finishedTime) && currentDateTime.isAfter(slaFinishTime)) {
            detailReportByGroupDto.setIsExpire("true");
            return true;
        } else {
            detailReportByGroupDto.setIsExpire("false");
            return false;
        }
    }
}