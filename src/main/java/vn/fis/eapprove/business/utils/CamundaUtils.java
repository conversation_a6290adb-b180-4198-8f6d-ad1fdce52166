package vn.fis.eapprove.business.utils;

import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.*;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.camunda.bpm.model.xml.ModelParseException;
import vn.fis.spro.common.util.ValidationUtils;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Author: PhucVM
 * Date: 28/10/2022
 */
@Slf4j
public class CamundaUtils {

    public static StartEvent getStartEvent(BpmnModelInstance modelInstance) {
        if (modelInstance != null) {
            Collection<StartEvent> startEvents = modelInstance.getModelElementsByType(StartEvent.class);
            for (StartEvent event : startEvents) {
                return event;
            }
        }

        return null;
    }

    public static EndEvent getEndEvent(BpmnModelInstance modelInstance) {
        if (modelInstance != null) {
            Collection<EndEvent> endEvents = modelInstance.getModelElementsByType(EndEvent.class);
            for (EndEvent event : endEvents) {
                return event;
            }
        }

        return null;
    }

    public static Collection<UserTask> getUserTasks(BpmnModelInstance modelInstance) {
        if (modelInstance != null) {
            return modelInstance.getModelElementsByType(UserTask.class);
        }

        return new ArrayList<>();
    }

    public static UserTask getUserTaskByKey(BpmnModelInstance modelInstance, String taskDefKey) {
        if (modelInstance != null && !ValidationUtils.isNullOrEmpty(taskDefKey)) {
            Collection<UserTask> userTasks = getUserTasks(modelInstance);
            return userTasks.stream().filter(task -> task.getId().equals(taskDefKey)).findAny().orElse(null);
        }

        return null;
    }

    public static String getCollectionVariableNameFromExpression(String var) {
        String regex = "^([#$])\\{.*\\((.*)\\)\\.elements\\(\\)}$";
        try {
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(var);
            if (matcher.find()) {
                return matcher.group(2);
            }

            return var;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return var;
    }

    public static String getVariableNameFromExpression(String var) {
        String regex = "^([#$])(.*?[(]|\\{)(.*?)([)]|})";
        try {
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(var);
            if (matcher.find()) {
                return matcher.group(3);
            }

            return var;
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return var;
    }

    public static boolean isMultiInstanceTask(Task task) {
        return task != null
                && task.getLoopCharacteristics() != null
                && task.getLoopCharacteristics() instanceof MultiInstanceLoopCharacteristics;
    }

    public static String getMultiInstanceCollection(Task task) {
        return isMultiInstanceTask(task) ?
                ((MultiInstanceLoopCharacteristics) task.getLoopCharacteristics()).getCamundaCollection()
                : null;
    }

    public static Collection<CamundaProperty> getCamundaProperties(FlowNode flowNode) {
        if (flowNode != null) {
            ExtensionElements extensionElements = flowNode.getExtensionElements();
            if (extensionElements != null) {
                CamundaProperties camundaProperties = extensionElements.getElementsQuery().filterByType(CamundaProperties.class).singleResult();
                if (camundaProperties != null) {
                    return camundaProperties.getCamundaProperties();
                }
            }
        }

        return new ArrayList<>();
    }

    public static BpmnModelInstance convertBytesToBpmnModelInstance(byte[] bpmnBytes) {
        try {
            // Convert byte array to String assuming UTF-8 encoding
            String bpmnXml = new String(bpmnBytes, StandardCharsets.UTF_8);

            // Parse the BPMN XML and create a BpmnModelInstance
            return Bpmn.readModelFromStream(new ByteArrayInputStream(bpmnXml.getBytes(StandardCharsets.UTF_8)));
        } catch (ModelParseException e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }
}
