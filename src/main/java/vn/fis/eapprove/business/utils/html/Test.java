package vn.fis.eapprove.business.utils.html;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTpSignZone;
import vn.fis.eapprove.business.model.request.VariableValueDto;
import vn.fis.eapprove.business.tenant.manager.GotenbergManager;
import vn.fis.spro.common.helper.ResponseHelper;

import java.io.File;
import java.util.*;

@RestController("TestControllerHtml")
@CrossOrigin("*")
@RequestMapping("/html")
public class Test {
    @Autowired
    private TemplateHtml templateHtml;
    @Autowired
    private GotenbergManager gotenbergManager;

    @GetMapping("/test")
    public ResponseEntity<?> test() throws Exception {

        String templateString = "<!DOCTYPE html> " +
                "<html> " +
                "<head> " +
                "    <meta charset=\"utf-8\"/> " +
                "    <title>Document with Page Breaks</title> " +
                "    <style> " +
                "        body { " +
                "            font-family: Arial, sans-serif; " +
                "            margin: 0; " +
                "            padding: 0; " +
                "        } " +
                " " +
                "        @page { " +
                "            size: A4; /* A4 */ " +
                "            /*margin: 8mm;*/ " +
                "        } " +
                " " +
                "        @page { " +
                "            @bottom-center { " +
                "                content: \"Page \" counter(page) \" of \" counter(pages); " +
                "                font-size: 10px; " +
                "                color: gray; " +
                "            } " +
                "        } " +
                " " +
                "        .section { " +
                "            padding: 20px; " +
                "        } " +
                " " +
                "        .page-break { " +
                "            page-break-after: always; " +
                "        } " +
                " " +
                "        p, div, span { " +
                "            word-wrap: break-word; " +
                "            overflow-wrap: break-word; " +
                "        } " +
                " " +
                "        thead { " +
                "            display: table-header-group; /* Đảm bảo tiêu đề bảng hiện ở mỗi trang */ " +
                "        } " +
                " " +
                "        tr { " +
                "            page-break-inside: avoid; " +
                "            break-inside: avoid; " +
                "        } " +
                "    </style> " +
                "</head> " +
                "<body> " +
                "<div class=\"section\"> " +
                "    <p>@{type:\"IMAGE\",targetWidth:194, targetHeight:103,isExternalUrl: true,imageUrl :\"@start_slt_logo\"}</p> " +
                "    <p>@{imageUrl:@start, targetWidth:150, targetHeight:150, isExternalUrl: false, addPosition:true, addSignDate:true, addSignImage:true, addSignatoryName:true, addSignAction:true}</p> " +
                "    <p>@{imageUrl:@st01, targetWidth:150, targetHeight:150, isExternalUrl: false, addPosition:true, addSignDate:true, addSignImage:true, addSignatoryName:true, addSignAction:true}</p> " +
                "    <p>Select1: @{type:\"TEXT\", value:\"@start_slt_select1\"}</p> " +
                "    <p>Th&ocirc;ng tin chung</p> " +
                "    <p>Text1: @{type:\"TEXT\", value:\"@start_txt_text1\"}</p> " +
                "    <p>Text2: @{type:\"TEXT\", value:\"@start_txt_text2\"}</p> " +
                "    <p>File: @{type:\"TEXT\", value:\"@start_up_trinhKy\"}</p> " +
                "    <p> " +
                "        <strong>slt_duyetTuanTu: </strong>@{type:\"TEXT\", value:\"@start_slt_duyetTuanTu\"}</p> " +
                "    <p> " +
                "        <strong>slt_duyetSongsong: </strong>@{type:\"TEXT\", value:\"@start_slt_duyetSongSong\"}</p> " +
                "    <p>start@{type:\"TABLE_SIGN\", tableName: \"@start\"}</p> " +
                "    <table cellspacing=\"0\" style=\"border-collapse:collapse\"> " +
                "        <thead> " +
                "        <tr> " +
                "            <td style=\"border-bottom:1px solid black; border-left:1px solid black; border-right:1px solid black; border-top:1px solid black; vertical-align:top; width:208px\"> " +
                "                <p>STT</p> " +
                "            </td> " +
                "            <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:1px solid black; vertical-align:top; width:208px\"> " +
                "                <p>Ky</p> " +
                "            </td> " +
                "            <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:1px solid black; vertical-align:top; width:208px\"> " +
                "                <p>Comment</p> " +
                "            </td> " +
                "        </tr> " +
                "        </thead> " +
                "        <tbody> " +
                "        <tr> " +
                "            <td style=\"border-bottom:1px solid black; border-left:1px solid black; border-right:1px solid black; border-top:none; vertical-align:top; width:208px\"> " +
                "                <p>${index}</p> " +
                "            </td> " +
                "            <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:none; vertical-align:top; width:208px\"> " +
                "                <p>${imageUrl:@start, targetWidth:150, targetHeight:150, addPosition:true, addSignDate:true}</p> " +
                "            </td> " +
                "            <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:none; vertical-align:top; width:208px\"> " +
                "                <p>${comment}</p> " +
                "            </td> " +
                "        </tr> " +
                "        </tbody> " +
                "    </table> " +
                "</div> " +
                "<div class=\"page-break\" style=\"page-break-after: always\"><span style=\"display:none\">&nbsp;</span></div> " +
                "<p> @{type:\"TABLE\", value:\"@start_tbl_thongTinLaiSuat2\", dynamicTitle:true, rowConfigs:[(value: \"number1\", format:\"#,###,###,###\")]}</p> " +
                "<table border=\"1\" cellpadding=\"1\" cellspacing=\"1\" style=\"width:500px\"> " +
                "    <thead> " +
                "    <tr> " +
                "        <th scope=\"col\">Stt</th> " +
                "        <th scope=\"col\">T&ecirc;n c&ocirc;ng ty gửi tiền</th> " +
                "        <th scope=\"col\">T&ecirc;n ng&acirc;n h&agrave;ng nhận</th> " +
                "        <th scope=\"col\">Số tiền gửi</th> " +
                "        <th scope=\"col\">Ng&agrave;y bắt đầu</th> " +
                "        <th scope=\"col\">Ng&agrave;y kết th&uacute;c</th> " +
                "        <th scope=\"col\">Kỳ hạn</th> " +
                "        <th scope=\"col\">L&atilde;i suất(% / năm)</th> " +
                "        <th scope=\"col\">${header1}</th> " +
                "        <th scope=\"col\">${header2}</th> " +
                "        <th scope=\"col\">${header3}</th> " +
                "    </tr> " +
                "    </thead> " +
                "    <tbody> " +
                "    <tr> " +
                "        <td>${index}</td> " +
                "        <td>${text1}</td> " +
                "        <td>${number1}</td> " +
                "        <td>${text1}</td> " +
                "        <td>${text1}</td> " +
                "        <td>${text1}</td> " +
                "        <td>${text1}</td> " +
                "        <td>${text1}</td> " +
                "        <td>${text1}</td> " +
                "        <td>${text1}</td> " +
                "        <td>${text1}</td> " +
                "    </tr> " +
                "    </tbody> " +
                "    <tfoot> " +
                "    <tr> " +
                "        <td colspan=\"3\" rowspan=\"1\">Tổng</td> " +
                "        <td>@{type:\"NUMBER\",value:\"@start_txt_number\",format:\"#,###,###,###\"}</td> " +
                "        <td>&nbsp;</td> " +
                "        <td>&nbsp;</td> " +
                "        <td>&nbsp;</td> " +
                "        <td>&nbsp;</td> " +
                "        <td>&nbsp;</td> " +
                "        <td>&nbsp;</td> " +
                "        <td>&nbsp;</td> " +
                "    </tr> " +
                "    </tfoot> " +
                "</table> " +
                "<div class=\"page-break\" style=\"page-break-after: always\"><span style=\"display:none\">&nbsp;</span></div> " +
                "<p>St01@{type:\"TABLE_SIGN\", tableName: \"@st01\"}</p> " +
                "<table cellspacing=\"0\" style=\"border-collapse:collapse\"> " +
                "    <thead> " +
                "    <tr> " +
                "        <td style=\"border-bottom:1px solid black; border-left:1px solid black; border-right:1px solid black; border-top:1px solid black; vertical-align:top; width:208px\"> " +
                "            <p>STT</p> " +
                "        </td> " +
                "        <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:1px solid black; vertical-align:top; width:208px\"> " +
                "            <p>Ky</p> " +
                "        </td> " +
                "        <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:1px solid black; vertical-align:top; width:208px\"> " +
                "            <p>Comment</p> " +
                "        </td> " +
                "    </tr> " +
                "    </thead> " +
                "    <tbody> " +
                "    <tr> " +
                "        <td style=\"border-bottom:1px solid black; border-left:1px solid black; border-right:1px solid black; border-top:none; height:.3in; vertical-align:top; width:208px\"> " +
                "            <p>${index}</p> " +
                "        </td> " +
                "        <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:none; height:.3in; vertical-align:top; width:208px\"> " +
                "            <p>${imageUrl:@st01, targetWidth:150, targetHeight:150, addPosition:true, addSignDate:true}</p> " +
                "        </td> " +
                "        <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:none; height:.3in; vertical-align:top; width:208px\"> " +
                "            <p>${comment}</p> " +
                "        </td> " +
                "    </tr> " +
                "    </tbody> " +
                "</table> " +
                "<p>St02@{type:\"TABLE_SIGN\", tableName: \"@st02\"}</p> " +
                "<table cellspacing=\"0\" style=\"border-collapse:collapse\"> " +
                "    <thead> " +
                "    <tr> " +
                "        <td style=\"border-bottom:1px solid black; border-left:1px solid black; border-right:1px solid black; border-top:1px solid black; vertical-align:top; width:208px\"> " +
                "            <p>STT</p> " +
                "        </td> " +
                "        <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:1px solid black; vertical-align:top; width:208px\"> " +
                "            <p>Ky</p> " +
                "        </td> " +
                "        <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:1px solid black; vertical-align:top; width:208px\"> " +
                "            <p>Comment</p> " +
                "        </td> " +
                "    </tr> " +
                "    </thead> " +
                "    <tbody> " +
                "    <tr> " +
                "        <td style=\"border-bottom:1px solid black; border-left:1px solid black; border-right:1px solid black; border-top:none; vertical-align:top; width:208px\"> " +
                "            <p>${index}</p> " +
                "        </td> " +
                "        <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:none; vertical-align:top; width:208px\"> " +
                "            <p>${imageUrl:@st02, targetWidth:150, targetHeight:150, addPosition:true, addSignDate:true}</p> " +
                "        </td> " +
                "        <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:none; vertical-align:top; width:208px\"> " +
                "            <p>${comment}</p> " +
                "        </td> " +
                "    </tr> " +
                "    </tbody> " +
                "</table> " +
                "<p>St03@{type:\"TABLE_SIGN\", tableName: \"@st03\"}</p> " +
                "<table cellspacing=\"0\" style=\"border-collapse:collapse\"> " +
                "    <thead> " +
                "    <tr> " +
                "        <td style=\"border-bottom:1px solid black; border-left:1px solid black; border-right:1px solid black; border-top:1px solid black; vertical-align:top; width:208px\"> " +
                "            <p>STT</p> " +
                "        </td> " +
                "        <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:1px solid black; vertical-align:top; width:208px\"> " +
                "            <p>Ky</p> " +
                "        </td> " +
                "        <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:1px solid black; vertical-align:top; width:208px\"> " +
                "            <p>Comment</p> " +
                "        </td> " +
                "    </tr> " +
                "    </thead> " +
                "    <tbody> " +
                "    <tr> " +
                "        <td style=\"border-bottom:1px solid black; border-left:1px solid black; border-right:1px solid black; border-top:none; vertical-align:top; width:208px\"> " +
                "            <p>${index}</p> " +
                "        </td> " +
                "        <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:none; vertical-align:top; width:208px\"> " +
                "            <p>${imageUrl:@st03, targetWidth:150, targetHeight:150, addPosition:true, addSignDate:true}</p> " +
                "        </td> " +
                "        <td style=\"border-bottom:1px solid black; border-left:none; border-right:1px solid black; border-top:none; vertical-align:top; width:208px\"> " +
                "            <p>${comment}</p> " +
                "        </td> " +
                "    </tr> " +
                "    </tbody> " +
                "</table> " +
                "<p>aaa</p> " +
                "<div style=\"page-break-after: always\"><span style=\"display:none\">&nbsp;</span> " +
                "</div> " +
                "<p>bbbb</p> " +
                "</body> " +
                "</html>";
        //List giá trị
        Map<String, VariableValueDto> mapVariableValueDto = new HashMap<>();
        //IMAGE
        VariableValueDto valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("https://s3-sgn09.fptcloud.com:443/eapp-uat/logo/fis.png");
        mapVariableValueDto.put("start_slt_logo", valueDto);

        // text
        valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("select1");
        mapVariableValueDto.put("start_slt_select1", valueDto);

        valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("text1");
        mapVariableValueDto.put("start_txt_text1", valueDto);

        valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("text2");
        mapVariableValueDto.put("start_txt_text2", valueDto);

        valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("[\"item-01\",\"item-02\"]");
        mapVariableValueDto.put("start_slt_duyetTuanTu", valueDto);

        valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("[000,1111]");
        mapVariableValueDto.put("start_slt_duyetSongSong", valueDto);

        valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("999999999999");
        mapVariableValueDto.put("start_txt_number", valueDto);

        //Table
        valueDto = new VariableValueDto();
        valueDto.setType("Json");
        String value = "{\"customType\":\"Table\",\"data\":{\"tableName\":\"Số tiền hoàn ứng trong kỳ\",\"columns\":[{\"name\":\"lbl_dienGiai\",\"label\":\"Diễn giải\"},{\"name\":\"lbl_soChungTuChi\",\"label\":\"Số chứng từ chi\"},{\"name\":\"lbl_thoiGian\",\"label\":\"Thời gian\"},{\"name\":\"lbl_soChungTuhoaDon\",\"label\":\"Số chứng từ/Hóa đơn\"},{\"name\":\"lbl_soTien\",\"label\":\"Số tiền\"},{\"name\":\"lbl_taiKhoanHachToan\",\"label\":\"Tài khoản hạch toán\"},{\"name\":\"lbl_type\",\"label\":\"Type\"},{\"name\":\"lbl_companycode\",\"label\":\"companyCode\"},{\"name\":\"txt_dienGiaiTrongKy\",\"label\":\"Diễn giải trong kỳ\"},{\"name\":\"slt_soChungTuChiTrongKy\",\"label\":\"Số chứng từ chi trong kỳ\"},{\"name\":\"dtm_thoiGianTrongKy\",\"label\":\"Thời gian trong kỳ\"},{\"name\":\"txt_soChungTuhoaDon\",\"label\":\"Số chứng từ/Hóa đơn\"},{\"name\":\"txt_soTienTrongKy\",\"label\":\"Số tiền trong kỳ\"},{\"name\":\"txt_taiKhoanHachToan\",\"label\":\"Tài khoản hạch toán\"},{\"name\":\"txt_text2\",\"label\":\"Text2\"},{\"name\":\"txt_text4\",\"label\":\"Text4\"}],\"data\":[{\"header1\":\"header1\",\"header2\":\"header2\",\"header3\":33333},{\"text1\":\"body1\",\"number1\":99999999},{\"text1\":\"body2\",\"number1\":10000000009}]}}";
        valueDto.setValue(value);
        mapVariableValueDto.put("start_tbl_thongTinLaiSuat2", valueDto);

        List<BpmTpSignZone> bpmTpSignZoneList = new ArrayList<>();
        BpmTpSignZone bpmTpSignZone = new BpmTpSignZone();
        bpmTpSignZone.setLastName("Tên người kí 1");
        bpmTpSignZone.setTaskDefKey("start");
        bpmTpSignZone.setComment("Đây là comment 1");
        bpmTpSignZone.setPosition("Nhân viên 1");
        bpmTpSignZone.setSignedDate(new Date());
        bpmTpSignZone.setSign("user_signature/employee/img_20241226090315.jpg");
        bpmTpSignZoneList.add(bpmTpSignZone);

        bpmTpSignZone = new BpmTpSignZone();
        bpmTpSignZone.setLastName("Tên người kí 2");
        bpmTpSignZone.setTaskDefKey("start");
        bpmTpSignZone.setComment("Đây là comment 2");
        bpmTpSignZone.setPosition("Nhân viên 2");
        bpmTpSignZone.setSignedDate(new Date());
        bpmTpSignZone.setSign("user_signature/employee/img_20241226090315.jpg");
        bpmTpSignZoneList.add(bpmTpSignZone);

        bpmTpSignZone = new BpmTpSignZone();
        bpmTpSignZone.setLastName("Tên người kí 3");
        bpmTpSignZone.setTaskDefKey("st01");
        bpmTpSignZone.setComment("Đây là comment 3");
        bpmTpSignZone.setPosition("Nhân viên 3");
        bpmTpSignZone.setSignedDate(new Date());
        bpmTpSignZone.setSign("user_signature/employee/img_20241226090315.jpg");
        bpmTpSignZoneList.add(bpmTpSignZone);

//        String htmlString = templateHtml.handleData(mapVariableValueDto, templateString, bpmTpSignZoneList);
        String htmlString = "<!DOCTYPE html> " +
                "<html> " +
                "<head> " +
                " <meta charset=\"utf-8\"/> " +
                " <title>Document with Page Breaks</title> " +
                " <style> " +
                "  @page { " +
                "   size: A4; /* A4, A5 landscape */ " +
                "   @bottom-center { " +
                "    content: \"Page \" counter(page) \" of \" counter(pages); " +
                "    font-size: 10px; " +
                "    color: gray; " +
                "   } " +
                "  } " +
                "  /*.section {*/ " +
                "  /* padding: 20px;*/ " +
                "  /*}*/ " +
                " " +
                "  .page-break { " +
                "   page-break-after: always; " +
                "  } " +
                " " +
                "  p, div, span { " +
                "   word-wrap: break-word; " +
                "   overflow-wrap: break-word; " +
                "  } " +
                " " +
                "  thead { " +
                "   display: table-header-group; /* Đảm bảo tiêu đề bảng hiện ở mỗi trang */ " +
                "  } " +
                " " +
                "  tr { " +
                "   page-break-inside: avoid; " +
                "   break-inside: avoid; " +
                "  } " +
                " </style> " +
                "</head> " +
                "<body> " +
                "<div class=\"padding-wrapper\" style=\"padding: 120px 30px 30px;\"> " +
                " <p align=\"center\" style=\"text-align:center; margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><img align=\"right\" alt=\"Bilancino Hotel logo\" src=\"data:image/png;base64,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\" style=\"width:75px; height:75px\" /><b><span style=\"font-size:24.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">The Flavorful Tuscany Meetup</span></span></b></span></span></span></p> " +
                " " +
                " <p align=\"center\" style=\"text-align:center; margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><b><span style=\"font-size:18.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\"><span style=\"color:#007ac9\">Welcome letter</span></span></span></b></span></span></span></p> " +
                " " +
                " <p style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">Dear Guest,</span></span></span></span></span></p> " +
                " " +
                " <p style=\"margin-bottom: 11px;\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">We are delighted to welcome you to the annual <i>Flavorful Tuscany Meetup</i> and hope you will enjoy the programme as well as your stay at the <a href=\"https://ckeditor.com\"><span style=\"color:blue\">Bilancino Hotel</span></a>.</span></span></span></span></span></p> " +
                " " +
                " <p style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">Please find attached the full schedule of the event.</span></span></span></span></span></p> " +
                " " +
                " <p style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">The annual Flavorful Tuscany meetups are always a culinary discovery. You get the best of Tuscan flavors during an intense one-day stay at one of the top hotels of the region. All the sessions are lead by top chefs passionate about their profession. I would certainly recommend to save the date in your calendar for this one!</span></span></span></span></span></p> " +
                " " +
                " <p style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">Angelina <u>Calvino, food journalist</u></span></span></span></span></span></p> " +
                " " +
                " <p style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><u><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">Please arrive at the <a href=\"https://ckeditor.com\"><span style=\"color:blue\">Bilancino Hotel</span></a> reception desk at least <b>half an hour earlier</b> to make sure that the registration process goes as smoothly as possible.</span></span></u></span></span></span></p> " +
                " " +
                " <p style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><u><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">We look forward to welcoming you to the event.</span></span></u></span></span></span></p> " +
                " " +
                " <p style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\"><img alt=\"Victoria Valc signature\" src=\"data:image/png;base64,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\" style=\"width:180px; height:101px\" /></span></span></span></span></span></p> " +
                " " +
                " <p style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><b><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">Victoria Valc</span></span></b></span></span></span></p> " +
                " " +
                " <p style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><b><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">Event Manager</span></span></b><br /> " +
                "<span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">Bilancino Hotel</span></span></span></span></span></p> " +
                " " +
                " <p style=\"margin-bottom:11px\">&nbsp;</p> " +
                " " +
                " <p style=\"margin-bottom:11px\">&nbsp;</p> " +
                "<div class=\"page-break\" style=\"page-break-after: always\"><span style=\"display:none\">&nbsp;</span></div>" +
                " <p align=\"center\" style=\"text-align:center; margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><b><span style=\"font-size:18.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\"><span style=\"color:#007ac9\">The Flavorful Tuscany Meetup Schedule</span></span></span></b></span></span></span></p> " +
                " " +
                " <table align=\"center\" class=\"Table\" style=\"width:6.25in; border-collapse:collapse\" width=\"0\"> " +
                "  <thead> " +
                "  <tr> " +
                "   <td colspan=\"2\" style=\"padding:15px 15px 15px 15px; background-color:#999999\"> " +
                "    <p align=\"center\" style=\"text-align:center\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><b><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\"><span style=\"color:white\">Saturday, July 14</span></span></span></b></span></span></span></p> " +
                "   </td> " +
                "  </tr> " +
                "  </thead> " +
                "  <tbody> " +
                "  <tr> " +
                "   <td style=\"padding:15px 15px 15px 15px; height:111px; background-color:#e6e6e6\"> " +
                "    <p align=\"center\" style=\"text-align:center\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">9:30 AM - 11:30 AM</span></span></span></span></span></p> " +
                "   </td> " +
                "   <td style=\"padding:15px 15px 15px 15px; height:111px\"> " +
                "    <p style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><b><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">Americano vs. Brewed - &ldquo;know your coffee&rdquo;</span></span></b><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\"> with:&nbsp;</span></span></span></span></span></p> " +
                " " +
                "    <ul style=\"margin-bottom:11px\"> " +
                "     <li style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"tab-stops:list .5in\"><span style=\"font-family:Calibri,sans-serif\"><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">Giulia Bianchi</span></span></span></span></span></span></li> " +
                "     <li style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"tab-stops:list .5in\"><span style=\"font-family:Calibri,sans-serif\"><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">Stefano Garau</span></span></span></span></span></span></li> " +
                "     <li style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"tab-stops:list .5in\"><span style=\"font-family:Calibri,sans-serif\"><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">Giuseppe Russo</span></span></span></span></span></span></li> " +
                "    </ul> " +
                "   </td> " +
                "  </tr> " +
                "  <tr> " +
                "   <td style=\"padding:15px 15px 15px 15px; height:102px; background-color:#e6e6e6\"> " +
                "    <p align=\"center\" style=\"text-align:center\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">1:00 PM - 3:00 PM</span></span></span></span></span></p> " +
                "   </td> " +
                "   <td style=\"padding:15px 15px 15px 15px; height:102px\"> " +
                "    <p style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><b><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">Pappardelle al pomodoro</span></span></b><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\"> - live cooking</span></span></span></span></span></p> " +
                " " +
                "    <p style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">Incorporate the freshest ingredients&nbsp;<br /> " +
                "   with Rita Fresco</span></span></span></span></span></p> " +
                "   </td> " +
                "  </tr> " +
                "  <tr> " +
                "   <td style=\"padding:15px 15px 15px 15px; height:54px; background-color:#e6e6e6\"> " +
                "    <p align=\"center\" style=\"text-align:center\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">5:00 PM - 8:00 PM</span></span></span></span></span></p> " +
                "   </td> " +
                "   <td style=\"padding:15px 15px 15px 15px; height:54px\"> " +
                "    <p style=\"margin-bottom:11px\"><span style=\"font-size:11pt\"><span style=\"line-height:normal\"><span style=\"font-family:Calibri,sans-serif\"><b><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\">Tuscan vineyards at a glance</span></span></b><span style=\"font-size:12.0pt\"><span style=\"font-family:&quot;Times New Roman&quot;,serif\"> - wine-tasting&nbsp;<br /> " +
                "   with Frederico Riscoli</span></span></span></span></span></p> " +
                "   </td> " +
                "  </tr> " +
                "  </tbody> " +
                " </table> " +
                " " +
                " <p style=\"margin-bottom:11px\">&nbsp;</p> " +
                " " +
                " <p style=\"margin-bottom:11px\">&nbsp;</p> " +
                "</div> " +
                "</body> " +
                "</html>";
        String headerString = "<!DOCTYPE html>\n" +
                "<html lang=\"en\">\n" +
                "<head>\n" +
                "    <meta charset=\"utf-8\" />\n" +
                "    <title>My PDF</title>\n" +
                "    <style>\n" +
                "        .header {\n" +
                "            font-size: 20px;\n" +
                "            color: black;\n" +
                "            width: 100%;\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "<div class=\"header\">\n" +
                "    <h1 style=\"text-align: center;\">Test</h1>\n" +
                "    <p style=\"text-align: center;\">test header</p>\n" +
                "</div>\n" +
                "</body>\n" +
                "</html>";
        String footerString = "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "    <style>\n" +
                "        .pageCount {\n" +
                "            font-size: 10px;\n" +
                "            color: gray;\n" +
                "            text-align: center;\n" +
                "            width: 100%;\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "<p class=\"pageCount\"><span class=\"pageNumber\">&nbsp;</span> of <span class=\"totalPages\">&nbsp;</span></p>\n" +
                "</body>\n" +
                "</html>";
        byte[] result = gotenbergManager.htmlStringToPdfTest(htmlString, headerString, footerString);
        return ResponseEntity.ok().body(result);
    }
}
