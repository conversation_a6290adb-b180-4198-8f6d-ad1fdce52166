package vn.fis.eapprove.business.utils;

import org.json.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;

import static vn.fis.eapprove.business.constant.Constant.KEY_ROLE_PUBLIC;

@Service
public class RedirectApiUtils {
    public HttpEntity<String> RedirectAPI(ServerHttpRequest req, String body) {
        String hostName = "testHeader";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("realm", hostName);
        HttpEntity<String> entity = new HttpEntity<String>(body, headers);
        return entity;
    }

    public HttpEntity<?> RedirectFormAPI(String tenant, Object body) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-TENANT-ID", tenant);
        HttpEntity<?> entity = new HttpEntity<>(body, headers);
        return entity;
    }

    public HttpEntity<String> RedirectFormAPIs(JSONObject body) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
//        headers.set("realm", tenant);
        HttpEntity<String> entity = new HttpEntity<>(body.toString(), headers);
        return entity;
    }

    public HttpEntity<String> RedirectFormAPIs( String body) {
        HttpHeaders headers = getHeaders( null);
        return new HttpEntity<>(body, headers);
    }

    public HttpEntity<?> RedirectGetAPI( String token) {
        HttpHeaders headers = getHeaders( token);
        return new HttpEntity<>(headers);
    }

    public HttpEntity<?> RedirectFormAPIs( Object body, String token) {
        HttpHeaders headers = getHeaders( token);
        return new HttpEntity<>(body, headers);
    }

    public HttpHeaders getHeaders( String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
//        headers.set("realm", tenant);
        headers.set("key_role", KEY_ROLE_PUBLIC);
        if (token != null && !token.trim().equals("")) {
            headers.setBearerAuth(token);
        }

        return headers;
    }
}
