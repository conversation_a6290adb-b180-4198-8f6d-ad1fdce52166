package vn.fis.eapprove.business.utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.util.DateTimeUtils;
import vn.fis.spro.common.util.ValidationUtils;

import javax.xml.bind.DatatypeConverter;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class FileUtils {

    public static Path getAttachmentFilePath(String type, String tenant, String fileName) {
        Path path = Paths.get(OSUtils.getApplicationDataFolder()
                + File.separator + tenant
                + File.separator + type
                + File.separator + fileName);
        return path;
    }

    public static File convertBase64DecodePdf(String base64, String name) {
        File file = new File("./" + name + ".pdf");
        try (FileOutputStream fos = new FileOutputStream(file)) {
            base64 = base64.replace("data:application/pdf;base64,", "");
            byte[] decoder = DatatypeConverter.parseBase64Binary(base64);
            fos.write(decoder);
            fos.flush();
            fos.close();
        } catch (Exception e) {
            return null;
        }

        return file;
    }

    public static File convertBase64Decode(String base64, String name, String type) {
        File file = new File("./" + name + "." + type);
        try (FileOutputStream fos = new FileOutputStream(file)) {
            base64 = base64.replace(base64.substring(0, base64.indexOf(",", 0)), "");
            byte[] decoder = DatatypeConverter.parseBase64Binary(base64);
            fos.write(decoder);
            fos.flush();
            fos.close();
        } catch (Exception e) {
            return null;
        }

        return file;
    }

    /**
     * Get input-stream from a string
     *
     * <AUTHOR>
     */
    public static InputStream getInputStreamFromString(String s) {
        return s != null ? new ByteArrayInputStream(s.getBytes(StandardCharsets.UTF_8)) : null;
    }

    /**
     * Extract mime-type from encoded base64 string
     *
     * <AUTHOR>
     */
    public static String extractMimeTypeFromEncodedBase64(String encodedBase64) {
        if (!ValidationUtils.isNullOrEmpty(encodedBase64)) {
            Pattern pattern = Pattern.compile("^data:([a-zA-Z0-9]+/[a-zA-Z0-9]+).*,.*");
            Matcher matcher = pattern.matcher(encodedBase64);
            if (matcher.find()) {
                return matcher.group(1).toLowerCase();
            }
        }

        return "";
    }

    /**
     * Get raw encoded base64 string
     *
     * <AUTHOR>
     */
    public static String getRawEncodedBase64(String encodedBase64) {
        if (!ValidationUtils.isNullOrEmpty(encodedBase64) && encodedBase64.contains(",")) {
            return encodedBase64.substring(encodedBase64.indexOf(",") + 1);
        }

        return encodedBase64;
    }

    /**
     * Decode base64 to byte array
     *
     * <AUTHOR>
     */
    public static byte[] getDecodedBase64(String encodedBase64) {
        if (!ValidationUtils.isNullOrEmpty(encodedBase64)) {
            return DatatypeConverter.parseBase64Binary(encodedBase64);
        }

        return null;
    }

    /**
     * Convert encoded base64 string to input-stream
     *
     * <AUTHOR>
     */
    public static InputStream convertBase64ToInputStream(String encodedBase64) {
        if (!ValidationUtils.isNullOrEmpty(encodedBase64)) {
            encodedBase64 = getRawEncodedBase64(encodedBase64);
            byte[] decodedBase64 = getDecodedBase64(encodedBase64);

            return decodedBase64 != null ? new ByteArrayInputStream(decodedBase64) : null;
        }

        return null;
    }

    /**
     * Get file extension from encoded base64 string
     *
     * <AUTHOR>
     */
    public static String getBase64FileExtension(String encodedBase64) {
        if (!ValidationUtils.isNullOrEmpty(encodedBase64)) {
            String mimeType = extractMimeTypeFromEncodedBase64(encodedBase64);
            if (!ValidationUtils.isNullOrEmpty(mimeType)) {
                return mimeType.split("/")[1];
            }
        }

        return "dat";
    }

    /**
     * Get upload folder name
     *
     * <AUTHOR>
     */
    public static String getUploadFolder(String folder, Date date, boolean splitFolderByDate) {
        if (folder == null) {
            folder = "";
        }

        if (splitFolderByDate) {
            String dateFolder = DateTimeUtils.dateToString(date, CommonConstants.DateFormat.YEAR_MONTH_DATE);
            folder += (!ValidationUtils.isNullOrEmpty(folder) ? CommonConstants.PATH_SEPARATOR : "") + dateFolder;
        }

        return folder;
    }

    /**
     * Get upload file name
     *
     * <AUTHOR>
     */
    public static String getUploadFileName(String folder, String fileName, Date date, boolean splitFolderByDate) {
        if (fileName == null) {
            return "";
        }

        String ext = fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".")) : "";
        String fileNameNoExt = fileName.contains(".") ? fileName.substring(0, fileName.lastIndexOf(".")) : fileName;
        fileNameNoExt = fileNameNoExt.replaceAll("[\\[\\]&\\/\\\\#,+()$~%.'\":*?<>{}]", "_");

        return getUploadFolder(folder, date, splitFolderByDate)
                + CommonConstants.PATH_SEPARATOR
                + DateTimeUtils.dateToString(date, CommonConstants.DateFormat.DATE_TIME_NO_SEPARATOR)
                + CommonConstants.PATH_SEPARATOR
                + UUID.randomUUID()
                + CommonConstants.PATH_SEPARATOR
                + fileNameNoExt
                + ext;
    }

    public static String lowerFileNameExtension(String fileName) {
        String ext = fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".")) : "";
        String fileNameNoExt = fileName.contains(".") ? fileName.substring(0, fileName.lastIndexOf(".")) : fileName;
        fileNameNoExt = fileNameNoExt.replaceAll("[\\[\\]&\\/\\\\#,+()$~%.'\":*?<>{}]", "_");
        return fileNameNoExt + ext.toLowerCase();
    }

    public static String convertFileName(String fileName) {
        if (fileName == null) {
            return "";
        }

        String ext = fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".")) : "";
        String fileNameNoExt = fileName.contains(".") ? fileName.substring(0, fileName.lastIndexOf(".")) : fileName;
        fileNameNoExt = fileNameNoExt.replaceAll("[\\[\\]&\\/\\\\#,+()$~%.'\":*?<>{}]", "_");

        return fileNameNoExt + ext;
    }
    public static File convertMuiltipartToFile(MultipartFile multipartFile) throws IOException {
        File file = new File(multipartFile.getOriginalFilename());
        FileOutputStream fileOutputStream = new FileOutputStream(file);
        fileOutputStream.write(multipartFile.getBytes());
        fileOutputStream.close();
        return file;
    }
    public static String getCellValue(Cell cell, FormulaEvaluator evaluator) {
        String cellValue;
        if (Objects.requireNonNull(cell.getCellType()) == CellType.FORMULA) {// Nếu là công thức, sử dụng evaluator để tính giá trị
            CellValue cellValueFml = evaluator.evaluate(cell);
            switch (cellValueFml.getCellType()) {
                case NUMERIC:
                    cellValue = String.valueOf(cellValueFml.getNumberValue());
                    break;
                case STRING:
                    cellValue = cellValueFml.getStringValue();
                    break;
                default:
                    return "Error in formula!";
            }
        } else {
            DataFormatter dataFormatter = new DataFormatter();
            cellValue = dataFormatter.formatCellValue(cell).trim();
        }

        return cellValue;
    }

    public static String getCellAsString(Cell cell) {

        DataFormatter dataFormatter = new DataFormatter();

        return dataFormatter.formatCellValue(cell).trim();
    }

    public static Workbook writeToExcel(List<Map<String, Object>> data) {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Data");

        // Tạo dòng tiêu đề
        Row headerRow = sheet.createRow(0);
        int columnIndex = 0;
        for (String key : data.get(0).keySet()) {
            Cell cell = headerRow.createCell(columnIndex++);
            cell.setCellValue(key);
        }

        // Thêm dữ liệu vào sheet
        int rowIndex = 1;
        for (Map<String, Object> rowData : data) {
            Row row = sheet.createRow(rowIndex++);
            columnIndex = 0;
            for (Object value : rowData.values()) {
                Cell cell = row.createCell(columnIndex++);
                if (value instanceof String) {
                    cell.setCellValue((String) value);
                } else if (value instanceof Number) {
                    cell.setCellValue(((Number) value).doubleValue());
                } else if (value instanceof Boolean) {
                    cell.setCellValue((Boolean) value);
                } else {
                    cell.setCellValue(value.toString());
                }
            }
        }

        // Tự động điều chỉnh độ rộng cột
        for (int i = 0; i < headerRow.getPhysicalNumberOfCells(); i++) {
            sheet.autoSizeColumn(i);
        }

        return workbook;
    }


}
