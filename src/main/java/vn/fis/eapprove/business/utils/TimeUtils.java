package vn.fis.eapprove.business.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

@Slf4j
public class TimeUtils {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat();

    public static final String FORMAT_DATE_DD_MM_YYYY = "yyyy-MM-dd HH:mm:ss.S";


    public static String convertToTime(long diff) {
        Duration d = Duration.ofMillis(diff);
        long days = d.toDaysPart();
        long hours = d.toHoursPart();
        long minutes = d.toMinutesPart();
        long seconds = d.toSecondsPart();
        StringBuilder sb = new StringBuilder();
        String day = StringUtils.leftPad(String.valueOf(days), 2, "0");
        String hour = StringUtils.leftPad(String.valueOf(hours), 2, "0");
        String minute = StringUtils.leftPad(String.valueOf(minutes), 2, "0");
        String second = StringUtils.leftPad(String.valueOf(seconds), 2, "0");
        sb.append(day).append("d").append(hour).append("h").append(minute).append("m").append(second).append("s");
        return sb.toString();
    }

    public static Date stringToDate(String dateString, String strFormat) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(strFormat);
            return simpleDateFormat.parse(dateString);
        } catch (ParseException e) {
            return null;
        }
    }

    public static LocalDateTime stringToDateTime(String dateString, String strFormat) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(strFormat);
            return LocalDateTime.parse(dateString, formatter);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public static Date plusDay(Date date, long day) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE, 1);
        return c.getTime();
    }

    /**
     * Convert date to format string
     *
     * <AUTHOR>
     */
    public static String dateToString(Date date, String format) {
        if (date != null && !ValidationUtils.isNullOrEmpty(format)) {
            try {
                DATE_FORMAT.applyPattern(format);
                return DATE_FORMAT.format(date);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }

        return "";
    }

    public static String localDateTimeToString(LocalDateTime dateTime, String format) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            return dateTime.format(formatter);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return "";
    }

    public static LocalDateTime stringToLocalDateTime(String str, String format) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            return LocalDateTime.parse(str, formatter);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return null;
    }

    public static String localDateToString(LocalDate date, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return date.format(formatter);
    }

    public static boolean checkMonth(LocalDateTime ldt) {
        if (ldt.getMonth().getValue() == 1 || ldt.getMonth().getValue() == 4 || ldt.getMonth().getValue() == 7 || ldt.getMonth().getValue() == 10) {
            if (ldt.getDayOfMonth() == 1) {
                return true;
            }
        }
        return false;
    }
}
