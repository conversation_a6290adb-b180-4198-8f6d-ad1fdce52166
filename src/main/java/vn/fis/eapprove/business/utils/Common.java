package vn.fis.eapprove.business.utils;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.modelmapper.ModelMapper;
import org.mvel2.MVEL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import vn.fis.eapprove.business.producer.PushNotificationProducer;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.SproProperties;
import vn.fis.spro.common.util.ObjectUtils;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import static vn.fis.eapprove.business.constant.Constant.NOTI_CONTEXTPATH;

@Component
@Slf4j
@SuppressWarnings({"unchecked"})
public class Common {

    @Autowired
    private RedirectApiUtils redirectApiUtils;

    @Autowired
    private CredentialHelper credentialHelper;

    @Autowired
    private ModelMapper modelMapper;

    @Autowired
    private PushNotificationProducer pushNotificationProducer;

    @Autowired
    private SproProperties sproProperties;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MessageSource messageSource;

    public Integer getPageCount(Long totalItems, Integer limit) {
        if (totalItems > 0 && totalItems % limit == 0) {
            return Math.toIntExact(totalItems / limit);
        } else if (totalItems > 0 && totalItems % limit > 0) {
            return Math.toIntExact((totalItems / limit) + 1);
        } else if (totalItems < limit) {
            return 1;
        }
        return 0;
    }

    public Boolean notification(String KEY_NAME, List<String> lsEmail, String token) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            String uri = NOTI_CONTEXTPATH + "/notification/notification";
            JSONObject object = new JSONObject();
            object.put("KEY_NAME", KEY_NAME);
            object.put("lsEmail", lsEmail);
            HttpEntity<?> entity = redirectApiUtils.RedirectFormAPIs( object, token);
            ResponseEntity<Object> response = restTemplate.postForEntity(uri, entity, Object.class);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    private static <T> T mapToEntity(Map<String, Object> map, Class<T> clazz) {
        try {
            T entity = clazz.getDeclaredConstructor().newInstance();
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                if (map.containsKey(field.getName())) {
                    field.set(entity, map.get(field.getName()));
                }
            }
            return entity;
        } catch (Exception e) {
            throw new RuntimeException("Lỗi khi ánh xạ Map sang Entity", e);
        }
    }

    public static <T> List<T> convertListToListEntity(List<Map<String, Object>> dataList, Class<T> clazz) {
        return dataList.stream()
                .map(map -> mapToEntity(map, clazz))
                .collect(Collectors.toList());
    }

//    public Boolean pushNotification(String keyRole, HashMap<String, String> mapping, List<String> mailTo) {
//
//        try {
//            RestTemplate restTemplate = new RestTemplate();
//
//            Matcher matcher;
//            Matcher matcherSubject;
//
//            FeatureActionDetailResponse response = pushNotificationManager.getContentAction(keyRole);
//
//            Pattern pattern = Pattern.compile("\\$\\{([a-zA-Z0-9]+)\\}", Pattern.CASE_INSENSITIVE);
//            String contents = response.getMailContent();
//            String subject = response.getMailSubject();
//            matcher = pattern.matcher(contents);
//            matcherSubject = pattern.matcher(subject);
//
//            // replace content mail
//            while (matcher.find()) {
//                contents = contents.replace(matcher.group(0), mapping.get(matcher.group(1)));
//            }
//            // replace subject mail
//            while (matcherSubject.find()) {
//                subject = subject.replace(matcherSubject.group(0), mapping.get(matcherSubject.group(1)));
//            }
//            response.setMailContent(contents);
//            response.setMailSubject(subject);
//
//            // send mail and push notification
//            if (response.getSendNotification()) {
//                Notification notification = modelMapper.map(response, Notification.class);
//                notification.setEmailTo(mailTo);
////                notification.setRealm(credentialHelper.getRealm());
//                notification.setToken(credentialHelper.getJWTToken());
//
//                pushNotificationProducer.pushNotification(notification);
//            }
//
//            // save action history
//            if (response.getSaveHistory()) {
//                JSONObject jsonObject = new JSONObject();
//                jsonObject.put("featureId", response.getFeatureId());
//                jsonObject.put("actionId", response.getActionId());
//                jsonObject.put("keyRole", response.getKeyRole());
//                jsonObject.put("mailSubject", response.getMailSubject());
//                jsonObject.put("mailContent", response.getMailContent());
//                jsonObject.put("iconNotification", response.getIconNotification());
//                jsonObject.put("webNotification", response.getWebNotification());
//                jsonObject.put("mobileNotification", response.getMobileNotification());
//                jsonObject.put("emailTo", mailTo);
//                ActionHistoryRequest req = modelMapper.map(response, ActionHistoryRequest.class);
//                req.setEmailTo(mailTo);
//
//                String url = sproProperties.getServiceUrls().get(MapKeyEnum.SUBSCRIPTION.key) + "/actHistory/create";
//                HttpEntity<?> entity = redirectApiUtils.RedirectFormAPIs( req, credentialHelper.getJWTToken());
//                restTemplate.postForObject(url, entity, Object.class);
//            }
//
//            return true;
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return false;
//        }
//    }

    /**
     * Check string is JSON or not
     *
     * @Author: PhucVM3
     */
    public boolean isJson(String json) {
        try {
            objectMapper.readTree(json);
        } catch (JacksonException ex) {
            return false;
        }

        return true;
    }

    /**
     * Convert JSON array to string with splitter
     *
     * @Author: PhucVM3
     */
    public String jsonArrayToString(String jsonArray) {
        return ObjectUtils.jsonArrayToString(jsonArray, ",");
    }

    /**
     * Get message from properties file
     *
     * @Author: PhucVM3
     */
    public String getMessage(String prop) {
        return getMessage(prop, null);
    }

    public String getMessage(String prop, Object[] args) {
        try {
            return messageSource.getMessage(prop, args, Locale.getDefault());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return "";
    }


    /**
     * Parse MVEL expression<br/>
     * More detail <a href="http://mvel.documentnode.com/">http://mvel.documentnode.com</a>
     *
     * <AUTHOR>
     */
    public <T> T evalExpressionByMVEL(String expression, Object context, Class<T> clazz) {
        try {
            return MVEL.eval(expression, context, clazz);
        } catch (Exception ex) {
            return null;
        }
    }

    public static String getFirstValue(String input) {
        // Tìm vị trí của dấu gạch trong chuỗi
        int dashIndex = input.indexOf("-");

        if (dashIndex != -1) {
            // Lấy giá trị trước dấu gạch bằng cách sử dụng substring
            String firstValue = input.substring(0, dashIndex).trim();
            return firstValue;
        } else {
            // Trường hợp không tìm thấy dấu gạch, trả về chuỗi ban đầu
            return input;
        }
    }
}
