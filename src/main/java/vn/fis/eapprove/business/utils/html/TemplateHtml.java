package vn.fis.eapprove.business.utils.html;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrintConfigUser;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTpSignZone;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTemplatePrintConfigUserRepository;
import vn.fis.eapprove.business.model.request.VariableValueDto;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TemplateHtml {

    @Value("${app.s3.bucket}")
    private String bucket;
    @Value("${spro.storage.url}")
    private String storageUrl;
    @Autowired
    private BpmTemplatePrintConfigUserRepository bpmTemplatePrintConfigUserRepository;

    public String handleData(Map<String, VariableValueDto> mVariable, String templateString, List<BpmTpSignZone> bpmTpSignZoneList) throws Exception {
        if (templateString.contains("&quot;")) {
            templateString = templateString.replaceAll("&quot;", "\"");
        }
        List<Map<String, Map<String, Object>>> lstConfigs = getAllParamsConfig(templateString);
        List<Map<String, Object>> lstConfigImages = new ArrayList<>();
        List<Map<String, Object>> lstConfigTextNumber = new ArrayList<>();
        List<Map<String, Object>> lstConfigTable = new ArrayList<>();
        List<Map<String, Object>> lstConfigSign = new ArrayList<>();
        List<Map<String, Object>> lstConfigTableSign = new ArrayList<>();
        Map<String, String> mConfigDelete = new HashMap<>();
        List<BpmTemplatePrintConfigUser> lstConfigUser = new ArrayList<>();
        if (lstConfigs != null && !lstConfigs.isEmpty()) {
            //Lọc cụ thể từng loại
            lstConfigs.forEach(itemConfig -> {
                Map<String, Object> objectConfig = itemConfig.get("objectConfig");
                String type = StringUtil.nvl(objectConfig.get("type"), "").trim().toUpperCase();
                switch (type) {
                    case "TEXT", "NUMBER" -> lstConfigTextNumber.add(objectConfig);
                    case "TABLE" -> {
                        mConfigDelete.put(StringUtil.nvl(objectConfig.get("keyReplace"), ""), "");
                        lstConfigTable.add(objectConfig);
                    }
                    case "TABLE_SIGN" -> {
                        mConfigDelete.put(StringUtil.nvl(objectConfig.get("keyReplace"), ""), "");
                        lstConfigTableSign.add(objectConfig);
                    }
                    case "IMAGE" -> lstConfigImages.add(objectConfig);
                    case "" -> { //type rỗng là cấu hình kí
                        mConfigDelete.put(StringUtil.nvl(objectConfig.get("keyReplace"), ""), "");
                        lstConfigSign.add(objectConfig);
                    }
                }
            });
        }
        System.out.println("lstConfigImages =" + lstConfigImages);
        System.out.println("lstConfigTextNumber =" + lstConfigTextNumber);
        System.out.println("lstConfigTable =" + lstConfigTable);
        System.out.println("lstConfigSign =" + lstConfigSign);
        System.out.println("lstConfigTableSign =" + lstConfigTableSign);
        System.out.println("mConfigDelete =" + mConfigDelete);
        Map<String, String> mapReplace = new HashMap<>();

        // replace image
        if (!lstConfigImages.isEmpty()) {
            mapReplace = handleImageNormal(lstConfigImages, mVariable, mapReplace);
        }

        // lấy config user - bpm_template_print_config_user
        if (!lstConfigSign.isEmpty() && !ValidationUtils.isNullOrEmpty(bpmTpSignZoneList)) {
            lstConfigUser = bpmTemplatePrintConfigUserRepository.getBpmTemplatePrintConfigUsersByUsernameIn(
                    bpmTpSignZoneList.stream().map(BpmTpSignZone::getEmail).filter(Objects::nonNull).collect(Collectors.toList())
            );
        }

        if (!lstConfigSign.isEmpty()) {
            handleSignImageNormal(lstConfigSign, bpmTpSignZoneList, lstConfigUser, mapReplace);
        }

        if (!lstConfigTableSign.isEmpty()) {
            handleTableSign(lstConfigTableSign, bpmTpSignZoneList, lstConfigUser, mapReplace, templateString);
        }

        if (!lstConfigTable.isEmpty()) {
            handleTable(mVariable, lstConfigTable, mapReplace, templateString);
        }

        if (!lstConfigTextNumber.isEmpty()) {
            handleTextOrNumber(mVariable, lstConfigTextNumber, mapReplace, templateString);
        }

        // Ưu tiên ngày giờ tạo phiếu
        if (!ValidationUtils.isNullOrEmpty(mVariable.get("dtm_thoiGianTaoPhieu")) && !ValidationUtils.isNullOrEmpty(mVariable.get("dtm_thoiGianTaoPhieu").getValue())) {
            String currentDateTime = mVariable.get("dtm_thoiGianTaoPhieu").getValue().toString();
            String[] currentDate = currentDateTime.split(" ");
            if (currentDate.length > 0) {
                String[] arrDate = currentDate[0].split("/");
                if (arrDate.length >= 3) {
                    mapReplace.put("$day", arrDate[0]);
                    mapReplace.put("$month", arrDate[1]);
                    mapReplace.put("$year", arrDate[2]);
                }
            }
        } else {
            //Set mặc định ngày tháng năm
            String currentDate = StringUtil.format(new Date(), "dd/MM/yyyy");
            String[] arrDate = currentDate.split("/");
            if (arrDate.length >= 3) {
                mapReplace.put("$day", arrDate[0]);
                mapReplace.put("$month", arrDate[1]);
                mapReplace.put("$year", arrDate[2]);
            }
        }

        // Danh sách chứa các entry bắt đầu với <table>: tránh trường hợp biến động trong table bị replace trước
        Map<String, String> tableEntries = new HashMap<>();
        // Danh sách chứa các entry còn lại
        Map<String, String> otherEntries = new HashMap<>();

        // Tách map thành 2 nhóm
        for (Map.Entry<String, String> entry : mapReplace.entrySet()) {
            if (entry.getKey().startsWith("<table")) {
                tableEntries.put(entry.getKey(), entry.getValue());
            } else {
                otherEntries.put(entry.getKey(), entry.getValue());
            }
        }
        if (!ValidationUtils.isNullOrEmpty(tableEntries)) {
            templateString = replaceWithMap(templateString, tableEntries);
        }
        if (!ValidationUtils.isNullOrEmpty(otherEntries)) {
            templateString = replaceWithMap(templateString, otherEntries);
        }

        // xoá các cấu hình chưa replace
        String regex = "[@$]\\{\\s*(.*?)\\s*\\}";
        Pattern pattern = Pattern.compile(regex);
        Matcher m = pattern.matcher(templateString);
        List<String> removeConfig = new ArrayList<>();
        while (m.find()) {
            removeConfig.add(m.group());
        }
        for (String item : removeConfig) {
            templateString = templateString.replace(item, "");
        }

        return templateString;
    }

    public static String replaceWithMap(String templateString, Map<String, String> mapReplace) {
        for (Map.Entry<String, String> entry : mapReplace.entrySet()) {
            String key = Pattern.quote(entry.getKey());
            String value = entry.getValue();
            templateString = templateString.replaceAll(key, value);
        }
        return templateString;
    }


    private Map<String, String> handleImageNormal(List<Map<String, Object>> lstConfigImage, Map<String, VariableValueDto> mVariable, Map<String, String> mapReplace) {
        if (lstConfigImage != null && !lstConfigImage.isEmpty()) {
            lstConfigImage.forEach(itemConfigImage -> {
                String imageUrl = StringUtil.nvl(itemConfigImage.get("imageUrl"), "");
                Integer targetWidth = getIntValueFromMap(itemConfigImage, "targetWidth", 150);
                Integer targetHeight = getIntValueFromMap(itemConfigImage, "targetHeight", null);
                String keyReplace = StringUtil.nvl(itemConfigImage.get("keyReplace"), "");
                // link ngoài hệ thống
                boolean isExternalUrl = Boolean.parseBoolean(StringUtil.nvl(itemConfigImage.get("isExternalUrl"), "false"));

                // biến động
                if (imageUrl.startsWith("@")) {
                    imageUrl = (String) findObjectInMapVariable(mVariable, imageUrl);
                }
                if (!isExternalUrl) {
                    imageUrl = storageUrl + "/" + bucket + "/" + imageUrl;
                }
                if (!ValidationUtils.isNullOrEmpty(imageUrl)) {
                    StringBuilder sb = new StringBuilder();
                    sb.append("<img src=\"");
                    sb.append(imageUrl);
                    sb.append("\"");
                    sb.append(" alt=\"");
                    sb.append(keyReplace);
                    sb.append("\"");
                    sb.append(" width=\"");
                    sb.append(targetWidth);
                    sb.append("\"");
                    if (targetHeight != null) {
                        sb.append(" height=\"");
                        sb.append(targetHeight);
                        sb.append("\"");
                    }
                    sb.append(" />");

                    mapReplace.put(keyReplace, sb.toString());
                } else {
                    mapReplace.put(keyReplace, "");
                }
            });
        }
        return mapReplace;
    }

    private void handleSignImageNormal(List<Map<String, Object>> lstConfigSign, List<BpmTpSignZone> lstTpSignZone, List<BpmTemplatePrintConfigUser> lstConfigUser, Map<String, String> mapReplace) {
        if (lstTpSignZone != null && !lstTpSignZone.isEmpty()) {
            Map<String, Integer> savePosition = new HashMap<>();
            lstTpSignZone.forEach(itemTpSignZone -> {
                if (itemTpSignZone.getSign() != null) {
                    Map<String, Object> itemConfigImage = null;
                    List<Map<String, Object>> lstItemConfigImage = lstConfigSign.stream().filter(config -> StringUtil.nvl(config.get("imageUrl"), "").endsWith("@" + itemTpSignZone.getTaskDefKey())).toList();
                    //Lấy lần lượt cấu hình chữ kí, nếu có nhiều vị trí sẽ insert lần lượt
                    if (!lstItemConfigImage.isEmpty()) {
                        int currentPosition = savePosition.getOrDefault(itemTpSignZone.getTaskDefKey(), -1);
                        int nextPosition;
                        if (lstItemConfigImage.size() == 1) {
                            nextPosition = 0;
                        } else if ((currentPosition + 1) < lstItemConfigImage.size()) {
                            nextPosition = currentPosition + 1;
                        } else {
                            nextPosition = 0;
                        }
                        savePosition.put(itemTpSignZone.getTaskDefKey(), nextPosition);
                        itemConfigImage = lstItemConfigImage.get(nextPosition);
                    }

                    if (itemConfigImage != null) {//Tìm thấy cấu hình
                        String keyReplace = StringUtil.nvl(itemConfigImage.get("keyReplace"), "");

                        // replace image
                        String sb = replaceSignImage(itemConfigImage, itemTpSignZone, lstConfigUser);

                        if (mapReplace.get(keyReplace) != null) {
                            mapReplace.put(keyReplace, mapReplace.get(keyReplace) + sb);
                        } else {
                            mapReplace.put(keyReplace, sb);
                        }
                    }
                }
            });
        }
    }

    private void handleTableSign(List<Map<String, Object>> lstConfigTableSign,
                                 List<BpmTpSignZone> lstTpSignZone,
                                 List<BpmTemplatePrintConfigUser> lstConfigUser,
                                 Map<String, String> mapReplace,
                                 String templateString
    ) {
        if (lstTpSignZone != null) {
            for (Map<String, Object> configTableSign : lstConfigTableSign) {
                String tableName = StringUtil.nvl(configTableSign.get("tableName"), "");
                String keyReplace = StringUtil.nvl(configTableSign.get("keyReplace"), "");
                String tableContent = extractTableContent(templateString, keyReplace);

                List<BpmTpSignZone> tpSignZones = lstTpSignZone.stream()
                        .filter(itemTpSignZone -> tableName.endsWith(itemTpSignZone.getTaskDefKey()) && !ValidationUtils.isNullOrEmpty(itemTpSignZone.getSign()))
                        .toList();
                if (!ValidationUtils.isNullOrEmpty(tpSignZones) && !ValidationUtils.isNullOrEmpty(tableContent)) {
                    String tableBodyTemplate = "";

                    // Tìm <tbody>
                    int tbodyStartIndex = tableContent.indexOf("<tbody>");
                    int tbodyEndIndex = tableContent.indexOf("</tbody>") + "</tbody>".length();
                    if (tbodyStartIndex != -1) {
                        tableBodyTemplate = tableContent.substring(tbodyStartIndex + "<tbody>".length(), tbodyEndIndex - "</tbody>".length());
                    }

                    if (!tableBodyTemplate.isEmpty()) {

                        StringBuilder newTableBody = new StringBuilder();
                        newTableBody.append("<tbody>");
                        for (int i = 0; i < tpSignZones.size(); i++) {
                            // mỗi 1 sign zone tạo 1 row mới
                            BpmTpSignZone tpSignZone = tpSignZones.get(i);
                            String newRow = tableBodyTemplate;

                            if (tableBodyTemplate.contains("${index}")) {
                                newRow = newRow.replace("${index}", String.valueOf(i + 1));
                            }

                            String regexImg = "\\$\\{imageUrl:([^}]+)}";
                            Pattern pattern = Pattern.compile(regexImg);
                            Matcher matcher = pattern.matcher(tableBodyTemplate);
                            if (matcher.find()) {
                                String strConfig = matcher.group(0);
                                Map<String, Object> itemConfigImage = getMapObjectFromJson(strConfig.replaceAll("\\$", ""));

                                // replace sign image
                                String imageReplace = replaceSignImage(itemConfigImage, tpSignZone, lstConfigUser);

                                newRow = newRow.replace(strConfig, imageReplace);
                            }

                            if (tableBodyTemplate.contains("${comment}")) {
                                newRow = newRow.replace("${comment}", StringUtil.nvl(tpSignZone.getComment(), ""));
                            }

                            newTableBody.append(newRow);
                        }
                        newTableBody.append("</tbody>");

                        // replace body table
                        String newTableContent = tableContent.replace(tableBodyTemplate, newTableBody.toString());
                        mapReplace.put(tableContent, newTableContent);
                    }
                }
                // xoá config table
                mapReplace.put(keyReplace, "");
            }
        }
    }

    public void handleTable(Map<String, VariableValueDto> mVariable,
                             List<Map<String, Object>> lstConfigTable,
                             Map<String, String> mapReplace,
                             String templateString
    ) {
        for (Map<String, Object> configTable : lstConfigTable) {
            String tableName = StringUtil.nvl(configTable.get("tableName"), "");
            String keyReplace = StringUtil.nvl(configTable.get("keyReplace"), "");
            String value = StringUtil.nvl(configTable.get("value"), "");
            String dynamicTitle = StringUtil.nvl(configTable.get("dynamicTitle"), "false");

            Object newValue = findObjectInMapVariable(mVariable, value);
            Map<String, Object> objectMap = getMapObjectFromJson((String) newValue);
            Map<String, Object> data = null;
            Map<String, String> mHeader = new HashMap<>();
            List<Map<String, String>> lstRowData = new ArrayList<>();

            String tableContent = extractTableContent(templateString, keyReplace);
            if (objectMap != null && !ValidationUtils.isNullOrEmpty(tableContent)) {
                // Trường hợp replace table từ api các phân hệ
                if (objectMap.get("data") instanceof ArrayList) {
                    data = objectMap;
                } else {
                    data = (Map<String, Object>) objectMap.get("data");
                }
                if (data != null) {
                    List<Map<String, String>> lstData = (List<Map<String, String>>) data.get("data");
                    if (!ValidationUtils.isNullOrEmpty(lstData)) {
                        if (dynamicTitle.equalsIgnoreCase("true")) {
                            mHeader = lstData.get(0);
                            lstRowData = lstData.subList(1, lstData.size());
                        } else {
                            lstRowData = lstData;
                        }
                    }
                }
                //Xử lý Cấu hình đặc biệt với từng cell data @{type:"TABLE", tableIndex:4, value:"@test_table", dynamicTitle:false, rowConfigs:[(value: "txt_text2", format:"#,###,###,###", numberToWord:true)]}
                if (configTable.containsKey("rowConfigs")) {
                    List<Map<String, Object>> rowConfigs = (List<Map<String, Object>>) configTable.get("rowConfigs");
                    if (!lstRowData.isEmpty() && rowConfigs != null && !rowConfigs.isEmpty()) {
                        lstRowData.forEach(rowDataItem -> {
                            Set<String> lstProperties = rowDataItem.keySet();
                            for (String key : lstProperties) {
                                //Tìm xem column có cấu hình không
                                Map<String, Object> rowConfigMap = rowConfigs.stream().filter(rowConfis -> StringUtil.nvl(rowConfis.get("value"), "").equals(key)).findAny().orElse(null);
                                if (rowConfigMap != null) {//Nếu có cấu hình thử format lại
                                    String newConfigValue = getFormatNumberFromMap(rowConfigMap, StringUtil.nvl(rowDataItem.get(key), "0"), "@{deletedValue}");
                                    if (newConfigValue != null) {
                                        rowDataItem.put(key, newConfigValue);
                                    }
                                }
                            }
                        });
                    }
                }

                String tableHeadTemplate = "";
                String tableBodyTemplate = "";
                // <thead>
                int theadStartIndex = tableContent.indexOf("<thead>");
                int theadEndIndex = tableContent.indexOf("</thead>") + "</thead>".length();
                if (theadStartIndex != -1) {
                    tableHeadTemplate = tableContent.substring(theadStartIndex + "<thead>".length(), theadEndIndex - "</thead>".length());
                }
                // replace header
                String newHeader = tableHeadTemplate;
                if (!tableHeadTemplate.isEmpty() && !ValidationUtils.isNullOrEmpty(mHeader)) {
                    Set<String> lstProperties = mHeader.keySet();
                    for (String key : lstProperties) {
                        if (key != null) {
                            newHeader = newHeader.replaceAll("\\$\\{" + key + "\\}", StringUtil.nvl(mHeader.get(key), ""));
                        }
                    }
                }

                // <tbody>
                int tbodyStartIndex = tableContent.indexOf("<tbody>");
                int tbodyEndIndex = tableContent.indexOf("</tbody>") + "</tbody>".length();
                if (tbodyStartIndex != -1) {
                    tableBodyTemplate = tableContent.substring(tbodyStartIndex + "<tbody>".length(), tbodyEndIndex - "</tbody>".length());
                }
                // replace body
                StringBuilder newTableBody = new StringBuilder();
                if (!tableBodyTemplate.isEmpty()) {

                    newTableBody.append("<tbody>");
                    for (int i = 0; i < lstRowData.size(); i++) {
                        Map<String, String> rowData = lstRowData.get(i);
                        String newRow = tableBodyTemplate;

                        if (tableBodyTemplate.contains("${index}")) {
                            newRow = newRow.replace("${index}", String.valueOf(i + 1));
                        }

                        Set<String> lstProperties = rowData.keySet();
                        for (String key : lstProperties) {
                            if (key != null) {
                                newRow = newRow.replaceAll("\\$\\{" + key + "\\}", StringUtil.nvl(rowData.get(key), ""));
                            }
                        }

                        newTableBody.append(newRow);
                    }
                    newTableBody.append("</tbody>");
                }

                // footer hiện chỉ cấu hình replace biến ngoài table -> chưa xử lý
                // replace header, body
                String newTableContent = tableContent.replace(tableHeadTemplate, newHeader).replace(tableBodyTemplate, newTableBody.toString());
                mapReplace.put(tableContent, newTableContent);
            }
            // xoá config table
            mapReplace.put(keyReplace, "");
        }
    }

    public void handleTextOrNumber(Map<String, VariableValueDto> mVariable,
                                   List<Map<String, Object>> lstConfigTextNumber,
                                   Map<String, String> mapReplace,
                                   String templateString
    ) {
        for (Map<String, Object> configTextNumber : lstConfigTextNumber) {
            String keyReplace = StringUtil.nvl(configTextNumber.get("keyReplace"), "");
            String value = StringUtil.nvl(configTextNumber.get("value"), "");
            String type = StringUtil.nvl(configTextNumber.get("type"), "");

            Object newValue = findObjectInMapVariable(mVariable, value);
            if (type.equalsIgnoreCase("NUMBER")) {
                String newValueStr = StringUtil.nvl(newValue, "");
                mapReplace.put(keyReplace, getFormatNumberFromMap(configTextNumber, newValueStr, ""));
            } else {
                String newValueStr = StringUtil.nvl(newValue, "");
                mapReplace.put(keyReplace, newValueStr);
            }
        }
    }

    public String replaceSignImage(Map<String, Object> itemConfigImage, BpmTpSignZone itemTpSignZone, List<BpmTemplatePrintConfigUser> lstConfigUser) {
        Integer targetWidth = getIntValueFromMap(itemConfigImage, "targetWidth", 150);
        Integer targetHeight = getIntValueFromMap(itemConfigImage, "targetHeight", 150);
        String imageUrl = StringUtil.nvl(itemTpSignZone.getSign(), "");
        String signatoryName = StringUtil.nvl(itemConfigImage.get("signatoryName"), "");
        String position = StringUtil.nvl(itemConfigImage.get("position"), "");
        String orgAssigneeTitle = StringUtil.nvl(itemConfigImage.get("orgAssigneeTitle"), "");
        String signDate = StringUtil.nvl(itemConfigImage.get("signDate"), "");
        String isExternalUrl = StringUtil.nvl(itemConfigImage.get("isExternalUrl"), "false");
        String addPosition = StringUtil.nvl(itemConfigImage.get("addPosition"), "true");
        String addSignDate = StringUtil.nvl(itemConfigImage.get("addSignDate"), "true");
        String type = StringUtil.nvl(itemConfigImage.get("type"), "").trim().toUpperCase();
        String addSignImage = StringUtil.nvl(itemConfigImage.get("addSignImage"), "true");
        String addSignatoryName = StringUtil.nvl(itemConfigImage.get("addSignatoryName"), "true");
        String addSignAction = StringUtil.nvl(itemConfigImage.get("addSignAction"), "true");
        if (type.isEmpty()) {
            if (signatoryName.isEmpty()) {//lấy tên
                signatoryName = StringUtil.nvl(itemTpSignZone.getLastName(), "") + " " + StringUtil.nvl(itemTpSignZone.getFirstName(), "");
            }

            if (position.isEmpty()) {//lấy chức vụ
                position = StringUtil.nvl(itemTpSignZone.getPosition(), "");
            }

            if (!ValidationUtils.isNullOrEmpty(itemTpSignZone.getOrgAssigneeTitle())) { //lấy chức vụ orgAssignee
                orgAssigneeTitle = "TUQ " + StringUtil.nvl(itemTpSignZone.getOrgAssigneeTitle(), "");
            }

            if (signDate.isEmpty()) {//lấy ngày kí
                signDate = StringUtil.nvl(StringUtil.format(itemTpSignZone.getSignedDate(), "dd/MM/yyyy HH:mm"), "");
            }
        }

        // check theo bpm_template_print_config_user
        if (!ValidationUtils.isNullOrEmpty(lstConfigUser)) {
            BpmTemplatePrintConfigUser configUser = lstConfigUser.stream().filter(e -> e.getUsername().equalsIgnoreCase(itemTpSignZone.getEmail())).findFirst().orElse(null);
            if (configUser != null) {
                addPosition = configUser.getAddTitle();
                addSignDate = configUser.getAddSignDate();
                addSignImage = configUser.getAddSignImage();
                addSignatoryName = configUser.getAddName();
                addSignAction = configUser.getAddSignAction();
            }
        }

        if (addPosition != null && addPosition.equalsIgnoreCase("FALSE")) {
            orgAssigneeTitle = "";
            position = "";
        }
        if (addSignDate != null && addSignDate.equalsIgnoreCase("FALSE")) {
            signDate = "";
        }
        String signAction = "";
        if (addSignAction != null && addSignAction.equalsIgnoreCase("true") && itemTpSignZone.getChartNodeLevel() != null) {
            signAction = switch (itemTpSignZone.getChartNodeLevel()) {
                case "0" -> "Đệ trình";
                case "1", "2", "3" -> "Phê duyệt";
                default -> "Xét duyệt";
            };
        }

        if (isExternalUrl.equalsIgnoreCase("false")) {
            imageUrl = storageUrl + "/" + bucket + "/" + imageUrl;
        }

        if (addSignImage != null && addSignImage.equalsIgnoreCase("false")) {
            imageUrl = storageUrl + "/" + bucket + "/" + "user_signature/default/default.png";
        }

        StringBuilder sb = new StringBuilder();
//        sb.append("<div style=\"text-align: center; width: ");
//        sb.append(targetWidth); // Gắn chiều rộng từ targetWidth
//        sb.append("px;\">");

        if (!orgAssigneeTitle.isEmpty()) {
            sb.append(orgAssigneeTitle);
            sb.append("<br>");
        }
        if (!position.isEmpty()) {
            sb.append(position);
            sb.append("<br>");
        }
        if (!signAction.isEmpty()) {
            sb.append(signAction);
            sb.append("<br>");
        }
        // signature
        sb.append("<img src=\"");
        sb.append(imageUrl);
        sb.append("\"");
        sb.append(" alt=\"");
        sb.append("\"");
        sb.append(" width=\"");
        sb.append(targetWidth);
        sb.append("\"");
        if (targetHeight != null) {
            sb.append(" height=\"");
            sb.append(targetHeight);
            sb.append("\"");
        }
        sb.append(" />");
        sb.append("<br>");

        if (addSignatoryName != null && addSignatoryName.equalsIgnoreCase("false")) {
            sb.append("<br>");
        } else if (!signatoryName.isEmpty()) {
            sb.append(signatoryName);
            sb.append("<br>");
        }
        if (!signDate.isEmpty()) {
            sb.append(signDate);
        }
//        sb.append("</div>");

        return sb.toString();
    }

    private String getFormatNumberFromMap(Map<String, Object> itemValue, String valueField, String defaultValue) {
        var ref = new Object() {
            String value = StringUtil.nvl(valueField, "");
        };

        try {
            if (ref.value != null) {
                //format số theo định dạng
                if (itemValue.containsKey("format")) {
                    String strPattern = StringUtil.nvl(itemValue.get("format"), "");
                    if (!strPattern.isEmpty()) {
                        try {
                            double dbValue = Double.parseDouble(StringUtil.nvl(ref.value, defaultValue));
                            if (strPattern.contains(".")) {
                                ref.value = StringUtil.format(dbValue, strPattern);
                            } else {
                                ref.value = StringUtil.format(Math.round(dbValue), strPattern);
                            }
                        } catch (Exception e) {
                            ref.value = defaultValue;
                        }
                    }
                }
            } else {
                ref.value = defaultValue;
            }
        } catch (Exception e) {
            ref.value = defaultValue;
        }

        return ref.value;
    }

    public static String extractTableContent(String templateString, String marker) {
        String startMarker = marker + "</p>";
        int markerIndex = templateString.indexOf(startMarker);

        if (markerIndex == -1) {
            return null; // Nếu không tìm thấy marker
        }

        // Tìm vị trí bắt đầu của <table>
        int tableStartIndex = templateString.indexOf("<table", markerIndex);
        if (tableStartIndex == -1) {
            return null;
        }

        // Tìm vị trí kết thúc của bảng (</table>)
        int tableEndIndex = templateString.indexOf("</table>", tableStartIndex);
        if (tableEndIndex == -1) {
            return null;
        }

        // Lấy nội dung bảng (bao gồm cả <table>...</table>)
        tableEndIndex += "</table>".length(); // Bao gồm cả thẻ </table>
        return templateString.substring(tableStartIndex, tableEndIndex);
    }

    private Map<String, Object> getMapObjectFromJson(String strJson) {
        Map<String, Object> map = null;
        Gson gson = new Gson();
        map = gson.fromJson(strJson, new TypeToken<HashMap<String, Object>>() {
        }.getType());
        return map;
    }

    public List<Map<String, Map<String, Object>>> getAllParamsConfig(String template) throws Exception {
        List<Map<String, Map<String, Object>>> lstConfigs = new ArrayList<>();
        if (template != null) {
            String regex = "@\\{.*?}";
            Pattern pattern = Pattern.compile(regex);
            Matcher m = pattern.matcher(template);
            List<String> finalConfig = new ArrayList<>();
            while (m.find()) {
                finalConfig.add(m.group());
            }

            if (!ValidationUtils.isNullOrEmpty(finalConfig)) { //Lọc ra chuỗi cấu hình
                // Convert ra object Config
                finalConfig.forEach(finalConfigItem -> {
                    Map<String, Map<String, Object>> configItem = new HashMap<>();
                    String finalConfigItemTmp = finalConfigItem.substring(1);
                    finalConfigItemTmp = finalConfigItemTmp.replaceAll("\\(", "{").replaceAll("\\)", "}");
                    System.out.println("handlerDataNormal value =" + finalConfigItemTmp);
                    Map<String, Object> objectConfig = getMapObjectFromJson(finalConfigItemTmp);
                    String type = StringUtil.nvl(objectConfig.get("type"), "").trim().toUpperCase();
                    if (type.isEmpty()) {
                        objectConfig.put("keyReplace", StringUtil.nvl(finalConfigItem, ""));
                    } else {
                        objectConfig.put("keyReplace", StringUtil.nvl(finalConfigItem, ""));
                    }
                    configItem.put("objectConfig", objectConfig);
                    lstConfigs.add(configItem);
                });
            }
        }
        return lstConfigs;
    }

    private Object findObjectInMapVariable(Map<String, VariableValueDto> mVariable, String value) {
        var ref = new Object() {
            Object newValue = null;
            String[] lstValue = new String[0];
        };
        //Bắt đầu @test_select mới tìm trong lstVariable
        if (value != null && value.startsWith("@")) {
            value = value.substring(1);
            if (value.contains(".")) {//Trường hợp lấy giá trị trong matrix @mtx_matrix1.txt_text2
                ref.lstValue = value.split("\\.");
                value = ref.lstValue[0];
            }
            if (mVariable != null) {
                try {
                    String finalValue = value;
                    //Thử tìm key
                    Set<String> lstProperties = mVariable.keySet();
                    String keyMatch = lstProperties.stream().filter(s -> s.endsWith(finalValue)).findFirst().orElse(null);
                    if (keyMatch != null) {
                        //Các biến thông thường replace lại nếu là dạng chuổi mảng string
                        if (mVariable.get(keyMatch) != null) {
                            ref.newValue = mVariable.get(keyMatch).getValue();
                            try {
                                if (ref.newValue != null && mVariable.get(keyMatch).getType() != null
                                        && mVariable.get(keyMatch).getType().equalsIgnoreCase("String")) {
                                    System.out.println(ref.newValue);
                                    ref.newValue = convertArrStringToString(StringUtil.nvl(ref.newValue, ""));
                                }
                            } catch (Exception e) {
                                log.error("findObjectInMapVariable", e);
                            }
                        }
                        //Matrix thì cần xử lý tiếp type của matrix, table luôn là Json
                        if (ref.newValue != null && ref.lstValue.length >= 2) {
                            Map<String, Object> objectMap = getMapObjectFromJson((String) ref.newValue);
                            if (objectMap != null) {
                                Map<String, Object> data = (Map<String, Object>) objectMap.get("data");
                                if (data != null) {
                                    List<Map<String, Object>> lstData = (List<Map<String, Object>>) data.get("data");
                                    if (lstData != null && !lstData.isEmpty()) {//Dữ liệu của matrix luôn chị lưu ở vị trí 0
                                        ref.newValue = lstData.get(0).get(ref.lstValue[1]);
                                        if (ref.newValue instanceof List || ref.newValue instanceof String) {
                                            ref.newValue = convertArrStringToString(StringUtil.nvl(ref.newValue, ""));
                                        }
                                    }
                                }
                            }

                        }
                        //trả về luôn
                        return ref.newValue;
                    }
                } catch (Exception e) {
                    log.error("findObjectInMapVariable", e);
                    throw e;
                }
            }
        }
        return ref.newValue;
    }

    private String convertArrStringToString(String newValue) {
        try {
            if (newValue != null && newValue.contains("[")) {
                System.out.println(newValue);
                return StringUtil.nvl(newValue, "")
                        .replaceAll("\\[", "")
                        .replaceAll("]", "")
                        .replaceAll("\"", "")
                        .replaceAll(",", ", ");
            }
        } catch (Exception e) {
            log.error("convertArrStringToString", e);
            return newValue;
        }
        return newValue;
    }

    private Integer getIntValueFromMap(Map<String, Object> itemValue, String key, Integer defaultValue) {
        Integer value;
        try {
            value = ((Double) itemValue.get(key)).intValue();
        } catch (Exception e) {
            value = defaultValue;
        }
        return value;
    }

}
