package vn.fis.eapprove.business.utils.template.tableTemplate;

import lombok.extern.slf4j.Slf4j;
import vn.fis.eapprove.security.CredentialHelper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import jakarta.xml.bind.JAXBElement;
import org.docx4j.dml.wordprocessingDrawing.Inline;
import org.docx4j.jaxb.Context;
import org.docx4j.model.datastorage.migration.VariablePrepare;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.BinaryPartAbstractImage;
import org.docx4j.utils.SingleTraversalUtilVisitorCallback;
import org.docx4j.wml.Color;
import org.docx4j.wml.*;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.manager.FileManager;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigInteger;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface CommonTable {
    default List<Object> getAllElementFromObject(Object obj, Class<?> toSearch) {
        List<Object> result = new ArrayList<>();
        if (obj instanceof JAXBElement) obj = ((JAXBElement<?>) obj).getValue();

        if (obj.getClass().equals(toSearch)) {
            result.add(obj);
        } else if (obj instanceof ContentAccessor) {
            List<?> children = ((ContentAccessor) obj).getContent();
            for (Object child : children) {
                result.addAll(getAllElementFromObject(child, toSearch));
            }

        }
        return result;
    }

    default Object prepareVariables(Object body) {
        SingleTraversalUtilVisitorCallback paragraphVisitor
                = new SingleTraversalUtilVisitorCallback(
                new VariablePrepare.TraversalUtilParagraphVisitor());
        paragraphVisitor.walkJAXBElements(body);
        return body;
    }

    default P createPageBreak() {
        Br br = Context.getWmlObjectFactory().createBr();
        br.setType(STBrType.PAGE);

        R run = Context.getWmlObjectFactory().createR();
        run.getContent().add(br);

        P paragraph = Context.getWmlObjectFactory().createP();
        paragraph.getContent().add(run);
        return paragraph;
    }

    default Integer getIntValueFromMap(Map<String, Object> itemValue, String key, Integer defaultValue) {
        Integer value;
        try {
            value = ((Double) itemValue.get(key)).intValue();
        } catch (Exception e) {
            value = defaultValue;
        }
        return value;
    }

    default String getFormatNumberFromMap(Map<String, Object> itemValue, String valueField, String defaultValue) {
        var ref = new Object() {
            String value = StringUtil.nvl(valueField, "");
        };

        try {
            if (ref.value != null) {
                //format số theo định dạng
                if (itemValue.containsKey("format")) {
                    String strPattern = StringUtil.nvl(itemValue.get("format"), "");
                    if (!strPattern.isEmpty()) {
                        try {
                            double dbValue = Double.parseDouble(StringUtil.nvl(ref.value, defaultValue));
                            if (strPattern.contains(".")) {
                                ref.value = StringUtil.format(dbValue, strPattern);
                            } else {
                                ref.value = StringUtil.format(Math.round(dbValue), strPattern);
                            }
                        } catch (Exception e) {
                            ref.value = defaultValue;
                        }
                    }
                }
                //format số thành chữ
                if (itemValue.containsKey("numberToWord") && !itemValue.containsKey("format")) {
                    String numberToWord = StringUtil.nvl(itemValue.get("numberToWord"), "");
                    if (numberToWord.equalsIgnoreCase("TRUE")) {
                        try {
                            if (ref.value != null) {
//                                ref.value = StringUtil.pronounceVietnameseNumber(Long.parseLong(StringUtil.nvl((ref.value), "0")));
                                ref.value = StringUtil.pronounceVietnameseNumber(StringUtil.nvl((ref.value), defaultValue));
                            }
                        } catch (Exception e) {
                            ref.value = defaultValue;
                        }
                    }
                }
            } else {
                ref.value = defaultValue;
            }
        } catch (Exception e) {
            ref.value = defaultValue;
        }

        return ref.value;
    }

    default byte[] resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight, String fileName) throws IOException {
        if (originalImage == null) {
            return null;
        }

        int imageType = originalImage.getType();
        BufferedImage outputImage = null;
        String formatName = "";
        Image resultingImage = originalImage.getScaledInstance(targetWidth, targetHeight, Image.SCALE_SMOOTH);
        // Check if the image is a PNG
        if (imageType == BufferedImage.TYPE_INT_ARGB || imageType == BufferedImage.TYPE_4BYTE_ABGR
                || (fileName != null && (fileName.endsWith(".png") || fileName.endsWith(".PNG")))) {
            System.out.println("Image is a PNG");
            formatName = "PNG";
            int type = ((originalImage.getType() == 0) ? BufferedImage.TYPE_INT_ARGB : originalImage.getType());
            outputImage = new BufferedImage(targetWidth, targetHeight, type);
            Graphics2D g2d = outputImage.createGraphics();
            g2d.drawImage(resultingImage, 0, 0, targetWidth, targetHeight, null);
            g2d.dispose();
            g2d.setComposite(AlphaComposite.Src);
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        } else {// Check if the image is a JPEG
            formatName = "JPEG";
            outputImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
            outputImage.getGraphics().drawImage(resultingImage, 0, 0, targetWidth, targetHeight, null);
        }

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(outputImage, formatName, baos);
        byte[] bytes = baos.toByteArray();
        return bytes;
    }


    default void addImageToPara(WordprocessingMLPackage wordMLPackage, ObjectFactory factory, P paragraph,
                                String altText, int id1, int id2,
                                String isExternalUrl, String pathImage, int targetWidth, int targetHeight, String signatoryName,
                                String isClear, FileManager fileManager, CredentialHelper credentialHelper, String bucket) throws Exception {
        addImageToPara(wordMLPackage, factory, paragraph, altText, id1, id2, isExternalUrl, pathImage, targetWidth, targetHeight,
                signatoryName, "", "", "", 20, isClear, fileManager, credentialHelper, "true", "true", bucket, "");
    }

    default void addImageToPara(WordprocessingMLPackage wordMLPackage, ObjectFactory factory, P paragraph,
                                String altText, int id1, int id2,
                                String isExternalUrl, String pathImage, int targetWidth, int targetHeight,
                                String signatoryName, String position, String orgAssigneeTitle, String signDate, int size, String isClear,
                                FileManager fileManager, CredentialHelper credentialHelper, String addSignImage, String addSignatoryName,
                                String bucket, String signAction) throws Exception {
        R run = factory.createR();
        BufferedImage bi = null;
        try {
            // có config addSignImage = false thì truyền img blank
            if (addSignImage != null && addSignImage.equalsIgnoreCase("false")) {
                pathImage = "user_signature/default/default.png";
                InputStream inputStream = fileManager.getFileInputStream(bucket, pathImage);
                bi = ImageIO.read(inputStream);
            } else {
                InputStream inputStream = new ByteArrayInputStream(new byte[0]);
                if (isExternalUrl.equalsIgnoreCase("TRUE")) {
                    inputStream = new BufferedInputStream(new URL(pathImage).openStream());
                } else if (fileManager != null && bucket != null) {
                    inputStream = fileManager.getFileInputStream(bucket, pathImage);
                }
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                byte[] buffer = new byte[10240]; //10kb
                int bytesRead;
                // Read the input stream in chunks
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                // Convert the downloaded bytes into a BufferedImage
                byte[] imageBytes = outputStream.toByteArray();
                ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(imageBytes);
                bi = ImageIO.read(byteArrayInputStream);
                bi.flush();
                inputStream.close();
                outputStream.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (bi == null) {
            // Xóa các biến ko có value
            paragraph.getContent().clear();
            return;
        }
        byte[] bytes = resizeImage(bi, targetWidth, targetHeight, pathImage);
        bi.flush();

        Br br = factory.createBr(); // this Br element is used break the current and go for next line
        if ((signatoryName != null && !signatoryName.trim().isEmpty())
                || position != null && !position.isEmpty()
                || orgAssigneeTitle != null && !orgAssigneeTitle.isEmpty()
                || (signDate != null && !signDate.isEmpty())) {
            RFonts font = new RFonts();
            font.setAscii("Arial");
            font.setHAnsi("Arial");
            font.setCs("Arial");
            RPr rpr = factory.createRPr();
            rpr.setRFonts(font);
            rpr.setSz(factory.createHpsMeasure());
            rpr.getSz().setVal(BigInteger.valueOf(size));
            org.docx4j.wml.BooleanDefaultTrue b = new org.docx4j.wml.BooleanDefaultTrue();
            b.setVal(true);
            rpr.setB(b);
            run.setRPr(rpr);
            // Create a new Color object and set its value to "auto" to make it transparent
            Color color = factory.createColor();
            color.setVal("auto"); // Set the color to transparent
            // Set the text color in the RunProperties object
            rpr.setColor(color);
        }

        if (orgAssigneeTitle != null && !orgAssigneeTitle.isEmpty()) {
            Text txtPosition = factory.createText();
            txtPosition.setValue(orgAssigneeTitle);
            run.getContent().add(txtPosition);
            run.getContent().add(br);
        }

        if (position != null && !position.isEmpty()) {
            Text txtPosition = factory.createText();
            txtPosition.setValue(position);
            run.getContent().add(txtPosition);
            run.getContent().add(br);
        } else if (isExternalUrl.equalsIgnoreCase("false")) {
            run.getContent().add(br);
            run.getContent().add(br);
        }

        if (!ValidationUtils.isNullOrEmpty(signAction)) {
            Text txtSignAction = factory.createText();
            txtSignAction.setValue(signAction);
            run.getContent().add(txtSignAction);
            run.getContent().add(br);
        }

        BinaryPartAbstractImage imagePart = BinaryPartAbstractImage.createImagePart(wordMLPackage, bytes);
        Inline inline = imagePart.createImageInline(pathImage, altText, id1, id2, false);
        Drawing drawing = factory.createDrawing();
        drawing.getAnchorOrInline().add(inline);
        run.getContent().add(drawing);


        //Với trường hợp kí do 1 vị trí có thể có nhiều ông kí => không xóa cấu hình đi để lần sau kí tiếp
        if (signatoryName != null && !signatoryName.trim().isEmpty()) {

            Text t = factory.createText();
            t.setValue(signatoryName);
            run.getContent().add(br);
            run.getContent().add(br);
            if (addSignatoryName != null && addSignatoryName.equalsIgnoreCase("true")) {
                run.getContent().add(t);
            } else {
                run.getContent().add(br);
            }
            run.getContent().add(br);

        }

        if (signDate != null && !signDate.isEmpty()) {
            Text txtSignDate = factory.createText();
            txtSignDate.setValue(signDate);
            run.getContent().add(txtSignDate);
        }

        if (isClear != null && isClear.equalsIgnoreCase("true")) {
            paragraph.getContent().clear();
        }
        paragraph.getContent().add(run);
    }

    default void addTextToPara(ObjectFactory factory, P paragraph, String isClear, String strContent,
                               Boolean isBold, int size, Boolean addBr) throws Exception {
        R run = factory.createR();
        if (isClear != null && isClear.equalsIgnoreCase("true")) {
            paragraph.getContent().clear();
        }
        if (strContent != null && !strContent.trim().isEmpty()) {
            RFonts font = new RFonts();
            font.setAscii("Times New Roman");
            font.setHAnsi("Times New Roman");
            font.setCs("Times New Roman");
            RPr rpr = factory.createRPr();
            rpr.setRFonts(font);
            if (size > -1) {
                rpr.setSz(factory.createHpsMeasure());
                rpr.getSz().setVal(BigInteger.valueOf(size));
            }
            if (isBold) {
                org.docx4j.wml.BooleanDefaultTrue b = new org.docx4j.wml.BooleanDefaultTrue();
                b.setVal(true);
                rpr.setB(b);
            }
            // Create a new Color object and set its value to "auto" to make it transparent
            Color color = factory.createColor();
            color.setVal("auto"); // Set the color to transparent
            // Set the text color in the RunProperties object
            rpr.setColor(color);
            run.setRPr(rpr);
            Text t = factory.createText();
            t.setValue(strContent);
            Br br = factory.createBr(); // this Br element is used break the current and go for next line

            if (addBr) {
                run.getContent().add(br);
            }
            run.getContent().add(t);
            if (addBr) {
                run.getContent().add(br);
            }
        }
        paragraph.getContent().add(run);
    }

    default void addTextToParaDefault(ObjectFactory factory, P paragraph, String isClear, String strContent, Boolean addBr) throws Exception {
        R run = factory.createR();
        if (strContent != null && !strContent.trim().isEmpty()) {
            List<Object> paragraphContent = paragraph.getContent();
            for (Object obj : paragraphContent) {
                if (obj instanceof R) {
                    run = (R) obj;
                    break;
                }
            }
            if (isClear != null && isClear.equalsIgnoreCase("true")) {
                run.getContent().clear();
            }
            // Xử lý comment có \n
            String[] objString = strContent.split("\n");
            if (objString.length > 1) {
                for (int i = 0; i < objString.length; i++) {
                    Text text = factory.createText();
                    text.setValue(objString[i].replaceAll("null", ""));
                    run.getContent().add(text);
                    Br br = factory.createBr();
                    if (i <= objString.length - 1) { // Add 1 dòng mới docx
                        run.getContent().add(br);
                    }
                }
                //Add text mới
                paragraph.getContent().clear();
                paragraph.getContent().add(run);
            } else {
                Text t = factory.createText();
                t.setValue(strContent);
                Br br = factory.createBr(); // this Br element is used break the current and go for next line
                if (addBr) {
                    run.getContent().add(br);
                }

                run.getContent().add(t);
                if (addBr) {
                    run.getContent().add(br);
                }
                if (isClear != null && isClear.equalsIgnoreCase("true")) {
                    paragraph.getContent().clear();
                }
                paragraph.getContent().add(run);
            }
        }
    }

    default Map<String, Object> getMapObjectFromJson(String strJson) {
        Map<String, Object> map = null;
        Gson gson = new Gson();
        map = gson.fromJson(strJson, new TypeToken<HashMap<String, Object>>() {
        }.getType());
        return map;
    }

    default List<Map> getLstMapObjectFromJson(String strJson) {
        List<Map> map = null;
        Gson gson = new Gson();
        map = gson.fromJson(strJson, new TypeToken<List<Map>>() {
        }.getType());
        return map;
    }
}

