package vn.fis.eapprove.business.utils.template;

import com.openhtmltopdf.outputdevice.helper.BaseRendererBuilder;
import com.openhtmltopdf.pdfboxout.PDFontSupplier;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.io.IOUtils;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.jsoup.Jsoup;
import org.jsoup.helper.W3CDom;
import org.jsoup.nodes.Document;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrint;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrintConfigUser;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTpSignZone;
import vn.fis.eapprove.business.utils.PDFUtils;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.spro.common.helper.RestHelper;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.exception.FileOperationException;
import vn.fis.spro.file.manager.FileManager;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.FileSystems;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class HtmlToPdfConverter {

    public static void main(String[] args) throws IOException {
        List<String> lstUser = Arrays.asList("abc", "def1", "def2", "def3", "def4", "def5", "def6");
        String comment = "Ông Nguyễn Thành Trung - phó giám đốc phụ trách Trung tâm Giáo dục môi trường và Dịch vụ môi trường, Ban quản lý Vườn quốc gia Núi Chúa - cho biết khi đến tham quan du lịch sinh thái rừng đặc dụng, tùy theo số lượng khách tham quan, đơn vị sẽ bố trí hướng dẫn viên là người Raglai bản địa hoặc cán bộ, nhân viên của Vườn quốc gia Núi Chúa đưa khách tham quan để giới thiệu các đặc trưng của vườn.\n" +
                "\n" +
                "UNESCO trao bằng công nhận khu dự trữ sinh quyển thế giới Núi Chúa\n" +
                "Cự ly di chuyển từ Ban quản lý Vườn quốc gia Núi Chúa đến các điểm du lịch khoảng 4 - 15km (bao gồm đường bằng và đường núi), trong đó điểm gần nhất là thác 5 tầng và xa nhất là suối nước ngọt Bình Tiên.\n" +
                "\n" +
                "Các điểm còn lại như thung lũng Ô Lim (7km), đỉnh Núi Chúa (9km), đỉnh Núi Ông (7km), ao hồ đá vách (6km).";
        // HTML file - Input
        String inputHTML = "<html>" +
                "<head>" +
                "<style>" +
                "table {" +
                "  border-collapse: collapse;" +
                "  width: 100%;" +
                "  font-family: source-times;" +
                "  font-size: 13;" +
                "  -fs-table-paginate: paginate;" +
                "  page-break-inside: avoid;" +
                "}" +
                "" +
                "td {" +
                "  border: 1px solid black;" +
                "  text-align: left;" +
                "  padding: 8px;" +
                "  font-family: source-times;" +
                "  height: 180px;" +
                "  font-weight: 500;" +
                "}" +
                "th {" +
                "  border: 1px solid black;" +
                "  text-align: left;" +
                "  padding: 8px;" +
                "  font-family: source-times;" +
                "  font-weight: 600;" +
                "  background-color: #c6d6eb;" +
                "}" +
                "" +
                "tr:nth-child(even) {" +
                "  background-color: #ffffff;" +
                "}" +
                "</style>" +
                "</head>" +
                "<body>" +
                "<table>" +
                "  <tr>" +
                "    <th style='min-width: 30%'>Người ký</th>" +
                "    <th style='min-width: 40%'>Chữ ký, Ngày ký</th>" +
                 "    <th style='min-width: 30%'>Nội dung phê duyệt</th>" +
                "  </tr>";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        for (String user : lstUser) {
            InputStream imageSignature = new FileInputStream(HtmlToPdfConverter.class.getClassLoader().getResource("fileSign/Signature.png").getFile());
            byte[] bytesSignature = IOUtils.toByteArray(imageSignature);
            String signature = "data:image/png;base64," + Base64.getEncoder().encodeToString(bytesSignature);
            float scale = 1f;
            float width = 244 * scale;
            float height = 100 * scale;
            inputHTML += "  <tr>" +
                    // "    <td style='min-width: 30%'>" + userItem.lastName + " " + userItem.firstName + "<br/>" + userItem.position + "</td>" +
                    "    <td style='min-width: 30%'>" + user + "</td>" +
                    "    <td style='min-width: 40%;text-align: center; opacity:0.5; background-image: url(\"Spro4_logo.png\");background-position: center; background-repeat: no-repeat;'>" +
                    "        <img width='" + width + "px' height='" + height + "px' src='" + signature + "'/>" +
                    "        <br/> " + user +
                    "        <br/> " + simpleDateFormat.format(new Date()) +
                    "    </td>" +
                     "    <td style='min-width: 30%'>" + comment + "</td>" +
                    "  </tr>";
        }
        inputHTML += "</table>" +
                "</body>" +
                "</html>";

        HtmlToPdfConverter htmlToPdfConverter = new HtmlToPdfConverter();
        //create well formed HTML
        org.w3c.dom.Document doc = htmlToPdfConverter.createWellFormedHtml(inputHTML);
        //Load file
        PDDocument docDestin = PDDocument.load(new FileInputStream("C:/Users/<USER>/Desktop/new.pdf"));
        PDPage page = docDestin.getPage(docDestin.getNumberOfPages() - 1);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        PDRectangle mediaBox = page.getMediaBox();
        float pageHeight = mediaBox.getUpperRightY() / (2.85f);
        float pageWidth = mediaBox.getUpperRightX() / (2.85f);
        htmlToPdfConverter.xhtmlToPdf(doc, bos, pageWidth, pageHeight);

        //Merge 2 file pdf
        PDDocument docSource = PDDocument.load(new ByteArrayInputStream(bos.toByteArray()));
        PDFMergerUtility merger = new PDFMergerUtility();
        merger.appendDocument(docDestin, docSource);

        //Save file
        docDestin.save(new FileOutputStream("C:/Users/<USER>/Desktop/Test.pdf"));
    }

    public byte[] autoBindSignature(BpmTemplatePrint bpmPrintPhase, List<BpmTpSignZone> signZones, FileManager fileManager, String bucket, List<BpmTemplatePrintConfigUser> lstConfigUser) throws IOException {
        PDDocument docDestin = null;
        PDDocument docSource = null;
        try {
            String templateName = bpmPrintPhase.getUploadWordsChange();
            InputStream inputStream = null;
            if ((templateName.startsWith("http"))) {//Lấy file mẫu từ hệ thống ngoài
                RestHelper restHelper = new RestHelper();
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                ResponseEntity<Resource> responseEntity = restHelper.exchange(templateName,
                        HttpMethod.GET,
                        headers,
                        null,
                        new ParameterizedTypeReference<>() {
                        });

                if (responseEntity.getBody() != null) {
                    inputStream = responseEntity.getBody().getInputStream();
                }
            } else {//Lấy file từ hệ thống s3
                inputStream = fileManager.getFileInputStream(bucket, templateName);
            }
            if (inputStream == null) {
                return null;
            }

            //Load file
            docDestin = PDDocument.load(inputStream);
            inputStream.close();
            if (docDestin == null) {
                return null;
            }

            // HTML file - Input
            String inputHTML = "<html>" +
                    "<head>" +
                    "<style>" +
                    "table {" +
                    "  border: 1px solid black;" +
                    "  border-collapse: collapse;" +
                    "  width: 100%;" +
                    "  font-family: source-times;" +
                    "  font-size: 13;" +
                    "  -fs-table-paginate: paginate;" +
                    "  page-break-inside: avoid;" +
                    "  table-layout: fixed;" +
                    "}" +
                    "td {" +
                    "  border: 1px solid black;" +
                    "  text-align: left;" +
                    "  padding: 8px;" +
                    "  font-family: source-times;" +
                    "  height: 180px;" +
                    "  font-weight: 500;" +
                    "}" +
                    "th {" +
                    "  border: none;" +
                    "  text-align: left;" +
                    "  padding: 8px;" +
                    "  font-family: source-times;" +
                    "  font-weight: 600;" +
                    "  background-color: #c6d6eb;" +
                    "}" +
                    "tr:nth-child(even) {" +
                    "  background-color: #ffffff;" +
                    "}" +
                    "</style>" +
                    "</head>" +
                    "<body>" +
                    "<table>" +
                    "  <tr>" +
                    "    <th style='width: 35%;text-align: center;'> </th>" +
//                    "    <th style='min-width: 40%'>Chữ ký, Ngày ký</th>" +
                     "    <th style='min-width: 65%;text-align: center;'>Ý kiến</th>" +
                    "  </tr>";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");

            for (BpmTpSignZone bpmTpSignZone : signZones) {
                if (!ValidationUtils.isNullOrEmpty(bpmTpSignZone.getSign())) {
                    byte[] bytes = fileManager.getFile(bucket, bpmTpSignZone.getSign());
                    if (bytes != null) {
                        String fullName = StringUtil.nvl(bpmTpSignZone.getLastName(), "") + " " + StringUtil.nvl(bpmTpSignZone.getFirstName(), "").trim();
                        String orgAssigneeTitle;
                        String comment = StringUtil.nvl(bpmTpSignZone.getComment(), "").replaceAll("\n", "<br/>");
                        String title = StringUtil.nvl(bpmTpSignZone.getPosition(), "").trim();
                        String signDate = simpleDateFormat.format(bpmTpSignZone.getSignedDate() != null ? bpmTpSignZone.getSignedDate() : new Date());
                        String signAction = "";
                        if (!ValidationUtils.isNullOrEmpty(bpmTpSignZone.getChartNodeLevel())) {
                            signAction = switch (bpmTpSignZone.getChartNodeLevel()) {
                                case "0" -> "Đệ trình";
                                case "1", "2", "3" -> "Phê duyệt";
                                default -> "Xét duyệt";
                            };
                        }

                        // check theo bpm_template_print_config_user
                        if (!ValidationUtils.isNullOrEmpty(lstConfigUser)) {
                            BpmTemplatePrintConfigUser configUser = lstConfigUser.stream().filter(e -> e.getUsername().equalsIgnoreCase(bpmTpSignZone.getEmail())).findFirst().orElse(null);
                            if (configUser != null) {
                                if (configUser.getAddTitle().equalsIgnoreCase("false")) {
                                    title = "";
                                }
                                if (configUser.getAddName().equalsIgnoreCase("false")) {
                                    fullName = "";
                                }
                                if (configUser.getAddSignImage().equalsIgnoreCase("false")) {
                                    bytes = fileManager.getFile(bucket, "user_signature/default/default.png");
                                }
                                if (configUser.getAddSignDate().equalsIgnoreCase("false")) {
                                    signDate = "";
                                }
                                if (configUser.getAddSignAction().equalsIgnoreCase("false")) {
                                    signAction = "";
                                }
                            }
                        }

                        if (!ValidationUtils.isNullOrEmpty(bpmTpSignZone.getOrgAssigneeTitle()) && !ValidationUtils.isNullOrEmpty(title)) {
                            orgAssigneeTitle = "TUQ " + bpmTpSignZone.getOrgAssigneeTitle();
                            inputHTML += "  <tr>" +
                                    "    <td style='width: 35%;max-width: 35%;text-align: center;'>" +
                                    "        " + StringUtil.nvl(orgAssigneeTitle, "").trim() +
                                    "        <br/> " + title +
                                    "        <br/> " + signAction + " <br/> " +
                                    "        <img width='50%' display: 'block' height='auto' src='data:image/png;base64, " + Base64.getEncoder().encodeToString(bytes) + "'/>" +
                                    "        <br/>" + fullName +
                                    "        <br/> " + signDate +
                                    "    </td>" +
                                    "    <td style='min-width: 65%; word-break: break-word; white-space: normal; overflow-wrap: break-word;'>" + comment + "</td>" +
                                    "  </tr>";
                        } else {
                            inputHTML += "  <tr>" +
                                    "    <td style='width: 35%;max-width: 35%;text-align: center;'>" +
                                    "        " + title +
                                    "        <br/> " + signAction + " <br/> " +
                                    "        <img width='50%' display: 'block' height='auto' src='data:image/png;base64, " + Base64.getEncoder().encodeToString(bytes) + "'/>" +
                                    "        <br/>" + fullName +
                                    "        <br/> " + signDate +
                                    "    </td>" +
                                    "    <td style='min-width: 65%; word-break: break-word; white-space: normal; overflow-wrap: break-word;'>" + comment + "</td>" +
                                    "  </tr>";
                        }
                    }
                }
            }

            inputHTML += "</table>" +
                    "</body>" +
                    "</html>";

            //create well formed HTML
            log.info("inputHTML...: {}", inputHTML);
            org.w3c.dom.Document doc = createWellFormedHtml(inputHTML);

            log.info("Starting conversion to PDF...");
            PDPage page = docDestin.getPage(docDestin.getNumberOfPages() - 1);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            PDRectangle mediaBox = page.getMediaBox();
            float pageHeight = mediaBox.getUpperRightY() / (2.85f);
            float pageWidth = mediaBox.getUpperRightX() / (2.85f);
            xhtmlToPdf(doc, bos, pageWidth, pageHeight);

            //Merge 2 file pdf
            docSource = PDDocument.load(new ByteArrayInputStream(bos.toByteArray()));
            PDFMergerUtility merger = new PDFMergerUtility();
            merger.appendDocument(docDestin, docSource);

            //Save file
            ByteArrayOutputStream outputPdf = new ByteArrayOutputStream();
            docDestin.save(outputPdf);
            return outputPdf.toByteArray();
        } catch (Exception e) {
            log.error("Error when auto bind signature", e);
            return null;
        } finally {
            if (docDestin != null) {
                docDestin.close();
            }
            if (docSource != null) {
                docSource.close();
            }
        }
    }

    public byte[] convertHtmlToPdf(String inputHTML, float pageWidth, float pageHeight) throws IOException {
        org.w3c.dom.Document doc = createWellFormedHtml(inputHTML);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        xhtmlToPdf(doc, bos, pageWidth, pageHeight);
        return bos.toByteArray();
    }

    // Creating well formed document
    private org.w3c.dom.Document createWellFormedHtml(String inputHTML) throws IOException {
        Document document = Jsoup.parse(inputHTML, "UTF-8");
        document.outputSettings().syntax(Document.OutputSettings.Syntax.xml);
        log.info("HTML parsing done...");
        return new W3CDom().fromJsoup(document);
    }

    private void xhtmlToPdf(org.w3c.dom.Document doc, OutputStream os, float pageWidth, float pageHeight) throws IOException {
        // base URI to resolve future resources
        String baseUri = FileSystems.getDefault()
                .getPath("src/main/resources/fileSign")
                .toUri()
                .toString();
        PdfRendererBuilder builder = new PdfRendererBuilder();
        builder.toStream(os);
        builder.useDefaultPageSize(pageWidth, pageHeight, BaseRendererBuilder.PageSizeUnits.MM);
        // add external font
//        builder.useFont(new File(getClass().getClassLoader().getResource("fonts/times.ttf").getFile()), "source-times");
        PDFontSupplier supplier = new PDFontSupplier(PDFUtils.getFontTimes(new PDDocument()));
        builder.useFont(supplier, "source-times");
        builder.withW3cDocument(doc, baseUri);
        builder.run();
        log.info("PDF creation completed");
        os.close();
    }

}