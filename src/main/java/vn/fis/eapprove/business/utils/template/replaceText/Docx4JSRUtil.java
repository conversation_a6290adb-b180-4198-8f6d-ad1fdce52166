package vn.fis.eapprove.business.utils.template.replaceText;

import jakarta.xml.bind.JAXBElement;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.wml.ContentAccessor;
import org.docx4j.wml.Text;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.*;
import java.util.stream.Collectors;

public class Docx4JSRUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(Docx4JSRUtil.class);

    public Docx4JSRUtil() {
    }

    public static void searchAndReplace(WordprocessingMLPackage docxDocument, Map<String, String> replaceMap) {
        List<Text> texts = getAllElementsOfType(docxDocument.getMainDocumentPart(), Text.class);
        String completeString = getCompleteString(texts);
        if (!completeString.isEmpty()) {
            List<TextMetaItem> metaItemList = buildMetaItemList(texts);
            TextMetaItem[] stringIndicesLookupArray = buildIndexToTextMetaItemArray(metaItemList);
            List<ReplaceCommand> replaceCommandList = buildAllReplaceCommands(completeString, replaceMap);

            // replace các biến ko có value trong table -> ""
            List<Text> finalText = texts.stream().map(text -> {
                if (text.getValue().matches("\\$\\{(.*?)}") || text.getValue().contains("@{deletedValue}")) {
                    text.setValue("");
                }
                return text;
            }).collect(Collectors.toList());

            replaceCommandList.forEach((rc) -> {
                executeReplaceCommand(finalText, rc, stringIndicesLookupArray);
            });
        }
    }

    public static <T> List<T> getAllElementsOfType(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList();
        if (obj instanceof JAXBElement) {
            obj = ((JAXBElement) obj).getValue();
        }

        if (obj.getClass().equals(clazz)) {
            result.add((T) obj);
        } else if (obj instanceof ContentAccessor) {
            List<?> children = ((ContentAccessor) obj).getContent();
            Iterator var4 = children.iterator();

            while (var4.hasNext()) {
                Object child = var4.next();
                result.addAll(getAllElementsOfType(child, clazz));
            }
        }

        return result;
    }

    public static String getCompleteString(List<Text> texts) {
        return texts.stream().map(Text::getValue).filter(Objects::nonNull).reduce(String::concat).orElse("");
    }

    public static List<TextMetaItem> buildMetaItemList(List<Text> texts) {
        int index = 0;
        int iteration = 0;
        List<TextMetaItem> list = new ArrayList();

        for (Iterator var4 = texts.iterator(); var4.hasNext(); ++iteration) {
            Text text = (Text) var4.next();
            int length = text.getValue().length();
            list.add(new TextMetaItem(index, index + length - 1, text, iteration));
            index += length;
        }

        return list;
    }

    public static TextMetaItem[] buildIndexToTextMetaItemArray(List<TextMetaItem> metaItemList) {
        int currentStringIndicesToTextIndex = 0;
        int max = metaItemList.get(metaItemList.size() - 1).getEnd() + 1;
        TextMetaItem[] arr = new TextMetaItem[max];

        for (int i = 0; i < max; ++i) {
            TextMetaItem currentTextMetaItem = metaItemList.get(currentStringIndicesToTextIndex);
            arr[i] = currentTextMetaItem;
            if (i >= currentTextMetaItem.getEnd()) {
                ++currentStringIndicesToTextIndex;
            }
        }

        return arr;
    }

    private static List<ReplaceCommand> buildReplaceCommandsForOnePlaceholder(String completeString, Map.Entry<String, String> searchAndReplaceEntry) {
        return StringFindUtil.findAllOccurrencesInString(completeString, searchAndReplaceEntry.getKey()).stream().map((fmi) -> {
            return new ReplaceCommand(searchAndReplaceEntry.getValue(), fmi);
        }).collect(Collectors.toList());
    }

    public static List<ReplaceCommand> buildAllReplaceCommands(String completeString, Map<String, String> replaceMap) {
        return replaceMap.entrySet().stream().map((e) -> {
            return buildReplaceCommandsForOnePlaceholder(completeString, e);
        }).flatMap(Collection::stream).sorted().collect(Collectors.toList());
    }

    public static void executeReplaceCommand(List<Text> texts, ReplaceCommand replaceCommand, TextMetaItem[] arr) {
        TextMetaItem tmi1 = arr[replaceCommand.getFoundResult().getStart()];
        TextMetaItem tmi2 = arr[replaceCommand.getFoundResult().getEnd()];
        String t1;
        int lowerBorder;
        int beginIndex;
        String newValue;
        if (tmi1.getPosition() == tmi2.getPosition()) {
            t1 = tmi1.getText().getValue();
            lowerBorder = tmi1.getPositionInsideTextObject(replaceCommand.getFoundResult().getStart());
            beginIndex = tmi2.getPositionInsideTextObject(replaceCommand.getFoundResult().getEnd());
            String keepBefore = t1.substring(0, lowerBorder);
            newValue = t1.substring(beginIndex + 1);
            tmi1.getText().setValue(keepBefore + replaceCommand.getNewValue() + newValue);
        } else {
            if (tmi2.getPosition() - tmi1.getPosition() > 1) {
                int upperBorder = tmi2.getPosition();
                lowerBorder = tmi1.getPosition() + 1;

                for (beginIndex = lowerBorder; beginIndex < upperBorder; ++beginIndex) {
                    texts.get(beginIndex).setValue(null);
                }
            }

            t1 = tmi1.getText().getValue();
            String t2 = tmi2.getText().getValue();
            beginIndex = tmi1.getPositionInsideTextObject(replaceCommand.getFoundResult().getStart());
            int endIndex = tmi2.getPositionInsideTextObject(replaceCommand.getFoundResult().getEnd());
            if (replaceCommand.getNewValue() == null) {
                LOGGER.warn("replaceCommand.getNewValue() is null! Using '' instead!");
                newValue = "";
            } else {
                newValue = replaceCommand.getNewValue();
            }

            System.out.println("######################### = " + t1 + newValue + t2);
            if (ValidationUtils.isNullOrEmpty(t1) && beginIndex > 0 && !ValidationUtils.isNullOrEmpty(t2)) {
                t1 = StringUtil.nvl(t2, "");
            }
            t1 = t1.substring(0, beginIndex);
            t1 = t1.concat(newValue);
            t2 = t2.substring(endIndex + 1);
            tmi1.getText().setValue(t1);
            tmi2.getText().setValue(t2);
        }

    }

    public static class TextMetaItem {
        private final int position;
        private final int start;
        private final int end;
        private final Text text;

        public TextMetaItem(int start, int end, Text text, int position) {
            this.start = start;
            this.end = end;
            this.text = text;
            this.position = position;
        }

        public int getStart() {
            return this.start;
        }

        public int getEnd() {
            return this.end;
        }

        public Text getText() {
            return this.text;
        }

        public int getPosition() {
            return this.position;
        }

        public int getPositionInsideTextObject(int completeStringIndex) {
            return completeStringIndex - this.start;
        }
    }

    public static class ReplaceCommand implements Comparable<ReplaceCommand> {
        private final String newValue;
        private final StringFindUtil.FoundResult foundMetaItem;

        public ReplaceCommand(String newValue, StringFindUtil.FoundResult foundMetaItem) {
            this.newValue = newValue;
            this.foundMetaItem = foundMetaItem;
        }

        public String getNewValue() {
            return this.newValue;
        }

        public StringFindUtil.FoundResult getFoundResult() {
            return this.foundMetaItem;
        }

        public int compareTo(ReplaceCommand other) {
            return other.getFoundResult().getStart() - this.getFoundResult().getStart();
        }
    }
}