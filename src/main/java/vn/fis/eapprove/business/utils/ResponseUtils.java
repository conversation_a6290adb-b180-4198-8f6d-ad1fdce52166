package vn.fis.eapprove.business.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import vn.fis.eapprove.business.tenant.manager.CamundaEngineService;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.spro.common.model.response.ChartInfoRoleResponse;
import vn.fis.spro.common.model.response.UserInfoResponse;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.*;

@Service
@Slf4j
public class ResponseUtils {

    private final CamundaEngineService camundaEngineService;
    private final CustomerService customerService;

    @Autowired
    public ResponseUtils(CamundaEngineService camundaEngineService,
                         CustomerService customerService) {
        this.camundaEngineService = camundaEngineService;
        this.customerService = customerService;
    }

    public String getHostName(ServerHttpRequest request) {
        return Objects.requireNonNull(request.getRemoteAddress()).getHostName();
    }

    public ResponseEntity<?> getResponseEntity(Object data, int code, String mess, HttpStatus status) {
        Map<String, Object> response = new HashMap<>();
        response.put("data", data);
        response.put("code", code);
        response.put("message", mess);
        return new ResponseEntity<>(response, status);
    }

    public Sort getSort(String sortBy, String type) {
        Sort sort = null;
        if (type.equals("ASC")) {
            sort = Sort.by(sortBy).ascending();
        }
        if (type.equals("DESC")) {
            sort = Sort.by(sortBy).descending();
        }
        return sort;
    }

    public Document getXml(String procDefId) {
        try {
            String diagramXml = camundaEngineService.getXML(procDefId);

            DocumentBuilder db = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            InputSource is = new InputSource();
            is.setCharacterStream(new StringReader(diagramXml));
            return db.parse(is);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public List<String> getNextTaskKey(String procDefId, String currTaskKey) {
        try {
            List<String> listTaskKey = new ArrayList<>();
            List<String> listOutKey = new ArrayList<>();
            String diagramXml = camundaEngineService.getXML(procDefId);

            DocumentBuilder db = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            InputSource is = new InputSource();
            is.setCharacterStream(new StringReader(diagramXml));
            Document doc = db.parse(is);
            NodeList nodes = doc.getElementsByTagName("bpmn:userTask");
            NodeList nodeGateway = doc.getElementsByTagName("bpmn:parallelGateway");
            NodeList nodeSequence = doc.getElementsByTagName("bpmn:sequenceFlow");
            NodeList nodeStart = doc.getElementsByTagName("bpmn:startEvent");
            for (int i = 0; i < nodes.getLength(); i++) {
                Element nodeElement = (Element) nodes.item(i);
                String taskKey = nodeElement.getAttributes().getNamedItem("id").getNodeValue();
                if (taskKey.equals(currTaskKey)) {
                    NodeList nodeOut = nodeElement.getElementsByTagName("bpmn:outgoing");
                    for (int j = 0; j < nodeOut.getLength(); j++) {
                        Element outEle = (Element) nodeOut.item(j);
                        listOutKey.add(outEle.getTextContent());
                    }
                }
            }
            recursiveNextTask(listOutKey, nodes, nodeGateway, nodeSequence, listTaskKey);
            return listTaskKey;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public void recursiveNextTask(List<String> listOutKey, NodeList nodes, NodeList nodeGateway,
                                  NodeList nodeSequence, List<String> listTaskKey) {
        try {
            for (int i = 0; i < nodes.getLength(); i++) {
                Element nodeElement = (Element) nodes.item(i);
                String taskKey = nodeElement.getAttributes().getNamedItem("id").getNodeValue();
                if (listOutKey.contains(taskKey)) {
                    listOutKey.remove(taskKey);
                    listTaskKey.add(taskKey);
                    if (!listOutKey.isEmpty()) {
                        recursiveNextTask(listOutKey, nodes, nodeGateway, nodeSequence, listTaskKey);
                    }
                }
            }
            for (int i = 0; i < nodeGateway.getLength(); i++) {
                Element nodeElement = (Element) nodeGateway.item(i);
                String taskKey = nodeElement.getAttributes().getNamedItem("id").getNodeValue();
                if (listOutKey.contains(taskKey)) {
                    NodeList gateOut = nodeElement.getElementsByTagName("bpmn:outgoing");
                    listOutKey.remove(taskKey);
                    for (int j = 0; j < gateOut.getLength(); j++) {
                        Element addedKey = (Element) gateOut.item(j);
                        String addKeyStr = addedKey.getTextContent();
                        listOutKey.add(addKeyStr);
                    }
                    recursiveNextTask(listOutKey, nodes, nodeGateway, nodeSequence, listTaskKey);
                }
            }
            for (int i = 0; i < nodeSequence.getLength(); i++) {
                Element element = (Element) nodeSequence.item(i);
                String seqKey = element.getAttributes().getNamedItem("id").getNodeValue();
                String tarRef = element.getAttributes().getNamedItem("targetRef").getNodeValue();
                String sourceRef = element.getAttributes().getNamedItem("sourceRef").getNodeValue();
                if (listOutKey.contains(seqKey)) {
                    listOutKey.remove(seqKey);
                    listOutKey.add(tarRef);
                    recursiveNextTask(listOutKey, nodes, nodeGateway, nodeSequence, listTaskKey);
                } else if (listOutKey.contains(sourceRef)) {
                    listOutKey.remove(sourceRef);
                    listOutKey.add(tarRef);
                    recursiveNextTask(listOutKey, nodes, nodeGateway, nodeSequence, listTaskKey);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public String getFullname(String username) {
        try {
            // call service
            UserInfoResponse userInfo = customerService.getUserInfo(username);
            if (userInfo != null) {
                return userInfo.getLastname() + " " + userInfo.getFirstname();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return null;
    }

    public List<ChartInfoRoleResponse> getUserByEmail(List<String> username) {
        return customerService.getUserByEmail(username);
    }

}
