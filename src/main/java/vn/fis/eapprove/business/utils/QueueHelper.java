package vn.fis.eapprove.business.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.Queue;

/**
 * Author: PhucVM
 * Date: 10/08/2022
 */
@Slf4j
public class QueueHelper {

    public static <T> void enqueue(Queue<T> queue, T item) {
        try {
            if (queue != null) {
                queue.add(item);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    public static <T> T dequeue(Queue<T> queue) {
        try {
            if (queue != null) {
                return queue.poll();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return null;
    }
}
