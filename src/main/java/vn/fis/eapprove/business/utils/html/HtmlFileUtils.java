package vn.fis.eapprove.business.utils.html;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

public class HtmlFileUtils {
    /**
     * Ghi chuỗi HTML thành một tệp HTML tạm thời.
     *
     * @param htmlContent Chuỗi HTML đầu vào.
     * @return Tệp HTML đã tạo.
     * @throws IOException Nếu xảy ra lỗi trong quá trình tạo tệp.
     */
    public static File createHtmlFileFromString(String htmlContent) throws IOException {
        File tempFile = Files.createTempFile("index", ".html").toFile();
        // Ghi nội dung HTML vào tệp
        Files.writeString(tempFile.toPath(), htmlContent);

        return tempFile;
    }
}
