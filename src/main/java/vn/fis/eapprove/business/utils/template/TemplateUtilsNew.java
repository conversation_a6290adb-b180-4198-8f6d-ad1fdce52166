package vn.fis.eapprove.business.utils.template;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.JAXBException;
import lombok.Data;
import org.docx4j.Docx4J;
import org.docx4j.jaxb.Context;
import org.docx4j.jaxb.XPathBinderAssociationIsPartialException;
import org.docx4j.openpackaging.exceptions.Docx4JException;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.*;
import org.springframework.core.io.ClassPathResource;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrintConfigUser;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTpSignZone;
import vn.fis.eapprove.business.model.request.VariableValueDto;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.eapprove.business.utils.template.replaceText.Docx4JSRUtil;
import vn.fis.eapprove.business.utils.template.tableTemplate.CommonTable;
import vn.fis.eapprove.business.utils.template.tableTemplate.TableSignTemplate;
import vn.fis.eapprove.business.utils.template.tableTemplate.TableTemplate;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.manager.FileManager;

import java.io.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Data
public class TemplateUtilsNew implements CommonTable {
    private FileManager fileManager;
    private CredentialHelper credentialHelper;
    private String sFilenameIn = "";
    private String sFilenameOut = "";
    private WordprocessingMLPackage template;
    private List<BpmTpSignZone> bpmTpSignZoneList;
    private List<BpmTemplatePrintConfigUser> lstConfigUser;
    private String bucket;

    public TemplateUtilsNew() {
    }

    public TemplateUtilsNew(String sFilenameIn, String sFilenameOut) throws Docx4JException {
        this.sFilenameOut = sFilenameOut;
        this.sFilenameIn = sFilenameIn;
        if (sFilenameIn != null) {
            template = WordprocessingMLPackage.load(new File(sFilenameIn));
        }
    }

    public TemplateUtilsNew(InputStream fileContent, String sFilenameOut) throws Docx4JException {
        this.sFilenameOut = sFilenameOut;
        if (fileContent != null) {
            template = WordprocessingMLPackage.load(fileContent);
        }
    }

    public static void main(String[] args) throws Exception {
//        String sFilenameIn = "C:\\Users\\<USER>\\Desktop\\Mô tả sử dụng API tích hợp tạo ticket.docx";
//        String sFilenameIn2 = "C:\\Users\\<USER>\\Desktop\\templateDemo.docx";
//        String sFilenameIn = "C:\\Users\\<USER>\\Desktop\\MTK_test_2.docx";

//        String sFilenameOut = "C:\\Users\\<USER>\\Desktop\\test2.docx";
        String sFilenameOut = "C:\\Users\\<USER>\\Desktop\\test2.pdf";

        //Khởi tạo với docx
//      TemplateUtilsNew templateUtils = new TemplateUtilsNew(sFilenameIn, sFilenameOut);

        //Khới tạo với file .doc, với .doc cần convert ra .docx
//        String sFilenameIn = "C:\\Users\\<USER>\\Desktop\\testdoc.doc";
//        TemplateUtilsNew templateUtils = new TemplateUtilsNew();
//        templateUtils.setSFilenameOut(sFilenameOut);
//        templateUtils.setTemplate(DocTemplate.convert(new FileInputStream(sFilenameIn)));

        //Khởi tạo với stream .docx
//        C:\Users\<USER>\Desktop\Project\Checkout\SPro4-src\BE\business-process-service\src\main\resources\templateDemo\templateDemoOut.docx
        InputStream inputStream = null;
        try {
            inputStream = new ClassPathResource("templateDemo\\ESAPT004.docx").getInputStream();
//            inputStream = new ClassPathResource("templateDemo\\templateDemo.docx").getInputStream();
            File currDir = new File(".");
            String path = currDir.getAbsolutePath();
            sFilenameOut = path.substring(0, path.length() - 1) + "\\src\\main\\resources\\templateDemo\\templateDemoOut.docx";
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        TemplateUtilsNew templateUtils = new TemplateUtilsNew(inputStream, sFilenameOut);

        List<BpmTpSignZone> bpmTpSignZoneList = new ArrayList<>();
        BpmTpSignZone bpmTpSignZone = new BpmTpSignZone();
//        bpmTpSignZone.setSign("signature_files/2023/03/09/sign_20230309161234.png");
//        bpmTpSignZone.setSign("https://tpc.googlesyndication.com/simgad/3248856506927570682");
        bpmTpSignZone.setLastName("Tên người kí này");
        bpmTpSignZone.setTaskDefKey("start");
        bpmTpSignZone.setComment("Đây là comment");
        bpmTpSignZone.setPosition("Nhân viên");
        bpmTpSignZone.setSignedDate(new Date());
        bpmTpSignZoneList.add(bpmTpSignZone);
        bpmTpSignZone = new BpmTpSignZone();
//        bpmTpSignZone.setSign("https://tpc.googlesyndication.com/simgad/3248856506927570682");
        bpmTpSignZone.setLastName("Tên người kí này 2");
        bpmTpSignZone.setTaskDefKey("start");
        bpmTpSignZone.setComment("<ul>\n" +
                "<li style=\"margin-left:1.5em;\"></li>\n" +
                "</ul>\n" +
                "<p style=\"text-align:left;\"><span style=\"color: rgba(0,0,0,0.85);background-color: rgb(255,255,255);font-size: 14px;font-family: Inter, sans-serif;\">tesstestsets2223</span></p>\n" +
                "<ul>\n" +
                "<li style=\"margin-left:1.5em;\"></li>\n" +
                "</ul>\n" +
                "<p style=\"text-align:left;\"><span style=\"color: rgba(0,0,0,0.85);background-color: rgb(255,255,255);font-size: 14px;font-family: Inter, sans-serif;\">5656</span></p>\n" +
                "<ul>\n" +
                "<li style=\"margin-left:1.5em;\"></li>\n" +
                "</ul>\n" +
                "<p style=\"text-align:left;\"><span style=\"color: rgba(0,0,0,0.85);background-color: rgb(255,255,255);font-size: 14px;font-family: Inter, sans-serif;\"><strong>wwww</strong></span>&nbsp;</p>");
//        bpmTpSignZone.setComment("Đây là comment 1");
        bpmTpSignZone.setPosition("Nhân viên");
        bpmTpSignZone.setSignedDate(new Date());
        bpmTpSignZoneList.add(bpmTpSignZone);
        bpmTpSignZone = new BpmTpSignZone();
        bpmTpSignZone.setSign("https://tpc.googlesyndication.com/simgad/3248856506927570682");
        bpmTpSignZone.setLastName("Tên người kí này 3");
        bpmTpSignZone.setTaskDefKey("st04");
        bpmTpSignZone.setComment("Đây là comment 2");
        bpmTpSignZoneList.add(bpmTpSignZone);
        bpmTpSignZone = new BpmTpSignZone();
        bpmTpSignZone.setSign("https://tpc.googlesyndication.com/simgad/3248856506927570682");
        bpmTpSignZone.setLastName("Tên người kí này 4");
        bpmTpSignZone.setTaskDefKey("st04");
        bpmTpSignZoneList.add(bpmTpSignZone);
        bpmTpSignZone.setComment("OK Th ời đi học ngoài nh ững thần đồng To án - L ý - Hóa ra, hội giỏi Văn cũng được nhi ều ngư ời vô cùng ngưỡng mộ. Chuyện ngồi trong ph òng thi chỉ mấy ti ếng đồng hồ mà họ mà vi ết ra được 6,7 trang giấy thi tưởng ch ừng đ ã qu á khủng khi ếp rồi, nhưng nay bạn sẽ phải shock hơn khi bi ết một nam sinh đ ã vi ết được hẳn 18 trang giấy cho một bài văn. Cụ thể mới đây, trên trang fanpage của trư ờng THPT Long Kh ánh (tỉnh Đồng Nai) đ ã đăng tải một bộ ảnh là một bài văn dài 18 trang giấy của cậu bạn Trần H ữu Thơ. Theo nh ững gì được đăng tải, bài văn này đ ã khi ến cô gi áo không khỏi ngỡ ngàng vì không bi ết phê gì vào bài thi, thậm ch í c òn cho cậu bạn s ố điểm gần tuyệt đ ối là 9,5 điểm. Đ ề bài yêu cầu nêu cảm nhận v ề một đoạn tr ích trong t ác phẩm \"Nh ững đ ứa con trong gia đình\" của Nguyễn Thi, t ừ đó rút ra suy nghĩ của mình v ề vai tr ò của gia đình trong cuộc s ống hiện nay. Với khả năng cảm thụ văn học tr ời phú của mình, H ữu Thơ đ ã khi ến nhi ều ngư ời không khỏi shock khi vi ết hẳn 18 trang giấy, đi ều đ áng nói là trang văn nào cũng trình bày sạch đẹp. Nhi ều cô cậu học tr ò cũng để lại l ời ngưỡng mộ dành cho cậu bạn. \"Thực sự là th ời đi học mình nghĩ vi ết đ ến t ờ th ứ 2 thôi là bạn đó cũng qu á giỏi rồi, đây cậu bạn này c òn vi ết hẳn 18 trang, thực sự nể phục\", bạn Hoàng Long cho bi ết. 1. Vi ết đoạn văn ngắn (khoảng 200 ch ữ) bàn v ề Bản Lĩnh “Bản lĩnh” – hai t ừ thật đơn giản nhưng bạn bi ết không, hàm ch ứa trong đó là cả một qu á trình quy ết tâm kiên cư ờng không ngại gian khổ. Gi ống như một chi ếc áo gi áp được tôi luyện t ừ nh ững nguyên liệu b ền v ững nhất, bản lĩnh giúp ta không c òn phải lo sợ trước nh ững cơn gió to hay nh ững ngọn sóng d ữ. Vậy bản lĩnh là gì? Bản lĩnh là khả năng giải quy ết mọi chuyện một c ách bình tĩnh, thông minh và tỉnh t áo. Ngư ời bản lĩnh là ngư ời d ám đương đầu với khó khăn gian khổ. Thất bại, OK Th ời đi học ngoài nh ững thần đồng To án - L ý - Hóa ra, hội giỏi Văn cũng được nhi ều ngư ời vô cùng ngưỡng mộ. Chuyện ngồi trong ph òng thi chỉ mấy ti ếng đồng hồ mà họ mà vi ết ra được 6,7 trang giấy thi tưởng ch ừng đ ã qu á khủng khi ếp rồi, nhưng nay bạn sẽ phải shock hơn khi bi ết một nam sinh đ ã vi ết được hẳn 18 trang giấy cho một bài văn. Cụ thể mới đây, trên trang fanpage của trư ờng THPT Long Kh ánh (tỉnh Đồng Nai) đ ã đăng tải một bộ ảnh là một bài văn dài 18 trang giấy của cậu bạn Trần H ữu Thơ. Theo nh ững gì được đăng tải, bài văn này đ ã khi ến cô gi áo không khỏi ngỡ ngàng vì không bi ết phê gì vào bài thi, thậm ch í c òn cho cậu bạn s ố điểm gần tuyệt đ ối là 9,5 điểm. Đ ề bài yêu cầu nêu cảm nhận v ề một đoạn tr ích trong t ác phẩm \"Nh ững đ ứa con trong gia đình\" của Nguyễn Thi, t ừ đó rút ra suy nghĩ của mình v ề vai tr ò của gia đình trong cuộc s ống hiện nay. Với khả năng cảm thụ văn học tr ời phú của mình, H ữu Thơ đ ã khi ến nhi ều ngư ời không khỏi shock khi vi ết hẳn 18 trang giấy, đi ều đ áng nói là trang văn nào cũng trình bày sạch đẹp. Nhi ều cô cậu học tr ò cũng để lại l ời ngưỡng mộ dành cho cậu bạn. \"Thực sự là th ời đi học mình nghĩ vi ết đ ến t ờ th ứ 2 thôi là bạn đó cũng qu á giỏi rồi, đây cậu bạn này c òn vi ết hẳn 18 trang, thực sự nể phục\", bạn Hoàng Long cho bi ết. 1. Vi ết đoạn văn ngắn (khoảng 200 ch ữ) bàn v ề Bản Lĩnh “Bản lĩnh” – hai t ừ thật đơn giản nhưng bạn bi ết không, hàm ch ứa trong đó là cả một qu á trình quy ết tâm kiên cư ờng không ngại gian khổ. Gi ống như một chi ếc áo gi áp được tôi luyện t ừ nh ững nguyên liệu b ền v ững nhất, bản lĩnh giúp ta không c òn phải lo sợ trước nh ững cơn gió to hay nh ững ngọn sóng d ữ. Vậy bản lĩnh là gì? Bản lĩnh là khả năng giải quy ết mọi chuyện một c ách bình tĩnh, thông minh và tỉnh t áo. Ngư ời bản lĩnh là ngư ời d ám đương đầu với khó khăn gian khổ. Thất bại, OK Th ời đi học ngoài nh ững thần đồng To án - L ý - Hóa ra, hội giỏi Văn cũng được nhi ều ngư ời vô cùng ngưỡng mộ. Chuyện ngồi trong ph òng thi chỉ mấy ti ếng đồng hồ mà họ mà vi ết ra được 6,7 trang giấy thi tưởng ch ừng đ ã qu á khủng khi ếp rồi, nhưng nay bạn sẽ phải shock hơn khi bi ết một nam sinh đ ã vi ết được hẳn 18 trang giấy cho một bài văn. Cụ thể mới đây, trên trang fanpage của trư ờng THPT Long Kh ánh (tỉnh Đồng Nai) đ ã đăng tải một bộ ảnh là một bài văn dài 18 trang giấy của cậu bạn Trần H ữu Thơ. Theo nh ững gì được đăng tải, bài văn này đ ã khi ến cô gi áo không khỏi ngỡ ngàng vì không bi ết phê gì vào bài thi, thậm ch í c òn cho cậu bạn s ố điểm gần tuyệt đ ối là 9,5 điểm. Đ ề bài yêu cầu nêu cảm nhận v ề một đoạn tr ích trong t ác phẩm \"Nh ững đ ứa con trong gia đình\" của Nguyễn Thi, t ừ đó rút ra suy nghĩ của mình v ề vai tr ò của gia đình trong cuộc s ống hiện nay. Với khả năng cảm thụ văn học tr ời phú của mình, H ữu Thơ đ ã khi ến nhi ều ngư ời không khỏi shock khi vi ết hẳn 18 trang giấy, đi ều đ áng nói là trang văn nào cũng trình bày sạch đẹp. Nhi ều cô cậu học tr ò cũng để lại l ời ngưỡng mộ dành cho cậu bạn. \"Thực sự là th ời đi học mình nghĩ vi ết đ ến t ờ th ứ 2 thôi là bạn đó cũng qu á giỏi rồi, đây cậu bạn này c òn vi ết hẳn 18 trang, thực sự nể phục\", bạn Hoàng Long cho bi ết. 1. Vi ết đoạn văn ngắn (khoảng 200 ch ữ) bàn v ề Bản Lĩnh “Bản lĩnh” – hai t ừ thật đơn giản nhưng bạn bi ết không, hàm ch ứa trong đó là cả một qu á trình quy ết tâm kiên cư ờng không ngại gian khổ. Gi ống như một chi ếc áo gi áp được tôi luyện t ừ nh ững nguyên liệu b ền v ững nhất, bản lĩnh giúp ta không c òn phải lo sợ trước nh ững cơn gió to hay nh ững ngọn sóng d ữ. Vậy bản lĩnh là gì? Bản lĩnh là khả năng giải quy ết mọi chuyện một c ách bình tĩnh, thông minh và tỉnh t áo. Ngư ời bản lĩnh là ngư ời d ám đương đầu với khó khăn gian khổ. Thất bại, OK Th ời đi học ngoài nh ững thần đồng To án - L ý - Hóa ra, hội giỏi Văn cũng được nhi ều ngư ời vô cùng ngưỡng mộ. Chuyện ngồi trong ph òng thi chỉ mấy ti ếng đồng hồ mà họ mà vi ết ra được 6,7 trang giấy thi tưởng ch ừng đ ã qu á khủng khi ếp rồi, nhưng nay bạn sẽ phải shock hơn khi bi ết một nam sinh đ ã vi ết được hẳn 18 trang giấy cho một bài văn. Cụ thể mới đây, trên trang fanpage của trư ờng THPT Long Kh ánh (tỉnh Đồng Nai) đ ã đăng tải một bộ ảnh là một bài văn dài 18 trang giấy của cậu bạn Trần H ữu Thơ. Theo nh ững gì được đăng tải, bài văn này đ ã khi ến cô gi áo không khỏi ngỡ ngàng vì không bi ết phê gì vào bài thi, thậm ch í c òn cho cậu bạn s ố điểm gần tuyệt đ ối là 9,5 điểm. Đ ề bài yêu cầu nêu cảm nhận v ề một đoạn tr ích trong t ác phẩm \"Nh ững đ ứa con trong gia đình\" của Nguyễn Thi, t ừ đó rút ra suy nghĩ của mình v ề vai tr ò của gia đình trong cuộc s ống hiện nay. Với khả năng cảm thụ văn học tr ời phú của mình, H ữu Thơ đ ã khi ến nhi ều ngư ời không khỏi shock khi vi ết hẳn 18 trang giấy, đi ều đ áng nói là trang văn nào cũng trình bày sạch đẹp. Nhi ều cô cậu học tr ò cũng để lại l ời ngưỡng mộ dành cho cậu bạn. \"Thực sự là th ời đi học mình nghĩ vi ết đ ến t ờ th ứ 2 thôi là bạn đó cũng qu á giỏi rồi, đây cậu bạn này c òn vi ết hẳn 18 trang, thực sự nể phục\", bạn Hoàng Long cho bi ết. 1. Vi ết đoạn văn ngắn (khoảng 200 ch ữ) bàn v ề Bản Lĩnh “Bản lĩnh” – hai t ừ thật đơn giản nhưng bạn bi ết không, hàm ch ứa trong đó là cả một qu á trình quy ết tâm kiên cư ờng không ngại gian khổ. Gi ống như một chi ếc áo gi áp được tôi luyện t ừ nh ững nguyên liệu b ền v ững nhất, bản lĩnh giúp ta không c òn phải lo sợ trước nh ững cơn gió to hay nh ững ngọn sóng d ữ. Vậy bản lĩnh là gì? Bản lĩnh là khả năng giải quy ết mọi chuyện một c ách bình tĩnh, thông minh và tỉnh t áo. Ngư ời bản lĩnh là ngư ời d ám đương đầu với khó khăn gian khổ. Thất bại,");
        bpmTpSignZone = new BpmTpSignZone();
        bpmTpSignZone.setSign("https://tpc.googlesyndication.com/simgad/3248856506927570682");
        bpmTpSignZone.setLastName("Tên người kí này 4");
        bpmTpSignZone.setTaskDefKey("st04");
        bpmTpSignZoneList.add(bpmTpSignZone);
        bpmTpSignZone.setComment("OK Th ời đi học ngoài nh ững thần đồng To án - L ý - Hóa ra, hội giỏi Văn cũng được nhi ều ngư ời vô cùng ngưỡng mộ. Chuyện ngồi trong ph òng thi chỉ mấy ti ếng đồng hồ mà họ mà vi ết ra được 6,7 trang giấy thi tưởng ch ừng đ ã qu á khủng khi ếp rồi, nhưng nay bạn sẽ phải shock hơn khi bi ết một nam sinh đ ã vi ết được hẳn 18 trang giấy cho một bài văn. Cụ thể mới đây, trên trang fanpage của trư ờng THPT Long Kh ánh (tỉnh Đồng Nai) đ ã đăng tải một bộ ảnh là một bài văn dài 18 trang giấy của cậu bạn Trần H ữu Thơ. Theo nh ững gì được đăng tải, bài văn này đ ã khi ến cô gi áo không khỏi ngỡ ngàng vì không bi ết phê gì vào bài thi, thậm ch í c òn cho cậu bạn s ố điểm gần tuyệt đ ối là 9,5 điểm. Đ ề bài yêu cầu nêu cảm nhận v ề một đoạn tr ích trong t ác phẩm \"Nh ững đ ứa con trong gia đình\" của Nguyễn Thi, t ừ đó rút ra suy nghĩ của mình v ề vai tr ò của gia đình trong cuộc s ống hiện nay. Với khả năng cảm thụ văn học tr ời phú của mình, H ữu Thơ đ ã khi ến nhi ều ngư ời không khỏi shock khi vi ết hẳn 18 trang giấy, đi ều đ áng nói là trang văn nào cũng trình bày sạch đẹp. Nhi ều cô cậu học tr ò cũng để lại l ời ngưỡng mộ dành cho cậu bạn. \"Thực sự là th ời đi học mình nghĩ vi ết đ ến t ờ th ứ 2 thôi là bạn đó cũng qu á giỏi rồi, đây cậu bạn này c òn vi ết hẳn 18 trang, thực sự nể phục\", bạn Hoàng Long cho bi ết. 1. Vi ết đoạn văn ngắn (khoảng 200 ch ữ) bàn v ề Bản Lĩnh “Bản lĩnh” – hai t ừ thật đơn giản nhưng bạn bi ết không, hàm ch ứa trong đó là cả một qu á trình quy ết tâm kiên cư ờng không ngại gian khổ. Gi ống như một chi ếc áo gi áp được tôi luyện t ừ nh ững nguyên liệu b ền v ững nhất, bản lĩnh giúp ta không c òn phải lo sợ trước nh ững cơn gió to hay nh ững ngọn sóng d ữ. Vậy bản lĩnh là gì? Bản lĩnh là khả năng giải quy ết mọi chuyện một c ách bình tĩnh, thông minh và tỉnh t áo. Ngư ời bản lĩnh là ngư ời d ám đương đầu với khó khăn gian khổ. Thất bại, OK Th ời đi học ngoài nh ững thần đồng To án - L ý - Hóa ra, hội giỏi Văn cũng được nhi ều ngư ời vô cùng ngưỡng mộ. Chuyện ngồi trong ph òng thi chỉ mấy ti ếng đồng hồ mà họ mà vi ết ra được 6,7 trang giấy thi tưởng ch ừng đ ã qu á khủng khi ếp rồi, nhưng nay bạn sẽ phải shock hơn khi bi ết một nam sinh đ ã vi ết được hẳn 18 trang giấy cho một bài văn. Cụ thể mới đây, trên trang fanpage của trư ờng THPT Long Kh ánh (tỉnh Đồng Nai) đ ã đăng tải một bộ ảnh là một bài văn dài 18 trang giấy của cậu bạn Trần H ữu Thơ. Theo nh ững gì được đăng tải, bài văn này đ ã khi ến cô gi áo không khỏi ngỡ ngàng vì không bi ết phê gì vào bài thi, thậm ch í c òn cho cậu bạn s ố điểm gần tuyệt đ ối là 9,5 điểm. Đ ề bài yêu cầu nêu cảm nhận v ề một đoạn tr ích trong t ác phẩm \"Nh ững đ ứa con trong gia đình\" của Nguyễn Thi, t ừ đó rút ra suy nghĩ của mình v ề vai tr ò của gia đình trong cuộc s ống hiện nay. Với khả năng cảm thụ văn học tr ời phú của mình, H ữu Thơ đ ã khi ến nhi ều ngư ời không khỏi shock khi vi ết hẳn 18 trang giấy, đi ều đ áng nói là trang văn nào cũng trình bày sạch đẹp. Nhi ều cô cậu học tr ò cũng để lại l ời ngưỡng mộ dành cho cậu bạn. \"Thực sự là th ời đi học mình nghĩ vi ết đ ến t ờ th ứ 2 thôi là bạn đó cũng qu á giỏi rồi, đây cậu bạn này c òn vi ết hẳn 18 trang, thực sự nể phục\", bạn Hoàng Long cho bi ết. 1. Vi ết đoạn văn ngắn (khoảng 200 ch ữ) bàn v ề Bản Lĩnh “Bản lĩnh” – hai t ừ thật đơn giản nhưng bạn bi ết không, hàm ch ứa trong đó là cả một qu á trình quy ết tâm kiên cư ờng không ngại gian khổ. Gi ống như một chi ếc áo gi áp được tôi luyện t ừ nh ững nguyên liệu b ền v ững nhất, bản lĩnh giúp ta không c òn phải lo sợ trước nh ững cơn gió to hay nh ững ngọn sóng d ữ. Vậy bản lĩnh là gì? Bản lĩnh là khả năng giải quy ết mọi chuyện một c ách bình tĩnh, thông minh và tỉnh t áo. Ngư ời bản lĩnh là ngư ời d ám đương đầu với khó khăn gian khổ. Thất bại, OK Th ời đi học ngoài nh ững thần đồng To án - L ý - Hóa ra, hội giỏi Văn cũng được nhi ều ngư ời vô cùng ngưỡng mộ. Chuyện ngồi trong ph òng thi chỉ mấy ti ếng đồng hồ mà họ mà vi ết ra được 6,7 trang giấy thi tưởng ch ừng đ ã qu á khủng khi ếp rồi, nhưng nay bạn sẽ phải shock hơn khi bi ết một nam sinh đ ã vi ết được hẳn 18 trang giấy cho một bài văn. Cụ thể mới đây, trên trang fanpage của trư ờng THPT Long Kh ánh (tỉnh Đồng Nai) đ ã đăng tải một bộ ảnh là một bài văn dài 18 trang giấy của cậu bạn Trần H ữu Thơ. Theo nh ững gì được đăng tải, bài văn này đ ã khi ến cô gi áo không khỏi ngỡ ngàng vì không bi ết phê gì vào bài thi, thậm ch í c òn cho cậu bạn s ố điểm gần tuyệt đ ối là 9,5 điểm. Đ ề bài yêu cầu nêu cảm nhận v ề một đoạn tr ích trong t ác phẩm \"Nh ững đ ứa con trong gia đình\" của Nguyễn Thi, t ừ đó rút ra suy nghĩ của mình v ề vai tr ò của gia đình trong cuộc s ống hiện nay. Với khả năng cảm thụ văn học tr ời phú của mình, H ữu Thơ đ ã khi ến nhi ều ngư ời không khỏi shock khi vi ết hẳn 18 trang giấy, đi ều đ áng nói là trang văn nào cũng trình bày sạch đẹp. Nhi ều cô cậu học tr ò cũng để lại l ời ngưỡng mộ dành cho cậu bạn. \"Thực sự là th ời đi học mình nghĩ vi ết đ ến t ờ th ứ 2 thôi là bạn đó cũng qu á giỏi rồi, đây cậu bạn này c òn vi ết hẳn 18 trang, thực sự nể phục\", bạn Hoàng Long cho bi ết. 1. Vi ết đoạn văn ngắn (khoảng 200 ch ữ) bàn v ề Bản Lĩnh “Bản lĩnh” – hai t ừ thật đơn giản nhưng bạn bi ết không, hàm ch ứa trong đó là cả một qu á trình quy ết tâm kiên cư ờng không ngại gian khổ. Gi ống như một chi ếc áo gi áp được tôi luyện t ừ nh ững nguyên liệu b ền v ững nhất, bản lĩnh giúp ta không c òn phải lo sợ trước nh ững cơn gió to hay nh ững ngọn sóng d ữ. Vậy bản lĩnh là gì? Bản lĩnh là khả năng giải quy ết mọi chuyện một c ách bình tĩnh, thông minh và tỉnh t áo. Ngư ời bản lĩnh là ngư ời d ám đương đầu với khó khăn gian khổ. Thất bại, OK Th ời đi học ngoài nh ững thần đồng To án - L ý - Hóa ra, hội giỏi Văn cũng được nhi ều ngư ời vô cùng ngưỡng mộ. Chuyện ngồi trong ph òng thi chỉ mấy ti ếng đồng hồ mà họ mà vi ết ra được 6,7 trang giấy thi tưởng ch ừng đ ã qu á khủng khi ếp rồi, nhưng nay bạn sẽ phải shock hơn khi bi ết một nam sinh đ ã vi ết được hẳn 18 trang giấy cho một bài văn. Cụ thể mới đây, trên trang fanpage của trư ờng THPT Long Kh ánh (tỉnh Đồng Nai) đ ã đăng tải một bộ ảnh là một bài văn dài 18 trang giấy của cậu bạn Trần H ữu Thơ. Theo nh ững gì được đăng tải, bài văn này đ ã khi ến cô gi áo không khỏi ngỡ ngàng vì không bi ết phê gì vào bài thi, thậm ch í c òn cho cậu bạn s ố điểm gần tuyệt đ ối là 9,5 điểm. Đ ề bài yêu cầu nêu cảm nhận v ề một đoạn tr ích trong t ác phẩm \"Nh ững đ ứa con trong gia đình\" của Nguyễn Thi, t ừ đó rút ra suy nghĩ của mình v ề vai tr ò của gia đình trong cuộc s ống hiện nay. Với khả năng cảm thụ văn học tr ời phú của mình, H ữu Thơ đ ã khi ến nhi ều ngư ời không khỏi shock khi vi ết hẳn 18 trang giấy, đi ều đ áng nói là trang văn nào cũng trình bày sạch đẹp. Nhi ều cô cậu học tr ò cũng để lại l ời ngưỡng mộ dành cho cậu bạn. \"Thực sự là th ời đi học mình nghĩ vi ết đ ến t ờ th ứ 2 thôi là bạn đó cũng qu á giỏi rồi, đây cậu bạn này c òn vi ết hẳn 18 trang, thực sự nể phục\", bạn Hoàng Long cho bi ết. 1. Vi ết đoạn văn ngắn (khoảng 200 ch ữ) bàn v ề Bản Lĩnh “Bản lĩnh” – hai t ừ thật đơn giản nhưng bạn bi ết không, hàm ch ứa trong đó là cả một qu á trình quy ết tâm kiên cư ờng không ngại gian khổ. Gi ống như một chi ếc áo gi áp được tôi luyện t ừ nh ững nguyên liệu b ền v ững nhất, bản lĩnh giúp ta không c òn phải lo sợ trước nh ững cơn gió to hay nh ững ngọn sóng d ữ. Vậy bản lĩnh là gì? Bản lĩnh là khả năng giải quy ết mọi chuyện một c ách bình tĩnh, thông minh và tỉnh t áo. Ngư ời bản lĩnh là ngư ời d ám đương đầu với khó khăn gian khổ. Thất bại,");
        bpmTpSignZone = new BpmTpSignZone();
        bpmTpSignZone.setSign("https://tpc.googlesyndication.com/simgad/3248856506927570682");
        bpmTpSignZone.setLastName("Tên người kí này 5");
        bpmTpSignZone.setTaskDefKey("st04");
        bpmTpSignZoneList.add(bpmTpSignZone);
        bpmTpSignZone = new BpmTpSignZone();
        bpmTpSignZone.setSign("https://tpc.googlesyndication.com/simgad/3248856506927570682");
        bpmTpSignZone.setLastName("Tên người kí này 6");
        bpmTpSignZone.setTaskDefKey("imageUrl");
        bpmTpSignZoneList.add(bpmTpSignZone);
        bpmTpSignZone = new BpmTpSignZone();
        bpmTpSignZone.setSign("https://tpc.googlesyndication.com/simgad/3248856506927570682");
        bpmTpSignZone.setLastName("Tên người kí này 7");
        bpmTpSignZone.setTaskDefKey("imageUrl");
        bpmTpSignZone.setPosition("Nhân viên");
        bpmTpSignZone.setSignedDate(new Date());
        bpmTpSignZoneList.add(bpmTpSignZone);
        bpmTpSignZone = new BpmTpSignZone();
        bpmTpSignZone.setSign("");
        bpmTpSignZone.setLastName("Tên người kí này 8");
        bpmTpSignZone.setTaskDefKey("imageUrl");
        bpmTpSignZoneList.add(bpmTpSignZone);
        templateUtils.setBpmTpSignZoneList(bpmTpSignZoneList);

        //List giá trị
        Map<String, VariableValueDto> mapVariableValueDto = new HashMap<>();
        //IMAGE
        VariableValueDto valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("https://tpc.googlesyndication.com/simgad/3248856506927570682");
//        valueDto.setValue("https://finance.vietstock.vn/image/DXG");
        mapVariableValueDto.put("imageUrl", valueDto);

        valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("LeCauchutoto");
        mapVariableValueDto.put("imageUrl_Name", valueDto);

        //TEXT
        valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("[\"item-01\",\"item-02\"]");
        mapVariableValueDto.put("test_select", valueDto);

        //TEXT
        valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("abc \n zzz \nbcdwwwwwwwwwwwwwwwwwwwww");
        mapVariableValueDto.put("test_select21", valueDto);

        //TEXT 2
        valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("test_test");
        mapVariableValueDto.put("test_area", valueDto);

        //TEXT 2
        valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("- Căn cứ vào nhu cầu vốn phục vụ cho đầu tư dự án dài hạn;");
        mapVariableValueDto.put("start_txa_31CanCuTaoPhieu", valueDto);

        //TEXT 2
        valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("CT CP ĐT KD BĐS Hà An");
        mapVariableValueDto.put("start_txt_33TenCongTyTangVonDieuLeName", valueDto);

        //TEST TEXT AREA
        valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("123 \n 321 \n123");
        mapVariableValueDto.put("start_txa_noiDungTrinhKy", valueDto);

        //TEST TEXT AREA
        valueDto = new VariableValueDto();
        valueDto.setType("String");
        valueDto.setValue("123 \n 321 \n123");
        mapVariableValueDto.put("start_txa_nghiaVuCuaNguoiNhanUyQuyen", valueDto);

        //NUMBER
        valueDto = new VariableValueDto();
        valueDto.setType("Long");
        valueDto.setValue(1000000);
        mapVariableValueDto.put("test_number_1", valueDto);

        //Table
        valueDto = new VariableValueDto();
        valueDto.setType("Json");
        String value = "{\"customType\":\"Table\",\"data\":{\"tableName\":\"Table1\",\"columns\":[{\"name\":\"slt_select3\",\"label\":\"Select3\"},{\"name\":\"txt_text4\",\"label\":\"Text4\"},{\"name\":\"slt_select2\",\"label\":\"Select2\"},{\"name\":\"txt_text2\",\"label\":\"Text2\"}],\"data\":[{\"slt_select3\":\"option 01\",\"txt_text4\":\"ssssss\",\"slt_select2\":\"option 01\",\"txt_text2\":\"2222222222223.34\n454545332\"},{\"slt_select2\":\"option 02\",\"\":\"\",\"txt_text2\":\"2222222222223.34454545332\"}]}}";
//        String value = "{\"customType\":\"Table\",\"data\":{\"tableName\":\"Table1\",\"columns\":[{\"name\":\"slt_select3\",\"label\":\"Select3\"},{\"name\":\"txt_text4\",\"label\":\"Text4\"},{\"name\":\"slt_select2\",\"label\":\"Select2\"},{\"name\":\"txt_text2\",\"label\":\"Text2\"}],\"data\":[{\"slt_select3\":\"option 01\",\"txt_text4\":\"ssssss\",\"slt_select2\":\"option 01\",\"txt_text2\":\"\"},{\"slt_select2\":\"option 02\",\"\":\"\",\"txt_text2\":\"\"},{\"slt_select2\":\"option 02\",\"\":\"\",\"txt_text2\":\"\"}]}}";
        valueDto.setValue(value);
        mapVariableValueDto.put("test_table", valueDto);

        //Table
        valueDto = new VariableValueDto();
        valueDto.setType("Json");
        value = "{\"customType\":\"Table\",\"data\":{\"tableName\":\"Số tiền hoàn ứng trong kỳ\",\"columns\":[{\"name\":\"lbl_dienGiai\",\"label\":\"Diễn giải\"},{\"name\":\"lbl_soChungTuChi\",\"label\":\"Số chứng từ chi\"},{\"name\":\"lbl_thoiGian\",\"label\":\"Thời gian\"},{\"name\":\"lbl_soChungTuhoaDon\",\"label\":\"Số chứng từ/Hóa đơn\"},{\"name\":\"lbl_soTien\",\"label\":\"Số tiền\"},{\"name\":\"lbl_taiKhoanHachToan\",\"label\":\"Tài khoản hạch toán\"},{\"name\":\"lbl_type\",\"label\":\"Type\"},{\"name\":\"lbl_companycode\",\"label\":\"companyCode\"},{\"name\":\"txt_dienGiaiTrongKy\",\"label\":\"Diễn giải trong kỳ\"},{\"name\":\"slt_soChungTuChiTrongKy\",\"label\":\"Số chứng từ chi trong kỳ\"},{\"name\":\"dtm_thoiGianTrongKy\",\"label\":\"Thời gian trong kỳ\"},{\"name\":\"txt_soChungTuhoaDon\",\"label\":\"Số chứng từ/Hóa đơn\"},{\"name\":\"txt_soTienTrongKy\",\"label\":\"Số tiền trong kỳ\"},{\"name\":\"txt_taiKhoanHachToan\",\"label\":\"Tài khoản hạch toán\"},{\"name\":\"txt_text2\",\"label\":\"Text2\"},{\"name\":\"txt_text4\",\"label\":\"Text4\"}],\"data\":[{\"lbl_dienGiai\":\"\",\"lbl_soChungTuChi\":\"\",\"lbl_thoiGian\":\"\",\"lbl_soChungTuhoaDon\":\"\",\"lbl_soTien\":\"\",\"lbl_taiKhoanHachToan\":\"\",\"lbl_type\":\"\",\"lbl_companycode\":\"\",\"txt_dienGiaiTrongKy\":\"\",\"slt_soChungTuChiTrongKy\":\"2355000312\",\"txt_soChungTuhoaDon\":\"\",\"dtm_thoiGianTrongKy\":\"\",\"txt_soTienTrongKy\":\"1000\",\"txt_taiKhoanHachToan\":\"\",\"txt_text2\":\"B\",\"txt_text4\":\"1000\",\"txt_soTienTrongKy_currencyText\":\"Một nghìn Việt Nam Đồng\",\"slt_soChungTuChiTrongKy_opt\":[{\"value\":2355000312,\"label\":\"2355000312\",\"options\":{\"documentNumber\":2355000312,\"documentDate\":\"28/10/2023\",\"balanceAdvance\":10000000,\"description\":\"Tạm ứng công tác\"}}]}]}}";
        valueDto.setValue(value);
        mapVariableValueDto.put("start_tbl_soTienHoanUngTrongKy", valueDto);

        //Table
        valueDto = new VariableValueDto();
        valueDto.setType("Json");
        value = "{\"customType\":\"Table\",\"data\":{\"tableName\":\"Số dư tạm ứng cuối kỳ\",\"columns\":[{\"name\":\"txt_\",\"label\":\"value0\"},{\"name\":\"txt_\",\"label\":\"value1\"},{\"name\":\"txt_\",\"label\":\"value2\"},{\"name\":\"txt_\",\"label\":\"value3\"},{\"name\":\"txt_soChungTuChi3\",\"label\":\"Số chứng từ chi(3)\"},{\"name\":\"dtm_thoiGianCuoiKy\",\"label\":\"Thời gian cuối kỳ\"},{\"name\":\"txt_soTienCuoiKy\",\"label\":\"Số tiền cuối kỳ\"},{\"name\":\"txt_dienGiaiCuoiKy\",\"label\":\"Diễn giải cuối kỳ\"}],\"data\":[{\"txt_soChungTuChi3\":\"\",\"dtm_thoiGianCuoiKy\":\"\",\"txt_soTienCuoiKy\":\"1000000\",\"txt_dienGiaiCuoiKy\":\"\"}]}}";
        valueDto.setValue(value);
        mapVariableValueDto.put("start_tbl_soDuTamUngCuoiKy", valueDto);

        //Table
        valueDto = new VariableValueDto();
        valueDto.setType("Json");
        value = "{\"customType\":\"Table\",\"data\":{\"tableName\":\"Table1\",\"columns\":[{\"name\":\"slt_select3\",\"label\":\"Select3\"},{\"name\":\"txt_text4\",\"label\":\"Text4\"},{\"name\":\"slt_select2\",\"label\":\"Select2\"},{\"name\":\"txt_text2\",\"label\":\"Text2\"}],\"data\":[{\"slt_select3\":\"option 01\",\"txt_text4\":\"ssssss\",\"slt_select2\":\"\",\"txt_text2\":\"\"},{\"slt_select2\":\"option 02\",\"\":\"\",\"txt_text2\":\"2222222222223454545332\"},{\"slt_select2\":\"option 02\",\"\":\"\",\"txt_text2\":\"\", \"txt_text3\":\"\"}]}}";
        valueDto.setValue(value);
        mapVariableValueDto.put("test_table_1", valueDto);

        //Xóa table với biến động
        valueDto = new VariableValueDto();
        valueDto.setType("Json");
        valueDto.setValue("7");
        mapVariableValueDto.put("deleteTableIndex", valueDto);

        //Matrix
        valueDto = new VariableValueDto();
        valueDto.setType("Json");
        value = "{\"customType\":\"Table\",\"data\":{\"tableName\":\"Matrix1\",\"columns\":[{\"name\":\"\",\"label\":\"\"},{\"name\":\"\",\"label\":\"Cột 1\"},{\"name\":\"\",\"label\":\"Cột 2\"}],\"data\":[{\"\":\"Dòng 1\",\"txt_text3\":\"Sản phẩm\",\"chk_checkbox1\":[\"item-01\",\"item-02\"]}]}}";
        valueDto.setValue(value);
        mapVariableValueDto.put("mtx_matrix1", valueDto);

        //Matrix
        valueDto = new VariableValueDto();
        valueDto.setType("Json");
        value = "{\"customType\":\"Table\",\"data\":{\"tableName\":\"3.6 Thông tin phương án đầu tư trái phiếu_copy1\",\"columns\":[{\"name\":\"txt_\",\"label\":\"\"},{\"name\":\"txt_cot1\",\"label\":\"Cột 1\"},{\"name\":\"\",\"label\":\"Cột 2\"},{\"name\":\"\",\"label\":\"Cột 3\"},{\"name\":\"txt_cot4\",\"label\":\"Cột 4\"},{\"name\":\"txt_cot5\",\"label\":\"Cột 5\"}],\"data\":[{\"lbl_noiDungcopy1\":\"Đơn vị phát hành/chủ sở hữu của mã TP/CP đầu tư\",\"txt_bp1Name\":\"NGUYỄN DUY LUYỆN\",\"txt_bp2Name\":\"LÊ ĐÌNH TÚ\",\"txt_bp3Name\":\"NINH SƠN HÀ\",\"txt_bp4Name\":\"LÊ THỊ PHƯƠNG LOAN\",\"lbl_38ThongTinToChucPhatHanhcopy1\":\"Đơn vị được ủy quyền phát hành tp/cp\",\"txt_391ThongTinToChucPhatHanhName\":\"NGUYỄN DUY LUYỆN\",\"txt_392ThongTinToChucPhatHanhName\":\"LÊ ĐÌNH TÚ\",\"txt_393ThongTinToChucPhatHanhName\":\"NINH SƠN HÀ\",\"txt_394ThongTinToChucPhatHanhName\":\"LÊ THỊ PHƯƠNG LOAN\",\"lbl_\":\"\",\"txt_361bpName\":\"NGUYỄN DUY LUYỆN\",\"txt_362bpName\":\"NGUYỄN THẾ DUY\",\"txt_363bpName\":\"VĂN PHƯƠNG UYÊN\",\"txt_364bpName\":\"NGUYỄN PHẠM QUỐC PHONG\"}]}}";
        valueDto.setValue(value);
        mapVariableValueDto.put("start_mtx_36ThongTinPhuongAnDauTuTraiPhieucopy1", valueDto);

        //Xử lý đổ dữ liệu
        try {
            templateUtils.handlerDataNormal(mapVariableValueDto);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private Tbl getTemplateTable(MainDocumentPart documentPart, int tableIndex) {
        return (Tbl) getAllElementFromObject(documentPart, Tbl.class).get(tableIndex);
    }

    private List<String> getSubParamsConfig(String content, String regex) throws Exception {
        List<String> subItemConfig = new ArrayList<>();
        Pattern pattern = Pattern.compile(regex);
        Matcher m = pattern.matcher(content);
        while (m.find()) {
            subItemConfig.add(m.group(1));
        }
        return subItemConfig;
    }

    public List<Map<String, Object>> getAllParamsConfig() throws Exception {
        List<Map<String, Object>> lstConfigs = new ArrayList<>();
        if (template != null) {
            String regex = ".*?(\\@\\{.*?\\}).*?";
            Pattern pattern = Pattern.compile(regex);
            Matcher m;

            var ref = new Object() {
                P paragraph;
            };
            List<Object> paragraphs = getAllElementFromObject(template.getMainDocumentPart(), P.class);
            String tmpConfig;
            for (Object par : paragraphs) {
                ref.paragraph = (P) par;
                tmpConfig = StringUtil.nvl(ref.paragraph, "");
                m = pattern.matcher(tmpConfig);
                List<String> finalConfig;
                if (m.matches()) {//Lọc ra chuỗi cấu hình
                    if (tmpConfig.split("\\@\\{").length >= 3) {//Có nhiều cầu hình trong cùng 1 hàng
                        finalConfig = getSubParamsConfig(tmpConfig, regex);
                    } else {
                        finalConfig = getSubParamsConfig(tmpConfig, ".*(\\@\\{.*\\}).*");
                    }

                    //Conver ra object Config
                    finalConfig.forEach(finalConfigItem -> {
                        Map<String, Object> configItem = new HashMap<>();
                        String finalConfigItemTmp = finalConfigItem.substring(1);
                        //rowConfigs:[(value: "txt_text2", numberToWord:true)] => rowConfigs:[{value: "txt_text2", numberToWord:true}] => dong nay khong can nua
                        finalConfigItemTmp = finalConfigItemTmp.replaceAll("\\(", "{").replaceAll("\\)", "}");
                        System.out.println("handlerDataNormal value =" + finalConfigItemTmp);
                        Map<String, Object> objectConfig = getMapObjectFromJson(finalConfigItemTmp);
                        String type = StringUtil.nvl(objectConfig.get("type"), "").trim().toUpperCase();
                        if (objectConfig != null) {
                            if ("IMAGE".equals(type)) {
                                objectConfig.put("paragraph", ref.paragraph);
                            } else if (type.isEmpty()) {
                                objectConfig.put("paragraph", ref.paragraph);
                                objectConfig.put("keyReplace", StringUtil.nvl(finalConfigItem, ""));
                            } else {
                                objectConfig.put("keyReplace", StringUtil.nvl(finalConfigItem, ""));
                            }
                            configItem.put("objectConfig", objectConfig);
                            lstConfigs.add(configItem);
                        }
                    });
                }
            }
        }
        return lstConfigs;
    }

    public void searchAndReplace(WordprocessingMLPackage template, Map<String, String> mParams) throws Exception {
        if (template != null) {
            Docx4JSRUtil.searchAndReplace(template, mParams);
        }
    }

    public void removeTables(List<Tbl> lstTableRemove) {
        if (lstTableRemove != null) {
            lstTableRemove.forEach(tableRemoveItem -> {
                if (tableRemoveItem != null) {
                    List<Object> rows = getAllElementFromObject(tableRemoveItem, Tr.class);
                    if (rows != null && rows.size() > 0) {
                        tableRemoveItem.getContent().removeAll(rows);
                    }
                }
            });
        }
    }

    public Object getObjInDocxTable(JAXBElement element) {
        if((element).getValue() instanceof Tbl) {
            Tbl table = (Tbl) (element).getValue();
            if (table.getContent().size() > 0) {
                for (Object wml : table.getContent()) {
                    if (wml instanceof Tr) {
                        Tr tr = (((Tr) wml));
                        List<Object> element1 = tr.getContent();
                        if (!element1.isEmpty()) {
                            for (Object obj : element1) {
                                if (obj instanceof JAXBElement) {
                                    JAXBElement objJax = (JAXBElement) obj;
                                    List<Object> tcs = ((Tc) (objJax).getValue()).getContent();
                                    if (!tcs.isEmpty()) {
                                        for (Object tc : tcs) {
                                            convertLineBreakDocx(tc);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return "";
    }

    public void convertLineBreakDocx(WordprocessingMLPackage template) throws JAXBException, XPathBinderAssociationIsPartialException {
        //Xử lý /n trong docx
        ObjectFactory factory = Context.getWmlObjectFactory();
        MainDocumentPart mainDocumentPart = template.getMainDocumentPart();
        List<Object> paragraphs = mainDocumentPart.getContent();
        Br lineBreak = factory.createBr();
        int count = 0;
        do { // Duyệt list content
            Object obj = paragraphs.get(count);

            //Lấy đúng obj trong table vì nếu trong table thì sẽ là JAXBElement;
            if (obj instanceof JAXBElement) {
                obj = getObjInDocxTable((JAXBElement) obj);
            }
            String[] objString = obj.toString().split("\n");

            if (objString.length > 1 && obj instanceof P) {
                R run = factory.createR();
                for (int i = 0; i < objString.length; i++) {
                    Text text = factory.createText();
                    text.setValue(objString[i].replaceAll("null", ""));
                    run.getContent().add(text);
                    if (i <= objString.length - 1) { // Add 1 dòng mới docx
                        run.getContent().add(lineBreak);
                    }

                    //Set lại style gốc của file docx .(Chỉ sửa text)
                    if (obj instanceof P && ((P) obj).getContent().get(0) instanceof R) {
                        R runOld = (R) ((P) obj).getContent().get(0);
                        run.setRPr(runOld.getRPr());
                        run.setRsidDel(runOld.getRsidDel());
                        run.setRsidR(runOld.getRsidRPr());
                        run.setRsidRPr(runOld.getRsidRPr());
                    }

                }
                //Add text mới
                ((P) obj).getContent().clear();
                ((P) obj).getContent().add(run);
            }
            count++;
        } while (count != paragraphs.size());
    }

    public void convertLineBreakDocx(Object obj) {
        ObjectFactory factory = Context.getWmlObjectFactory();
        Br lineBreak = factory.createBr();
        String[] objString = obj.toString().split("\n");
        if (objString.length > 1 && obj instanceof P) {
            R run = factory.createR();
            for (int i = 0; i < objString.length; i++) {
                Text text = factory.createText();
                text.setValue(objString[i].replaceAll("null", ""));
                run.getContent().add(text);
                if (i <= objString.length - 1) { // Add 1 dòng mới docx
                    run.getContent().add(lineBreak);
                }

                //Set lại style gốc của file docx .(Chỉ sửa text)
                if (obj instanceof P && ((P) obj).getContent().get(0) instanceof R) {
                    R runOld = (R) ((P) obj).getContent().get(0);
                    run.setRPr(runOld.getRPr());
                    run.setRsidDel(runOld.getRsidDel());
                    run.setRsidR(runOld.getRsidRPr());
                    run.setRsidRPr(runOld.getRsidRPr());
                }

            }
            //Add text mới
            ((P) obj).getContent().clear();
            ((P) obj).getContent().add(run);
        }
    }

    public void saveToDocx(WordprocessingMLPackage template, String pathFile) throws Exception {
        convertLineBreakDocx(template);
        if (pathFile != null) {
            Docx4J.save(template, new File(pathFile));
        }
    }

    public void saveToPdf(WordprocessingMLPackage template, String pathFile) throws Exception {
        convertLineBreakDocx(template);
        if (pathFile != null) {
            Docx4J.toPDF(template, new FileOutputStream(pathFile));
        }
    }

    public byte[] saveToBytesDocx(WordprocessingMLPackage template) throws Exception {
        convertLineBreakDocx(template);
        byte[] docBytes = null;
        if (template != null) {
            ByteArrayOutputStream outStream = new ByteArrayOutputStream();
            Docx4J.save(template, outStream);
            docBytes = outStream.toByteArray();
        }
        return docBytes;
    }

    private String convertArrStringToString(String newValue) {
        try {
            if (newValue != null && newValue.indexOf("[") > -1) {
                System.out.println(newValue);
                return StringUtil.nvl(newValue, "").replaceAll("\\[", "").replaceAll("\\]", "").replaceAll("\"", "").replaceAll(",", ", ");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return newValue;
        }
        return newValue;
    }

    private String replaceSpecialXmlChar(String value) { // Replace 1 số biến đặc biệt để hiển thị trong xml
        try {
            if (value.contains("\n")) {
                value = value.replaceAll("\\n", "&#xA;");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    private Object findObjectInMapVariable(Map<String, VariableValueDto> mVariable, String value) {
        var ref = new Object() {
            Object newValue = null;
            String[] lstValue = new String[0];
        };
        //Bắt đầu @test_select mới tìm trong lstVariable
        if (value != null && value.startsWith("@")) {
            value = value.substring(1);
            if (value.indexOf(".") > -1) {//Trường hợp lấy giá trị trong matrix @mtx_matrix1.txt_text2
                ref.lstValue = value.split("\\.");
                value = ref.lstValue[0];
            }
            if (mVariable != null) {
                try {
                    String finalValue = value;
                    //Thử tìm key
                    Set<String> lstProperties = mVariable.keySet();
                    String keyMatch = lstProperties.stream().filter(s -> s.endsWith(finalValue)).findFirst().orElse(null);
                    if (keyMatch != null) {
                        //Các biến thông thường replace lại nếu là dạng chuổi mảng string
                        if (mVariable.get(keyMatch) != null) {
                            ref.newValue = mVariable.get(keyMatch).getValue();
                            try {
                                if (ref.newValue != null && mVariable.get(keyMatch).getType() != null
                                        && mVariable.get(keyMatch).getType().equalsIgnoreCase("String")) {
                                    System.out.println(ref.newValue);
                                    ref.newValue = convertArrStringToString(StringUtil.nvl(ref.newValue, ""));
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                        //Matrix thì cần xử lý tiếp type của matrix, table luôn là Json
                        if (ref.newValue != null && ref.lstValue.length >= 2) {
                            Map<String, Object> objectMap = getMapObjectFromJson((String) ref.newValue);
                            if (objectMap != null) {
                                Map<String, Object> data = (Map<String, Object>) objectMap.get("data");
                                if (data != null) {
                                    List<Map<String, Object>> lstData = (List<Map<String, Object>>) data.get("data");
                                    if (lstData != null && lstData.size() > 0) {//Dữ liệu của matrix luôn chị lưu ở vị trí 0
                                        ref.newValue = lstData.get(0).get(ref.lstValue[1]);
                                        if (ref.newValue instanceof List || ref.newValue instanceof String) {
                                            ref.newValue = convertArrStringToString(StringUtil.nvl(ref.newValue, ""));
                                        }
                                    }
                                }
                            }

                        }
                        //trả về luôn
                        return ref.newValue;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    throw e;
                }
            }
        }
        return ref.newValue;
    }

    //Thay thế ảnh @{type:"IMAGE", targetWidth:200, targetHeight:200, imageUrl:"abc.jpg"}
    private void handlerImageNormal(List<Map<String, Object>> lstConfigImage, Map<String, VariableValueDto> mVariable) {
        if (template != null) {
            lstConfigImage.forEach(itemConfigImage -> {
                P paragraph = (P) itemConfigImage.get("paragraph");
                if (paragraph != null) {
                    Integer targetWidth = getIntValueFromMap(itemConfigImage, "targetWidth", 150);
                    Integer targetHeight = getIntValueFromMap(itemConfigImage, "targetHeight", 150);
                    String imageUrl = StringUtil.nvl(itemConfigImage.get("imageUrl"), "");
                    String signatoryName = StringUtil.nvl(itemConfigImage.get("signatoryName"), "");
                    String isExternalUrl = StringUtil.nvl(itemConfigImage.get("isExternalUrl"), "false");
                    String isClear = StringUtil.nvl(itemConfigImage.get("isClear"), "true");
                    String type = StringUtil.nvl(itemConfigImage.get("type"), "").trim().toUpperCase();
                    if (imageUrl.startsWith("@")) {//Đặt biến động
                        //Lấy tên người kí
                        if (type.isEmpty()) {
                            if (signatoryName.isEmpty()) {//theo biến ảnh người kí
                                signatoryName = StringUtil.nvl(findObjectInMapVariable(mVariable, imageUrl.replace("\\@", "") + "_Name"), "");
                            } else if (signatoryName.startsWith("@")) {//Đặt biến động
                                signatoryName = StringUtil.nvl(findObjectInMapVariable(mVariable, signatoryName), "");
                            }
                        }
                        //Lấy link ảnh
                        imageUrl = StringUtil.nvl(findObjectInMapVariable(mVariable, imageUrl), "");
                    }
                    try {
                        addImageToPara(template, new ObjectFactory(), paragraph,
                                "", 0, 1, isExternalUrl,
                                imageUrl,
                                targetWidth,
                                targetHeight, signatoryName, isClear, fileManager, credentialHelper, bucket);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            });
        }
    }

    private void handlerSignImageNormal(List<Map<String, Object>> lstConfigSign, List<BpmTpSignZone> lstTpSignZone) {
        if (template != null && lstTpSignZone != null && lstConfigSign != null) {
            Map<String, Integer> savePosition = new HashMap<>();
            lstTpSignZone.forEach(itemTpSignZone -> {
                //Tìm xem column có cấu hình không
//                Map<String, Object> itemConfigImage = lstConfigSign.stream().filter(signConfisItem -> StringUtil.nvl(signConfisItem.get("imageUrl"), "").endsWith(itemTpSignZone.getTaskDefKey())).findAny().orElse(null);
                Map<String, Object> itemConfigImage = null;
                List<Map<String, Object>> lstItemConfigImage = lstConfigSign.stream().filter(signConfisItem -> StringUtil.nvl(signConfisItem.get("imageUrl"), "").endsWith("@" + itemTpSignZone.getTaskDefKey())).collect(Collectors.toList());
                //Lấy lần lượt cấu hình chữ kí, nếu có nhiều vị trí sẽ insert lần lượt
                if (lstItemConfigImage != null && lstItemConfigImage.size() > 0) {
                    Integer currentPosition = savePosition.containsKey(itemTpSignZone.getTaskDefKey()) ? savePosition.get(itemTpSignZone.getTaskDefKey()) : -1;
                    Integer nextPosition;
                    if (lstItemConfigImage.size() == 1) {
                        nextPosition = 0;
                    } else if ((currentPosition + 1) < lstItemConfigImage.size()) {
                        nextPosition = currentPosition + 1;
                    } else {
                        nextPosition = 0;
                    }
                    savePosition.put(itemTpSignZone.getTaskDefKey(), nextPosition);
                    itemConfigImage = lstItemConfigImage.get(nextPosition);
                }

                if (itemConfigImage != null) {//Tìm thấy cấu hình
                    P paragraph = (P) itemConfigImage.get("paragraph");

                    Integer targetWidth = getIntValueFromMap(itemConfigImage, "targetWidth", 150);
                    Integer targetHeight = getIntValueFromMap(itemConfigImage, "targetHeight", 150);
                    String imageUrl = StringUtil.nvl(itemTpSignZone.getSign(), "");
                    String signatoryName = StringUtil.nvl(itemConfigImage.get("signatoryName"), "");
                    String position = StringUtil.nvl(itemConfigImage.get("position"), "");
                    String orgAssigneeTitle = StringUtil.nvl(itemConfigImage.get("orgAssigneeTitle"), "");
                    String signDate = StringUtil.nvl(itemConfigImage.get("signDate"), "");
                    String isExternalUrl = StringUtil.nvl(itemConfigImage.get("isExternalUrl"), "false");
                    String isClear = StringUtil.nvl(itemConfigImage.get("isClear"), "false");
                    String addPosition = StringUtil.nvl(itemConfigImage.get("addPosition"), "true");
                    String addSignDate = StringUtil.nvl(itemConfigImage.get("addSignDate"), "true");
                    String type = StringUtil.nvl(itemConfigImage.get("type"), "").trim().toUpperCase();
                    String addSignImage = StringUtil.nvl(itemConfigImage.get("addSignImage"), "true");
                    String addSignatoryName = StringUtil.nvl(itemConfigImage.get("addSignatoryName"), "true");
                    String addSignAction = StringUtil.nvl(itemConfigImage.get("addSignAction"), "true");
                    if (type.isEmpty()) {
                        if (signatoryName.isEmpty()) {//lấy tên người
                            // full name
                            signatoryName = StringUtil.nvl(itemTpSignZone.getLastName(), "") + " " + StringUtil.nvl(itemTpSignZone.getFirstName(), "");
                        }

                        if (position.isEmpty()) {//lấy chức vụ
                            // full name
                            position = StringUtil.nvl(itemTpSignZone.getPosition(), "");
                        }

                        if (!ValidationUtils.isNullOrEmpty(itemTpSignZone.getOrgAssigneeTitle())) { //lấy chức vụ orgAssignee
                            orgAssigneeTitle = "TUQ " + StringUtil.nvl(itemTpSignZone.getOrgAssigneeTitle(), "");
                        }

                        if (signDate.isEmpty()) {//lấy ngày kí
                            // full name
                            signDate = StringUtil.nvl(StringUtil.format(itemTpSignZone.getSignedDate(), "dd/MM/yyyy HH:mm"), "");
                        }
                    }

                    // check theo bpm_template_print_config_user
                    if (!ValidationUtils.isNullOrEmpty(lstConfigUser)) {
                        BpmTemplatePrintConfigUser configUser = lstConfigUser.stream().filter(e -> e.getUsername().equalsIgnoreCase(itemTpSignZone.getEmail())).findFirst().orElse(null);
                        if (configUser != null) {
                            addPosition = configUser.getAddTitle();
                            addSignDate = configUser.getAddSignDate();
                            addSignImage = configUser.getAddSignImage();
                            addSignatoryName = configUser.getAddName();
                            addSignAction = configUser.getAddSignAction();
                        }
                    }

                    if (addPosition != null && addPosition.equalsIgnoreCase("FALSE")) {
                        orgAssigneeTitle = "";
                        position = "";
                    }
                    if (addSignDate != null && addSignDate.equalsIgnoreCase("FALSE")) {
                        signDate = "";
                    }
                    String signAction = "";
                    if (addSignAction != null && addSignAction.equalsIgnoreCase("true") && itemTpSignZone.getChartNodeLevel() != null) {
                        switch (itemTpSignZone.getChartNodeLevel()) {
                            case "0":
                                signAction = "Đệ trình";
                                break;
                            case "1":
                            case "2":
                            case "3":
                                signAction = "Phê duyệt";
                                break;
                            default:
                                signAction = "Xét duyệt";
                                break;
                        }
                    }

                    try {
                        addImageToPara(template, new ObjectFactory(), paragraph,
                                "", 0, 1, isExternalUrl,
                                imageUrl,
                                targetWidth,
                                targetHeight,
                                signatoryName, position, orgAssigneeTitle, signDate, 10 * 2,
                                isClear, fileManager, credentialHelper, addSignImage, addSignatoryName, bucket, signAction);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            });
        }
    }

    private Map<String, String> handlerTextOrNumberNormal(WordprocessingMLPackage template, List<Map<String, Object>> lstConfigTextNumber,
                                                          Map<String, VariableValueDto> mVariable) throws IOException {
        Map<String, String> mParams = new HashMap<>();
        if (template != null) {
            lstConfigTextNumber.forEach(item -> {
                String type = StringUtil.nvl(item.get("type"), "").toUpperCase();
                String keyReplace = StringUtil.nvl(item.get("keyReplace"), "");
                var ref = new Object() {
                    String value = StringUtil.nvl(item.get("value"), "");
                    Object newValue = null;
                };

                //Thử tìm lại trong lstVariable nếu có thì thay thế value bằng giá trị mới trong lstVariable
                ref.newValue = findObjectInMapVariable(mVariable, ref.value);
                if (ref.newValue != null) {
//                    ref.value = StringUtil.nvl(replaceSpecialXmlChar(String.valueOf(ref.newValue)), "");
                    ref.value = String.valueOf(ref.newValue);
                } else {
                    // Xóa các biến ko có value
                    ref.value = "";
                }
                switch (type) {
                    case "NUMBER":
                        if (ref.newValue == null) {
                            ref.newValue = ref.value;
                        }
                        ref.value = getFormatNumberFromMap(item, StringUtil.nvl(ref.newValue, ""), "");
                        mParams.put(keyReplace, ref.value);
                        break;
                    case "TEXT":
                        mParams.put(keyReplace, ref.value);
                        break;
                    default:
                }
            });
            //Thực hiện thay thế
//            searchAndReplace(template, mParams);
        }
        return mParams;
    }

    private void handlerTableSignNormal(WordprocessingMLPackage template, List<Map<String, Object>> lstConfigTable,
                                        Map<String, VariableValueDto> mVariable, List<BpmTpSignZone> bpmTpSignZoneList) throws Exception {
        if (template != null) {
            var ref = new Object() {
                MainDocumentPart documentPart = template.getMainDocumentPart();
            };
            List<Object> elements = template.getMainDocumentPart().getJaxbElement().getContent();
            lstConfigTable.forEach(tableItem -> {
                Integer tableIndex = 0;
                tableIndex = getIntValueFromMap(tableItem, "tableIndex", -1);
                TableSignTemplate tableTemplate = null;
                try {
                    if (elements.size() > 0) {//Có vị trí mới thao tác dữ liệu
                        //Tự tìm vị trí của table thao tác không cần config sẵn nếu config sẵn thì lấy theo config
                        if (tableIndex > -1) {
                            tableTemplate = new TableSignTemplate(template, getTemplateTable(ref.documentPart, tableIndex), fileManager, credentialHelper);
                        } else {
                            tableIndex = IntStream.range(0, elements.size())
                                    .filter(i -> (elements.get(i) != null && elements.get(i).toString().indexOf((String) tableItem.get("keyReplace")) > -1))
                                    .findFirst()
                                    .orElse(-1) + 1;
                            tableTemplate = new TableSignTemplate(template, (Tbl) ((JAXBElement) elements.get(tableIndex)).getValue(), fileManager, credentialHelper);
                        }

                        //Add page break không
                        boolean addPageBreak = false;
                        if (tableItem.containsKey("createPageBreak")
                                && StringUtil.nvl(tableItem.containsKey("createPageBreak"), "FALSE").equalsIgnoreCase("TRUE")) {
                            addPageBreak = true;
                        }
                        //Đổ dữ liệu
                        tableTemplate.fillWithData(bpmTpSignZoneList, addPageBreak, lstConfigUser, bucket);
                    }

                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
    }

    private void handlerTableNormal(WordprocessingMLPackage template, List<Map<String, Object>> lstConfigTable,
                                    Map<String, VariableValueDto> mVariable) throws Exception {
        if (template != null) {
            var ref = new Object() {
                MainDocumentPart documentPart = template.getMainDocumentPart();
                ArrayList<Integer> deleteTables = new ArrayList<>();
                List<Tbl> tableRemoves = new ArrayList<>();
            };
            List<Object> elements = template.getMainDocumentPart().getJaxbElement().getContent();
            lstConfigTable.forEach(tableItem -> {
                String value = StringUtil.nvl(tableItem.get("value"), "");
                String dynamicTitle = StringUtil.nvl(tableItem.get("dynamicTitle"), "false");
                String preFix = StringUtil.nvl(tableItem.get("prefix"), "");
                String tableName = StringUtil.nvl(tableItem.get("tableName"), "");
                String addFooter = StringUtil.nvl(tableItem.get("addFooter"), "false");
                Integer tableIndex = 0, totalCols = 0;
                tableIndex = getIntValueFromMap(tableItem, "tableIndex", -1);
                totalCols = getIntValueFromMap(tableItem, "totalCols", 0);
                TableTemplate tableTemplate = null;
                Map<String, String> mHeader = new HashMap<>();
                List<Map<String, String>> lstRowData = new ArrayList();
                Map<String, String> mFooter = new HashMap<>();
                try {
                    if (elements.size() > 0) {//Có vị trí mới thao tác dữ liệu
                        //Tự tìm vị trí của table thao tác không cần config sẵn nếu config sẵn thì lấy theo config
                        if (tableIndex > -1) {
                            tableTemplate = new TableTemplate(getTemplateTable(ref.documentPart, tableIndex), addFooter);
                        } else {
                            tableIndex = IntStream.range(0, elements.size())
                                    .filter(i -> (elements.get(i) != null && elements.get(i).toString().indexOf((String) tableItem.get("keyReplace")) > -1))
                                    .findFirst()
                                    .orElse(-1) + 1;
                            tableTemplate = new TableTemplate((Tbl) ((JAXBElement) elements.get(tableIndex)).getValue(), addFooter);
                        }

                        if (value.equalsIgnoreCase("TEST")) {
                            for (int i = 1; i <= totalCols; i++) {
                                mHeader.put(preFix + i, "Test fill header" + i);
                            }
                            for (int row = 0; row < 7; row++) {
                                Map<String, String> rowItem = new HashMap<>();
                                for (int i = 1; i <= totalCols; i++) {
                                    rowItem.put(preFix + i, "Test fill row" + row);
                                }
                                lstRowData.add(rowItem);
                            }
                        } else {
                            //Thử tìm lại trong lstVariable nếu có thì thay thế value bằng giá trị mới trong lstVariable
                            Object newValue = findObjectInMapVariable(mVariable, value);
                            if (newValue != null) {
                                Map<String, Object> objectMap = getMapObjectFromJson((String) newValue);
                                Map<String, Object> data = null;
                                if (objectMap != null) {
                                    // Trường hợp replace table từ api các phân hệ
                                    if (objectMap.get("data") instanceof ArrayList) {
                                        data = objectMap;
                                    } else {
                                        data = (Map<String, Object>) objectMap.get("data");
                                    }
                                    if (data != null) {
                                        List<Map<String, String>> lstData = (List<Map<String, String>>) data.get("data");
                                        if (lstData != null) {//Dữ liệu của table
                                            //Tiêu đề động thì row đầu là header
                                            if (dynamicTitle.equalsIgnoreCase("TRUE") && lstData.size() > 0) {
                                                mHeader = lstData.get(0);
                                                if (lstData.size() >= 2) {
                                                    lstRowData = lstData.subList(1, lstData.size());
                                                }
                                            } else {
                                                lstRowData = lstData;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        //Xử lý Cấu hình đặc biệt với từng cell data @{type:"TABLE", tableIndex:4, value:"@test_table", dynamicTitle:false, rowConfigs:[(value: "txt_text2", format:"#,###,###,###", numberToWord:true)]}
                        if (tableItem.containsKey("rowConfigs")) {
                            List<Map<String, Object>> rowConfigs = (List<Map<String, Object>>) tableItem.get("rowConfigs");
                            if (lstRowData != null && lstRowData.size() > 0 && rowConfigs != null && rowConfigs.size() > 0) {
                                lstRowData.forEach(rowDataItem -> {
                                    Set<String> lstProperties = rowDataItem.keySet();
                                    for (String key : lstProperties) {
                                        //Tìm xem column có cấu hình không
                                        Map<String, Object> objectMap = rowConfigs.stream().filter(rowConfis -> StringUtil.nvl(rowConfis.get("value"), "").equals(key)).findAny().orElse(null);
                                        if (objectMap != null) {//Nếu có cấu hình thử format lại
                                            String newValue = getFormatNumberFromMap(objectMap, StringUtil.nvl(rowDataItem.get(key), "0"), "@{deletedValue}");
                                            if (newValue != null) {
                                                rowDataItem.put(key, newValue);
                                            }
                                        }
                                    }
                                });
                            }
                        }
                        //Add page break không
                        boolean addPageBreak = false;
                        if (tableItem.containsKey("createPageBreak")
                                && StringUtil.nvl(tableItem.containsKey("createPageBreak"), "FALSE").equalsIgnoreCase("TRUE")) {
                            addPageBreak = true;
                        }
                        //Đổ dữ liệu
                        tableTemplate.fillWithData(mHeader, lstRowData, mFooter, addPageBreak);

                        //Lọc các cấu hình xóa table
                        if (tableItem.containsKey("deleteTableIndex") || tableItem.containsKey("deleteTable")) {
                            Integer deleteTableIndex = -1;
                            if (tableItem.containsKey("deleteTable")) {// deleteTable: true tự tính toán table index để xóa
                                String deleteTable = StringUtil.nvl(tableItem.get("deleteTable"), "");
                                if (deleteTable.startsWith("@")) {//Đặt biến động
                                    deleteTable = StringUtil.nvl(findObjectInMapVariable(mVariable, deleteTable), "FALSE");
                                }
                                if (deleteTable.equalsIgnoreCase("TRUE")) {//Tự tính table index
                                    ref.tableRemoves.add((Tbl) ((JAXBElement) elements.get(tableIndex)).getValue());
                                }
                            } else if (!tableItem.containsKey("deleteTable") && tableItem.containsKey("deleteTableIndex")) {//Cung cấp index thì không cần tính toán
                                String deleteTableIndexValue = StringUtil.nvl(tableItem.get("deleteTableIndex"), "");
                                if (deleteTableIndexValue.startsWith("@")) {//Đặt biến động
                                    String strIndex = StringUtil.nvl(findObjectInMapVariable(mVariable, deleteTableIndexValue), "-1");
                                    deleteTableIndex = ((Double) Double.parseDouble(strIndex)).intValue();
                                } else {//giá trị cụ thể
                                    deleteTableIndex = getIntValueFromMap(tableItem, "deleteTableIndex", -1);
                                }
                                if (deleteTableIndex > -1) {
                                    ref.tableRemoves.add(getTemplateTable(ref.documentPart, deleteTableIndex));
                                }
                            }
                        }
                    }

                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
            //Thực hiện xóa các bảng
            if (ref.tableRemoves != null && ref.tableRemoves.size() > 0) {
                removeTables(ref.tableRemoves);
            }
        }
    }

    public void handlerDataNormal(Map<String, VariableValueDto> mVariable) throws Exception {
        List<Map<String, Object>> lstConfigs = getAllParamsConfig();
        List<Map<String, Object>> lstConfigImages = new ArrayList<>();
        List<Map<String, Object>> lstConfigTextNumber = new ArrayList<>();
        List<Map<String, Object>> lstConfigTable = new ArrayList<>();
        List<Map<String, Object>> lstConfigSign = new ArrayList<>();
        List<Map<String, Object>> lstConfigTableSign = new ArrayList<>();
        Map<String, String> mConfigDelete = new HashMap<>();
        if (lstConfigs != null && !lstConfigs.isEmpty()) {
            //Lọc cụ thể từng loại
            lstConfigs.forEach(itemConfig -> {
                Map<String, Object> objectConfig = (Map<String, Object>) itemConfig.get("objectConfig");
                String type = StringUtil.nvl(objectConfig.get("type"), "").trim().toUpperCase();
                if ("TEXT".equals(type) || "NUMBER".equals(type)) {
                    lstConfigTextNumber.add(objectConfig);
                } else if ("TABLE".equals(type)) {
                    mConfigDelete.put(StringUtil.nvl(objectConfig.get("keyReplace"), ""), "");
                    lstConfigTable.add(objectConfig);
                } else if ("TABLE_SIGN".equals(type)) {
                    mConfigDelete.put(StringUtil.nvl(objectConfig.get("keyReplace"), ""), "");
                    lstConfigTableSign.add(objectConfig);
                } else if ("IMAGE".equals(type)) {
                    lstConfigImages.add(objectConfig);
                } else if (type.isEmpty()) {//type rỗng là cấu hình kí
                    mConfigDelete.put(StringUtil.nvl(objectConfig.get("keyReplace"), ""), "");
                    lstConfigSign.add(objectConfig);
                }
            });
            if (template != null) {
                //Xử lý thay thế ảnh @{type:"IMAGE", targetWidth:200, targetHeight:200, imageUrl:"abc.jpg"}
                if (!lstConfigImages.isEmpty()) {
                    handlerImageNormal(lstConfigImages, mVariable);
                }

                if (ValidationUtils.isNullOrEmpty(bpmTpSignZoneList)) {
                    bpmTpSignZoneList = new ArrayList<>();
                }
                //Chỉ quan tâm các ông kí
                List<BpmTpSignZone> bpmTpSignZoneSignList = bpmTpSignZoneList.stream().filter(bpmTpSignZone -> !ValidationUtils.isNullOrEmpty(bpmTpSignZone.getSign())).collect(Collectors.toList());
                //Xử lý phần kí
                if (!ValidationUtils.isNullOrEmpty(lstConfigSign)) {
                    handlerSignImageNormal(lstConfigSign, bpmTpSignZoneSignList);
                }

                if (!ValidationUtils.isNullOrEmpty(lstConfigTableSign)) {
                    handlerTableSignNormal(template, lstConfigTableSign, mVariable, bpmTpSignZoneSignList);
                }

                //@{type:"TABLE", tableIndex:4, value:"@test_table", dynamicTitle:false, rowConfigs:[(value: "txt_text2", format:"#,###,###,###", numberToWord:true)]}
                if (!lstConfigTable.isEmpty()) {
                    handlerTableNormal(template, lstConfigTable, mVariable);
                }


                //Xử lý với các trường hợpnumber @{type:"NUMBER", value:"10000", format:"#,###,###,##0.00", numberToWord:true}
                //Xử lý với các trường hợp text @{type:"TEXT", value:"Đã replace"}
                Map<String, String> mParams = new HashMap<>();
                if (!lstConfigTextNumber.isEmpty()) {
                    mParams = handlerTextOrNumberNormal(template, lstConfigTextNumber, mVariable);
                }

                //Thay thể tất cả các trường trong mParams
                mParams.putAll(mConfigDelete);
                // Ưu tiên ngày giờ tạo phiếu
                if (!ValidationUtils.isNullOrEmpty(mVariable.get("dtm_thoiGianTaoPhieu")) && !ValidationUtils.isNullOrEmpty(mVariable.get("dtm_thoiGianTaoPhieu").getValue())) {
                    String currentDateTime = mVariable.get("dtm_thoiGianTaoPhieu").getValue().toString();
                    String[] currentDate = currentDateTime.split(" ");
                    if (currentDate.length > 0) {
                        String[] arrDate = currentDate[0].split("/");
                        if (arrDate.length >= 3) {
                            mParams.put("$day", arrDate[0]);
                            mParams.put("$month", arrDate[1]);
                            mParams.put("$year", arrDate[2]);
                        }
                    }
                } else {
                    //Set mặc định ngày tháng năm
                    String currentDate = StringUtil.format(new Date(), "dd/MM/yyyy");
                    String[] arrDate = currentDate.split("/");
                    if (arrDate.length >= 3) {
                        mParams.put("$day", arrDate[0]);
                        mParams.put("$month", arrDate[1]);
                        mParams.put("$year", arrDate[2]);
                    }
                }
                searchAndReplace(template, mParams);
            }
        }

        //Lưu file
        if (sFilenameOut != null && template != null) {
            if (sFilenameOut.endsWith(".pdf")) {//Lưu file .pdf
                saveToPdf(template, sFilenameOut);
            } else if (sFilenameOut.endsWith(".docx")) {//Lưu file .docx
                saveToDocx(template, sFilenameOut);
            }
        }
    }

    public void handleLogo(Map<String, VariableValueDto> mVariable) throws Exception {
        List<Map<String, Object>> lstConfigs = getAllParamsConfig();
        List<Map<String, Object>> lstConfigImages = new ArrayList<>();
        if (lstConfigs != null && lstConfigs.size() > 0) {
            //Lọc cụ thể từng loại
            lstConfigs.forEach(itemConfig -> {
                Map<String, Object> objectConfig = (Map<String, Object>) itemConfig.get("objectConfig");
                String type = StringUtil.nvl(objectConfig.get("type"), "").trim().toUpperCase();
                String isExternalUrl = StringUtil.nvl(objectConfig.get("isExternalUrl"), "false");
                if (objectConfig != null) {
                    if ("IMAGE".equals(type) && isExternalUrl.equals("true")) {
                        lstConfigImages.add(objectConfig);
                    }
                }
            });

            if (template != null) {
                //Xử lý thay thế ảnh @{type:"IMAGE", targetWidth:200, targetHeight:200, imageUrl:"abc.jpg"}
                if (lstConfigImages != null && lstConfigImages.size() > 0) {
                    handlerImageNormal(lstConfigImages, mVariable);
                }
            }
        }
    }
}