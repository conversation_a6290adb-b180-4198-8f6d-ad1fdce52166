package vn.fis.eapprove.business.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jxls.builder.JxlsOutputFile;
import org.jxls.transform.poi.JxlsPoiTemplateFillerBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.constant.ExcelEnum;
import vn.fis.eapprove.business.domain.file.service.FileService;
import vn.fis.eapprove.business.model.ExcelValid;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.exception.FileOperationException;
import vn.fis.spro.file.manager.FileManager;

import java.io.*;
import java.nio.file.Files;
import java.util.*;

@Component
@Slf4j
@RequiredArgsConstructor
public class ExcelHelper {
    private final FileManager fileManager;
    private final FileService fileService;

    @Value("${app.s3.bucket}")
    private String BUCKET_NAME;

    public static boolean checkAllValuesEmptyOrNull(Map<?, ?> map) {
        if (map == null || map.isEmpty()) {
            return true;
        }
        for (Object value : map.values()) {
            if (!ValidationUtils.isNullOrEmpty(value)) {
                return false;
            }
        }
        return true;
    }

    public List<Map<String, Object>> handlerExcelFile(String fileName, int sheetIndex, int headerIndex) throws FileOperationException {
        InputStream file = fileManager.getFileInputStream(BUCKET_NAME, fileName);
        try (Workbook workbook = new XSSFWorkbook(file)) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            Row headerRow = sheet.getRow(headerIndex);
            List<Map<String, Object>> dataFinal = new ArrayList<>();
            if (headerRow == null) {
                log.error("Header row is null");
                return dataFinal;
            }

            // Lấy FormulaEvaluator để tính giá trị công thức
            FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();

            for (int rowIndex = headerIndex + 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row currentRow = sheet.getRow(rowIndex);
                if (currentRow == null) {
                    continue;
                }
                Map<String, Object> rowData = new LinkedHashMap<>();
                for (int colIndex = 0; colIndex < headerRow.getLastCellNum(); colIndex++) {
                    Cell currentCell = currentRow.getCell(colIndex);
                    Cell headerCell = headerRow.getCell(colIndex);
                    if (headerCell == null || headerCell.getCellType() == CellType.BLANK || !org.apache.commons.lang3.StringUtils.isNotBlank(headerCell.toString())) {
                        return null;
                    }
                    String cellValue = null;
                    String columnName = headerCell.getStringCellValue();
                    if (currentCell == null || currentCell.getCellType() == CellType.BLANK || !org.apache.commons.lang3.StringUtils.isNotBlank(currentCell.toString())) {
                        rowData.put(columnName, "");
                        continue;
                    }
                    if (currentCell.getCellType() != CellType.BLANK && StringUtils.isNotBlank(currentCell.toString())) {
                        cellValue = FileUtils.getCellValue(currentCell, evaluator);
                        rowData.put(columnName, cellValue);
                    }
                }
                if (checkAllValuesEmptyOrNull(rowData)) {
                    continue;
                }
                dataFinal.add(rowData);
            }
            return dataFinal;
        } catch (Exception e) {
            log.error("handlerExcelFile error: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    public String validateExcel(List<ExcelValid> keys,
                                List<Map<String, Object>> data,
                                int sheetIndex,
                                int headerRowIndex,
                                int mapperRowIndex) throws IOException, FileOperationException {
        String ext2 = FilenameUtils.getExtension("bar.xlsx");
        String fileNameNew = "file_fail_" + System.currentTimeMillis() + "." + ext2;
        try (Workbook workbook = FileUtils.writeToExcel(data)) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
//            Row headerRow = sheet.getRow(headerRowIndex);
            Row mapperRow = sheet.getRow(0);
            keys = keys.stream().peek(k -> {
                for (int i = 0; i < mapperRow.getLastCellNum(); i++) {
                    Cell cell = mapperRow.getCell(i);
                    if (cell != null && cell.getCellType() == CellType.STRING && cell.getStringCellValue().equals(k.getKey())) {
                        k.setIndexInExcel(i);

                        //Check valid contain list key
                        if (k.getKeyContain().contains(cell.getStringCellValue())) {
                            k.getIndexKeyContainInExcel().put(k.getKey(), i);
                        }
                    }
                }
            }).toList();
            boolean isValid = true;
            for (ExcelValid excelValid : keys) {
                int idxValid = excelValid.getIndexInExcel();
                Set<String> uniqueRow = new HashSet<>();
                for (Row row : sheet) {
                    if (row.getRowNum() < mapperRowIndex)
                        continue;
                    Cell cell = row.getCell(idxValid, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    String cellValue = FileUtils.getCellAsString(cell).trim();
                    if (excelValid.getIsValidEmpty() && ValidationUtils.isNullOrEmpty(cellValue)) {
                        cell.setCellValue("Không được bỏ trống");
                        cell.setCellStyle(redFontText(workbook));
                        isValid = false;
                        continue;
                    }
                    if (excelValid.getIsValidMinLength() && cellValue.length() < excelValid.getMinLength()) {
                        cell.setCellValue("Tối thiếu " + excelValid.getMinLength() + " ký tự");
                        cell.setCellStyle(redFontText(workbook));
                        isValid = false;
                        continue;
                    }
                    if (excelValid.getIsValidMaxLength() && cellValue.length() > excelValid.getMaxLength()) {
                        cell.setCellValue("Tối đa " + excelValid.getMaxLength() + " ký tự");
                        cell.setCellStyle(redFontText(workbook));
                        isValid = false;
                        continue;
                    }

                    if (excelValid.getIsValidExistInDb() && excelValid.getListKeyInDb().contains(cellValue)) {
                        cell.setCellValue("Dữ liệu tồn tại");
                        cell.setCellStyle(redFontText(workbook));
                        isValid = false;
                        continue;
                    }

                    if (excelValid.getIsValidExistNoInDb() && !excelValid.getListKeyNoInDb().contains(cellValue)) {
                        cell.setCellValue("Dữ liệu không tồn tại");
                        cell.setCellStyle(redFontText(workbook));
                        isValid = false;
                        continue;
                    }

                    if (excelValid.getIsValidFormat() && !ValidationUtils.isNullOrEmpty(cellValue) && !cellValue.matches(excelValid.getFormatRegex())) {
                        cell.setCellValue("Dữ liệu không hợp lệ");
                        cell.setCellStyle(redFontText(workbook));
                        isValid = false;
                        continue;
                    }

                    if(excelValid.getIsValidExactList() && !ValidationUtils.isNullOrEmpty(cellValue) && !excelValid.getListExact().contains(cellValue) ) {
                        cell.setCellValue("Nhập sai giá trị "+mapperRow.getCell(idxValid));
                        cell.setCellStyle(redFontText(workbook));
                        isValid = false;
                        continue;
                    }

                    if (excelValid.getIsValidExistInExcel()) {
                        if (!cellValue.isEmpty()) {
                            if (uniqueRow.contains(cellValue)) {
                                cell.setCellValue("Có giá trị bị trùng");
                                cell.setCellStyle(redFontText(workbook));
                                isValid = false;
                            } else {
                                uniqueRow.add(cellValue);
                            }
                        }
                    }
                }
            }
            if (Boolean.FALSE.equals(isValid)) {
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);
                InputStream inputStreamNew = new ByteArrayInputStream(bos.toByteArray());
                return saveFileError(inputStreamNew, fileNameNew);
            }
            return ExcelEnum.COMPLETED.status;
        }
    }

    public String saveFileError(InputStream is, String fileName) throws FileOperationException, IOException {
        return fileService.saveFileToMinIO(is, "import/error", fileName, new Date(), true);
    }

    public String exportExcel(String templateName, Map<String, Object> inputData) throws IOException, FileOperationException {
        InputStream file = fileManager.getFileInputStream(BUCKET_NAME, "import/sample/" + templateName + ".xlsx");

        JxlsPoiTemplateFillerBuilder.newInstance()
                .withTemplate(file)
                .build()
                .fill(inputData, new JxlsOutputFile(new File(templateName + "_template.xlsx")));

        File output = new File(templateName + "_template.xlsx");
        byte[] fileBytes = Files.readAllBytes(output.toPath());
        InputStream inputStreamNew = new ByteArrayInputStream(fileBytes);
        String fileName = fileManager.putFile(BUCKET_NAME, "FPT_eApprove_Template_" + templateName + ".xlsx", inputStreamNew.available(), inputStreamNew);
        output.deleteOnExit();
        return fileManager.getUrlFile(BUCKET_NAME, fileName);
    }

    private CellStyle redFontText(Workbook workbook) {
        Font redFont = workbook.createFont();
        redFont.setColor(IndexedColors.RED.getIndex());
        CellStyle redTextStyle = workbook.createCellStyle();
        redTextStyle.setFont(redFont);
        return redTextStyle;
    }
}

