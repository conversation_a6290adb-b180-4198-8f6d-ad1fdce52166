package vn.fis.eapprove.business.utils.template.replaceText;

import java.util.ArrayList;
import java.util.List;

public class StringFindUtil {
    public StringFindUtil() {
    }

    public static List<FoundResult> findAllOccurrencesInString(String data, String search) {
        List<FoundResult> list = new ArrayList();
        String remaining = data;
        int totalIndex = 0;

        while (true) {
            int index = remaining.indexOf(search);
            if (index == -1) {
                return list;
            }

            int throwAwayCharCount = index + search.length();
            remaining = remaining.substring(throwAwayCharCount);
            list.add(new FoundResult(totalIndex + index, search));
            totalIndex += throwAwayCharCount;
        }
    }

    public static class FoundResult {
        private final int start;
        private final int end;

        public FoundResult(int start, String searchString) {
            this.start = start;
            this.end = start + searchString.length() - 1;
        }

        public int getStart() {
            return this.start;
        }

        public int getEnd() {
            return this.end;
        }
    }
}
