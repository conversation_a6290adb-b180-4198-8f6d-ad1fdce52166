package vn.fis.eapprove.business.utils;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * vn.fis.eapprove.business.utils.CastUtils
 * Created by ThaiTuanHiep - <EMAIL>
 * 7/7/2022 12:40 PM
 */

public class CastUtils {

    public static List<Long> stringToListLong(String arrLongInString) {
        try {
            List<Long> list = new Gson().fromJson(arrLongInString, new TypeToken<List<Long>>() {
            }.getType());
            if (Objects.isNull(list)) {
                list = new ArrayList<>();
            }
            return list;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public static List<String> stringToListString(String arrStringInString) {
        try {
            List<String> list = new Gson().fromJson(arrStringInString, new TypeToken<List<String>>() {
            }.getType());
            if (Objects.isNull(list)) {
                list = new ArrayList<>();
            }
            return list;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public static boolean toBooleanDefaultIfNull(Boolean bool, boolean valueIfNull) {
        if (bool == null) {
            return valueIfNull;
        }
        return bool;
    }

    public static <T> List<T> mergeList(List<T>... lists) {
        List<T> tList = new ArrayList<>();
        Arrays.stream(lists).forEach(o -> {
            if (Objects.nonNull(o)) {
                tList.addAll(o);
            }
        });
        return tList;
    }

    public static <T> Set<T> mergeSet(Set<T>... sets) {
        Set<T> tSet = new HashSet<>();
        Arrays.stream(sets).forEach(o -> {
            if (Objects.nonNull(o)) {
                tSet.addAll(o);
            }
        });
        return tSet;
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    public static Double stringToDouble(String value) {
        return !ValidationUtils.isNullOrEmpty(value) ? Double.valueOf(value) : null;
    }

    public static Long stringToLong(String value) {
        return !ValidationUtils.isNullOrEmpty(value) ? Long.valueOf(value) : null;
    }
}
