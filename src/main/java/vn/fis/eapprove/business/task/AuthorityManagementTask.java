package vn.fis.eapprove.business.task;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.fis.eapprove.business.domain.assign.entity.AssignManagement;
import vn.fis.eapprove.business.domain.assign.repository.AssignRepository;
import vn.fis.eapprove.business.domain.authority.entity.AuthorityManagement;
import vn.fis.eapprove.business.domain.authority.service.AuthorityManagementService;
import vn.fis.eapprove.business.domain.bpm.service.BpmTaskManager;


import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @project business-process-service
 * @created 3/13/2023 - 5:22 PM
 */

@Slf4j
@Service
public class AuthorityManagementTask {
    private final AuthorityManagementService service;
    private final BpmTaskManager taskManager;
    private final AssignRepository repository;

    @Autowired
    public AuthorityManagementTask(AuthorityManagementService service, AssignRepository repository, BpmTaskManager taskManager) {
        this.service = service;
        this.repository = repository;
        this.taskManager = taskManager;
    }

    public void transferAssign()  {
        try {
            List<AssignManagement> lstManager = repository.getAllAssignByEffect();
            if (!lstManager.isEmpty()) {
                for (AssignManagement assign : lstManager) {
                    // expire authority
                    if (assign.getEndDate().isBefore(LocalDate.now())) {
                        List<AuthorityManagement> lstAuthority = service.findAuthorityByEmail(assign.getAssignUser(), assign.getAssignedUser());
                        for (AuthorityManagement item : lstAuthority) {
                            Map<String, Object> body = new HashMap<>();
                            body.put("ticketId", item.getTicketId());
                            body.put("taskId", item.getTaskId());
                            body.put("taskDefKey", item.getTaskDefKey());
                            body.put("email", item.getToAccount());
                            this.taskManager.changeImplementer(body, item.getToAccount(), 2);
                            service.deleteAuthority(item.getId());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
