package vn.fis.eapprove.business.task;

import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.api.entity.ApiLog;
import vn.fis.eapprove.business.domain.api.service.ApiLogService;
import vn.fis.eapprove.business.model.ActionApiLogAfterBean;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.helper.RedisHelper;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.Iterator;
import java.util.List;

/**
 * Author: PhucVM
 * Date: 29/11/2022
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "job.apiLogTask.enable", havingValue = "true")
public class ApiLogTask {

    private static final long DEQUEUE_TOTAL = 100;
    private final RedisHelper redisHelper;
    private final ApiLogService apiLogService;

//    @Value("${action.api.cons.url}")
//    private String consUrl;
//    @Value("${action.api.sap.url}")
//    private String sapUrl;
//    @Value("${action.api.admin.url}")
//    private String adminUrl;
//    @Value("${action.api.ihrp.url}")
//    private String ihrpUrl;

    @Autowired
    public ApiLogTask(RedisHelper redisHelper,
                      ApiLogService apiLogService) {
        this.redisHelper = redisHelper;
        this.apiLogService = apiLogService;
    }

    @Scheduled(fixedDelayString = "${scheduler.api-log-task.delay}")
    public void process() {
        List<ApiLog> apiLogs = redisHelper.dequeueAll(CommonConstants.QueueName.API_LOG_QUEUE, DEQUEUE_TOTAL, ApiLog.class);
        processLogs(apiLogs);
        processLogAfter();
    }

    private void processLogAfter() {
        List<ActionApiLogAfterBean> logAfterBeans = redisHelper.dequeueAll(CommonConstants.QueueName.LOG_AFTER_QUEUE, DEQUEUE_TOTAL, ActionApiLogAfterBean.class);
        if (!ValidationUtils.isNullOrEmpty(logAfterBeans)) {
            logAfterBeans.forEach(logAfterBean -> {
                String procInstId = logAfterBean.getProcInstId();
                Long bpmProcInstId = logAfterBean.getBpmProcInstId();

                // dequeue all log of create ticket phase
                List<ApiLog> apiLogs = redisHelper.dequeueAll(procInstId, ApiLog.class);
                apiLogs.forEach(e -> e.setBpmProcinstId(bpmProcInstId));
                processLogs(apiLogs);
            });
        }
    }

    private void processLogs(List<ApiLog> apiLogs) {
        if (!ValidationUtils.isNullOrEmpty(apiLogs)) {
            apiLogs.forEach(log -> {
                String header = log.getHeader();

                if (header != null) {
                    JSONObject jsonObject = new JSONObject(header);
                    log.setHeader(jsonObject.toString());
                }
            });
            log.info("Insert total {} api-logs", apiLogs.size());
            apiLogService.saveAll(apiLogs);
        }
    }
}
