package vn.fis.eapprove.business.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Author: PhucVM
 * Date: 21/02/2023
 */
@Slf4j
@Component
public class TicketAutoTask {

//    private long lastProcessTime;
//
//    @Scheduled(fixedDelay = 5000)
//    @SchedulerLock(name = "ticketAutoTask", lockAtLeastFor = "PT5S", lockAtMostFor = "PT5M")
//    public void process() {
//        long newProcessTime = System.currentTimeMillis();
//        log.info("PROCESS 1: last=" + lastProcessTime + ", new=" + newProcessTime);
//        lastProcessTime = newProcessTime;
//    }
}
