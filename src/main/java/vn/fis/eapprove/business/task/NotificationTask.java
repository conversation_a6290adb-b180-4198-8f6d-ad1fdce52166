package vn.fis.eapprove.business.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.notification.entity.Notification;
import vn.fis.eapprove.business.domain.notification.service.NotificationService;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.helper.RedisHelper;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.List;

/**
 * Author: AnhVTN
 * Date: 03/01/2023
 */
@Component
@Slf4j
public class NotificationTask {
    private final RedisHelper redisHelper;
    private final NotificationService notificationService;
    private final Common common;

    @Autowired
    public NotificationTask(RedisHelper redisHelper, NotificationService notificationService, Common common) {
        this.redisHelper = redisHelper;
        this.notificationService = notificationService;
        this.common = common;
    }

    @Scheduled(fixedDelayString = "${scheduler.notification-push.delay}")
    public void process() {
        List<Notification> notifications = redisHelper.dequeueAll(CommonConstants.QueueName.NOTIFICATION_QUEUE, 100, Notification.class);
        if (!ValidationUtils.isNullOrEmpty(notifications)) {
            //TODO: flushing to notification service process
            log.info("Insert total {} notifications", notifications.size());
            saveAllNotifications(notifications);
        }
    }

    private void saveAllNotifications(List<Notification> notifications) {
        notificationService.saveBatch(notifications);
    }
}
