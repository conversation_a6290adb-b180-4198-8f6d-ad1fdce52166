package vn.fis.eapprove.business.task;

import lombok.extern.slf4j.Slf4j;
import vn.fis.eapprove.business.model.ActionApiExecuteResult;
import vn.fis.eapprove.business.tenant.manager.ActionApiService;

/**
 * Author: PhucVM
 * Date: 15/02/2023
 */
@Slf4j
public class CallActionApi implements Runnable {

    private final ActionApiService actionApiService;
    private final ActionApiExecuteResult actionApi;

    public CallActionApi(ActionApiService actionApiService, ActionApiExecuteResult actionApi) {
        this.actionApiService = actionApiService;
        this.actionApi = actionApi;
    }

    @Override
    public void run() {
        if (actionApiService != null) {
            try {
                actionApiService.callApi(actionApi, false, true);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }
    }
}
