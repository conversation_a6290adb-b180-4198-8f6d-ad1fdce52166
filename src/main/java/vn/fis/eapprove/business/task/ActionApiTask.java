package vn.fis.eapprove.business.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.model.ActionApiExecuteResult;
import vn.fis.eapprove.business.tenant.manager.ActionApiService;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.helper.RedisHelper;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Author: PhucVM
 * Date: 15/02/2023
 */
@Component
@Slf4j
public class ActionApiTask {

    private static final int QUEUE_SIZE = 10;
    private final RedisHelper redisHelper;
    private final ActionApiService actionApiService;
    private final ThreadPoolExecutor executor;

    @Autowired
    public ActionApiTask(RedisHelper redisHelper,
                         ActionApiService actionApiService) {
        this.redisHelper = redisHelper;
        this.actionApiService = actionApiService;
        this.executor = (ThreadPoolExecutor) Executors.newFixedThreadPool(QUEUE_SIZE);
    }

    @Scheduled(fixedDelay = 500)
    public void process() {
        List<ActionApiExecuteResult> actionApis = redisHelper.dequeueAll(CommonConstants.QueueName.ACTION_API_QUEUE, QUEUE_SIZE, ActionApiExecuteResult.class);
        if (!ValidationUtils.isNullOrEmpty(actionApis)) {
            actionApis.forEach(e -> {
                CallActionApi callActionApi = new CallActionApi(actionApiService, e);
                executor.execute(callActionApi);
            });
        }
    }
}
