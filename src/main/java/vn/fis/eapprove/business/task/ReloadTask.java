package vn.fis.eapprove.business.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.constant.AppConstants;
import vn.fis.eapprove.business.domain.task.entity.TaskAction;
import vn.fis.eapprove.business.domain.task.service.TaskActionManager;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.annotation.PostConstruct;
import java.util.Map;

/**
 * Author: PhucVM
 * Date: 04/12/2022
 */
@Component
@Slf4j
public class ReloadTask {

    private final TaskActionManager taskActionManager;

    @Autowired
    public ReloadTask(TaskActionManager taskActionManager) {
        this.taskActionManager = taskActionManager;
    }

    @PostConstruct
    public void init() {
        loadData();
    }

    @Scheduled(cron = "${scheduler.reload-task.cron}")
    public void reload() {
        loadData();
    }

    private void loadData() {
        long startTime = System.currentTimeMillis();
        log.info("Starting load cache data from DB");
        loadActionCodeToTaskAction();
        log.info("Finished load cache data from DB in {} ms", System.currentTimeMillis() - startTime);
    }

    private void loadActionCodeToTaskAction() {
        // load map action_code to task_action
        Map<String, TaskAction> actionCodeToTaskActionMap = taskActionManager.getActionCodeToTaskActionMap();
        if (!ValidationUtils.isNullOrEmpty(actionCodeToTaskActionMap)) {
            AppConstants.Cache.ACTION_CODE_TO_TASK_ACTION.clear();
            AppConstants.Cache.ACTION_CODE_TO_TASK_ACTION.putAll(actionCodeToTaskActionMap);
            log.info("Loaded action_code to task_action map, size={}", AppConstants.Cache.ACTION_CODE_TO_TASK_ACTION.size());
        }
    }
}
