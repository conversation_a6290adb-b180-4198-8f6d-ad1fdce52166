package vn.fis.eapprove.business.application.controller;


import org.springframework.http.ResponseEntity;
import vn.fis.eapprove.business.model.response.BaseResponse;

public class BaseController {
    public ResponseEntity<BaseResponse<Void>> success() {
        return ResponseEntity.ok(BaseResponse.success());
    }

    public <T> ResponseEntity<BaseResponse<T>> success(T data) {
        return ResponseEntity.ok(BaseResponse.success(data));
    }
}
