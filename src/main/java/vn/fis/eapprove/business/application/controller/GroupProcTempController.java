package vn.fis.eapprove.business.application.controller;

import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.fis.eapprove.business.domain.groupTable.service.GroupProcTempManager;
import vn.fis.eapprove.business.model.response.GroupProcTempResponse;
import vn.fis.eapprove.business.utils.ResponseUtils;

import java.util.List;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("GroupProcTempControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/group-proc-temp")
public class GroupProcTempController {


    @Autowired
    CredentialHelper credentialHelper;
    @Autowired
    ResponseUtils responseUtils;
    @Autowired
    GroupProcTempManager groupProcTempManager;


    @GetMapping("/getAll")
    public ResponseEntity<?> getAll() {
        try {
            List<GroupProcTempResponse> result = groupProcTempManager.getAll();
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
