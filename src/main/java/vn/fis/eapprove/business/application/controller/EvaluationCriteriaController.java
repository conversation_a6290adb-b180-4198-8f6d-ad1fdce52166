package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.evaluation.service.EvaluationCriteriaService;
import vn.fis.eapprove.business.dto.EvaluationCriteriaDto;
import vn.fis.eapprove.business.dto.EvaluationCriteriaSearchDto;
import vn.fis.eapprove.business.model.request.EvaluationCriteriaRequest;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.model.ResponseDto;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@Slf4j
@RestController("EvaluationCriteriaControllerV1")
@CrossOrigin("*")
@RequestMapping(SERVICE_PATH + "/evaluation-criteria")
@Tag(name = "evaluation-criteria", description = "The Evaluation Criteria service")
public class EvaluationCriteriaController {
    private final EvaluationCriteriaService service;
    private final Common common;
    private final BusinessManager businessManager;
    private final ResponseUtils responseUtils;

    @Autowired
    public EvaluationCriteriaController(EvaluationCriteriaService service, Common common,BusinessManager businessManager,ResponseUtils responseUtils) {
        this.service = service;
        this.common = common;
        this.businessManager = businessManager;
        this.responseUtils = responseUtils;
    }

    @PostMapping("/create-update")
    public ResponseEntity<?> creatOrUpdate(@RequestBody EvaluationCriteriaRequest request) throws Exception {
//        try {
            service.saveAll(request);
            return ResponseHelper.ok();
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return ResponseHelper.fail();
//        }
    }

    @PostMapping("/delete")
    public ResponseEntity<?> delete(@RequestBody EvaluationCriteriaRequest request) {
        try {
            service.deleteByIds(request);
            return ResponseHelper.ok(true, common.getMessage("message.masterdata.delete.success"));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/search")
    public ResponseEntity<?> getCodeGen(@RequestBody EvaluationCriteriaDto request) {
        try {
            return ResponseHelper.ok(service.search(request));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/getAllCriteriaByReviewAndDepartment")
    public ResponseEntity<?> getAllCriteriaByReviewAndDepartment(@RequestBody EvaluationCriteriaSearchDto request) {
        try {
            return ResponseHelper.ok(service.getAllCriteriaByReviewAndDepartment(request));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @GetMapping("/getAllDepartmentByReview")
    public ResponseEntity<?> getAllDepartmentByReview(@RequestParam(required = false, value = "companyCode") String companyCode) {
        try {
            return ResponseHelper.ok(service.getAllDepartmentByReview(companyCode));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/query-by-predicate")
    public ResponseEntity<?> queryByPredicate(@RequestBody EvaluationCriteriaDto request) {
        try {
            return ResponseHelper.ok(service.getAllCriteriaByPredicate(request));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody EvaluationCriteriaDto request) {
        try {
            Object result = businessManager.getFilterData(service.searchFilter(request), FilterDataEnum.EVALUATION_CRITERIA);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                    message("Thành Công").build());
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
        }
    }
}
