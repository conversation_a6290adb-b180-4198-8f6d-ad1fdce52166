package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.system.service.SystemGroupService;
import vn.fis.eapprove.business.model.request.AddSystemGroupRequest;
import vn.fis.eapprove.business.model.request.SystemGroupRequest;
import vn.fis.eapprove.business.model.request.SystemGroupSearch;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.model.ResponseDto;

import java.util.List;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("SystemGroupControllerV1")
@CrossOrigin("*")
@RequestMapping(SERVICE_PATH + "/system-group")
public class SystemGroupController {

    private static final Logger log = LoggerFactory.getLogger(SystemGroupController.class);
    private final SystemGroupService systemGroupService;
    private final BusinessManager businessManager;;

    public SystemGroupController(SystemGroupService systemGroupService, BusinessManager businessManager) {
        this.systemGroupService = systemGroupService;
        this.businessManager = businessManager;
    }


    @PostMapping(value = "/createUpdate")
    public ResponseEntity<?> createUpdate(@RequestBody SystemGroupRequest systemGroupRequest) {
        try{
            return ResponseEntity.ok(systemGroupService.createUpdate(systemGroupRequest));
        }catch(Exception ex){
            return ResponseEntity.badRequest().body(null);
        }
    }

    @GetMapping(value = "/findById")
    public ResponseEntity<?> findById(@RequestParam Long id) {
        try{
            return ResponseEntity.ok(systemGroupService.findAllById(id));
        }catch(Exception ex){
            return ResponseEntity.badRequest().body(null);
        }
    }

    @PostMapping(value = "/findByGroupType")
    public ResponseEntity<?> findByGroupType(@RequestBody SystemGroupSearch request) {
        try{
            return ResponseEntity.ok(systemGroupService.findByGroupType(request));
        }catch(Exception ex){
            return ResponseEntity.badRequest().body(null);
        }
    }

    @Operation(summary = "Get list data accept in group")
    @PostMapping(value = "/findDataByTableName")
    public ResponseEntity<?> findDataByTableName(@RequestParam String tableName,@RequestBody Object filter) {
        try{
            return ResponseEntity.ok(systemGroupService.findDataByTableName(tableName,filter));
        }catch(Exception ex){
            return ResponseEntity.badRequest().body(null);
        }
    }

    @Operation(summary = "Get list User ")
    @GetMapping(value = "/getUserGroup")
    public ResponseEntity<?> getUserGroup() {
        try{
            return ResponseEntity.ok(systemGroupService.getUserGroup());
        }catch(Exception ex){
            return ResponseEntity.badRequest().body(null);
        }
    }

    @Operation(summary = "AddGroup")
    @PostMapping(value = "/addGroup")
    public ResponseEntity<?> addGroup(@RequestBody AddSystemGroupRequest addSystemGroupRequest) {
        try{
            return systemGroupService.addSystemGroup(addSystemGroupRequest);
        }catch(Exception ex){
            return ResponseEntity.badRequest().body(null);
        }
    }

    @Operation(summary = "update")
    @GetMapping(value = "/update")
    public ResponseEntity<Boolean> update(@RequestParam List<Long> ids,@RequestParam(required = false) Integer status) {
        try{
            return ResponseEntity.ok(systemGroupService.update(ids,status));
        }catch(Exception ex){
            return ResponseEntity.badRequest().body(null);
        }
    }

    @PostMapping(value = "/checkDuplicateName")
    public ResponseEntity<Boolean> checkDuplicateName(@RequestParam String name,@RequestParam String tableName,@RequestParam(required = false) Long id) {
        try{
            return systemGroupService.checkDuplicateName(tableName,name,id);
        }catch(Exception ex){
            return ResponseEntity.badRequest().body(null);
        }
    }

    @Operation(summary = "get list id system group")
    @GetMapping(value = "/getListIdSystemGroup")
    public ResponseEntity<?> getListIdSystemGroup(@RequestParam("groupType") String groupType, @RequestParam("username") String username) {
        try {
            Object result = systemGroupService.getListIdSystemGroup(groupType, username);
            return ResponseHelper.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Get All Filter Data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "TemplateManager", description = "Get all filterData")
    @PostMapping("/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody SystemGroupSearch request) {
        try {
            Object result = businessManager.getFilterData(systemGroupService.findByGroupTypeFilter(request),null);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).message("Thành công").build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message("Thất bại").build());
        }
    }

    @Operation(summary = "check id in system group")
    @GetMapping(value = "/checkIdInSystemGroup")
    public ResponseEntity<?> checkIdInSystemGroup(@RequestParam("groupType") String groupType,
                                                  @RequestParam("username") String username,
                                                  @RequestParam("groupValue") String groupValue)
    {
        try {
            Boolean result = systemGroupService.checkIdInSystemGroup(groupType, username, groupValue);
            return ResponseHelper.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }
}