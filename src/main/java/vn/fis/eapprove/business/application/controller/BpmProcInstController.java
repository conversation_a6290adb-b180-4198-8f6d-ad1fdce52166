package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.repository.query.Param;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vn.fis.eapprove.business.domain.assistant.entity.Assistant;
import vn.fis.eapprove.business.domain.assistant.entity.AssistantOpinion;
import vn.fis.eapprove.business.domain.assistant.repository.AssistantOpinionRepository;
import vn.fis.eapprove.business.domain.assistant.service.AssistantService;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTaskRepository;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcInstManager;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcdefNotificationService;
import vn.fis.eapprove.business.domain.bpm.service.BpmRuManager;
import vn.fis.eapprove.business.domain.bpm.service.BpmTaskManager;
import vn.fis.eapprove.business.dto.*;
import vn.fis.eapprove.business.model.NotificationUser;
import vn.fis.eapprove.business.model.request.*;
import vn.fis.eapprove.business.model.response.*;
import vn.fis.eapprove.business.producer.ReportProducer;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.eapprove.business.tenant.manager.CamundaEngineService;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.business.utils.template.common.StringUtil;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.constants.*;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.model.request.QueryRequest;
import vn.fis.spro.common.util.DateTimeUtils;
import vn.fis.spro.common.util.LogUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController("BpmProcInstControllerV1")
@RequiredArgsConstructor
@CrossOrigin("*")
@Tag(name = "bpm-proc-inst-service", description = "The BPM process instance services")
@RequestMapping("")
public class BpmProcInstController {

    private final BpmProcInstManager bpmProcInstManager;
    private final ResponseUtils responseUtils;
    private final BpmRuManager bpmRuHistoryManager;
    private final CamundaEngineService camundaEngineService;
    private final CredentialHelper credentialHelper;
    private final MessageSource messageSource;
    private final AssistantOpinionRepository assistantOpinionServices;
    private final AssistantService assistantService;
    @Lazy
    private final BpmProcdefNotificationService bpmProcdefNotificationService;
    private final ReportProducer reportProducer;
    private final BusinessManager businessManager;
    private final BpmTaskRepository bpmTaskRepository;
    private final BpmTaskManager bpmTaskManager;
    private final KafkaTemplate<String, Object> kafkaTemplate;

    @Value("${spring.kafka.consumer.topic.insert-report-by-group}")
    private String insertReportByGroup;
    @Value("${spring.kafka.consumer.topic.insert-report-by-chart-node}")
    private String insertReportByChartNode;
    @Value("${spring.kafka.consumer.topic.notification-user}")
    private String notificationUser;

    @PostMapping("/delete/draft")
    public ResponseEntity<?> deleteDaft(@RequestBody List<Long> ids) {
        try {
            Boolean result = bpmProcInstManager.deleteAllDraft(ids);
            if (result) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(true).
                        message(messageSource.getMessage("message.bpmProInst.delete.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.bpmProInst.delete.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @PostMapping("/bpmProcInst/search")
    public ResponseEntity<?> search(@RequestBody LoadTicketDto criteria) {
        try {
            String account = credentialHelper.getJWTPayload().getUsername();
            PageDto result = bpmProcInstManager.search(criteria, account);
            if (result != null) {
                return ResponseHelper.ok(result);
            }

            return ResponseHelper.notFound();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/bpmProcInst/getTicketByServiceAndTicketStatus")
    public ResponseEntity<?> getTicketByServiceAndTicketStatus(@RequestBody(required = false) GetTicketDto criteria) {
        try {
            Object result = bpmProcInstManager.getTicketByServiceAndTicketStatus(criteria);
            if (result != null) {
                return ResponseHelper.ok(result);
            }

            return ResponseHelper.notFound();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

//    @PostMapping("/bpmProcInst/searchTest")
//    public ResponseEntity<?> searchTest(@RequestBody LoadTicketDto criteria) {
//        try {
//            List<String> user = Arrays.asList("NVE100000196", "NVE100000212", "NVE100000197", "NVE100000130", "NVE100000129",
//                    "NVE168000003", "NVE168000002", "NVE168000005", "NVE168000018", "NVE168000016",
//                    "NVE168000004", "NVE100000127", "NVE100000207", "NVE100000210", "NVE100000204",
//                    "NVE100000199", "NVE100000202", "NVE100000194", "NVE100000137", "NVE101000004",
//                    "NVE100000134", "NVE100004301", "NVE100000138", "NVE168000019", "NVE168000008",
//                    "NVE100000211", "NVE100000238", "NVE100000067", "NVE100000080", "NVE100000076",
//                    "NVE100000069", "NVE100000079", "NVE168000011", "NVE101000001", "NVE100000172",
//                    "NVE100000056", "NVE100000176", "NVE100000026", "NVE100000221", "NVE168000010",
//                    "NVE100000066", "NVE100000072", "NVE100000073", "NVE100000071", "NVE100000175",
//                    "NVE100000162", "NVE100000178", "NVE100004302", "NVE100000170", "NVE100000098",
//                    "NVE101000003", "NVE125001884", "NVE125002262", "NVE125004301", "NVE125002123",
//                    "NVE125002418", "NVE126000002", "NVE126000015", "NVE126000034", "NVE126000105",
//                    "NVE126000050", "NVE126000110", "NVE128000010", "NVE128000022", "NVE126000018",
//                    "NVE126000076", "NVE137000002", "NVE137000003", "NVE137000045", "NVE137000052",
//                    "NVE137000008", "NVE137000009", "NVE137000053", "NVE137004301", "NVE137000046",
//                    "NVE137000047", "NVE137000049", "NVE137004302", "NVE143000031", "NVE137004303",
//                    "NVE155000172", "NVE151000249", "NVE151000281", "NVE151000050", "NVE151004301",
//                    "NVE151004302", "NVE151004303", "NVE151004304", "NVE151004305", "NVE151004306",
//                    "NVE151004307", "NVE151000310", "NVE151004308", "NVE155000300", "NVE151000037",
//                    "NVE151004309", "NVE151000022", "NVE151000262", "NVE151000182", "NVE151000274",
//                    "NVE151004310", "NVE151004311", "NVE151004312", "NVE151004313", "NVE151004314",
//                    "NVE151004315", "NVE151000003", "NVE151000090", "NVE151000188", "NVE151000143",
//                    "NVE151000096", "NVE151004316", "NVE151004317", "NVE151004318", "NVE151004319",
//                    "NVE151004320", "NVE151004321", "NVE151004322", "NVE151004323", "NVE151000069",
//                    "NVE151004324", "NVE151000004", "NVE151000013", "NVE151000026", "NVE151000131",
//                    "NVE151004325", "NVE151004326", "NVE151004327", "NVE151004328", "NVE151004329",
//                    "NVE155000110", "NVE155000065", "NVE155000023", "NVE155000043", "NVE155000123",
//                    "NVE155000228", "NVE155000233");
//
//            List<String> user2 = Arrays.asList("employee");
////            String account = credentialHelper.getJWTPayload().getUsername();
//            List<Object> allData = new ArrayList<>();
//            user.stream().forEach(u -> {
//                List<Object> result = bpmProcInstManager.searchTest(criteria, u);
//                if (!ValidationUtils.isNullOrEmpty(result)) {
//                    allData.addAll(result);
//                }
//            });
//            return ResponseEntity.ok(allData);
//
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return ResponseHelper.fail();
//        }
//    }

    @PostMapping("/assistant/ticket-search")
    public ResponseEntity<?> search(@RequestBody LoadAssistantTicketDto criteria) {
        try {
            PageDto result = bpmProcInstManager.searchByListAssistantEmail(criteria);
            if (result != null) {
                return ResponseHelper.ok(result);
            }

            return ResponseHelper.notFound();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    //get filter
    @PostMapping("/bpmProcInst/getFilter")
    public ResponseEntity<?> getFilter() {
        try {
            String email = credentialHelper.getJWTPayload().getUsername();
            Map<String, Object> result = bpmProcInstManager.getFilterTicket(email);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Sucess", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/ticket/count")
    public ResponseEntity<?> countTicket(@Param("ownerProcess") Boolean ownerProcess, @Param("search") String search) {
        try {
            String account = credentialHelper.getJWTPayload().getUsername();
            Map<String, Long> result = bpmProcInstManager.countTicket(account, ownerProcess, search);
            if (result != null) {
                return ResponseHelper.ok(result);
            }
            return ResponseHelper.fail(null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/ticket/count")
    public ResponseEntity<?> countTicket(@RequestBody TicketCountRequest request) {
        try {
            String account = credentialHelper.getJWTPayload().getUsername();
            Map<String, Long> result = bpmProcInstManager.countTicket(account, request.getOwnerProcess(), request.getSearch());
            if (result != null) {
                return ResponseHelper.ok(result);
            }
            return ResponseHelper.fail(null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/bpmProcInst/search/{ticketId}")
    public ResponseEntity<?> searchByTicketId(@PathVariable Long ticketId) {
        TicketDetailResponse result = bpmProcInstManager.searchByTicketId(ticketId);
        if (result != null) {
            return ResponseHelper.ok(result);
        }

        return ResponseHelper.fail();
    }

    @GetMapping("/bpmProcInst/getTicketInfo/{procInstId}")
    public ResponseEntity<?> getTicketInfo(@PathVariable String procInstId) {
        try {
            log.info("process=get-bpmProcInst");
            List<LoadTicketDto> getListTicket = bpmProcInstManager.getTicketInfo(procInstId);
            if (getListTicket != null) {
                return responseUtils.getResponseEntity(getListTicket, 1, "Fail", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/bpmProcInst/{procDefId}/xml")
    public ResponseEntity<?> getXmlProcInst(@PathVariable String procDefId) {
        return camundaEngineService.getXMLEntity(procDefId);
    }

    @PostMapping("/bpmProcInst/getPrintFileTemplate/{ticketId}")
    public ResponseEntity<?> getPrintFileTemplate(@PathVariable Long ticketId,
                                                  @RequestBody BpmTemplatePrintRequest bpmTemplatePrintRequest) {
        try {
            byte[] result = bpmProcInstManager.getPrintFileTemplate(ticketId, bpmTemplatePrintRequest, bpmTemplatePrintRequest.getBpmTpSignZoneList());
            if (result != null) {
                return ResponseEntity.ok().body(result);
            }
            return ResponseHelper.fail(null, "File tờ trình lỗi, vui lòng kiểm tra lại");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/bpmProcInst/getHtmlFileTemplate/{ticketId}")
    public ResponseEntity<?> getHtmlFileTemplate(@PathVariable Long ticketId,
                                                  @RequestBody BpmTemplatePrintRequest bpmTemplatePrintRequest) {
        try {
            byte[] result = bpmProcInstManager.getHtmlFileTemplate(ticketId, bpmTemplatePrintRequest, bpmTemplatePrintRequest.getBpmTpSignZoneList());
            if (result != null) {
                return ResponseEntity.ok().body(result);
            }
            return ResponseHelper.fail(null, "File tờ trình lỗi, vui lòng kiểm tra lại");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/bpmProcInst/addNotifications/{ticketId}")
    public ResponseEntity<?> addNotifications(@PathVariable Long ticketId,
                                              @RequestBody BpmNotifyUserRequest bpmNotifyUserRequest) {
        try {
            String account = credentialHelper.getJWTPayload().getUsername();
            return ResponseEntity.ok().body(bpmProcdefNotificationService.addNotificationsByConfig(bpmNotifyUserRequest.getBpmProcdefId(),
                    bpmNotifyUserRequest.getNextTaskDefKey(), bpmNotifyUserRequest.getVariables(), ticketId,
                    bpmNotifyUserRequest.getIsGetOldVariable(), bpmNotifyUserRequest.getLstCustomerEmails(), bpmNotifyUserRequest.getActionCode(), account));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/bpmProcInst/create/{procDefId}/{ticketId}")
    public ResponseEntity<?> createTicket(@PathVariable String procDefId,
                                          @PathVariable Long ticketId,
                                          @RequestBody StartProcessInstanceDto startProcessInstanceDto) throws Exception {
        ResponseEntity<?> response = bpmProcInstManager.createTicket(procDefId, ticketId, startProcessInstanceDto);

        try {
            if (response.getBody() instanceof ResponseDto) {

                ResponseDto responseDto = (ResponseDto) response.getBody();

                Map<String, Object> data = (Map<String, Object>) responseDto.getData();

                Long saveTicketId = (Long) data.get("ticketId");

                String procInstId = data.get("procInstId").toString();

                bpmProcInstManager.createReport(procInstId, saveTicketId);
                if(!startProcessInstanceDto.getIsDraft())
                    bpmProcInstManager.handleSaveVersionTemplateProcinst(data, procDefId);

            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return response;
    }

    @PostMapping("/bpmProcInst/createBasicAuth/{procDefId}/{ticketId}")
    public ResponseEntity<?> createTicketBasicAuth(@PathVariable String procDefId,
                                                   @PathVariable Long ticketId,
                                                   @RequestBody StartProcessInstanceDto startProcessInstanceDto) {
        try {

            ResponseEntity<?> response = bpmProcInstManager.createTicketBasicAuth(procDefId, ticketId, startProcessInstanceDto);

            try {
                if (response.getBody() instanceof ResponseDto) {

                    ResponseDto responseDto = (ResponseDto) response.getBody();

                    Map<String, Object> data = (Map<String, Object>) responseDto.getData();

                    Long saveTicketId = (Long) data.get("ticketId");

                    String procInstId = data.get("procInstId").toString();

                    bpmProcInstManager.createReport(procInstId, saveTicketId);
                    if(!startProcessInstanceDto.getIsDraft())
                        bpmProcInstManager.handleSaveVersionTemplateProcinst(data, procDefId);

                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

            return response;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Get assistants")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get Success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get Fail, server interal, processing error", content = @Content),
            @ApiResponse(responseCode = "400", description = "Get Fail, bad request, wrong param", content = @Content),
    })
    @PostMapping("/bpmProcInst/get-assistants-opinion")
    public ResponseEntity<?> getAssistantOpinion(@RequestBody AssistantOpinionDto request) {
        try {
            if (StringUtil.isEmpty(request.getTicketId())) {
                return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
            }
            PageDto response = assistantService.getAssistantsOpinion(request);
            return responseUtils.getResponseEntity(response, 1, "success", HttpStatus.OK);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Something wrong", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/bpmProcInst/get-assistants")
    public ResponseEntity<?> getAssistant(@RequestParam("ticketId") String ticketId) {
        try {
            if (StringUtil.isEmpty(ticketId)) {
                return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
            }
            List<Assistant> response = assistantService.getListAssistantByTicketId(ticketId);
            if (!response.isEmpty()) {
                response.stream().map(item -> {
                    item.setFormatedCreateAt(DateTimeUtils.dateToString(item.getCreateAt(), "dd/MM/yyyy HH:mm:ss"));
                    item.setFormatedUpdateAt(DateTimeUtils.dateToString(item.getUpdateAt(), "dd/MM/yyyy HH:mm:ss"));
                    return item;
                }).collect(Collectors.toList());
            }
            return responseUtils.getResponseEntity(response, 1, "success", HttpStatus.OK);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Something wrong", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get opinon")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get Success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get Fail, server interal, processing error", content = @Content),
            @ApiResponse(responseCode = "400", description = "Get Fail, bad request, wrong param", content = @Content),
    })
    @GetMapping("/bpmProcInst/get-opinion")
    public ResponseEntity<?> getOpinion(@RequestParam("assistantEmail") String assistantEmail) {
        try {
            if (StringUtil.isEmpty(assistantEmail)) {
                return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
            }
            List<AssistantOpinion> response = assistantOpinionServices.getAssistantOpinionByAssistantEmail(assistantEmail);
            if (!response.isEmpty()) {
                response.stream().map(item -> {
                    item.setFormatedCreateAt(DateTimeUtils.dateToString(item.getCreateAt(), "dd/MM/yyyy HH:mm:ss"));
                    item.setFormatedUpdateAt(DateTimeUtils.dateToString(item.getUpdateAt(), "dd/MM/yyyy HH:mm:ss"));
                    return item;
                }).collect(Collectors.toList());
            }
            return responseUtils.getResponseEntity(response, 1, "success", HttpStatus.OK);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Something wrong", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Create opinon")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Crete Success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Crete Fail, server interal, processing error", content = @Content),
            @ApiResponse(responseCode = "400", description = "Crete Fail, bad request, wrong param", content = @Content),
    })
    @PostMapping("/bpmProcInst/create-opinion")
    public ResponseEntity<?> createOpinion(@RequestBody List<AsisstantOpinionCreate> resquests) {
        try {
            if (resquests == null || resquests.isEmpty()) {
                return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
            }

            BpmProcInst bpmProcInst = bpmProcInstManager.findById(Long.valueOf(resquests.get(0).getTicketId()));

            List<String> ignoreStatus = Arrays.asList(TaskConstants.Status.DELETED_BY_RU.code,
                    TaskConstants.Status.RE_CREATED_BY_RU.code,
                    TaskConstants.Status.COMPLETED.code,
                    TaskConstants.Status.CANCEL.code);
            //Check status ticket
            if (ignoreStatus.contains(bpmProcInst.getTicketStatus())) {
                return responseUtils.getResponseEntity(null, -1, messageSource.getMessage("message.ticket.status-change", null, Locale.getDefault()), HttpStatus.BAD_REQUEST);
            }
            List<AssistantOpinion> response = new ArrayList<>();
            resquests.forEach(item -> response.add(
                    AssistantOpinion.builder()
                            .fileUrl(item.getUrl())
                            .fileName(item.getName())
                            .status(item.getStatus())
                            .opinion(item.getOpinion())
                            .assistantEmail(item.getAssistantEmail())
                            .ticketId(Long.valueOf(item.getTicketId()))
                            .createAt(new Date())
                            .updateAt(new Date())
                            .build()
            ));
            if (!response.isEmpty()) {
                assistantOpinionServices.saveAll(response);
                AssistantOpinion assistantOpinion = response.get(0);
                //Lưu ý kiến thật mới gửi thông báo
                if (assistantOpinion != null && assistantOpinion.getStatus() == 1) {
                    Long ticketId = assistantOpinion.getTicketId();

                    //Thông báo cho user liên quan
                    NotificationUser request = new NotificationUser();
                    request.setBpmProcdefId(null);
                    request.setNextTaskDefKey(ProcInstConstants.Notifications.ASSISTANT_OPINION.code);
                    request.setVariables(new HashMap<>());
                    request.setTicketId(ticketId);
                    request.setIsGetOldVariable(true);
                    request.setLstCustomerEmails(null);
                    request.setActionCode(ProcInstConstants.Notifications.ASSISTANT_OPINION.code);
                    request.setEmailExe(credentialHelper.getJWTPayload().getUsername());
                    kafkaTemplate.send(notificationUser, request);
//                    bpmProcdefNotificationService.addNotificationsByConfig(null, ProcInstConstants.Notifications.ASSISTANT_OPINION.code, new HashMap<>(),
//                            ticketId, true, null, ProcInstConstants.Notifications.ASSISTANT_OPINION.code);
                }
            }

            return responseUtils.getResponseEntity(response, 1, "success", HttpStatus.OK);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Something wrong", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Create opinion")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Crete Success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Crete Fail, server interal, processing error", content = @Content),
            @ApiResponse(responseCode = "400", description = "Crete Fail, bad request, wrong param", content = @Content),
    })
    @PostMapping("/bpmProcInst/update-opinion")
    public ResponseEntity<?> updateOpinion(@RequestBody List<AsisstantOpinionUpdate> resquests) {
        try {
            if (resquests == null || resquests.isEmpty()) {
                return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
            }

            BpmProcInst bpmProcInst = bpmProcInstManager.findById(Long.valueOf(resquests.get(0).getTicketId()));

            List<String> ignoreStatus = Arrays.asList(TaskConstants.Status.DELETED_BY_RU.code,
                    TaskConstants.Status.RE_CREATED_BY_RU.code,
                    TaskConstants.Status.COMPLETED.code,
                    TaskConstants.Status.CANCEL.code);
            //Check status ticket
            if (ignoreStatus.contains(bpmProcInst.getTicketStatus())) {
                return responseUtils.getResponseEntity(null, -1, messageSource.getMessage("message.ticket.status-change", null, Locale.getDefault()), HttpStatus.BAD_REQUEST);
            }
            List<AssistantOpinion> response = new ArrayList<>();
            resquests.forEach(item -> response.add(
                    AssistantOpinion.builder()
                            .id(item.getId())
                            .fileUrl(item.getUrl())
                            .fileName(item.getName())
                            .status(item.getStatus())
                            .opinion(item.getOpinion())
                            .assistantEmail(item.getAssistantEmail())
                            .ticketId(Long.valueOf(item.getTicketId()))
                            .updateAt(new Date())
                            .build()
            ));
            assistantOpinionServices.saveAll(response);
            return responseUtils.getResponseEntity(response, 1, "success", HttpStatus.OK);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Something wrong", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/bpmProcInst/delete-opinion")
    public ResponseEntity<?> deleteOpinion(@RequestBody Long[] resquests) {
        try {
            if (resquests == null || resquests.length == 0) {
                return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
            }
            for (Long id : resquests) {
                assistantOpinionServices.deleteById(id);
            }
            return responseUtils.getResponseEntity(null, 1, "success", HttpStatus.OK);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Something wrong", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Update Ticket Additional Request")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK",
                    content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Failed",
                    content = @Content)})
    @PutMapping("/bpmProcInst/update")
    public ResponseEntity<?> updateTicketAdditionalRequest(@RequestBody StartProcessInstanceDto startProcessInstanceDto) {
        try {
            BpmProcInst currentTicket = bpmProcInstManager.findById(startProcessInstanceDto.getTicketId());
            Boolean result = bpmProcInstManager.updateTicketAdditionalRequest(currentTicket, startProcessInstanceDto);
            if (Boolean.TRUE.equals(result)) {
                reportProducer.sendKafka(currentTicket.getTicketId(), insertReportByGroup);
                return responseUtils.getResponseEntity(null, 1, "success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "message.task.additional-request.completed.fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/closeTicket")
    public ResponseEntity<?> closeTicket(@RequestBody TicketRequest request,
                                         @RequestParam(value = "taskDefKey", required = false) String taskDefKey) {
        try {
            request.setActionUser(credentialHelper.getJWTPayload().getUsername());
            BpmProcInst result = bpmProcInstManager.closeTicket(request, taskDefKey);
            if (result != null) {
                return ResponseHelper.ok(result);
            }

            return ResponseHelper.fail();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/cancel/{id}")
    public ResponseEntity<?> cancelTicket(@RequestBody CancelTicketRequest body,
                                          @PathVariable String id,
                                          @RequestParam(value = "taskDefKey", required = false) String taskDefKey) {

        try {
            String reason = body.getReason();
            List<String> attachFiles = body.getAttachFiles();
            List<String> attachFilesName = body.getAttachFilesName();
            List<String> attachFilesSize = body.getAttachFilesSize();
            Long ticketId = body.getTicketId();
            String account = credentialHelper.getJWTPayload().getUsername();
            Map<String, Object> result = bpmProcInstManager.cancelTicket(ticketId, id, account, reason, taskDefKey, attachFiles, attachFilesName, attachFilesSize);
            if (result != null) {
                if (result.get("isSuccess").equals(Boolean.TRUE)) {
                    reportProducer.sendKafka(ticketId, insertReportByGroup);
                    List<BpmTask> tasks = bpmTaskManager.getTaskByProcInstId(id);
                    for (BpmTask task : tasks) {
                        reportProducer.sendKafka(task.getTaskId(), insertReportByChartNode);
                    }
                }
                return responseUtils.getResponseEntity(result, 1, "Succes", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/bpmProcInst/requestUpdate")
    public ResponseEntity<?> requestUpdate(@RequestBody RequestUpdateDto req) {
        try {
            req.setUser(credentialHelper.getJWTPayload().getUsername());
            Map<String, Object> result = bpmRuHistoryManager.requestUpdate(req);
            if (result != null) {
                if (result.get("isSuccess").equals(Boolean.TRUE)) {
                    reportProducer.sendKafka(req.getTicketId(), insertReportByGroup);
                    List<BpmTask> taskReport = bpmTaskRepository.getBpmTaskByTaskProcInstId(req.getProcInstId());
                    for (BpmTask e : taskReport) {
                        reportProducer.sendKafka(e.getTaskId(), insertReportByChartNode);
                    }
                }
                return ResponseHelper.ok(result);
            }

            return ResponseHelper.fail();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping(value = "/bpmProcInst/discussion", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<?> discussion(@RequestPart(value = "file", required = false) MultipartFile[] files,
                                        @RequestParam("content") String content,
                                        @RequestParam("ticketId") String ticketId,
                                        @RequestParam(value = "groupId", required = false) String groupId,
                                        @RequestParam(value = "discusId", required = false) String discusId,
                                        @RequestParam(value = "idFilesDelete", required = false) List<Long> idFilesDelete,
                                        @RequestParam(value = "typeDiscussion") Long typeDiscussion,
                                        @RequestParam(value = "ticketIntId") Long ticketIntId,
                                        @RequestParam(value = "isAdditionalRequest") Boolean isAdditionalRequest,
                                        @RequestParam(value = "isCreateUserAdditionalRequest") Boolean isCreateUserAdditionalRequest

    ) {
        try {
            Boolean result = bpmProcInstManager.discussion(files, content, ticketId, groupId, discusId, idFilesDelete, typeDiscussion, ticketIntId, isAdditionalRequest, isCreateUserAdditionalRequest);
            if (Boolean.TRUE.equals(result)) {
                reportProducer.sendKafka(ticketIntId, insertReportByGroup);
                return ResponseHelper.ok();
            }
            return ResponseHelper.fail();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping(value = "/bpmProcInst/discussionAdditionalRequest", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<?> discussionAdditionalRequest(@RequestPart(value = "file", required = false) MultipartFile[] files,
                                                         @RequestParam("content") String content,
                                                         @RequestParam("ticketId") String ticketId,
                                                         @RequestParam(value = "groupId", required = false) String groupId,
                                                         @RequestParam(value = "discusId", required = false) String discusId,
                                                         @RequestParam(value = "idFilesDelete", required = false) List<Long> idFilesDelete,
                                                         @RequestParam(value = "typeDiscussion") Long typeDiscussion,
                                                         @RequestParam(value = "ticketIntId") Long ticketIntId,
                                                         @RequestParam(value = "isAdditionalRequest") Boolean isAdditionalRequest,
                                                         @RequestParam(value = "isCreateUserAdditionalRequest") Boolean isCreateUserAdditionalRequest

    ) {
        try {
            Boolean result = bpmProcInstManager.discussionAdditionalRequest(files, content, ticketId, groupId, discusId, idFilesDelete, typeDiscussion, ticketIntId, isAdditionalRequest, isCreateUserAdditionalRequest);
            if (Boolean.TRUE.equals(result)) {
                reportProducer.sendKafka(ticketIntId, insertReportByGroup);
                return ResponseHelper.ok();
            }
            return ResponseHelper.fail();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.error(e);
        }
    }

    @PostMapping("/bpmProcInst/getDiscussion")
    public ResponseEntity<?> getDiscussion(@RequestBody DiscussionRequest req) {
        try {
            String email = credentialHelper.getJWTPayload().getEmail();
            req.setEmail(email);
            PageDto result = bpmProcInstManager.getDiscussion(req);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/bpmProcInst/Discussion/last")
    public ResponseEntity<?> LastDiscussion(@RequestParam("procInstId") String procInstId) {
        try {
            DiscussionResponse result = bpmProcInstManager.getLastComment(procInstId);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/bpmProcInst/Discussion/count")
    public ResponseEntity<?> countDiscussion(@RequestParam("procInstId") String procInstId) {
        try {
            Long result = bpmProcInstManager.CountCmtByTicket(procInstId);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("/bpmProcInst/deleteDiscussion")
    public ResponseEntity<?> deleteDiscus(@RequestParam("discusId") Long discusId) {
        try {
            Boolean result = bpmProcInstManager.deleteDiscussion(discusId);
            if (result) {
                return responseUtils.getResponseEntity(null, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @PostMapping("bpmProcInst/share/create")
    public ResponseEntity<?> createShare(@RequestBody Map<String, Object> body) {
        try {
            String email = credentialHelper.getJWTPayload().getUsername();
            Boolean result = bpmProcInstManager.createShared(body, email);
            if (result) {
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(result, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("bpmProcInst/share/load")
    public ResponseEntity<?> createShare(@RequestBody ShareRequest body) {
        try {
            String email = credentialHelper.getJWTPayload().getUsername();
            PageDto result = bpmProcInstManager.loadShared(body, email);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(result, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("bpmProcInst/share/delete")
    public ResponseEntity<?> createShare(@RequestParam("shareId") Long id) {
        try {
            Boolean result = bpmProcInstManager.deleteShared(id);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(result, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("bpmProcInst/share/cancelMonitor")
    public ResponseEntity<?> cancelMonitor(@RequestParam("procInstId") String procInstId) {
        try {
            String email = credentialHelper.getJWTPayload().getEmail();
            Boolean result = bpmProcInstManager.cancelMonitor(procInstId, email.substring(0, email.indexOf("@")));
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(result, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("bpmProcInst/getRolePermission")
    public ResponseEntity<?> getRolePermission(@RequestParam("procInstId") String procInstId) {
        try {
            List<String> result = bpmProcInstManager.getListRolePermission(procInstId);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(result, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Check user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Check user is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Check user is fail", content = @Content)})
    @GetMapping("bpmProcInst/checkUser")
    public ResponseEntity<?> checkUser(@RequestParam("account") String account) {
        log.info("Entering checkUser()...");
        try {
            return ResponseHelper.ok(bpmProcInstManager.checkUser(account));
        } catch (Exception e) {
            log.error("Error checkUser: {}", e.getMessage());
            return ResponseHelper.fail();
        }
    }

    @PostMapping("bpmProcInst/query")
    @Operation(summary = "Query process-instance data by conditions")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK",
                    content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Failed",
                    content = @Content)})
    public ResponseEntity<?> query(@Valid @RequestBody QueryRequest<TicketFilter> request) {
        long startTime = LogUtils.logBegin(log);
        try {
            return ResponseHelper.ok(bpmProcInstManager.query(request));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @PostMapping("assistant/ticket-count")
    @Operation(summary = "Count process-instance data by conditions")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK",
                    content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Failed",
                    content = @Content)})
    public ResponseEntity<?> assistantTicketCount(@Valid @RequestBody TicketAssistantFilter sample) {
        long startTime = LogUtils.logBegin(log);
        try {
            return ResponseHelper.ok(bpmProcInstManager.assistantTicketCount(sample));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @PostMapping("bpmProcInst/count")
    @Operation(summary = "Count process-instance data by conditions")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK",
                    content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Failed",
                    content = @Content)})
    public ResponseEntity<?> count(@Valid @RequestBody TicketFilter sample) {
        long startTime = LogUtils.logBegin(log);
        try {
            return ResponseHelper.ok(bpmProcInstManager.count(sample));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    /**
     * @param procInstId String
     * @return bpm_procinstdto
     * <AUTHOR> - 08/12/2022
     */
    @PostMapping("bpmProcInst/getDraft")
    @Operation(summary = "Get Draft or duplicate ticket information by procInstId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK",
                    content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "400", description = "BAD_REQUEST",
                    content = {@Content}),
            @ApiResponse(responseCode = "404", description = "NOT_FOUND",
                    content = {@Content}),
            @ApiResponse(responseCode = "500", description = "Failed",
                    content = @Content)})
    public ResponseEntity<?> getDraftByProcDefId(@Valid @RequestParam("procInstId") String procInstId) {
        long startTime = LogUtils.logBegin(log);
        try {
            if (StringUtil.isEmpty(procInstId)) {
                return ResponseHelper.fail(null, "Param is not valid. ProcInstId not must be null");
            }
            BpmProcInst result = bpmProcInstManager.findBpmProcInstByTicketProcInstId(procInstId);
            if (result != null) {
                return ResponseHelper.ok(result);
            }
            return ResponseHelper.notFound(null, "Không tìm thấy bản ghi !");
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @GetMapping("bpmProcInst/getDefaultTicket")
    @Operation(summary = "Get default ticket information by procInstId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK",
                    content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "400", description = "BAD_REQUEST",
                    content = {@Content}),
            @ApiResponse(responseCode = "404", description = "NOT_FOUND",
                    content = {@Content}),
            @ApiResponse(responseCode = "500", description = "Failed",
                    content = @Content)})
    public ResponseEntity<?> getDefaultByProcInstId(@Valid @RequestParam("ticketId") Long ticketId) {
        long startTime = LogUtils.logBegin(log);
        try {
            if (StringUtil.isEmpty(ticketId)) {
                return ResponseHelper.fail(null, "Param is not valid. ProcInstId not must be null");
            }
            TicketDefaultResponse result = bpmProcInstManager.getDefaultByProcInstId(ticketId);
            if (result != null) {
                return ResponseHelper.ok(result);
            }
            return ResponseHelper.notFound(null, "Không tìm thấy bản ghi !");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @GetMapping("bpmProcInst/getLocation")
    public ResponseEntity<?> getLocationByUserName() {
        long startTime = LogUtils.logBegin(log);
        try {
            String account = credentialHelper.getJWTPayload().getUsername();
            LocationManagementResponse locationManagementResponse = bpmProcInstManager.getLocationByEmail(account);
            if (locationManagementResponse != null) {
                return ResponseHelper.ok(locationManagementResponse);
            }
            return ResponseHelper.notFound(null, "Không tìm thấy bản ghi !");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @PostMapping("bpmProcInst/get-ticket-by-service-ids")
    @Operation(summary = "Query process-instance data by conditions")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK",
                    content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Failed",
                    content = @Content)})
    public ResponseEntity<?> getTicketByServiceIds(@Valid @RequestBody List<Long> serviceIds,
                                                   @RequestParam(value = "pageNumber", required = false) Integer pageNumber,
                                                   @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                   @RequestParam(value = "isGetChildSpecialFlow", required = false, defaultValue = "false") Boolean isGetChildSpecialFlow
    ) {
        long startTime = LogUtils.logBegin(log);
        try {
            return ResponseHelper.ok(bpmProcInstManager.getTicketByServiceIds(serviceIds, pageNumber, pageSize, isGetChildSpecialFlow));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @Operation(summary = "Get ticket link")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ticket link is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ticket link  is fail", content = @Content)})
    @GetMapping("bpmProcInst/getTicketLink/{ticketId}")
    public ResponseEntity<?> getTicketLink(@PathVariable Long ticketId) {
        log.info("Entering getTicketLink()...");
        try {
            return ResponseHelper.ok(bpmProcInstManager.getTicketLink(ticketId));
        } catch (Exception e) {
            log.error("Error getTicketLink: {}", e.getMessage());
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Get ticket")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ticket is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ticket is fail", content = @Content)})
    @GetMapping("bpmProcInst/getTicket/{ticketId}")
    public ResponseEntity<?> getTicket(@PathVariable Long ticketId) {
        log.info("Entering getTicket()...");
        try {
            return ResponseHelper.ok(bpmProcInstManager.getTicket(ticketId));
        } catch (Exception e) {
            log.error("Error getTicket: {}", e.getMessage());
            return ResponseHelper.fail();
        }
    }

    @PostMapping("bpmProcInst/getListAutoCancel")
    public ResponseEntity<?> autoCancelTicket(@RequestBody AutoCancelRequest autoCancelRequest) {
        log.info("Entering getLocation()...");
        try {
            bpmProcInstManager.autoCancel(autoCancelRequest);
            return ResponseHelper.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("bpmProcInst/waringExpirationTimeComplete")
    public ResponseEntity<?> waringExpirationTimeComplete(@RequestBody AutoCancelRequest autoCancelRequest) {
        log.info("Entering getLocation()...");
        try {
            bpmProcInstManager.autoCancel(autoCancelRequest);
            return ResponseHelper.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @GetMapping("bpmProcInst/getAllTicketId")
    public ResponseEntity<?> getListTicketId(@RequestParam("status") String status) {
        log.info("Entering getLocation()...");
        try {
            return ResponseHelper.ok(bpmProcInstManager.getListTicketId(status));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @GetMapping("bpmProcInst/autoCloseTicket")
    public ResponseEntity<?> autoCloseTicket() {
        log.info("Entering auto close ticket");
        long startTime = LogUtils.logBegin(log);
        try {
            bpmProcInstManager.autoCloseTicket();
            return ResponseHelper.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @Operation(summary = "Start recall ticket")
    @PostMapping("bpmProcInst/{ticket-id}/recall")
    public ResponseEntity<?> recall(@PathVariable("ticket-id") Long ticketId,
                                    @Valid @RequestBody TicketRecallRequest request) {
        long startTime = LogUtils.logBegin(log);
        try {
            bpmProcInstManager.recall(ticketId, request);
            reportProducer.sendKafka(ticketId, insertReportByGroup);
            BpmProcInst currentTicket = bpmProcInstManager.findBpmProcInstById(ticketId);
            List<BpmTask> taskReports = bpmTaskRepository.getBpmTaskByTaskProcInstId(currentTicket.getTicketProcInstId());
            for (BpmTask e : taskReports) {
                reportProducer.sendKafka(e.getTaskId(), insertReportByChartNode);
            }

            return ResponseHelper.ok();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @PostMapping("/bpmProcInst/loadFilter")
    public ResponseEntity<?> loadFilter(@RequestBody LoadFilterTicketRequest request) {
        try {
            Map<String, Object> result = bpmProcInstManager.loadFilter(request);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Sucess", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


//    @GetMapping("/bpmProcInst/getAllFileByProcInstId")
//    public ResponseEntity<?> getAllFile(@RequestParam String procInstId) {
//        try {
//            List<HistoryFileResponse> result = bpmProcInstManager.getAllFileByBpmProcinst(procInstId);
//            if (result != null) {
//                return responseUtils.getResponseEntity(result, 1, "Sucess", HttpStatus.OK);
//            }
//            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
//        } catch (Exception e) {
//            e.printStackTrace();
//            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }

    @GetMapping("/bpmProcInst/getAllFileByTicketId")
    public ResponseEntity<?> getAllFile(@RequestParam Long ticketId) {
        try {
            List<HistoryFileResponse> result = bpmProcInstManager.getAllFileByTicketId(ticketId);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Sucess", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/bpmProcInst/getAllUserTaskInfo")
    public ResponseEntity<?> getAllUserTaskInfo(@RequestParam Long ticketId) {
        try {
            List<TaskInfoResponse> result = bpmProcInstManager.getAllUserTaskInfo(ticketId);
            return ResponseHelper.ok(result);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        }
    }

    @GetMapping("/bpmProcInst/getAllAssigneeForDrawFlow")
    public ResponseEntity<?> getAllAssigneeForDrawFlow(@RequestParam Long ticketId) {
        try {
            Map<String, Object> result = bpmProcInstManager.getAllAssigneeForDrawFlow(ticketId);
            return ResponseHelper.ok(result);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        }
    }

    @GetMapping("/bpmProcInst/getAllListUserNameUserTask")
    public ResponseEntity<?> getAllListUserNameUserTask(@RequestParam Long ticketId) {
        try {
            List<String> result = bpmProcInstManager.getAllListUserNameUserTask(ticketId);
            return ResponseHelper.ok(result);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        }
    }

    @GetMapping("/bpmProcInst/getBpmProcinstStatisticByUser")
    public ResponseEntity<?> getBpmProcinstStatisticByUser() {
        try {
            Map<String, Integer> result = bpmProcInstManager.getBpmProcinstStatisticByUser();
            return ResponseHelper.ok(result);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        }
    }

    @PostMapping("/bpmProcInst/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody LoadTicketDto criteria) {
        try {
            String account = credentialHelper.getJWTPayload().getUsername();
            Object data = businessManager.getFilterData(bpmProcInstManager.searchFilter(criteria, account), FilterDataEnum.BPM_PROCINST);

            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(data).message("Thành Công").build());
        } catch (Exception e) {
            log.error("Error searchChart: {}", e.getMessage());
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/bpmProcInst/getFilterDataAssistant")
    public ResponseEntity<?> getFilterDataAssistant(@RequestBody LoadAssistantTicketDto criteria) {
        try {
            List<Map<String, Object>> result = bpmProcInstManager.searchByListAssistantEmailFilter(criteria);
            try {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(businessManager.getFilterData(result, FilterDataEnum.BPM_PROCINST)).
                        message("Thành Công").build());
            } catch (Exception e) {
                return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
            }
        } catch (Exception e) {
            log.error("Error searchChart: {}", e.getMessage());
            return ResponseHelper.fail();
        }
    }

    @GetMapping("/bpmProcInst/getListTicketCompletedInfo")
    public ResponseEntity<?> getListTicketCompletedInfo() {
        try {
            String username = credentialHelper.getJWTPayload().getUsername();
            List<TicketCompletedResponse> result = bpmProcInstManager.getListTicketCompletedInfo(username);
            return ResponseHelper.ok(result);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        }
    }

    @GetMapping("/bpmProcInst/getListAssigneeTicketCompleted")
    public ResponseEntity<?> getListAssigneeTicketCompleted(@RequestParam("ticketId") Long ticketId) {
        try {
            List<String> result = bpmProcInstManager.getListAssigneeTicketCompleted(ticketId);
            return ResponseHelper.ok(result);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        }
    }

    @PostMapping("bpmProcInst/cancelByTicket")
    public ResponseEntity<?> cancelByTicket(@RequestBody CancelByTicketRequest request) {
        log.info("Entering cancelByTicket()...");
        try {
            Map<String, Object> response = bpmProcInstManager.cancelByTicket(request);
            return ResponseHelper.ok(response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("bpmProcInst/getListAssistantTicket")
    public ResponseEntity<?> getListAssistantTicket(@RequestBody TicketAssistantFilter request) {
        try {
            List<String> response = assistantService.getListAssistantTicket(request.getUsers());
            return ResponseHelper.ok(response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("bpmProcInst/getListTaskDefKeyByServiceId")
    public ResponseEntity<?> getListAssistantTicket(@RequestBody GetTicketDto request)  {
        String username = credentialHelper.getJWTPayload().getUsername();
        List<Map<String, Object>> response = bpmProcInstManager.getListTaskDefKeyByServiceId(request.getListServiceId(), username);
        return ResponseHelper.ok(response);
    }

    @PostMapping("bpmProcInst/checkUserInTicket")
    public ResponseEntity<?> checkUserInTicket(@RequestBody TicketAssistantFilter request) {
        try {
            return ResponseHelper.ok(bpmProcInstManager.checkUserInTicket(request.getUsers()));
        } catch (Exception e) {
            return ResponseHelper.fail();
        }
    }
}
