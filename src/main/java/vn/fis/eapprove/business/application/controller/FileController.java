package vn.fis.eapprove.business.application.controller;

import vn.fis.eapprove.security.CredentialHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vn.fis.eapprove.business.domain.fileCondition.service.FileService;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.util.LogUtils;
import vn.fis.spro.file.exception.FileOperationException;
import vn.fis.spro.file.manager.FileManager;

import java.util.List;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@Slf4j
@RestController("FileControllerV1")
@CrossOrigin("*")
@RequestMapping(SERVICE_PATH)
@Tag(name = "file-service", description = "The file services")
public class FileController {

    private final CredentialHelper credentialHelper;
    private final FileManager fileManager;
    private final FileService fileService;

    @Autowired
    public FileController(CredentialHelper credentialHelper,
                          FileManager fileManager,
                          FileService fileService) {
        this.credentialHelper = credentialHelper;
        this.fileManager = fileManager;
        this.fileService = fileService;
    }

    @Value("${app.s3.bucket}")
    private String bucket;

    @Operation(summary = "Download file from object storage")
    @GetMapping("/file/download")
    public ResponseEntity<byte[]> download(@RequestParam String filename) throws FileOperationException {
        log.info("Entering download()...");

        byte[] content = fileManager.getFile(bucket, filename);
        return ResponseEntity.ok().body(content);
    }

    @Operation(summary = "Upload multi files from client")
    @PostMapping(value = "/file/upload-multi", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<?> uploadMulti(@RequestPart("files") MultipartFile[] files,
                                         @Parameter(description = "Type of file upload to determine folder: "
                                                 + CommonConstants.FileUploadType.TICKET
                                                 + ", "
                                                 + CommonConstants.FileUploadType.SIGN)
                                         @RequestParam(value = "type", required = false) String type,
                                         @RequestParam(value = "folderByDate", required = false, defaultValue = "true") boolean folderByDate) {
        long startTime = LogUtils.logBegin(log);

        try {
            return ResponseHelper.ok(fileService.saveAll(files, type, folderByDate));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @Operation(summary = "Upload file from client")
    @PostMapping(value = "/file/upload", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<?> upload(@RequestParam("file") MultipartFile file,
                                    @Parameter(description = "Type of file upload to determine folder: "
                                            + CommonConstants.FileUploadType.TICKET
                                            + ", "
                                            + CommonConstants.FileUploadType.SIGN)
                                    @RequestParam(value = "type", required = false) String type,
                                    @RequestParam(value = "folderByDate", required = false, defaultValue = "true") boolean folderByDate) {
        long startTime = LogUtils.logBegin(log);

        try {
            return ResponseHelper.ok(fileService.save(file, type, folderByDate));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @Operation(summary = "Get Url by FileName")
    @PostMapping(value = "/file/getFileUrl")
    public ResponseEntity<?> getFileUrl(@RequestParam("fileName") String fileName) {
        long startTime = LogUtils.logBegin(log);

        try {
            return ResponseHelper.ok(fileManager.getUrlFile(bucket,fileName));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @Operation(summary = "Upload File and folder")
    @PostMapping(value = "/file/uploadByFileName")
    public ResponseEntity<?> uploadByFolder(@RequestParam("files") MultipartFile[] file,
                                            @RequestParam(value = "folder",defaultValue = "/") String folder,
                                            @RequestParam("fileName") List<String> fileName) {
        long startTime = LogUtils.logBegin(log);

        try {
            return ResponseHelper.ok(fileService.uploadByFileName(file,folder,fileName));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }
}
