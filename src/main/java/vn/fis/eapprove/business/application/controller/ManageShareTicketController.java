package vn.fis.eapprove.business.application.controller;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.manageApi.service.ManageShareTicketService;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.CreateShareTicketRequest;
import vn.fis.eapprove.business.model.request.ProcessShareTicketRequest;
import vn.fis.eapprove.business.model.request.SearchShareTicketRequest;
import vn.fis.eapprove.business.model.response.ManageShareTicketDetailResponse;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.helper.ResponseHelper;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("ManageShareTicketControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/manage-share-ticket")
public class ManageShareTicketController {

    @Autowired
    private ManageShareTicketService manageShareTicketService;

    @Autowired
    private BusinessManager businessManager;

    @PostMapping("/create-update")
    public ResponseEntity<?> createUpdate(@RequestBody CreateShareTicketRequest request) {
        // validate - status = active
        if (request.getStatus().equalsIgnoreCase("active")) {
            String messageValid = manageShareTicketService.validate(request);
            if (!messageValid.isEmpty()) {
                return ResponseHelper.invalid(messageValid);
            }
        }
        boolean result = manageShareTicketService.createUpdate(request);
        if (result) {
            return ResponseHelper.ok();
        }
        return ResponseHelper.fail();
    }

    @PostMapping("/update-status")
    public ResponseEntity<?> updateStatus(@RequestBody CreateShareTicketRequest request)  {
        // validate - active shareTicket
        if (request.getStatus().equalsIgnoreCase("active")) {
            String messageValid = manageShareTicketService.validateUpdateStatus(request);
            if (!messageValid.isEmpty()) {
                return ResponseHelper.invalid(messageValid);
            }
        }
        boolean result = manageShareTicketService.updateStatus(request.getId(), request.getStatus());
        if (result) {
            return ResponseHelper.ok();
        }
        return ResponseHelper.fail(null,"Không tìm thấy bản ghi!");
    }

    @PostMapping("/search")
    public ResponseEntity<?> search(@RequestBody SearchShareTicketRequest request) {
        try {
            PageDto page = manageShareTicketService.search(request);
            return ResponseHelper.ok(page);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail(null, e.getMessage());
        }
    }

    @PostMapping("/search-filter")
    public ResponseEntity<?> searchFilter(@RequestBody SearchShareTicketRequest request) {
        try {
            Object result = businessManager.getFilterData(manageShareTicketService.searchFilter(request), FilterDataEnum.MANAGE_SHARE_TICKET);
            return ResponseHelper.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail(null, e.getMessage());
        }
    }

    @GetMapping("/get-detail/{id}")
    public ResponseEntity<?> getDetail(@PathVariable Long id) {
        ManageShareTicketDetailResponse manageShareTicket = manageShareTicketService.getDetail(id);
        if (manageShareTicket != null) {
            return ResponseHelper.ok(manageShareTicket);
        }
        return ResponseHelper.fail(null, "Không tìm thấy bản ghi!");
    }

    @PostMapping("/testShareTicket")
    public ResponseEntity<?> testShareTicket(@RequestBody ProcessShareTicketRequest request) {
        manageShareTicketService.processShareTicket(
                request.getProcInstId(),
                request.getProcDefId(),
                request.getCompanyCode(),
                request.getCreatedUser(),
                request.getChartNodeCode(),
                request.getServiceId(),
                request.getCreatedDate()
        );
        return ResponseHelper.ok();
    }

}
