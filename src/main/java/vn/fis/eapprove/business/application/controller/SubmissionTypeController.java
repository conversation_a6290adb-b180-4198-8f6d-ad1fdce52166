package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.submission.entity.SubmissionType;
import vn.fis.eapprove.business.domain.submission.service.SubmissionTypeManager;
import vn.fis.eapprove.business.dto.PageSearchDto;
import vn.fis.eapprove.business.dto.SearchSubmissionDto;
import vn.fis.eapprove.business.dto.SubmissionTypeDto;
import vn.fis.eapprove.business.model.request.SubmissionTypeRequest;
import vn.fis.eapprove.business.model.response.SubmissionTypeResponse;

import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.List;
import java.util.Locale;

import static vn.fis.eapprove.business.constant.Constant.*;

@RestController("SubmissionTypeControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/submissionType")
public class SubmissionTypeController {

    @Autowired
    SubmissionTypeManager submissionTypeManager;

    @Autowired
    MessageSource messageSource;

    @Autowired
    ResponseUtils responseUtils;

    @Autowired
    BusinessManager businessManager;

    @Operation(summary = "Get submissionType")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get submissionType  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get submissionType is fail", content = @Content)})
    @PostMapping("/searchSubmissionType")
    public ResponseEntity<?> searchSubmissionType(@RequestBody SearchSubmissionDto req) {
        try {
            PageSearchDto page = submissionTypeManager.searchSubmissionType(req);
            if (!CollectionUtils.isEmpty(page.getContent())) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(page).
                        message(messageSource.getMessage("message.submission_type.searchSubmissionType.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.submission_type.searchSubmissionType.fail", null, Locale.getDefault())).data(page.getContent()).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Create submissionType in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "create submissionType  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "create submissionType is fail", content = @Content)})
    @PostMapping("/createSubmissionType")
    public ResponseEntity<?> createSubmissionType(@RequestBody SubmissionTypeRequest submissionTypeRequest) {
        try {
            String status = submissionTypeManager.createSubmissionType(submissionTypeRequest);
            if (status == MESSAGE_SUCCESS) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).
                        message(messageSource.getMessage("message.submission_type.createSubmissionType.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.submission_type.createSubmissionType.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Get chartNode in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get chartNode  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get chartNode is fail", content = @Content)})
    @GetMapping("/getChartNodeByChartId")
    public ResponseEntity<?> getChartNodeByChartId(@RequestParam Long chartId) {
        return ResponseEntity.ok(submissionTypeManager.getChartNodeByChartId(chartId));
    }

    @Operation(summary = "Get account in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get account  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get account is fail", content = @Content)})
    @GetMapping("/shareWithSubmisstionType")
    public ResponseEntity<?> getAlluserInfo() {
        return ResponseEntity.ok(submissionTypeManager.getAlluserInfo());
    }


    @Operation(summary = "Check dupplicate name of submissionType")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Check dupplicate  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Check dupplicate is fail", content = @Content)})
    @GetMapping("/checkExistName")
    public ResponseEntity<?> checkExistName(@RequestParam("nameSubmissionType") String nameSubmissionType,
                                            @RequestParam(value = "id", required = false) Long id) {
        try {
            Boolean checkNameSubmissionType = submissionTypeManager.checkNameExits(nameSubmissionType, id);
            if (checkNameSubmissionType == true) {
                return responseUtils.getResponseEntity(true, 1, "Loại tờ trình đã tồn tại trong hệ thống", HttpStatus.OK);
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(checkNameSubmissionType).
                    message(messageSource.getMessage("message.submission_type.createSubmissionType.success", null, Locale.getDefault())).build());
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Update submissionType")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Update submissionType  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Update submissionType is fail", content = @Content)})
    @PutMapping("/updateSubmissionType")
    public ResponseEntity<?> updateSubmissionType(@RequestBody SubmissionTypeRequest submissionTypeRequest) {
        try {
            String status = submissionTypeManager.updateSubmissionType(submissionTypeRequest);
            if (status == MESSAGE_SUCCESS) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).
                        message(messageSource.getMessage("message.submission_type.updateSubmissionType.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.submission_type.updateSubmissionType.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Delete submissionType")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "delete submissionType  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "delete submissionType is fail", content = @Content)})
    @DeleteMapping("/deleteSubmissionType")
    public ResponseEntity<?> deleteSubmissionType(@RequestParam List<Long> listSubmissionId) {
        try {
            String status = submissionTypeManager.deleteSubmissionType(listSubmissionId);
            if (status == MESSAGE_SUCCESS) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).
                        message(messageSource.getMessage("message.submission_type.deleteSubmissionType.success", null, Locale.getDefault())).build());
            }
            if (status == MESSAGE_FAIL) {
                return ResponseEntity.ok().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                        .message(messageSource.getMessage("message.submission_type.deleteSubmissionType.fail", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.submission_type.deleteSubmissionType.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Clone submissionType")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "clone submissionType  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "clone submissionType is fail", content = @Content)})
    @PostMapping("/cloneSubmissionType")
    public ResponseEntity<?> cloneSubmissionType(@RequestParam("id") Long id) {
        try {
            SubmissionType submissionType = submissionTypeManager.cloneSubmissionType(id);
            if (!ValidationUtils.isNullOrEmpty(submissionType)) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).
                        message(messageSource.getMessage("message.submission_type.cloneSubmissionType.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.submission_type.cloneSubmissionType.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Get all submissionType")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get all submissionType  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get all submissionType is fail", content = @Content)})
    @GetMapping("/getAllSubmissionType")
    public ResponseEntity<?> getAllSubmissionType() {
        try {
            List<SubmissionTypeDto> submissionType = submissionTypeManager.getAllSubmissionType();
            if (!ValidationUtils.isNullOrEmpty(submissionType)) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(submissionType).
                        message(messageSource.getMessage("message.submission_type.getAllSubmissionType.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.submission_type.getAllSubmissionType.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }


    @Operation(summary = "Get filter data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get filter data  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get filter data is fail", content = @Content)})
    @PostMapping("/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody SearchSubmissionDto req) {
        try {
            List<SubmissionTypeResponse> page = submissionTypeManager.searchSubmissionTypeFilter(req);
            try {
                Object result = businessManager.getFilterData(page, FilterDataEnum.SUBMISSION_TYPE);
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                        message("Thành Công").build());
            } catch (Exception e) {
                return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
            }
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }
}
