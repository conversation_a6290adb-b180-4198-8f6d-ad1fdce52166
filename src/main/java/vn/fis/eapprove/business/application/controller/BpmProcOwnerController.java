package vn.fis.eapprove.business.application.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.bpm.service.BpmOwnerProcessManager;
import vn.fis.eapprove.business.utils.ResponseUtils;

import java.util.List;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@Slf4j
@RestController("BpmProcOwnerControllerV1")
@CrossOrigin("*")
@RequestMapping(SERVICE_PATH)
public class BpmProcOwnerController {
    @Autowired
    private ResponseUtils responseUtils;
    @Autowired
    private BpmOwnerProcessManager OwnerProcessManager;

    //Search ticket and load ticket
    @GetMapping("/bpmProcOwner/{procDefId}")
    public ResponseEntity<?> search(@PathVariable String procDefId) {
        try {
            List<String> result = OwnerProcessManager.getProcessOwnersByProcDefId(procDefId);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Sucess", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
