package vn.fis.eapprove.business.application.controller;

import vn.fis.eapprove.security.CredentialHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcInstManager;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcdefManager;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.service.ServicePackageManager;
import vn.fis.eapprove.business.dto.*;
import vn.fis.eapprove.business.tenant.manager.*;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.FileNameAwareByteArrayResource;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.model.request.QueryRequest;
import vn.fis.spro.common.model.response.PagingResponse;
import vn.fis.spro.common.util.LogUtils;
import vn.fis.spro.common.util.ValidationUtils;
import vn.fis.spro.file.manager.FileManager;

import jakarta.validation.Valid;
import java.util.*;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@Slf4j
@RestController("BpmProcdefControllerV1")
@CrossOrigin("*")
@RequestMapping(SERVICE_PATH)
@Tag(name = "bpm-proc-def-service", description = "The BPMN Process Definition API")
public class BpmProcdefController {

    @Autowired
    MessageSource messageSource;
    @Autowired
    private BpmProcdefManager bpmProcdefManager;
    @Autowired
    private BpmProcInstManager bpmProcInstManager;
    @Autowired
    private ServicePackageManager servicePackageManager;
    @Autowired
    private ResponseUtils responseUtils;
    @Autowired
    CredentialHelper credentialHelper;
    @Autowired
    private CamundaEngineService camundaEngineService;
    @Autowired
    private FileManager fileManager;

    @Autowired
    BusinessManager businessManager;

    @Value("${app.s3.bucket}")
    private String bucket;

    //get bpmProcDef
    @PostMapping("/bpmProcdef/get-bpm-for-combo")
    public ResponseEntity<?> getBpmProcdefForCombo(@RequestBody BpmProcdefDto criteria) {
        try {
            log.info("Start processing get bpm procdef for combo ---------> START SUCESSS ");
            PageDto result = bpmProcdefManager.getBpmProcdefForCombo(criteria);
            if (result != null) {
                log.info("End processing get bpm procdef for combo ---------> LOAD SUCESSS {} items", result.getContent().size());
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            log.info("End processing get bpm procdef for combo ---------> LOAD FAIL -  BAD_REQUEST");
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.info("End processing get bpm procdef for combo ---------> LOAD ERROR" + e.getMessage());
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    //Search bpmProcDef and load bpmProcDef
    @PostMapping("/bpmProcdef/search")
    public ResponseEntity<?> search(@RequestBody BpmProcdefDto criteria) {
        try {
            log.info("Start processing search");
            PageDto result = bpmProcdefManager.search(criteria);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/bmProcdef/share")
    public ResponseEntity<List<String>> shareWithUser() {
        return ResponseEntity.ok(bpmProcdefManager.shareWithUser());
    }

    //GetByName
    @PostMapping("/bpmProcdef/listByName")
    public ResponseEntity<?> getByName(@RequestParam String name) {
        try {
            List<BpmProcdefDto> bpmProcdefDtoList = bpmProcdefManager.listAllBpmProcdefNew(name);
            if (!ValidationUtils.isNullOrEmpty(bpmProcdefDtoList)) {
                return responseUtils.getResponseEntity(bpmProcdefDtoList, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(bpmProcdefDtoList, 1, "not found", HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Not Found", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/bpmProcdef/listByTemplateId")
    public ResponseEntity<?> getByTemplateId(@RequestBody BpmProcdefDto criteria, @RequestParam Long templateId) {
        try {
            PageDto bpmProcdefDtoList = bpmProcdefManager.listAllBpmProcdefByTemplateId(criteria, templateId);
            return responseUtils.getResponseEntity(bpmProcdefDtoList, 1, "Success", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Not Found", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    //Get all  name
    @GetMapping("/bpmProcdef")
    public ResponseEntity<?> getAll() {
        try {
            List<BpmProcdefDto> bpmProcdefDtoList = bpmProcdefManager.listAllBpmProcdef();
            return responseUtils.getResponseEntity(bpmProcdefDtoList, 1, "Success", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Not Found", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    //Get check
    @GetMapping("/bpmProcdef/check-type/{procDefId}")
    public ResponseEntity<?> checkProcessType(@PathVariable String procDefId) {
        try {
            Map<String, Object> bpmProcdefDtoList = bpmProcdefManager.checkProcessType(procDefId);
            if (bpmProcdefDtoList != null) {
                return ResponseHelper.ok(bpmProcdefDtoList);
            } else {
                return ResponseHelper.notFound();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @GetMapping("/bpmProcdef/check-type/{serviceId}/{procDefId}/{taskDefKey}")
    public ResponseEntity<?> checkProcessType(@PathVariable Long serviceId,
                                              @PathVariable String procDefId,
                                              @PathVariable String taskDefKey) {
        try {
            Map<String, Object> bpmProcdefDtoList = bpmProcdefManager.checkProcessType(procDefId, taskDefKey, serviceId);
            if (bpmProcdefDtoList != null) {
                return ResponseHelper.ok(bpmProcdefDtoList);
            } else {
                return ResponseHelper.notFound();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    //Auto check name process
    @GetMapping("/bpmProcdef/autoCheckName")
    public ResponseEntity<?> getActReProcdef(@RequestParam String name, @RequestParam String procDefId) {
        try {
            if (procDefId.isEmpty()) {
                log.info("bpmProcdef autoCheckName", name.trim());
                List<BpmProcdefDto> bpmProcdefDtoList = bpmProcdefManager.getBpmProcdefByName(name.trim());
                if (bpmProcdefDtoList == null || bpmProcdefDtoList.size() == 0) {
                    return responseUtils.getResponseEntity(false, 1, "Success", HttpStatus.OK);
                }
                return responseUtils.getResponseEntity(true, 1, "Quy trình đã tồn tại trong hệ thống", HttpStatus.OK);
            } else {
                BpmProcdef bpmProcdef = bpmProcdefManager.getById(Long.valueOf(procDefId));
                List<BpmProcdefDto> bpmProcdefDtoList = bpmProcdefManager.getBpmProcdefByName(name.trim());
                long dem = 0;
                for (BpmProcdefDto bpmProcdefDto : bpmProcdefDtoList) {
                    if (bpmProcdefDto.getName().trim().equals(name.trim())) {
                        dem++;
                    }
                    if (bpmProcdef.getName().trim().equals(name.trim())) {
                        dem--;
                    }
                }
                if (dem > 0) {
                    return responseUtils.getResponseEntity(true, 1, "Quy trình đã tồn tại trong hệ thống", HttpStatus.OK);
                }
                return responseUtils.getResponseEntity(false, 1, "Success", HttpStatus.OK);
            }

        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Not Found", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/bpmProcdef/{id}")
    public ResponseEntity<?> getActReProcdef(@PathVariable Long id) {
        try {
            log.info("process=get-actReProcDef, actReProcDef_id={}", id);
            String token = credentialHelper.getJWTToken();

            List<BpmProcDefDetailDTO> bpmProcdef = bpmProcdefManager.getDetail(id, token);
            if (bpmProcdef != null) {
                return responseUtils.getResponseEntity(bpmProcdef, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Not Found", HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Not Found", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @Operation(summary = "Get all bpmProcdef")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get priority  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get priority is fail", content = @Content)})
    @GetMapping("/bpmProcdefGetAll/{id}")
    public ResponseEntity<?> getDetail(@PathVariable Long id) {
        try {
            List<ProDefDetailDto> bpmProcdef = bpmProcdefManager.getAllDetail(id);
            if (bpmProcdef != null) {
                return responseUtils.getResponseEntity(bpmProcdef, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Not Found", HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            log.error(e.getMessage());
            return responseUtils.getResponseEntity(null, -1, "Error", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

//    @PostMapping("/bpmProcdef/data/variable")
//    public ResponseEntity<?> get(@RequestParam String procDefId, @RequestParam String formKeyChoose) {
//        return ResponseEntity.ok(bpmProcdefManager.getVariable1(procDefId, formKeyChoose));
//    }

    @Operation(summary = "Api load variable")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get variable is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "400", description = "Get variable is fail", content = @Content)})
    @PostMapping("/bpmProcdef/data/load-variable")
    public ResponseEntity<?> loadVariable(@RequestParam String json, @RequestParam String procDefId) {
        try {
            log.info("Start load variable ---> START LOAD");
            Map response = bpmProcdefManager.loadVariable(json, procDefId);
            if (response != null) {
                log.info("End load variable ---> END LOAD SUCCESS");
                return responseUtils.getResponseEntity(response, 1, "Load Sucess", HttpStatus.OK);
            } else {
                log.info("End load variable ---> END LOAD - NOT_FOUND");
                return responseUtils.getResponseEntity(null, -1, "Not Found", HttpStatus.NOT_FOUND);
            }

        } catch (Exception e) {
            log.info("End load variable ---> LOAD ERROR {}", e.getMessage());
            return responseUtils.getResponseEntity(null, -1, e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

//    @PostMapping("/bpmProcdef/data/list-before")
//    public ResponseEntity<?> getBefore(@RequestParam String json, @RequestParam String prodefId) {
//        return ResponseEntity.ok(bpmProcdefManager.getListBefore(json, prodefId));
//    }

    //Get approval task
    @GetMapping("/bpmProcdef/approval/{id}")
    public ResponseEntity<?> getApprovalTask(@PathVariable String id) {
        try {
            log.info("process=get-actReProcDef, actReProcDef_id={}", id);
            List<ApprovalTaskDto> approvalTasks = bpmProcdefManager.getApprovalTask(id);
            if (approvalTasks != null) {
                return ResponseHelper.ok(approvalTasks);
            }
            return ResponseHelper.notFound();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    //upload file lên minIO và xử lí file trả ra deploymentId
    @PostMapping(value = "/bpmProcdef/uploadFile", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<?> uploadFile(@RequestPart("file") MultipartFile file) {
        try {
            String fileName = file.getOriginalFilename();
            String[] tokens = fileName.split("\\.(?=[^\\.]+$)");
            String extension = tokens[1];
            if (!extension.equals("bpmn")) {
                return responseUtils.getResponseEntity(null, -2, "Sai định dạng file", HttpStatus.OK);
            }
            fileManager.putFile(bucket, fileName, file.getSize(), file.getInputStream());
            Map<String, Object> mapResponseCreate = camundaEngineService.createDeployment(file.getResource());
            String deploymentId = mapResponseCreate.get("id").toString();
            UploadFileDto uploadFileDto = UploadFileDto.builder().fileName(fileName).deploymentId(deploymentId).mapResponseCreate(mapResponseCreate).build();
            if (!deploymentId.isEmpty()) {
                return responseUtils.getResponseEntity(uploadFileDto, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "fail save database", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "File không hợp lẹ", HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/bpmProcdef/createBpm")
    public ResponseEntity<?> createBPM(@RequestBody CreateBPMRequest createBPMRequest) {
        try {
            BpmProcdef bpmProcdef = bpmProcdefManager.saveProcessInBPM(createBPMRequest);
            if (!ValidationUtils.isNullOrEmpty(bpmProcdef)) {
//                return responseUtils.getResponseEntity(bpmProcdefManager.saveProcessInBPM1(createBPMRequest), 1, "Success", HttpStatus.OK);
                return ResponseEntity.ok().body(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(bpmProcdef.getProcDefId())
                        .message(messageSource.getMessage("message.bpmProcdef.create.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.bpmProcdef.create.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    //Update Process
    @PostMapping(value = "/bpmProcdef/update")
    public ResponseEntity<?> updateProcdef(@RequestBody CreateBPMRequest createBPMRequest) {
        try {

            if (!createBPMRequest.getDeploymentId().isEmpty()) {
                return responseUtils.getResponseEntity(bpmProcdefManager.updateProcessInBPM(createBPMRequest), 1, "Success", HttpStatus.OK);

            }
            return responseUtils.getResponseEntity(null, -1, "fail save database", HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    //Deploy Process
    @PostMapping("/bpmProcdef/copy/{id}")
    public ResponseEntity<?> createCopy(@PathVariable("id") Long id) {
        try {
            BpmProcdef bpmProcdef = bpmProcdefManager.getById(id);
            Resource resource = new FileNameAwareByteArrayResource(bpmProcdef.getResourceName(), bpmProcdef.getBytes(), null);

            Map<String, Object> mapResponseCreate = camundaEngineService.createDeployment(resource);
            String deploymentId = mapResponseCreate.get("id").toString();
            BpmProcdef bpmProcDefCpoy = bpmProcdefManager.createCopy(bpmProcdef, deploymentId);
            if (bpmProcDefCpoy != null) {
                return responseUtils.getResponseEntity(bpmProcDefCpoy, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @DeleteMapping("/bpmProcdef")
    public ResponseEntity<?> deleteActReProcdef(@RequestParam List<Long> ids) {
        try {
            ProcdefDeleteResponse procdefDeleteResponse = new ProcdefDeleteResponse();
            Map<String, String> idFail = new HashMap<>();
            List<String> idSuccess = new ArrayList<>();

            for (Long id : ids) {
                BpmProcdef bpmProcdef = bpmProcdefManager.getById(id);
                List<ServicePackage> servicePackageList = servicePackageManager.listByProcessId(bpmProcdef.getId());
                log.info(servicePackageList.toString());
                if (bpmProcdef != null && servicePackageList.size() > 0) {
                    idFail.put(String.valueOf(id), "Quy trình có dữ liệu trong hệ thống. Không thể xóa");
                }
                if (bpmProcdef != null && servicePackageList.size() == 0) {
                    List<LoadTicketDto> loadTicketDtoList = bpmProcInstManager.getByProcDefId(bpmProcdef.getProcDefId());
                    if (loadTicketDtoList.size() == 0) {
                        bpmProcdefManager.delete(bpmProcdef);
                        idSuccess.add(String.valueOf(id));
                    }
                    bpmProcdefManager.delete(bpmProcdef);
                }
            }
            procdefDeleteResponse.setProcDefIdFail(idFail);
            procdefDeleteResponse.setProcDefIdSuccess(idSuccess);
            return responseUtils.getResponseEntity(procdefDeleteResponse, 1, "Success", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/bpmProcdef/files/{procDefId}")
    public ResponseEntity<?> downLoadFile(@PathVariable Long procDefId) {
        try {
            BpmProcdef bpmProcdef = bpmProcdefManager.getById(procDefId);
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + bpmProcdef.getResourceName() + "\"")
                    .body(bpmProcdef.getBytes());
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Find all bpm-process-definition")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK",
                    content = {@Content(mediaType = "application/json",
                            array = @ArraySchema(schema = @Schema(implementation = PagingResponse.class)))}),
            @ApiResponse(responseCode = "500", description = "Failed",
                    content = @Content)})
    @PostMapping("bpmProcdef/find-all")
    public ResponseEntity<?> query(@Valid @RequestBody QueryRequest<BpmProcdefDto> request) {
        long startTime = LogUtils.logBegin(log);
        try {
            return ResponseHelper.ok(bpmProcdefManager.findAll(request));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @Operation(summary = "get all filter name procdef")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK",
                    content = {@Content(mediaType = "application/json",
                            array = @ArraySchema(schema = @Schema(implementation = PagingResponse.class)))}),
            @ApiResponse(responseCode = "500", description = "Failed",
                    content = @Content)})
    @GetMapping("/bpmProcdef/getFilterName")
    public ResponseEntity<?> getFilterName() {
        try {
            GetAllNameDto bpmProcdefFilterNameDtos = bpmProcdefManager.getAllFilterName();
            return responseUtils.getResponseEntity(bpmProcdefFilterNameDtos, 1, "Success", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Not Found", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get template controls by procDefId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK",
                    content = {@Content(mediaType = "application/json",
                            array = @ArraySchema(schema = @Schema(implementation = PagingResponse.class)))}),
            @ApiResponse(responseCode = "500", description = "Failed",
                    content = @Content)})
    @GetMapping("/bpmProcdef/getTemplateForm")
    public ResponseEntity<?> getTemplateForm(@RequestParam String procDefId,@RequestParam(required = false, defaultValue = "false") Boolean getAllNoFilter) {
        try {
            List<TemplateFormControlDto> controls = bpmProcdefManager.getTemplateFormControls(procDefId,getAllNoFilter);
            return responseUtils.getResponseEntity(controls, 1, "Success", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Not Found", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Lay ds dich vu to trinh uy quyen")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get list service is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "400", description = "Get list service is fail", content = @Content)})
    @GetMapping("/bpmProcdef/getServicePackage")
    public ResponseEntity<?> getServicePackage() {
        try {
            return responseUtils.getResponseEntity(bpmProcdefManager.getServicePackage(), 1, "Success", HttpStatus.OK);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get All Filter Data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "bpmProcdef", description = "Get all filterData")
    @PostMapping("/bpmProcdef/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody BpmProcdefDto request) {
        try {
            Object result = businessManager.getFilterData(bpmProcdefManager.searchFilter(request), FilterDataEnum.BPM_PROC_DEF);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                    message(messageSource.getMessage("message.searchApiLog.searchApiAction.success", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }
}
