package vn.fis.eapprove.business.application.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.bpm.service.BpmService;
import vn.fis.eapprove.business.dto.BpmServiceRequestDto;
import vn.fis.spro.common.helper.ResponseHelper;

import java.util.Map;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

/**
 * Author: AnhVTN
 * Date: 15/03/2023
 */
@Slf4j
@RestController("BpmControllerV1")
@RequestMapping(SERVICE_PATH + "/bpm-service")
public class BpmController {

    private final BpmService bpmService;

    public BpmController(BpmService bpmService) {
        this.bpmService = bpmService;
    }

    @PostMapping("/call-service")
    public ResponseEntity<?> call(@RequestBody BpmServiceRequestDto request) {
        try {
            Object response = bpmService.callBpm(request.getUrl(), request.getMethod(), request.getParams(), request.getHeaders(),request.getIsSaveLog());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

//    @PostMapping("/call-service-subsystem")
//    public ResponseEntity<?> callSubSystem(@RequestBody BpmServiceRequestDto request,@RequestHeader MultiValueMap<String, String> headers) {
//        try {
//            if(ValidationUtils.isNullOrEmpty(request.getHeaders().get(HttpHeaders.AUTHORIZATION))) {
//                Base64.Encoder encoder = Base64.getEncoder();
//                Map<String, String[]> authInfoMap = new HashMap<>();
//                authInfoMap.put(callServiceProperties.getAdmin().getUrl(),
//                        new String[]{callServiceProperties.getAdmin().getAccount(),
//                                callServiceProperties.getAdmin().getPassword()});
//                authInfoMap.put(callServiceProperties.getCons().getUrl(),
//                        new String[]{callServiceProperties.getCons().getAccount(),
//                                callServiceProperties.getCons().getPassword()});
//                authInfoMap.put(callServiceProperties.getSap().getUrl(),
//                        new String[]{callServiceProperties.getSap().getAccount(),
//                                callServiceProperties.getSap().getPassword()});
//
//                for (Map.Entry<String, String[]> entry : authInfoMap.entrySet()) {
//                    String urlPattern = entry.getKey();
//                    String[] authInfo = entry.getValue();
//
//                    if (request.getUrl().matches(urlPattern)) {
//                        String basicAuthen = authInfo[0] + ":" + authInfo[1];
//                        String encodedString = encoder.encodeToString(basicAuthen.getBytes());
//                        headers.add(HttpHeaders.AUTHORIZATION,"Basic "+encodedString);
//                        break;
//                    }
//                }
//            }
//            if(ValidationUtils.isNullOrEmpty(request.getHeaders().get("X-Api-key"))){
//                Map<String, String[]> authApiKey = new HashMap<>();
//                authApiKey.put(callServiceProperties.getIhrp().getUrl(),
//                        new String[]{callServiceProperties.getIhrp().getXApiKey(),});
//
//                for (Map.Entry<String, String[]> entry : authApiKey.entrySet()) {
//                    String urlPattern = entry.getKey();
//                    String[] authInfo = entry.getValue();
//
//                    if (request.getUrl().matches(urlPattern)) {
//                        headers.add("X-Api-key", authInfo[0]);
//                        break;
//                    }
//                }
//            }
//            if(ValidationUtils.isNullOrEmpty(request.getHeaders().get(HttpHeaders.COOKIE))){
//                Map<String, String[]> authCookie = new HashMap<>();
//                authCookie.put(callServiceProperties.getCons().getCookie(),
//                        new String[]{callServiceProperties.getCons().getCookie(),});
//
//                for (Map.Entry<String, String[]> entry : authCookie.entrySet()) {
//                    String urlPattern = entry.getKey();
//                    String[] authInfo = entry.getValue();
//
//                    if (request.getUrl().matches(urlPattern)) {
//                        headers.add("cookie", authInfo[0]);
//                        break;
//                    }
//                }
//            }
//
//            Object response = bpmService.callBpm(request.getUrl(), request.getMethod(), request.getParams(), request.getHeaders(),request.getIsSaveLog());
//            return ResponseEntity.ok(response);
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return ResponseHelper.fail();
//        }
//    }

//    @PostMapping("/call-service-admin")
//    public ResponseEntity<?> callAdmin(@RequestBody BpmServiceRequestDto request) {
//        try {
//            Object response = bpmService.callBpmSubSystem(request.getUrl(), request.getMethod(), request.getParams(), request.getHeaders(),"ADMIN",request.getIsSaveLog(),new HashMap<>(),false);
//            return ResponseEntity.ok(response);
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return ResponseHelper.fail();
//        }
//    }

//    @PostMapping("/call-service-ihrp")
//    public ResponseEntity<?> callIhrp(@RequestBody BpmServiceRequestDto request) {
//        try {
//            Object response = bpmService.callBpmSubSystem(request.getUrl(), request.getMethod(), request.getParams(), request.getHeaders(),"IHRP",request.getIsSaveLog(),new HashMap<>(),false);
//            return ResponseEntity.ok(response);
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return ResponseHelper.fail();
//        }
//    }
//    @PostMapping("/call-service-sap")
//    public ResponseEntity<?> callSap(@RequestBody BpmServiceRequestDto request) {
//        try {
//            Object response = bpmService.callBpmSubSystem(request.getUrl(), request.getMethod(), request.getParams(), request.getHeaders(),"SAP",request.getIsSaveLog(),new HashMap<>(),false);
//            return ResponseEntity.ok(response);
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return ResponseHelper.fail();
//        }
//    }
//    @PostMapping("/call-service-cons")
//    public ResponseEntity<?> callCons(@RequestBody BpmServiceRequestDto request) {
//        try {
//            Object response = bpmService.callBpmSubSystem(request.getUrl(), request.getMethod(), request.getParams(), request.getHeaders(),"CONS",request.getIsSaveLog(),new HashMap<>(),false);
//            return ResponseEntity.ok(response);
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return ResponseHelper.fail();
//        }
//    }

    @PostMapping("/call-service-authen")
    public ResponseEntity<?> callServiceAuthen(@RequestParam Long actionApiId) {
        try {
            Map<String, Object> response = bpmService.callBpmWithAuthen(actionApiId);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/findSystemConfigByCodeAndName")
    public ResponseEntity<?> call(@RequestParam(value = "code", required = false) String code, @RequestParam(value = "name", required = false) String name) {
        try {
            Map<String, Object> response = bpmService.findSystemConfigByCodeAndName(code, name);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

}
