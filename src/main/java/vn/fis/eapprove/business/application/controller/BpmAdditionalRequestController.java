package vn.fis.eapprove.business.application.controller;

import vn.fis.eapprove.security.CredentialHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.fis.eapprove.business.domain.bpm.service.BpmAdditionalRequestManager;
import vn.fis.eapprove.business.dto.BpmAdditionalRequestDto;
import vn.fis.spro.common.helper.ResponseHelper;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("BpmAdditionalRequestControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/additional-request")
@Tag(name = "bpm-additional-request-service", description = "Bpm additional request API Services")
public class BpmAdditionalRequestController {

    @Autowired
    private BpmAdditionalRequestManager bpmAdditionalRequestManager;

    @Autowired
    private CredentialHelper credentialHelper;

    @Operation(summary = "Save additional request")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Save additional request Success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Save additional request Fail", content = @Content)})
    @PostMapping("/save")
    public ResponseEntity<?> saveAdditionalRequest(@RequestBody BpmAdditionalRequestDto dto) {
        log.info("Entering saveAdditionalRequest()...");
        try {
            dto.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
            return bpmAdditionalRequestManager.saveBpmAdditionalRequest(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }
}
