package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.fis.eapprove.business.domain.api.service.ApiLogManager;
import vn.fis.eapprove.business.dto.ApiLogDto;
import vn.fis.eapprove.business.dto.BaseSearchDto;
import vn.fis.eapprove.business.dto.PageSearchDto;
import vn.fis.eapprove.business.dto.SearchPrintDto;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.model.ResponseDto;

import java.util.Locale;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("ApiLogControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/apiLog")
public class ApiLogController {

    @Autowired
    ApiLogManager apiLogManager;

    @Autowired
    MessageSource messageSource;

    @Autowired
    private BusinessManager businessManager;

    @Operation(summary = "search Log API in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "apiLog", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/searchApiLog")
    public ResponseEntity<?> searchApiAction(@RequestBody ApiLogDto req) {
        try {
            PageSearchDto page = apiLogManager.searchViewLogApi(req);
            if (!CollectionUtils.isEmpty(page.getContent())) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(page).
                        message(messageSource.getMessage("message.searchApiLog.searchApiAction.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.searchApiLog.searchApiAction.fail", null, Locale.getDefault())).data(page.getContent()).build());
        } catch (Exception e) {
            log.error("Error: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @PostMapping("/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody ApiLogDto req) {
        try {
            PageSearchDto page = apiLogManager.searchViewLogApi(req);
            Object data = businessManager.getFilterData(page.getContent(), null);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(data).message("Thành công").build());
        } catch (Exception e) {
            log.error("Error: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }
}
