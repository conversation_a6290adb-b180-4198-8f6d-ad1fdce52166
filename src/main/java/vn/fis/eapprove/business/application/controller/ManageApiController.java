package vn.fis.eapprove.business.application.controller;


import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.manageApi.entity.ManageAPI;
import vn.fis.eapprove.business.domain.manageApi.service.ManageAPIManager;
import vn.fis.eapprove.business.model.request.BasePageRequest;
import vn.fis.eapprove.business.model.request.ManageApiRequest;
import vn.fis.eapprove.business.model.response.ManageApiResponse;
import vn.fis.eapprove.business.utils.ResponseUtils;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("ManageApiControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/manager-api")
public class ManageApiController {

    @Autowired
    CredentialHelper credentialHelper;

    @Autowired
    ManageAPIManager manageAPIManager;

    @Autowired
    ResponseUtils responseUtils;

    @PostMapping("/create")
    public ResponseEntity<?> createMDT(@RequestBody ManageApiRequest manageApiRequest) {
        try {
            String email = credentialHelper.getJWTPayload().getEmail();
            Boolean result = manageAPIManager.createManageAPI(manageApiRequest, email);
            if (result) {
                return responseUtils.getResponseEntity(result, 1, "success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/delete")
    public ResponseEntity<?> delete(@RequestBody ManageApiRequest manageApiRequest) {
        try {
            Boolean result = manageAPIManager.deleteManageAPI(manageApiRequest);
            if (result) {
                return responseUtils.getResponseEntity(result, 1, "success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/getAll")
    public ResponseEntity<?> getAll(@RequestBody BasePageRequest basePageRequest) {
        try {
            Page<ManageApiResponse> result = manageAPIManager.getAll(basePageRequest);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @PostMapping("/update")
    public ResponseEntity<?> update(@RequestBody ManageApiRequest manageApiRequest) {
        try {
            String email = credentialHelper.getJWTPayload().getEmail();
            ManageAPI result = manageAPIManager.updateManageAPI(manageApiRequest, email);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
