package vn.fis.eapprove.business.application.controller;

import vn.fis.eapprove.security.CredentialHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.data.repository.query.Param;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.service.ServicePackageManager;
import vn.fis.eapprove.business.dto.BudgetServiceDto;
import vn.fis.eapprove.business.dto.ServicePackageDto;
import vn.fis.eapprove.business.exception.BusinessException;
import vn.fis.eapprove.business.exception.rest.response.BaseResponse;
import vn.fis.eapprove.business.model.AccountModel;
import vn.fis.eapprove.business.model.request.BasePageRequest;
import vn.fis.eapprove.business.model.request.SearchServicePackRequest;
import vn.fis.eapprove.business.model.request.ServicePackageFilter;
import vn.fis.eapprove.business.model.request.ServicePackageRequest;
import vn.fis.eapprove.business.model.response.ServicePackageResponse;
import vn.fis.eapprove.business.model.response.UserInfoByUsername;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.utils.CastUtils;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static vn.fis.eapprove.business.constant.Constant.MESSAGE_500;
import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;
import static vn.fis.eapprove.business.exception.DefaultErrorCode.DEFAULT_BAD_REQUEST;
import static vn.fis.eapprove.business.exception.DefaultErrorCode.DEFAULT_INTERNAL_SERVER_ERROR;
import static vn.fis.spro.common.constants.ResponseCodeEnum.INTERNAL_SERVER_ERROR;

@RestController("ServicePackageControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/service-pack")
public class ServicePackageController {

    @Autowired
    ServicePackageManager servicePackageService;

    @Autowired
    ResponseUtils responseUtils;
    @Autowired
    CredentialHelper credentialHelper;

    @Autowired
    MessageSource messageSource;

    @Autowired
    BusinessManager businessManager;

    @Value("${app.sso.realm}")
    String realm;

    @Value("${app.s3.bucket}")
    private String bucket;

    @Autowired
    CustomerService customerService;

    @PostMapping("/createServicePackage")
    public ResponseEntity<?> createServicePackage(@RequestBody ServicePackageRequest servicePackageRequest) {
        try {
            Boolean check = servicePackageService.checkExistName(servicePackageRequest.getServiceName(),
                    servicePackageRequest.getId(), servicePackageRequest.getIdOrgChart(),
                    servicePackageRequest.getDeleted(), servicePackageRequest.getParentId(), servicePackageRequest.getVisibleChart(), servicePackageRequest.getServiceType());

            if (check) {
                if (servicePackageRequest.getParentId() == null) {
                    return responseUtils.getResponseEntity(true, 1, "Gói dịch vụ đã tồn tại trong hệ thống!", HttpStatus.OK);
                }
                return responseUtils.getResponseEntity(true, 1, "Dịch vụ đã tồn tại trong hệ Thống!", HttpStatus.OK);
            }
            ServicePackageResponse result = servicePackageService.createServicePackage(servicePackageRequest);
            if (result == null) {
                return responseUtils.getResponseEntity(null, -1, "Thất bại", HttpStatus.BAD_REQUEST);
            }
            return responseUtils.getResponseEntity(result, 1, "Thêm mới dịch vụ thành công", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/deleteServicePackage")
    public ResponseEntity<?> deleteServicePackage(@RequestBody ServicePackageRequest servicePackageRequest) {
        try {
            List<Long> listServiceDelete = servicePackageRequest.getListIds();
            Map<String, Object> response = servicePackageService.deleteServicePackageByListId(listServiceDelete);
            if (!ValidationUtils.isNullOrEmpty(response)) {
                if (response.get("status").equals(0)) {
                    return responseUtils.getResponseEntity(false, -2, response.get("message").toString(), HttpStatus.OK);
                } else if (response.get("status").equals(1)) {
                    return responseUtils.getResponseEntity(true, 1, response.get("message").toString(), HttpStatus.OK);
                }
            }

            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
//            List<Long> checkPackage = new ArrayList<>();
//            for (Long id : listServiceDelete) {
//                List<Long> ids = servicePackageService.getListIdByParentId(id);
//                checkPackage.addAll(ids);
//                Boolean check = servicePackageService.CheckServiceTicket(ids.isEmpty() ? Collections.singletonList(id) : ids);
//                if (check) {
//                    return responseUtils.getResponseEntity(false, -2, "Dịch vụ đang tồn tại trong Quản lý phiếu yêu cầu. Không được xóa!", HttpStatus.OK);
//                }
//
//                Boolean result = servicePackageService.deleteServicePackage(id);
//                if (!result) {
//                    return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
//                }
//            }
//            if (!checkPackage.isEmpty()) {
//                return responseUtils.getResponseEntity(true, 1, "Xóa gói vụ thành công", HttpStatus.OK);
//            } else {
//                return responseUtils.getResponseEntity(true, 1, "Xóa dịch vụ thành công", HttpStatus.OK);
//            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/getAll")
    public ResponseEntity<?> getAll(@RequestBody SearchServicePackRequest searchService) {
        try {
            //String realm = credentialHelper.getRealm();

            boolean requestTypeAdmin = CastUtils.toBooleanDefaultIfNull(searchService.getAdmin(), false);
            boolean isMaster = "Master".equals(realm) && searchService.getChartId().equals(0L);
            boolean isAdmin = isMaster || requestTypeAdmin;

            List<ServicePackageResponse> listServicePackageResponses;
            listServicePackageResponses = servicePackageService.getAll(searchService, bucket, isAdmin);
            return responseUtils.getResponseEntity(listServicePackageResponses, 1, "Thành công!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Thất bại", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/getServiceById")
    public ResponseEntity<?> getAllByIdAndProcessId(@RequestParam Long serviceId) {
        try {
//            //String realm = credentialHelper.getRealm();
//            String token = credentialHelper.getJWTToken();

            List<ServicePackageResponse> listServicePackageResponses;
            listServicePackageResponses = servicePackageService.getAllByIdAndProcessId(serviceId, bucket);
            return responseUtils.getResponseEntity(listServicePackageResponses, 1, "Thành công!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Thất bại", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/getServiceByIdBasicAuth")
    public ResponseEntity<?> getAllByIdAndProcessIdBasicAuth(@RequestParam Long serviceId, @RequestBody(required = false) AccountModel accountModel) {
        try {

            List<ServicePackageResponse> listServicePackageResponses;
            listServicePackageResponses = servicePackageService.getAllByIdAndProcessIdBasicAuth(serviceId, bucket);
            return responseUtils.getResponseEntity(listServicePackageResponses, 1, "Thành công!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Thất bại", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/getAllService")
    public ResponseEntity<?> getAllService(@RequestBody BasePageRequest searchService) {
        try {
            List<ServicePackageResponse> listServicePackageResponses = servicePackageService.getAllService(searchService);
            return responseUtils.getResponseEntity(listServicePackageResponses, 1, "Thành công!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Thất bại", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/getByParent")
    public ResponseEntity<?> getAll(@RequestParam("parentId") Long parentId, @RequestParam("chartId") Long chartId,
                                    @RequestParam(value = "isWeb", defaultValue = "true") Boolean check,
                                    @RequestParam(required = false) Boolean admin) {
        try {
            //String realm = credentialHelper.getRealm();
            String token = credentialHelper.getJWTToken();

            boolean requestTypeAdmin = CastUtils.toBooleanDefaultIfNull(admin, false);
            boolean isMaster = "Master".equals(realm) && chartId.equals(0L);
            boolean isAdmin = isMaster || requestTypeAdmin;

            List<ServicePackageResponse> result = servicePackageService.findByParent(parentId, bucket, token, check, isAdmin);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/getParentService")
    public ResponseEntity<?> getParentService(@RequestParam("id") Long id) {
        try {
            List<ServicePackageResponse> result = servicePackageService.getParentService(id);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/checkExistName")
    public ResponseEntity<?> checkExistName(@RequestParam("serviceName") String serviceName, @Param("id") Long
            id, @Param("idOrgChart") Long idOrgChart, @Param("isParent") boolean isParent, @Param("deleted") boolean deleted,
                                            @Param("parentId") Long parentId,
                                            @RequestParam("visibleChart") List<String> visibleChart,
                                            @RequestParam("serviceType") Integer serviceType) {
        try {
            boolean check = servicePackageService.checkExistName(serviceName, id, idOrgChart, deleted, parentId, visibleChart, serviceType);
            if (check) {
                if (isParent) {
                    return responseUtils.getResponseEntity(true, 1, "Gói dịch vụ đã tồn tại trong hệ thống!", HttpStatus.OK);
                }
                return responseUtils.getResponseEntity(true, 1, "Dịch vụ đã tồn tại trong hệ thống!", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(false, 1, "Success!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

//    @GetMapping("/checkServiceTicket")
//    public ResponseEntity<?> checkExistName(@RequestParam("id") Long id) {
//        try {
//            Boolean check = servicePackageService.CheckServiceTicket(id);
//            if (check) {
//                return responseUtils.getResponseEntity(false, -1, "Dịch vụ đã gắn vào ticket", HttpStatus.OK);
//            }
//            return responseUtils.getResponseEntity(false, 1, "Success!", HttpStatus.OK);
//        } catch (Exception e) {
//            e.printStackTrace();
//            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }

    @PostMapping("/updateServicePackage")
    public ResponseEntity<?> updateServicePackage(@RequestBody ServicePackageRequest servicePackageRequest) {
        try {
            ServicePackage result = servicePackageService.updateServicePackage(servicePackageRequest);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Sửa thành công", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Thất bại", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Thất bại", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/updatePosition")
    public ResponseEntity<?> update(@RequestBody ServicePackageRequest servicePackageRequest) {
        try {

            ServicePackage result = servicePackageService.updatePosition(servicePackageRequest);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Sửa thành công", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Thất bại", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Thất bại", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping(value = "/uploadIcon")
    public ResponseEntity<?> uploadIcon(@RequestParam(value = "icon", required = false) MultipartFile
                                                icon, @RequestParam("id") Long id)  {
        try {
            // type: contract - appendix
            // typeFile: contract - payment
            boolean saveFile = servicePackageService.saveIcon(bucket, icon, id);
            if (saveFile) {
                return responseUtils.getResponseEntity(true, 1, "Success!", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(false, -1, "Lưu file thất bại", HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping(value = "/uploadFile")
    public ResponseEntity<?> uploadFile(@RequestParam(value = "file", required = false) MultipartFile
                                                file, @RequestParam("id") Long id)  {
        try {
            // type: contract - appendix
            // typeFile: contract - payment
            boolean saveFile = servicePackageService.saveFile(bucket, file, id);
            if (saveFile) {
                return responseUtils.getResponseEntity(true, 1, "Success!", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(false, -1, "Lưu file thất bại", HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/getByChartAndNode")
    public ResponseEntity<?> getByChartAndNode(@RequestParam("id") Long id, @RequestParam("chartId") Long chartId) {
        try {
            List<ServicePackageDto> res = servicePackageService.getByChartAndNode(id, chartId);
            if (res == null) {
                return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
            }
            return responseUtils.getResponseEntity(res, 1, "Success!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/searchServiceMyReport")
    public ResponseEntity<?> searchServiceMyReport(@RequestParam("search") String
                                                           search, @RequestHeader("chart") Long chartId) {
        try {
            List<ServicePackageDto> res = servicePackageService.searchServiceMyReport(search, chartId);
            if (res == null) {
                return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
            }
            return responseUtils.getResponseEntity(res, 1, "Success!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, MESSAGE_500, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/getListIdsByName")
    public ResponseEntity<?> getListIdsByName(@RequestParam("search") String search) {
        try {
            List<String> res = servicePackageService.getListIdsByName(search);
            if (res == null) {
                return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
            }
            return responseUtils.getResponseEntity(res, 1, "Success!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, MESSAGE_500, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

//    @GetMapping("/getByChartAndNode")
//    public ResponseEntity<?> getByChartAndNode(@RequestParam("nodeId") Long nodeId,@RequestParam("chartId") Long chartId){
//        try{
//            List<ServicePackageDto> res = servicePackageService.getByChartAndNode(nodeId, chartId);
//            if(res == null){
//                return responseUtils.getResponseEntity(null,-1,"fail",HttpStatus.INTERNAL_SERVER_ERROR);
//            }
//            return responseUtils.getResponseEntity(res,1,"Success!", HttpStatus.OK);
//        }catch (Exception e){
//            e.printStackTrace();
//            return responseUtils.getResponseEntity(null,-1,"fail",HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }

    @GetMapping("/cloneService")
    public ResponseEntity<?> cloneService(@RequestParam("Id") Long Id, @RequestParam("chartId") Long chartId) {
        try {

            servicePackageService.clone(Id, chartId);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(true).
                    message(messageSource.getMessage("message.servicePackage.clone.success", null, Locale.getDefault())).build());
        } catch (BusinessException be) {
            return ResponseEntity.badRequest().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(be.getMessage()).build());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage("internal_server_error", null, Locale.getDefault())).build());
        }
    }

    @GetMapping("/getIdService")
    public ResponseEntity<?> getIdService() {
        try {
            List<BudgetServiceDto> result = servicePackageService.getListIdsAndNameById();
            if (result != null) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                        message(messageSource.getMessage("message.servicePackage.clone.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.servicePackage.clone.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage("internal_server_error", null, Locale.getDefault())).build());
        }
    }

    @PostMapping("/upload-file")
    public BaseResponse<?> uploadFile(@RequestParam(value = "file", required = false) MultipartFile file)  {
        if (!file.isEmpty()) {
            try {
                String url = servicePackageService.uploadFile(bucket, file);
                if (url == null) {
                    return BaseResponse.ofFailed(DEFAULT_BAD_REQUEST);
                }
                return BaseResponse.ofSucceeded(url);
            } catch (
                    Exception e) {
                e.printStackTrace();
                return BaseResponse.ofFailed(DEFAULT_INTERNAL_SERVER_ERROR);
            }
        }
        return BaseResponse.ofFailed(DEFAULT_BAD_REQUEST);
    }

    //        @PostMapping("/get-all")
//        public BaseResponse<List<ServicePackage>> getAllByFilter (@RequestBody ServicePackageFilter filter){
//            return BaseResponse.ofSucceeded(servicePackageService.getServicePackageByFilter(filter));
//        }
    @PostMapping("/get-all")
    public ResponseEntity<?> getAllByFilter(@Valid @RequestBody ServicePackageFilter searchService) {
        try {
            //String realm = credentialHelper.getRealm();
            String token = credentialHelper.getJWTToken();

            boolean requestTypeAdmin = false;
            boolean isMaster = "Master".equals(realm) && searchService.getChartId().equals(0L);
            boolean isAdmin = isMaster || requestTypeAdmin;

            List<ServicePackageResponse> listServicePackageResponses;
            listServicePackageResponses = servicePackageService.getAllTest(searchService, bucket, token, isAdmin);
            return responseUtils.getResponseEntity(listServicePackageResponses, 1, "Thành công!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Thất bại", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "search service package by id with permission")
    @GetMapping("/getServiceByIdWithPermission")
    public ResponseEntity<?> getServiceByIdWithPermission(@RequestParam Long id) {
        log.info("Entering getServiceByIdWithPermission()...");
        try {
            ServicePackageResponse result = servicePackageService.getServiceByIdWithPermission(id);
            if (result != null) {
                return ResponseHelper.ok(result, "success");
            } else {
                return ResponseHelper.invalid(messageSource.getMessage("ticket.no-permission-user", null, Locale.getDefault()));
            }
        } catch (Exception e) {
            log.error("Error getServiceByIdWithPermission: {}", e.getMessage());
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/switchPositionPack")
    public ResponseEntity<?> switchPositionPack(@RequestBody List<ServicePackageRequest> filter) {
        try {
            if (servicePackageService.updateListPosition(filter)) {
                return ResponseHelper.ok("success");
            }
            return ResponseHelper.invalid("fail");
        } catch (Exception e) {
            log.error("Error switchPositionPack: {}", e.getMessage());
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "search special service package by parent id")
    @GetMapping("/getServiceSpecialByParentId")
    public ResponseEntity<?> getServiceSpecialByParentId(@RequestParam Long id) {
        log.info("Entering getServiceSpecialByParentId()...");
        try {
            ServicePackageResponse result = servicePackageService.getServiceSpecialByParentId(id);
            if (result != null) {
                return ResponseHelper.ok(result, "success");
            } else {
                return ResponseHelper.invalid(messageSource.getMessage("ticket.no-permission-user", null, Locale.getDefault()));
            }
        } catch (Exception e) {
            log.error("Error getServiceSpecialByParentId: {}", e.getMessage());
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Get All Filter Data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get all filterData is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get all filterData is fail", content = @Content)})
    @Tag(name = "servicePackage", description = "Get all filterData")
    @PostMapping("/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody SearchServicePackRequest searchService) {
        try {
            //String realm = credentialHelper.getRealm();

            boolean requestTypeAdmin = CastUtils.toBooleanDefaultIfNull(searchService.getAdmin(), false);
            boolean isMaster = "Master".equals(realm) && searchService.getChartId().equals(0L);
            boolean isAdmin = isMaster || requestTypeAdmin;
            Object result = businessManager.getFilterData(servicePackageService.getAllFilter(searchService, isAdmin, false), FilterDataEnum.SERVICE_PACKAGE);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                    message(messageSource.getMessage("message.searchApiLog.searchApiAction.success", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @GetMapping("/test")
    public UserInfoByUsername test(@RequestParam String username) {
        try {
            return customerService.getUserInfoByUsername(username);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Operation(summary = "get list share service with permission")
    @GetMapping("/getListShareService")
    public ResponseEntity<?> getListShareService() {
        log.info("Entering getListShareService()...");
        try {
            List<Map<String, Object>> result = servicePackageService.getListShareService();
            return ResponseHelper.ok(result);
        } catch (Exception e) {
            log.error("Error getListShareService: {}", e.getMessage());
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/getFilterServiceOption")
    public ResponseEntity<?> getFilterServiceOption(@RequestBody SearchServicePackRequest searchService) {
        try {
            //String realm = credentialHelper.getRealm();

            boolean requestTypeAdmin = CastUtils.toBooleanDefaultIfNull(searchService.getAdmin(), false);
            boolean isMaster = "Master".equals(realm) && searchService.getChartId().equals(0L);
            boolean isAdmin = isMaster || requestTypeAdmin;
            List<ServicePackageResponse> responses = servicePackageService.getAllFilter(searchService, isAdmin, true);
            return ResponseHelper.ok(responses);
        } catch (Exception e) {
            log.error("Error getFilterServiceOption: {}", e.getMessage());
            return ResponseHelper.fail();
        }
    }
}