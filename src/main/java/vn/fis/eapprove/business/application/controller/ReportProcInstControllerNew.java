package vn.fis.eapprove.business.application.controller;


import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.report.service.ReportProcInstServiceNew;
import vn.fis.eapprove.business.dto.filter.ReportProcInstFilter;
import vn.fis.eapprove.business.dto.filter.RequestDetailTaskReturned;
import vn.fis.eapprove.business.dto.report.DetailTicketCancelResponse;
import vn.fis.eapprove.business.model.response.BaseResponse;
import vn.fis.eapprove.business.model.response.ReportProcInstByChartNodeResponse;
import vn.fis.eapprove.business.model.response.ReportProcInstByGroupResponse;
import vn.fis.eapprove.business.tenant.DetailReportService;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@AllArgsConstructor
@RestController("ReportProcInstControllerNewV1")
@RequestMapping(SERVICE_PATH + "/report-procInst")
public class ReportProcInstControllerNew extends BaseController {

    private final ReportProcInstServiceNew reportProcInstServiceNew;

    private final DetailReportService detailReportService;


    @PostMapping("/by-group")
    public ResponseEntity<BaseResponse<ReportProcInstByGroupResponse>> getReportProcInstByGroup(@RequestBody ReportProcInstFilter filter) {

        ReportProcInstByGroupResponse response = reportProcInstServiceNew.getReportProcInstByGroup(filter);

        return success(response);
    }

    @PostMapping("/by-user")
    public ResponseEntity<BaseResponse<ReportProcInstByGroupResponse>> getReportTaskByUser(@RequestBody ReportProcInstFilter filter) {

        ReportProcInstByGroupResponse response = reportProcInstServiceNew.getReportTaskByUser(filter);

        return success(response);
    }


    @PostMapping("/by-chart-node")
    public ResponseEntity<BaseResponse<ReportProcInstByChartNodeResponse>> getReportProcInstByChartNode(@RequestBody ReportProcInstFilter filter, @RequestHeader("Authorization") String authorizationHeader) {

        ReportProcInstByChartNodeResponse response = reportProcInstServiceNew.getReportProcInstByChartNode(filter);

        return success(response);
    }

    @PostMapping("/detail-procInst-cancel")
    public ResponseEntity<BaseResponse<DetailTicketCancelResponse>> getDetailTicketCancel(@RequestBody RequestDetailTaskReturned request) {

        DetailTicketCancelResponse response = detailReportService.getDetailTicketCancel(request.getProcInstId());

        return success(response);

    }
}
