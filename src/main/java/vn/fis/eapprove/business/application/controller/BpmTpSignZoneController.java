package vn.fis.eapprove.business.application.controller;

import vn.fis.eapprove.security.CredentialHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTempTicket;
import vn.fis.eapprove.business.domain.bpm.service.BpmTpSignZoneManager;
import vn.fis.eapprove.business.model.request.BpmCreateSignRequest;
import vn.fis.eapprove.business.model.request.BpmPrintSignZoneRequest;
import vn.fis.eapprove.business.model.request.GetTpSignZoneRequest;

import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.util.LogUtils;

import static vn.fis.eapprove.business.constant.Constant.*;

@Slf4j
@RestController("BpmTpSignZoneControllerV1")
@CrossOrigin("*")
@RequestMapping(SERVICE_PATH + "/print-sign-zone")
@Tag(name = "bpm-tp-sign-zone-service", description = "The BPM TP sign zone services")
public class BpmTpSignZoneController {

    @Autowired
    private BpmTpSignZoneManager bpmTpSignZoneManager;
    @Autowired
    private ResponseUtils responseUtils;
    @Autowired
    private CredentialHelper credentialHelper;

    @PostMapping
    public ResponseEntity<?> createPrintSignZone(@RequestBody BpmPrintSignZoneRequest req) {
        try {
            int status = bpmTpSignZoneManager.save(req);
            switch (status) {
                case SUCCESS:
                    return responseUtils.getResponseEntity(true, 1, "Success", HttpStatus.OK);
                case FAILED:
                    return responseUtils.getResponseEntity(false, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
                case NAME_EXISTED:
                    return responseUtils.getResponseEntity(false, -1, "Tên đã tồn tại", HttpStatus.OK);
                default:
                    return responseUtils.getResponseEntity(false, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/check-order")
    public ResponseEntity<?> checkOrderSign(@RequestParam("bpmPrintPhaseId") Long bpmPrintPhaseId, @RequestParam("orderSign") Long orderSign) {
        try {
            boolean status = bpmTpSignZoneManager.checkOrder(bpmPrintPhaseId, orderSign);
            if (status == true) {
                return responseUtils.getResponseEntity(true, 1, "Thứ tự đã tồn tại", HttpStatus.OK);
            } else {
                return responseUtils.getResponseEntity(false, 1, "Success", HttpStatus.OK);
            }
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/sign")
    public ResponseEntity<?> assignSignToSignZone(@RequestBody BpmCreateSignRequest req) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("Starting sign ==> taskId={}, ticketId={}", req.getTaskId(), req.getTicketId());
            String userName = credentialHelper.getJWTPayload().getUsername();
            int status = bpmTpSignZoneManager.assignSign(req, userName);
            switch (status) {
                case SUCCESS:
                    return responseUtils.getResponseEntity(true, 1, "Success", HttpStatus.OK);
                case FAILED:
                    return responseUtils.getResponseEntity(false, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
                case NAME_EXISTED:
                    return responseUtils.getResponseEntity(false, -1, "Tên đã tồn tại", HttpStatus.OK);
                default:
                    return responseUtils.getResponseEntity(false, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        } finally {
            log.info("Finished sign ==> taskId={}, ticketId={}, in {} ms", req.getTaskId(), req.getTicketId(), System.currentTimeMillis() - startTime);
        }
    }

    @PostMapping("/type-template")
    public ResponseEntity<?> createTypeTemplatePrintSignZone(@RequestBody BpmPrintSignZoneRequest req) {
        try {
            int status = bpmTpSignZoneManager.saveTypeTemplatePrint(req);
            if (status == SUCCESS) {
                return responseUtils.getResponseEntity(true, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(false, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping("/update-print-sign-zone")
    public ResponseEntity<?> updatePrintSignZone(@RequestBody BpmPrintSignZoneRequest req) {
        try {
            int status = bpmTpSignZoneManager.update(req);
            switch (status) {
                case SUCCESS:
                    return responseUtils.getResponseEntity(true, 1, "Success", HttpStatus.OK);
                case FAILED:
                    return responseUtils.getResponseEntity(false, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
                case NAME_EXISTED:
                    return responseUtils.getResponseEntity(false, -1, "Tên đã tồn tại", HttpStatus.OK);
                default:
                    return responseUtils.getResponseEntity(false, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/temp-ticket")
    public ResponseEntity<?> createTempTicket(@RequestBody BpmTempTicket req) {
        try {
            BpmTempTicket status = bpmTpSignZoneManager.saveTempTicket(req);
            if (status == null) {
                return responseUtils.getResponseEntity(status, -1, "Failed", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(status, 1, "Success", HttpStatus.OK);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/temp-ticket/{id}")
    public ResponseEntity<?> getTempTicketById(@PathVariable Long id) {
        try {
            BpmTempTicket status = bpmTpSignZoneManager.getTempTicketById(id);
            if (status == null) {
                return responseUtils.getResponseEntity(status, -1, "Failed", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(status, 1, "Success", HttpStatus.OK);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/find-all/{procInstId}")
    @Operation(summary = "Find all sign zones")
    public ResponseEntity<?> findAll(@PathVariable("procInstId") String procInstId,
                                     @RequestParam(value = "taskDefKey", required = false) String taskDefKey) {
        long startTime = LogUtils.logBegin(log);
        try {
            return ResponseHelper.ok(bpmTpSignZoneManager.findAll(procInstId, taskDefKey));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @GetMapping("/find-one/{procInstId}")
    @Operation(summary = "Find all sign zones")
    public ResponseEntity<?> findOne(@PathVariable("procInstId") String procInstId,
                                     @RequestParam(value = "taskDefKey", required = false) String taskDefKey) {
        long startTime = LogUtils.logBegin(log);
        try {
            return ResponseHelper.ok(bpmTpSignZoneManager.findOne(procInstId, taskDefKey));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @PostMapping("/findAllByTicketId")
    @Operation(summary = "Find all sign zones")
    public ResponseEntity<?> findAllByTicketId(@RequestBody GetTpSignZoneRequest request) {
        long startTime = LogUtils.logBegin(log);
        try {
            return ResponseHelper.ok(bpmTpSignZoneManager.findAllByTicketIdOrRequestCode(request.getTicketId(),request.getRequestCode(), request.getTaskDefKey()));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }


}
