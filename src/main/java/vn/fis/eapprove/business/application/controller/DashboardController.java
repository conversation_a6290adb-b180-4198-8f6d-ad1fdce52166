package vn.fis.eapprove.business.application.controller;

import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.dashboard.service.DashboardService;
import vn.fis.eapprove.business.model.request.DashboardConfigRequest;
import vn.fis.eapprove.business.model.request.DashboardRequest;
import vn.fis.eapprove.business.model.response.DashboardConfigResponse;
import vn.fis.eapprove.business.model.response.DashboardResponse;
import vn.fis.eapprove.business.model.response.DashboardTotalResponse;
import vn.fis.spro.common.helper.ResponseHelper;

import java.util.List;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@AllArgsConstructor
@RestController("DashboardController")
@RequestMapping(SERVICE_PATH + "/dashboard")
public class DashboardController  {

    private final DashboardService dashboardService;

    @PostMapping("/get-chart-bar-employee")
    public ResponseEntity<?> getChartBarEmployee(@RequestBody DashboardRequest request) {
        List<DashboardResponse> response = dashboardService.getChartBarEmployee(request);
        return ResponseHelper.ok(response);
    }

    @PostMapping("/get-chart-pie-status")
    public ResponseEntity<?> getChartPieStatus(@RequestBody DashboardRequest request) {
        List<DashboardResponse> response = dashboardService.getChartPieStatus(request);
        return ResponseHelper.ok(response);
    }

    @PostMapping("/get-chart-pie-priority")
    public ResponseEntity<?> getChartPiePriority(@RequestBody DashboardRequest request) {
        List<DashboardResponse> response = dashboardService.getChartPiePriority(request);
        return ResponseHelper.ok(response);
    }

    @PostMapping("/get-table-employee-workload")
    public ResponseEntity<?> getTableEmployeeWorkload(@RequestBody DashboardRequest request) {
        DashboardTotalResponse response = dashboardService.getTableEmployeeWorkload(request);
        return ResponseHelper.ok(response);
    }

    @PostMapping("/get-table-proposal-status")
    public ResponseEntity<?> getTableProposalStatus(@RequestBody DashboardRequest request) {
        DashboardTotalResponse response = dashboardService.getTableProposalStatus(request);
        return ResponseHelper.ok(response);
    }

    @GetMapping("/get-dashboard-config")
    public ResponseEntity<?> getDashboardConfig(@RequestParam("username") String username) {
        List<DashboardConfigResponse> response = dashboardService.getDashboardConfig(username);
        return ResponseHelper.ok(response);
    }

    @PostMapping("/create-dashboard-config")
    public ResponseEntity<?> createDashboardConfig(@RequestBody List<DashboardConfigRequest> lstRequest) {
        dashboardService.createDashboardConfig(lstRequest);
        return ResponseHelper.ok();
    }
}
