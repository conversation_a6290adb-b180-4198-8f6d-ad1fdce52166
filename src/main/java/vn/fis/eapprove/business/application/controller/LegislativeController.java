package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.constant.LegislativeEnum;
import vn.fis.eapprove.business.domain.legislative.model.request.LegislativeDashboardRequest;
import vn.fis.eapprove.business.domain.legislative.model.request.ReportRequest;
import vn.fis.eapprove.business.domain.legislative.service.LegislativeService;
import vn.fis.eapprove.business.domain.legislative.model.mapper.LegislativeCreateModel;
import vn.fis.eapprove.business.domain.legislative.model.request.LegislativeSearchRequest;
import vn.fis.eapprove.business.domain.legislative.model.request.LegislativeTicketRequest;
import vn.fis.eapprove.business.model.response.InitialLegislativeResponse;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.model.ResponseDto;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("LegislativeController")
@Slf4j
@RequestMapping(SERVICE_PATH + "/legislative")
public class LegislativeController {

    private final BusinessManager businessManager;
    private final LegislativeService legislativeService;

    public LegislativeController( LegislativeService legislativeService, BusinessManager businessManager) {
        this.legislativeService = legislativeService;
        this.businessManager = businessManager;
    }

    @PostMapping("/create-update")
    public ResponseEntity<?> createUpdateLegislative(@RequestBody LegislativeCreateModel request) {
        try {
            return ResponseHelper.ok(legislativeService.createUpdate(request));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/update-status")
    public ResponseEntity<?> updateStatus(@RequestBody LegislativeCreateModel request)  {
        try {
            String code = legislativeService.updateStatus(request.getId(), request.getStatus());
            if (code.equals(LegislativeEnum.LegislativeMessage.SUCCESS.code)) {
                return ResponseHelper.okWithMessage(LegislativeEnum.LegislativeMessage.SUCCESS.message);
            } else {
                return ResponseHelper.fail(null, LegislativeEnum.LegislativeMessage.getMessageByCode(code));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/cancel")
    public ResponseEntity<?> cancel(@RequestBody LegislativeCreateModel request)  {
        try {
            legislativeService.cancel(request.getId());
            return ResponseHelper.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @GetMapping("/get-detail")
    public ResponseEntity<?> getDetailById(@RequestParam Long id) {
        return ResponseHelper.ok(legislativeService.getById(id));
    }

    @PostMapping("/search")
    public ResponseEntity<?> search(@RequestBody LegislativeSearchRequest request) {
        try {
            return ResponseHelper.ok(legislativeService.search(request));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody LegislativeSearchRequest request) {
        try {
            Object result = businessManager.getFilterData(legislativeService.searchFilter(request), FilterDataEnum.MANAGE_SHARE_TICKET);
            return ResponseHelper.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @GetMapping("/get-history")
    public ResponseEntity<?> getHistory(@RequestParam Long id) {
        try {
            return ResponseHelper.ok(legislativeService.getHistory(id));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @GetMapping("/get-legislative-active")
    public ResponseEntity<?> getLegislativeActive(@RequestParam(required = false, defaultValue = "false") Boolean isLoadAll) {
        try {
            return ResponseHelper.ok(legislativeService.getLegislativeActive(isLoadAll));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Load init param")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Load init success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Load init fail", content = @Content)})
    @GetMapping("/loadInit")
    public ResponseEntity<?> loadInit() {
        try {
            InitialLegislativeResponse response = legislativeService.loadInit();
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(response).build());

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @PostMapping("/update-action-api")
    public ResponseEntity<?> updateActionApi(@RequestBody LegislativeTicketRequest request) {
        try {
            legislativeService.updateActionApi(request);
            return ResponseHelper.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/dashboard/get-percent-chart")
    public ResponseEntity<?> getLegislativePercentChart(@RequestBody LegislativeDashboardRequest request) {
        return ResponseEntity.ok(legislativeService.getPercentChart(request));
    }

    @PostMapping("/dashboard/get-rank-ticket")
    public ResponseEntity<?> getRankTickets(@RequestBody LegislativeDashboardRequest request) {
        return ResponseEntity.ok(legislativeService.getRankTickets(request));
    }

    @PostMapping("/dashboard/get-report")
    public ResponseEntity<?> getReport(@RequestBody ReportRequest request) {
        try {
            return ResponseHelper.ok(legislativeService.getReport(request));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/dashboard/getFilterData")
    public ResponseEntity<?> getFilterDataReport(@RequestBody ReportRequest request) {
        try {
            Object result = businessManager.getFilterData(legislativeService.getFilterReport(request), FilterDataEnum.MANAGE_SHARE_TICKET);
            return ResponseHelper.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }
}
