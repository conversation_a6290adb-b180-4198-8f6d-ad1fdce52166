package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.system.service.SystemGroupHistoryService;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("SystemGroupHistoryControllerV1")
@CrossOrigin("*")
@RequestMapping(SERVICE_PATH + "/system-group-history")
public class SystemGroupHistoryController {

    private static final Logger log = LoggerFactory.getLogger(SystemGroupHistoryController.class);
    private final SystemGroupHistoryService systemGroupHistoryService;

    public SystemGroupHistoryController(SystemGroupHistoryService systemGroupHistoryService) {
        this.systemGroupHistoryService = systemGroupHistoryService;
    }

    @Operation(summary = "Get list data accept in group")
    @PostMapping(value = "/findDataByTableName")
    public ResponseEntity<?> findDataByTableNameAndId(@RequestParam String tableName,@RequestParam Long id) {
        try{
            return systemGroupHistoryService.findDataByTableNameAndId(tableName,id);
        }catch(Exception ex){
            return ResponseEntity.badRequest().body(null);
        }
    }

}