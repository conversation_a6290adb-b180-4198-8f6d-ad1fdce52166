package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.fis.eapprove.business.domain.payment.service.PaymentScheduleManager;
import vn.fis.eapprove.business.dto.BaseSearchDto;
import vn.fis.eapprove.business.dto.PageSearchDto;
import vn.fis.eapprove.business.model.request.PaymentScheduleRequest;
import vn.fis.eapprove.business.model.request.SyncPaymentScheduleRequest;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.model.ResponseDto;

import java.util.Locale;

import static vn.fis.eapprove.business.constant.Constant.MESSAGE_SUCCESS;
import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("PaymentScheduleControllerV1")
@Slf4j
@Transactional
@RequestMapping(SERVICE_PATH + "/PaymentSchedule")
public class PaymentScheduleController {
    @Autowired
    PaymentScheduleManager paymentScheduleManager;

    @Autowired
    MessageSource messageSource;


    @Operation(summary = "Create payment schedule")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Create payment schedule  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Create payment schedule is fail", content = @Content)})
    @PostMapping("/createPaymentSchedule")
    public ResponseEntity<?> createPaymentSchedule(@RequestBody PaymentScheduleRequest paymentScheduleRequest) {
        try {
            String status = paymentScheduleManager.createPaymentSchedule(paymentScheduleRequest);
            if (status == MESSAGE_SUCCESS) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).
                        message(messageSource.getMessage("message.PaymentSchedule.createPaymentSchedule.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.PaymentSchedule.createPaymentSchedule.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @PostMapping("/syncPaymentSchedule")
    public ResponseEntity<?> syncPaymentSchedule(@RequestBody SyncPaymentScheduleRequest syncPaymentScheduleRequest) {
        try {

            String status = paymentScheduleManager.syncPaymentSchedule(syncPaymentScheduleRequest);

            if (status == MESSAGE_SUCCESS) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).
                        message(messageSource.getMessage("message.PaymentSchedule.createPaymentSchedule.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.PaymentSchedule.createPaymentSchedule.fail", null, Locale.getDefault())).build());

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Search payment schedule")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Search payment schedule  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Search payment schedule is fail", content = @Content)})
    @PostMapping("/searchPaymentSchedule")
    public ResponseEntity<?> searchPaymentSchedule(@RequestBody BaseSearchDto req) {
        try {
            PageSearchDto page = paymentScheduleManager.search(req);
            if (!CollectionUtils.isEmpty(page.getContent())) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(page).
                        message(messageSource.getMessage("message.priority-management.searchTemplate.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code)
                    .message(messageSource.getMessage("message.priority-management.searchTemplate.fail", null, Locale.getDefault())).data(page.getContent()).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

}
