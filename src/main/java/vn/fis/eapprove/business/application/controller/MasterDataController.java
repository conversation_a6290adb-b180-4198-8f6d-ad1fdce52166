package vn.fis.eapprove.business.application.controller;

import vn.fis.eapprove.security.CredentialHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.template.service.TemplateManager;
import vn.fis.eapprove.business.model.request.MasterDataRequest;
import vn.fis.eapprove.business.model.response.MasterDataPermitResponse;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.eapprove.business.tenant.manager.MasterDataManager;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.model.ResponseDto;

import java.util.Locale;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("MasterDataControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/master-data")
public class MasterDataController {

    @Autowired
    MasterDataManager masterDataManager;
    @Autowired
    TemplateManager templateManager;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private CredentialHelper credentialHelper;

    @Value("${app.superAdmin.account}")
    private String appSuperAdminAccount;

    @Autowired
    BusinessManager businessManager;

    @Operation(summary = "Create MasterData")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Create MasterData Success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Create MasterData Fail", content = @Content)})
    @PostMapping(value = "/savePermitMD")
    public ResponseEntity<?> savePermitMD(@RequestBody MasterDataRequest masterDataRequest) {
        try {
            String account = appSuperAdminAccount;
            try {
                account = credentialHelper.getJWTPayload().getUsername();
            } catch (Exception e) {
            }
            Boolean result = masterDataManager.savePermitMD(masterDataRequest, account);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                    message(messageSource.getMessage("message.masterdata.create.success", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Create MasterData")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Create MasterData Success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Create MasterData Fail", content = @Content)})
    @PostMapping(value = "/getPermitMD")
    public ResponseEntity<?> getPermitMD(@RequestBody MasterDataRequest masterDataRequest) {
        try {
            String account = appSuperAdminAccount;
            try {
                account = credentialHelper.getJWTPayload().getUsername();
            } catch (Exception e) {
            }

            MasterDataPermitResponse result = masterDataManager.getPermitMD(masterDataRequest, account);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                    message(messageSource.getMessage("message.masterdata.create.success", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e.getMessage());
            return ResponseHelper.fail();
        }
    }

    @GetMapping(value = "/getMDPermit/{account}")
    public ResponseEntity<?> getMDPermit(@PathVariable String account) {
        try {
            MasterDataPermitResponse result = masterDataManager.getMDPermit(account);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).message("success").build());
        } catch (Exception e) {
            log.error("Error getMDPermit: {}", e.getMessage());
            return ResponseHelper.fail();
        }
    }

}