package vn.fis.eapprove.business.application.controller;

import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.bpm.service.BpmTicketFilterService;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.BpmTicketFilterRequest;
import vn.fis.eapprove.business.model.request.BpmTicketFilterSearchRequest;
import vn.fis.eapprove.business.model.response.BpmTicketFilterResponse;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.helper.ResponseHelper;

import java.util.List;
import java.util.Map;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("BpmTicketFilterControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/bpm-ticket-filter")
public class BpmTicketFilterController {

    @Autowired
    public BpmTicketFilterService bpmTicketFilterService;

    @Autowired
    public CredentialHelper credentialHelper;

    @Autowired
    private BusinessManager businessManager;

    @PostMapping("/create-update")
    public ResponseEntity<?> createUpdate(@RequestBody BpmTicketFilterRequest request)  {
        String username = credentialHelper.getJWTPayload().getUsername();
        String messageValid = bpmTicketFilterService.validate(request, username);
        if (!messageValid.isEmpty()) {
            return ResponseHelper.invalid(messageValid);
        }
        Map<String, Object> result = bpmTicketFilterService.createUpdate(request);
        if (result != null) {
            return ResponseHelper.ok(result);
        }
        return ResponseHelper.fail();
    }

    @PostMapping("/update-status")
    public ResponseEntity<?> updateStatus(@RequestBody BpmTicketFilterRequest request)  {
        String username = credentialHelper.getJWTPayload().getUsername();
        String messageValid = bpmTicketFilterService.validate(request, username);
        if (!messageValid.isEmpty()) {
            return ResponseHelper.invalid(messageValid);
        }
        boolean result = bpmTicketFilterService.updateStatus(request);
        if (result) {
            return ResponseHelper.ok();
        }
        return ResponseHelper.fail(null,"Không tìm thấy bản ghi!");
    }

    @PostMapping("/search-filter")
    public ResponseEntity<?> searchFilter(@RequestBody BpmTicketFilterSearchRequest request)  {
        String username = credentialHelper.getJWTPayload().getUsername();
        request.setUsername(username);
        List<BpmTicketFilterResponse> result = bpmTicketFilterService.searchFilter(request);
        return ResponseHelper.ok(result);
    }

    @GetMapping("/get-detail-by-id")
    public ResponseEntity<?> getDetailById(@RequestParam("id") Long id)  {
        String username = credentialHelper.getJWTPayload().getUsername();
        Map<String, Object> response = bpmTicketFilterService.getDetailById(id, username);
        return ResponseHelper.ok(response);
    }

    @PostMapping("/clone")
    public ResponseEntity<?> clone(@RequestBody BpmTicketFilterRequest request) {
        bpmTicketFilterService.cloneById(request.getId());
        return ResponseHelper.ok();
    }

    @DeleteMapping("/delete")
    public ResponseEntity<?> deleteById(@RequestParam("id") Long id) {
        bpmTicketFilterService.deleteById(id);
        return ResponseHelper.ok();
    }

    @PostMapping("/search-filter-ticket")
    public ResponseEntity<?> searchFilterTicket(@RequestBody BpmTicketFilterSearchRequest request)  {
        String username = credentialHelper.getJWTPayload().getUsername();
        request.setUsername(username);
        PageDto result = bpmTicketFilterService.searchFilterTicket(request);
        return ResponseHelper.ok(result);
    }

    @GetMapping("/count-filter-ticket")
    public ResponseEntity<?> countFilterTicket(@RequestParam("type") String type)  {
        String username = credentialHelper.getJWTPayload().getUsername();
        Map<Long, Object> result = bpmTicketFilterService.countSearchFilter(username, type);
        return ResponseHelper.ok(result);
    }

    @PostMapping("/search-filter-option")
    public ResponseEntity<?> searchFilterOption(@RequestBody BpmTicketFilterSearchRequest request) throws IllegalAccessException {
        String username = credentialHelper.getJWTPayload().getUsername();
        request.setUsername(username);
        Object result = businessManager.getFilterData(bpmTicketFilterService.searchFilterOption(request), FilterDataEnum.MANAGE_SHARE_TICKET);
        return ResponseHelper.ok(result);
    }
}
