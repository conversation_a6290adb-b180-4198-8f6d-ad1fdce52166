package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vn.fis.eapprove.business.domain.location.service.LocationManager;
import vn.fis.eapprove.business.dto.CreateLocationImportRequest;
import vn.fis.eapprove.business.dto.LocationManagementFilterDto;
import vn.fis.eapprove.business.model.response.CreateImportResponse;
import vn.fis.eapprove.business.model.response.LocationManagementDto;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.model.ResponseDto;

import java.util.List;
import java.util.Locale;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;


@RestController("LocationManagementControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/local-management")
@Transactional
public class LocationManagementController {

    @Autowired
    LocationManager locationManager;
    @Autowired
    private MessageSource messageSource;

    @Autowired
    BusinessManager businessManager;

    @Operation(summary = "Get location in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get user info organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get user info organization chart is fail", content = @Content)})
    @Tag(name = "location-management-controller", description = "The Business Process Service API with description tag annotation")
    @GetMapping("/getLocation")
    public ResponseEntity<?> getLocation(@RequestParam("search") String search) {
        log.info("Entering getLocation()...");
        try {
            List<LocationManagementDto> locationManagementResponses = locationManager.getListLocation(search);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(locationManagementResponses).build());

        } catch (Exception e) {
            log.error("Error getChartInfoRole: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @GetMapping("/getChart")
    public ResponseEntity<?> getChart(@RequestParam String name) {
        return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(locationManager.getChart(name)).build());
    }

    @GetMapping("/getWorkingTime")
    public ResponseEntity<?> getWorkingTime()  {
        return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(locationManager.checkWorkingTime()).build());
    }

    @GetMapping("/getWorkingTimeCode")
    public ResponseEntity<?> getWorkingTimeCode(@RequestParam("workingTimeCode") String workingTimeCode) {
        return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(locationManager.getWorkingTimeCode(workingTimeCode)).build());
    }

    @PostMapping("/createImport")
    public ResponseEntity<?> createLocationImport(@RequestBody CreateLocationImportRequest createLocationRequest) {
//        return ResponseEntity.ok(locationManager.createImport(createLocationRequest));
        CreateImportResponse status = locationManager.createImport(createLocationRequest);
        if (status.getMessage().equalsIgnoreCase(messageSource.getMessage("message.location.success", null, Locale.getDefault()))) {
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code)
                    .message(status.getMessage()).build());
        }
        return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                .message(status.getMessage()).data(status.getUrlFile()).build());
    }

    @PostMapping("/createManual")
    public ResponseEntity<?> createLocationManual(@RequestBody LocationManagementDto createLocationRequest) {
        return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).message(locationManager.createManual(createLocationRequest)).build());
    }

    @GetMapping("/getWorkingTimeCodeId")
    public ResponseEntity<?> getWTCode(@RequestParam("id") Long id) {
        return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(locationManager.getCode(id)).build());
    }

    @PostMapping(value = "/upload", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE, MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<?> loadSheet(@RequestParam MultipartFile file) {
        return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(locationManager.checkWorkbook(file)).build());
    }

    @PostMapping("/search")
    public ResponseEntity<?> getAll(@RequestBody LocationManagementFilterDto req) {
        return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(locationManager.search(req)).build());
    }

    //    @GetMapping( "/load/{id}")
//    public ResponseEntity<?> load(@PathVariable Long id){
//        return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(locationManager.loadInfor(id)).build());
//    }
    @DeleteMapping("/delete")
    public ResponseEntity<?> deleteById(@RequestParam List<Long> id) {
        return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).message(locationManager.deleteLocation(id)).build());
    }

    @GetMapping("/checkName")
    public ResponseEntity<?> checkName(@RequestParam("name") String name, @RequestParam(value = "id", required = false) Long id) {
        if (locationManager.checkName(name, id)) {
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .data(locationManager.checkName(name, id))
                    .message(messageSource.getMessage("message.location.checkName", null, Locale.getDefault())).build());
        }
        return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code)
                .data(locationManager.checkName(name, id))
                .message(messageSource.getMessage("message.location.nameOK", null, Locale.getDefault())).build());

    }

    @GetMapping("/getAllListWorkingCodeInLocationManagement")
    public ResponseEntity<?> getAllListWorkingCodeInLocationManagement() {
        return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code)
                .data(locationManager.getAllListWorkingCodeInLocationManagement())
                .message("Thành công").build());

    }

    @PostMapping("/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody LocationManagementFilterDto req) {
//        return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(locationManager.searchFilter(req)).build());
        try {
            Object result = businessManager.getFilterData(locationManager.searchFilter(req), FilterDataEnum.LOCATION_MANAGEMENT);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                    message(messageSource.getMessage("message.searchApiLog.searchApiAction.success", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }

    }

}
