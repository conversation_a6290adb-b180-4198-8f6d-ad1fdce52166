package vn.fis.eapprove.business.application.controller;


import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.report.service.ReportProcInstService;
import vn.fis.eapprove.business.dto.filter.ReportProcInstFilter;
import vn.fis.eapprove.business.dto.filter.RequestDetailTaskReturned;
import vn.fis.eapprove.business.dto.report.DetailTicketCancelResponse;
import vn.fis.eapprove.business.model.response.BaseResponse;
import vn.fis.eapprove.business.model.response.ReportProcInstByChartNodeResponse;
import vn.fis.eapprove.business.model.response.ReportProcInstByGroupResponse;
import vn.fis.eapprove.business.tenant.DetailReportService;
import vn.fis.eapprove.business.utils.JwtDecode;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@AllArgsConstructor
@RestController("ReportProcInstControllerV1")
@RequestMapping(SERVICE_PATH + "/report-procInst/old")
public class ReportProcInstController extends BaseController {

    private final ReportProcInstService reportProcInstService;
    private final JwtDecode jwtDecode;
    private final DetailReportService detailReportService;


    @PostMapping("/by-group")
    public ResponseEntity<BaseResponse<ReportProcInstByGroupResponse>> getReportProcInstByGroup(@RequestBody ReportProcInstFilter filter, @RequestHeader("Authorization") String authorizationHeader) {
        String token = authorizationHeader.substring("Bearer".length());
        String username = jwtDecode.getUserName(token);
        ReportProcInstByGroupResponse response = reportProcInstService.getReportProcInstByGroup(filter, username);
        return success(response);
    }

    @PostMapping("/by-user")
    public ResponseEntity<BaseResponse<ReportProcInstByGroupResponse>> getReportTaskByUser(@RequestBody ReportProcInstFilter filter, @RequestHeader("Authorization") String authorizationHeader) {
        String token = authorizationHeader.substring("Bearer".length());
        String username = jwtDecode.getUserName(token);
        ReportProcInstByGroupResponse response = reportProcInstService.getReportTaskByUser(filter, username);
        return success(response);
    }


    @PostMapping("/by-chart-node")
    public ResponseEntity<BaseResponse<ReportProcInstByChartNodeResponse>> getReportProcInstByChartNode(@RequestBody ReportProcInstFilter filter, @RequestHeader("Authorization") String authorizationHeader) {
        String token = authorizationHeader.substring("Bearer".length());
        String username = jwtDecode.getUserName(token);
        ReportProcInstByChartNodeResponse response = reportProcInstService.getReportProcInstByChartNode(filter, username);
        return success(response);
    }

    @PostMapping("/detail-procInst-cancel")
    public ResponseEntity<BaseResponse<DetailTicketCancelResponse>> getDetailTicketCancel(@RequestBody RequestDetailTaskReturned request) {
        return success(detailReportService.getDetailTicketCancel(request.getProcInstId()));
    }
}
