package vn.fis.eapprove.business.application.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.fis.eapprove.business.domain.task.service.TaskActionManager;
import vn.fis.eapprove.business.model.response.TaskActionResponse;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.model.ResponseDto;

import java.util.List;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("TaskActionControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/task-action")
@Tag(name = "task-action-service", description = "Task Action APIs")
public class TaskActionController {
    @Autowired
    private TaskActionManager taskActionManager;

    @Operation(summary = "Get location in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @GetMapping("/getAll")
    public ResponseEntity<?> getAll() {
        try {
            List<TaskActionResponse> result = taskActionManager.getAllTaskActionResponse();
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).build());
        } catch (Exception e) {
            log.error("Error getAllApiManagement: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

}
