package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.fis.eapprove.business.domain.permission.service.PermissionDataManager;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.model.request.PermissionDataRequest;
import vn.fis.spro.common.model.response.PermissionDataResponse;

import java.util.List;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("PermissionDataControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/permission-data")
@Tag(name = "permission-data-management", description = "Permission Data Management")
public class PermissionDataController {

    @Autowired
    private PermissionDataManager permissionDataManager;

    @Operation(summary = "Create permission data")
    @PostMapping("/create-update")
    public ResponseEntity<?> createPermissionData(@RequestBody PermissionDataRequest data) {
        log.info("Entering create permission data.......");
        try {
            permissionDataManager.createPermissionData(data);
            return ResponseHelper.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Create permission data")
    @PostMapping("/create-update-all")
    public ResponseEntity<?> createPermissionData(@RequestBody List<PermissionDataRequest> data) {
        log.info("Entering create permission data.......");
        try {
            permissionDataManager.createAllPermissionData(data);
            return ResponseHelper.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Get list ids permission data")
    @PostMapping("/get-list-permission")
    public ResponseEntity<?> getListPermission(@RequestBody PermissionDataRequest data) {
        log.info("Entering get list ids permission data.......");
        try {
            List<PermissionDataResponse> listPermission = permissionDataManager.getListPermission(data);
            return ResponseHelper.ok(listPermission);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/get-permission-by-id")
    public ResponseEntity<?> getPermissionById(@RequestBody PermissionDataRequest data) {
        log.info("Entering get permission data, id = {}", data.getTypeId());
        try {
            List<String> response = permissionDataManager.getPermissionById(data);
            return ResponseHelper.ok(response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }
}
