package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.model.request.WorkFlowRequest;
import vn.fis.eapprove.business.tenant.manager.ActHiVarInstManager;
import vn.fis.eapprove.business.tenant.manager.CamundaEngineService;
import vn.fis.eapprove.business.tenant.manager.SproService;
import vn.fis.spro.common.camunda.SproFlow;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.util.LogUtils;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

/**
 * Author: PhucVM
 * Date: 16/09/2022
 */
@Slf4j
@RestController("SproControllerV1")
@Tag(name = "spro-service", description = "The SPRO Service API")
@RequestMapping(SERVICE_PATH + "/spro")
public class SproController {

    private final SproService sproService;
    private final CamundaEngineService camundaEngineService;
    private final ActHiVarInstManager actHiVarInstManager;

    @Autowired
    public SproController(SproService sproService,
                          CamundaEngineService camundaEngineService,
                          ActHiVarInstManager actHiVarInstManager
    ) {
        this.sproService = sproService;
        this.camundaEngineService = camundaEngineService;
        this.actHiVarInstManager = actHiVarInstManager;
    }

    @Operation(summary = "Get a work flow by its process-definition-id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK",
                    content = {@Content(mediaType = "application/json",
                            array = @ArraySchema(schema = @Schema(implementation = SproFlow.class)))}),
            @ApiResponse(responseCode = "500", description = "Failed",
                    content = @Content)})
    @PostMapping("/work-flow")
    public ResponseEntity<?> getWorkFlow(
            @Parameter(description = "Work flow request", required = true, schema = @Schema(implementation = WorkFlowRequest.class))
            @Valid @RequestBody WorkFlowRequest request) {
        long startTime = LogUtils.logBegin(log);
        try {
            return ResponseHelper.ok(sproService.getWorkFlow(request));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @PostMapping("/all-work-flow")
    public ResponseEntity<?> getAllWorkFlow(
            @Parameter(description = "Work flow request", required = true, schema = @Schema(implementation = WorkFlowRequest.class))
            @Valid @RequestBody WorkFlowRequest request) {
        long startTime = LogUtils.logBegin(log);
        try {
            log.info("procDefId={}", request.getProcDefId());
            log.info("procInstId={}", request.getProcInstId());

            return ResponseHelper.ok(sproService.getAllWorkFlow(request));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }


    @Operation(summary = "Calculate process-instance expressions")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK",
                    content = @Content),
            @ApiResponse(responseCode = "500", description = "Failed",
                    content = @Content)})
    @GetMapping("/calc-proc-inst-expressions")
    public ResponseEntity<?> calcProcInstExpressions(@RequestParam("procDefId") String procDefId,
                                                     @RequestParam("procInstId") String procInstId) {
        long startTime = LogUtils.logBegin(log);
        try {
            return ResponseHelper.ok(camundaEngineService.calcProcInstExpressions(procDefId, procInstId));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }
}
