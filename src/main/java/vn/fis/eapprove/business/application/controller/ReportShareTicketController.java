package vn.fis.eapprove.business.application.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.fis.eapprove.business.domain.report.service.ReportShareTicketService;
import vn.fis.eapprove.business.model.request.ReportShareTicketRequest;
import vn.fis.eapprove.business.model.response.ReportShareTicketResponse;

import java.util.List;

@RestController("ReportShareTicketController")
@RequestMapping("/report-share-ticket")
public class ReportShareTicketController {

    @Autowired
    private ReportShareTicketService reportShareTicketService;

    @PostMapping("/export")
    public ResponseEntity<?> exportReportShareTicket(@RequestBody ReportShareTicketRequest request) {
        List<ReportShareTicketResponse> response = reportShareTicketService.getReportShareTicket(request);
        return ResponseEntity.ok(response);
    }
}
