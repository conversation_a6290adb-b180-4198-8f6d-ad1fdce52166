package vn.fis.eapprove.business.application.controller;


import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.report.service.ReportByGroupServiceNew;
import vn.fis.eapprove.business.dto.ReportByGroupDto;
import vn.fis.eapprove.business.exception.rest.response.BaseResponse;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("ReportByGroupControllerNewV1")
@RequiredArgsConstructor
@RequestMapping(SERVICE_PATH + "/report-by-group-new")
public class ReportByGroupControllerNew {

    private final ReportByGroupServiceNew reportByGroupServiceNew;

    @PostMapping("/create-new")
    public BaseResponse<String> create(@RequestBody ReportByGroupDto reportByGroupDto) {
        reportByGroupServiceNew.createReportByGroup(reportByGroupDto.getTicketId());
        return BaseResponse.ofSucceeded("Create report by group successfully");
    }

    @PostMapping("/sync-new")
    public BaseResponse<String> sync(@RequestParam(required = false) String fromDate, @RequestParam(required = false) String toDate) {
        reportByGroupServiceNew.syncReportByGroup(fromDate, toDate);
        return BaseResponse.ofSucceeded("Sync report by group successfully");
    }
}
