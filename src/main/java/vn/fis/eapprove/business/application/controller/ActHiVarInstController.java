package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.model.request.ActHiVarInstRequest;
import vn.fis.eapprove.business.model.request.GetByNameAndProcInstId;
import vn.fis.eapprove.business.model.response.VariablesResponse;
import vn.fis.eapprove.business.tenant.manager.ActHiVarInstManager;
import vn.fis.eapprove.business.tenant.manager.CamundaEngineService;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.helper.ResponseHelper;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController("ActHiVarInstControllerV0")
@CrossOrigin("*")
@RequestMapping("")
@Tag(name = "act-hi-var-inst-service", description = "Get variables services")
public class ActHiVarInstController {

    @Autowired
    private ActHiVarInstManager actHiVarInstManager;
    @Autowired
    private ResponseUtils responseUtils;
    @Autowired
    private CamundaEngineService camundaEngineService;

    // Get all variables by procInstId
    @GetMapping(value = "/actHiVarInst/getVariable/{procInstId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getAllVariableByTicket(@PathVariable String procInstId) {
        try {
            return ResponseEntity.ok(camundaEngineService.getVariableInstances(procInstId));
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    //Get variables by procInstId and type
    @GetMapping("/actHiVarInst/getByType/{procInstId}/{type}")
    public ResponseEntity<?> getVariableByType(@PathVariable String procInstId, @PathVariable List<String> type) {
        try {
            List<Map<String, Object>> variableInstDtoList = actHiVarInstManager.getVariableByType(procInstId, type);
            if (variableInstDtoList != null) {
                return responseUtils.getResponseEntity(variableInstDtoList, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, 1, "Success", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    //Get variables by procInstId and name
    @GetMapping("/actHiVarInst/getByName/{procInstId}/{name}")
    public ResponseEntity<?> getVariableByName(@PathVariable String procInstId, @PathVariable List<String> name) {
        try {
            List<Map<String, Object>> variableInstDtoList = actHiVarInstManager.getVariableByName(procInstId, name);
            if (variableInstDtoList != null) {
                return responseUtils.getResponseEntity(variableInstDtoList, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, 1, "Success", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    //Get variables by procInstId and name
    @GetMapping("/actHiVarInst/getByTask")
    public ResponseEntity<?> getVariTask(@RequestParam("taskId") String taskId,
                                         @RequestParam("type") String type) {
        try {
            Map<String, Object> variableInstDtoList = actHiVarInstManager.getVariByTask(taskId, type);
            if (variableInstDtoList != null) {
                return responseUtils.getResponseEntity(variableInstDtoList, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, 1, "Success", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/actHiVarInst/get-by-incoming-task")
    @Operation(summary = "Get all incoming task's variables")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK",
                    content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Failed",
                    content = @Content)})
    public ResponseEntity<?> getByIncomingTask(@RequestParam("procInstId") String procInstId,
                                               @RequestParam("incomingTaskKey") String incomingTaskKey) {
        try {
            Map<String, Object> result = actHiVarInstManager.getByIncomingTask(procInstId, incomingTaskKey);
            if (result != null) {
                return ResponseHelper.ok(result);
            }

            return ResponseHelper.notFound();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    //Get variables by procInstId and name fixed
    @GetMapping("/actHiVarInst/getByNamev2/{procInstId}/{name}")
    public ResponseEntity<?> getVariName(@PathVariable String procInstId, @PathVariable List<String> name,
                                         @Param("isRu") Boolean isRu) {
        try {
            List<Map<String, Object>> variableInstDtoList = actHiVarInstManager.getVariByName(name, procInstId, isRu);
            if (variableInstDtoList != null) {
                return responseUtils.getResponseEntity(variableInstDtoList, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, 1, "Success", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    //Get variables by procInstId and name fixed
    @PostMapping("/actHiVarInst/getByNameAndProcInstId")
    public ResponseEntity<?> getByNameAndProcInstId(@RequestBody GetByNameAndProcInstId getByNameAndProcInstId) {
        try {
            List<Map<String, Object>> variableInstDtoList = actHiVarInstManager.getVariByName(getByNameAndProcInstId.getNames(), getByNameAndProcInstId.getProcInstId(), null);
            if (variableInstDtoList != null) {
                return responseUtils.getResponseEntity(variableInstDtoList, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, 1, "Success", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    //Get variables by procInstId and name fixed
    @GetMapping("/actHiVarInst/getByTicket/{procInstId}")
    public ResponseEntity<?> getByTicket(@PathVariable String procInstId) {
        try {
            List<Map<String, Object>> variableInstDtoList = actHiVarInstManager.getVariByTicket(procInstId);
            if (variableInstDtoList != null) {
                return responseUtils.getResponseEntity(variableInstDtoList, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, 1, "Success", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/actHiVarInst/getVariByRu")
    public ResponseEntity<?> getVariByRu(@RequestBody ActHiVarInstRequest request) {
        try {
            VariablesResponse variablesResponse = actHiVarInstManager.listVariablesByRu(request);
            if (variablesResponse != null) {
                return responseUtils.getResponseEntity(variablesResponse, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, 1, "Success", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
