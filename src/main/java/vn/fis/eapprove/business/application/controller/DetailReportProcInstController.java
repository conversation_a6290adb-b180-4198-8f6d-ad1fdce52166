package vn.fis.eapprove.business.application.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.dto.filter.ReportProcInstFilter;
import vn.fis.eapprove.business.dto.filter.RequestDetailTaskReturned;
import vn.fis.eapprove.business.dto.report.DetailTaskDto;
import vn.fis.eapprove.business.dto.report.DetailTaskReturnedDto;
import vn.fis.eapprove.business.model.response.BaseResponse;
import vn.fis.eapprove.business.model.response.DetailReportByGroupResponse;
import vn.fis.eapprove.business.tenant.DetailReportService;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.utils.JwtDecode;

import java.util.List;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("DetailReportProcInstControllerV1")
@RequiredArgsConstructor
@RequestMapping(SERVICE_PATH + "/detail-report-procInst/old")
public class DetailReportProcInstController extends BaseController {
    private final DetailReportService service;
    private final CustomerService customerService;
    private final JwtDecode jwtDecode;


    @PostMapping("/by-user")
    public ResponseEntity<BaseResponse<DetailReportByGroupResponse>> getAllDetailReportByUser(@RequestBody ReportProcInstFilter filter, @RequestHeader("Authorization") String authorizationHeader) {
        String token = authorizationHeader.substring("Bearer".length());
        String username = jwtDecode.getUserName(token);
        DetailReportByGroupResponse response = service.getDetailReportByUser(filter,username);
        return success(response);
    }

    @PostMapping("/by-chart-node")
    public ResponseEntity<BaseResponse<DetailReportByGroupResponse>> getAllDetailReportByChartNode(@RequestBody ReportProcInstFilter filter, @RequestHeader("Authorization") String authorizationHeader) {
        String token = authorizationHeader.substring("Bearer".length());
        String username = jwtDecode.getUserName(token);
        DetailReportByGroupResponse response = service.getDetailReportByChartNode(filter,username);
        return success(response);
    }
    @PostMapping("/by-group")
    public ResponseEntity<BaseResponse<DetailReportByGroupResponse>> getAllDetailReportByGroup(@RequestBody ReportProcInstFilter filter, @RequestHeader("Authorization") String authorizationHeader) {
        String token = authorizationHeader.substring("Bearer".length());
        String username = jwtDecode.getUserName(token);
        DetailReportByGroupResponse response = service.getDetailReportByGroup(filter,username);
        return success(response);
    }

    @GetMapping("/detail-task")
    public ResponseEntity<BaseResponse<List<DetailTaskDto>>> getDetailTaskByProcInstId(@RequestParam Long ticketId) {
        return success(service.getDetailTaskByProcInstId(ticketId));
    }

    @PostMapping("/detail-task-returned")
    public ResponseEntity<BaseResponse<List<DetailTaskReturnedDto>>> getDetailTaskReturnedByProcInstId(@RequestBody RequestDetailTaskReturned request) {
        return success(service.getDetailTaskReturned(request));
    }

}
