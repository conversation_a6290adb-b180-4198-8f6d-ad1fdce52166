package vn.fis.eapprove.business.application.controller;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.report.service.ReportByGroupService;
import vn.fis.eapprove.business.dto.ReportByGroupDto;
import vn.fis.eapprove.business.exception.rest.response.BaseResponse;
import vn.fis.eapprove.business.producer.ReportProducer;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("ReportByGroupControllerV1")
@RequiredArgsConstructor
@Slf4j
@RequestMapping(SERVICE_PATH + "/report-by-group")
public class ReportByGroupController {

    private final ReportByGroupService reportByGroupService;

    private final ReportProducer reportProducer;

    @Value("${spring.kafka.consumer.topic.insert-report-by-group}")
    private String topicInsertReportByGroup;

    @PostMapping("/create")
    public BaseResponse<String> create(@RequestBody ReportByGroupDto reportByGroupDto) {
        log.info("Create report by group with ticket Id: {}", reportByGroupDto.getTicketId());
        reportProducer.sendKafka(reportByGroupDto.getTicketId(), topicInsertReportByGroup);
        return BaseResponse.ofSucceeded("ok");
    }

    @PostMapping("/sync")
    public BaseResponse<String> sync(@RequestParam(required = false) String fromDate, @RequestParam(required = false) String toDate) {
        reportByGroupService.syncReportByGroup(fromDate, toDate);
        return BaseResponse.ofSucceeded("Sync report by group successfully");
    }
}
