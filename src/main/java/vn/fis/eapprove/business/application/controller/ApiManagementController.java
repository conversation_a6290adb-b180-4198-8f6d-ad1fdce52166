package vn.fis.eapprove.business.application.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.api.entity.ApiManagement;
import vn.fis.eapprove.business.domain.api.repository.ApiManagementRepository;
import vn.fis.eapprove.business.domain.api.service.ApiManager;
import vn.fis.eapprove.business.dto.PageSearchDto;
import vn.fis.eapprove.business.model.request.ApiManagementFilterRequest;
import vn.fis.eapprove.business.model.request.ApiManagementRequest;
import vn.fis.eapprove.business.model.response.ApiManagementResponse;

import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.List;
import java.util.Locale;

import static vn.fis.eapprove.business.constant.Constant.MESSAGE_SUCCESS;
import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("ApiManagementControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/api-management")
public class ApiManagementController {

    @Autowired
    ApiManager apiManager;

    @Autowired
    MessageSource messageSource;

    @Autowired
    ResponseUtils responseUtils;

    @Autowired
    BusinessManager businessManager;

    @Autowired
    ApiManagementRepository apiManagementRepository;


    @Operation(summary = "Get location in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "business-process-service", description = "The Business Process Service API with description tag annotation")
    @GetMapping("/getAll")
    public ResponseEntity<?> getAll() {
        try {
            List<ApiManagementResponse> result = apiManager.getAll();
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).build());
        } catch (Exception e) {
            log.error("Error getAllApiManagement: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @Operation(summary = "search ApiAtion in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "api-management", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/searchApiAction")
    public ResponseEntity<?> searchApiAction(@RequestBody ApiManagementFilterRequest req) {
        try {
            PageSearchDto page = apiManager.searchApiAction(req);
            if (!CollectionUtils.isEmpty(page.getContent())) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(page).
                        message(messageSource.getMessage("message.api-management.searchApiAction.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code)
                    .message(messageSource.getMessage("message.api-management.searchApiAction.fail", null, Locale.getDefault())).data(page.getContent()).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Get List Base Url ApiAtion in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "api-management", description = "The Business Process Service API with description tag annotation")
    @GetMapping("/getListBaseUrl")
    public ResponseEntity<?> getListBaseUrl() {
        return ResponseEntity.ok(apiManager.listBaseUrl());
    }

    @Operation(summary = "Get List Api Authen ApiAtion in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "api-management", description = "The Business Process Service API with description tag annotation")
    @GetMapping("/getListApiAuthen")
    public ResponseEntity<?> getListApiAuthen() {
        return ResponseEntity.ok(apiManager.listApiAuthen());
    }


    @Operation(summary = "Share with people ApiAtion in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "api-management", description = "The Business Process Service API with description tag annotation")
    @GetMapping("/shareWithApiAction")
    public ResponseEntity<?> shareWithApiAction() {
        return ResponseEntity.ok(apiManager.shareWithApiAction());
    }


    @Operation(summary = "check Exist Name ApiAtion in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "api-management", description = "The Business Process Service API with description tag annotation")
    @GetMapping("/checkExistName")
    public ResponseEntity<?> checkExistName(@RequestParam("nameApiAction") String nameApiAction,
                                            @RequestParam(value = "id", required = false) Long id) {
        try {
            Boolean checkNameApiAction = apiManager.checkNameExits(nameApiAction, id);
            if (checkNameApiAction == true) {
                return responseUtils.getResponseEntity(true, 1, "Tên API đã tồn tại trong hệ thống", HttpStatus.OK);
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(checkNameApiAction).
                    message(messageSource.getMessage("message.api-management.createApiAction.success", null, Locale.getDefault())).build());
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Create ApiAtion in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "api-management", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/createApiConfig")
    public ResponseEntity<?> createApiAction(@RequestBody ApiManagementRequest apiManagementRequest) {
        try {
            String status = apiManager.createAction(apiManagementRequest);
            if (status.equals(MESSAGE_SUCCESS)) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).
                        message(messageSource.getMessage("message.api-management.createApiAction.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.api-management.createApiAction.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Update ApiAtion in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "api-management", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/updateApiAction")
    public ResponseEntity<?> updateApiAction(@RequestBody ApiManagementRequest apiManagementRequest) {
        try {
            String status = apiManager.updateApiAction(apiManagementRequest);
            if (status.equals(MESSAGE_SUCCESS)) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).
                        message(messageSource.getMessage("message.api-management.updateApiAction.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.api-management.updateApiAction.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Clone ApiAtion in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "api-management", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/cloneApiAction")
    public ResponseEntity<?> cloneApiAction(@RequestParam("id") Long id) {
        try {
            ApiManagement apiManagement = apiManager.cloneApiAction(id);
            if (!ValidationUtils.isNullOrEmpty(apiManagement)) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).
                        message(messageSource.getMessage("message.api-management.cloneApiAction.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.api-management.cloneApiAction.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "delete ApiAction in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "api-management", description = "The Business Process Service API with description tag annotation")
    @DeleteMapping("/deleteApiAction")
    public ResponseEntity<?> deleteApiAction(@RequestParam List<Long> listApiActionId) {
        try {
            String status = apiManager.deleteApiAction(listApiActionId);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).
                    message(status).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "delete ApiAction in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "api-active", description = "The Business Process Service API with description tag annotation")
    @DeleteMapping("/active")
    public ResponseEntity<?> active(@RequestParam List<Long> listApiActionId) {
        try {
            apiManager.active(listApiActionId);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).
                    message("Thành công").build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Get All Filter Data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "api-management", description = "Get all filterData")
    @PostMapping("/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody ApiManagementFilterRequest req) {
        try {
            Object result = businessManager.getFilterData(apiManager.searchFilter(req), FilterDataEnum.API_MANAGEMENT);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                    message(messageSource.getMessage("message.searchApiLog.searchApiAction.success", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }
}
