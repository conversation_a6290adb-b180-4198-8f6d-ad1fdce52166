package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.codeGen.service.CodeGenConfigService;
import vn.fis.eapprove.business.dto.CodeGenConfigDto;
import vn.fis.eapprove.business.model.request.CodeGenConfigCrudRequest;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.model.ResponseDto;

import java.util.List;
import java.util.Locale;
import java.util.Map;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

/**
 * Author: AnhVTN
 * Date: 30/03/2023
 */

@Slf4j
@RestController("CodeGenControllerV1")
@CrossOrigin("*")
@RequestMapping(SERVICE_PATH + "/code-gen-config")
@Tag(name = "code-gen-config", description = "The code gen config service")
public class CodeGenController {
    private final CodeGenConfigService service;
    private final MessageSource messageSource;

    private final BusinessManager businessManager;

    @Autowired
    public CodeGenController(CodeGenConfigService service, MessageSource messageSource, BusinessManager businessManager) {
        this.service = service;
        this.messageSource = messageSource;
        this.businessManager = businessManager;
    }

    @Operation(summary = "Create Update Code gen config and Code gen structor")
    @PostMapping("/create-update")
    public ResponseEntity<?> creatOrUpdate(@RequestBody CodeGenConfigCrudRequest request) {
        try {
            String messValid = "";
            if (request.getCode() == null || request.getCode().equalsIgnoreCase("")) {
                messValid = "Code không được null";
            }
//            } else if(request.getId() == null){
//                messValid = "Id không được null";
            else if (request.getId() == null && !service.checkExist(request.getCode())) {
                messValid = "Loại mã đã tồn tại";
            }
//            else if(request.getId() != null && service.checkUpdateDuplicateCode(request.getId(), request.getCode())){
//                messValid = "Đã tồn tại mã code và id";
//            }
            if (messValid.equals("")) {
                service.saveAll(request);
                return ResponseHelper.ok();
            }
            return ResponseHelper.ok(null, messValid);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Delete by ids Code gen config and Code gen structor")
    @PostMapping("/delete")
    public ResponseEntity<?> delete(@RequestBody List<Long> request) {
        try {
            Boolean check = service.updateStatusByIds(request,"deactive");
            if (!check)
                return ResponseHelper.ok();
            else
                return ResponseHelper.fail(null,"Cấu hình đang được sử dụng");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e.getMessage().contains("No class vn.fis.eapprove.business.tenant.entity.CodeGenConfig entity with id"))
                return ResponseHelper.ok(null, "Không tìm thấy cấu hình hiện tại");
            return ResponseHelper.fail();
        }
    }
    @Operation(summary = "Delete by ids Code gen config and Code gen structor")
    @PostMapping("/active")
    public ResponseEntity<?> active(@RequestBody List<Long> request) {
        try {
            Boolean check = service.updateStatusByIds(request,"active");
            if (!check)
                return ResponseHelper.ok();
            else
                return ResponseHelper.fail(null,"Cấu hình đang được sử dụng");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e.getMessage().contains("No class vn.fis.eapprove.business.tenant.entity.CodeGenConfig entity with id"))
                return ResponseHelper.ok(null, "Không tìm thấy cấu hình hiện tại");
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Delete by ids Code gen config and Code gen structor")
    @GetMapping("/get-code-gen-edit")
    public ResponseEntity<?> getCodeGenEdit(@RequestParam Long id) {
        try {
            if (id == null) {
                return ResponseHelper.invalid("Id must not be null !!");
            }
            return ResponseHelper.ok(service.getCodeGenEdit(id));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Delete by ids Code gen config and Code gen structor")
    @PostMapping("/get-code-gen")
    public ResponseEntity<?> getCodeGen(@RequestBody CodeGenConfigDto request) {
        try {
            return ResponseHelper.ok(service.getCodeGenConfig(request));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Delete by ids Code gen config and Code gen structor")
    @PostMapping("/findById")
    public ResponseEntity<?> findById(@RequestParam("id") Long id ) {
        try {
            return ResponseHelper.ok(service.findById(id));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }


    @PostMapping("/autoGenerateCode/{code}")
    public ResponseEntity<?> autoGenerateCode(@RequestBody Map<String, Object> mDataConfig,
                                              @PathVariable String code) {
        try {
            return ResponseHelper.ok(service.autoGenerateCode(code, mDataConfig));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/autoGenerateCodeBasicAuth/{code}")
    public ResponseEntity<?> autoGenerateCodeBasicAuth(@RequestBody Map<String, Object> mDataConfig, @PathVariable String code) {
        try {
            return ResponseHelper.ok(service.autoGenerateCodeBasicAuth(code, mDataConfig));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @GetMapping("/autoResetStructCode")
    public ResponseEntity<?> autoReset() {
        try {
            service.autoResetGenerateCode();
            return ResponseHelper.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Check exist by Code gen config and Code gen structor")
    @GetMapping("/checkExistByCode")
    public ResponseEntity<?> checkExistByCode(@RequestParam String code, @RequestParam String configCode) {
        try {
            return ResponseHelper.ok(service.checkExistByCode(code, configCode));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Get all filter")
    @PostMapping("/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody CodeGenConfigDto request) {
        try {
            Object result = businessManager.getFilterData(service.getCodeGenConfigFilter(request), FilterDataEnum.CODE_GEN_CONFIG);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                    message(messageSource.getMessage("message.searchApiLog.searchApiAction.success", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Reset all RedisAtomicInteger")
    @PostMapping("/getAtomicInteger/{key}")
    public ResponseEntity<?> getAtomicInteger(@PathVariable String key) {
//        service.getAtomicInteger(key);
        return ResponseHelper.ok(service.getAtomicInteger(key));
    }
}
