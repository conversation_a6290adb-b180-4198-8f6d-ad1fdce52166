
package vn.fis.eapprove.business.application.controller;

import vn.fis.eapprove.security.CredentialHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vn.fis.eapprove.business.domain.assign.entity.AssignManagement;
import vn.fis.eapprove.business.domain.assign.service.AssignManager;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.dto.AssignDto;
import vn.fis.eapprove.business.dto.AssignHistoryDto;
import vn.fis.eapprove.business.dto.ListTicketDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.AssignRequest;
import vn.fis.eapprove.business.model.response.LoadAssignResponse;
import vn.fis.eapprove.business.task.AuthorityManagementTask;

import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.List;
import java.util.Locale;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;
import static vn.fis.eapprove.business.constant.Constant.SUCCESS;

@RestController("AssignControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/assign")
public class AssignController {
    @Autowired
    CredentialHelper credentialHelper;
    @Autowired
    ResponseUtils responseUtils;
    @Autowired
    AssignManager assignManager;
    @Autowired
    private MessageSource messageSource;

    @Autowired
    private AuthorityManagementTask authorityManagementTask;

    @Autowired
    private BusinessManager businessManager;
    @Operation(summary = "Create assign")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Creating assign is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Creating assign is fail", content = @Content)})
    @Tag(name = "Assign-controller", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/createAssign")
    public ResponseEntity<?> createAssign(@RequestBody AssignRequest assignRequest) {
        try {
            String result = assignManager.createAssign(assignRequest);
            String msg = "message.assign.createAssign.success";
            Integer code = ResponseCodeEnum.SUCCESS.code;
            if (result == "Fail") {
                msg = "message.assign.createAssign.fail";
                code = ResponseCodeEnum.FAIL.code;
            } else if (result == "NoApproved") {
                msg = "message.assign.createAssign.notApproved";
            }
            return ResponseEntity.ok(ResponseDto.builder().code(code).data(result).
                    message(messageSource.getMessage(msg, null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());

        }
    }

    @Operation(summary = "Updated assign")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Updated assign is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Updated assign is fail", content = @Content)})
    @Tag(name = "Assign-controller", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/updatedAssign")
    public ResponseEntity<?> updatedAssign(@RequestBody AssignRequest assignRequest) {
        try {
            Boolean result = assignManager.updatedAssign(assignRequest);
            if (result) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                        message(messageSource.getMessage("message.assign.updateAssign.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).data(result).
                    message(messageSource.getMessage("message.assign.updateAssign.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());

        }
    }

    @Operation(summary = "Load file")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Load file is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Load file is fail", content = @Content)})
    @Tag(name = "Assign-controller", description = "The Business Process Service API with description tag annotation")
    @PostMapping(value = "/loadFile", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE, MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<?> loadFile(@RequestParam("file") MultipartFile file) {
        try {
            String status = assignManager.upload(file);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

//    @Operation(summary = "Load assign")
//    @ApiResponses(value = {
//            @ApiResponse(responseCode = "200", description = "Load assign is success", content = {@Content(mediaType = "application/json")}),
//            @ApiResponse(responseCode = "500", description = "Load assign is fail", content = @Content)})
//    @Tag(name = "Assign-controller", description = "The Business Process Service API with description tag annotation")
//    @PostMapping("/loadAssign/{id}")
//    public ResponseEntity<?> loadAssign(@PathVariable Long id) {
//        try {
//             LoadAssignResponse loadAssignResponse = assignManager.loadAssign(id);
//            if (!ValidationUtils.isNullOrEmpty(loadAssignResponse)) {
//                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(loadAssignResponse).
//                        message(messageSource.getMessage("message.assign.loadAssign.success", null, Locale.getDefault())).build());
//            }
//            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).data(loadAssignResponse).
//                    message(messageSource.getMessage("message.assign.loadAssign.fail", null, Locale.getDefault())).build());
//        } catch (Exception e) {
//            log.error("Error: {}", e);
//            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
//                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
//
//        }
//    }

    @Operation(summary = "Delete assign")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Delete assign is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Delete assign is fail", content = @Content)})
    @Tag(name = "Assign-controller", description = "The Business Process Service API with description tag annotation")
    @DeleteMapping("/deleteAssign/{id}")
    public ResponseEntity<?> deleteAssign(@PathVariable List<Long> id) {
        try {
            int status = assignManager.deleteAssign(id);
            switch (status) {
                case SUCCESS:
                    return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).
                            message(messageSource.getMessage("message.assign.deletebyid.success", null, Locale.getDefault())).build());
                default:
                    return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                            .message(messageSource.getMessage("error.assign.deletebyid.fail", null, Locale.getDefault())).build());
            }
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Search and Get all assign")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Search and Get all assign is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Search and Get all assign is fail", content = @Content)})
    @Tag(name = "Assign-controller", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/searchAssign")
    public ResponseEntity<?> searchAssign(@RequestBody AssignDto req) {
        try {
            PageDto page = assignManager.searchAssign(req);
            if (!CollectionUtils.isEmpty(page.getContent())) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(page).
                        message(messageSource.getMessage("message.assign.searchAssign.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code)
                    .message(messageSource.getMessage("message.assign.searchAssign.fail", null, Locale.getDefault())).data(page.getContent()).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Get all assign history")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get all assign history is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get all assign history is fail", content = @Content)})
    @Tag(name = "Assign-controller", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/getAllAssignHistory/{id}")
    public ResponseEntity<?> getAllAssignHistory(@PathVariable Long id) {
        try {
            List<AssignHistoryDto> assignHistory = assignManager.getAllAssignHistory(id);
            if (!ValidationUtils.isNullOrEmpty(assignHistory)) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(assignHistory).
                        message(messageSource.getMessage("message.assign.getAllAssignHistory.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).data(assignHistory).
                    message(messageSource.getMessage("message.assign.getAllAssignHistory.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }

    }

    @Operation(summary = "Get all procInst")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get all proInst is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get all proInst is fail", content = @Content)})
    @Tag(name = "Assign-controller", description = "The Business Process Service API with description tag annotation")
    @GetMapping("/getAllProcInst")
    public ResponseEntity<?> getAllProcInst() {
        try {
            List<ListTicketDto> data = assignManager.getAllProcInst();
            if (!ValidationUtils.isNullOrEmpty(data)) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(data).
                        message(messageSource.getMessage("message.assign.getAllProInst.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).data(data).
                    message(messageSource.getMessage("message.assign.getAllProInst.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Get service range by id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get all proInst is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get all proInst is fail", content = @Content)})
    @Tag(name = "Assign-controller", description = "The Business Process Service API with description tag annotation")
    @GetMapping("/getServiceRange")
    public ResponseEntity<?> getServiceRange(Long id) {
        try {
            List<ServicePackage> data = assignManager.findServiceRangeById(id);
            if (!ValidationUtils.isNullOrEmpty(data)) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(data).
                        message(messageSource.getMessage("message.assign.getAllProInst.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).data(data).
                    message(messageSource.getMessage("message.assign.getAllProInst.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Get all assign")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get all proInst is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get all proInst is fail", content = @Content)})
    @Tag(name = "Assign-controller", description = "The Business Process Service API with description tag annotation")
    @GetMapping("/getAllAssign")
    public ResponseEntity<?> getAllAssign(@RequestParam String username) {
        try {
            List<AssignManagement> data = assignManager.getAllAssign(username);
            if (!ValidationUtils.isNullOrEmpty(data)) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(data).
                        message(messageSource.getMessage("message.assign.getAllProInst.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).data(data).
                    message(messageSource.getMessage("message.assign.getAllProInst.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Get all assign")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get all proInst is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get all proInst is fail", content = @Content)})
    @Tag(name = "Assign-controller", description = "The Business Process Service API with description tag annotation")
    @GetMapping("/transferAssign")
    public ResponseEntity<?> transferAssign() {
        try {
            authorityManagementTask.transferAssign();
            return ResponseHelper.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Get all assign active by username")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get all assign active success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get all assign active fail", content = @Content)})
    @Tag(name = "Assign-controller", description = "The Business Process Service API with description tag annotation")
    @GetMapping("/getAllAssignActiveByUsername")
    public ResponseEntity<?> getAllAssignActiveByUsername(@RequestParam("username") String username) {
        try {
            List<AssignManagement> data = assignManager.getAllAssignActiveByUsername(username);
            if (!ValidationUtils.isNullOrEmpty(data)) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(data).
                        message(messageSource.getMessage("message.assign.getAllProInst.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).data(data).
                    message(messageSource.getMessage("message.assign.getAllProInst.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Get all history")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get all history success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get all history fail", content = @Content)})
    @Tag(name = "Assign-controller")
    @GetMapping("/getHistoryAssistant")
    public ResponseEntity<?> getAllAssignActiveByUsername(@RequestParam("serviceId") Long serviceId) {
        try {
            LoadAssignResponse data = assignManager.getHistoryAssigned(serviceId);
            if (!ValidationUtils.isNullOrEmpty(data)) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(data).
                        message(messageSource.getMessage("message.assign.getAllProInst.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).data(data).
                    message(messageSource.getMessage("message.assign.getAllProInst.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    //Hàm test (Bỏ được)
    @GetMapping("/updateStatusAssignManager")
    public ResponseEntity<?> updateStatusAssignManager() {
        try {
            assignManager.updateStatusAssignManager();
            return ResponseEntity.ok(null);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Get all assign ")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get all assign history is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get all assign history is fail", content = @Content)})
    @Tag(name = "Assign-controller", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/getAllAssignByRequestCode")
    public ResponseEntity<?> getAllAssignByRequestCode(@RequestParam(value = "requestCode", required = false) String requestCode) {
        try {
            LoadAssignResponse loadAssignResponses = assignManager.getAllAssignByRequestCode(requestCode);
            if (!ValidationUtils.isNullOrEmpty(loadAssignResponses)) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(loadAssignResponses).
                        message(messageSource.getMessage("message.assign.searchAssign.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).data(loadAssignResponses).
                    message(messageSource.getMessage("message.assign.searchAssign.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }

    }

    @Operation(summary = "Get filter data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Search and Get all signatures is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Search and Get all signatures is fail", content = @Content)})
    @Tag(name = "Assign-controller", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody AssignDto req) {
        try {
            List<LoadAssignResponse> page = assignManager.searchAssignFilter(req);
            try {
                Object result = businessManager.getFilterData(page, FilterDataEnum.ASSIGN_MANAGEMENT);
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                        message("Thành Công").build());
            } catch (Exception e) {
                return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
            }
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }


}
