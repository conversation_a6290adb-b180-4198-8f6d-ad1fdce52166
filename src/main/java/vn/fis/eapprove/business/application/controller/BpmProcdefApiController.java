package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcdefApiManager;
import vn.fis.eapprove.business.dto.BpmProcdefApiDto;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.model.ResponseDto;

import java.util.Locale;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("BpmProcdefApiControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/bpm-proc-def-api")
@Tag(name = "bpm-proc-def-api-service", description = "BPM Procdef API Services")
public class BpmProcdefApiController {
    @Autowired
    private MessageSource messageSource;

    @Autowired
    private BpmProcdefApiManager bpmProcdefApiManager;

    @Operation(summary = "Delete bpm-proc-def-api")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Delete is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Delete is fail", content = @Content)})
    @PostMapping("/delete")
    public ResponseEntity<?> delete(@RequestBody BpmProcdefApiDto bpmProcdefApiDto) {
        try {
            Boolean result = bpmProcdefApiManager.delete(bpmProcdefApiDto);
            if (result) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                        message(messageSource.getMessage("message.bpmProcdefApi.delete.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("error.bpmProcdefApi.delete.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }
}
