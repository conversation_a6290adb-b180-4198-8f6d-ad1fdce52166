package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.currency.entity.Currency;
import vn.fis.eapprove.business.domain.currency.service.CurrencyService;
import vn.fis.eapprove.business.dto.CurrencyFilterDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.CurrencyRequest;

import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.model.ResponseDto;

import java.util.Date;
import java.util.List;
import java.util.Locale;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

/**
 * Author: AnhVTN
 * Date: 31/03/2023
 */

@RestController("CurrencyControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/currency")
public class CurrencyController {

    private final CurrencyService currencyService;
    private final MessageSource messageSource;

    private final ResponseUtils responseUtils;

    @Autowired
    public CurrencyController(CurrencyService currencyService, MessageSource messageSource, ResponseUtils responseUtils) {
        this.currencyService = currencyService;
        this.messageSource = messageSource;
        this.responseUtils = responseUtils;
    }

    @Autowired
    BusinessManager businessManager;

    @PostMapping("/create-update")
    @Operation(summary = "Create and update currency")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Currency created or updated",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = Currency.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "500", description = "Server error", content = @Content)})
    public ResponseEntity<?> createCurrency(@RequestBody CurrencyRequest request) {
        try {
            Currency currency = null;
            if (request.getId() == null) {
                if (request.getDescription() == null || request.getDescription().isEmpty()) {
                    return responseUtils.getResponseEntity(null, -1, "Description is required", HttpStatus.OK);
                }
                if (currencyService.isExistCode(request.getCode()))
                    return responseUtils.getResponseEntity(null, -1, "Code is exist", HttpStatus.OK);
                if (currencyService.checkNameExist(request.getName())) {
                    return responseUtils.getResponseEntity(null, -1, "Name is exist", HttpStatus.OK);
                }
//                if (currencyService.checkAnotherNameExist(request.getAnotherName())) {
//                    return responseUtils.getResponseEntity(null, -1, "Another Name is exist", HttpStatus.OK);
//                }
                currency = Currency.builder()
                        .createdAt(new Date())
                        .updatedAt(null)
                        .roundingRules(request.getRoundingRules())
                        .anotherName(request.getAnotherName())
                        .userCreate(request.getUserCreate())
                        .description(request.getDescription())
                        .name(request.getName())
                        .code(request.getCode())
                        .build();
            } else {
                if (request.getDescription() == null || request.getDescription().isEmpty()) {
                    return responseUtils.getResponseEntity(null, -1, "Description is required", HttpStatus.OK);
                }
                if (currencyService.checkNameExistForUpdate(request.getId(), request.getName())) {
                    return responseUtils.getResponseEntity(null, -1, "Name is exist", HttpStatus.OK);
                }
//                if (currencyService.checkNameExistForUpdate(request.getId(), request.getAnotherName())) {
//                    return responseUtils.getResponseEntity(null, -1, "Another Name is exist", HttpStatus.OK);
//                }
                if (currencyService.checkCodeExistForUpdate(request.getId(), request.getCode())) {
                    return responseUtils.getResponseEntity(null, -1, "Code is exist", HttpStatus.OK);
                }
                Currency currencyExist = currencyService.findById(request.getId());
                currency = Currency.builder()
                        .id(request.getId())
                        .createdAt(currencyExist.getCreatedAt())
                        .updatedAt(new Date())
                        .roundingRules(request.getRoundingRules())
                        .anotherName(request.getAnotherName())
                        .description(request.getDescription())
                        .userCreate(request.getUserCreate())
                        .userUpdated(request.getUpdatedUser())
                        .name(request.getName())
                        .code(request.getCode())
                        .build();
            }
            Currency result = currencyService.saveCurrency(currency);
            if (result != null) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).message("success").build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).data(result).message("fail").build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code).message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get currency by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Currency found",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = Currency.class))),
            @ApiResponse(responseCode = "404", description = "Currency not found"),
            @ApiResponse(responseCode = "500", description = "Server error", content = @Content)})
    public ResponseEntity<?> getCurrencyById(@PathVariable Long id) {
        try {
            Currency result = currencyService.findById(id);
            if (result != null) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                        message("success").build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).data(result).
                    message("fail").build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());

        }
    }

    @PostMapping("/delete")
    @Operation(summary = "Delete currency by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Currency deleted"),
            @ApiResponse(responseCode = "404", description = "Currency not found"),
            @ApiResponse(responseCode = "500", description = "Server error", content = @Content)})
    public ResponseEntity<?> deleteCurrency(@RequestBody List<Long> ids) {
        try {
            if (ids.isEmpty()) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).data(null).
                        message("fail").build());
            }
            currencyService.deleteByIds(ids);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(null).
                    message("success").build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());

        }
    }

    @PostMapping("/get-currencys")
    @Operation(summary = "Get currencys")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Currency loaded "),
            @ApiResponse(responseCode = "404", description = "Currency not found"),
            @ApiResponse(responseCode = "500", description = "Server error", content = @Content)})
    public ResponseEntity<?> getCurrencys(@RequestBody CurrencyFilterDto request) {
        try {
            PageDto currencies = currencyService.getCurrencys(request);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(currencies).
                    message("success").build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @PostMapping("/getFilterData")
    @Operation(summary = "getFilterData")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Currency loaded "),
            @ApiResponse(responseCode = "404", description = "Currency not found"),
            @ApiResponse(responseCode = "500", description = "Server error", content = @Content)})
    public ResponseEntity<?> getFilterData(@RequestBody CurrencyFilterDto request) {
        try {
            List<Currency> currencies = currencyService.getCurrencysFilter(request);
            Object result = businessManager.getFilterData(currencies, FilterDataEnum.CURRENCY);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                    message("Thành Công").build());
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
        }
    }
}
