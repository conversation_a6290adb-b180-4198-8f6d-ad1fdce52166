package vn.fis.eapprove.business.application.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.service.ShareUserManager;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.model.request.SharedUserRequest;
import vn.fis.spro.common.model.response.SharedUserResponse;

import java.util.List;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;


@RestController("ShareUserControllerV1")
@Slf4j
@Transactional
@RequestMapping(SERVICE_PATH + "/sharedUser")
public class ShareUserController {

    @Autowired
    ShareUserManager shareUserManager;
    @Autowired
    MessageSource messageSource;

    @Operation(summary = "getAllShareUser")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Search working time is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Search working time is fail", content = @Content)})
    @Tag(name = "get-all-share-user", description = "The user Service API with description tag annotation")
    @GetMapping("/getAllShareUser")
    public ResponseEntity<?> getAllShareUser() {
        try {
            List<SharedUser> sharedUsers = shareUserManager.getAll();
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(sharedUsers).build());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());

        }
    }

    @Operation(summary = "Create shared user")
    @PostMapping("/create-update")
    public ResponseEntity<?> createPermissionData(@RequestBody SharedUserRequest data) {
        log.info("Entering create shared user.......");
        try {
            shareUserManager.createSharedUsers(data);
            return ResponseHelper.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @Operation(summary = "Get data shared user")
    @PostMapping("/get-list-shared-user")
    public ResponseEntity<?> getListPermission(@RequestBody SharedUserRequest data) {
        log.info("Entering get data shared user.......");
        try {
            List<SharedUserResponse> listSharedUser = shareUserManager.getListSharedUser(data);
            return ResponseHelper.ok(listSharedUser);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }
}
