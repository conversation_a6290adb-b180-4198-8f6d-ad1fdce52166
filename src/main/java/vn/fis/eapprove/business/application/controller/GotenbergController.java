package vn.fis.eapprove.business.application.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import vn.fis.eapprove.business.tenant.manager.GotenbergManager;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

/**
 * Author: AnhVTN
 * Date: 15/03/2023
 */
@Slf4j
@RestController("GotenbergControllerV1")
@RequestMapping(SERVICE_PATH + "/gotenberg")
public class GotenbergController {

    private GotenbergManager gotenbergManager;

    public GotenbergController(GotenbergManager gotenbergManager) {
        this.gotenbergManager = gotenbergManager;
    }

    @PostMapping("/html-to-pdf")
    public ResponseEntity<?> htmlToPdf(@RequestPart("file") MultipartFile file) throws Exception {
      return gotenbergManager.htmlToPdf(file);
    }

}
