package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.repository.query.Param;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.constant.AppConstants;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcInstRepository;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTaskRepository;
import vn.fis.eapprove.business.domain.bpm.service.BpmRuManager;
import vn.fis.eapprove.business.domain.bpm.service.BpmTaskManager;
import vn.fis.eapprove.business.dto.BpmTaskDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.dto.TaskSettingConfigDto;
import vn.fis.eapprove.business.model.request.*;
import vn.fis.eapprove.business.model.response.CountTaskStatisticResponse;
import vn.fis.eapprove.business.model.response.TaskDetailResponse;
import vn.fis.eapprove.business.producer.ReportProducer;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.util.LogUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static vn.fis.eapprove.business.constant.Constant.MESSAGE_500;

@Slf4j
@RestController("BpmTaskControllerV1")
@RequestMapping("")
public class BpmTaskController {

    @Autowired
    private BpmTaskManager bpmTaskManager;
    @Autowired
    private ResponseUtils responseUtils;
    @Autowired
    private BpmRuManager bpmRuManager;
    @Autowired
    private CredentialHelper credentialHelper;
    @Autowired
    BusinessManager businessManager;
    @Autowired
    private BpmProcInstRepository bpmProcInstRepository;
    @Autowired
    private BpmTaskRepository bpmTaskRepository;
    @Autowired
    private ReportProducer reportProducer;

    @Value("${spring.kafka.consumer.topic.insert-report-by-group}")
    private String topicInsertReportByGroup;
    @Value("${spring.kafka.consumer.topic.insert-report-by-chart-node}")
    private String topicInsertReportByChartNode;

    @GetMapping("/loadActiveTask/getByType/{type}")
    public ResponseEntity<?> getActiveTaskByType(@PathVariable String type) {
        try {
            log.info("process=get-task, task_id={}", type);
            String username = credentialHelper.getJWTPayload().getUsername();
            List<BpmTaskDto> bpmTaskDtoList = bpmTaskManager.loadActiveTaskByType(type, username);
            if (bpmTaskDtoList != null) {
                return responseUtils.getResponseEntity(bpmTaskDtoList, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/task/myTask")
    public ResponseEntity<?> getActiveTaskByType(@RequestBody MyTaskRequest req) {
        try {
            String account = credentialHelper.getJWTPayload().getUsername();
            PageDto result = bpmTaskManager.myTask(req, account);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/task/{taskId}/{procInstId}/draft")
    public ResponseEntity<?> draftTask(@PathVariable String taskId,
                                       @PathVariable String procInstId,
                                       @RequestBody CompleteTaskDto completeTaskDto) {
        try {
            Boolean result = bpmTaskManager.draftTask(taskId, procInstId, completeTaskDto);
            if (result) {
                return responseUtils.getResponseEntity(true, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/task/getFilter")
    public ResponseEntity<?> getFilter(@RequestBody MyTaskRequest req) {
        try {
            String account = credentialHelper.getJWTPayload().getUsername();
            Map<String, Object> result = bpmTaskManager.getFilterMyTask(req, account);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/loadActiveTask/getDetail")
    public ResponseEntity<?> getTaskDetail(@RequestParam("procInstId") Long procInstId, @RequestParam("taskDefKey") String taskKey,
                                           @RequestParam(value = "user", required = false) String user,
                                           @RequestParam(value = "status", required = false) String status) {
        try {
            String account = credentialHelper.getJWTPayload().getUsername();
            TaskDetailResponse bpmTaskDtoList = bpmTaskManager.getTaskDetail(procInstId, taskKey, user, status, account);
            if (bpmTaskDtoList != null) {
                if (bpmTaskDtoList.getTaskId() != null) {
                    return responseUtils.getResponseEntity(bpmTaskDtoList, 1, "Success", HttpStatus.OK);
                }
                return responseUtils.getResponseEntity(null, -2, "Fail", HttpStatus.BAD_REQUEST);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/task/count2")
    public ResponseEntity<?> countTask(@RequestBody TaskCountRequest request) {
        try {
            Map<String, Long> result = bpmTaskManager.countTaskStatus(request.getType(), null, request.getFilterChangeAssignee(), request.getSearch());
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "SUCCESS", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/task/count")
    public ResponseEntity<?> countTask(@RequestParam("type") String type, @Valid @RequestBody MyTaskRequest request, @Param("filterChangeAssignee") Boolean filterChangeAssignee) {
        long startTime = LogUtils.logBegin(log);
        try {
            return ResponseHelper.ok(bpmTaskManager.countTaskStatus(type, request, filterChangeAssignee, request.getSearch()));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @GetMapping("/task/count/type")
    public ResponseEntity<?> countType() {
        try {
            String account = credentialHelper.getJWTPayload().getUsername();
            Map<String, Long> result = bpmTaskManager.countTypeByUser(account);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "SUCCESS", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Start phase
    @PostMapping("/task/start/{procInstId}/{taskId}")
    public ResponseEntity<?> startPhase(@RequestBody Map<String, Object> body, @PathVariable String procInstId, @PathVariable String taskId) {
        long startTime = LogUtils.logBegin(log);
        try {

            String account = credentialHelper.getJWTPayload().getUsername();
            Boolean isAutoClaim = (Boolean) body.get("isAutoClaim");
            Date date = bpmTaskManager.startPhase(taskId, procInstId, isAutoClaim, account);
            BpmProcInst currentTicket = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(procInstId);
            reportProducer.sendKafka(currentTicket.getTicketId(), topicInsertReportByGroup);
            List<BpmTask> bpmTasks = bpmTaskRepository.getBpmTaskByTaskProcInstId(currentTicket.getTicketProcInstId());
            for (BpmTask e : bpmTasks) {
                reportProducer.sendKafka(e.getTaskId(), topicInsertReportByChartNode);
            }
            return ResponseHelper.ok(date);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    // Claim task
    @PostMapping("/task/claim/{id}")
    public ResponseEntity<?> claimTask(@PathVariable String id) {
        try {
            String account = credentialHelper.getJWTPayload().getUsername();
            Boolean result = bpmTaskManager.claimTask(account, id);
            if (Boolean.TRUE.equals(result)) {
                reportProducer.sendKafka(id, topicInsertReportByChartNode);
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Change implementer
    @PostMapping("/task/changeImplementer")
    public ResponseEntity<?> changeImplementer(@RequestBody Map<String, Object> body) {
        try {
            String account = credentialHelper.getJWTPayload().getUsername();
            Map<String, String> result = bpmTaskManager.changeImplementer(body, account, 0);
            String ticketId = body.get("ticketId") != null ? body.get("ticketId").toString() : null;
            BpmProcInst currentTicket = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(ticketId);
            reportProducer.sendKafka(currentTicket.getTicketId(), topicInsertReportByGroup);
            List<BpmTask> bpmTasks = bpmTaskRepository.getBpmTaskByTaskProcInstId(ticketId);
            for (BpmTask e : bpmTasks) {
                reportProducer.sendKafka(e.getTaskId(), topicInsertReportByChartNode);
            }
            return ResponseHelper.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    // Get Task By Task Def Key
    @GetMapping("/loadTaskByDefKey/{procInstId}/{taskDefKey}")
    public ResponseEntity<List<BpmTaskDto>> getTaskInfoByTaskDefKey(@PathVariable String procInstId, @PathVariable String taskDefKey) {
        log.info("process=get-tasks");
        List<BpmTaskDto> bpmTaskDto = bpmTaskManager.getTaskInfoByTaskDefKey(procInstId, taskDefKey);
        return ResponseEntity.ok(bpmTaskDto);
    }

    //Load Active Task
    @GetMapping("/loadActiveTask/{procInstId}")
    public ResponseEntity<List<BpmTaskDto>> getTask(@PathVariable String procInstId) {
        log.info("process=get-task, task_id={}", procInstId);
        List<BpmTaskDto> bpmTaskDtoList = bpmTaskManager.loadActiveTask(procInstId);
        return ResponseEntity.ok(bpmTaskDtoList);
    }

    // Complete Task
    @PostMapping("/task/{taskId}/{procInstId}/complete")
    public ResponseEntity<?> completeTask(@PathVariable String taskId,
                                          @PathVariable String procInstId,
                                          @RequestBody CompleteTaskDto completeTaskDto,
                                          @RequestParam("signComplete") Boolean signComplete) {
        try {

            Map<String, Object> result = bpmTaskManager.completeTask(taskId, procInstId, completeTaskDto, signComplete);
            BpmProcInst currentTicket = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(procInstId);
            reportProducer.sendKafka(currentTicket.getTicketId(), topicInsertReportByGroup);
            List<BpmTask> bpmTasks = bpmTaskRepository.getBpmTaskByTaskProcInstId(currentTicket.getTicketProcInstId());
            for (BpmTask e : bpmTasks) {
                reportProducer.sendKafka(e.getTaskId(), topicInsertReportByChartNode);
            }
            return ResponseHelper.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @PostMapping("/task/search")
    public ResponseEntity<?> search(@RequestBody BpmTaskDto criteria) {
        log.info("process=search");
        return ResponseEntity.ok(bpmTaskManager.search(criteria));
    }

    // load list of pre sequel task
    @GetMapping("/task/get/previous")
    public ResponseEntity<?> getPreviousTask(@RequestParam("taskDefKey") String taskDefKey, @RequestParam("procDefId") String procDefId) {
        try {
            List<Map<String, Object>> result = bpmRuManager.preSequelTask(taskDefKey, procDefId);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/task/get/assignee")
    public ResponseEntity<?> getAssignee(@RequestParam("procInstId") String procInstId, @RequestParam("taskKey") List<String> procDefId) {
        try {
            List<Map<String, Object>> result = bpmTaskManager.getAssigneeByTicket(procInstId, procDefId);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/task/totalTasksStatistic")
    public ResponseEntity<?> totalTasksStatistic(@RequestBody TotalTasksStatisticRequest req, @RequestHeader("chart") Long chartId) {
        try {
            CountTaskStatisticResponse response = bpmTaskManager.countTaskCallCenter(req.getLsUserAccount(), chartId);
            if (response == null) {
                return responseUtils.getResponseEntity(null, -1, MESSAGE_500, HttpStatus.INTERNAL_SERVER_ERROR);
            }
            return responseUtils.getResponseEntity(response, 1, "Success", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, MESSAGE_500, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/task/taskListDetail")
    public ResponseEntity<?> taskListDetail(@RequestBody CountTaskListDetailRequest req, @RequestHeader("chart") Long chartId) {
        try {
            PageDto dto = bpmTaskManager.getCountTaskListDetail(req, chartId);
            if (dto == null) {
                return responseUtils.getResponseEntity(null, -1, MESSAGE_500, HttpStatus.INTERNAL_SERVER_ERROR);
            }
            return responseUtils.getResponseEntity(dto, 1, "Success", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, MESSAGE_500, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/task/getByAssigneeAndStatus")
    public ResponseEntity<?> getByAssigneeAndStatus(@RequestBody TaskCallCenterRequest req, @RequestHeader("chart") Long chartId) {
        try {
            PageDto response = bpmTaskManager.getByAssigneeAndStatus(req, chartId);
            if (response == null) {
                return responseUtils.getResponseEntity(null, -1, MESSAGE_500, HttpStatus.INTERNAL_SERVER_ERROR);
            }
            return responseUtils.getResponseEntity(response, 1, "Thành công", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, MESSAGE_500, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/task/getSettingConfig")
    public ResponseEntity<?> getSettingConfig() {
        try {
            TaskSettingConfigDto dto = bpmTaskManager.getSettingConfig();
            if (dto == null) {
                return responseUtils.getResponseEntity(null, -1, MESSAGE_500, HttpStatus.INTERNAL_SERVER_ERROR);
            }
            return responseUtils.getResponseEntity(dto, 1, "Thành công", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, MESSAGE_500, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/task/saveSettingConfig")
    public ResponseEntity<?> saveSettingConfig(@Valid @RequestBody TaskSettingConfigDto dto, BindingResult bindingResult) {
        try {
            if (bindingResult.hasErrors()) {
                Map<String, String> errors = new HashMap<>();

                bindingResult.getFieldErrors().forEach(
                        error -> errors.put(error.getField(), error.getDefaultMessage())
                );

                String errorMsg = "";

                for (String key : errors.keySet()) {
                    errorMsg += " error at : " + key + " , reason: " + errors.get(key) + "\n ";
                }
                return responseUtils.getResponseEntity(false, -1, errorMsg, HttpStatus.INTERNAL_SERVER_ERROR);
            }
            Boolean save = bpmTaskManager.saveSettingConfig(dto);
            if (!save) {
                return responseUtils.getResponseEntity(false, -1, "save fail !", HttpStatus.INTERNAL_SERVER_ERROR);
            }
            return responseUtils.getResponseEntity(true, 1, "Lưu thành công", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, MESSAGE_500, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/task/edit/{procInstId}/{taskDefKey}")
    public ResponseEntity<?> editTask(@PathVariable String procInstId,
                                      @PathVariable String taskDefKey,
                                      @RequestBody CompleteTaskDto completeTaskDto) {
        try {
            Integer result = bpmTaskManager.editTask(taskDefKey, procInstId, completeTaskDto);
            if (result == 1) {
                BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(procInstId);
                reportProducer.sendKafka(bpmProcInst.getTicketId(), topicInsertReportByGroup);
                List<BpmTask> bpmTasks = bpmTaskRepository.getBpmTaskByTaskProcInstId(procInstId);
                for (BpmTask e : bpmTasks) {
                    reportProducer.sendKafka(e.getTaskId(), topicInsertReportByChartNode);
                }
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("task/expireTicket")
    public ResponseEntity<?> expireTicket() {
        log.info("Entering auto expire ticket");
        long startTime = LogUtils.logBegin(log);
        try {
            bpmTaskManager.expired();
            return ResponseHelper.ok();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @Operation(summary = "Update task recall")
    @PostMapping("task/{id}/recall")
    public ResponseEntity<?> recall(@PathVariable("id") Long id,
                                    @RequestParam(value = "status", defaultValue = AppConstants.RecallStatus.AGREE, required = false) String status,
                                    @Valid @RequestBody TaskRecallRequest request) {
        long startTime = LogUtils.logBegin(log);
        try {
            Map<String, Object> result = bpmTaskManager.recall(id, status, request);
            if (result != null) {
                if (result.get("isSuccess").equals(Boolean.TRUE)) {
                    BpmTask task = bpmTaskRepository.findById(id).orElse(null);
                    BpmProcInst bpmProcInst = bpmProcInstRepository.findBpmProcInstByTicketProcInstId(task.getTaskProcInstId());
                    reportProducer.sendKafka(bpmProcInst.getTicketId(), topicInsertReportByGroup);
                    reportProducer.sendKafka(task.getTaskId(), topicInsertReportByChartNode);
                }
                return ResponseHelper.ok(result);
            }
            return ResponseHelper.fail();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @Operation(summary = "Reminder task processing")
    @GetMapping("task/reminderTaskProcessing")
    public ResponseEntity<?> reminderTaskProcessing(@RequestParam("delayTime") Long delayTime) {
        long startTime = LogUtils.logBegin(log);
        try {
            Map<String, Object> response = bpmTaskManager.reminderTaskProcessing(delayTime);
            return ResponseHelper.ok(response, "Thành công");
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ResponseHelper.error(ex);
        } finally {
            LogUtils.logEnd(log, startTime);
        }
    }

    @PostMapping("/task/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody MyTaskRequest req) {
        try {
            String account = credentialHelper.getJWTPayload().getUsername();
            List<Map<String, Object>> result = bpmTaskManager.myTaskFilter(req, account);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(businessManager.getFilterData(result, FilterDataEnum.BPM_PROCINST)).
                    message("Thành Công").build());
        } catch (Exception e) {
            log.error("Error searchChart: {}", e.getMessage());
            return ResponseHelper.fail();
        }
    }

    @GetMapping("/task/getTaskInfoByProcInstId")
    public ResponseEntity<?> getTaskInfoByProcInstId(@RequestParam("procInstId") String procInstId) {
        return ResponseHelper.ok(bpmTaskManager.getTaskInfoByProcInstId(procInstId));
    }

}
