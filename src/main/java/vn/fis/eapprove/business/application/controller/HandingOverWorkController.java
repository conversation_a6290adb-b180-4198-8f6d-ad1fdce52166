package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.fis.eapprove.business.domain.report.service.ReportByGroupService;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.HandingOverWorkRequest;
import vn.fis.eapprove.business.model.request.HandingOverWorkSearchRequest;
import vn.fis.eapprove.business.tenant.manager.HandingOverWorkManager;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.Locale;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("HandingOverWorkControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/handingOverWork")
public class HandingOverWorkController {
    @Autowired
    private MessageSource messageSource;

    @Autowired
    private HandingOverWorkManager handingOverWorkManager;

    @Autowired
    private ReportByGroupService reportByGroupService;

    @Operation(summary = "processing handing over work")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", content = @Content)})
    @Tag(name = "handingOverWork-controller", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/process")
    public ResponseEntity<?> handingOver(@RequestBody HandingOverWorkRequest handingOverWorkRequests) {
        try {
            Boolean status = handingOverWorkManager.process(handingOverWorkRequests);
            if (status) {
//                for (Long e : handingOverWorkRequests.getTicketId()) {
//                    reportByGroupService.createReportByGroup(e);
//                }
                return ResponseEntity.ok().body(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).message("Bàn giao thành công")
                        .build());
            }
            return ResponseEntity.ok().body(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).message("FAIL")
                    .build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "processing handing over work")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", content = @Content)})
    @Tag(name = "handingOverWork-controller", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/search")
    public ResponseEntity<?> handingOver(@RequestBody HandingOverWorkSearchRequest handingOverWorkSearchRequest) {
        try {
            PageDto status = handingOverWorkManager.search(handingOverWorkSearchRequest);
            if (!ValidationUtils.isNullOrEmpty(status)) {
                return ResponseEntity.ok().body(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status)
                        .build());
            }
            return ResponseEntity.ok().body(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status)
                    .build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }
}
