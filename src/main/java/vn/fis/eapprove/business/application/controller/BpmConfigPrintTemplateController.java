package vn.fis.eapprove.business.application.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.bpm.entity.BpmConfigPrintTemplate;
import vn.fis.eapprove.business.domain.bpm.service.BpmConfigPrintTemplateManager;
import vn.fis.eapprove.business.dto.BaseDto;
import vn.fis.eapprove.business.dto.BpmPrintTemplateDto;
import vn.fis.eapprove.business.dto.PageDto;

import vn.fis.eapprove.business.utils.ResponseUtils;

import java.util.List;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("BpmConfigPrintTemplateControllerV1")
@CrossOrigin("*")
@RequestMapping(SERVICE_PATH + "/print-template")
public class BpmConfigPrintTemplateController {
    @Autowired
    private ResponseUtils responseUtils;

    @Autowired
    private BpmConfigPrintTemplateManager bpmPrintTemplateManager;

    @PostMapping
    public ResponseEntity<?> createTemplate(@RequestBody BpmPrintTemplateDto req) {
        try {
            boolean isSuccess = bpmPrintTemplateManager.save(req);
            if (isSuccess == true) {
                return responseUtils.getResponseEntity(true, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(false, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/search")
    public ResponseEntity<?> searchTemplate(@RequestBody BaseDto req) {
        try {
            PageDto page = bpmPrintTemplateManager.getPagingPrintTemplate(req);
            if (page != null) {
                return responseUtils.getResponseEntity(page, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/checkName")
    public ResponseEntity<?> checkName(@RequestParam("name") String name) {
        try {
            boolean hasName = bpmPrintTemplateManager.checkExistName(name);
            return responseUtils.getResponseEntity(hasName, 1, "Success", HttpStatus.OK);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getById(@PathVariable Long id) {
        try {
            BpmConfigPrintTemplate bpmPrintTemplate = bpmPrintTemplateManager.getPrintTemplateById(id);
            if (bpmPrintTemplate != null) {
                return responseUtils.getResponseEntity(bpmPrintTemplate, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, 1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping
    public ResponseEntity<?> getAll() {
        try {
            List<BpmConfigPrintTemplate> bpmPrintTemplate = bpmPrintTemplateManager.getAllPrintTemplate();
            return responseUtils.getResponseEntity(bpmPrintTemplate, 1, "Success", HttpStatus.OK);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping
    public ResponseEntity<?> updateTemplate(@RequestBody BpmPrintTemplateDto req) {
        try {
            boolean isSuccess = bpmPrintTemplateManager.updatePrintTemplate(req);
            if (isSuccess == true) {
                return responseUtils.getResponseEntity(true, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(false, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteById(@PathVariable Long id) {
        try {
            boolean hasTemplate = bpmPrintTemplateManager.deletePrintTemplate(id);
            return responseUtils.getResponseEntity(hasTemplate, 1, "Success", HttpStatus.OK);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


}
