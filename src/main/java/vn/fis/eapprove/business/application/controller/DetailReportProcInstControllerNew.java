package vn.fis.eapprove.business.application.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.fis.eapprove.business.dto.filter.ReportProcInstFilter;
import vn.fis.eapprove.business.dto.filter.RequestDetailTaskReturned;
import vn.fis.eapprove.business.dto.report.DetailTaskDto;
import vn.fis.eapprove.business.dto.report.DetailTaskReturnedDto;
import vn.fis.eapprove.business.model.response.BaseResponse;
import vn.fis.eapprove.business.model.response.DetailReportByGroupResponse;
import vn.fis.eapprove.business.tenant.manager.DetailReportServiceNew;

import java.util.List;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("DetailReportProcInstControllerNewV1")
@RequiredArgsConstructor
@RequestMapping(SERVICE_PATH + "/detail-report-procInst")
public class DetailReportProcInstControllerNew extends BaseController {

    private final DetailReportServiceNew service;


    @PostMapping("/by-user")
    public ResponseEntity<BaseResponse<DetailReportByGroupResponse>> getAllDetailReportByUser(@RequestBody ReportProcInstFilter filter) {

        DetailReportByGroupResponse response = service.getDetailReportByUser(filter);

        return success(response);
    }

    @PostMapping("/by-chart-node")
    public ResponseEntity<BaseResponse<DetailReportByGroupResponse>> getAllDetailReportByChartNode(@RequestBody ReportProcInstFilter filter) {

        DetailReportByGroupResponse response = service.getDetailReportByChartNode(filter);

        return success(response);
    }

    @PostMapping("/by-group")
    public ResponseEntity<BaseResponse<DetailReportByGroupResponse>> getAllDetailReportByGroup(@RequestBody ReportProcInstFilter filter) {

        DetailReportByGroupResponse response = service.getDetailReportByGroup(filter);

        return success(response);
    }

    @PostMapping("/detail-task")
    public ResponseEntity<BaseResponse<List<DetailTaskDto>>> getDetailTaskByProcInstId(@RequestBody ReportProcInstFilter filter) {

        List<DetailTaskDto> response = service.getDetailTaskByProcInstId(filter);

        return success(response);
    }

    @PostMapping("/detail-task-returned")
    public ResponseEntity<BaseResponse<List<DetailTaskReturnedDto>>> getDetailTaskReturnedByProcInstId(@RequestBody RequestDetailTaskReturned request) {

        List<DetailTaskReturnedDto> response = service.getDetailTaskReturned(request);

        return success(response);
    }

}
