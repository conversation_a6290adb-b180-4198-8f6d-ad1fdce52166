package vn.fis.eapprove.business.application.controller;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.report.service.ReportByChartNodeService;
import vn.fis.eapprove.business.dto.ReportByChartNodeDto;
import vn.fis.eapprove.business.dto.UserInfoDto;
import vn.fis.eapprove.business.exception.rest.response.BaseResponse;
import vn.fis.eapprove.business.producer.ReportProducer;

import java.util.List;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("ReportByChartNodeControllerV1")
@RequiredArgsConstructor
@Slf4j
@RequestMapping(SERVICE_PATH + "/report-by-chart-node")
public class ReportByChartNodeController {

    private final ReportByChartNodeService reportByChartNodeService;
    private final ReportProducer reportProducer;

    @Value("${spring.kafka.consumer.topic.insert-report-by-chart-node}")
    private String topicInsertReportByChartNode;

    @PostMapping("/create")
    public BaseResponse<String> create(@RequestBody ReportByChartNodeDto reportByChartNodeDto) {
        log.info("Create report by chart node with task Id: {}", reportByChartNodeDto.getTaskId());
        reportProducer.sendKafka(reportByChartNodeDto.getTaskId(), topicInsertReportByChartNode);
        return BaseResponse.ofSucceeded("ok");
    }

    @PostMapping("/sync")
    public BaseResponse<String> sync(@RequestParam(required = false) String fromDate, @RequestParam(required = false) String toDate) {
        reportByChartNodeService.syncReportByChartNode(fromDate, toDate);
        return BaseResponse.ofSucceeded("Sync report by group successfully");
    }

    @PostMapping("/list-user-filter")
    public BaseResponse<List<String>> getListUserFilter(@RequestBody UserInfoDto userInfoDto) {
        return BaseResponse.ofSucceeded(reportByChartNodeService.getListUserFilter(userInfoDto.getUsernames()));
    }
}
