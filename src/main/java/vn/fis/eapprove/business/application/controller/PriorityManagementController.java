package vn.fis.eapprove.business.application.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.priority.service.PriorityManager;
import vn.fis.eapprove.business.dto.PageSearchDto;
import vn.fis.eapprove.business.dto.SearchPriorityDto;
import vn.fis.eapprove.business.model.request.PriorityRequest;
import vn.fis.eapprove.business.model.response.PriorityResponse;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.model.ResponseDto;

import java.util.List;
import java.util.Locale;

import static vn.fis.eapprove.business.constant.Constant.*;

@RestController("PriorityManagementControllerV1")
@Slf4j
@Transactional
@RequestMapping(SERVICE_PATH + "/priority-management")
public class PriorityManagementController {
    @Autowired
    PriorityManager priorityManager;

    @Autowired
    MessageSource messageSource;

    @Autowired
    ResponseUtils responseUtils;

    @Autowired
    BusinessManager businessManager;

    @Operation(summary = "Get priority in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get priority  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get priority is fail", content = @Content)})
    @Tag(name = "priortiy management", description = "The Business Process Service API with description tag annotation")
    @GetMapping("/getPriority")
    public ResponseEntity<?> getListPriority(@RequestParam("search") String search) {
        log.info("Entering getLocation()...");
        try {
            List<PriorityResponse> priorityResponses = priorityManager.getPriority(search);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(priorityResponses).build());

        } catch (Exception e) {
            log.error("Error getChartInfoRole: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @Operation(summary = "search priority in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get priority  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get priority is fail", content = @Content)})
    @Tag(name = "priortiy management", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/search")
    public ResponseEntity<?> searchPriority(@RequestBody SearchPriorityDto req) {
        try {
            PageSearchDto page = priorityManager.search(req);
//            if (!CollectionUtils.isEmpty(page.getContent())) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(page).
                        message(messageSource.getMessage("message.priority-management.searchTemplate.success", null, Locale.getDefault())).build());
//            }
//            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
//                    .message(messageSource.getMessage("message.priority-management.searchTemplate.fail", null, Locale.getDefault())).data(page.getContent()).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }


    @Operation(summary = "create priority in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get priority  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get priority is fail", content = @Content)})
    @Tag(name = "priortiy management", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/createPriority")
    public ResponseEntity<?> createPriority(@RequestBody PriorityRequest priorityRequest) {
        try {
            String status = priorityManager.createPriority(priorityRequest);
            if (status == MESSAGE_SUCCESS) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).
                        message(messageSource.getMessage("message.priority-management.createPriority.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.priority-management.createPriority.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "Get all user in all chart priority in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get priority  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get priority is fail", content = @Content)})
    @Tag(name = "priortiy management", description = "The Business Process Service API with description tag annotation")
    @GetMapping("/getAlluserInfo")
    public ResponseEntity<?> getAlluserInfo() {
        return ResponseEntity.ok(priorityManager.getAlluserInfo());
    }

    @Operation(summary = "check name priority in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get priority  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get priority is fail", content = @Content)})
    @Tag(name = "priortiy management", description = "The Business Process Service API with description tag annotation")
    @GetMapping("/checkExistName")
    public ResponseEntity<?> checkExistName(@RequestParam("priorityName") String priorityName,
                                            @RequestParam(value = "id", required = false) Long id) {
        try {
            Boolean checkPriorityName = priorityManager.checkNameExits(priorityName, id);
            if (checkPriorityName == true) {
                return responseUtils.getResponseEntity(true, 1, "Độ ưu tiên đã tồn tại trong hệ thống", HttpStatus.OK);
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(checkPriorityName).
                    message(messageSource.getMessage("message.priority-management.createPriority.success", null, Locale.getDefault())).build());
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "update priority in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get priority  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get priority is fail", content = @Content)})
    @Tag(name = "priortiy management", description = "The Business Process Service API with description tag annotation")
    @PutMapping("/updateGroupSystem")
    public ResponseEntity<?> updatePriority(@RequestBody PriorityRequest priorityRequest) {
        try {
            String status = priorityManager.updatePriority(priorityRequest);
            if (status == MESSAGE_SUCCESS) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).
                        message(messageSource.getMessage("message.priority-management.updatePriority.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.priority-management.updatePriority.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @Operation(summary = "delete priority in organization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get priority  is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get priority is fail", content = @Content)})
    @Tag(name = "priortiy management", description = "The Business Process Service API with description tag annotation")
    @DeleteMapping("/deletePriority")
    public ResponseEntity<?> deletePriority(@RequestParam List<Long> listPriorityId) {
        try {
            Integer status = priorityManager.deletePriority(listPriorityId);
            if (status.equals(SUCCESS)) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).
                        message(messageSource.getMessage("message.priority-management.deletePriority.success", null, Locale.getDefault())).build());
            }
            if (status.equals(FAILED)) {
                return ResponseEntity.ok().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                        .message(messageSource.getMessage("message.priority-management.deletePriority.fail", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("message.priority-management.deletePriority.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @GetMapping("/getAllPriotity")
    public ResponseEntity<?> getAllPriotity() {
        return ResponseEntity.ok(priorityManager.getAllPriotity());
    }

    @GetMapping("/findByStatus")
    public ResponseEntity<?> findByStatus(@RequestParam(value = "status", required = false) Integer status) {
        return ResponseEntity.ok(priorityManager.findByStatus(status));
    }


    @Operation(summary = "Get filter data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Search and Get all signatures is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Search and Get all signatures is fail", content = @Content)})
    @Tag(name = "priortiy management", description = "The Business Process Service API with description tag annotation")
    @PostMapping("/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody SearchPriorityDto req) {
        try {
            List<PriorityResponse> page = priorityManager.searchFilter(req);
            try {
                Object result = businessManager.getFilterData(page, FilterDataEnum.PRIORITY_MANAGEMENT);
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                        message("Thành Công").build());
            } catch (Exception e) {
                return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
            }
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @PostMapping("/activePriority")
    public ResponseEntity<?> activePriority(@RequestParam("id") List<Long> id, @RequestParam("isActive") Boolean isActive) {
//        priorityManager.activePriority(name, isActive);
        return ResponseHelper.ok(priorityManager.activePriority(id, isActive));
    }
}
