package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplateHtml;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrint;
import vn.fis.eapprove.business.domain.bpm.service.BpmTemplatePrintManager;
import vn.fis.eapprove.business.dto.*;
import vn.fis.eapprove.business.model.request.BpmPrintPhaseRequest;
import vn.fis.eapprove.business.model.response.BpmPrintSignResponse;
import vn.fis.eapprove.business.model.response.BpmTemplatePrintResponse;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.helper.ResponseHelper;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.util.ValidationUtils;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import static vn.fis.eapprove.business.constant.Constant.*;

@Slf4j
@RestController("BpmTemplatePrintControllerV1")
@CrossOrigin("*")
@RequestMapping(SERVICE_PATH + "/print")
public class BpmTemplatePrintController {
    @Autowired
    SimpMessagingTemplate template;
    @Autowired
    private ResponseUtils responseUtils;
    @Autowired
    private BpmTemplatePrintManager bpmPrintPhaseManager;
    @Autowired
    private CredentialHelper credentialHelper;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    BusinessManager businessManager;

    @PostMapping
    public ResponseEntity<?> createPrint(@RequestBody BpmPrintPhaseRequest req) {
        try {
            int status = bpmPrintPhaseManager.save(req);
            switch (status) {
                case SUCCESS:
                    return responseUtils.getResponseEntity(false, 1, "Success", HttpStatus.OK);
                case FAILED:
                    return responseUtils.getResponseEntity(false, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
                case NAME_EXISTED:
                    return responseUtils.getResponseEntity(false, -1, "Tên đã tồn tại", HttpStatus.OK);
                default:
                    return responseUtils.getResponseEntity(false, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping(value = "/create")
    public ResponseEntity<?> createPrintbpm(@RequestBody CreateBpmTemplatePrintRequest bpmTemplatePrintRequest) {
        try {
            String status = bpmPrintPhaseManager.create1(bpmTemplatePrintRequest);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).message(status).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @PostMapping(value = "/loadFormFile", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE, MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<?> loadFormFile(@RequestParam("file") MultipartFile[] file) {
        try {
            ContentDto status = bpmPrintPhaseManager.getFormFile1(file);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @PostMapping("/review")
    public ResponseEntity<?> review(@RequestBody PreviewRequest previewRequest) {
        try {
            List<ReviewDto> status = bpmPrintPhaseManager.review(previewRequest);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @GetMapping(value = "/getConfigHtml")
    public ResponseEntity<?> getConfigHtml(@RequestParam("ids") List<Long> ids) {
        ContentDto result = bpmPrintPhaseManager.getConfigHtml(ids);
        return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).build());
    }

    @PostMapping("/reviewHtml")
    public ResponseEntity<?> reviewHtml(@RequestBody PreviewRequest previewRequest) {
        try {
            List<ReviewDto> result = bpmPrintPhaseManager.reviewHtml(previewRequest);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @GetMapping(value = "/getAllTemplateHtml")
    public ResponseEntity<?> getAllTemplateHtml() {
        List<BpmTemplatePrintResponse> result = bpmPrintPhaseManager.getAllTemplateHtml();
        return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).build());
    }

    @GetMapping("/show/{id}")
    public ResponseEntity<?> show(@PathVariable Long id) {
        try {
            List<ReviewDto> status = bpmPrintPhaseManager.getObject(id);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @GetMapping("/showHtml/{id}")
    public ResponseEntity<?> showHtml(@PathVariable Long id) {
        try {
            List<ReviewDto> status = bpmPrintPhaseManager.showHtml(id);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @PostMapping(value = "/edit")
    public ResponseEntity<?> edit(@RequestBody CreateBpmTemplatePrintRequest bpmTemplatePrintRequest) {
        try {
            String status = bpmPrintPhaseManager.updateBpm(bpmTemplatePrintRequest);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).message(status).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @GetMapping("/load/{id}")
    public ResponseEntity<?> loadData(@PathVariable Long id) {
        try {
            DataTemplatePrint status = bpmPrintPhaseManager.showInformation(id);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @GetMapping("/loadUsed")
    public ResponseEntity<?> loadProcessUsed() {
        try {
            Set<LoadUseDto> status = bpmPrintPhaseManager.processUsed();
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @PostMapping("/search")
    public ResponseEntity<?> searchTemplate(@RequestBody SearchPrintDto req) {
        try {
            PageDto status = bpmPrintPhaseManager.search(req);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @GetMapping("/config")
    public ResponseEntity<?> getConfig() {
        try {
            List<BpmTemplatePrintConfigDto> status = bpmPrintPhaseManager.getConfig();
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @PostMapping("/filter-search")
    public ResponseEntity<?> searchFilter(@RequestBody SearchFilter filter) {
        try {
            PageDto status = bpmPrintPhaseManager.searchFilter(filter);
            if (!ValidationUtils.isNullOrEmpty(status)) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).message(messageSource.getMessage("error.templatePrint.valid.dataValid", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @GetMapping("/checkNameAll")
    public ResponseEntity<?> checkName(@RequestParam("name") String name, @RequestParam(value = "id", required = false) Long id) {
        try {
            Boolean status = bpmPrintPhaseManager.checkName(name, id);
            if (status) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).message(messageSource.getMessage("message.template.checkName", null, Locale.getDefault())).build());
            }
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(status).message(messageSource.getMessage("error.templatePrint.valid.nameUpdateDuplicate", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @GetMapping("/checkName")
    public ResponseEntity<?> checkName(@RequestParam("name") String name) {
        try {
            boolean hasName = bpmPrintPhaseManager.checkExistName(name);
            return responseUtils.getResponseEntity(hasName, 1, "Success", HttpStatus.OK);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @GetMapping("/{id}")
    public ResponseEntity<?> getById(@PathVariable Long id) {
        try {
            BpmTemplatePrint bpmPrintTemplate = bpmPrintPhaseManager.getById(id);
            if (bpmPrintTemplate != null) {
                return responseUtils.getResponseEntity(bpmPrintTemplate, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, 1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/sign/{id}/{procInstId}")
    public ResponseEntity<?> getBpmPrintSignById(@PathVariable Long id, @PathVariable String procInstId) {
        try {
            String username = credentialHelper.getJWTPayload().getUsername();
            BpmPrintSignResponse bpmPrintTemplate = bpmPrintPhaseManager.getBpmPrintSignById(id, procInstId, username);
            if (bpmPrintTemplate != null) {
                return responseUtils.getResponseEntity(bpmPrintTemplate, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, 1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/combine-sign/{id}/{procInstId}")
    public ResponseEntity<?> combinePdf(@PathVariable Long id, @PathVariable String procInstId) {
        try {
            Map<String, Object> response = bpmPrintPhaseManager.combineSignedFile(id, procInstId);
            if (response != null) {
                return responseUtils.getResponseEntity(response, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, 1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping
    public ResponseEntity<?> updateTemplate(@RequestBody BpmPrintPhaseRequest req) {
        try {
            int status = bpmPrintPhaseManager.update(req);
            switch (status) {
                case SUCCESS:
                    return responseUtils.getResponseEntity(false, 1, "Success", HttpStatus.OK);
                case FAILED:
                    return responseUtils.getResponseEntity(false, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
                case NAME_EXISTED:
                    return responseUtils.getResponseEntity(false, -1, "Tên đã tồn tại", HttpStatus.OK);
                default:
                    return responseUtils.getResponseEntity(false, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/delete-template-print")
    public ResponseEntity<?> deleteById(@RequestBody List<Long> id) {
        try {
            return bpmPrintPhaseManager.deleteById(id);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code).message(e.getMessage()).build());
        }
    }

    @PostMapping("/send")
    public ResponseEntity<?> sendMessage(@RequestBody TextMessageDTO textMessageDTO) {
        template.convertAndSend("/topic/message", textMessageDTO);
        return responseUtils.getResponseEntity(true, 1, "Success", HttpStatus.OK);
    }

    //    @MessageMapping("/hello")
    @SendTo("/topic/message")
    public TextMessageDTO send(@Payload TextMessageDTO textMessageDTO) {
        System.out.println("connect");
        return textMessageDTO;
    }

    @Operation(summary = "Get All Filter Data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "apiLog", description = "Get all filterData")
    @PostMapping("/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody SearchPrintDto searchPrintDto) {
        try {
            Object result = businessManager.getFilterData(bpmPrintPhaseManager.searchFilter(searchPrintDto), FilterDataEnum.BPM_TEMPLATE_PRINT);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                    message(messageSource.getMessage("message.searchApiLog.searchApiAction.success", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @PostMapping(value = "/createHtml")
    public ResponseEntity<?> createPrintHtml(@RequestBody BpmPrintPhaseRequest req) {
        try {
            int status = bpmPrintPhaseManager.createPrintHtml(req);
            return switch (status) {
                case SUCCESS -> responseUtils.getResponseEntity(false, 1, "Success", HttpStatus.OK);
                case NAME_EXISTED -> responseUtils.getResponseEntity(false, -1, "Tên đã tồn tại", HttpStatus.OK);
                default -> responseUtils.getResponseEntity(false, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
            };
        } catch (Exception e) {
            log.error("Error when create print html", e);
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping(value = "/getHtmlTemplateDetail/{id}")
    public ResponseEntity<?> getHtmlTemplateDetail(@PathVariable Long id) {
        BpmTemplateHtml response = bpmPrintPhaseManager.getDetailTemplateHtml(id);
        if (response != null) {
            return ResponseHelper.ok(response);
        }
        return ResponseHelper.fail();
    }
}
