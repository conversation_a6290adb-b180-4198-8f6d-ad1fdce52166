package vn.fis.eapprove.business.application.controller;

import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.template.service.TemplateHistoryManager;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.FilterTemplateHistoryRequest;
import vn.fis.eapprove.business.utils.ResponseUtils;

import static vn.fis.eapprove.business.constant.Constant.MESSAGE_500;
import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@Slf4j
@RestController("TemplateHistoryControllerV1")
@RequestMapping(SERVICE_PATH + "/template-history")
public class TemplateHistoryController {


    @Autowired
    TemplateHistoryManager templateHistoryManager;

    @Autowired
    ResponseUtils responseUtils;

    @Autowired
    CredentialHelper credentialHelper;

    @PostMapping("/getAll")
    public ResponseEntity<?> getAll(@RequestBody FilterTemplateHistoryRequest filterTemplateHistoryRequest) {
        try {
//            Page<TemplateHistoryResponse> listWorkingTime = templateHistoryManager.getAll(filterTemplateHistoryRequest);
            PageDto listWorkingTime = templateHistoryManager.getAll(filterTemplateHistoryRequest);
            return responseUtils.getResponseEntity(listWorkingTime, 1, "Success!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, MESSAGE_500, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/restoreTemplate")
    public ResponseEntity<?> restoreTemplate(@RequestParam Long id) {
        return templateHistoryManager.restore(id);
    }

    @PostMapping("/getTemplateById")
    public ResponseEntity<?> getTemplateById(@RequestParam Long id) {
        try {
            String result = templateHistoryManager.getTemplateById(id);
            return responseUtils.getResponseEntity(result, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
