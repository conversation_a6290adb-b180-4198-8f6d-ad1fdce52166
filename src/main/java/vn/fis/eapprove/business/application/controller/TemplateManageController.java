package vn.fis.eapprove.business.application.controller;

import vn.fis.eapprove.security.CredentialHelper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcInst;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcInstManager;
import vn.fis.eapprove.business.domain.bpm.service.BpmProcdefManager;
import vn.fis.eapprove.business.domain.bpm.service.BpmTaskManager;
import vn.fis.eapprove.business.domain.template.entity.TemplateManage;
import vn.fis.eapprove.business.domain.template.repository.TemplateRepository;
import vn.fis.eapprove.business.domain.template.service.TemplateManager;
import vn.fis.eapprove.business.dto.ApprovalTaskDto;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.FillterTempateRequest;
import vn.fis.eapprove.business.model.request.SearchTemplateHistory;
import vn.fis.eapprove.business.model.request.TemplateRequest;
import vn.fis.eapprove.business.model.response.TemplateResponse;
import vn.fis.eapprove.business.tenant.manager.*;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.util.ObjectUtils;

import java.util.*;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@Slf4j
@RestController("TemplateManageControllerV1")
@RequestMapping(SERVICE_PATH + "/template-manage")
public class TemplateManageController {

    @Autowired
    CredentialHelper credentialHelper;

    @Autowired
    TemplateManager templateManager;

    @Autowired
    ResponseUtils responseUtils;

    @Autowired
    MessageSource messageSource;

    @Autowired
    private BpmProcdefManager bpmProcdefManager;

    @Autowired
    private BpmProcInstManager bpmProcInstManager;

    @Autowired
    private BpmTaskManager bpmTaskManager;

    @Autowired
    private TemplateRepository templateRepository;


    @Autowired
    BusinessManager businessManager;

/*    @PostMapping("/createTemplate")
    public ResponseEntity<?> createTemplate(@RequestBody TemplateRequest templateRequest) {
        try {
            Boolean check =  templateManager.checkNameExits(templateRequest.getTemplateName(),null);
            if(check){
                return responseUtils.getResponseEntity(true,1,"Tên biểu mẫu đã tồn tại trong hệ thống!", HttpStatus.OK);
            }
            String email = credentialHelper.getJWTPayload().getEmail();
            Boolean result = templateManager.createTemplate(templateRequest,email);
            if (result) {
                return responseUtils.getResponseEntity(result, 1, "Tạo biểu mẫu thành công", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }*/

    @PostMapping("/createNewTemplate")
    public ResponseEntity<?> createTemplateV2(@RequestBody TemplateRequest templateRequest) {
        try {
            Boolean check = templateManager.checkNameExits(templateRequest.getTemplateName(), null);
            if (check) {
                return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                        .message(messageSource.getMessage("error.template.create.checkname", null, Locale.getDefault())).build());
            }
            long result = templateManager.createTemplate(templateRequest);
            if (result != 0) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                        message(messageSource.getMessage("message.template.create.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("error.template.create.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

    @PostMapping("/deleteTemplate")
    public ResponseEntity<?> deleteTemplate(@RequestBody TemplateRequest templateRequest) {
        try {
            Boolean check = templateManager.CheckProdefID(templateRequest.getId());
            if (check) {
                return responseUtils.getResponseEntity(false, -2, "Biểu mẫu đã gắn vào quy trình, bạn không thể xóa", HttpStatus.OK);
            }
            Boolean result = templateManager.deleteTemplate(templateRequest);
            if (result) {
                return responseUtils.getResponseEntity(result, 1, "Xóa biểu mẫu thành công", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/deleteAllTemplate")
    public ResponseEntity<?> deleteTemplatev2(@RequestBody List<Long> ids) {
        try {
            Boolean check = templateManager.CheckProdefIDv2(ids);
            if (check) {
                return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                        .message(messageSource.getMessage("error.template.delete.checkProdef", null, Locale.getDefault())).build());
            }
            Boolean result = templateManager.deleteTemplatev2(ids);
            if (result) {
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(true).
                        message(messageSource.getMessage("message.template.delete.success", null, Locale.getDefault())).build());
            }
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.FAIL.code)
                    .message(messageSource.getMessage("error.template.delete.fail", null, Locale.getDefault())).build());
        } catch (Exception e) {
            log.error("Error: {}", e);
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }

//    @GetMapping("/getAll")
//    public ResponseEntity<?> getAll() {
//        try {
//            List<WorkingTimeResponse> result = workingTimeService.getAll();
//            if (result != null) {
//                return responseUtils.getResponseEntity(result, SUCCESS, "success", HttpStatus.OK);
//            }
//            return responseUtils.getResponseEntity(null, FAILED, "fail", HttpStatus.BAD_REQUEST);
//        } catch (Exception e) {
//            return responseUtils.getResponseEntity(null, FAILED, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }

    @PostMapping("/getAll")
    public ResponseEntity<?> getAll(@RequestBody FillterTempateRequest fillterTempateRequest) {
        try {
            PageDto listTemplate = templateManager.getAll(fillterTempateRequest);
            return responseUtils.getResponseEntity(listTemplate, 1, "Thành công!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    @PostMapping("/getAllExportExcel")
    public ResponseEntity<?> getAllExportExcel(@RequestBody FillterTempateRequest fillterTempateRequest) {
        try {
            List<TemplateResponse> listTemplate = templateManager.getAllExportExcel(fillterTempateRequest);
            return responseUtils.getResponseEntity(listTemplate, 1, "Thành công!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/getTemplateHistory")
    public ResponseEntity<?> getTemplateHistory(@RequestBody SearchTemplateHistory filter) {
        try {
            Map<String,Object> listTemplate = templateManager.getTemplateHistory(filter);
            return responseUtils.getResponseEntity(listTemplate, 1, "Thành công!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @GetMapping("/getLstTasksInherits/{procDefId}/{ticketId}")
    public ResponseEntity<?> getLstTasksInherits(@PathVariable String procDefId, @PathVariable Long ticketId) {
        try {
            BpmProcInst currentTicket;
            ArrayList<BpmTask> lstTaskInherits = new ArrayList();
            if (ticketId != null && !Objects.equals(ticketId, 0L)) {
                currentTicket = bpmProcInstManager.findById(ticketId);
                if (currentTicket != null) {//Đã có ticket thì lấy tất cả các bước đã hoàn thành
                    //Add bước start
                    List<ApprovalTaskDto> approvalTasks = bpmProcdefManager.getApprovalTask(procDefId);
                    for (ApprovalTaskDto task : approvalTasks) {
                        if (task.getElementType() == "startEvent") {
                            BpmTask bpmTaskStart = new BpmTask();
                            bpmTaskStart.setTaskId("start");
                            bpmTaskStart.setTaskProcInstId(currentTicket.getTicketProcInstId());
                            bpmTaskStart.setTaskName(task.getTaskName());
                            bpmTaskStart.setTaskType(task.getTaskType());
                            bpmTaskStart.setTaskDefKey(task.getTaskKey());
                            lstTaskInherits.add(bpmTaskStart);
                            break;
                        }
                    }
                    //Add sau bước start đã completed
                    List<BpmTask> bpmTasksCompleted = bpmTaskManager.getByTicketIdAndStatus(currentTicket.getTicketProcInstId(), Arrays.asList("COMPLETED"));
                    if (bpmTasksCompleted != null && bpmTasksCompleted.size() > 0) {
                        lstTaskInherits.addAll(bpmTasksCompleted);
                    }
                }
            }
            return responseUtils.getResponseEntity(lstTaskInherits, 1, "Thành công!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, e.getMessage(), HttpStatus.OK);
        }
    }

    @GetMapping("/getTemplateStartByProcDefId/{id}")
    public ResponseEntity<?> getTemplateStartByProcDefId(@PathVariable String id) {
        try {
            //Lấy ra bước đầu tiên
            List<ApprovalTaskDto> approvalTasks = bpmProcdefManager.getApprovalTask(id);
            String formKey = "";
            String strIdStart = "";
            for (ApprovalTaskDto task : approvalTasks) {
                if (task.getElementType() == "startEvent") {
                    formKey = task.getFormKey();
                    strIdStart = task.getTaskKey();
                    break;
                }
            }

            PageDto listTemplate = null;
            if (formKey != null && formKey.length() > 0) {
                FillterTempateRequest fillterTempateRequest = new FillterTempateRequest();
                fillterTempateRequest.setPage(1);
                fillterTempateRequest.setLimit(9999);
                fillterTempateRequest.setSearch("");
                fillterTempateRequest.setSortBy("id");
                fillterTempateRequest.setSortType("ASC");
                fillterTempateRequest.setStatus(Arrays.asList(-1));
                fillterTempateRequest.setUrlName(Arrays.asList(formKey));
                listTemplate = templateManager.getAll(fillterTempateRequest);
                //Nối tạo thành tên biến chuẩn cho camuda
                if (listTemplate.getContent() != null && listTemplate.getContent().size() > 0) {
                    for (Object item : listTemplate.getContent()) {
                        TemplateResponse templateResponse = (TemplateResponse) item;
                        if (templateResponse != null) {
//                            Map<String, Object> template = getMapObjectFromJson(templateResponse.getTemplate());
                            Map<String, Object> template = ObjectUtils.toObject(templateResponse.getTemplate(), Map.class);
                            if (template != null) {
                                ArrayList<Map<String, Object>> formTemplate = (ArrayList<Map<String, Object>>) template.get("form");
                                for (Map<String, Object> itemForm : formTemplate) {
                                    //Ghép thêm id vào tên biến
                                    String nameNew = (String) itemForm.get("name");
                                    nameNew = strIdStart + "_" + nameNew;
                                    itemForm.put("name", nameNew);
                                }
                                template.put("form", formTemplate);
//                                ((TemplateResponse) item).setTemplate(new Gson().toJson(template));
                                ((TemplateResponse) item).setTemplate(ObjectUtils.toJson(template));
                            }
                        }
                    }
                }
            }
            return responseUtils.getResponseEntity(listTemplate, 1, "Thành công!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, e.getMessage(), HttpStatus.OK);
        }
    }

    public Map<String, Object> getMapObjectFromJson(String strJson) {
        Map<String, Object> map = null;
        Gson gson = new Gson();
        map = gson.fromJson(strJson, new TypeToken<HashMap<String, Object>>() {
        }.getType());
        return map;
    }

    @GetMapping("/getTemplate/ticket")
    public ResponseEntity<?> getTemplateByTicket(@RequestParam("procDefId") String procDefId,
                                                 @RequestParam(value = "procInstId", required = false) String procInstId) {
        try {
            List<TemplateResponse> listTemplate = templateManager.getTemplateByTicket(procDefId, procInstId);
            if (listTemplate != null) {
                return responseUtils.getResponseEntity(listTemplate, 1, "Success!", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/getTemplate/task")
    public ResponseEntity<?> getTemplateByTask(@RequestParam("procDefId") String procDefId,
                                               @RequestParam("type") String type, @RequestParam("taskId") String taskId) {
        try {
            List<TemplateResponse> listTemplate = templateManager.getTemplateByTask(procDefId, type, taskId);
            if (listTemplate != null) {
                return responseUtils.getResponseEntity(listTemplate, 1, "Success!", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/getTemplate/formKey")
    public ResponseEntity<?> getTemplateByFormkey(@RequestParam("formKey") String formKey) {
        try {
            TemplateResponse listTemplate = templateManager.getTempaletByFormKey(formKey);
            if (listTemplate != null) {
                return responseUtils.getResponseEntity(listTemplate, 1, "Success!", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/getTemplate/id")
    public ResponseEntity<?> getTemplateById(@RequestParam("id") Integer id) {
        try {
            TemplateResponse listTemplate = templateManager.getTemplateById(id);
            if (listTemplate != null) {
                return responseUtils.getResponseEntity(listTemplate, 1, "Success!", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


//    @PostMapping("/getAll")
//    public ResponseEntity<?> getListTemplate(@RequestBody FillterTempateRequest fillterTempateRequest){
//        try{
//            Page<TemplateResponse> listTemplate= templateManager.getAll(fillterTempateRequest);
//            return responseUtils.getResponseEntity(listTemplate,1,"Success!",HttpStatus.OK);
//        }catch (Exception e){
//            e.printStackTrace();
//            return responseUtils.getResponseEntity(null,-1,"Fail!",HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }

    @PostMapping("/updateTemplate")
    public ResponseEntity<?> updateTemplate(@RequestBody TemplateRequest templateRequest) {
        try {
            Boolean check = templateManager.checkNameExits(templateRequest.getTemplateName(), templateRequest.getId());
            if (check) {
                return responseUtils.getResponseEntity(true, 1, "Tên biểu mẫu đã tồn tại trong hệ thống!", HttpStatus.OK);
            }
            TemplateManage result = templateManager.updateTemplate(templateRequest);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Cập nhật biểu mẫu thành công", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/cloneTemplate")
    public ResponseEntity<?> cloneTemplate(@RequestParam("Id") Long Id) {
        try {
            Long result = templateManager.cloneTemplate(Id);
            String msg = "message.manageTemplate.clone.success";
            int code = 1;
            HttpStatus httpStatus = HttpStatus.OK;
            if(result != 1L){
                if (result == -2L) {
                    msg = "message.manageTemplate.clone.nameLength";
                } else if(result == -3L)
                    msg = "message.manageTemplate.clone.urlNameLength";
                else msg = "message.manageTemplate.clone.fail";
                code =-1;
                httpStatus = HttpStatus.BAD_REQUEST;
            }
            return responseUtils.getResponseEntity(result, code, messageSource.getMessage(msg, null, Locale.getDefault()), httpStatus);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/checkExistName")
    public ResponseEntity<?> checkExistName(@RequestParam("templateName") String templateName,
                                            @RequestParam("urlName") String urlName,
                                            @RequestParam(value = "id", required = false) Long id) {
        try {

            Boolean checkTemplateName = templateManager.checkNameExits(templateName, id);
            Boolean checkUrlName = templateManager.checkUrlNameExits(urlName, id);
            Map result = new HashMap();
            result.put("templateName", checkTemplateName);
            result.put("urlName", checkUrlName);
            return responseUtils.getResponseEntity(result, 1, "success", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/checkProdefID")
    public ResponseEntity<?> checkExistName(@RequestParam("id") Long id) {
        try {
            Boolean check = templateManager.CheckProdefID(id);
            if (check) {
                return responseUtils.getResponseEntity(false, -1, "Biểu mẫu đã gắn vào quy trình, bạn không thể xóa", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(false, 1, "Success!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/getTemplate/getTemplateField")
    public ResponseEntity<?> getTemplateField(@RequestParam("id") Long id) {
        try {
            List<Map<String, String>> response = templateManager.getTemplateField(id);
            return responseUtils.getResponseEntity(response, 1, "Success!", HttpStatus.OK);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/getTemplate/getAllFormKey")
    public ResponseEntity<?> getAllFormKey() {
        try {
            List<Map<String, Object>> response = templateManager.getAllFormKey();
            return responseUtils.getResponseEntity(response, 1, "Success!", HttpStatus.OK);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/checkMasterData")
    public ResponseEntity<?> checkMasterData(@RequestParam("masterDataId") Long masterDataId) {
        try {
            boolean check = templateManager.checkMasterDataId(masterDataId);
            if (check) {
                return responseUtils.getResponseEntity(false, -1, "Biểu mẫu đã gắn vào masterdata", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(false, 1, "Success!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/checkMasterDataFilter")
    public ResponseEntity<?> checkMasterDataFilter(@RequestParam("masterDataId") Long masterDataFilterId) {
        try {
            Boolean check = templateManager.checkMasterDataFilterId(masterDataFilterId);
            if (check) {
                return responseUtils.getResponseEntity(false, -1, "Biểu mẫu đã gắn vào masterdataFilter", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(false, 1, "Success!", HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return responseUtils.getResponseEntity(null, -1, "Fail!", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get All Filter Data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get ApiManager organization chart is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get ApiManager organization chart is fail", content = @Content)})
    @Tag(name = "TemplateManager", description = "Get all filterData")
    @PostMapping("/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody FillterTempateRequest fillterTempateRequest) {
        try {
            Object result = businessManager.getFilterData(templateManager.getAllFilter(fillterTempateRequest), FilterDataEnum.TEMPLATE_MANAGER);
            return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                    message(messageSource.getMessage("message.searchApiLog.searchApiAction.success", null, Locale.getDefault())).build());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ResponseDto.builder().code(ResponseCodeEnum.INTERNAL_SERVER_ERROR.code)
                    .message(messageSource.getMessage(e.getMessage(), null, Locale.getDefault())).build());
        }
    }
}
