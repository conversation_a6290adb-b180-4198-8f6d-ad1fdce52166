package vn.fis.eapprove.business.application.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.bpm.service.BpmHistoryManager;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.HistoryDto;
import vn.fis.eapprove.business.utils.ResponseUtils;

import java.util.Map;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("BpmHistoryControllerV1")
@CrossOrigin("*")
@RequestMapping(SERVICE_PATH + "/history")
public class BpmHistoryController {

    @Autowired
    BpmHistoryManager bpmHistoryManager;

    @Autowired
    ResponseUtils responseUtils;

    @PostMapping("/getHistoryByProcess")
    public ResponseEntity<?> getHistoryByProcess(@RequestBody HistoryDto historyDto) {
        try {
            PageDto result = bpmHistoryManager.getHistoryByProcess(historyDto);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/getHistoryRu")
    public ResponseEntity<?> getHistoryRu(@RequestParam String taskId, @RequestParam String oldTaskId) {
        try {

            return null;
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/getFilterHistory")
    public ResponseEntity<?> getFilterHistory(@RequestParam Long ticketId) {
        try {
            Map<String, Object> result = bpmHistoryManager.getFilterHistory(ticketId);
            if (result != null) {
                return responseUtils.getResponseEntity(result, 1, "Success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
