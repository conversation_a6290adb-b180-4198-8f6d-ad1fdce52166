package vn.fis.eapprove.business.application.controller;

import vn.fis.eapprove.security.CredentialHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.bpm.service.BpmTemplatePrintHistoryManager;
import vn.fis.eapprove.business.dto.DataTemplatePrint;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.TemplatePrintHistoryRequest;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.helper.ResponseHelper;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@Slf4j
@RestController("BpmTemplatePrintHistoryControllerV1")
@CrossOrigin("*")
@RequestMapping(SERVICE_PATH + "/bpmTemplatePrintHistory")
public class BpmTemplatePrintHistoryController {

    @Autowired
    private BpmTemplatePrintHistoryManager bpmTemplatePrintHistoryManager;

    @Autowired
    private BusinessManager businessManager;

    @Autowired
    private CredentialHelper credentialHelper;

    @PostMapping("/getAllHistory")
    public ResponseEntity<?> getAll(@RequestBody TemplatePrintHistoryRequest request) {
        PageDto result = bpmTemplatePrintHistoryManager.getAll(request);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/getAllFilter")
    public ResponseEntity<?> getAllFilter(@RequestBody TemplatePrintHistoryRequest request) {
        try {
            Object result = businessManager.getFilterData(bpmTemplatePrintHistoryManager.getAllFilter(request), FilterDataEnum.BPM_TEMPLATE_PRINT);
            return ResponseHelper.ok(result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail(null, e.getMessage());
        }
    }

    @GetMapping("/getDetailHistory/{id}")
    public ResponseEntity<?> getDetailHistory(@PathVariable Long id) {
        DataTemplatePrint result = bpmTemplatePrintHistoryManager.getDetailHistory(id);
        return ResponseHelper.ok(result);
    }

    @PostMapping("/restoreHistory")
    public ResponseEntity<?> restoreHistory(@RequestParam Long id) {
        try {
            String username = credentialHelper.getJWTPayload().getUsername();
            bpmTemplatePrintHistoryManager.restoreHistory(id, username);
            return ResponseHelper.okWithMessage("Khôi phục bản ghi thành công.");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail(null, e.getMessage());
        }
    }
}
