package vn.fis.eapprove.business.application.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.sequence.service.SequenceDataManager;
import vn.fis.spro.common.helper.ResponseHelper;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@Slf4j
@RestController("SequenceDataControllerV1")
@CrossOrigin("*")
@Tag(name = "sequence-data-service", description = "The sequence data service")
@RequestMapping(SERVICE_PATH + "/sequence-data")
public class SequenceDataController {
    @Autowired
    SequenceDataManager sequenceDataManager;

    @GetMapping("/generateId")
    public ResponseEntity<?> generateIDBySequenceTemplate(@RequestParam("sequenceTemplate") String sequenceTemplate) {
        try {
            return ResponseHelper.ok(sequenceDataManager.generateIDBySequenceTemplate(sequenceTemplate));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseHelper.fail();
        }
    }

    @GetMapping("/redisAtomicInteger")
    public ResponseEntity<?> redisAtomicInteger() {
        return ResponseHelper.ok(sequenceDataManager.redisAtomicInteger());
    }
}
