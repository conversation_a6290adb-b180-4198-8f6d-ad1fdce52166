package vn.fis.eapprove.business.application.controller;


import vn.fis.eapprove.security.CredentialHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdefNotification;
import vn.fis.eapprove.business.domain.bpm.repository.BpmProcdefNotificationRepository;
import vn.fis.eapprove.business.domain.notification.entity.NotificationTemplate;
import vn.fis.eapprove.business.domain.notification.entity.NotificationTemplateDetail;
import vn.fis.eapprove.business.domain.notification.repository.NotificationTemplateDetailRepository;
import vn.fis.eapprove.business.domain.notification.service.NotificationTemplateDetailService;
import vn.fis.eapprove.business.domain.notification.service.NotificationTemplateService;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.repository.PermissionDataManagementRepository;
import vn.fis.eapprove.business.dto.NotificationTemplateDTO;
import vn.fis.eapprove.business.dto.PageDto;
import vn.fis.eapprove.business.model.request.NotificationTemplateSearchRequest;
import vn.fis.eapprove.business.model.response.NameAndCodeCompanyResponse;
import vn.fis.eapprove.business.model.response.NotificationTemplateResponse;
import vn.fis.eapprove.business.tenant.manager.BusinessManager;
import vn.fis.eapprove.business.tenant.manager.CustomerService;
import vn.fis.eapprove.business.utils.Common;
import vn.fis.eapprove.business.utils.ResponseUtils;
import vn.fis.spro.common.constants.FilterDataEnum;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ProcInstConstants;
import vn.fis.spro.common.constants.ResponseCodeEnum;
import vn.fis.spro.common.model.ResponseDto;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static vn.fis.eapprove.business.constant.Constant.SERVICE_PATH;

@RestController("NotificationTemplateControllerV1")
@Slf4j
@RequestMapping(SERVICE_PATH + "/notification-template")
public class NotificationTemplateController {
    private final NotificationTemplateService notificationTemplateService;
    private final NotificationTemplateDetailService notificationTemplateDetailService;
    private final NotificationTemplateDetailRepository notifTmplDetailRepo;
    private final ResponseUtils responseUtils;
    private final BpmProcdefNotificationRepository bpmProcdefNotificationRepository;
    private final PermissionDataManagementRepository permissionDataManagementRepository;

    @Autowired
    private Common common;

    @Autowired
    private CredentialHelper credentialHelper;

    @Autowired
    BusinessManager businessManager;
    @Autowired
    private CustomerService customerService;

    @Autowired
    public NotificationTemplateController(
            NotificationTemplateService notificationTemplateService,
            NotificationTemplateDetailService notificationTemplateDetailService,
            NotificationTemplateDetailRepository notifTmplDetailRepo,
            ResponseUtils responseUtils,
            BpmProcdefNotificationRepository bpmProcdefNotificationRepository,
            PermissionDataManagementRepository permissionDataManagementRepository) {
        this.notificationTemplateService = notificationTemplateService;
        this.notificationTemplateDetailService = notificationTemplateDetailService;
        this.notifTmplDetailRepo = notifTmplDetailRepo;
        this.responseUtils = responseUtils;
        this.bpmProcdefNotificationRepository = bpmProcdefNotificationRepository;
        this.permissionDataManagementRepository = permissionDataManagementRepository;
    }

    @Operation(summary = "Get all notification template")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get Success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get Fail, server interal, processing error", content = @Content),
            @ApiResponse(responseCode = "400", description = "Get Fail, bad request, wrong param", content = @Content),
    })
    @PostMapping("/getTemplates")
    public ResponseEntity<?> getTemplates(@RequestBody NotificationTemplateSearchRequest request) {
        try {
            PageDto response = notificationTemplateService.getTemplates(request);
            if (response.getSize() >= 0) {
                return responseUtils.getResponseEntity(response, 1, "success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Something wrong", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get all notification detail")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get Success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Get Fail, server interal, processing error", content = @Content),
            @ApiResponse(responseCode = "400", description = "Get Fail, bad request, wrong param", content = @Content),
    })
    @GetMapping("/getTemplateDetails")
    public ResponseEntity<?> getTemplatesDetail(@RequestParam("templateId") Long templateId) {
        try {
            List<NotificationTemplateDetail> response = notifTmplDetailRepo.getDetailFromTemplateId(templateId);
            if (!response.isEmpty()) {
                return responseUtils.getResponseEntity(response, 1, "success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return responseUtils.getResponseEntity(null, -1, "Something wrong", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "delete by id notification template")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "delete Success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "delete Fail, server interal, processing error", content = @Content),
            @ApiResponse(responseCode = "400", description = "delete Fail, bad request, wrong param", content = @Content),
    })
    @PostMapping("/deleteByIds")
    public ResponseEntity<?> deleteByIds(@RequestBody Long[] ids) {
        try {
            if (ids.length > 0) {
                //kiểm tra nếu mẫu thông báo đã dùng không cho xóa
                List<BpmProcdefNotification> bpmProcdefNotifications = bpmProcdefNotificationRepository.findBpmProcdefNotificationsByStatusAndNotificationTemplateIdIn("1", Arrays.asList(ids));
                if (bpmProcdefNotifications != null && bpmProcdefNotifications.size() > 0) {
                    return responseUtils.getResponseEntity(null, -1, common.getMessage("notification.delete-error"), HttpStatus.INTERNAL_SERVER_ERROR);
                }
                notificationTemplateService.deleteByIds(ids);
                return responseUtils.getResponseEntity(null, 1, "success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "create notification template")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "create Success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "create Fail, server interal, processing error", content = @Content),
            @ApiResponse(responseCode = "400", description = "create Fail, bad request, wrong param", content = @Content),
    })
    @PostMapping("/create-update")
    public ResponseEntity<?> create(@Valid @RequestBody NotificationTemplateDTO template) {
        try {
            if (template != null) {
                List<NotificationTemplate> templateChecks;
                //Kiểm tra xem trùng tên không
                templateChecks = notificationTemplateService.findNotificationTemplatesByTitleAndType(template.getTitle(), template.getType());
                if (templateChecks != null) {
                    if ((templateChecks.size() > 1)
                            || (templateChecks.size() == 1
                            && (ValidationUtils.isNullOrEmpty(template.getId()) || (template.getId() != null && !templateChecks.get(0).getId().equals(template.getId())))
                    )) {
                        return responseUtils.getResponseEntity(null, -1, common.getMessage("notification.save-title-error", new Object[]{template.getTitle()}), HttpStatus.INTERNAL_SERVER_ERROR);
                    }
                }

                //Kiem tra neu action rieng le thi no phai la duy nhất để làm template thông báo cho all quy trình
                if (template.getType() != null && template.getType().equalsIgnoreCase("SYSTEM")
                        && template.getActionCode() != null) {
                    templateChecks = notificationTemplateService.findNotificationTemplatesByActionCodeAndTypeAndNotificationObject(template.getActionCode(), template.getType(), template.getNotificationObject());
                    if (templateChecks != null) {
                        if ((templateChecks.size() > 1)
                                || (templateChecks.size() == 1
                                && (ValidationUtils.isNullOrEmpty(template.getId()) || (template.getId() != null && !templateChecks.get(0).getId().equals(template.getId())))
                        )) {
                            if (template.getType().equalsIgnoreCase("SYSTEM") && template.getActionCode().equalsIgnoreCase("ALL")) {
                                return responseUtils.getResponseEntity(null, -1, common.getMessage("notification.save-error",
                                        new Object[]{"Khác", template.getNotificationObject()}), HttpStatus.INTERNAL_SERVER_ERROR);
                            } else {
                                return responseUtils.getResponseEntity(null, -1, common.getMessage("notification.save-error",
                                        new Object[]{ProcInstConstants.Notifications.valueOf(template.getActionCode()).name, template.getNotificationObject()}), HttpStatus.INTERNAL_SERVER_ERROR);
                            }
                        }
                    }
                }
                //Chỉ áp dụng cho các mẫu hệ thống
                if (!template.getType().equalsIgnoreCase("SYSTEM")) {
                    template.setNotificationObject(null);
                }

                //Lưu template
                NotificationTemplate currentSave;
                if (template.getId() != null) {
                    NotificationTemplate currentUpdate = notificationTemplateService.findNotificationTemplateById(template.getId());

                    // Xóa data phân quyền dữ liệu cũ
                    List<PermissionDataManagement> oldData = permissionDataManagementRepository.getPermissionDataManagementsByTypeIdAndTypeName(template.getId(), PermissionDataConstants.Type.NOTIFICATION_TEMPLATE.code);
                    if (!ValidationUtils.isNullOrEmpty(oldData)) {
                        permissionDataManagementRepository.deleteAll(oldData);
                    }

                    if (currentUpdate != null) {
                        notificationTemplateDetailService.deleteAllByNotificationTemplateId(template.getId());
                        currentUpdate.setTitle(template.getTitle());
                        currentUpdate.setActionCode(template.getActionCode());
                        currentUpdate.setNotificationObject(template.getNotificationObject());
                        currentUpdate.setType(template.getType());
                        currentUpdate.setContent(template.getContent());
                        currentUpdate.setShareWith(template.getShareWith());
                        currentUpdate.setSourceType(template.getSourceType());
                        currentUpdate.setUpdateAt(new Date());
                        currentUpdate.setUserUpdate(credentialHelper.getJWTPayload().getUsername());
                        currentSave = notificationTemplateService.save(currentUpdate);
                    } else {
                        currentSave = null;
                    }
                } else {
                    NotificationTemplate notificationTemplate = new NotificationTemplate();
                    List<NameAndCodeCompanyResponse> listCompanyCodeAndName = customerService.responseCompanyCodeAndName(credentialHelper.getJWTPayload().getUsername());
                    for(NameAndCodeCompanyResponse response : listCompanyCodeAndName){
                        notificationTemplate.setCompanyCode(response.getCompanyCode());
                        notificationTemplate.setCompanyName(response.getCompanyName());
                    }
                    currentSave = notificationTemplateService.save(
                            NotificationTemplate
                                    .builder()
                                    .id(template.getId())
                                    .actionCode(template.getActionCode())
                                    .notificationObject(template.getNotificationObject())
                                    .title(template.getTitle())
                                    .type(template.getType())
                                    .content(template.getContent())
                                    .createAt(new Date())
                                    .updateAt(null)
                                    .userCreate(template.getUserCreate())
                                    .sourceType(template.getSourceType())
                                    .shareWith(template.getShareWith())
                                    .companyCode(notificationTemplate.getCompanyCode())
                                    .companyName(notificationTemplate.getCompanyName())
                                    .build()
                    );
                }
                //Lưu detail
                if (currentSave != null && !template.getNotifTmpDetail().isEmpty()) {
                    template.getNotifTmpDetail().stream().forEach(item -> {
                        item.setNotificationTemplateId(currentSave.getId());
                        if (item.getCreateAt() == null)
                            item.setCreateAt(new Date());
                        else
                            item.setUpdateAt(new Date());
                    });
                    this.notifTmplDetailRepo.saveAll(template.getNotifTmpDetail());
                }

                // Lưu phân quyền dữ liệu
                if (!ValidationUtils.isNullOrEmpty(template.getApplyFor()) && currentSave != null) {
                    List<PermissionDataManagement> permissionDataManagements = new ArrayList<>();
                    for (String data : template.getApplyFor()) {
                        PermissionDataManagement permissionDataManagement = new PermissionDataManagement();
                        permissionDataManagement.setTypeId(currentSave.getId());
                        permissionDataManagement.setTypeName(PermissionDataConstants.Type.NOTIFICATION_TEMPLATE.code);
                        permissionDataManagement.setCompanyCode(data);
                        permissionDataManagement.setCreatedUser(credentialHelper.getJWTPayload().getUsername());
                        permissionDataManagement.setCreatedTime(LocalDateTime.now());
                        permissionDataManagements.add(permissionDataManagement);
                    }

                    permissionDataManagementRepository.saveAll(permissionDataManagements);
                }

                return responseUtils.getResponseEntity(null, 1, "success", HttpStatus.OK);
            }
            return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Processing Error", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get filter data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Search and Get all signatures is success", content = {@Content(mediaType = "application/json")}),
            @ApiResponse(responseCode = "500", description = "Search and Get all signatures is fail", content = @Content)})
    @PostMapping("/getFilterData")
    public ResponseEntity<?> getFilterData(@RequestBody NotificationTemplateSearchRequest request) {
        try {
            List<NotificationTemplateResponse> response = notificationTemplateService.getTemplatesFilter(request);
            try {
                Object result = businessManager.getFilterData(response, FilterDataEnum.NOTIFICATION_TEMPLATE);
                return ResponseEntity.ok(ResponseDto.builder().code(ResponseCodeEnum.SUCCESS.code).data(result).
                        message("Thành Công").build());
            } catch (Exception e) {
                return responseUtils.getResponseEntity(null, -1, "Something wrong !!!. Param is not valid", HttpStatus.BAD_REQUEST);
            }
        } catch (Exception e) {
            return responseUtils.getResponseEntity(null, -1, "Something wrong", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}