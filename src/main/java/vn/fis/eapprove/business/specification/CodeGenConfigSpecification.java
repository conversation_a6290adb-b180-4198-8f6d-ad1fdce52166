package vn.fis.eapprove.business.specification;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.codeGen.entity.CodeGenConfig;
import vn.fis.eapprove.business.domain.codeGen.entity.CodeGenConfig_;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement_;
import vn.fis.eapprove.business.dto.CodeGenConfigDto;

import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Author: AnhVTN
 * Date: 30/03/2023
 */

@Component
public class CodeGenConfigSpecification {
    public Specification<CodeGenConfig> filter(final CodeGenConfigDto criteria) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (ValidationUtils.isNullOrEmpty(criteria.getListCompanyCode())) {
                criteria.setListCompanyCode(new ArrayList<>());
            }
            if (StringUtils.isNotEmpty(criteria.getSearch())) {
                Root<CodeGenConfig> codeGenConfigRoot = query.from(CodeGenConfig.class);
                query.distinct(true);
                predicates.add(cb.or(
                        cb.like(cb.lower(root.get("code")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("structorCode")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(codeGenConfigRoot.get("description")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        root.get("code").in(criteria.getSystemConfigCode())
                ));
            }

            if (!criteria.getListCompanyCode().contains(CommonConstants.FILTER_SELECT_ALL)) {
                Join<CodeGenConfig, PermissionDataManagement> permissionDataManagement = root.join(CodeGenConfig_.permissionDataManagements);
                if (!ValidationUtils.isNullOrEmpty(criteria.getLstGroupPermissionId())) {
                    // có phân quyền theo nhóm ưu tiên lấy các bản ghi trong nhóm + theo companyCode member admin (nếu có)
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.CODE_GEN_CONFIG.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(criteria.getListCompanyCodeMemberAdmin()),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL),
                                    // Phân quyền theo nhóm
                                    root.get(CodeGenConfig_.id).in(criteria.getLstGroupPermissionId())
                            )
                    ));
                } else {
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.CODE_GEN_CONFIG.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(criteria.getListCompanyCode()),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
                            )
                    ));
                }
            }

            if (criteria.getStatus() != null && !criteria.getStatus().contains(CommonConstants.FILTER_SELECT_ALL)) {
                predicates.add(cb.and(root.get("status").in(criteria.getStatus())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCode())){
                predicates.add(root.get(CodeGenConfig_.CODE).in(criteria.getListCode()));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListDescription())){
                predicates.add(root.get(CodeGenConfig_.DESCRIPTION).in(criteria.getListDescription()));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListUserUpdate())){
                predicates.add(root.get(CodeGenConfig_.USER_UPDATE).in(criteria.getListUserUpdate()));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListUserCreate())){
                predicates.add(root.get(CodeGenConfig_.USER_CREATE).in(criteria.getListUserCreate()));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListStructorCode())){
                predicates.add(root.get(CodeGenConfig_.structorCode).in(criteria.getListStructorCode()));
            }

            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            Date fromDateLocal =DateDto.getFromDate();
                            Date toDateLocal = DateDto.getToDate();
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(toDateLocal);
                            calendar.add(Calendar.DAY_OF_MONTH, 1);
                            toDateLocal = calendar.getTime();
                            predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            Date toDateLocal = DateDto.getToDate();
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(toDateLocal);
                            calendar.add(Calendar.DAY_OF_MONTH, 1);
                            toDateLocal = calendar.getTime();
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            Date fromDateLocal =DateDto.getFromDate();
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                            System.out.println(e.getMessage());
                        }
                    }
                }
            }

            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }
}
