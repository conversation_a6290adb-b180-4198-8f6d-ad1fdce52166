package vn.fis.eapprove.business.specification;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.manageApi.entity.ManageShareTicket;
import vn.fis.eapprove.business.domain.manageApi.entity.ManageShareTicketDetail;
import vn.fis.eapprove.business.domain.manageApi.entity.ManageShareTicketDetail_;
import vn.fis.eapprove.business.domain.manageApi.entity.ManageShareTicket_;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement_;
import vn.fis.eapprove.business.model.request.SearchShareTicketRequest;


import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.criteria.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
public class ManageShareTicketSpecification {
    public Specification<ManageShareTicket> filter(SearchShareTicketRequest request) {
        return ((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            query.distinct(true);

            predicates.add(cb.equal(root.get(ManageShareTicket_.status), request.getStatus()));

            if (!request.getListCompanyCode().contains(CommonConstants.FILTER_SELECT_ALL)) {
                Join<ManageShareTicket, PermissionDataManagement> permissionDataManagement = root.join(ManageShareTicket_.permissionDataManagements);

                // check permission data - shared user (companyCode = -1 -> view all)
                predicates.add(cb.and(
                        cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.MANAGE_SHARE_TICKET.code),
                        cb.or(
                                permissionDataManagement.get(PermissionDataManagement_.companyCode).in(request.getListCompanyCode()),
                                cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
                        ))
                );
            }

            if (!ValidationUtils.isNullOrEmpty(request.getSearchName())) {
                predicates.add(cb.like(root.get(ManageShareTicket_.name), "%" + request.getSearchName() + "%"));
            }

            if (!ValidationUtils.isNullOrEmpty(request.getSearch())) {
                Join<ManageShareTicket, ManageShareTicketDetail> manageShareTicketDetail = root.join(ManageShareTicket_.manageShareTicketDetails);

                predicates.add(cb.and(
                        cb.not(manageShareTicketDetail.get(ManageShareTicketDetail_.type).in(Arrays.asList("companyCode", "chartNodeCode"))),
                        cb.or(
                                cb.like(manageShareTicketDetail.get(ManageShareTicketDetail_.value), "%" + request.getSearch() + "%")
                        ))
                );
            }

            if (request.getListDateFilter() != null && !request.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : request.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1); // PhucVM3: Add 1 day to fix search by date range
                            predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1); // PhucVM3: Add 1 day to fix search by date range
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                        }
                    }
                }
            }
            if(request.getName() != null && !request.getName().contains(CommonConstants.FILTER_SELECT_ALL)){
                predicates.add(root.get("name").in(request.getName()));
            }
            if (request.getUpdatedUser() != null && !request.getUpdatedUser().contains(CommonConstants.FILTER_SELECT_ALL)) {
                predicates.add(root.get("updatedUser").in(request.getUpdatedUser()));
            }
            if (request.getCreatedUser() != null && !request.getCreatedUser().contains(CommonConstants.FILTER_SELECT_ALL)) {
                predicates.add(root.get("createdUser").in(request.getCreatedUser()));
            }
            if(request.getDescription() != null && !request.getDescription().contains(CommonConstants.FILTER_SELECT_ALL)){
                predicates.add(root.get("description").in(request.getDescription()));
            }
            if(request.getCompanyCode() != null && !request.getCompanyCode().contains(CommonConstants.FILTER_SELECT_ALL)){
                predicates.add(root.get("companyCode").in(request.getCompanyCode()));
            }
            if(request.getCompanyName() != null && !request.getCompanyName().contains(CommonConstants.FILTER_SELECT_ALL)){
                predicates.add(root.get("companyName").in(request.getCompanyName()));
            }
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        });
    }
}
