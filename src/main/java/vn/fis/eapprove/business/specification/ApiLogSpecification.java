package vn.fis.eapprove.business.specification;

import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.api.entity.ApiLog;
import vn.fis.eapprove.business.domain.api.entity.ApiLog_;
import vn.fis.eapprove.business.dto.ApiLogDto;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Component
public class ApiLogSpecification {
    public Specification<ApiLog> filter(ApiLogDto search) {

        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            if (!ValidationUtils.isNullOrEmpty(search.getFromDate())) {
                LocalDateTime fromDate = LocalDate.parse(search.getFromDate(), formatter).atStartOfDay();
                predicates.add(cb.greaterThanOrEqualTo(root.get(ApiLog_.requestTime), fromDate));
            }
            if (!ValidationUtils.isNullOrEmpty(search.getToDate())) {
                LocalDateTime toDate = LocalDate.parse(search.getToDate(), formatter).atStartOfDay();
                toDate = toDate.plusDays(1);
                predicates.add(cb.lessThanOrEqualTo(root.get(ApiLog_.requestTime), toDate));
            }

            if (StringUtils.isNotEmpty(search.getSearch())) {
                if (StringUtils.isNotEmpty(search.getSearch())) {
                    predicates.add(cb.or(
                            cb.like(cb.lower(root.get(ApiLog_.url)), "%" + search.getSearch().trim().toLowerCase() + "%"),
                            cb.like(cb.lower(root.get(ApiLog_.apiName)), "%" + search.getSearch().trim().toLowerCase() + "%"),
                            cb.like(cb.lower(root.get(ApiLog_.ticketName)), "%" + search.getSearch().trim().toLowerCase() + "%")
                    ));
                }
            }

            // filter
            if (ValidationUtils.isAcceptSearchListIn(search.getListApiName())){
                predicates.add(cb.and(root.get(ApiLog_.apiName).in(search.getListApiName())));
            }
            if (ValidationUtils.isAcceptSearchListIn(search.getListMethod())){
                predicates.add(cb.and(root.get(ApiLog_.method).in(search.getListMethod())));
            }
            if (ValidationUtils.isAcceptSearchListIn(search.getListUrl())) {
                predicates.add(cb.and(root.get(ApiLog_.url).in(search.getListUrl())));
            }
            if (ValidationUtils.isAcceptSearchListIn(search.getListHeader())) {
                predicates.add(cb.and(root.get(ApiLog_.header).in(search.getListHeader())));
            }
            if (ValidationUtils.isAcceptSearchListIn(search.getListRequestBody())) {
                predicates.add(cb.and(root.get(ApiLog_.requestBody).in(search.getListRequestBody())));
            }
            if (ValidationUtils.isAcceptSearchListIn(search.getListResponseStatus())) {
                predicates.add(cb.and(root.get(ApiLog_.responseStatus).in(search.getListResponseStatus())));
            }
            if (ValidationUtils.isAcceptSearchListIn(search.getListResponseData())) {
                predicates.add(cb.and(root.get(ApiLog_.responseData).in(search.getListResponseData())));
            }
            if (ValidationUtils.isAcceptSearchListIn(search.getListApiType())) {
                predicates.add(cb.and(root.get(ApiLog_.apiType).in(search.getListApiType())));
            }
            if (ValidationUtils.isAcceptSearchListIn(search.getListRequestCode())) {
                predicates.add(cb.and(root.get(ApiLog_.requestCode).in(search.getListRequestCode())));
            }
            if (ValidationUtils.isAcceptSearchListIn(search.getListTicketName())) {
                predicates.add(cb.and(root.get(ApiLog_.ticketName).in(search.getListTicketName())));
            }
            if (ValidationUtils.isAcceptSearchListIn(search.getListTaskDefKey())) {
                predicates.add(cb.and(root.get(ApiLog_.taskDefKey).in(search.getListTaskDefKey())));
            }
            if (ValidationUtils.isAcceptSearchListIn(search.getListAppCode())) {
                predicates.add(cb.and(root.get(ApiLog_.appCode).in(search.getListAppCode())));
            }
            if (!ValidationUtils.isNullOrEmpty(search.getListDateFilter())) {
                for (DateFilterDto DateDto : search.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                        }
                    }
                }
            }

            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }
}
