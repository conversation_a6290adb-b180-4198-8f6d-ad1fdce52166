package vn.fis.eapprove.business.specification;

import jakarta.persistence.criteria.Predicate;

import jakarta.persistence.criteria.Root;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.entity.BpmVariables;
import vn.fis.eapprove.business.domain.bpm.entity.BpmVariables_;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class BpmVariablesSpecification {

    public Specification<BpmVariables> filterComplete(String procInstId, String taskId) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            List<String> listType = Arrays.asList("INTEGER", "LONG", "STRING", "DOUBLE", "FLOAT", "FILE", "JSON");
            query.distinct(true);

            predicates.add(cb.equal(root.get(BpmVariables_.isDraft), 0));
            predicates.add(root.get("type").in(listType));
            predicates.add(cb.equal(root.get("procInstId"), procInstId));
            predicates.add(cb.equal(root.get("taskId"), taskId));
            return cb.and(predicates.toArray(Predicate[]::new));
        };
    }

    public Specification<BpmVariables> filterByInput(String procInstId, List<String> listTaskKey, Boolean isStart) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            query.distinct(true);
            if (isStart) {
                predicates.add(cb.equal(root.get("procInstId"), procInstId));
                predicates.add(cb.isNull(root.get("taskId")));
            } else {
                Root<BpmTask> rootTask = query.from(BpmTask.class);
                predicates.add(cb.equal(root.get(BpmVariables_.isDraft), 0));
                predicates.add(cb.equal(root.get("taskId"), rootTask.get("taskId")));
                predicates.add(cb.equal(rootTask.get("taskProcInstId"), procInstId));
                predicates.add(rootTask.get("taskDefKey").in(listTaskKey));
                predicates.add(cb.equal(rootTask.get("taskStatus"), "COMPLETED"));
            }
            return cb.and(predicates.toArray(Predicate[]::new));
        };
    }

    public Specification<BpmVariables> filterByName(List<String> listName, String taskId, Boolean isRu) {
        return (root, query, cb) -> {
            query.distinct(true);
            List<Predicate> predicates = new ArrayList<>();
            Root<BpmTask> rootTask = query.from(BpmTask.class);
            predicates.add(cb.equal(root.get("procInstId"), taskId));
            predicates.add(root.get("name").in(listName));
            if (isRu != null && !isRu) {
                predicates.add(cb.and(cb.equal(root.get("taskId"), rootTask.get("taskId")),
                        cb.notEqual(rootTask.get("taskStatus"), "DELETED_BY_RU")));
            }
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }

    public Specification<BpmVariables> filterByProcInstId(String procInstId) {
        return filterByProcInstId(procInstId, true);
    }

    public Specification<BpmVariables> filterByProcInstId(String procInstId, Boolean taskIsNull) {
        return (root, query, cb) -> {
            query.distinct(true);
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("procInstId"), procInstId));
            if (taskIsNull) {
                predicates.add(cb.isNull(root.get("taskId")));
            }
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }
}
