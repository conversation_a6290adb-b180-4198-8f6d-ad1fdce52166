package vn.fis.eapprove.business.specification;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.assign.entity.AssignManagement;
import vn.fis.eapprove.business.domain.assign.entity.AssignManagement_;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement_;
import vn.fis.eapprove.business.dto.AssignDto;

import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.criteria.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class AssignSpecification {
    public Specification<AssignManagement> filter(final AssignDto criteria) {

        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            String search = criteria.getSearch().trim().toLowerCase();
            query.distinct(true);

            Join<AssignManagement, PermissionDataManagement> permissionDataManagement ;

            if (ValidationUtils.isAcceptSearchListIn(criteria.getLstCompanyCode())) {
                permissionDataManagement = root.join(AssignManagement_.permissionDataManagements, JoinType.LEFT);
                if (criteria.getHasPermission()) {
                    predicates.add(cb.or(
                            cb.equal(root.get("assignUser"), criteria.getUserLogin()),
                            cb.equal(root.get("createdUser"), criteria.getUserLogin()),
                            cb.equal(root.get("updatedUser"), criteria.getUserLogin()),
                            cb.equal(root.get("assignedUser"), criteria.getUserLogin()),
                            cb.and(
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.ASSIGN_MANAGEMENT.code),
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(criteria.getLstCompanyCode())))
                    );
                } else {
                    predicates.add(cb.or(
                            cb.equal(root.get("assignUser"), criteria.getUserLogin()),
                            cb.equal(root.get("createdUser"), criteria.getUserLogin()),
                            cb.equal(root.get("updatedUser"), criteria.getUserLogin()),
                            cb.equal(root.get("assignedUser"), criteria.getUserLogin()))
                    );
                }
            }

            if (StringUtils.isNotEmpty(criteria.getSearch())) {
                predicates.add(cb.or(
                                cb.like(cb.lower(root.get("assignName")), "%" + search + "%"),
                                cb.like(cb.lower(root.get("ticketId").as(String.class)), "%" + search + "%"),
                                cb.like(cb.lower(root.get("serviceRange")), "%" + search + "%"),
//                        cb.like(cb.lower(root.get("assignUser")), "%" + search + "%"),
                                cb.like(cb.lower(root.get("createdUser")), "%" + search + "%"),
                                cb.like(cb.lower(root.get("updatedUser")), "%" + search + "%"),
//                        cb.like(cb.lower(root.get("assignedUser")), "%" + search + "%"),
                                cb.like(cb.lower(root.get("status").as(String.class)), "%" + search + "%"),
                                cb.like(cb.lower(root.get("requestCode")), "%" + search + "%")
                        )
                );
            }

            if (criteria.getStatus() != null) {
                predicates.add(root.get("status").in(criteria.getStatus()));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyCode())){
                predicates.add(cb.and(root.get("companyCode").in(criteria.getListCompanyCode())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyName())){
                predicates.add(cb.and(root.get("companyName").in(criteria.getListCompanyName())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCreatedUser())){
                predicates.add(cb.and(root.get(AssignManagement_.CREATED_USER).in(criteria.getListCreatedUser())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getUpdatedUser())){
                predicates.add(cb.and(root.get(AssignManagement_.UPDATED_USER).in(criteria.getUpdatedUser())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListRequestCode())){
                predicates.add(cb.and(root.get(AssignManagement_.REQUEST_CODE).in(criteria.getListRequestCode())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListAssignName())){
                predicates.add(cb.and(root.get(AssignManagement_.ASSIGN_NAME).in(criteria.getListAssignName())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getAssignUser())){
                predicates.add(cb.and(root.get(AssignManagement_.ASSIGN_USER).in(criteria.getAssignUser())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getAssignedUser())){
                predicates.add(cb.and(root.get(AssignManagement_.ASSIGNED_USER).in(criteria.getAssignedUser())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListNewRequestCode())){
                predicates.add(cb.and(root.get(AssignManagement_.NEW_REQUEST_CODE).in(criteria.getListNewRequestCode())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListServiceId())){
                predicates.add(cb.and(root.get(AssignManagement_.SERVICE_ID).in(criteria.getListServiceId())));
            }

            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    List<String> listLocalDate = Arrays.asList("startDate", "endDate");
                    Boolean isLocalDate = listLocalDate.contains(DateDto.getType());
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);

                            if (isLocalDate)
                                predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal.toLocalDate(), toDateLocal.toLocalDate()));
                            else
                                predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
//                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            if (isLocalDate)
                                predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal.toLocalDate()));
                            else
                                predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
//                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            if (isLocalDate)
                                predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal.toLocalDate()));
                            else
                                predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
//                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }
            
            return cb.and(predicates.toArray(Predicate[]::new));
        };
    }
}
