//package vn.fis.eapprove.business.specification;
//
//import org.springframework.data.jpa.domain.Specification;
//import org.springframework.stereotype.Component;
//import vn.fis.eapprove.business.entity.tenant.MasterData;
//import vn.fis.eapprove.business.model.request.LoadMasterData;
//
//import jakarta.persistence.criteria.Predicate;
//import jakarta.persistence.criteria.Root;
//import jakarta.persistence.criteria.Subquery;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
//@Component
//public class BpmMasterDataSpecification {
//    public Specification<MasterData> loadMasterData(final LoadMasterData criteria) {
//        return (root, query, cb) -> {
//            Predicate pred = null;
//            Root<MasterData> rootMD = query.from(MasterData.class);
//
//
//            query.distinct(true);
//            Boolean checkAddLoc = false;
//            Boolean checkAddDep = false;
//            List<Predicate> listLocPred = new ArrayList<>();
//            List<Predicate> listDepPred = new ArrayList<>();
//            List<Predicate> listFinal = new ArrayList<>();
//            listFinal.add(cb.like(rootMD.get("key_detail"), cb.concat("%", cb.concat(root.get("group_id_detail"), "%"))));
//            listFinal.add(cb.equal(rootMD.get("value_detail"), criteria.getOrgchart()));
//            for (Map<String, Object> exp : criteria.getCondition()) {
//                String expression = exp.get("expression") != null ? exp.get("expression").toString() : "first";
//                String userField = exp.get("filterField").toString();
//                String value = exp.get("value").toString();
//
//                //Subquery------------------------------
////                Subquery<ChartNode> subqNode = query.subquery(ChartNode.class);
////                Root<ChartNode> subqRoot = subqNode.from(ChartNode.class);
////                List<Predicate> subFilter = new ArrayList<>();
////                subFilter.add(cb.equal(subqRoot.get("chartId"), criteria.getOrgchart()));
////
////                Subquery<ChartNode> subqNode2 = query.subquery(ChartNode.class);
////                Root<ChartNode> subqRoot2 = subqNode2.from(ChartNode.class);
////                List<Predicate> subFilter2 = new ArrayList<>();
////                subFilter2.add(cb.equal(subqRoot2.get("chartId"), criteria.getOrgchart()));
//                //--------------------------------------
//
//                switch (exp.get("operator").toString()) {
//                    case "like":
//                        subFilter.add(cb.like(cb.lower(subqRoot.get("title")), "%" + value.toLowerCase() + "%"));
//                        subFilter2.add(cb.like(cb.lower(subqRoot2.get("title")), "%" + value.toLowerCase() + "%"));
//                        break;
//                    default:
//                        subFilter.add(cb.equal(cb.lower(subqRoot.get("title")), value.toLowerCase()));
//                        subFilter2.add(cb.equal(cb.lower(subqRoot2.get("title")), value.toLowerCase()));
//                        break;
//                }
//
//                switch (expression) {
//                    case "and":
//                        switch (exp.get("filterType").toString().toLowerCase()) {
//                            case "user":
//                                switch (exp.get("operator").toString()) {
//                                    case "like":
//                                        pred = cb.and(pred, cb.like(cb.lower(root.get(userField)), "%" + value.toLowerCase() + "%"));
//                                        break;
//                                    default:
//                                        pred = cb.and(pred, cb.equal(root.get(userField), value));
//                                        break;
//                                }
//                                break;
//                            case "location":
//                                subFilter.add(cb.equal(subqRoot.get("type"), 5));
//                                subFilter2.add(cb.equal(subqRoot2.get("type"), 5));
//                                Predicate subPred = cb.or(cb.and(cb.equal(rootNode.get("createdType"), 0),
//                                                rootNode.get("location").in(subqNode2.select(subqRoot2.get("id")).where(cb.and(subFilter2.stream().toArray(Predicate[]::new))))),
//                                        cb.and(cb.equal(rootNode.get("createdType"), 1),
//                                                rootNode.get("location").in(subqNode.select(subqRoot.get("nodeId")).where(cb.and(subFilter.stream().toArray(Predicate[]::new))))));
//                                pred = cb.and(pred, subPred);
//                                break;
//                            case "department":
//                                subFilter.add(cb.equal(subqRoot.get("type"), 4));
//                                subFilter2.add(cb.equal(subqRoot2.get("type"), 4));
//                                Predicate subPredDep = cb.or(cb.and(cb.equal(rootNode.get("createdType"), 0),
//                                                rootNode.get("department").in(subqNode2.select(subqRoot2.get("id")).where(cb.and(subFilter2.stream().toArray(Predicate[]::new))))),
//                                        cb.and(cb.equal(rootNode.get("createdType"), 1),
//                                                rootNode.get("department").in(subqNode.select(subqRoot.get("nodeId")).where(cb.and(subFilter.stream().toArray(Predicate[]::new))))));
//                                pred = cb.and(pred, subPredDep);
//                                break;
//                        }
//                        break;
//                    case "or":
//                        switch (exp.get("filterType").toString().toLowerCase()) {
//                            case "user":
//                                switch (exp.get("operator").toString()) {
//                                    case "like":
//                                        pred = cb.or(pred, cb.like(cb.lower(root.get(userField)), "%" + value.toLowerCase() + "%"));
//                                        break;
//                                    default:
//                                        pred = cb.or(pred, cb.equal(root.get(userField), value));
//                                        break;
//                                }
//                                break;
//                            case "location":
//                                subFilter.add(cb.equal(subqRoot.get("type"), 5));
//                                subFilter2.add(cb.equal(subqRoot2.get("type"), 5));
//                                Predicate subPred = cb.or(cb.and(cb.equal(rootNode.get("createdType"), 0),
//                                                rootNode.get("location").in(subqNode2.select(subqRoot2.get("id")).where(cb.and(subFilter2.stream().toArray(Predicate[]::new))))),
//                                        cb.and(cb.equal(rootNode.get("createdType"), 1),
//                                                rootNode.get("location").in(subqNode.select(subqRoot.get("nodeId")).where(cb.and(subFilter.stream().toArray(Predicate[]::new))))));
//                                pred = cb.or(pred, subPred);
//                                break;
//                            case "department":
//                                subFilter.add(cb.equal(subqRoot.get("type"), 4));
//                                subFilter2.add(cb.equal(subqRoot2.get("type"), 4));
//                                Predicate subPredDep = cb.or(cb.and(cb.equal(rootNode.get("createdType"), 0),
//                                                rootNode.get("department").in(subqNode2.select(subqRoot2.get("id")).where(cb.and(subFilter2.stream().toArray(Predicate[]::new))))),
//                                        cb.and(cb.equal(rootNode.get("createdType"), 1),
//                                                rootNode.get("department").in(subqNode.select(subqRoot.get("nodeId")).where(cb.and(subFilter.stream().toArray(Predicate[]::new))))));
//                                pred = cb.or(pred, subPredDep);
//                                break;
//                        }
//                        break;
//                    default:
//                        switch (exp.get("filterType").toString().toLowerCase()) {
//                            case "user":
//                                switch (exp.get("operator").toString()) {
//                                    case "like":
//                                        pred = cb.like(cb.lower(root.get(userField)), "%" + value.toLowerCase() + "%");
//                                        break;
//                                    default:
//                                        pred = cb.equal(root.get(userField), value);
//                                        break;
//                                }
//                                break;
//                            case "location":
//                                subFilter.add(cb.equal(subqRoot.get("type"), 5));
//                                subFilter2.add(cb.equal(subqRoot2.get("type"), 5));
//                                pred = cb.or(cb.and(cb.equal(rootNode.get("createdType"), 0),
//                                                rootNode.get("location").in(subqNode2.select(subqRoot2.get("id")).where(cb.and(subFilter2.stream().toArray(Predicate[]::new))))),
//                                        cb.and(cb.equal(rootNode.get("createdType"), 1),
//                                                rootNode.get("location").in(subqNode.select(subqRoot.get("nodeId")).where(cb.and(subFilter.stream().toArray(Predicate[]::new))))));
//                                break;
//                            case "department":
//                                subFilter.add(cb.equal(subqRoot.get("type"), 4));
//                                subFilter2.add(cb.equal(subqRoot2.get("type"), 4));
//                                pred = cb.or(cb.and(cb.equal(rootNode.get("createdType"), 0),
//                                                rootNode.get("department").in(subqNode2.select(subqRoot2.get("id")).where(cb.and(subFilter2.stream().toArray(Predicate[]::new))))),
//                                        cb.and(cb.equal(rootNode.get("createdType"), 1),
//                                                rootNode.get("department").in(subqNode.select(subqRoot.get("nodeId")).where(cb.and(subFilter.stream().toArray(Predicate[]::new))))));
//                                break;
//                        }
//                        break;
//                }
//            }
//            listFinal.add(pred);
//            if (!listLocPred.isEmpty()) {
//                listFinal.addAll(listLocPred);
//            }
//            if (!listFinal.isEmpty()) {
//                return cb.and(listFinal.stream().toArray(Predicate[]::new));
//            } else {
//                return pred;
//            }
//        };
//    }
//}
