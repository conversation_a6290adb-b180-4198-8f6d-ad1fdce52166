package vn.fis.eapprove.business.specification;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement_;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser_;
import vn.fis.eapprove.business.domain.template.entity.TemplateManage;
import vn.fis.eapprove.business.domain.template.entity.TemplateManage_;
import vn.fis.eapprove.business.model.request.FillterTempateRequest;
import vn.fis.eapprove.business.model.response.TemplateResponse;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ShareUserTypeEnum;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.criteria.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class BpmTemplateSpecification {
    @Autowired
    private ApplicationContext appContext;
    @Autowired
    private EntityManagerFactory entityManagerFactory;

    public Specification<TemplateManage> filter(final FillterTempateRequest criteria) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (criteria.getUrlName() != null || !criteria.getUrlName().isEmpty()) {
                predicates.add(root.get("urlName").in(criteria.getUrlName()));
            }
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }

    public Specification<TemplateManage> filter1(final FillterTempateRequest criteria) {
        return (root, query, cb) -> {
            List<Predicate> predicates1 = new ArrayList<>();

            if (criteria.getId() != null || !criteria.getId().isEmpty()) {
                predicates1.add(root.get("id").in(criteria.getId()));
            }
            return cb.and(predicates1.stream().toArray(Predicate[]::new));
        };
    }

    public Map<String, Object> getAllTemplate(final FillterTempateRequest criteria) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Map<String, Object> mapFinal = new HashMap<>();

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<TemplateResponse> query = cb.createQuery(TemplateResponse.class);
            CriteriaQuery<Long> queryCount = cb.createQuery(Long.class);
            Join<TemplateManage, PermissionDataManagement> permissionDataManagement = null;
            Join<TemplateManage, PermissionDataManagement> permissionDataManagementCount = null;
            Join<TemplateManage, SharedUser> sharedUser = null;
            Join<TemplateManage, SharedUser> sharedUserCount = null;

            Root<TemplateManage> root = query.from(TemplateManage.class);
            if (criteria.getListCompanyCode() != null && !criteria.getListCompanyCode().contains(CommonConstants.FILTER_SELECT_ALL)) {
                permissionDataManagement = root.join(TemplateManage_.permissionDataManagements);
            }
            if (!ValidationUtils.isNullOrEmpty(criteria.getIsShared()) && criteria.getIsShared()) {
                sharedUser = root.join(TemplateManage_.sharedUsers, JoinType.LEFT);
            }

            Predicate pred = getPredMAllTemplate(criteria, cb, root, permissionDataManagement, sharedUser, query);

            //----------------------------COUNT PRED---------------------//
            Root<TemplateManage> rootCount = queryCount.from(TemplateManage.class);
            if (!criteria.getListCompanyCode().contains(CommonConstants.FILTER_SELECT_ALL)) {
                permissionDataManagementCount = rootCount.join(TemplateManage_.permissionDataManagements);
            }
            if (!ValidationUtils.isNullOrEmpty(criteria.getIsShared()) && criteria.getIsShared()) {
                sharedUserCount = rootCount.join(TemplateManage_.sharedUsers, JoinType.LEFT);
            }
            Predicate predCount = getPredMAllTemplate(criteria, cb, rootCount, permissionDataManagementCount, sharedUserCount, queryCount);
            if (!criteria.getSortBy().equalsIgnoreCase("bpmProcdefDtos")) {
                if (criteria.getSortType().equalsIgnoreCase("asc")) {
                    switch (criteria.getSortBy()) {
                        case "id":
                            query.orderBy(cb.asc(root.get("id")));
                            break;
                        case "templateName":
                            query.orderBy(cb.asc(root.get("templateName")));
                            break;
                        case "urlName":
                            query.orderBy(cb.asc(root.get("urlName")));
                            break;
                        case "status":
                            query.orderBy(cb.asc(root.get("status")));
                            break;
                        case "createdDate":
                            query.orderBy(cb.asc(root.get("createdDate")));
                            break;
                        default:
                            query.orderBy(cb.asc(root.get(criteria.getSortBy())));
                            break;
                    }
                } else {
                    switch (criteria.getSortBy()) {
                        case "id":
                            query.orderBy(cb.desc(root.get("id")));
                            break;
                        case "templateName":
                            query.orderBy(cb.desc(root.get("templateName")));
                            break;
                        case "urlName":
                            query.orderBy(cb.desc(root.get("urlName")));
                            break;
                        case "status":
                            query.orderBy(cb.desc(root.get("status")));
                            break;
                        case "createdDate":
                            query.orderBy(cb.desc(root.get("createdDate")));
                            break;
                        default:
                            query.orderBy(cb.desc(root.get(criteria.getSortBy())));
                            break;
                    }
                }
            }


            query.multiselect(
                    root.get("id"),
                    root.get("templateName"),
                    root.get("description"),
                    root.get("status"),
                    root.get("template"),
                    root.get("createdDate"),
                    root.get("createdUser"),
                    root.get("modifiedDate"),
                    root.get("modifiedUser"),
                    root.get("urlName"),
                    root.get("companyCode"),
                    root.get("companyName"),
                    root.get(TemplateManage_.specialCompanyCode)
            ).where(pred);
            query.distinct(true);
            List<TemplateResponse> listResult = em.createQuery(query) != null ? em.createQuery(query).
                    setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList() : new ArrayList<>();
            queryCount.select(cb.countDistinct(rootCount)).where(predCount);
            Long count = em.createQuery(queryCount).getSingleResult();
            mapFinal.put("count", count);
            mapFinal.put("data", listResult);
            return mapFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;

        } finally {
            if (em != null)
                em.close();
        }
    }

    public List<TemplateResponse>  getAllTemplateExportExcel(final FillterTempateRequest criteria) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<TemplateResponse> query = cb.createQuery(TemplateResponse.class);
            Join<TemplateManage, PermissionDataManagement> permissionDataManagement = null;
            Join<TemplateManage, SharedUser> sharedUser = null;

            Root<TemplateManage> root = query.from(TemplateManage.class);
            if (!ValidationUtils.isNullOrEmpty(criteria.getIsShared()) && criteria.getIsShared()) {
                sharedUser = root.join(TemplateManage_.sharedUsers, JoinType.LEFT);
            }
            if (!criteria.getListCompanyCode().contains(CommonConstants.FILTER_SELECT_ALL)) {
                permissionDataManagement = root.join(TemplateManage_.permissionDataManagements);
            }
            Predicate pred = getPredMAllTemplate(criteria, cb, root, permissionDataManagement, sharedUser, query);

            if (!criteria.getSortBy().equalsIgnoreCase("bpmProcdefDtos")) {
                if (criteria.getSortType().equalsIgnoreCase("asc")) {
                    switch (criteria.getSortBy()) {
                        case "id":
                            query.orderBy(cb.asc(root.get("id")));
                            break;
                        case "templateName":
                            query.orderBy(cb.asc(root.get("templateName")));
                            break;
                        case "urlName":
                            query.orderBy(cb.asc(root.get("urlName")));
                            break;
                        case "status":
                            query.orderBy(cb.asc(root.get("status")));
                            break;
                        case "createdDate":
                            query.orderBy(cb.asc(root.get("createdDate")));
                            break;
                        default:
                            query.orderBy(cb.asc(root.get(criteria.getSortBy())));
                            break;
                    }
                } else {
                    switch (criteria.getSortBy()) {
                        case "id":
                            query.orderBy(cb.desc(root.get("id")));
                            break;
                        case "templateName":
                            query.orderBy(cb.desc(root.get("templateName")));
                            break;
                        case "urlName":
                            query.orderBy(cb.desc(root.get("urlName")));
                            break;
                        case "status":
                            query.orderBy(cb.desc(root.get("status")));
                            break;
                        case "createdDate":
                            query.orderBy(cb.desc(root.get("createdDate")));
                            break;
                        default:
                            query.orderBy(cb.desc(root.get(criteria.getSortBy())));
                            break;
                    }
                }
            }
            query.multiselect(
                    root.get("id"),
                    root.get("templateName"),
                    root.get("description"),
                    root.get("status"),
                    root.get("templateName"),
                    root.get("createdDate"),
                    root.get("createdUser"),
                    root.get("modifiedDate"),
                    root.get("modifiedUser"),
                    root.get("urlName"),
                    root.get("companyCode"),
                    root.get("companyName"),
                    root.get("specialCompanyCode")
            ).where(pred);
            return em.createQuery(query).getResultList();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;

        } finally {
            if (em != null)
                em.close();
        }
    }

    public Predicate getPredMAllTemplate(FillterTempateRequest criteria,
                                         CriteriaBuilder cb,
                                         Root<TemplateManage> root,
                                         Join<TemplateManage, PermissionDataManagement> permissionDataManagement,
                                         Join<TemplateManage, SharedUser> sharedUser,
                                         CriteriaQuery<?> query) {
        try {
            List<Predicate> predicates = new ArrayList<>();
            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }
            if (criteria.getStatus() != null && !criteria.getStatus().contains(CommonConstants.FILTER_SELECT_ALL)) {
                predicates.add(root.get("status").in(criteria.getStatus()));
            }
            if (!ValidationUtils.isNullOrEmpty(criteria.getSearch())) {
                Predicate predicate = cb.or(cb.like(cb.lower(root.get(TemplateManage_.id).as(String.class)), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get(TemplateManage_.templateName)), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get(TemplateManage_.urlName)), "%" + criteria.getSearch().trim().toLowerCase() + "%")
                );
                predicates.add(predicate);
            }

            // Shared data
            if (!ValidationUtils.isNullOrEmpty(criteria.getIsShared()) && criteria.getIsShared()) {
                predicates.add(cb.and(
                        cb.equal(sharedUser.get(SharedUser_.referenceType), ShareUserTypeEnum.TEMPLATE.type),
                        cb.equal(sharedUser.get(SharedUser_.email), criteria.getUsername()))
                );
            }
            // check permission data (companyCode = -1 -> next)
            else if (!criteria.getListCompanyCode().contains(CommonConstants.FILTER_SELECT_ALL)) {
                if (!ValidationUtils.isNullOrEmpty(criteria.getLstGroupPermissionId())) {
                    // có phân quyền theo nhóm ưu tiên lấy các bản ghi trong nhóm + theo companyCode member admin (nếu có)
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.TEMPLATE_MANAGE.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(criteria.getListCompanyCodeMemberAdmin()),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL),
                                    // Phân quyền theo nhóm
                                    root.get(TemplateManage_.id).in(criteria.getLstGroupPermissionId())
                            )
                    ));
                } else {
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.TEMPLATE_MANAGE.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(criteria.getListCompanyCode()),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
                            )
                    ));
                }
            }

            // filter special clone template
            if (!ValidationUtils.isNullOrEmpty(criteria.getSpecialFlow())) {
                if (criteria.getSpecialFlow()) { // search template con
                    predicates.add(cb.isTrue(root.get(TemplateManage_.specialFlow)));
                } else {
                    predicates.add(cb.isNull(root.get(TemplateManage_.specialFlow)));
                }
            }

            if (criteria.getModifiedUser() != null && !criteria.getModifiedUser().contains(CommonConstants.FILTER_SELECT_ALL)) {
                predicates.add(root.get("modifiedUser").in(criteria.getModifiedUser()));
            }

            if (criteria.getTemplateName() != null && !criteria.getTemplateName().contains(CommonConstants.FILTER_SELECT_ALL)) {
                predicates.add(root.get("templateName").in(criteria.getTemplateName()));
            }

            if (criteria.getUrlName() != null && !criteria.getUrlName().contains(CommonConstants.FILTER_SELECT_ALL)) {
                predicates.add(root.get("urlName").in(criteria.getUrlName()));
            }

            if (criteria.getCreatedUser() != null && !criteria.getCreatedUser().contains(CommonConstants.FILTER_SELECT_ALL)) {
                predicates.add(root.get("createdUser").in(criteria.getCreatedUser()));
            }

            if (criteria.getCompanyCode() != null && !criteria.getCompanyCode().contains(CommonConstants.FILTER_SELECT_ALL)) {
                predicates.add(root.get("companyCode").in(criteria.getCompanyCode()));
            }

            if (criteria.getCompanyName() != null && !criteria.getCompanyName().contains(CommonConstants.FILTER_SELECT_ALL)) {
                predicates.add(root.get("companyName").in(criteria.getCompanyName()));
            }

            return cb.and(predicates.toArray(Predicate[]::new));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }


    public Map<String, Object> getAllTemplateSystemGroup(final FillterTempateRequest criteria) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Map<String, Object> mapFinal = new HashMap<>();

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<TemplateResponse> query = cb.createQuery(TemplateResponse.class);
            Join<TemplateManage, PermissionDataManagement> permissionDataManagement = null;
            Join<TemplateManage, SharedUser> sharedUser = null;

            Root<TemplateManage> root = query.from(TemplateManage.class);
            if (criteria.getListCompanyCode() != null && !criteria.getListCompanyCode().contains(CommonConstants.FILTER_SELECT_ALL)) {
                permissionDataManagement = root.join(TemplateManage_.permissionDataManagements);
            }
            if (!ValidationUtils.isNullOrEmpty(criteria.getIsShared()) && criteria.getIsShared()) {
                sharedUser = root.join(TemplateManage_.sharedUsers, JoinType.LEFT);
            }

            Predicate pred = getPredMAllTemplate(criteria, cb, root, permissionDataManagement, sharedUser, query);

            query.multiselect(
                    root.get("id"),
                    root.get("templateName"),
                    root.get("description"),
                    root.get("status"),
                    root.get("template"),
                    root.get("createdDate"),
                    root.get("createdUser"),
                    root.get("modifiedDate"),
                    root.get("modifiedUser"),
                    root.get("urlName"),
                    root.get("companyCode"),
                    root.get("companyName"),
                    root.get("specialCompanyCode")
            );
            if(pred != null)
                query.where(pred);
            query.distinct(true);
            List<TemplateResponse> listResult = em.createQuery(query) != null ?
                    em.createQuery(query).getResultList() : new ArrayList<>();
            mapFinal.put("data", listResult);
            return mapFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;

        } finally {
            if (em != null)
                em.close();
        }
    }


}
