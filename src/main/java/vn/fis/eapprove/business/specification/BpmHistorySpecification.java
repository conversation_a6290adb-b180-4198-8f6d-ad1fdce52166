package vn.fis.eapprove.business.specification;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.criteria.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.bpm.entity.BpmHistory;
import vn.fis.eapprove.business.domain.bpm.entity.BpmHistory_;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTask_;
import vn.fis.eapprove.business.model.request.HistoryDto;
import vn.fis.eapprove.business.model.response.BpmHistoryResponse;
import vn.fis.spro.common.util.ValidationUtils;



import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class BpmHistorySpecification {
    @Autowired
    private ApplicationContext appContext;

    @Autowired
    private EntityManagerFactory entityManagerFactory;

    public Specification<BpmHistory> filter(final HistoryDto criteria) {

        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (criteria.getTicketId() != null) {
                predicates.add(cb.equal(root.get("ticketId"), criteria.getTicketId()));
            }
            if (StringUtils.isNotEmpty(criteria.getProcInstId())) {
                predicates.add(cb.equal(cb.lower(root.get("procInstId")), criteria.getProcInstId().trim().toLowerCase()));
            }
            if (StringUtils.isNotEmpty(criteria.getAction())) {
                predicates.add(cb.equal(cb.lower(root.get("action")), criteria.getAction().trim().toLowerCase()));
            }
            if (StringUtils.isNotEmpty(criteria.getNote())) {
                predicates.add(cb.like(cb.lower(root.get("note")), "%" + criteria.getNote().trim().toLowerCase() + "%"));
            }
            if (StringUtils.isNotEmpty(criteria.getFromTask())) {
                predicates.add(cb.like(cb.lower(root.get("fromTask")), "%" + criteria.getFromTask().trim().toLowerCase() + "%"));
            }
            if (StringUtils.isNotEmpty(criteria.getToTask())) {
                predicates.add(cb.like(cb.lower(root.get("toTask")), "%" + criteria.getToTask().trim().toLowerCase() + "%"));
            }
            if (StringUtils.isNotEmpty(criteria.getActionUser())) {
                predicates.add(cb.like(cb.lower(root.get("actionUser")), "%" + criteria.getActionUser().trim().toLowerCase() + "%"));
            }
            if (StringUtils.isNotEmpty(criteria.getSearch())) {
                predicates.add(cb.or(cb.like(cb.lower(root.get("actionUser")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("fromTask")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("toTask")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("note")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("action")), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
            }
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }

    public Map<String, Object> queryHistory(final HistoryDto criteria) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Map<String, Object> mapFinal = new HashMap<>();

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<BpmHistoryResponse> query = cb.createQuery(BpmHistoryResponse.class);
            Root<BpmHistory> root = query.from(BpmHistory.class);
            Predicate pred = getPredHistory(criteria, cb, root);

            CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
            Root<BpmHistory> rootCount = countQuery.from(BpmHistory.class);
            Predicate predCount = getPredHistory(criteria, cb, rootCount);

            //-------------subquery from task------------------------//
            Subquery<String> subqFromTask = query.subquery(String.class);
            Root<BpmTask> rootSubTask1 = subqFromTask.from(BpmTask.class);
            Predicate subPred1 = cb.and(
                    cb.equal(rootSubTask1.get(BpmTask_.taskDefKey), root.get(BpmHistory_.fromTaskKey)),
                    cb.equal(rootSubTask1.get(BpmTask_.taskProcInstId), root.get(BpmHistory_.procInstId))
            );

            //-------------subquery from task------------------------//
            Subquery<String> subqToTask = query.subquery(String.class);
            Root<BpmTask> rootSubTask2 = subqToTask.from(BpmTask.class);
            Predicate subPred2 = cb.and(
                    cb.equal(rootSubTask2.get(BpmTask_.taskDefKey), root.get(BpmHistory_.toTaskKey)),
                    cb.equal(rootSubTask2.get(BpmTask_.taskProcInstId), root.get(BpmHistory_.procInstId))
            );

            //------------------------sort----------------------------------------------//
            if (criteria.getSortType().equalsIgnoreCase("asc")) {
                switch (criteria.getSortBy()) {
                    case "id":
                        query.orderBy(cb.asc(root.get("id")));
                        break;
                    case "time":
                        query.orderBy(cb.asc(root.get("createdTime")));
                        break;
                    case "action":
                        query.orderBy(cb.asc(root.get("action")));
                        break;
                    case "actionUser":
                        query.orderBy(cb.asc(root.get("actionUser")));
                        break;
                    case "note":
                        query.orderBy(cb.asc(root.get("note")));
                        break;
                    case "fromTask":
                        query.orderBy(cb.asc(root.get("fromTask")));
                        break;
                    case "toTask":
                        query.orderBy(cb.asc(root.get("toTask")));
                        break;
                }
            } else {
                switch (criteria.getSortBy()) {
                    case "id":
                        query.orderBy(cb.desc(root.get("id")));
                        break;
                    case "time":
                        query.orderBy(cb.desc(root.get("createdTime")));
                        break;
                    case "action":
                        query.orderBy(cb.desc(root.get("action")));
                        break;
                    case "actionUser":
                        query.orderBy(cb.desc(root.get("actionUser")));
                        break;
                    case "note":
                        query.orderBy(cb.desc(root.get("note")));
                        break;
                    case "toTask":
                        query.orderBy(cb.desc(root.get("toTask")));
                        break;
                    case "fromTask":
                        query.orderBy(cb.desc(root.get("fromTask")));
                        break;
                }
            }

            //-----------------------------query data---------------------------------//
            query.multiselect(
                    root.get("id"),
                    root.get("ticketId"),
                    root.get("procInstId"),
                    root.get("taskInstId"),
                    root.get("fromTask"),
                    root.get("toTask"),
                    subqFromTask.select(rootSubTask1.get("taskName")).distinct(true).where(subPred1).getSelection(),
                    subqToTask.select(rootSubTask2.get("taskName")).distinct(true).where(subPred2).getSelection(),
                    root.get("createdTime"),
                    root.get("action"),
                    root.get("actionUser"),
                    root.get("taskDefKey"),
                    root.get("note"),
                    root.get("affectedTask"),
                    root.get("taskType"),
                    root.get("oldTaskId"),
                    root.get("oldProcInstId"),
                    root.get("attachFiles"),
                    root.get("attachFilesName"),
                    root.get("attachFilesSize"),
                    root.get("taskAssignee"),
                    root.get("receivedTime"),
                    root.get("actionUserInfo")
            ).where(pred);
            List<BpmHistoryResponse> listResult = em.createQuery(query) != null ? em.createQuery(query).
                    setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList() : new ArrayList<>();
            countQuery.select(cb.countDistinct(rootCount)).where(predCount);
            Long count = em.createQuery(countQuery).getSingleResult();

            mapFinal.put("data", listResult);
            mapFinal.put("count", count);
            return mapFinal;
        } catch (Exception e) {
            e.printStackTrace();
            return new HashMap<>();
        } finally {
            if (em != null)
                em.close();
        }
    }

    public Predicate getPredHistory(HistoryDto criteria, CriteriaBuilder cb, Root<BpmHistory> root) {
        List<Predicate> predicates = new ArrayList<>();

        if (!ValidationUtils.isNullOrEmpty(criteria.getTicketId())) {
            predicates.add(cb.equal(root.get("ticketId"), criteria.getTicketId()));
        }
        if (!ValidationUtils.isNullOrEmpty(criteria.getListAction())) {
            predicates.add(cb.lower(root.get("action")).in(criteria.getListAction()));
        }
        if (StringUtils.isNotEmpty(criteria.getNote())) {
            predicates.add(cb.like(cb.lower(root.get("note")), "%" + criteria.getNote().trim().toLowerCase() + "%"));
        }
        if (StringUtils.isNotEmpty(criteria.getFromTask())) {
            predicates.add(cb.like(cb.lower(root.get("fromTask")), "%" + criteria.getFromTask().trim().toLowerCase() + "%"));
        }
        if (StringUtils.isNotEmpty(criteria.getToTask())) {
            predicates.add(cb.like(cb.lower(root.get("toTask")), "%" + criteria.getToTask().trim().toLowerCase() + "%"));
        }
        if (!ValidationUtils.isNullOrEmpty(criteria.getListActionUser())) {
            predicates.add(root.get("actionUser").in(criteria.getListActionUser()));
        }
        if (StringUtils.isNotEmpty(criteria.getTaskDefKey())) {
            predicates.add(cb.like(cb.lower(root.get("taskDefKey")), "%" + criteria.getTaskDefKey().trim().toLowerCase() + "%"));
        }
        if (StringUtils.isNotEmpty(criteria.getSearch())) {
            predicates.add(cb.or(cb.like(cb.lower(root.get("actionUser")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                    cb.like(cb.lower(root.get("fromTask")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                    cb.like(cb.lower(root.get("toTask")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                    cb.like(cb.lower(root.get("note")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                    cb.like(cb.lower(root.get("action")), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
        }
        // DXG_ERP_TH-36234 - bỏ  GET_REQUEST_UPDATE, AFFECTED_BY_RU
        predicates.add(root.get("action").in("AFFECTED_BY_RU", "GET_REQUEST_UPDATE").not());

        return cb.and(predicates.toArray(Predicate[]::new));
    }


    public Specification<BpmHistory> filterRU(final HistoryDto criteria) {

        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotEmpty(criteria.getAction())) {
                predicates.add(cb.like(cb.lower(root.get("action")), "%" + criteria.getAction().trim().toLowerCase() + "%"));
            }
            if (StringUtils.isNotEmpty(criteria.getProcInstId())) {
                predicates.add(cb.like(cb.lower(root.get("procInstId")), "%" + criteria.getProcInstId().trim().toLowerCase() + "%"));
            }
            if (StringUtils.isNotEmpty(criteria.getFromTask())) {
                predicates.add(cb.like(cb.lower(root.get("fromTask")), "%" + criteria.getFromTask().trim() + "%"));
            }
            if (StringUtils.isNotEmpty(criteria.getToTask())) {
                predicates.add(cb.like(cb.lower(root.get("toTask")), "%" + criteria.getToTask().trim() + "%"));
            }
            if (StringUtils.isNotEmpty(criteria.getSearch())) {
                predicates.add(cb.or(cb.like(cb.lower(root.get("fromTask")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("fromTask")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("note")), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
            }
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }
}
