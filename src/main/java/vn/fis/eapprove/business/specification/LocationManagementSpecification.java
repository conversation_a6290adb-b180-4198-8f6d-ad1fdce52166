package vn.fis.eapprove.business.specification;

import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.location.entity.LocationManagement;
import vn.fis.eapprove.business.domain.location.entity.LocationManagement_;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement_;
import vn.fis.eapprove.business.dto.LocationManagementFilterDto;

import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;


import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

@Component
public class LocationManagementSpecification {

    public Specification<LocationManagement> filter(final LocationManagementFilterDto criteria, List<String> lstCompanyCode) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotEmpty(criteria.getSearch())) {
                predicates.add(cb.or(cb.like(cb.lower(root.get("abbreviations")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("locationName")), "%" + criteria.getSearch().trim().toLowerCase() + "%"))
                );
            }

            if (!lstCompanyCode.contains(CommonConstants.FILTER_SELECT_ALL)) {
                Join<LocationManagement, PermissionDataManagement> permissionDataManagement = root.join(LocationManagement_.permissionDataManagements);
                predicates.add(cb.and(
                        cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.LOCATION_MANAGEMENT.code),
                        cb.or(
                                permissionDataManagement.get(PermissionDataManagement_.companyCode).in(lstCompanyCode),
                                cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
                        )
                ));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyCode())){
                predicates.add(cb.and(root.get("companyCode").in(criteria.getListCompanyCode())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyName())){
                predicates.add(cb.and(root.get("companyName").in(criteria.getListCompanyName())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCreatedUser())){
                predicates.add(cb.and(root.get(LocationManagement_.CREATED_USER).in(criteria.getListCreatedUser())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListModifiedUser())){
                predicates.add(cb.and(root.get(LocationManagement_.MODIFIED_USER).in(criteria.getListModifiedUser())));
            }


            if(ValidationUtils.isAcceptSearchListIn(criteria.getListLocationName())){
                predicates.add(cb.and(root.get(LocationManagement_.LOCATION_NAME).in(criteria.getListLocationName())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListAddress())){
                predicates.add(cb.and(root.get(LocationManagement_.ADDRESS).in(criteria.getListAddress())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListAbbreviations())){
                predicates.add(cb.and(root.get(LocationManagement_.ABBREVIATIONS).in(criteria.getListAbbreviations())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListWorkingTimeCode())){
                predicates.add(cb.and(root.get(LocationManagement_.WORKING_TIME_CODE).in(criteria.getListWorkingTimeCode())));
            }

            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                        }
                    }
                }
            }





            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }
}
