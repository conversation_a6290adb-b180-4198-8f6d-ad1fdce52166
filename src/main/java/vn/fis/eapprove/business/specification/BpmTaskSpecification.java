package vn.fis.eapprove.business.specification;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import vn.fis.eapprove.business.constant.BusinessEnum;
import vn.fis.eapprove.business.domain.assign.entity.AssignManagement;
import vn.fis.eapprove.business.domain.assign.entity.AssignManagement_;
import vn.fis.eapprove.business.domain.assistant.entity.Assistant;
import vn.fis.eapprove.business.domain.assistant.entity.Assistant_;
import vn.fis.eapprove.business.domain.authority.entity.AuthorityManagement;
import vn.fis.eapprove.business.domain.authority.entity.AuthorityManagement_;
import vn.fis.eapprove.business.domain.bpm.entity.*;
import vn.fis.eapprove.business.domain.bpm.repository.BpmTaskRepository;
import vn.fis.eapprove.business.domain.changeAssignee.entity.ChangeAssigneeHistory;
import vn.fis.eapprove.business.domain.changeAssignee.entity.ChangeAssigneeHistory_;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement_;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage_;
import vn.fis.eapprove.business.dto.BpmTaskDto;
import vn.fis.eapprove.business.model.PrivateFilterRequest;
import vn.fis.eapprove.business.model.request.BpmTicketFilterSearchRequest;
import vn.fis.eapprove.business.model.request.MyTaskRequest;
import vn.fis.eapprove.business.model.response.*;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.ProcInstConstants;
import vn.fis.spro.common.constants.TaskConstants;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.criteria.*;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BpmTaskSpecification {

    @Autowired
    private ApplicationContext appContext;

    @Autowired
    private EntityManagerFactory entityManagerFactory;

    @Autowired
    private BpmTaskRepository bpmTaskRepository;

    public Specification<BpmTask> filter(final BpmTaskDto criteria) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            query.distinct(true);
            predicates.add(cb.notEqual(root.get("taskStatus"), "DELETED_BY_EDIT"));
            if (StringUtils.isNotBlank(criteria.getTaskId())) {
                predicates.add(cb.equal(root.get("taskId"), criteria.getTaskId()));
            }
            if (StringUtils.isNotBlank(criteria.getTaskStatus())) {
                predicates.add(cb.equal(root.get("taskStatus"), criteria.getTaskStatus().toUpperCase()));
            }
            if (criteria.getListStatus() != null && !criteria.getListStatus().isEmpty()) {
                predicates.add(root.get("taskStatus").in(criteria.getListStatus()));
            }
            if (StringUtils.isNotBlank(criteria.getTaskName())) {
                predicates.add(cb.like(root.get("taskName"), "%" + criteria.getTaskName().trim() + "%"));
            }
            if (StringUtils.isNotBlank(criteria.getTaskDefKey()) &&
                    (criteria.getAffected() == null || criteria.getAffected().isEmpty())) {
                predicates.add(cb.equal(root.get("taskDefKey"), criteria.getTaskDefKey().trim()));
            } else if (StringUtils.isNotBlank(criteria.getTaskDefKey()) &&
                    (criteria.getAffected() != null && !criteria.getAffected().isEmpty())) {
                predicates.add(cb.or(cb.equal(root.get("taskDefKey"), criteria.getTaskDefKey().trim()),
                        root.get("taskDefKey").in(criteria.getAffected())));
            } else if (!CollectionUtils.isEmpty(criteria.getAffected())) {
                predicates.add(root.get("taskDefKey").in(criteria.getAffected()));
            }
            if (StringUtils.isNotBlank(criteria.getTaskAssignee())) {
                Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistory = root.join(BpmTask_.changeAssigneeHistories, JoinType.LEFT);
                predicates.add(cb.or(
                        cb.and(
                                cb.isNotNull(root.get("taskAssignee")),
                                cb.equal(root.get("taskAssignee"), criteria.getTaskAssignee().trim())
                        ),
                        cb.and(
                                cb.isTrue(root.get(BpmTask_.assignType)),
                                cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.orgAssignee), criteria.getTaskAssignee())
                        ),
                        cb.isNull(root.get("taskAssignee"))
                ));
            }
            if (criteria.getTicketId() != null) {
                Root<BpmProcInst> rootTicket = query.from(BpmProcInst.class);
                predicates.add(cb.and(cb.equal(rootTicket.get("ticketProcInstId"), root.get("taskProcInstId")),
                        cb.equal(rootTicket.get("ticketId"), criteria.getTicketId())));
            } else {
                if (StringUtils.isNotBlank(criteria.getProcInstId())) {
                    predicates.add(cb.equal(root.get("taskProcInstId"), criteria.getProcInstId().trim()));
                }
            }
            return cb.and(predicates.toArray(Predicate[]::new));
        };
    }

    public Map<String, Object> getMyTask(final MyTaskRequest criteria) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Map<String, Object> mapFinal = new HashMap<>();

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<MyTaskResponse> query = cb.createQuery(MyTaskResponse.class);
            CriteriaQuery<Long> queryCount = cb.createQuery(Long.class);
            LocalDate now = LocalDate.now();

            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, BpmHistory> bpmHistory = null;
            Join<BpmProcInst, BpmHistory> bpmHistoryCount = null;
            Join<BpmProcInst, BpmTask> bpmTask = bpmProcInst.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
            Join<BpmProcInst, AuthorityManagement> authRoot = bpmProcInst.join((BpmProcInst_.authorityManagements), JoinType.LEFT);
            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join((BpmProcInst_.servicePackage), JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priorityManagement;
            priorityManagement = bpmProcInst.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join((ServicePackage_.bpmProcdef), JoinType.LEFT);
            Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistory = bpmTask.join(BpmTask_.changeAssigneeHistories, JoinType.LEFT);
            Join<BpmTask, BpmTaskUser> bpmTaskUser = bpmTask.join((BpmTask_.bpmTaskUsers), JoinType.LEFT);
            Join<ChangeAssigneeHistory, AssignManagement> assignManagement = changeAssigneeHistory.join(ChangeAssigneeHistory_.assignManagement, JoinType.LEFT);
            if (criteria.getTaskStatus().equals("CANCEL")) {
                bpmHistory = bpmProcInst.join(BpmProcInst_.bpmHistories, JoinType.LEFT);
            }


            //get predicate
            Predicate pred = getPredMyTask(criteria, cb, bpmProcInst, bpmTask, servicePackage, bpmProcdef, priorityManagement, changeAssigneeHistory, assignManagement, bpmTaskUser, bpmHistory, query, authRoot);


            Root<BpmProcInst> bpmProcInstCount = queryCount.from(BpmProcInst.class);
            Join<BpmProcInst, BpmTask> bpmTaskCount = bpmProcInstCount.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
            Join<BpmProcInst, AuthorityManagement> authRootCount = bpmProcInstCount.join((BpmProcInst_.authorityManagements), JoinType.LEFT);
            Join<BpmProcInst, ServicePackage> servicePackageCount = bpmProcInstCount.join((BpmProcInst_.servicePackage), JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priorityManagementCount = bpmProcInstCount.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
            Join<ServicePackage, BpmProcdef> bpmProcdefCount = servicePackageCount.join((ServicePackage_.bpmProcdef), JoinType.LEFT);
            Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistoryCount = bpmTaskCount.join(BpmTask_.changeAssigneeHistories, JoinType.LEFT);
            Join<BpmTask, BpmTaskUser> countBpmTaskUser = bpmTaskCount.join((BpmTask_.bpmTaskUsers), JoinType.LEFT);
            Join<ChangeAssigneeHistory, AssignManagement> countAssignManagement = changeAssigneeHistoryCount.join(ChangeAssigneeHistory_.assignManagement, JoinType.LEFT);
            if (criteria.getTaskStatus().equals("CANCEL")) {
                bpmHistoryCount = bpmProcInstCount.join(BpmProcInst_.bpmHistories, JoinType.LEFT);
            }

            Predicate predCount = getPredMyTask(criteria, cb, bpmProcInstCount, bpmTaskCount, servicePackageCount, bpmProcdefCount, priorityManagementCount, changeAssigneeHistoryCount, countAssignManagement, countBpmTaskUser, bpmHistoryCount, queryCount, authRootCount);


            if (!criteria.getSortBy().equalsIgnoreCase("remainingTime")) {
                if (criteria.getSortType().equalsIgnoreCase("asc")) {
                    switch (criteria.getSortBy()) {
                        case "ticketId":
                            query.orderBy(cb.asc(bpmProcInst.get("ticketId")));
                            break;
                        case "createdUser":
                            query.orderBy(cb.asc(bpmProcInst.get("createdUser")));
                            break;
                        case "procTitle":
                            query.orderBy(cb.asc(bpmProcInst.get("ticketTitle")));
                            break;
                        case "serviceName":
                            query.orderBy(cb.asc(servicePackage.get("serviceName")));
                            break;
                        case "cancelReason":
                            query.orderBy(cb.asc(bpmProcInst.get("cancelReason")));
                            break;
                        case "taskPriority":
                            query.orderBy(cb.asc(priorityManagement.get(PriorityManagement_.name)));
                            break;
                        case "taskCanceledTime":
                            query.orderBy(cb.asc(bpmProcInst.get("ticketCanceledTime")));
                            break;
                        case "ticketCreatedTime":
                            query.orderBy(cb.asc(bpmProcInst.get("ticketCreatedTime")));
                            break;
                        case "requestCode":
                            query.orderBy(cb.asc(bpmProcInst.get("requestCode")));
                            break;
                        case "orgAssignee":
                            query.orderBy(cb.asc(changeAssigneeHistory.get("orgAssignee")));
                            break;
                        case "companyCode":
                            query.orderBy(cb.asc(bpmProcInst.get("companyCode")));
                            break;
                        case "cancelActionUser":
                            query.orderBy(cb.asc(bpmHistory.get("actionUser")));
                            break;
                        case "chartNodeName":
                            query.orderBy(cb.asc(cb.coalesce(bpmProcInst.get(BpmProcInst_.chartNodeName), "")));
                            break;
                        case "ticketStatus":
                            query.orderBy(cb.asc(bpmProcInst.get(BpmProcInst_.TICKET_STATUS)));
                            break;
                        default:
                            query.orderBy(cb.asc(bpmTask.get(criteria.getSortBy())));
                            break;
                    }
                } else {
                    switch (criteria.getSortBy()) {
                        case "ticketId":
                            query.orderBy(cb.desc(bpmProcInst.get("ticketId")));
                            break;
                        case "createdUser":
                            query.orderBy(cb.desc(bpmProcInst.get("createdUser")));
                            break;
                        case "procTitle":
                            query.orderBy(cb.desc(bpmProcInst.get("ticketTitle")));
                            break;
                        case "serviceName":
                            query.orderBy(cb.desc(servicePackage.get("serviceName")));
                            break;
                        case "cancelReason":
                            query.orderBy(cb.desc(bpmProcInst.get("cancelReason")));
                            break;
                        case "taskPriority":
                            query.orderBy(cb.desc(priorityManagement.get(PriorityManagement_.name)));
                            break;
                        case "taskCanceledTime":
                            query.orderBy(cb.desc(bpmProcInst.get("ticketCanceledTime")));
                            break;
                        case "ticketCreatedTime":
                            query.orderBy(cb.desc(bpmProcInst.get("ticketCreatedTime")));
                            break;
                        case "requestCode":
                            query.orderBy(cb.desc(bpmProcInst.get("requestCode")));
                            break;
                        case "orgAssignee":
                            query.orderBy(cb.desc(changeAssigneeHistory.get("orgAssignee")));
                            break;
                        case "companyCode":
                            query.orderBy(cb.desc(bpmProcInst.get("companyCode")));
                            break;
                        case "cancelActionUser":
                            query.orderBy(cb.desc(bpmHistory.get("actionUser")));
                            break;
                        case "chartNodeName":
                            query.orderBy(cb.desc(cb.coalesce(bpmProcInst.get(BpmProcInst_.chartNodeName), "")));
                            break;
                        case "ticketStatus":
                            query.orderBy(cb.desc(bpmProcInst.get(BpmProcInst_.TICKET_STATUS)));
                            break;
                        default:
                            query.orderBy(cb.desc(bpmTask.get(criteria.getSortBy())));
                            break;
                    }
                }
            }

            List<Selection<?>> inputs = new ArrayList<Selection<?>>();
            inputs.add(bpmTask.get(BpmTask_.id));
            inputs.add(bpmProcInst.get(BpmProcInst_.ticketId));
            inputs.add(bpmTask.get(BpmTask_.taskId));
            inputs.add(bpmProcInst.get(BpmProcInst_.ticketProcInstId));
            inputs.add(bpmProcInst.get(BpmProcInst_.ticketEndActId));
            inputs.add(bpmProcInst.get(BpmProcInst_.ticketStartActId));
            inputs.add(bpmProcInst.get(BpmProcInst_.createdUser));
            inputs.add(bpmTask.get(BpmTask_.taskDefKey));
            inputs.add(bpmTask.get(BpmTask_.assignType));
            inputs.add(bpmTask.get(BpmTask_.taskStatus));
            inputs.add(bpmProcInst.get(BpmProcInst_.ticketTitle));
            inputs.add(bpmTask.get(BpmTask_.taskName));
            inputs.add(servicePackage.get(ServicePackage_.serviceName));
            inputs.add(bpmProcInst.get(BpmProcInst_.priorityId));
            inputs.add(bpmProcInst.get(BpmProcInst_.ticketStartUserId));
            inputs.add(bpmTask.get(BpmTask_.taskCreatedTime));
            inputs.add(bpmTask.get(BpmTask_.taskFinishedTime));
            inputs.add(bpmTask.get(BpmTask_.taskStartedTime));
            inputs.add(bpmProcInst.get(BpmProcInst_.ticketCanceledTime));
            inputs.add(bpmProcInst.get(BpmProcInst_.cancelReason));
            inputs.add(bpmTask.get(BpmTask_.slaFinish));
            inputs.add(bpmTask.get(BpmTask_.slaResponse));
            inputs.add(bpmProcInst.get(BpmProcInst_.ticketProcDefId));
            inputs.add(bpmTask.get(BpmTask_.slaFinishTime));
            inputs.add(bpmProcInst.get(BpmProcInst_.ticketDescription));
            inputs.add(priorityManagement.get(PriorityManagement_.name));
            inputs.add(priorityManagement.get(PriorityManagement_.color));
            inputs.add(bpmProcInst.get(BpmProcInst_.requestCode));
            inputs.add(bpmProcInst.get(BpmProcInst_.companyCode));
            inputs.add(bpmProcInst.get(BpmProcInst_.ticketRating));
            inputs.add(changeAssigneeHistory.get(ChangeAssigneeHistory_.orgAssignee));
            inputs.add(bpmProcInst.get(BpmProcInst_.ticketCreatedTime));
            inputs.add(bpmProcInst.get(BpmProcInst_.comment));
            inputs.add(bpmTask.get(BpmTask_.actionUser));
            inputs.add(changeAssigneeHistory.get(ChangeAssigneeHistory_.type));
            // check expire assign
            inputs.add(cb.selectCase()
                    .when(cb.or(
                            // check date
                            cb.and(
                                    cb.isTrue(bpmTask.get(BpmTask_.assignType)),
                                    cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.type), 1),
                                    cb.or(
                                            cb.lessThan(assignManagement.get(AssignManagement_.endDate), now),
                                            cb.greaterThan(assignManagement.get(AssignManagement_.startDate), now)
                                    ),
                                    cb.equal(assignManagement.get(AssignManagement_.effect), 0)
                            ),
                            // check status
                            assignManagement.get(AssignManagement_.status).in(0, 1).not()
                    ), cb.literal(true)));
            inputs.add(bpmProcInst.get(BpmProcInst_.serviceId));
            inputs.add(bpmTask.get(BpmTask_.taskAssignee));
            inputs.add(bpmProcInst.get(BpmProcInst_.ticketStatus));
            inputs.add(cb.coalesce(bpmProcInst.get(BpmProcInst_.chartNodeName), ""));
            if (bpmHistory != null) {
                inputs.add(bpmHistory.get("actionUser"));
            }
            query.multiselect(inputs).where(pred);
            query.distinct(true);

            List<MyTaskResponse> listResult = em.createQuery(query) != null ? em.createQuery(query).
                    setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList() : new ArrayList<>();
            queryCount.select(cb.countDistinct(bpmTaskCount)).where(predCount);
            Long count = em.createQuery(queryCount).getSingleResult();
            mapFinal.put("count", count);
            mapFinal.put("data", listResult);
            return mapFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            if (em != null) {
                em.close();
            }
        }
    }

    public List<Map<String, Object>> getMyTaskFilter(final MyTaskRequest criteria) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Map<String, Object> mapFinal = new HashMap<>();

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<Object> query = cb.createQuery(Object.class);

            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, BpmHistory> bpmHistory = null;
            Join<BpmProcInst, BpmTask> bpmTask = bpmProcInst.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
            Join<BpmProcInst, AuthorityManagement> authRoot = bpmProcInst.join((BpmProcInst_.authorityManagements), JoinType.LEFT);
            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join((BpmProcInst_.servicePackage), JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priorityManagement = bpmProcInst.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join((ServicePackage_.bpmProcdef), JoinType.LEFT);
            Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistory = bpmTask.join(BpmTask_.changeAssigneeHistories, JoinType.LEFT);
            Join<BpmTask, BpmTaskUser> bpmTaskUser = bpmTask.join((BpmTask_.bpmTaskUsers), JoinType.LEFT);
            Join<ChangeAssigneeHistory, AssignManagement> assignManagement = changeAssigneeHistory.join(ChangeAssigneeHistory_.assignManagement, JoinType.LEFT);
            if (criteria.getTaskStatus().equalsIgnoreCase("CANCEL")) {
                bpmHistory = bpmProcInst.join(BpmProcInst_.bpmHistories, JoinType.LEFT);
            }

            Predicate pred = getPredMyTask(criteria, cb, bpmProcInst, bpmTask, servicePackage, bpmProcdef, priorityManagement, changeAssigneeHistory, assignManagement, bpmTaskUser, bpmHistory, query, authRoot);

            List<Selection<?>> inputs = new ArrayList<Selection<?>>();
            if (criteria.getSearchBy() == null || criteria.getSearchRoot() == null) {
                return null;
            }

            switch (criteria.getSearchRoot()) {
                case "bpmProcInst":
                    inputs.add(bpmProcInst.get(criteria.getSearchBy()));
                    break;
                case "bpmHistory":
                    inputs.add(bpmHistory.get(criteria.getSearchBy()));
                    break;
                case "bpmTask":
                    if(criteria.getSearchBy().equals("taskAssignee")){
                        inputs.add(bpmProcInst.get(BpmProcInst_.ticketProcInstId));
                    }else {
                        inputs.add(bpmTask.get(criteria.getSearchBy()));
                    }
                    break;
                case "servicePackage":
                    inputs.add(servicePackage.get(criteria.getSearchBy()));
                    break;
                case "bpmProcdef":
                    inputs.add(bpmProcdef.get(criteria.getSearchBy()));
                    break;
                case "priorityManagement":
                    inputs.add(priorityManagement.get(criteria.getSearchBy()));
                    break;
                default:
                    break;
            }

            query.multiselect(inputs).where(pred);
            query.distinct(true);

            List<Map<String, Object>> rs = new ArrayList<>();
            List<Object> result = em.createQuery(query) != null ? em.createQuery(query).
                    setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList() : new ArrayList<>();
            if(criteria.getSearchBy().equals("taskAssignee")){
                List<String> procInstId = result.stream()
                        .map(object -> Objects.toString(object, null))
                        .collect(Collectors.toList());
                List<BpmTask> listTask = bpmTaskRepository.getBpmTaskByTaskStatusInAndTaskProcInstIdIn(TaskConstants.TabStatus.PROCESSING, procInstId);
                listTask.forEach(i->{
                    Map<String, Object> data = new HashMap<>();
                    data.put(criteria.getSearchBy(), i.getTaskAssignee());
                    rs.add(data);
                });
            }else{
                result.forEach(i -> {
                    Map<String, Object> data = new HashMap<>();
                    data.put(criteria.getSearchBy(), i);
                    rs.add(data);
                });
            }

            return rs;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            if (em != null) {
                em.close();
            }
        }
    }

    public Long countOngoing(String account, String type, Boolean filterChangeAssignee, List<String> listTaskId, String search) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Long> query = cb.createQuery(Long.class);
            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, BpmTask> bpmTask = bpmProcInst.join(BpmProcInst_.bpmTasks, JoinType.LEFT);
            Join<BpmTask, BpmTaskUser> bpmTaskUser = bpmTask.join(BpmTask_.bpmTaskUsers);
            Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistory = bpmTask.join(BpmTask_.changeAssigneeHistories, JoinType.LEFT);
            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef, JoinType.LEFT);

            //predicate
            predicates.add(cb.equal(bpmTask.get(BpmTask_.taskType), type));
//            predicates.add(cb.equal(bpmTaskUser.get(BpmTaskUser_.userName), account));

            predicates.add(cb.or(
                    cb.and(
                            cb.or(
                                    cb.isFalse(bpmTask.get(BpmTask_.assignType)),
                                    cb.isNull(bpmTask.get(BpmTask_.assignType))),
                            bpmTask.get(BpmTask_.taskId).in(listTaskId)),
                    cb.isTrue(bpmTask.get(BpmTask_.assignType))
            ));

            predicates.add(cb.or(
                    cb.equal(bpmTask.get(BpmTask_.taskAssignee), account),
                    cb.and(cb.isTrue(bpmTask.get(BpmTask_.assignType)),
                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.orgAssignee), account)),
                    cb.equal(bpmTaskUser.get(BpmTaskUser_.userName), account)
            ));
            predicates.add(cb.not(bpmProcInst.get(BpmProcInst_.ticketStatus).in(Arrays.asList(ProcInstConstants.Status.DELETED.code, ProcInstConstants.Status.CANCEL.code, ProcInstConstants.Status.DRAFT.code))));

//            if(type.equals(TaskConstants.Type.APPROVAL.code)){
                predicates.add(cb.and(
                        bpmTask.get(BpmTask_.taskStatus).in("ACTIVE","PROCESSING")
//                        cb.and(
//                                cb.equal(bpmProcInst.get(BpmProcInst_.ticketStatus), ProcInstConstants.Status.DELETED_BY_RU.code),
//                                cb.equal(bpmTask.get(BpmTask_.taskStatus), TaskConstants.Status.DELETED_BY_RU.code)
//                        ))
                ));
//            } else {
//                predicates.add(cb.or(
//                        bpmTask.get(BpmTask_.taskStatus).in(TaskConstants.TabStatus.PROCESSING),
//                        cb.and(
//                                cb.equal(bpmProcInst.get(BpmProcInst_.ticketStatus), ProcInstConstants.Status.DELETED_BY_RU.code),
//                                cb.equal(bpmTask.get(BpmTask_.taskStatus), TaskConstants.Status.DELETED_BY_RU.code)
//                        )));
//            }


            if (filterChangeAssignee) {
                predicates.add(cb.isTrue(bpmTask.get(BpmTask_.assignType)));
            }
            if (StringUtils.isNotBlank(search)) {
                Long idSearch = null;
                try {
                    idSearch = Long.parseLong(search);
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }
                Predicate predicate = cb.or(
                        cb.like(cb.lower(bpmProcInst.get("ticketTitle")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("requestCode")), "%" + search.trim().toLowerCase() + "%")
                );
                if (idSearch != null) {
                    predicates.add(cb.or(predicate, cb.equal(cb.lower(bpmProcInst.get("ticketId")), idSearch)));
                } else {
                    predicates.add(predicate);
                }
            }

            query.select(cb.countDistinct(bpmTask)).where(cb.and(predicates.toArray(Predicate[]::new))).getSelection();
            Long countResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : 0l;

            return countResult;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }
    public Long countRecalled(String account, String type, Boolean filterChangeAssignee, List<String> listTaskId, String search) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Long> query = cb.createQuery(Long.class);
            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, BpmTask> bpmTask = bpmProcInst.join(BpmProcInst_.bpmTasks, JoinType.LEFT);
            Join<BpmTask, BpmTaskUser> bpmTaskUser = bpmTask.join(BpmTask_.bpmTaskUsers);
            Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistory = bpmTask.join(BpmTask_.changeAssigneeHistories, JoinType.LEFT);
            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef, JoinType.LEFT);

            //predicate
            predicates.add(cb.equal(bpmTask.get(BpmTask_.taskType), type));
//            predicates.add(cb.equal(bpmTaskUser.get(BpmTaskUser_.userName), account));

            predicates.add(cb.or(
                    cb.and(
                            cb.or(
                                    cb.isTrue(bpmTask.get(BpmTask_.assignType)),
                                    cb.isNull(bpmTask.get(BpmTask_.assignType))),
                            bpmTask.get(BpmTask_.taskId).in(listTaskId)),
                    cb.isTrue(bpmTask.get(BpmTask_.assignType))
            ));

            predicates.add(cb.or(
                    cb.equal(bpmTask.get(BpmTask_.taskAssignee), account),
                    cb.and(cb.isTrue(bpmTask.get(BpmTask_.assignType)),
                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.orgAssignee), account)),
                    cb.equal(bpmTaskUser.get(BpmTaskUser_.userName), account)
            ));
            predicates.add(cb.not(bpmProcInst.get(BpmProcInst_.ticketStatus).in(Arrays.asList(ProcInstConstants.Status.DELETED.code, ProcInstConstants.Status.CANCEL.code, ProcInstConstants.Status.DRAFT.code))));
            predicates.add(cb.or(
                    bpmTask.get(BpmTask_.taskStatus).in("AGREE_TO_RECALL"),
                    cb.and(
                            cb.equal(bpmProcInst.get(BpmProcInst_.ticketStatus), ProcInstConstants.Status.DELETED_BY_RU.code),
                            cb.equal(bpmTask.get(BpmTask_.taskStatus), TaskConstants.Status.DELETED_BY_RU.code)
                    )));


            if (filterChangeAssignee) {
                predicates.add(cb.isTrue(bpmTask.get(BpmTask_.assignType)));
            }
            if (StringUtils.isNotBlank(search)) {
                Long idSearch = null;
                try {
                    idSearch = Long.parseLong(search);
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }
                Predicate predicate = cb.or(
                        cb.like(cb.lower(bpmProcInst.get("ticketTitle")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("requestCode")), "%" + search.trim().toLowerCase() + "%")
                );
                if (idSearch != null) {
                    predicates.add(cb.or(predicate, cb.equal(cb.lower(bpmProcInst.get("ticketId")), idSearch)));
                } else {
                    predicates.add(predicate);
                }
            }

            query.select(cb.countDistinct(bpmTask)).where(cb.and(predicates.toArray(Predicate[]::new))).getSelection();
            Long countResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : 0l;

            return countResult;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public Long countCompleted(String account, String type, Boolean filterChangeAssignee, List<String> listTaskId, String search) {
        EntityManager em = entityManagerFactory.createEntityManager();

        try {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Long> query = cb.createQuery(Long.class);
            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, BpmTask> bpmTask = bpmProcInst.join(BpmProcInst_.bpmTasks, JoinType.LEFT);
            Join<BpmTask, BpmTaskUser> bpmTaskUser = bpmTask.join(BpmTask_.bpmTaskUsers, JoinType.LEFT);
            Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistory = bpmTask.join(BpmTask_.changeAssigneeHistories, JoinType.LEFT);
            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef, JoinType.LEFT);

            predicates.add(cb.equal(bpmTask.get(BpmTask_.taskType), type));
//            predicates.add(cb.equal(bpmTask.get(BpmTask_.taskAssignee), account));

            predicates.add(cb.or(
                    cb.and(
                            cb.or(
                                    cb.isFalse(bpmTask.get(BpmTask_.assignType)),
                                    cb.isNull(bpmTask.get(BpmTask_.assignType))),
                            bpmTask.get(BpmTask_.taskId).in(listTaskId)),
                    cb.isTrue(bpmTask.get(BpmTask_.assignType))
            ));

            predicates.add(cb.or(
                    cb.equal(bpmTask.get(BpmTask_.taskAssignee), account),
                    cb.and(cb.isTrue(bpmTask.get(BpmTask_.assignType)),
                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.orgAssignee), account)),
                    // trường hợp candidate task
                    cb.equal(bpmTaskUser.get(BpmTaskUser_.userName), account)
            ));

            predicates.add(cb.not(bpmProcInst.get(BpmProcInst_.ticketStatus).in(Arrays.asList(ProcInstConstants.Status.DELETED.code, ProcInstConstants.Status.CANCEL.code, ProcInstConstants.Status.DRAFT.code,ProcInstConstants.Status.RECALLED.code))));
            predicates.add(cb.equal(bpmTask.get(BpmTask_.taskStatus), TaskConstants.Status.COMPLETED.code));
            if (filterChangeAssignee) {
                predicates.add(cb.isTrue(bpmTask.get(BpmTask_.assignType)));
            }
            if (StringUtils.isNotBlank(search)) {
                Long idSearch = null;
                try {
                    idSearch = Long.parseLong(search);
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }
                Predicate predicate = cb.or(
                        cb.like(cb.lower(bpmProcInst.get("ticketTitle")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("requestCode")), "%" + search.trim().toLowerCase() + "%")
                );
                if (idSearch != null) {
                    predicates.add(cb.or(predicate, cb.equal(cb.lower(bpmProcInst.get("ticketId")), idSearch)));
                } else {
                    predicates.add(predicate);
                }
            }
            query.select(cb.countDistinct(bpmTask)).where(cb.and(predicates.toArray(Predicate[]::new))).getSelection();
            Long countResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : 0l;
            return countResult;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public Long countDraft(String account, String type, Boolean filterChangeAssignee) {
        EntityManager em = entityManagerFactory.createEntityManager();

        try {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Long> query = cb.createQuery(Long.class);
            Root<BpmTask> root = query.from(BpmTask.class);
            Root<BpmVariables> rootVariables = query.from(BpmVariables.class);

            predicates.add(cb.and(cb.equal(root.get("taskId"), rootVariables.get("taskId")),
                    cb.equal(rootVariables.get("isDraft"), 1),
                    root.get("taskStatus").in(TaskConstants.TabStatus.PROCESSING),
                    cb.equal(cb.lower(root.get("taskAssignee")), account)));
            predicates.add(cb.equal(root.get(BpmTask_.taskType), type));
            if (filterChangeAssignee) {
                predicates.add(cb.isTrue(root.get(BpmTask_.assignType)));
            }
            query.select(cb.countDistinct(root)).where(cb.and(predicates.toArray(Predicate[]::new))).getSelection();
            Long countResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : 0l;
            return countResult;
        } catch (Exception e) {
            e.printStackTrace();
            return 0l;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public Long countShare(String account, String type, Boolean filterChangeAssignee, String search) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Long> query = cb.createQuery(Long.class);
            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, BpmShared> bpmShare = bpmProcInst.join(BpmProcInst_.bpmShareds);
            Join<BpmProcInst, BpmTask> bpmTask = bpmProcInst.join(BpmProcInst_.bpmTasks, JoinType.LEFT);
            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef);
            List<String> status = new ArrayList<>(TaskConstants.TabStatus.PROCESSING);
            status.add("COMPLETED");
            //
            predicates.add(cb.equal(bpmShare.get(BpmShared_.sharedUser), account));
            predicates.add(cb.equal(bpmShare.get(BpmShared_.type), TaskConstants.Type.FOLLOWED.code));
            predicates.add(cb.equal(bpmTask.get(BpmTask_.taskType), type));
            predicates.add(cb.not(bpmProcInst.get(BpmProcInst_.ticketStatus).in(Arrays.asList(ProcInstConstants.Status.CANCEL.code, ProcInstConstants.Status.DRAFT.code))));
            predicates.add(bpmTask.get(BpmTask_.taskStatus).in(status));
            if (filterChangeAssignee) {
                predicates.add(cb.isTrue(bpmTask.get(BpmTask_.assignType)));
            }
            if (StringUtils.isNotBlank(search)) {
                Long idSearch = null;
                try {
                    idSearch = Long.parseLong(search);
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }
                Predicate predicate = cb.or(
                        cb.like(cb.lower(bpmProcInst.get("ticketTitle")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("requestCode")), "%" + search.trim().toLowerCase() + "%")
                );
                if (idSearch != null) {
                    predicates.add(cb.or(predicate, cb.equal(cb.lower(bpmProcInst.get("ticketId")), idSearch)));
                } else {
                    predicates.add(predicate);
                }
            }
            query.select(cb.countDistinct(bpmTask)).where(cb.and(predicates.stream().toArray(Predicate[]::new))).getSelection();
            Long countResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : 0l;

            return countResult;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return 0L;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public Long countCancel(String account, String type, Boolean filterChangeAssignee, List<String> listTaskId, String search) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {

            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Long> query = cb.createQuery(Long.class);
            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, BpmTask> bpmTask = bpmProcInst.join(BpmProcInst_.bpmTasks);
            Join<BpmTask, BpmTaskUser> bpmTaskUser = bpmTask.join(BpmTask_.bpmTaskUsers);
            Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistory = bpmTask.join(BpmTask_.changeAssigneeHistories, JoinType.LEFT);
            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef);
            //predicate

            predicates.add(cb.or(
                    cb.and(
                            cb.or(
                                    cb.isFalse(bpmTask.get(BpmTask_.assignType)),
                                    cb.isNull(bpmTask.get(BpmTask_.assignType))),
                            bpmTask.get(BpmTask_.taskId).in(listTaskId)),
                    cb.isTrue(bpmTask.get(BpmTask_.assignType))
            ));

            predicates.add(cb.and(
                    cb.equal(bpmProcInst.get("ticketStatus"), ProcInstConstants.Status.CANCEL.code),
                    cb.or(
                            cb.equal(cb.lower(bpmTaskUser.get(BpmTaskUser_.userName)), account),
                            cb.and(
                                    cb.isTrue(bpmTask.get(BpmTask_.assignType)),
                                    cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.orgAssignee), account)
                            ))));
            predicates.add(cb.equal(bpmTask.get(BpmTask_.taskType), type));
            if (filterChangeAssignee) {
                predicates.add(cb.isTrue(bpmTask.get(BpmTask_.assignType)));
            }
            if (StringUtils.isNotBlank(search)) {
                Long idSearch = null;
                try {
                    idSearch = Long.parseLong(search);
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }
                Predicate predicate = cb.or(
                        cb.like(cb.lower(bpmProcInst.get("ticketTitle")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("requestCode")), "%" + search.trim().toLowerCase() + "%")
                );
                if (idSearch != null) {
                    predicates.add(cb.or(predicate, cb.equal(cb.lower(bpmProcInst.get("ticketId")), idSearch)));
                } else {
                    predicates.add(predicate);
                }
            }
            query.select(cb.countDistinct(bpmTask)).where(cb.and(predicates.stream().toArray(Predicate[]::new))).getSelection();
            Long countResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : 0l;
            return countResult;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return 0L;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public Predicate getPredMyTask(MyTaskRequest criteria, CriteriaBuilder cb,
                                   Root<BpmProcInst> rootProcInst, Join<BpmProcInst, BpmTask> root,
                                   Join<BpmProcInst, ServicePackage> rootService,
                                   Join<ServicePackage, BpmProcdef> rootProcdef,
                                   Join<BpmProcInst, PriorityManagement> rootPriority,
                                   Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistory,
                                   Join<ChangeAssigneeHistory, AssignManagement> assignManagement,
                                   Join<BpmTask, BpmTaskUser> bpmTaskUser,
                                   Join<BpmProcInst, BpmHistory> bpmHistory,
                                   CriteriaQuery<?> query,
                                   Join<BpmProcInst, AuthorityManagement> authRoot) {
        try {
            List<Predicate> predicates = new ArrayList<>();

            List<String> ignoreTaskStatus = Arrays.asList("ongoing","completed","recalled");
            // ignore ticket status = cancel
            if (!ValidationUtils.isNullOrEmpty(criteria.getTaskStatus()) && ignoreTaskStatus.contains(criteria.getTaskStatus().toLowerCase())) {
                predicates.add(cb.not(rootProcInst.get("ticketStatus").in(Arrays.asList(ProcInstConstants.Status.DELETED.code, ProcInstConstants.Status.CANCEL.code, ProcInstConstants.Status.DRAFT.code))));
            }
            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            if (DateDto.getType().equalsIgnoreCase("ticketCreatedTime") || DateDto.getType().equalsIgnoreCase("ticketCanceledTime")) {
                                if(DateDto.getFromDate().equals(DateDto.getToDate())){
                                    predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                                    predicates.add(cb.lessThan(root.get(DateDto.getType()), toDateLocal));
                                }   else
                                    predicates.add(cb.between(rootProcInst.get(DateDto.getType()), fromDateLocal, toDateLocal));
                            } else {
                                if(DateDto.getFromDate().equals(DateDto.getToDate())){
                                    predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                                    predicates.add(cb.lessThan(root.get(DateDto.getType()), toDateLocal));
                                }   else
                                    predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            if (DateDto.getType().equalsIgnoreCase("ticketCreatedTime") || DateDto.getType().equalsIgnoreCase("ticketCanceledTime")) {
                                predicates.add(cb.lessThanOrEqualTo(rootProcInst.get(DateDto.getType()), toDateLocal));
                            } else {
                                predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            if (DateDto.getType().equalsIgnoreCase("ticketCreatedTime") || DateDto.getType().equalsIgnoreCase("ticketCanceledTime")) {
                                predicates.add(cb.greaterThanOrEqualTo(rootProcInst.get(DateDto.getType()), fromDateLocal));
                            } else {
                                predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }
            if (criteria.getListService() != null && !criteria.getListService().isEmpty()) {
                if (!criteria.getListService().contains(CommonConstants.FILTER_SELECT_ALL)) {
                    predicates.add(rootService.get("serviceName").in(criteria.getListService()));
                }
            }
            if (criteria.isFilterChangeAssignee()) {
                predicates.add(cb.isTrue(root.get(BpmTask_.assignType)));
            }
            if (criteria.getListUser() != null && !criteria.getListUser().isEmpty()) {
                if (!criteria.getListUser().contains(CommonConstants.FILTER_SELECT_ALL)) {
                    predicates.add(rootProcInst.get(BpmProcInst_.createdUser).in(criteria.getListUser()));
                }
            }

            if (criteria.getListRating() != null && !criteria.getListRating().isEmpty()) {
                if (!criteria.getListRating().contains(CommonConstants.FILTER_SELECT_ALL)) {
                    if (String.join("", criteria.getListRating()).equals("0"))
                        predicates.add(rootProcInst.get("ticketRating").in(0));
                    else
                        predicates.add(rootProcInst.get("ticketRating").in(0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5));
                }
            }


            if (!ValidationUtils.isNullOrEmpty(criteria.getListPriority())) {
                if (!criteria.getListPriority().contains(CommonConstants.FILTER_SELECT_ALL)) {
                    predicates.add(rootPriority.get(PriorityManagement_.name).in(criteria.getListPriority()));
                }
            }
            if (!criteria.getTaskStatus().equalsIgnoreCase("handingWork")) {
                if (criteria.getListStatus() != null && !criteria.getListStatus().contains(TaskConstants.Status.DRAFT.code)) {
                    if (criteria.getTaskStatus().equalsIgnoreCase(TaskConstants.Status.CANCEL.code)) {
                        predicates.add(cb.equal(rootProcInst.get("ticketStatus"), criteria.getTaskStatus()));
                    } else if (criteria.getTaskStatus().equalsIgnoreCase(TaskConstants.Status.COMPLETED.code)) {
                        if(!ValidationUtils.isAcceptSearchListIn(criteria.getListTaskAssignee()))
                            predicates.add(cb.equal(root.get("taskStatus"), criteria.getTaskStatus()));
                    } else if (criteria.getTaskStatus().equalsIgnoreCase(TaskConstants.Status.SHARED.code)) {
                        criteria.getListStatus().add(TaskConstants.Status.COMPLETED.code);
                        predicates.add(root.get("taskStatus").in(criteria.getListStatus()));
                    } else if (!criteria.getTaskStatus().contains("SHARED")) {
                        boolean isApproval = false;
                        if(criteria.getType() != null && !criteria.getType().isEmpty()){
                            isApproval = criteria.getType().get(0).equals(TaskConstants.Type.APPROVAL.code);
                        }
                        if (!criteria.getListStatus().contains(CommonConstants.FILTER_SELECT_ALL)) {
                            if (criteria.getListStatus().contains(ProcInstConstants.Status.RECALLING.code)) {
//                                if(isApproval) {
                                    predicates.add(
                                            cb.and(
                                                                                        root.get("taskStatus").in(Arrays.asList(criteria.getListStatus(),TaskConstants.Status.ACTIVE.code,TaskConstants.Status.PROCESSING.code)),
                                                    cb.equal(rootProcInst.get(BpmProcInst_.TICKET_STATUS),ProcInstConstants.Status.RECALLING.code)
//                                                    cb.and(root.get("taskStatus").in("ACTIVE","PROCESSING")
//                                                            cb.equal(rootProcInst.get(BpmProcInst_.TICKET_STATUS), ProcInstConstants.Status.RECALLING.code)
//                                                    )
//                                                    ,
//                                                    root.get("taskStatus").in(criteria.getListStatus())
                                            )
                                );
//                                } else {
//                                    predicates.add(
//                                            cb.or(
//                                                    cb.and(
//                                                            root.get("taskStatus").in(
//                                                                    TaskConstants.TabStatus.PROCESSING
//                                                            ),
//                                                            cb.equal(rootProcInst.get(BpmProcInst_.TICKET_STATUS), ProcInstConstants.Status.RECALLING.code)
//                                                    ),
//                                                    root.get("taskStatus").in(criteria.getListStatus())
//                                            ));
//                                }
                            } else
                                predicates.add(root.get("taskStatus").in(criteria.getListStatus()));
                        } else {
//                            if(isApproval) {
//                        predicates.add(root.get("taskStatus").in(TaskConstants.TabStatus.PROCESSING));
                                List<String> statusIn = new ArrayList<>();
                                if(criteria.getTaskStatus().equalsIgnoreCase("recalled")){
                                    statusIn.addAll(Arrays.asList("AGREE_TO_RECALL", "DELETED_BY_RU"));
                                } else {
                                        statusIn.addAll(Arrays.asList("ACTIVE","PROCESSING"));
                                }
                                predicates.add(cb.and(
                                        root.get("taskStatus").in(statusIn)
                                        // Trường hợp task status = DELETED_BY_RU thì check cả status ticket
//                                        cb.and(
//                                                cb.equal(rootProcInst.get(BpmProcInst_.ticketStatus), ProcInstConstants.Status.DELETED_BY_RU.code),
//                                                cb.equal(root.get(BpmTask_.taskStatus), TaskConstants.Status.DELETED_BY_RU.code)
//                                        )
                                ));
//                            } else {
//                                predicates.add(cb.or(
//                                        root.get("taskStatus").in(TaskConstants.TabStatus.PROCESSING),
//                                        // Trường hợp task status = DELETED_BY_RU thì check cả status ticket
//                                        cb.and(
//                                                cb.equal(rootProcInst.get(BpmProcInst_.ticketStatus), ProcInstConstants.Status.DELETED_BY_RU.code),
//                                                cb.equal(root.get(BpmTask_.taskStatus), TaskConstants.Status.DELETED_BY_RU.code)
//                                        )
//                                ));
//                            }
                        }
                    }
                }
            } else if (criteria.getListStatus() != null && criteria.getListStatus().contains(TaskConstants.Status.DRAFT.code)) {
                Root<BpmVariables> rootVariables = query.from(BpmVariables.class);
                predicates.add(cb.and(cb.equal(root.get("taskId"), rootVariables.get("taskId")),
                        cb.equal(rootVariables.get("isDraft"), 1),
                        root.get("taskStatus").in(TaskConstants.TabStatus.PROCESSING)));
            }
            if (criteria.getType() != null) {
                predicates.add(cb.and(root.get(BpmTask_.taskType).in(criteria.getType())));

            }
//            if (!criteria.getTaskStatus().equalsIgnoreCase(TaskConstants.Status.SHARED.code)) {
//                predicates.add(root.get("taskId").in(criteria.getTaskId()));
//            }
            // Thêm case search task ủy quyền/ được ủy quyền
            if (!criteria.getTaskStatus().equalsIgnoreCase("handingWork")) {
                if (!criteria.getTaskStatus().equalsIgnoreCase(TaskConstants.Status.SHARED.code) && !ValidationUtils.isAcceptSearchListIn(criteria.getListTaskAssignee())) {
                    predicates.add(cb.or(
                            cb.and(
                                    cb.or(
                                            cb.isFalse(root.get(BpmTask_.assignType)),
                                            cb.isNull(root.get(BpmTask_.assignType))),
                                    root.get("taskId").in(criteria.getTaskId())),
                            cb.isTrue(root.get(BpmTask_.assignType))
                    ));
                }else predicates.add(root.get(BpmTask_.taskAssignee).in(criteria.getListTaskAssignee()));
            }
            if (StringUtils.isNotBlank(criteria.getSearch())) {
                if (!ValidationUtils.isNullOrEmpty(criteria.getSearchBy())) {
                    switch (criteria.getSearchRoot()) {
                        case "bpmProcInst":
                            predicates.add(cb.like(cb.lower(rootProcInst.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
                            break;
                        case "bpmHistory":
                            predicates.add(cb.like(cb.lower(bpmHistory.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
                            break;
                        case "servicePackage":
                            predicates.add(cb.like(cb.lower(rootService.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
                            break;
                        case "bpmProcdef":
                            predicates.add(cb.like(cb.lower(rootProcdef.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
                            break;
                        case "bpmTaskUser":
                            predicates.add(cb.like(cb.lower(bpmTaskUser.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
                            break;
                        case "priorityManagement":
                            predicates.add(cb.like(cb.lower(rootPriority.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
                            break;
                        default:
                            predicates.add(cb.like(cb.lower(root.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
                            break;
                    }

                } else {
                    Long idSearch = null;
                    try {
                        idSearch = Long.parseLong(criteria.getSearch());
                    } catch (Exception ex) {
                        log.error(ex.getMessage(), ex);
                    }

                    Predicate predicate = cb.or(
//                        cb.like(cb.lower(rootService.get("serviceName")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
//                        cb.like(cb.lower(root.get("taskName")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                            cb.like(cb.lower(rootProcInst.get("ticketTitle")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
//                        cb.like(cb.lower(rootProcInst.get("ticketStartUserId")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                            cb.like(cb.lower(rootProcInst.get("requestCode")), "%" + criteria.getSearch().trim().toLowerCase() + "%")
//                        cb.like(cb.lower(rootProcInst.get("ticketId").as(String.class)), "%" + criteria.getSearch().trim().toLowerCase() + "%")
                    );
                    if (idSearch != null) {
                        predicates.add(cb.or(predicate, cb.equal(cb.lower(rootProcInst.get("ticketId")), idSearch)));
                    } else {
                        predicates.add(predicate);
                    }
                }
            }

            if (criteria.getTaskStatus().equals("CANCEL") && bpmHistory != null) { // CANCEL thì join lấy ra người hủy
                predicates.add(cb.and(
                        cb.equal(rootProcInst.get("ticketStatus"), "CANCEL"),
                        cb.equal(bpmHistory.get("action"), "CANCEL_TICKET")));
            }

            if (criteria.getTaskStatus().equalsIgnoreCase("handingWork")) {
                if (!ValidationUtils.isNullOrEmpty(criteria.getUser())) {
                    predicates.add(cb.and(
                            cb.or(
                                    // lịch sử bàn giao
                                    cb.and(
                                            cb.or(
                                                    cb.equal(authRoot.get(AuthorityManagement_.TO_ACCOUNT), criteria.getUser()),
                                                    cb.equal(authRoot.get(AuthorityManagement_.FROM_ACCOUNT), criteria.getUser())
                                            ),
                                            cb.equal(authRoot.get(AuthorityManagement_.type), 3), // chỉ check author type 3
                                            cb.equal(authRoot.get(AuthorityManagement_.taskId), root.get(BpmTask_.taskId))
                                    ),
                                    cb.and(
                                            cb.not(root.get(BpmTask_.TASK_STATUS).in(Arrays.asList(TaskConstants.Status.COMPLETED.code,
                                                    TaskConstants.Status.CANCEL.code, TaskConstants.Status.DELETED_BY_RU.code))),
                                            cb.or(
                                                    // ko ủy quyền
                                                    cb.and(
                                                            cb.equal(root.get(BpmTask_.taskAssignee), criteria.getUser()),
                                                            cb.isNull(root.get(BpmTask_.assignType))
                                                    ),
                                                    // có ủy quyền
                                                    cb.and(
                                                            cb.isTrue(root.get(BpmTask_.assignType)),
                                                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.orgAssignee), criteria.getUser())
                                                    )
                                            )
                                    )
                            )
                    ));
                }
            } else if (criteria.getTaskStatus().equalsIgnoreCase("shared")) {
                Root<BpmShared> rootShare = query.from(BpmShared.class);
                predicates.add(cb.equal(rootProcInst.get(BpmProcInst_.ticketProcInstId), rootShare.get(BpmShared_.procInstId)));
                predicates.add(cb.equal(rootShare.get(BpmShared_.sharedUser), criteria.getUser()));
                predicates.add(cb.equal(rootShare.get(BpmShared_.type), TaskConstants.Type.FOLLOWED.code));
                predicates.add(cb.not(rootProcInst.get("ticketStatus").in(Arrays.asList(ProcInstConstants.Status.CANCEL.code, ProcInstConstants.Status.DRAFT.code))));
            } else {
                // Thêm case search task ủy quyền/ được ủy quyền
                if (!ValidationUtils.isNullOrEmpty(criteria.getUser()) && !ValidationUtils.isAcceptSearchListIn(criteria.getListTaskAssignee())) {
                    predicates.add(cb.or(
                            cb.equal(root.get(BpmTask_.taskAssignee), criteria.getUser()),
                            cb.and(
                                    cb.isTrue(root.get(BpmTask_.assignType)),
                                    cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.orgAssignee), criteria.getUser())
                            ),
                            // trường hợp candidate task
                            cb.equal(bpmTaskUser.get(BpmTaskUser_.userName), criteria.getUser())
                    ));
                } else predicates.add(root.get(BpmTask_.taskAssignee).in(criteria.getListTaskAssignee()));
            }


            // filter ủy quyền
            if (!ValidationUtils.isNullOrEmpty(criteria.getListOrgAssign())) {
                LocalDate now = LocalDate.now();
                if (criteria.getListOrgAssign().contains("-1")) {
                    predicates.add(cb.and(
                            cb.isTrue(root.get(BpmTask_.assignType)),
                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.orgAssignee), criteria.getUser())
                    ));
                } else if (criteria.getListOrgAssign().contains("1")) { // Đang hiệu lực
                    predicates.add(cb.and(
                            cb.isTrue(root.get(BpmTask_.assignType)),
                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.orgAssignee), criteria.getUser()),
                            cb.or(
                                    cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.type), 0),
                                    cb.and(
                                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.type), 1),
                                            cb.lessThanOrEqualTo(assignManagement.get(AssignManagement_.startDate), now),
                                            cb.greaterThanOrEqualTo(assignManagement.get(AssignManagement_.endDate), now),
                                            assignManagement.get(AssignManagement_.status).in(0, 1)
                                    )
                            )
                    ));
                } else if (criteria.getListOrgAssign().contains("0")) { // Hết hiệu lực
                    predicates.add(cb.and(
                            cb.isTrue(root.get(BpmTask_.assignType)),
                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.orgAssignee), criteria.getUser()),
                            cb.and(
                                    cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.type), 1),
                                    cb.or(
                                            cb.lessThan(assignManagement.get(AssignManagement_.endDate), now),
                                            cb.greaterThan(assignManagement.get(AssignManagement_.startDate), now),
                                            assignManagement.get(AssignManagement_.status).in(0, 1).not()
                                    )
                            )
                    ));
                }
            }

            // filter được ủy quyền
            if (!ValidationUtils.isNullOrEmpty(criteria.getListAssigned())) {
                LocalDate now = LocalDate.now();
                if (criteria.getListAssigned().contains("-1")) {
                    predicates.add(cb.and(
                            cb.isTrue(root.get(BpmTask_.assignType)),
                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.toAssignee), criteria.getUser())
                    ));
                } else if (criteria.getListAssigned().contains("1")) { // Đang hiệu lực
                    predicates.add(cb.and(
                            cb.isTrue(root.get(BpmTask_.assignType)),
                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.toAssignee), criteria.getUser()),
                            cb.or(
                                    cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.type), 0),
                                    cb.and(
                                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.type), 1),
                                            cb.lessThanOrEqualTo(assignManagement.get(AssignManagement_.startDate), now),
                                            cb.greaterThanOrEqualTo(assignManagement.get(AssignManagement_.endDate), now),
                                            assignManagement.get(AssignManagement_.status).in(0, 1)
                                    )
                            )
                    ));
                } else if (criteria.getListAssigned().contains("0")) { // Hết hiệu lực
                    predicates.add(cb.and(
                            cb.isTrue(root.get(BpmTask_.assignType)),
                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.toAssignee), criteria.getUser()),
                            cb.and(
                                    cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.type), 1),
                                    cb.or(
                                            cb.lessThan(assignManagement.get(AssignManagement_.endDate), now),
                                            cb.greaterThan(assignManagement.get(AssignManagement_.startDate), now),
                                            assignManagement.get(AssignManagement_.status).in(0, 1).not()
                                    )
                            )
                    ));
                }
            }

            // filter mã công ty
            if (!ValidationUtils.isNullOrEmpty(criteria.getChartIds())) {
                predicates.add(rootProcInst.get(BpmProcInst_.companyCode).in(criteria.getChartIds()));
            }
            handleAddFilterTask(criteria, cb, rootProcInst,
                    root, rootService,
                    rootProcdef, rootPriority,
                    changeAssigneeHistory, assignManagement,
                    bpmTaskUser, bpmHistory, query, authRoot, predicates);
            return cb.and(predicates.toArray(Predicate[]::new));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    private void handleAddFilterTask(MyTaskRequest criteria, CriteriaBuilder cb,
                                     Root<BpmProcInst> rootProcInst,
                                     Join<BpmProcInst, BpmTask> root,
                                     Join<BpmProcInst, ServicePackage> rootService,
                                     Join<ServicePackage, BpmProcdef> rootProcdef,
                                     Join<BpmProcInst, PriorityManagement> rootPriority,
                                     Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistory,
                                     Join<ChangeAssigneeHistory, AssignManagement> assignManagement,
                                     Join<BpmTask, BpmTaskUser> bpmTaskUser,
                                     Join<BpmProcInst, BpmHistory> bpmHistory,
                                     CriteriaQuery<?> query,
                                     Join<BpmProcInst, AuthorityManagement> authRoot,
                                     List<Predicate> predicates) throws IllegalAccessException {

        List<String> filterList = Arrays.asList(
                "listRequestCode", "listProcTitle",
                "listServiceName",
                "listCreatedUser", "listCompanyCode",
                "listChartNodeName",
                "listTaskName",
                "listTaskStatus",
                "listTicketCreatedTime",
                "listTaskCreatedTime",
                "listRemainingTime",
                "listTaskPriority",
                "listOrgAssignee",
                "listAssignType",
                "listId","listTicketId",
                "listTaskNameExt",
                "listTaskStartedTime",
                "listTaskFinishedTime",
                "listActionUserCompleteTask",
                "listTaskCanceledTime",
                "listCancelUser",
                "listCancelActionUser",
                "listCancelReason",
                "listCompanyName",
                "listModifiedUser",
                "listSharedUser",
                "listDescription",
                "listTicketStatus",
                "listActionUser",
                "listTicketTaskDtoTaskAssignee"
        );
        Field[] fields = criteria.getClass().getDeclaredFields();
        for (Field field : fields) {
            String fieldName = field.getName();
            if (filterList.contains(fieldName)) {
                field.setAccessible(true);
                Object fieldValue = field.get(criteria);
                if (fieldValue instanceof ArrayList) {
                    List<String> filterData = (List<String>) fieldValue;
                    if (ValidationUtils.isAcceptSearchListIn(filterData)) {
                        switch (fieldName) {
                            case "listRequestCode":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.REQUEST_CODE).in(filterData)));
                                break;
                                case "listActionUser":
                                predicates.add(cb.and(root.get(BpmTask_.ACTION_USER).in(filterData)));
                                break;
                            case "listTicketStatus":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.ticketStatus).in(filterData)));
                                break;
                            case "listProcTitle":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.TICKET_TITLE).in(filterData)));
                                break;
                            case "listServiceName":
                                predicates.add(cb.and(rootService.get(ServicePackage_.SERVICE_NAME).in(filterData)));
                                break;
                            case "listCreatedUser":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.CREATED_USER).in(filterData)));
                                break;
                            case "listCompanyCode":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.COMPANY_CODE).in(filterData)));
                                break;
                            case "listChartNodeName":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.CHART_NODE_NAME).in(filterData)));
                                break;
                            case "listTaskName":
                                predicates.add(cb.and(root.get(BpmTask_.TASK_NAME).in(filterData)));
                                break;
                            case "listTaskStatus":
                                if (filterData.contains(TaskConstants.Status.PROCESSING.code)) {
                                    filterData.add(TaskConstants.Status.ACTIVE.code);
                                }
                                predicates.add(cb.and(root.get(BpmTask_.TASK_STATUS).in(filterData)));
                                break;
                            case "listTaskPriority":
                                predicates.add(cb.and(rootPriority.get(PriorityManagement_.NAME).in(filterData)));
                                break;
                            case "listOrgAssignee":
                                predicates.add(cb.and(changeAssigneeHistory.get(ChangeAssigneeHistory_.ORG_ASSIGNEE).in(filterData)));
                                break;
                            case "listAssignType":
                                predicates.add(cb.and(bpmTaskUser.get(BpmTask_.ASSIGN_TYPE).in(filterData)));
                                break;
                            case "listId":
                            case "listTicketId":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.TICKET_ID).in(filterData)));
                                break;
                            case "listActionUserCompleteTask":
                                predicates.add(cb.and(root.get(BpmTask_.ACTION_USER).in(filterData)));
                                break;
                            case "listCancelUser":
                            case "listCancelActionUser":
                                predicates.add(cb.and(bpmHistory.get(BpmHistory_.actionUser).in(filterData)));
                                break;
                            case "listCancelReason":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.CANCEL_REASON).in(filterData)));
                                break;
                            case "listTicketTaskDtoTaskAssignee":
                                Join<BpmProcInst, BpmTask> subTask = rootProcInst.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
                                predicates.add(cb.and(
                                        subTask.get(BpmTask_.TASK_ASSIGNEE).in(filterData),
                                        subTask.get(BpmTask_.TASK_STATUS).in(TaskConstants.TabStatus.PROCESSING)
                                ));
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
        }

        if (!ValidationUtils.isNullOrEmpty(criteria.getSearchBy())) {
            switch (criteria.getSearchRoot()) {
                case "servicePackage":
                    predicates.add(cb.and(rootService.get(criteria.getSearchBy()).isNotNull()));
                    break;
                case "bpmProcInst":
                    predicates.add(cb.and(rootProcInst.get(criteria.getSearchBy()).isNotNull()));
                    break;
                case "bpmProcdef":
                    predicates.add(cb.and(rootProcdef.get(criteria.getSearchBy()).isNotNull()));
                    break;
                case "bpmHistory":
                    if (bpmHistory != null)
                        predicates.add(cb.and(bpmHistory.get(criteria.getSearchBy()).isNotNull()));
                    break;
                case "bpmTaskUser":
                    predicates.add(cb.and(bpmTaskUser.get(criteria.getSearchBy()).isNotNull()));
                    break;
                case "priorityManagement":
                    predicates.add(cb.and(rootPriority.get(criteria.getSearchBy()).isNotNull()));
                    break;
                default:
                    predicates.add(cb.and(root.get(criteria.getSearchBy()).isNotNull()));
                    break;
            }
        }
    }

    //----------------------------MY REPORT FIRST SECTION FILTER--------------------------------------//
    public Specification<BpmTask> getRuFilter(LocalDateTime firstDateOfTerm, List<String> listUser) {
        return (root, query, cb) -> {
            query.distinct(true);
            Root<BpmHistory> rootHistory = query.from(BpmHistory.class);
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(rootHistory.get("toUser").in(listUser));
//            predicates.add(cb.equal(rootHistory.get("action"), "RequestUpdate"));
            predicates.add(cb.equal(rootHistory.get("action"), "RE_CREATED_BY_RU"));
            predicates.add(cb.notEqual(root.get("taskStatus"), "RE_CREATED_BY_RU"));
            predicates.add(cb.greaterThanOrEqualTo(rootHistory.get("time"), Date.from(firstDateOfTerm.atZone(ZoneId.systemDefault()).toInstant())));
            predicates.add(cb.equal(root.get("taskProcInstId"), rootHistory.get("procInstId")));
            predicates.add(cb.equal(root.get("taskDefKey"), rootHistory.get("toTaskKey")));
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }

    public Specification<BpmTask> countTaskCallCenter(List<String> accounts, Long chartId) {
        return (root, query, cb) -> {
            Root<BpmProcInst> rootProcInst = query.from(BpmProcInst.class);
            Root<ServicePackage> rootService = query.from(ServicePackage.class);

            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("taskProcInstId"), rootProcInst.get("ticketProcInstId")));
            predicates.add(cb.equal(rootProcInst.get("serviceId"), rootService.get("id")));
            predicates.add(cb.equal(rootService.get("idOrgChart"), chartId));
            if (accounts != null && accounts.size() > 0) {
                predicates.add(root.get("taskAssignee").in(accounts));
            }
            predicates.add(root.get("taskStatus").in(TaskConstants.TabStatus.PROCESSING));
            query.groupBy(root.get("id"));
            return cb.and(predicates.toArray(Predicate[]::new));
        };
    }


    public Object[] MonthlyTaskCount(List<String> listUser, LinkedList<Map<String, LocalDateTime>> listDatefilter, String type, String criteriaType, List<Long> listService, List<String> listPriority) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {

            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Object[]> query = cb.createQuery(Object[].class);
            Root<BpmTask> root = query.from(BpmTask.class);
            Root<BpmProcInst> rootProcInst = query.from(BpmProcInst.class);
            Root<ServicePackage> rootService = query.from(ServicePackage.class);

            predicates.add(cb.equal(root.get("taskProcInstId"), rootProcInst.get("ticketProcInstId")));
            predicates.add(cb.equal(rootProcInst.get("serviceId"), rootService.get("id")));

            if (listService != null && !listService.isEmpty()) {
                predicates.add(rootService.get("id").in(listService));
            }
            if (listPriority != null && !listPriority.isEmpty()) {
                predicates.add(rootProcInst.get("priority").in(listPriority));
            }

            if (type.equals("BACKLOG")) {
                predicates.add(root.get("taskAssignee").in(listUser));
                switch (criteriaType.toUpperCase()) {
                    case "FINISHED":
                        predicates.add(cb.isNotNull(root.get("taskFinishedTime")));
                        break;
                    case "CANCEL":
                        predicates.add(cb.equal(root.get("taskStatus"), "CANCEL"));
                        break;
                    case "RU":
                        Root<BpmHistory> rootHistory = query.from(BpmHistory.class);
                        predicates.add(cb.equal(rootHistory.get("action"), "RequestUpdate"));
                        predicates.add(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(0).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(11).get("end").atZone(ZoneId.systemDefault()).toInstant())));
                        predicates.add(cb.equal(root.get("taskProcInstId"), rootHistory.get("procInstId")));
                        predicates.add(cb.equal(root.get("taskDefKey"), rootHistory.get("toTaskKey")));
                        query.distinct(true);
                        break;
                    default:
                        break;
                }

                query.multiselect(
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(0).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(1).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(2).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(3).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(4).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(5).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(6).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(7).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(8).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(9).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(10).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(11).get("start")), 1).otherwise(0))
                ).where(cb.and(predicates.stream().toArray(Predicate[]::new)));
            } else if (type.equals("NEW")) {

                predicates.add(cb.and(cb.between(root.get("taskCreatedTime"), listDatefilter.get(0).get("start"), listDatefilter.get(11).get("end")),
                        root.get("taskAssignee").in(listUser)));
                switch (criteriaType.toUpperCase()) {
                    case "FINISHED":
                        predicates.add(cb.isNotNull(root.get("taskFinishedTime")));
                        break;
                    case "CANCEL":
                        predicates.add(cb.equal(root.get("taskStatus"), "CANCEL"));
                        break;
                    case "RU":
                        Root<BpmHistory> rootHistory = query.from(BpmHistory.class);
                        predicates.add(rootHistory.get("toUser").in(listUser));
                        predicates.add(cb.equal(rootHistory.get("action"), "RequestUpdate"));
                        predicates.add(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(0).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(11).get("end").atZone(ZoneId.systemDefault()).toInstant())));
                        predicates.add(cb.equal(root.get("taskProcInstId"), rootHistory.get("procInstId")));
                        predicates.add(cb.equal(root.get("taskDefKey"), rootHistory.get("toTaskKey")));
                        query.distinct(true);
                        break;
                    default:
                        break;
                }

                query.multiselect(
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(0).get("start"), listDatefilter.get(0).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(1).get("start"), listDatefilter.get(1).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(2).get("start"), listDatefilter.get(2).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(3).get("start"), listDatefilter.get(3).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(4).get("start"), listDatefilter.get(4).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(5).get("start"), listDatefilter.get(5).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(6).get("start"), listDatefilter.get(6).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(7).get("start"), listDatefilter.get(7).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(8).get("start"), listDatefilter.get(8).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(9).get("start"), listDatefilter.get(9).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(10).get("start"), listDatefilter.get(10).get("end")), 1)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(11).get("start"), listDatefilter.get(11).get("end")), 1))
                ).where(cb.and(predicates.toArray(Predicate[]::new)));
            } else {
                Root<BpmHistory> rootHistory = query.from(BpmHistory.class);
                predicates.add(rootHistory.get("toUser").in(listUser));
                predicates.add(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(0).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(11).get("end").atZone(ZoneId.systemDefault()).toInstant())));
                predicates.add(cb.equal(root.get("taskProcInstId"), rootHistory.get("procInstId")));
                predicates.add(cb.equal(root.get("taskDefKey"), rootHistory.get("toTaskKey")));
                predicates.add(cb.equal(rootHistory.get("action"), "RE_CREATED_BY_RU"));
                predicates.add(root.get("taskAssignee").in(listUser));
                switch (criteriaType.toUpperCase()) {
                    case "FINISHED":
                        predicates.add(cb.isNotNull(root.get("taskFinishedTime")));
                        break;
                    case "CANCEL":
                        predicates.add(cb.equal(root.get("taskStatus"), TaskConstants.Status.CANCEL.code));
                        break;
                    case "RU":
                        predicates.add(root.get("taskStatus").in(TaskConstants.TabStatus.PROCESSING));
                        break;
                }

                query.multiselect(
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(0).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(0).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(1).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(1).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(2).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(2).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(3).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(3).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(4).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(4).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(5).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(5).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(6).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(6).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(7).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(7).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(8).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(8).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(9).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(9).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(10).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(10).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(11).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(11).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0))
                ).where(cb.and(predicates.toArray(Predicate[]::new)));
            }

            Object[] listResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : null;

            return listResult;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }finally {
            if(em != null)
                em.close();
        }
    }

    public Object[] WeeklyTaskCount(List<String> listUser, LinkedList<Map<String, LocalDateTime>> listDatefilter, String type, String criteriaType, List<Long> listService, List<String> listPriority) {
        try {
            EntityManager em = entityManagerFactory.createEntityManager();
            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Object[]> query = cb.createQuery(Object[].class);
            Root<BpmTask> root = query.from(BpmTask.class);
            Root<BpmProcInst> rootProcInst = query.from(BpmProcInst.class);
            Root<ServicePackage> rootService = query.from(ServicePackage.class);

            predicates.add(cb.equal(root.get("taskProcInstId"), rootProcInst.get("ticketProcInstId")));
            predicates.add(cb.equal(rootProcInst.get("serviceId"), rootService.get("id")));

            if (listService != null && !listService.isEmpty()) {
                predicates.add(rootService.get("id").in(listService));
            }
            if (listPriority != null && !listPriority.isEmpty()) {
                predicates.add(rootProcInst.get("priority").in(listPriority));
            }

            if (type.equals("BACKLOG")) {

                predicates.add(root.get("taskAssignee").in(listUser));
                predicates.add(cb.lessThanOrEqualTo(root.get("taskCreatedTime"), listDatefilter.get(9).get("end")));
                switch (criteriaType.toUpperCase()) {
                    case "FINISHED":
                        predicates.add(cb.isNotNull(root.get("taskFinishedTime")));
                        break;
                    case "CANCEL":
                        predicates.add(cb.equal(root.get("taskStatus"), "CANCEL"));
                        break;
                    case "RU":
                        Root<BpmHistory> rootHistory = query.from(BpmHistory.class);
                        predicates.add(cb.equal(rootHistory.get("action"), "RequestUpdate"));
                        predicates.add(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(0).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(9).get("end").atZone(ZoneId.systemDefault()).toInstant())));
                        predicates.add(cb.equal(root.get("taskProcInstId"), rootHistory.get("procInstId")));
                        predicates.add(cb.equal(root.get("taskDefKey"), rootHistory.get("toTaskKey")));
                        query.distinct(true);
                        break;
                    default:
                        break;
                }

                query.multiselect(
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(0).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(1).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(2).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(3).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(4).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(5).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(6).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(7).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(8).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(9).get("start")), 1).otherwise(0))
                ).where(cb.and(predicates.stream().toArray(Predicate[]::new)));
            } else if (type.equals("NEW")) {
                predicates.add(cb.between(root.get("taskCreatedTime"), listDatefilter.get(0).get("start"), listDatefilter.get(9).get("end")));
                predicates.add(root.get("taskAssignee").in(listUser));
                switch (criteriaType.toUpperCase()) {
                    case "FINISHED":
                        predicates.add(cb.isNotNull(root.get("taskFinishedTime")));
                        break;
                    case "CANCEL":
                        predicates.add(cb.equal(root.get("taskStatus"), "CANCEL"));
                        break;
                    case "RU":
                        Root<BpmHistory> rootHistory = query.from(BpmHistory.class);
                        predicates.add(rootHistory.get("toUser").in(listUser));
                        predicates.add(cb.equal(rootHistory.get("action"), "RequestUpdate"));
                        predicates.add(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(0).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(9).get("end").atZone(ZoneId.systemDefault()).toInstant())));
                        predicates.add(cb.equal(root.get("taskProcInstId"), rootHistory.get("procInstId")));
                        predicates.add(cb.equal(root.get("taskDefKey"), rootHistory.get("toTaskKey")));
                        query.distinct(true);
                        break;
                    default:
                        break;
                }

                query.multiselect(
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(0).get("start"), listDatefilter.get(0).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(1).get("start"), listDatefilter.get(1).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(2).get("start"), listDatefilter.get(2).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(3).get("start"), listDatefilter.get(3).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(4).get("start"), listDatefilter.get(4).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(5).get("start"), listDatefilter.get(5).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(6).get("start"), listDatefilter.get(6).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(7).get("start"), listDatefilter.get(7).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(8).get("start"), listDatefilter.get(8).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(9).get("start"), listDatefilter.get(9).get("end")), 1).otherwise(0))
                ).where(cb.and(predicates.toArray(Predicate[]::new)));
            } else {
                Root<BpmHistory> rootHistory = query.from(BpmHistory.class);
                predicates.add(rootHistory.get("toUser").in(listUser));
                predicates.add(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(0).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(9).get("end").atZone(ZoneId.systemDefault()).toInstant())));
                predicates.add(cb.equal(root.get("taskProcInstId"), rootHistory.get("procInstId")));
                predicates.add(cb.equal(root.get("taskDefKey"), rootHistory.get("toTaskKey")));
                predicates.add(cb.equal(rootHistory.get("action"), "RE_CREATED_BY_RU"));
                predicates.add(root.get("taskAssignee").in(listUser));
                switch (criteriaType.toUpperCase()) {
                    case "FINISHED":
                        predicates.add(cb.isNotNull(root.get("taskFinishedTime")));
                        break;
                    case "CANCEL":
                        predicates.add(cb.equal(root.get("taskStatus"), TaskConstants.Status.CANCEL.code));
                        break;
                    case "RU":
                        predicates.add(root.get("taskStatus").in(TaskConstants.TabStatus.PROCESSING));
                        break;
                }

                query.multiselect(
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(0).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(0).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(1).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(1).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(2).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(2).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(3).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(3).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(4).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(4).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(5).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(5).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(6).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(6).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(7).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(7).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(8).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(8).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(9).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(9).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0))
                ).where(cb.and(predicates.toArray(Predicate[]::new)));
            }

            Object[] listResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : null;
            em.close();
            return listResult;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Object[] YearlyTaskCount(List<String> listUser, LinkedList<Map<String, LocalDateTime>> listDatefilter, String type, String criteriaType, List<Long> listService, List<String> listPriority) {
        try {
            EntityManager em = entityManagerFactory.createEntityManager();
            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Object[]> query = cb.createQuery(Object[].class);
            Root<BpmTask> root = query.from(BpmTask.class);
            Root<BpmProcInst> rootProcInst = query.from(BpmProcInst.class);
            Root<ServicePackage> rootService = query.from(ServicePackage.class);

            predicates.add(cb.equal(root.get("taskProcInstId"), rootProcInst.get("ticketProcInstId")));
            predicates.add(cb.equal(rootProcInst.get("serviceId"), rootService.get("id")));

            if (listService != null && !listService.isEmpty()) {
                predicates.add(rootService.get("id").in(listService));
            }
            if (listPriority != null && !listPriority.isEmpty()) {
                predicates.add(rootProcInst.get("priority").in(listPriority));
            }

            if (type.equals("BACKLOG")) {

                predicates.add(root.get("taskAssignee").in(listUser));
                predicates.add(cb.lessThanOrEqualTo(root.get("taskCreatedTime"), listDatefilter.get(1).get("end")));
                switch (criteriaType.toUpperCase()) {
                    case "FINISHED":
                        predicates.add(cb.isNotNull(root.get("taskFinishedTime")));
                        break;
                    case "CANCEL":
                        predicates.add(cb.equal(root.get("taskStatus"), "CANCEL"));
                        break;
                    case "RU":
                        Root<BpmHistory> rootHistory = query.from(BpmHistory.class);
                        predicates.add(cb.equal(rootHistory.get("action"), "RequestUpdate"));
                        predicates.add(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(0).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(1).get("end").atZone(ZoneId.systemDefault()).toInstant())));
                        predicates.add(cb.equal(root.get("taskProcInstId"), rootHistory.get("procInstId")));
                        predicates.add(cb.equal(root.get("taskDefKey"), rootHistory.get("toTaskKey")));
                        query.distinct(true);
                        break;
                    default:
                        break;
                }

                query.multiselect(
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(0).get("start")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.lessThan(root.get("taskCreatedTime"), listDatefilter.get(1).get("start")), 1).otherwise(0))
                ).where(root.get("taskAssignee").in(listUser));
            } else if (type.equals("NEW")) {

                predicates.add(cb.between(root.get("taskCreatedTime"), listDatefilter.get(0).get("start"), listDatefilter.get(1).get("end")));
                predicates.add(root.get("taskAssignee").in(listUser));
                switch (criteriaType.toUpperCase()) {
                    case "FINISHED":
                        predicates.add(cb.isNotNull(root.get("taskFinishedTime")));
                        break;
                    case "CANCEL":
                        predicates.add(cb.equal(root.get("taskStatus"), "CANCEL"));
                        break;
                    case "RU":
                        Root<BpmHistory> rootHistory = query.from(BpmHistory.class);
                        predicates.add(rootHistory.get("toUser").in(listUser));
                        predicates.add(cb.equal(rootHistory.get("action"), "RequestUpdate"));
                        predicates.add(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(0).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(1).get("end").atZone(ZoneId.systemDefault()).toInstant())));
                        predicates.add(cb.equal(root.get("taskProcInstId"), rootHistory.get("procInstId")));
                        predicates.add(cb.equal(root.get("taskDefKey"), rootHistory.get("toTaskKey")));
                        query.distinct(true);
                        break;
                    default:
                        break;
                }

                query.multiselect(
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(0).get("start"), listDatefilter.get(0).get("end")), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(root.get("taskCreatedTime"), listDatefilter.get(1).get("start"), listDatefilter.get(1).get("end")), 1).otherwise(0))
                ).where(cb.and(cb.between(root.get("taskCreatedTime"), listDatefilter.get(0).get("start"), listDatefilter.get(12).get("end")),
                        root.get("taskAssignee").in(listUser)));
            } else {
                Root<BpmHistory> rootHistory = query.from(BpmHistory.class);
                predicates.add(rootHistory.get("toUser").in(listUser));
                predicates.add(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(0).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(1).get("end").atZone(ZoneId.systemDefault()).toInstant())));
                predicates.add(cb.equal(root.get("taskProcInstId"), rootHistory.get("procInstId")));
                predicates.add(cb.equal(root.get("taskDefKey"), rootHistory.get("toTaskKey")));
                predicates.add(cb.equal(rootHistory.get("action"), "RE_CREATED_BY_RU"));
                predicates.add(root.get("taskAssignee").in(listUser));
                switch (criteriaType.toUpperCase()) {
                    case "FINISHED":
                        predicates.add(cb.isNotNull(root.get("taskFinishedTime")));
                        break;
                    case "CANCEL":
                        predicates.add(cb.equal(root.get("taskStatus"), TaskConstants.Status.CANCEL.code));
                        break;
                    case "RU":
                        predicates.add(root.get("taskStatus").in(TaskConstants.TabStatus.PROCESSING));
                        break;
                }

                query.multiselect(
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(0).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(0).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0)),
                        cb.sum(cb.<Number>selectCase().when(cb.between(rootHistory.get("time"), Date.from(listDatefilter.get(1).get("start").atZone(ZoneId.systemDefault()).toInstant()), Date.from(listDatefilter.get(1).get("end").atZone(ZoneId.systemDefault()).toInstant())), 1).otherwise(0))
                ).where(cb.and(predicates.toArray(Predicate[]::new)));
            }

            Object[] listResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : null;
            em.close();
            return listResult;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public List<ThirdSectionMiniResponse> countReportByUser(List<String> listUSer, LocalDateTime currentDate, LocalDateTime firstDateOfTerm, String type, List<Long> listService, List<String> listPriority) {
        try {
            EntityManager em = entityManagerFactory.createEntityManager();
            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<ThirdSectionMiniResponse> query = cb.createQuery(ThirdSectionMiniResponse.class);
            Root<BpmTask> root = query.from(BpmTask.class);
            Root<BpmProcInst> rootProcInst = query.from(BpmProcInst.class);
            Root<ServicePackage> rootService = query.from(ServicePackage.class);

            predicates.add(cb.equal(root.get("taskProcInstId"), rootProcInst.get("ticketProcInstId")));
            predicates.add(cb.equal(rootProcInst.get("serviceId"), rootService.get("id")));

            if (listService != null && !listService.isEmpty()) {
                predicates.add(rootService.get("id").in(listService));
            }
            if (listPriority != null && !listPriority.isEmpty()) {
                predicates.add(rootProcInst.get("priority").in(listPriority));
            }
            switch (type) {
                case "FINISHED":
                    predicates.add(root.get("taskAssignee").in(listUSer));
                    query.multiselect(
                            root.get("taskAssignee"),
                            cb.sum(cb.<Number>selectCase().when(cb.and(
                                    cb.lessThan(root.get("taskCreatedTime"), firstDateOfTerm),
                                    cb.greaterThanOrEqualTo(root.get("taskFinishedTime"), firstDateOfTerm),
                                    cb.isNotNull(root.get("taskFinishedTime"))
                            ), 1).otherwise(0)),
                            cb.sum(cb.<Number>selectCase().when(cb.and(
                                    cb.greaterThanOrEqualTo(root.get("taskCreatedTime"), firstDateOfTerm),
                                    cb.isNotNull(root.get("taskFinishedTime"))
                            ), 1).otherwise(0))
                    ).where(cb.and(predicates.stream().toArray(Predicate[]::new)));
                    query.groupBy(root.get("taskAssignee"));
                    break;
                case "TOTAL":
                    predicates.add(root.get("taskAssignee").in(listUSer));
                    query.multiselect(
                            root.get("taskAssignee"),
                            cb.sum(cb.<Number>selectCase().when(cb.and(
                                    cb.lessThan(root.get("taskCreatedTime"), firstDateOfTerm),
                                    cb.or(
                                            cb.greaterThanOrEqualTo(root.get("taskFinishedTime"), firstDateOfTerm),
                                            cb.isNull(root.get("taskFinishedTime"))
                                    )
                            ), 1).otherwise(0)),
                            cb.sum(cb.<Number>selectCase().when(cb.greaterThanOrEqualTo(root.get("taskCreatedTime"), firstDateOfTerm), 1).otherwise(0))
                    ).where(cb.and(predicates.stream().toArray(Predicate[]::new)));
                    query.groupBy(root.get("taskAssignee"));
                    break;
                case "CANCEL":
                    predicates.add(root.get("taskAssignee").in(listUSer));
                    predicates.add(cb.equal(root.get("taskStatus"), "CANCEL"));
                    query.multiselect(
                            root.get("taskAssignee"),
                            cb.sum(cb.<Number>selectCase().when(cb.and(
                                    cb.lessThan(root.get("taskCreatedTime"), firstDateOfTerm),
                                    cb.or(
                                            cb.greaterThanOrEqualTo(root.get("taskFinishedTime"), firstDateOfTerm),
                                            cb.isNull(root.get("taskFinishedTime"))
                                    )
                            ), 1).otherwise(0)),
                            cb.sum(cb.<Number>selectCase().when(cb.greaterThanOrEqualTo(root.get("taskCreatedTime"), firstDateOfTerm), 1).otherwise(0))
                    ).where(cb.and(predicates.stream().toArray(Predicate[]::new)));
                    query.groupBy(root.get("taskAssignee"));
                    break;
                case "RU":
                    Root<BpmHistory> rootHistory = query.from(BpmHistory.class);
                    predicates.add(cb.between(rootHistory.get("time"), Date.from(firstDateOfTerm.atZone(ZoneId.systemDefault()).toInstant()), Date.from(currentDate.atZone(ZoneId.systemDefault()).toInstant())));
                    predicates.add(cb.equal(root.get("taskProcInstId"), rootHistory.get("procInstId")));
                    predicates.add(cb.equal(root.get("taskDefKey"), rootHistory.get("toTaskKey")));
                    predicates.add(cb.equal(rootHistory.get("action"), "RE_CREATED_BY_RU"));
                    predicates.add(cb.notEqual(root.get("taskStatus"), "DELETED_BY_RU"));
                    query.multiselect(
                            root.get("taskAssignee"),
                            cb.sum(cb.<Number>selectCase().when(cb.and(
                                    cb.lessThan(root.get("taskCreatedTime"), firstDateOfTerm),
                                    cb.or(
                                            cb.greaterThanOrEqualTo(root.get("taskFinishedTime"), firstDateOfTerm),
                                            cb.isNull(root.get("taskFinishedTime"))
                                    )
                            ), 1).otherwise(0)),
                            cb.sum(cb.<Number>selectCase().when(cb.greaterThanOrEqualTo(root.get("taskCreatedTime"), firstDateOfTerm), 1).otherwise(0))
                    ).where(cb.and(predicates.stream().toArray(Predicate[]::new)));
                    query.groupBy(root.get("taskAssignee"));
                    break;
            }
            List<ThirdSectionMiniResponse> listResult = em.createQuery(query) != null ? em.createQuery(query).getResultList() : null;
            em.close();
            return listResult;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<ThirdSectionMiniResponse> countRuReportByUser(List<String> listUser, LocalDateTime curentDate, LocalDateTime firstDateOfTerm, String type, List<Long> listService, List<String> listPriority) {
        try {
            EntityManager em = entityManagerFactory.createEntityManager();
            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<ThirdSectionMiniResponse> query = cb.createQuery(ThirdSectionMiniResponse.class);
            Root<BpmTask> root = query.from(BpmTask.class);
            Root<BpmProcInst> rootProcInst = query.from(BpmProcInst.class);
            Root<ServicePackage> rootService = query.from(ServicePackage.class);

            predicates.add(cb.equal(root.get("taskProcInstId"), rootProcInst.get("ticketProcInstId")));
            predicates.add(cb.equal(rootProcInst.get("serviceId"), rootService.get("id")));

            if (listService != null && !listService.isEmpty()) {
                predicates.add(rootService.get("id").in(listService));
            }
            if (listPriority != null && !listPriority.isEmpty()) {
                predicates.add(rootProcInst.get("priority").in(listPriority));
            }

            Root<BpmHistory> rootHistory = query.from(BpmHistory.class);
//                predicates.add(cb.equal(rootHistory.get("action"), "RequestUpdate"));
            predicates.add(cb.between(rootHistory.get("time"), Date.from(firstDateOfTerm.atZone(ZoneId.systemDefault()).toInstant()), Date.from(curentDate.atZone(ZoneId.systemDefault()).toInstant())));
            predicates.add(cb.equal(root.get("taskProcInstId"), rootHistory.get("procInstId")));
            predicates.add(cb.equal(root.get("taskDefKey"), rootHistory.get("toTaskKey")));
            predicates.add(cb.equal(rootHistory.get("action"), "RE_CREATED_BY_RU"));
            predicates.add(root.get("taskAssignee").in(listUser));

            switch (type) {
                case "FINISHED":
                    predicates.add(cb.isNotNull(root.get("taskFinishedTime")));
                    query.multiselect(
                            root.get("taskAssignee"),
                            cb.count(root.get("taskId"))
                    ).where(cb.and(predicates.stream().toArray(Predicate[]::new)));
                    query.groupBy(root.get("taskAssignee"));
                    break;
                case "TOTAL":
                    query.multiselect(
                            root.get("taskAssignee"),
                            cb.count(root.get("taskId"))
                    ).where(cb.and(predicates.stream().toArray(Predicate[]::new)));
                    query.groupBy(root.get("taskAssignee"));
                    break;
                case "CANCEL":
                    predicates.add(cb.equal(root.get("taskStatus"), TaskConstants.Status.CANCEL.code));
                    query.multiselect(
                            root.get("taskAssignee"),
                            cb.count(root.get("taskId"))
                    ).where(cb.and(predicates.stream().toArray(Predicate[]::new)));
                    query.groupBy(root.get("taskAssignee"));
                    break;
                case "RU":
                    predicates.add(root.get("taskStatus").in(TaskConstants.TabStatus.PROCESSING));
                    query.multiselect(
                            root.get("taskAssignee"),
                            cb.count(root.get("taskId"))
                    ).where(cb.and(predicates.toArray(Predicate[]::new)));
                    query.groupBy(root.get("taskAssignee"));
                    break;
            }
            List<ThirdSectionMiniResponse> listResult = em.createQuery(query) != null ? em.createQuery(query).getResultList() : null;
            em.close();
            return listResult;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public GaugeChartResponse getGaugeChart(List<String> listUser, LocalDateTime firstDateOfTerm, LocalDateTime currentDate, List<Long> listService, List<String> listPriority) {
        try {
            EntityManager em = entityManagerFactory.createEntityManager();
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<GaugeChartResponse> query = cb.createQuery(GaugeChartResponse.class);
            List<Predicate> predicates = new ArrayList<>();
            Root<BpmTask> root = query.from(BpmTask.class);
            Root<BpmProcInst> rootProcInst = query.from(BpmProcInst.class);
            Root<ServicePackage> rootService = query.from(ServicePackage.class);

            predicates.add(cb.equal(root.get("taskProcInstId"), rootProcInst.get("ticketProcInstId")));
            predicates.add(cb.equal(rootProcInst.get("serviceId"), rootService.get("id")));

            if (listService != null && !listService.isEmpty()) {
                predicates.add(rootService.get("id").in(listService));
            }
            if (listPriority != null && !listPriority.isEmpty()) {
                predicates.add(rootProcInst.get("priority").in(listPriority));
            }
            predicates.add(cb.or(cb.between(root.get("taskFinishedTime"), currentDate, firstDateOfTerm),
                    cb.between(root.get("taskStartedTime"), currentDate, firstDateOfTerm)));
            predicates.add(root.get("taskAssignee").in(listUser));
            query.multiselect(
                    cb.avg(root.get("responseDuration")),
                    cb.avg(root.get("slaResponse")),
                    cb.avg(root.get("finishDuration")),
                    cb.avg(root.get("slaFinish"))
            ).where(cb.and(predicates.stream().toArray(Predicate[]::new)));
            GaugeChartResponse listResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : null;
            em.close();
            return listResult;
        } catch (Exception e) {
            return new GaugeChartResponse();
        }
    }


    public Map<String, Object> getGetLastSection(List<String> listUser, LocalDateTime currentDate, LocalDateTime firstDateOfTerm, List<Long> listService, List<String> listPriority, Integer page, Integer limit) {
        try {
            Map<String, Object> mapFinal = new HashMap<>();
            EntityManager em = entityManagerFactory.createEntityManager();
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<LastSectionResponse> query = cb.createQuery(LastSectionResponse.class);
            Root<BpmTask> root = query.from(BpmTask.class);

            Root<BpmProcInst> rootTicket = query.from(BpmProcInst.class);
            Root<ServicePackage> rootService = query.from(ServicePackage.class);
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(root.get("taskAssignee").in(listUser));
            predicates.add(cb.equal(root.get("taskProcInstId"), rootTicket.get("procInstId")));
            predicates.add(cb.equal(rootTicket.get("serviceId"), rootService.get("id")));
            predicates.add(cb.equal(root.get("taskStatus"), "DELETED_BY_RU"));

            if (listService != null && !listService.isEmpty()) {
                predicates.add(rootService.get("id").in(listService));
            }
            if (listPriority != null && !listPriority.isEmpty()) {
                predicates.add(rootTicket.get("priority").in(listPriority));
            }

            //--------------------COUNT QUERY-------------------------//
            CriteriaQuery<Long> queryCount = cb.createQuery(Long.class);
            Root<BpmTask> countRoot = queryCount.from(BpmTask.class);
            Root<BpmProcInst> countTicket = queryCount.from(BpmProcInst.class);
            Root<ServicePackage> countService = queryCount.from(ServicePackage.class);
            List<Predicate> predCount = new ArrayList<>();
            predCount.add(countRoot.get("taskAssignee").in(listUser));
            predCount.add(cb.equal(countRoot.get("taskProcInstId"), countTicket.get("procInstId")));
            predCount.add(cb.equal(countTicket.get("serviceId"), countService.get("id")));
            //--------------------------------------------------------//
            predicates.add(cb.or(
                    cb.and(
                            cb.lessThan(root.get("taskCreatedTime"), firstDateOfTerm),
                            cb.or(cb.isNull(root.get("taskFinishedTime")),
                                    cb.greaterThanOrEqualTo(root.get("taskFinishedTime"), firstDateOfTerm))
                    ),
                    cb.between(root.get("taskCreatedTime"), firstDateOfTerm, currentDate)
            ));
            query.multiselect(
                    rootService.get("serviceName"),
                    cb.sum(cb.<Number>selectCase().when(
                            cb.or(
                                    cb.and(
                                            cb.lessThan(root.get("taskCreatedTime"), firstDateOfTerm),
                                            cb.or(cb.isNull(root.get("taskFinishedTime")),
                                                    cb.greaterThanOrEqualTo(root.get("taskFinishedTime"), firstDateOfTerm))
                                    ),
                                    cb.and(
                                            cb.between(root.get("taskCreatedTime"), firstDateOfTerm, currentDate)
                                    )
                            )
                            , 1).otherwise(0)),
                    cb.sum(cb.<Number>selectCase().when(
                            cb.and(
                                    cb.or(
                                            cb.lessThan(root.get("taskCreatedTime"), firstDateOfTerm),
                                            cb.between(root.get("taskCreatedTime"), firstDateOfTerm, currentDate)
                                    ),
                                    cb.and(
                                            cb.isNotNull(root.get("taskFinishedTime")),
                                            cb.notEqual(root.get("taskStatus"), "DELETED_BY_RU"),
                                            cb.greaterThan(root.get("taskFinishedTime"), root.get("slaFinishTime"))
                                    )

                            )
                            , 1).otherwise(0)),
                    cb.sum(cb.<Number>selectCase().when(
                            cb.and(
                                    cb.or(
                                            cb.lessThan(root.get("taskCreatedTime"), firstDateOfTerm),
                                            cb.between(root.get("taskCreatedTime"), firstDateOfTerm, currentDate)
                                    ),
                                    cb.and(
                                            cb.isNotNull(root.get("taskFinishedTime")),
                                            cb.notEqual(root.get("taskStatus"), "DELETED_BY_RU"),
                                            cb.lessThanOrEqualTo(root.get("taskFinishedTime"), root.get("slaFinishTime"))
                                    )

                            )
                            , 1).otherwise(0)),
                    cb.sum(cb.<Number>selectCase().when(
                            cb.and(
                                    cb.or(
                                            cb.and(
                                                    cb.lessThan(root.get("taskCreatedTime"), firstDateOfTerm),
                                                    cb.or(cb.isNull(root.get("taskFinishedTime")),
                                                            cb.greaterThanOrEqualTo(root.get("taskFinishedTime"), firstDateOfTerm))
                                            ),
                                            cb.between(root.get("taskCreatedTime"), firstDateOfTerm, currentDate)
                                    ),
                                    cb.equal(root.get("taskStatus"), "CANCEL")

                            )
                            , 1).otherwise(0)),
                    cb.sum(cb.<Number>selectCase().when(
                            cb.and(
                                    cb.or(
                                            cb.lessThan(root.get("taskCreatedTime"), firstDateOfTerm),
                                            cb.between(root.get("taskCreatedTime"), firstDateOfTerm, currentDate)
                                    ),
                                    cb.isNull(root.get("taskFinishedTime"))
                            )
                            , 1).otherwise(0))
            ).where(cb.and(predicates.stream().toArray(Predicate[]::new)));
            query.groupBy(rootService.get("serviceName"));

            queryCount.select(countService.get("serviceName")).where(cb.and(predCount.stream().toArray(Predicate[]::new)));
            queryCount.groupBy(countService.get("serviceName"));

            List<LastSectionResponse> listResult = em.createQuery(query) != null ? em.createQuery(query).
                    setFirstResult((page - 1) * limit)
                    .setMaxResults(limit).getResultList() : new ArrayList<>();
            Long count = em.createQuery(queryCount).getSingleResult();
            em.close();
            mapFinal.put("data", listResult);
            mapFinal.put("count", count);
            return mapFinal;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public Map<String, Object> getReportTask(String reportType, String filterType, LocalDateTime firstDateOfTerm, Integer serviceId,
                                             List<String> listUser, String search, Integer page, Integer limit, String sortBy, String sortType,
                                             List<String> filterTaskType, List<String> filterTaskStatus, List<String> filterPriority) {

        Map<String, Object> map = new HashMap<>();
        try {
            EntityManager em = entityManagerFactory.createEntityManager();
            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<ReportTaskResponse> query = cb.createQuery(ReportTaskResponse.class);
            Root<BpmTask> bpmTaskRoot = query.from(BpmTask.class);
            Root<BpmProcInst> bpmProcInstRoot = query.from(BpmProcInst.class);
            Root<ServicePackage> servicePackageRoot = query.from(ServicePackage.class);

            CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
            Root<BpmTask> countTask = countQuery.from(BpmTask.class);
            Root<BpmProcInst> countProcInstRoot = countQuery.from(BpmProcInst.class);
            Root<ServicePackage> countServicePackageRoot = countQuery.from(ServicePackage.class);

            //count
            List<Predicate> countPredicates = new ArrayList<>();

            countPredicates.add(cb.equal(countTask.get("taskProcInstId"), countProcInstRoot.get("ticketProcInstId")));
            countPredicates.add(cb.equal(countProcInstRoot.get("serviceId"), countServicePackageRoot.get("id")));

            predicates.add(cb.equal(bpmTaskRoot.get("taskProcInstId"), bpmProcInstRoot.get("ticketProcInstId")));
            predicates.add(cb.equal(bpmProcInstRoot.get("serviceId"), servicePackageRoot.get("id")));

            // list User
            if (listUser != null && !listUser.isEmpty()) {
                countPredicates.add(countTask.get("taskAssignee").in(listUser));
                predicates.add(bpmTaskRoot.get("taskAssignee").in(listUser));
            }


            // search
            if (search != null) {
                predicates.add(cb.like(bpmTaskRoot.get("taskName"), "%" + search.trim() + "%"));
                countPredicates.add(cb.like(countTask.get("taskName"), "%" + search.trim() + "%"));
            }

            if (serviceId != null) {
                predicates.add(cb.equal(bpmProcInstRoot.get("serviceId"), serviceId));
            }

            switch (filterType) {
                case "backlog":
                    predicates.add(cb.lessThan(bpmTaskRoot.get("taskCreatedTime"), firstDateOfTerm));
                    predicates.add(cb.or(cb.greaterThanOrEqualTo(bpmTaskRoot.get("taskFinishedTime"), firstDateOfTerm),
                            cb.isNull(bpmTaskRoot.get("taskFinishedTime"))));

                    countPredicates.add(cb.lessThan(countTask.get("taskCreatedTime"), firstDateOfTerm));
                    countPredicates.add(cb.or(cb.greaterThanOrEqualTo(countTask.get("taskFinishedTime"), firstDateOfTerm),
                            cb.isNull(countTask.get("taskFinishedTime"))));
                    break;
                case "new":
                    predicates.add(cb.greaterThanOrEqualTo(bpmTaskRoot.get("taskCreatedTime"), firstDateOfTerm));

                    countPredicates.add(cb.greaterThanOrEqualTo(countTask.get("taskCreatedTime"), firstDateOfTerm));
                    break;
                case "requestUpdate":
                    Root<BpmHistory> bpmHistoryRoot = query.from(BpmHistory.class);
                    predicates.add(cb.equal(bpmHistoryRoot.get("action"), "RequestUpdate"));
                    predicates.add(cb.greaterThanOrEqualTo(bpmHistoryRoot.get("createdTime"), Date.from(firstDateOfTerm.atZone(ZoneId.systemDefault()).toInstant())));
                    predicates.add(cb.equal(bpmTaskRoot.get("taskProcInstId"), bpmHistoryRoot.get("procInstId")));
                    predicates.add(cb.equal(bpmTaskRoot.get("taskDefKey"), bpmHistoryRoot.get("toTaskKey")));

                    Root<BpmHistory> countHistoryRoot = countQuery.from(BpmHistory.class);
                    countPredicates.add(cb.equal(countHistoryRoot.get("action"), "RequestUpdate"));
                    countPredicates.add(cb.greaterThanOrEqualTo(countHistoryRoot.get("createdTime"), Date.from(firstDateOfTerm.atZone(ZoneId.systemDefault()).toInstant())));
                    countPredicates.add(cb.equal(countTask.get("taskProcInstId"), countHistoryRoot.get("procInstId")));
                    countPredicates.add(cb.equal(countTask.get("taskDefKey"), countHistoryRoot.get("toTaskKey")));
                    break;
                default:
                    predicates.add(cb.or(cb.and(cb.lessThan(bpmTaskRoot.get("taskCreatedTime"), firstDateOfTerm),
                                    cb.or(cb.greaterThanOrEqualTo(bpmTaskRoot.get("taskFinishedTime"), firstDateOfTerm),
                                            cb.isNull(bpmTaskRoot.get("taskFinishedTime")))),
                            cb.greaterThanOrEqualTo(bpmTaskRoot.get("taskCreatedTime"), firstDateOfTerm)));

                    countPredicates.add(cb.or(cb.and(cb.lessThan(countTask.get("taskCreatedTime"), firstDateOfTerm),
                                    cb.or(cb.greaterThanOrEqualTo(countTask.get("taskFinishedTime"), firstDateOfTerm),
                                            cb.isNull(countTask.get("taskFinishedTime")))),
                            cb.greaterThanOrEqualTo(countTask.get("taskCreatedTime"), firstDateOfTerm)));
                    break;
            }

            // report type
            switch (reportType) {
                case "Late":
                    predicates.add(cb.greaterThan(bpmTaskRoot.get("taskFinishedTime"), bpmTaskRoot.get("slaFinishTime")));
                    countPredicates.add(cb.greaterThan(countTask.get("taskFinishedTime"), countTask.get("slaFinishTime")));
                    break;
                case "OnTime":
                    predicates.add(cb.greaterThanOrEqualTo(bpmTaskRoot.get("slaFinishTime"), bpmTaskRoot.get("taskFinishedTime")));
                    countPredicates.add(cb.greaterThanOrEqualTo(countTask.get("slaFinishTime"), countTask.get("taskFinishedTime")));
                    break;
                case "Cancel":
                    predicates.add(cb.equal(bpmTaskRoot.get("taskStatus"), "CANCEL"));
                    countPredicates.add(cb.equal(countTask.get("taskStatus"), "CANCEL"));
                    break;
                case "AverageTime":
                    predicates.add(cb.isNotNull(bpmTaskRoot.get("taskFinishedTime")));
                    countPredicates.add(cb.isNotNull(countTask.get("taskFinishedTime")));
                    break;
                // da xu ly 88h
                case "Finished":
                    predicates.add(cb.or(cb.isNotNull(bpmTaskRoot.get("taskFinishedTime")),
                            cb.greaterThanOrEqualTo(bpmTaskRoot.get("taskCreatedTime"), firstDateOfTerm)));

                    predicates.add(cb.and(cb.and(cb.lessThan(bpmTaskRoot.get("taskCreatedTime"), firstDateOfTerm),
                                    cb.or(cb.greaterThanOrEqualTo(bpmTaskRoot.get("taskFinishedTime"), firstDateOfTerm),
                                            cb.isNull(countTask.get("taskFinishedTime")))),
                            cb.greaterThanOrEqualTo(countTask.get("taskCreatedTime"), firstDateOfTerm)));

                    countPredicates.add(cb.or(cb.isNotNull(countTask.get("taskFinishedTime")),
                            cb.greaterThanOrEqualTo(countTask.get("taskCreatedTime"), firstDateOfTerm)));

                    countPredicates.add(cb.and(cb.and(cb.lessThan(countTask.get("taskCreatedTime"), firstDateOfTerm),
                                    cb.or(cb.greaterThanOrEqualTo(countTask.get("taskFinishedTime"), firstDateOfTerm),
                                            cb.isNull(countTask.get("taskFinishedTime")))),
                            cb.greaterThanOrEqualTo(countTask.get("taskCreatedTime"), firstDateOfTerm)));
                    break;
                // 465h xu ly
                case "Process":
                    predicates.add(cb.and(cb.isNotNull(bpmTaskRoot.get("taskFinishedTime")),
                            cb.greaterThanOrEqualTo(bpmTaskRoot.get("taskFinishedTime"), firstDateOfTerm)));

                    countPredicates.add(cb.and(cb.isNotNull(countTask.get("taskFinishedTime")),
                            cb.greaterThanOrEqualTo(countTask.get("taskFinishedTime"), firstDateOfTerm)));
                    break;
                case "Feedback":
                    predicates.add(cb.and(cb.isNotNull(bpmTaskRoot.get("taskStartedTime")),
                            cb.greaterThanOrEqualTo(bpmTaskRoot.get("taskStartedTime"), firstDateOfTerm)));

                    countPredicates.add(cb.and(cb.isNotNull(countTask.get("taskStartedTime")),
                            cb.greaterThanOrEqualTo(countTask.get("taskStartedTime"), firstDateOfTerm)));
                    break;
                case "AlreadyEvaluate":
                    predicates.add(cb.and(cb.isNotNull(bpmProcInstRoot.get("ticketClosedTime")),
                            cb.greaterThanOrEqualTo(bpmProcInstRoot.get("ticketClosedTime"), firstDateOfTerm)));

                    countPredicates.add(cb.and(cb.isNotNull(countProcInstRoot.get("ticketClosedTime")),
                            cb.greaterThanOrEqualTo(countProcInstRoot.get("ticketClosedTime"), firstDateOfTerm)));
                    break;
                case "PassTermEvaluate":
                    predicates.add(cb.and(cb.isNotNull(bpmProcInstRoot.get("ticketFinishTime")),
                            cb.greaterThanOrEqualTo(bpmProcInstRoot.get("ticketFinishTime"), firstDateOfTerm)));

                    predicates.add(cb.isNull(bpmProcInstRoot.get("ticketClosedTime")));

                    countPredicates.add(cb.and(cb.isNotNull(bpmProcInstRoot.get("ticketFinishTime")),
                            cb.greaterThanOrEqualTo(bpmProcInstRoot.get("ticketFinishTime"), firstDateOfTerm)));

                    countPredicates.add(cb.isNull(bpmProcInstRoot.get("ticketClosedTime")));
                    break;
                case "WaitingEvaluate":
                    predicates.add(cb.and(cb.isNotNull(bpmProcInstRoot.get("ticketFinishTime")),
                            cb.lessThanOrEqualTo(bpmProcInstRoot.get("ticketFinishTime"), firstDateOfTerm)));

                    predicates.add(cb.isNull(bpmProcInstRoot.get("ticketClosedTime")));

                    countPredicates.add(cb.and(cb.isNotNull(bpmProcInstRoot.get("ticketFinishTime")),
                            cb.lessThanOrEqualTo(bpmProcInstRoot.get("ticketFinishTime"), firstDateOfTerm)));

                    countPredicates.add(cb.isNull(bpmProcInstRoot.get("ticketClosedTime")));
                    break;
                case "AutoClose":
                    predicates.add(cb.equal(bpmProcInstRoot.get("ticketStatus"), "CANCEL"));
                    predicates.add(cb.equal(bpmProcInstRoot.get("isAuto"), true));
                    predicates.add(cb.greaterThanOrEqualTo(bpmProcInstRoot.get("ticketCanceledTime"), firstDateOfTerm));

                    countPredicates.add(cb.equal(bpmProcInstRoot.get("ticketStatus"), "CANCEL"));
                    countPredicates.add(cb.equal(bpmProcInstRoot.get("isAuto"), true));
                    countPredicates.add(cb.greaterThanOrEqualTo(bpmProcInstRoot.get("ticketCanceledTime"), firstDateOfTerm));
                    break;
            }


            //------------------------sort----------------------------------------------//
            if (sortType.equalsIgnoreCase("asc")) {
                switch (sortBy) {
                    case "id":
                        query.orderBy(cb.asc(bpmTaskRoot.get("id")));
                        break;
                    case "title":
                        query.orderBy(cb.asc(bpmProcInstRoot.get("ticketTitle")));
                        break;
                    case "taskName":
                        query.orderBy(cb.asc(bpmTaskRoot.get("taskName")));
                        break;
                    case "taskType":
                        query.orderBy(cb.asc(bpmTaskRoot.get(BpmTask_.taskType)));
                        break;
                    case "taskAssignee":
                        query.orderBy(cb.asc(bpmTaskRoot.get("taskAssignee")));
                        break;
                    case "rating":
                        query.orderBy(cb.asc(bpmProcInstRoot.get("rating")));
                        break;
                    case "comment":
                        query.orderBy(cb.asc(bpmProcInstRoot.get("comment")));
                        break;
                    case "taskStatus":
                        query.orderBy(cb.asc(bpmTaskRoot.get("taskStatus")));
                        break;
                    case "priority":
                        query.orderBy(cb.asc(bpmProcInstRoot.get("priority")));
                        break;
                }
            } else {
                switch (sortBy) {
                    case "id":
                        query.orderBy(cb.desc(bpmTaskRoot.get("id")));
                        break;
                    case "title":
                        query.orderBy(cb.desc(bpmProcInstRoot.get("ticketTitle")));
                        break;
                    case "taskName":
                        query.orderBy(cb.desc(bpmTaskRoot.get("taskName")));
                        break;
                    case "taskType":
                        query.orderBy(cb.desc(bpmTaskRoot.get(BpmTask_.taskType)));
                        break;
                    case "taskAssignee":
                        query.orderBy(cb.desc(bpmTaskRoot.get("taskAssignee")));
                        break;
                    case "rating":
                        query.orderBy(cb.desc(bpmProcInstRoot.get("rating")));
                        break;
                    case "comment":
                        query.orderBy(cb.desc(bpmProcInstRoot.get("comment")));
                        break;
                    case "taskStatus":
                        query.orderBy(cb.desc(bpmTaskRoot.get("taskStatus")));
                        break;
                    case "priority":
                        query.orderBy(cb.desc(bpmProcInstRoot.get("priority")));
                        break;
                }
            }

            //------------------------filter----------------------------------------------//
            if (filterTaskType != null) {
                predicates.add(bpmTaskRoot.get(BpmTask_.taskType).in(filterTaskType));

                countPredicates.add(countTask.get(BpmTask_.taskType).in(filterTaskType));
            }

            if (filterTaskStatus != null) {
                predicates.add(bpmProcInstRoot.get("taskStatus").in(filterTaskStatus));

                countPredicates.add(countProcInstRoot.get("taskStatus").in(filterTaskStatus));
            }

            if (filterPriority != null) {
                predicates.add(bpmProcInstRoot.get("priority").in(filterPriority));

                countPredicates.add(countProcInstRoot.get("priority").in(filterPriority));
            }

            query.multiselect(
                    bpmTaskRoot.get("id"),
                    bpmProcInstRoot.get("ticketTitle"),
                    bpmTaskRoot.get("taskName"),
                    servicePackageRoot.get("serviceName"),
                    bpmTaskRoot.get("taskType"),
                    bpmTaskRoot.get("taskAssignee"),
                    bpmProcInstRoot.get("priority"),
                    bpmProcInstRoot.get("ticketRating"),
                    bpmProcInstRoot.get("comment"),
                    bpmTaskRoot.get("taskStatus"),
                    bpmTaskRoot.get("taskFinishedTime"),
                    bpmTaskRoot.get("slaFinishTime"),
                    bpmTaskRoot.get("taskStartedTime")
            ).where(cb.and(predicates.stream().toArray(Predicate[]::new)));

            countQuery.select(cb.countDistinct(countTask)).where(cb.and(countPredicates.stream().toArray(Predicate[]::new)));
            Long count = em.createQuery(countQuery).getSingleResult();

            query.distinct(true);
            List<ReportTaskResponse> resultList = em.createQuery(query) != null ? em.createQuery(query).
                    setFirstResult((page - 1) * limit).setMaxResults(limit).getResultList()
                    : new ArrayList<>();
            map.put("data", resultList);
            map.put("count", count);

            em.close();
        } catch (Exception e) {
            e.printStackTrace();
            return new HashMap<>();
        }

        return map;
    }

    public Map<String, Object> searchPrivateFilterTask(PrivateFilterRequest filter, BpmTicketFilterSearchRequest criteria, List<Long> addtionalTicketId) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Map<String, Object> mapFinal = new HashMap<>();

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<PrivateFilterResponse> query = cb.createQuery(PrivateFilterResponse.class);
            CriteriaQuery<Long> queryCount = cb.createQuery(Long.class);

            Root<BpmProcInst> root = query.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackage = root.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priorityManagement = root.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
            Join<BpmProcInst, BpmTask> bpmTask = root.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
            Join<BpmTask, BpmTaskUser> bpmTaskUser = bpmTask.join((BpmTask_.bpmTaskUsers), JoinType.LEFT);
            Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistory = bpmTask.join(BpmTask_.changeAssigneeHistories, JoinType.LEFT);
            Join<ChangeAssigneeHistory, AssignManagement> assignManagement = changeAssigneeHistory.join(ChangeAssigneeHistory_.assignManagement, JoinType.LEFT);

            Predicate predicate = getPredPrivateFilterTask(filter, criteria, query, cb, root, servicePackage, bpmTask, bpmTaskUser, priorityManagement, changeAssigneeHistory, assignManagement, addtionalTicketId);

            Root<BpmProcInst> rootCount = queryCount.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackageCount = rootCount.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priorityManagementCount = rootCount.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
            Join<BpmProcInst, BpmTask> bpmTaskCount = rootCount.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
            Join<BpmTask, BpmTaskUser> bpmTaskUserCount = bpmTaskCount.join((BpmTask_.bpmTaskUsers), JoinType.LEFT);
            Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistoryCount = bpmTaskCount.join(BpmTask_.changeAssigneeHistories, JoinType.LEFT);
            Join<ChangeAssigneeHistory, AssignManagement> assignManagementCount = changeAssigneeHistoryCount.join(ChangeAssigneeHistory_.assignManagement, JoinType.LEFT);

            Predicate predicateCount = getPredPrivateFilterTask(filter, criteria, queryCount, cb, rootCount, servicePackageCount, bpmTaskCount, bpmTaskUserCount, priorityManagementCount, changeAssigneeHistoryCount, assignManagementCount, addtionalTicketId);

            List<Selection<?>> inputs = new ArrayList<>();
            inputs.add(root.get("ticketId"));
            inputs.add(root.get("ticketProcInstId"));
            inputs.add(root.get("requestCode"));
            inputs.add(root.get("ticketTitle"));
            inputs.add(root.get("ticketStatus"));
            inputs.add(servicePackage.get("serviceName"));
            inputs.add(root.get("ticketCreatedTime"));
            inputs.add(root.get("createdUser"));
            inputs.add(root.get("companyCode"));
            inputs.add(root.get("chartNodeName"));
            inputs.add(priorityManagement.get("name"));
            inputs.add(cb.coalesce(root.get("ticketCanceledTime"), root.get("ticketFinishTime")));
            inputs.add(cb.coalesce(root.get("cancelUser"), ""));
            inputs.add(root.get("ticketProcDefId"));
            inputs.add(root.get("ticketStartActId"));
            inputs.add(root.get("serviceId"));
            inputs.add(root.get("chartId"));

            query.multiselect(inputs).where(predicate);
            query.distinct(true);

            List<PrivateFilterResponse> listResult = em.createQuery(query) != null ? em.createQuery(query).
                    setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList() : new ArrayList<>();
            queryCount.select(cb.countDistinct(rootCount)).where(predicateCount);
            Long count = em.createQuery(queryCount).getSingleResult();

            mapFinal.put("count", count);
            mapFinal.put("data", listResult);
            return mapFinal;
        }catch (Exception e){
            return null;
        }finally {
            if(em != null)
                em.close();
        }
    }

    public Predicate getPredPrivateFilterTask(
            PrivateFilterRequest filter,
            BpmTicketFilterSearchRequest criteria,
            CriteriaQuery<?> query,
            CriteriaBuilder cb,
            Root<BpmProcInst> root,
            Join<BpmProcInst, ServicePackage> servicePackage,
            Join<BpmProcInst, BpmTask> bpmTask,
            Join<BpmTask, BpmTaskUser> bpmTaskUser,
            Join<BpmProcInst, PriorityManagement> priorityManagement,
            Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistory,
            Join<ChangeAssigneeHistory, AssignManagement> assignManagement,
            List<Long> additionalTicketId
    ) {
        try {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(cb.not(root.get(BpmProcInst_.ticketStatus).in(Arrays.asList(ProcInstConstants.Status.DELETED.code, ProcInstConstants.Status.DRAFT.code))));

            predicates.add(cb.or(
                    cb.equal(bpmTask.get(BpmTask_.taskAssignee), criteria.getUsername()),
                    cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.orgAssignee), criteria.getUsername()),
                    cb.equal(bpmTaskUser.get(BpmTaskUser_.userName), criteria.getUsername()) // đồng duyệt
            ));

            // approval - execute
            predicates.add(cb.equal(bpmTask.get(BpmTask_.taskType), criteria.getType().toUpperCase()));

            if (!ValidationUtils.isNullOrEmpty(filter.getTicketTitle())) {
                predicates.add(cb.like(root.get(BpmProcInst_.ticketTitle), "%" + filter.getTicketTitle().trim() + "%"));
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListTicketStatus())) {
                if (filter.getListTicketStatus().contains(ProcInstConstants.Status.PROCESSING.code)) {
                    filter.getListTicketStatus().add(ProcInstConstants.Status.OPENED.code);
                }
                if (filter.getListTicketStatus().contains(ProcInstConstants.Status.COMPLETED.code)) {
                    filter.getListTicketStatus().add(ProcInstConstants.Status.CLOSED.code);
                }
                predicates.add(root.get(BpmProcInst_.ticketStatus).in(filter.getListTicketStatus()));
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListService())) {
                predicates.add(cb.or(
                        servicePackage.get(ServicePackage_.id).in(filter.getListService()),
                        servicePackage.get(ServicePackage_.specialParentId).in(filter.getListService())
                ));
            }

            // change: search by taskDefKey -> taskName
            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListTaskDefKey())) {
                predicates.add(bpmTask.get(BpmTask_.taskName).in(filter.getListTaskDefKey()));
            }

            // có listAssignee -> sub task processing
            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListAssignee())) {
                Join<BpmProcInst, BpmTask> subTask = root.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
                predicates.add(subTask.get(BpmTask_.TASK_STATUS).in(TaskConstants.TabStatus.PROCESSING));

                if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListAssignee())) {
                    Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistorySub = subTask.join(BpmTask_.changeAssigneeHistories, JoinType.LEFT);
                    predicates.add(cb.or(
                            subTask.get(BpmTask_.TASK_ASSIGNEE).in(filter.getListAssignee()),
                            changeAssigneeHistorySub.get(ChangeAssigneeHistory_.orgAssignee).in(filter.getListAssignee())
                    ));
                }
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListTaskStatus())) {
                if (filter.getListTaskStatus().contains(TaskConstants.Status.PROCESSING.code)) {
                    filter.getListTaskStatus().add(TaskConstants.Status.ACTIVE.code);
                }
                if (!filter.getListTaskStatus().contains(TaskConstants.Status.CANCEL.code)) {
                    predicates.add(cb.equal(root.get(BpmProcInst_.ticketStatus), ProcInstConstants.Status.CANCEL.code).not());
                }
                if (filter.getListTaskStatus().contains(ProcInstConstants.Status.RECALLING.code)) {
                    predicates.add(
                            cb.or(
                                    bpmTask.get(BpmTask_.taskStatus).in(filter.getListTaskStatus()),
                                    cb.and(
                                            cb.equal(root.get(BpmProcInst_.TICKET_STATUS), ProcInstConstants.Status.RECALLING.code),
                                            bpmTask.get(BpmTask_.taskStatus).in(Arrays.asList(TaskConstants.Status.ACTIVE.code, TaskConstants.Status.PROCESSING.code))
                                    )
                            )
                    );
                } else {
                    predicates.add(bpmTask.get(BpmTask_.taskStatus).in(filter.getListTaskStatus()));
                }
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListChartNodeId())) {
                predicates.add(root.get(BpmProcInst_.chartNodeId).in(filter.getListChartNodeId()));
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListChartId())) {
                predicates.add(root.get(BpmProcInst_.chartId).in(filter.getListChartId()));
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListCreatedUser())) {
                predicates.add(root.get(BpmProcInst_.createdUser).in(filter.getListCreatedUser()));
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListTaskPriority())) {
                predicates.add(priorityManagement.get(PriorityManagement_.id).in(filter.getListTaskPriority()));
            }

            // Phê duyệt bởi -> taskStatus = COMPLETED
            if (!ValidationUtils.isNullOrEmpty(filter.getListApprovalByStatus())) {
                // có ủy quyền
                predicates.add(cb.isTrue(bpmTask.get(BpmTask_.assignType)));

                predicates.add(cb.equal(bpmTask.get(BpmTask_.taskStatus), TaskConstants.Status.COMPLETED.code));
                List<Predicate> predicatesApprovalBy = new ArrayList<>();

                if (filter.getListApprovalByStatus().contains("assignUser") || filter.getListApprovalByStatus().contains("-1")) { // người ủy quyền ký
                    predicatesApprovalBy.add(cb.and(
//                            cb.equal(bpmTask.get(BpmTask_.actionUser), criteria.getUsername()),
                            cb.equal(bpmTask.get(BpmTask_.actionUser), bpmTask.get(BpmTask_.taskAssignee)).not()
                    ));
                }
                if (filter.getListApprovalByStatus().contains("assignedUser") || filter.getListApprovalByStatus().contains("-1")) { // người được ủy quyền ký
                    predicatesApprovalBy.add(cb.and(
//                            cb.equal(bpmTask.get(BpmTask_.actionUser), criteria.getUsername()),
                            cb.equal(bpmTask.get(BpmTask_.actionUser), bpmTask.get(BpmTask_.taskAssignee))
                    ));
                }
                // combine 2 predicate
                predicates.add(cb.or(predicatesApprovalBy.toArray(new Predicate[0])));
            }

            // Ủy quyền
            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListAssignStatus())) {
                LocalDate now = LocalDate.now();
                List<Predicate> predicatesAssignStatus = new ArrayList<>();

                // Đang hiệu lực
                if (filter.getListAssignStatus().contains("active")) { // Đang hiệu lực
                    predicatesAssignStatus.add(cb.and(
                            cb.isTrue(bpmTask.get(BpmTask_.assignType)),
                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.orgAssignee), criteria.getUsername()),
                            cb.or(
                                    cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.type), 0),
                                    cb.and(
                                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.type), 1),
                                            cb.lessThanOrEqualTo(assignManagement.get(AssignManagement_.startDate), now),
                                            cb.greaterThanOrEqualTo(assignManagement.get(AssignManagement_.endDate), now),
                                            assignManagement.get(AssignManagement_.status).in(0, 1)
                                    )
                            )
                    ));
                }
                // Hết hiệu lực
                if (filter.getListAssignStatus().contains("expired")) {
                    predicatesAssignStatus.add(cb.and(
                            cb.isTrue(bpmTask.get(BpmTask_.assignType)),
                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.orgAssignee), criteria.getUsername()),
                            cb.and(
                                    cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.type), 1),
                                    cb.or(
                                            cb.lessThan(assignManagement.get(AssignManagement_.endDate), now),
                                            cb.greaterThan(assignManagement.get(AssignManagement_.startDate), now),
                                            assignManagement.get(AssignManagement_.status).in(0, 1).not()
                                    )
                            )
                    ));
                }
                // Không có ủy quyền
                if (filter.getListAssignStatus().contains("none")) {
                    predicatesAssignStatus.add(cb.and(
                            cb.isNull(bpmTask.get(BpmTask_.assignType))
                    ));
                }

                // combine predicate
                predicates.add(cb.or(predicatesAssignStatus.toArray(new Predicate[0])));
            }

            // Được ủy quyền
            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListAssignedStatus())) {
                LocalDate now = LocalDate.now();
                List<Predicate> predicatesAssignedStatus = new ArrayList<>();

                // Đang hiệu lực
                if (filter.getListAssignedStatus().contains("active")) {
                    predicatesAssignedStatus.add(cb.and(
                            cb.isTrue(bpmTask.get(BpmTask_.assignType)),
                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.toAssignee), criteria.getUsername()),
                            cb.or(
                                    cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.type), 0),
                                    cb.and(
                                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.type), 1),
                                            cb.lessThanOrEqualTo(assignManagement.get(AssignManagement_.startDate), now),
                                            cb.greaterThanOrEqualTo(assignManagement.get(AssignManagement_.endDate), now),
                                            assignManagement.get(AssignManagement_.status).in(0, 1)
                                    )
                            )
                    ));
                }
                // Hết hiệu lực
                if (filter.getListAssignedStatus().contains("expired")) {
                    predicatesAssignedStatus.add(cb.and(
                            cb.isTrue(bpmTask.get(BpmTask_.assignType)),
                            cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.toAssignee), criteria.getUsername()),
                            cb.and(
                                    cb.equal(changeAssigneeHistory.get(ChangeAssigneeHistory_.type), 1),
                                    cb.or(
                                            cb.lessThan(assignManagement.get(AssignManagement_.endDate), now),
                                            cb.greaterThan(assignManagement.get(AssignManagement_.startDate), now),
                                            assignManagement.get(AssignManagement_.status).in(0, 1).not()
                                    )
                            )
                    ));
                }
                // Không có ủy quyền
                if (filter.getListAssignStatus().contains("none")) {
                    predicatesAssignedStatus.add(cb.and(
                            cb.isNull(bpmTask.get(BpmTask_.assignType))
                    ));
                }

                // combine predicate
                predicates.add(cb.or(predicatesAssignedStatus.toArray(new Predicate[0])));
            }

            // Yêu cầu bổ sung
            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListAdditionalStatus())) {
                List<Predicate> predicatesAdditionalStatus = new ArrayList<>();

                if (filter.getListAdditionalStatus().contains(BusinessEnum.AdditionStatusRequest.NEEDS_UPDATE.value)
                        || filter.getListAdditionalStatus().contains(BusinessEnum.AdditionStatusRequest.UPDATED.value)
                        || filter.getListAdditionalStatus().contains(BusinessEnum.AdditionStatusRequest.COMPLETED_NO_UPDATE.value)
                ) {
                    Root<BpmDiscussion> subDiscussion = query.from(BpmDiscussion.class);
                    predicates.add(
                            cb.and(
                                    cb.equal(subDiscussion.get(BpmDiscussion_.ticketId), root.get(BpmProcInst_.ticketId)),
                                    cb.isTrue(subDiscussion.get(BpmDiscussion_.isAdditionalRequest)),
                                    cb.isFalse(subDiscussion.get(BpmDiscussion_.isAdditionalRequestCompleted))
                            )
                    );

                    // cần bổ sung -> check status ticket = ADDITIONAL_REQUEST + các phiếu user login ycbs (additionalTicketId)
                    if (filter.getListAdditionalStatus().contains(BusinessEnum.AdditionStatusRequest.NEEDS_UPDATE.value)) {
                        predicatesAdditionalStatus.add(cb.and(
                                cb.or(
                                        cb.equal(subDiscussion.get(BpmDiscussion_.statusRequest), BusinessEnum.AdditionStatusRequest.NEEDS_UPDATE.code),
                                        // data cũ check status phiếu = ADDITIONAL_REQUEST + statusRequest = null
                                        cb.and (
                                                cb.isNull(subDiscussion.get(BpmDiscussion_.statusRequest)),
                                                cb.equal(root.get(BpmProcInst_.ticketStatus), ProcInstConstants.Status.ADDITIONAL_REQUEST.code)
                                        )
                                )
                        ));
                    }

                    // đã bổ sung -> check status ticket != ADDITIONAL_REQUEST + các phiếu user login ycbs (additionalTicketId)
                    if (filter.getListAdditionalStatus().contains(BusinessEnum.AdditionStatusRequest.UPDATED.value)) {
                        predicatesAdditionalStatus.add(cb.or(
                                cb.equal(subDiscussion.get(BpmDiscussion_.statusRequest), BusinessEnum.AdditionStatusRequest.UPDATED.code),
                                // data cũ check status phiếu != ADDITIONAL_REQUEST + statusRequest = null
                                cb.and(
                                        cb.notEqual(root.get(BpmProcInst_.ticketStatus), ProcInstConstants.Status.ADDITIONAL_REQUEST.code),
                                        cb.isNull(subDiscussion.get(BpmDiscussion_.statusRequest))
                                )
                        ));
                    }

                    // duyệt không bổ sung
                    if (filter.getListAdditionalStatus().contains(BusinessEnum.AdditionStatusRequest.COMPLETED_NO_UPDATE.value)) {
                        predicatesAdditionalStatus.add(
                                cb.equal(subDiscussion.get(BpmDiscussion_.statusRequest), BusinessEnum.AdditionStatusRequest.COMPLETED_NO_UPDATE.code)
                        );
                    }

                    // không chọn Người yêu cầu bổ sung -> lấy theo user đăng nhập
                    if (ValidationUtils.isNullOrEmpty(filter.getListAdditionalUser())) {
                        filter.getListAdditionalUser().add(criteria.getUsername());
                    }
                    predicates.add(subDiscussion.get(BpmDiscussion_.createdUser).in(filter.getListAdditionalUser()));
                }

                if (filter.getListAdditionalStatus().contains("none")) {
                    predicatesAdditionalStatus.add(root.get(BpmProcInst_.ticketId).in(additionalTicketId).not());
                }

                // combine predicate
                predicates.add(cb.or(predicatesAdditionalStatus.toArray(new Predicate[0])));
            }

            if (!ValidationUtils.isNullOrEmpty(filter.getIsAssistant()) && filter.getIsAssistant()) {
                predicates.add(cb.equal(root.get(BpmProcInst_.isAssistant), filter.getIsAssistant()));
                if (filter.getIsAssistant()) {
                    Root<Assistant> assistantRoot = query.from(Assistant.class);
                    predicates.add(cb.equal(assistantRoot.get(Assistant_.ticketId), root.get(BpmProcInst_.ticketId).as(String.class)));
                }
            }

            // có tham vấn
            if (filter.getHasDiscussion()) {
                Root<BpmDiscussion> subDiscussion2 = query.from(BpmDiscussion.class);
                predicates.add(
                        cb.and(
                                cb.equal(subDiscussion2.get(BpmDiscussion_.ticketId), root.get(BpmProcInst_.ticketId)),
                                cb.isFalse(subDiscussion2.get(BpmDiscussion_.isAdditionalRequest))
                        )
                );
            }

            if (!ValidationUtils.isNullOrEmpty(criteria.getSearch())) {
                predicates.add(cb.or(
                        cb.like(cb.lower(root.get(BpmProcInst_.ticketTitle)), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get(BpmProcInst_.requestCode)), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get(BpmProcInst_.ticketId).as(String.class)), "%" + criteria.getSearch().trim().toLowerCase() + "%")
                ));
            }

            if (!ValidationUtils.isNullOrEmpty(filter.getDateType())) {
                if (!ValidationUtils.isNullOrEmpty(filter.getFromDate()) && !ValidationUtils.isNullOrEmpty(filter.getToDate())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    LocalDateTime fromDate = LocalDate.parse(filter.getFromDate(), formatter).atStartOfDay();
                    LocalDateTime toDate = LocalDate.parse(filter.getToDate(), formatter).plusDays(1).atStartOfDay();

                    predicates.add(cb.between(root.get(filter.getDateType()), fromDate, toDate));

                } else if (ValidationUtils.isNullOrEmpty(filter.getFromDate()) && !ValidationUtils.isNullOrEmpty(filter.getToDate())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    LocalDateTime toDate = LocalDate.parse(filter.getToDate(), formatter).plusDays(1).atStartOfDay();

                    predicates.add(cb.lessThanOrEqualTo(root.get(filter.getDateType()), toDate));

                } else if (!ValidationUtils.isNullOrEmpty(filter.getFromDate())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    LocalDateTime fromDate = LocalDate.parse(filter.getFromDate(), formatter).atStartOfDay();

                    predicates.add(cb.greaterThanOrEqualTo(root.get(filter.getDateType()), fromDate));
                }
            }

            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1); // PhucVM3: Add 1 day to fix search by date range
                            if (DateDto.getFromDate().equals(DateDto.getToDate())) {
                                predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                                predicates.add(cb.lessThan(root.get(DateDto.getType()), toDateLocal));
                            } else
                                predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }

            if (criteria.getSortType().equalsIgnoreCase("asc")) {
                switch (criteria.getSortBy()) {
                    case "id":
                        query.orderBy(cb.asc(root.get("ticketId")));
                        break;
                    //Bước đang xử lý
                    case "ticketTaskDtoList":
                        Root<BpmTask> rootTask = query.from(BpmTask.class);
                        predicates.add(cb.equal(root.get("ticketProcInstId"), rootTask.get("taskProcInstId")));
                        query.orderBy(cb.asc(rootTask.get("taskName")));
                        break;
                    case "procServiceName":
                    case "serviceName":
                        query.orderBy(cb.asc(servicePackage.get("serviceName")));
                        break;
                    case "ticketCreatedTime":
                        query.orderBy(cb.asc(root.get("ticketCreatedTime")));
                        break;
                    case "ticketEditTime":
                        query.orderBy(cb.asc(cb.coalesce(root.get("ticketCanceledTime"), root.get("ticketFinishTime"))));
                        break;
                    case "ticketStatus":
                        query.orderBy(cb.asc(root.get("ticketStatus")));
                        break;
                    case "createdUser":
                        query.orderBy(cb.asc(root.get("createdUser")));
                        break;
                    case "companyCode":
                        query.orderBy(cb.asc(root.get("companyCode")));
                        break;
                    case "chartNodeName":
                        query.orderBy(cb.asc(root.get("chartNodeName")));
                        break;
                    case "ticketPriority":
                        query.orderBy(cb.asc(priorityManagement.get("name")));
                        break;
                    case "requestCode":
                        query.orderBy(cb.asc(root.get("requestCode")));
                        break;
                    case "cancelUser":
                        query.orderBy(cb.asc(root.get("cancelUser")));
                        break;
                    default:
                        query.orderBy(cb.asc(root.get(criteria.getSortBy())));
                        break;
                }
            } else {
                switch (criteria.getSortBy()) {
                    case "id":
                        query.orderBy(cb.desc(root.get("ticketId")));
                        break;
                    //Bước đang xử lý
                    case "ticketTaskDtoList":
                        Root<BpmTask> rootTask = query.from(BpmTask.class);
                        predicates.add(cb.equal(root.get("ticketProcInstId"), rootTask.get("taskProcInstId")));
                        query.orderBy(cb.desc(rootTask.get("taskName")));
                        break;
                    case "procServiceName":
                    case "serviceName":
                        query.orderBy(cb.desc(servicePackage.get("serviceName")));
                        break;
                    case "ticketCreatedTime":
                        query.orderBy(cb.desc(root.get("ticketCreatedTime")));
                        break;
                    case "ticketStatus":
                        query.orderBy(cb.desc(root.get("ticketStatus")));
                        break;
                    case "createdUser":
                        query.orderBy(cb.desc(root.get("createdUser")));
                        break;
                    case "companyCode":
                        query.orderBy(cb.desc(root.get("companyCode")));
                        break;
                    case "chartNodeName":
                        query.orderBy(cb.desc(root.get("chartNodeName")));
                        break;
                    case "ticketPriority":
                        query.orderBy(cb.desc(priorityManagement.get("name")));
                        break;
                    case "requestCode":
                        query.orderBy(cb.desc(root.get("requestCode")));
                        break;
                    case "ticketEditTime":
                        query.orderBy(cb.desc(cb.coalesce(root.get("ticketCanceledTime"), root.get("ticketFinishTime"))));
                        break;
                    case "cancelUser":
                        query.orderBy(cb.desc(root.get("cancelUser")));
                        break;
                    default:
                        query.orderBy(cb.desc(root.get(criteria.getSortBy())));
                        break;
                }
            }

            handleAddPrivateFilterTask(criteria, cb, root,
                    bpmTask, servicePackage,
                    priorityManagement,
                    changeAssigneeHistory,
                    bpmTaskUser, predicates);

            return cb.and(predicates.toArray(Predicate[]::new));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Long countFilterTask(PrivateFilterRequest filter, String username, String type, List<Long> additionalTicketIds) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<Long> queryCount = cb.createQuery(Long.class);
            Root<BpmProcInst> rootCount = queryCount.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackageCount = rootCount.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priorityManagementCount = rootCount.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
            Join<BpmProcInst, BpmTask> bpmTaskCount = rootCount.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
            Join<BpmTask, BpmTaskUser> bpmTaskUserCount = bpmTaskCount.join((BpmTask_.bpmTaskUsers), JoinType.LEFT);
            Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistoryCount = bpmTaskCount.join(BpmTask_.changeAssigneeHistories, JoinType.LEFT);
            Join<ChangeAssigneeHistory, AssignManagement> assignManagementCount = changeAssigneeHistoryCount.join(ChangeAssigneeHistory_.assignManagement, JoinType.LEFT);

            BpmTicketFilterSearchRequest criteria = new BpmTicketFilterSearchRequest();
            criteria.setUsername(username);
            criteria.setSortBy("id");
            criteria.setSortType("asc");
            criteria.setType(type);
            Predicate predicateCount = getPredPrivateFilterTask(filter, criteria, queryCount, cb, rootCount, servicePackageCount, bpmTaskCount, bpmTaskUserCount, priorityManagementCount, changeAssigneeHistoryCount, assignManagementCount, additionalTicketIds);
            queryCount.select(cb.countDistinct(rootCount)).where(predicateCount);


            return em.createQuery(queryCount).getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }

    private void handleAddPrivateFilterTask(Object criteria, CriteriaBuilder cb,
                                     Root<BpmProcInst> rootProcInst,
                                     Join<BpmProcInst, BpmTask> root,
                                     Join<BpmProcInst, ServicePackage> rootService,
                                     Join<BpmProcInst, PriorityManagement> rootPriority,
                                     Join<BpmTask, ChangeAssigneeHistory> changeAssigneeHistory,
                                     Join<BpmTask, BpmTaskUser> bpmTaskUser,
                                     List<Predicate> predicates) throws IllegalAccessException {

        List<String> filterList = Arrays.asList(
                "listRequestCode", "listTicketTitle",
                "listServiceName",
                "listCreatedUser", "listCompanyCode",
                "listChartNodeName",
                "listTaskName",
                "listTaskStatus",
                "listTicketCreatedTime",
                "listTaskCreatedTime",
                "listRemainingTime",
                "listTaskPriority",
                "listOrgAssignee",
                "listAssignType",
                "listId","listTicketId",
                "listTaskNameExt",
                "listTaskStartedTime",
                "listTaskFinishedTime",
                "listActionUserCompleteTask",
                "listTaskCanceledTime",
                "listCancelUser",
                "listCancelActionUser",
                "listCancelReason",
                "listCompanyName",
                "listModifiedUser",
                "listSharedUser",
                "listDescription",
                "listTicketStatus",
                "listActionUser",
                "listTicketTaskDtoTaskAssignee",
                "listTicketTaskDtoList",
                "listCompanyCodeFilter",
                "listChartNodeNameFilter"
        );
        Field[] fields = criteria.getClass().getDeclaredFields();
        for (Field field : fields) {
            String fieldName = field.getName();
            if (filterList.contains(fieldName)) {
                field.setAccessible(true);
                Object fieldValue = field.get(criteria);
                if (fieldValue instanceof ArrayList) {
                    List<String> filterData = (List<String>) fieldValue;
                    if (ValidationUtils.isAcceptSearchListIn(filterData)) {
                        switch (fieldName) {
                            case "listRequestCode":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.REQUEST_CODE).in(filterData)));
                                break;
                            case "listActionUser":
                                predicates.add(cb.and(root.get(BpmTask_.ACTION_USER).in(filterData)));
                                break;
                            case "listTicketStatus":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.ticketStatus).in(filterData)));
                                break;
                            case "listTicketTitle":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.TICKET_TITLE).in(filterData)));
                                break;
                            case "listServiceName":
                                predicates.add(cb.and(rootService.get(ServicePackage_.SERVICE_NAME).in(filterData)));
                                break;
                            case "listCreatedUser":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.CREATED_USER).in(filterData)));
                                break;
                            case "listCompanyCode":
                            case "listCompanyCodeFilter":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.COMPANY_CODE).in(filterData)));
                                break;
                            case "listChartNodeName":
                            case "listChartNodeNameFilter":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.CHART_NODE_NAME).in(filterData)));
                                break;
                            case "listTaskName":
                                predicates.add(cb.and(root.get(BpmTask_.TASK_NAME).in(filterData)));
                                break;
                            case "listTaskStatus":
                                predicates.add(cb.and(root.get(BpmTask_.TASK_STATUS).in(filterData)));
                                break;
                            case "listTaskPriority":
                                predicates.add(cb.and(rootPriority.get(PriorityManagement_.NAME).in(filterData)));
                                break;
                            case "listOrgAssignee":
                                predicates.add(cb.and(changeAssigneeHistory.get(ChangeAssigneeHistory_.ORG_ASSIGNEE).in(filterData)));
                                break;
                            case "listAssignType":
                                predicates.add(cb.and(bpmTaskUser.get(BpmTask_.ASSIGN_TYPE).in(filterData)));
                                break;
                            case "listId":
                            case "listTicketId":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.TICKET_ID).in(filterData)));
                                break;
                            case "listActionUserCompleteTask":
                                predicates.add(cb.and(root.get(BpmTask_.ACTION_USER).in(filterData)));
                                break;
                            case "listCancelUser":
                            case "listCancelActionUser":
//                                predicates.add(cb.and(bpmHistory.get(BpmHistory_.actionUser).in(filterData)));
                                break;
                            case "listCancelReason":
                                predicates.add(cb.and(rootProcInst.get(BpmProcInst_.CANCEL_REASON).in(filterData)));
                                break;
                            case "listTicketTaskDtoTaskAssignee":
                                Join<BpmProcInst, BpmTask> subTask = rootProcInst.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
                                predicates.add(cb.and(
                                        subTask.get(BpmTask_.TASK_ASSIGNEE).in(filterData),
                                        subTask.get(BpmTask_.TASK_STATUS).in(TaskConstants.TabStatus.PROCESSING)
                                ));
                                break;
                            case "listTicketTaskDtoList":
                                Join<BpmProcInst, BpmTask> subTask2 = rootProcInst.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
                                predicates.add(cb.and(
                                        subTask2.get(BpmTask_.taskName).in(filterData),
                                        subTask2.get(BpmTask_.TASK_STATUS).in(TaskConstants.TabStatus.PROCESSING)
                                ));
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
        }
    }
}
