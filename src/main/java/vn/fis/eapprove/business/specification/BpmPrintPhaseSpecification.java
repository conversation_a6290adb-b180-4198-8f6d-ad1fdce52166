package vn.fis.eapprove.business.specification;

import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.bpm.entity.*;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement_;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser_;
import vn.fis.eapprove.business.dto.SearchFilter;
import vn.fis.eapprove.business.dto.SearchPrintDto;
import vn.fis.eapprove.business.utils.TimeUtils;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ShareUserTypeEnum;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;


import java.util.*;

import static vn.fis.eapprove.business.constant.Constant.FORMAT_DATE_2;

@Component
public class BpmPrintPhaseSpecification {
    public Specification<BpmTemplatePrint> filter(final SearchPrintDto criteria, List<String> lstCompanyCodes, String username) {

        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            query.distinct(true);
            Root<BpmTpTask> bpmTpTaskRoot = null;
            if (StringUtils.isNotEmpty(criteria.getSearch())) {
                bpmTpTaskRoot = query.from(BpmTpTask.class);
                predicates.add(cb.equal(root.get("id"), bpmTpTaskRoot.get("bpmTemplatePrintId")));
                predicates.add(cb.or(cb.like(cb.lower(root.get("name")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("processName")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmTpTaskRoot.get("name")), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
            }

            // check permission data - shared user (companyCode = -1 -> next)
            // Tab search
            Join<BpmTemplatePrint, SharedUser> sharedUser = null;
            if (!ValidationUtils.isNullOrEmpty(criteria.getIsShared()) && criteria.getIsShared()) {
                sharedUser = root.join(BpmTemplatePrint_.sharedUsers, JoinType.LEFT);
                predicates.add(cb.and(
                        cb.equal(sharedUser.get(SharedUser_.referenceType), ShareUserTypeEnum.TEMPLATEPRINT.type),
                        cb.equal(sharedUser.get(SharedUser_.email), username)));
            } else
            if (!lstCompanyCodes.contains(CommonConstants.FILTER_SELECT_ALL)) {
                Join<BpmTemplatePrint, PermissionDataManagement> permissionDataManagement = root.join(BpmTemplatePrint_.permissionDataManagements);
                if (!ValidationUtils.isNullOrEmpty(criteria.getLstGroupPermissionId())) {
                    // có phân quyền theo nhóm ưu tiên lấy các bản ghi trong nhóm + theo companyCode member admin (nếu có)
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.BPM_TEMPLATE_PRINT.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(criteria.getListCompanyCodeMemberAdmin()),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL),
                                    // Phân quyền theo nhóm
                                    root.get(BpmTemplatePrint_.id).in(criteria.getLstGroupPermissionId())
                            ))
                    );
                } else {
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.BPM_TEMPLATE_PRINT.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(lstCompanyCodes),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
                            ))
                    );
                }
            }

            if(criteria.getSignStep() != null || criteria.getFirstStep() != null){
                if(bpmTpTaskRoot == null) {
                    bpmTpTaskRoot = query.from(BpmTpTask.class);
                    predicates.add(cb.equal(root.get("id"), bpmTpTaskRoot.get("bpmTemplatePrintId")));
                }
                if (criteria.getFirstStep() !=null && !criteria.getFirstStep().contains(CommonConstants.FILTER_SELECT_ALL)) {
                    predicates.add(cb.equal(root.get("id"), bpmTpTaskRoot.get("bpmTemplatePrintId")));
                    predicates.add(cb.and(bpmTpTaskRoot.get("name").in(criteria.getFirstStep())));
                }

                if (criteria.getSignStep() !=null && !criteria.getSignStep().contains(CommonConstants.FILTER_SELECT_ALL)) {
                    predicates.add(cb.equal(root.get("id"), bpmTpTaskRoot.get("bpmTemplatePrintId")));
                    predicates.add(cb.and(bpmTpTaskRoot.get("name").in(criteria.getSignStep())));
                }
            }


            if(ValidationUtils.isAcceptSearchListIn(criteria.getName())){
                predicates.add(cb.and(root.get("name").in(criteria.getName())));
            }


            if(ValidationUtils.isAcceptSearchListIn(criteria.getUpdatedUser())){
                predicates.add(cb.and(root.get("updatedUser").in(criteria.getUpdatedUser())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getCreatedUser())){
                predicates.add(cb.and(root.get("createdUser").in(criteria.getCreatedUser())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyCode())){
                predicates.add(cb.and(root.get("companyCode").in(criteria.getListCompanyCode())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyName())){
                predicates.add(cb.and(root.get("companyName").in(criteria.getListCompanyName())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListStatus())){
                if(criteria.getListStatus().contains(Boolean.FALSE)){
                    predicates.add(cb.or(
                            cb.and(root.get(BpmTemplatePrint_.status).in(criteria.getListStatus())),
                            root.get(BpmTemplatePrint_.status).isNull()));
                }else {
                    predicates.add(cb.and(root.get(BpmTemplatePrint_.status).in(criteria.getListStatus())));
                }

            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getProcDefName())){
                predicates.add(cb.and(root.get("processName").in(criteria.getProcDefName())));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getSharedWith())) {
                if(sharedUser == null)
                    sharedUser = root.join(BpmTemplatePrint_.sharedUsers, JoinType.LEFT);
                predicates.add(cb.and(
                        cb.equal(sharedUser.get(SharedUser_.referenceType), ShareUserTypeEnum.TEMPLATEPRINT.type),
                        cb.equal(sharedUser.get(SharedUser_.email), criteria.getSharedWith())));
            }

            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            Date fromDateLocal =DateDto.getFromDate();
                            Date toDateLocal = DateDto.getToDate();
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(toDateLocal);
                            calendar.add(Calendar.DAY_OF_MONTH, 1);
                            toDateLocal = calendar.getTime();
                            predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            Date toDateLocal = DateDto.getToDate();
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(toDateLocal);
                            calendar.add(Calendar.DAY_OF_MONTH, 1);
                            toDateLocal = calendar.getTime();
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            Date fromDateLocal =DateDto.getFromDate();
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                            System.out.println(e.getMessage());
                        }
                    }
                }
            }

            // filter special clone
            if (!ValidationUtils.isNullOrEmpty(criteria.getSpecialFlow())) {
                if (criteria.getSpecialFlow()) { // search mtk con
                    predicates.add(cb.isTrue(root.get(BpmTemplatePrint_.specialFlow)));
                } else {
                    predicates.add(cb.isNull(root.get(BpmTemplatePrint_.specialFlow)));
                }
            }

            predicates.add(cb.equal(root.get("isDeleted"), false));
            predicates.add(cb.equal(root.get("printType"), 0));

            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }

    public Specification<BpmTemplatePrint> filterSearch(final SearchFilter criteria, String username) {

        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            query.distinct(true);
            if (StringUtils.isNotEmpty(criteria.getProcessName())) {
                predicates.add(cb.and(cb.equal(root.get("processName"), criteria.getProcessName().trim())));
            }
            if (StringUtils.isNotEmpty(criteria.getSteps())) {
                Root<BpmTpTask> bpmTpTaskRoot = query.from(BpmTpTask.class);
                predicates.add(cb.equal(root.get("id"), bpmTpTaskRoot.get("bpmTemplatePrintId")));
                predicates.add(cb.and(cb.equal(bpmTpTaskRoot.get("taskDefKey"), criteria.getSteps().trim())));
            }
            if (CollectionUtils.isNotEmpty(criteria.getCreateUser())) {
                predicates.add(cb.and(root.get("createdUser").in(criteria.getCreateUser())));
            }
            if (!StringUtils.isEmpty(criteria.getFromDate()) && !StringUtils.isEmpty(criteria.getToDate())) {
                predicates.add(cb
                        .between(root.get("createdDate")
                                , TimeUtils.stringToDate(criteria.getFromDate(), FORMAT_DATE_2), TimeUtils.stringToDate(criteria.getToDate(), FORMAT_DATE_2)));
            }
            if (!StringUtils.isEmpty(criteria.getFromDate()) && StringUtils.isEmpty(criteria.getToDate())) {
                predicates.add(cb
                        .between(root.get("createdDate")
                                , TimeUtils.stringToDate(criteria.getFromDate(), FORMAT_DATE_2), TimeUtils.plusDay(TimeUtils.stringToDate(criteria.getFromDate(), FORMAT_DATE_2), 1)));
            }

            // check permission data - shared user (companyCode = -1 -> next)
            if (!criteria.getLstCompanyCodes().contains(CommonConstants.FILTER_SELECT_ALL)) {
                Join<BpmTemplatePrint, PermissionDataManagement> permissionDataManagement = root.join(BpmTemplatePrint_.permissionDataManagements);
                Join<BpmTemplatePrint, SharedUser> sharedUser = root.join(BpmTemplatePrint_.sharedUsers, JoinType.LEFT);

                predicates.add(cb.or(
                        cb.and(
                                cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.BPM_TEMPLATE_PRINT.code),
                                cb.or(
                                        permissionDataManagement.get(PermissionDataManagement_.companyCode).in(criteria.getLstCompanyCodes()),
                                        cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
                                )),
                        cb.and(
                                cb.equal(sharedUser.get(SharedUser_.referenceType), ShareUserTypeEnum.TEMPLATEPRINT.type),
                                cb.equal(sharedUser.get(SharedUser_.email), username))

                ));
            }

//            if (!criteria.getLstCompanyCodes().contains(CommonConstants.FILTER_SELECT_ALL)) {
//                Join<BpmTemplatePrint, PermissionDataManagement> permissionDataManagement = root.join(BpmTemplatePrint_.permissionDataManagements);
//                predicates.add(cb.and(
//                        cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.BPM_TEMPLATE_PRINT.code),
//                        cb.or(
//                                permissionDataManagement.get(PermissionDataManagement_.companyCode).in(criteria.getLstCompanyCodes()),
//                                cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
//                        )
//                ));
//            }

            predicates.add(cb.equal(root.get("isDeleted"), false));
            predicates.add(cb.equal(root.get("printType"), 0));
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }

    public Specification<BpmTemplateTask> taskIsUsed(final List<Long> id) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            List<String> listType = Arrays.asList("ACTIVE", "PROCESSING");
            predicates.add(cb.and(root.get("templateId").in(id)));
            Root<BpmTask> bpmTpTaskRoot = query.from(BpmTask.class);
            predicates.add(cb.equal(root.get("templateId"), bpmTpTaskRoot.get("id")));
            predicates.add(bpmTpTaskRoot.get("taskStatus").in(listType));
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }
}
