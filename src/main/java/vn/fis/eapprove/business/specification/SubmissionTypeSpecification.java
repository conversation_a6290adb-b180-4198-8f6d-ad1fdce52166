package vn.fis.eapprove.business.specification;

import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement_;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser_;
import vn.fis.eapprove.business.domain.submission.entity.SubmissionType;
import vn.fis.eapprove.business.domain.submission.entity.SubmissionType_;
import vn.fis.eapprove.business.dto.SearchSubmissionDto;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ShareUserTypeEnum;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;


import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

@Component
public class SubmissionTypeSpecification {
    public Specification<SubmissionType> filter(final SearchSubmissionDto search, List<String> lstCompanyCode, String username) {

        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            query.distinct(true);

            if (StringUtils.isNotEmpty(search.getSearch())) {
                predicates.add(cb.or(cb.like(cb.lower(root.get("typeName")), "%" + search.getSearch().trim().toLowerCase() + "%")));
            }
            Join<SubmissionType, SharedUser> sharedUser = null;
            // check permission data - shared user (companyCode = -1 -> view all)
            if (!ValidationUtils.isNullOrEmpty(search.getIsShared()) && search.getIsShared()) {
                sharedUser = root.join(SubmissionType_.sharedUsers, JoinType.LEFT);

                predicates.add(cb.and(
                        cb.equal(sharedUser.get(SharedUser_.referenceType), ShareUserTypeEnum.SUBMISSION.type),
                        cb.equal(sharedUser.get(SharedUser_.email), username))
                );
            } else
            if (!lstCompanyCode.contains(CommonConstants.FILTER_SELECT_ALL)) {
                Join<SubmissionType, PermissionDataManagement> permissionDataManagement = root.join(SubmissionType_.permissionDataManagements);
                if (!ValidationUtils.isNullOrEmpty(search.getLstGroupPermissionId())) {
                    // có phân quyền theo nhóm ưu tiên lấy các bản ghi trong nhóm + theo companyCode member admin (nếu có)
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.SUBMISSION_TYPE.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(search.getListCompanyCodeMemberAdmin()),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL),
                                    // Phân quyền theo nhóm
                                    root.get(SubmissionType_.id).in(search.getLstGroupPermissionId())
                            ))
                    );
                } else {
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.SUBMISSION_TYPE.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(lstCompanyCode),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
                            ))
                    );
                }
            }

            if(ValidationUtils.isAcceptSearchListIn(search.getListShareWith())){
                if(ValidationUtils.isNullOrEmpty(sharedUser)) {
                    sharedUser =  root.join(SubmissionType_.sharedUsers, JoinType.LEFT);
                }
                predicates.add(cb.and(
                        cb.equal(sharedUser.get(SharedUser_.referenceType), ShareUserTypeEnum.SUBMISSION.type),
                        sharedUser.get(SharedUser_.email).in(search.getListShareWith()))
                );
            }

            if(ValidationUtils.isAcceptSearchListIn(search.getListCompanyCodeFilter())){
                predicates.add(cb.and(root.get("companyCode").in(search.getListCompanyCodeFilter())));
            }

            if(ValidationUtils.isAcceptSearchListIn(search.getListCompanyName())){
                predicates.add(cb.and(root.get("companyName").in(search.getListCompanyName())));
            }

            if(ValidationUtils.isAcceptSearchListIn(search.getListCreatedUser())){
                predicates.add(cb.and(root.get(SubmissionType_.createdUser).in(search.getListCreatedUser())));
            }

            if(ValidationUtils.isAcceptSearchListIn(search.getListModifiedUser())){
                predicates.add(cb.and(root.get(SubmissionType_.modifiedUser).in(search.getListModifiedUser())));
            }

            if(ValidationUtils.isAcceptSearchListIn(search.getListTypeName())){
                predicates.add(cb.and(root.get(SubmissionType_.TYPE_NAME).in(search.getListTypeName())));
            }
            if(ValidationUtils.isAcceptSearchListIn(search.getListCompanyCode())){
                predicates.add(cb.and(root.get("companyCode").in(search.getListCompanyCode())));
            }
            if(ValidationUtils.isAcceptSearchListIn(search.getListDescription())){
                predicates.add(cb.and(root.get("description").in(search.getListDescription())));
            }

            if (search.getListDateFilter() != null && !search.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : search.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
//                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
//                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
//                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }

            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }
}
