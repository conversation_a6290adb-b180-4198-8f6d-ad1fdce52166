package vn.fis.eapprove.business.specification;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement_;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement_;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser_;
import vn.fis.eapprove.business.dto.SearchPriorityDto;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ShareUserTypeEnum;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.criteria.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

@Component
public class PrioritySpecification {
    public Specification<PriorityManagement> filter(final SearchPriorityDto search, List<String> lstCompanyCode, String username) {

        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            query.distinct(true);

            if (StringUtils.isNotEmpty(search.getSearch())) {
                predicates.add(cb.or(cb.like(cb.lower(root.get("name")), "%" + search.getSearch().trim().toLowerCase() + "%")));
            }

            Join<PriorityManagement, SharedUser> sharedUser = null;
            // check permission data - shared user (companyCode = -1 -> view all)
            if (!ValidationUtils.isNullOrEmpty(search.getIsShared()) && search.getIsShared()) {
                sharedUser = root.join(PriorityManagement_.sharedUsers, JoinType.LEFT);
                predicates.add(cb.and(
                        cb.equal(sharedUser.get(SharedUser_.referenceType), ShareUserTypeEnum.PRIORITY.type),
                        cb.equal(sharedUser.get(SharedUser_.email), username))
                );
            } else
            if (!lstCompanyCode.contains(CommonConstants.FILTER_SELECT_ALL)) {
                Join<PriorityManagement, PermissionDataManagement> permissionDataManagement = root.join(PriorityManagement_.permissionDataManagements);
                if (!ValidationUtils.isNullOrEmpty(search.getLstGroupPermissionId())) {
                    // có phân quyền theo nhóm ưu tiên lấy các bản ghi trong nhóm + theo companyCode member admin (nếu có)
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.PRIORITY_MANAGEMENT.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(search.getListCompanyCodeMemberAdmin()),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL),
                                    // Phân quyền theo nhóm
                                    root.get(PriorityManagement_.id).in(search.getLstGroupPermissionId())
                            ))
                    );
                } else {
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.PRIORITY_MANAGEMENT.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(lstCompanyCode),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
                            ))
                    );
                }
            }

            if(ValidationUtils.isAcceptSearchListIn(search.getListName())){
                predicates.add(cb.and(root.get(PriorityManagement_.NAME).in(search.getListName())));
            }

            if(ValidationUtils.isAcceptSearchListIn(search.getListSlaValue())){
                predicates.add(cb.and(root.get(PriorityManagement_.SLA_VALUE).in(search.getListSlaValue())));
            }

            if(ValidationUtils.isAcceptSearchListIn(search.getListShareWith())){
                if(ValidationUtils.isNullOrEmpty(sharedUser)) {
                    sharedUser = root.join(PriorityManagement_.sharedUsers, JoinType.LEFT);
                }
                predicates.add(cb.and(
                        cb.equal(sharedUser.get(SharedUser_.referenceType), ShareUserTypeEnum.PRIORITY.type),
                        sharedUser.get(SharedUser_.email).in(search.getListShareWith()))
                );
            }

            if(ValidationUtils.isAcceptSearchListIn(search.getListCompanyCodeFilter())){
                predicates.add(cb.and(root.get("companyCode").in(search.getListCompanyCodeFilter())));
            }

            if(ValidationUtils.isAcceptSearchListIn(search.getListCompanyName())){
                predicates.add(cb.and(root.get("companyName").in(search.getListCompanyName())));
            }

            if(ValidationUtils.isAcceptSearchListIn(search.getListCreatedUser())){
                predicates.add(cb.and(root.get(PriorityManagement_.CREATED_USER).in(search.getListCreatedUser())));
            }

            if(ValidationUtils.isAcceptSearchListIn(search.getListModifiedUser())){
                predicates.add(cb.and(root.get(PriorityManagement_.MODIFIED_USER).in(search.getListModifiedUser())));
            }

            if(ValidationUtils.isAcceptSearchListIn(search.getListDescription())){
                predicates.add(cb.and(root.get(PriorityManagement_.DESCRIPTION).in(search.getListDescription())));
            }

            if (!ValidationUtils.isNullOrEmpty(search.getListActiveStatus()) && !search.getListActiveStatus().contains(Long.valueOf("-1"))) {
                predicates.add(root.get(PriorityManagement_.activeStatus).in(search.getListActiveStatus()));
            }

            if (search.getListDateFilter() != null && !search.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : search.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
//                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
//                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
//                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }


            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }
}
