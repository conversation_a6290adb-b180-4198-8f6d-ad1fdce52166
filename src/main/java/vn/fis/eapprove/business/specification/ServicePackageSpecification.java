package vn.fis.eapprove.business.specification;

import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement_;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage_;
import vn.fis.eapprove.business.model.request.SearchServicePackRequest;
import vn.fis.eapprove.business.utils.TimeUtils;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

import static vn.fis.eapprove.business.constant.Constant.LOCALDATEFORMAT;

@Component
public class ServicePackageSpecification {

    public Specification<ServicePackage> getServiceAndParent(SearchServicePackRequest request, List<Long> lsParent, List<Long> masterId, List<Long> longList) {
        return (root, query, cb) -> {

            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isEmpty(request.getServiceName()) && StringUtils.isEmpty(request.getProcessName())) {
                predicates.add(cb.or(cb.isNull(root.get("parentId")),
                        root.get("parentId").in(lsParent)
                ));
            }
            if (StringUtils.isEmpty(request.getProcessName()) && StringUtils.isNotEmpty(request.getServiceName())) {
                predicates.add(cb.or(cb.like(root.get("serviceName"), "%" + request.getServiceName() + "%"),
                        root.get("id").in(longList)));
            }
            if (StringUtils.isNotEmpty(request.getProcessName()) && StringUtils.isNotEmpty(request.getServiceName())) {
                Root<BpmProcdef> bpmProcdefRoot = query.from(BpmProcdef.class);
                predicates.add(cb.equal(root.get("id"), bpmProcdefRoot.get("serviceId")));
                predicates.add(cb.or(
                        cb.and(cb.like(bpmProcdefRoot.get("name"), "%" + request.getProcessName() + "%"), cb.like(root.get("serviceName"), "%" + request.getServiceName() + "%")),
                        root.get("id").in(masterId)));
            }
            predicates.add(root.get("serviceType").in(request.getServiceType()));
            predicates.add(cb.equal(root.get("idOrgChart"), request.getChartId()));
            if (request.getShowWeb()) {
                predicates.add(cb.isFalse(root.get("notShowingWebsite")));
            } else {
                predicates.add(cb.isFalse(root.get("notShowingMoblie")));
            }
            query.distinct(true);
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }

    public Specification<ServicePackage> getServicePackageAll(String processName,
                                                              String serviceName,
                                                              List<Integer> serviceType,
                                                              Long orgchartId,
                                                              Boolean isWeb,
                                                              boolean isAdmin,
                                                              List<String> listStatus,
                                                              List<String> createdUser,
                                                              List<String> modifiedUser,
                                                              String fromDate,
                                                              String toDate,
                                                              Boolean visibleAllUser,
                                                              List<String> lstCompanyCode,
                                                              Boolean specialFlow,
                                                              List<Integer> specialServiceTypes,
                                                              Boolean isShared,
                                                              SearchServicePackRequest searchService,
                                                              Boolean isPrivateFilterOption
    ) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotEmpty(serviceName)) {
                predicates.add(cb.and(cb.like(cb.lower(root.get("serviceName")), "%" + serviceName.trim().toLowerCase() + "%")));
            }


//           if(!ValidationUtils.isNullOrEmpty(orgchartId)){{
//                predicates.add(root.get("createdUser").in(createdUser));
//            }}
            if (StringUtils.isNotEmpty(processName)) {
                Root<BpmProcdef> bpmProcdefRoot = query.from(BpmProcdef.class);
                predicates.add(cb.equal(root.get("id"), bpmProcdefRoot.get("serviceId")));
                predicates.add(cb.and(cb.like(cb.lower(bpmProcdefRoot.get("name")), "%" + processName.trim().toLowerCase() + "%")));
            }

            if (!CollectionUtils.isEmpty(serviceType)) {
                CriteriaBuilder.In<Integer> inClause = cb.in(root.get("serviceType"));
                for (Integer type : serviceType) {
                    inClause.value(type);
                }
                predicates.add(cb.and(inClause));
            }

            if (!isAdmin && !isPrivateFilterOption) {
                if (isWeb) {
                    predicates.add(cb.and(cb.isFalse(root.get("notShowingWebsite"))));
                } else {
                    predicates.add(cb.and(cb.isFalse(root.get("notShowingMoblie"))));
                }
            }

            if (!CollectionUtils.isEmpty(listStatus)) {
                {
                    predicates.add(root.get("status").in(listStatus));
                }
            }

            if (!ValidationUtils.isNullOrEmpty(createdUser)) {
                {
                    predicates.add(root.get("createdUser").in(createdUser));
                }
            }if (!ValidationUtils.isNullOrEmpty(modifiedUser)) {
                {
                    predicates.add(root.get("modifiedUser").in(modifiedUser));
                }
            }

            if (!StringUtils.isEmpty(fromDate) && !StringUtils.isEmpty(toDate) && !fromDate.equals(toDate)) {
                predicates.add(cb
                        .between(root.get("createdDate")
                                , TimeUtils.stringToDateTime(fromDate, LOCALDATEFORMAT), TimeUtils.stringToDateTime(toDate, LOCALDATEFORMAT).plusDays(1)));
            }
            if (!StringUtils.isEmpty(fromDate) && !StringUtils.isEmpty(toDate) && fromDate.equals(toDate)) {
                predicates.add(cb
                        .between(root.get("createdDate")
                                , TimeUtils.stringToDateTime(fromDate, LOCALDATEFORMAT), TimeUtils.stringToDateTime(fromDate, LOCALDATEFORMAT).plusDays(1)));
            }
            // filter by company code
            if (!ValidationUtils.isNullOrEmpty(searchService.getSortCompanyCode())) {
                Join<ServicePackage, PermissionDataManagement> permissionDataManagement = root.join(ServicePackage_.permissionDataManagements);
                predicates.add(cb.and(
                        cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.SERVICE_PACKAGE.code),
                        cb.or(
                                permissionDataManagement.get(PermissionDataManagement_.companyCode).in(searchService.getSortCompanyCode()),
                                cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
                        )

                ));
            } else
                if (!lstCompanyCode.contains(CommonConstants.FILTER_SELECT_ALL) && (!ValidationUtils.isNullOrEmpty(isShared) && !isShared)) {
                Join<ServicePackage, PermissionDataManagement> permissionDataManagement = root.join(ServicePackage_.permissionDataManagements);
                if (!ValidationUtils.isNullOrEmpty(searchService.getLstGroupPermissionId())) {
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.SERVICE_PACKAGE.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(searchService.getListCompanyCodeMemberAdmin()),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL),
                                    // Phân quyền theo nhóm
                                    root.get(ServicePackage_.id).in(searchService.getLstGroupPermissionId())
                            )
                    ));
                } else {
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.SERVICE_PACKAGE.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(lstCompanyCode),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
                            )
                    ));
                }
            }

            // search service special clone
            if (isAdmin && !ValidationUtils.isNullOrEmpty(specialFlow)) {
                if (specialFlow) {
                    // special service
                    predicates.add(
                            cb.and(
                                    cb.isTrue(root.get(ServicePackage_.specialFlow)),
                                    cb.isNotNull(root.get(ServicePackage_.specialParentId))
                            )
                    );
                } else {
                    // special service parent + normal service
                    predicates.add(
                            cb.isNull(root.get(ServicePackage_.specialParentId))
                    );
                }
            } else {
                predicates.add(
                        cb.isNull(root.get(ServicePackage_.specialParentId))
                );
            }

            // filter search service special clone
            if (isAdmin && !specialServiceTypes.isEmpty() && !specialServiceTypes.contains(-1)) {
                // normal service
                if (specialServiceTypes.contains(1)) {
                    predicates.add(
                            cb.isNull(root.get(ServicePackage_.specialFlow))
                    );
                } else if (specialServiceTypes.contains(2)) {
                    predicates.add(
                            cb.isTrue(root.get(ServicePackage_.specialFlow))
                    );
                }
            }

            if (ValidationUtils.isAcceptSearchListIn(searchService.getListServiceName())) {
                predicates.add(cb.and(root.get(ServicePackage_.serviceName).in(searchService.getListServiceName())));
            }

            if (searchService.getListDateFilter() != null && !searchService.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : searchService.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
//                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
//                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
//                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }

            if(ValidationUtils.isAcceptSearchListIn(searchService.getListCompanyCode())){
                predicates.add(root.get(ServicePackage_.companyCode).in(searchService.getListCompanyCode()));
            }
            if(ValidationUtils.isAcceptSearchListIn(searchService.getListCompanyName())){
                predicates.add(root.get(ServicePackage_.companyName).in(searchService.getListCompanyName()));
            }
//            if(ValidationUtils.isAcceptSearchListIn(searchService.getListServiceType())){
//                predicates.add(cb.and(root.get(ServicePackage_.serviceType).in(searchService.getListServiceType())));
//            }


            query.distinct(true);
            predicates.add(cb.and(cb.isFalse(root.get("deleted"))));
            return cb.and(predicates.toArray(Predicate[]::new));
        };
    }

    public Specification<ServicePackage> getServicePackageAllByIdAndProcessId(Long id) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.and(cb.equal(cb.lower(root.get("id")), id)));
            query.distinct(true);
            predicates.add(cb.and(cb.isFalse(root.get("deleted"))));
            return cb.and(predicates.toArray(Predicate[]::new));
        };
    }

//    public Specification<ServicePackage> getListAllToDeque(Boolean isWeb, boolean isAdmin, Set<Long> masterParentIds) {
//        return (root, criteriaQuery, criteriaBuilder) -> {
//            List<Predicate> predicates = new ArrayList<>();
//            predicates.add(criteriaBuilder.and(criteriaBuilder.isFalse(root.get("deleted"))));
//
//            CriteriaBuilder.In<Long> masterParentIdInClause = criteriaBuilder.in(root.get("masterParentId"));
//            CriteriaBuilder.In<Long> idInClause = criteriaBuilder.in(root.get("id"));
//            for (Long masterId: masterParentIds) {
//                masterParentIdInClause.value(masterId);
//                idInClause.value(masterId);
//            }
//            predicates.add(criteriaBuilder.or(masterParentIdInClause, idInClause));
//
//            if (!isAdmin) {
//                if (isWeb) {
//
//                }
//            }
//            return null;
//        };
//    }

    public Specification<ServicePackage> getServiceParent() {
        try {
            return ((root, query, cb) -> {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(cb.isNull(root.get("parentId")));
                return cb.and(predicates.stream().toArray(Predicate[]::new));
            });
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    public Specification<ServicePackage> getServiceMasterParentByServiceAndProcess(String searchService, String searchBpm) {
        return ((root, query, cb) -> {
            Root<BpmProcdef> bpmProcdefRoot = query.from(BpmProcdef.class);
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("id"), bpmProcdefRoot.get("serviceId")));
            predicates.add(cb.and(cb.like(root.get("serviceName"), "%" + searchService + "%"), cb.like(bpmProcdefRoot.get("name"), "%" + searchBpm + "%")));
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        });
    }

    public Specification<ServicePackage> getServiceMasterParentByService(String search) {
        return ((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.and(cb.like(root.get("serviceName"), "%" + search + "%")));
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        });
    }

    public Specification<ServicePackage> getParentByChild(Long id) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // subquery
            Subquery<Long> sub1 = query.subquery(Long.class);
            Root<ServicePackage> subRoot1 = sub1.from(ServicePackage.class);
            Predicate subPredicate = cb.equal(subRoot1.get("id"), id);

            Subquery<Long> sub2 = query.subquery(Long.class);
            Root<ServicePackage> subRoot2 = sub2.from(ServicePackage.class);
            Predicate subPredicate2 = cb.equal(subRoot2.get("id"), id);


            predicates.add(cb.equal(root.get("id"), id));
            predicates.add(root.get(ServicePackage_.masterParentId).in(sub2.select(subRoot2.get(ServicePackage_.masterParentId)).where(subPredicate2)));
            predicates.add(root.get(ServicePackage_.id).in(sub1.select(subRoot1.get(ServicePackage_.masterParentId)).where(subPredicate)));


            return cb.or(predicates.stream().toArray(Predicate[]::new));
        };
    }

    public Specification<ServicePackage> getServicePackageByProcIds(List<Long> lsProcIds) {
        return (root, query, cb) -> {
            return cb.and(cb.isFalse(root.get("deleted")),
                    root.get("processId").in(lsProcIds));
        };
    }

    public Specification<ServicePackage> getByChartAndNode(Long nodeId, Long chartId) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
//          predicates.add(cb.equal(root.get("idOrgChart"),chartId));
            predicates.add(
                    cb.or(
                            cb.like(root.get("hide_name"), "%" + nodeId + "%"),
                            cb.like(root.get("visible_name"), "%" + nodeId + "%")
                    )
            );
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }
//
//    public Specification<ServicePackage> getByChartAndNode(Long nodeId,Long chartId){
//        return (root, query, cb) -> {
//            List<Predicate> predicates = new ArrayList<>();
//            predicates.add(cb.equal(root.get("idOrgChart"),chartId));
//            predicates.add(
//                    cb.or(
//                            cb.like(root.get("hide_name"),"%" + nodeId + "%"),
//                            cb.like(root.get("visible_name"),"%" + nodeId + "%")
//                    )
//            );
//            return cb.and(predicates.stream().toArray(Predicate[]::new));
//        };
//    }

    public Specification<ServicePackage> getServiceById(Long id, List<String> lstCompanyCode) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            query.distinct(true);

            predicates.add(cb.equal(root.get(ServicePackage_.id), id));

            if (!lstCompanyCode.contains(CommonConstants.FILTER_SELECT_ALL)) {
                Join<ServicePackage, PermissionDataManagement> permissionDataManagement = root.join(ServicePackage_.permissionDataManagements);
                predicates.add(cb.and(
                        cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.SERVICE_PACKAGE.code),
                        cb.or(
                                permissionDataManagement.get(PermissionDataManagement_.companyCode).in(lstCompanyCode),
                                cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
                        )
                ));
            }

            predicates.add(cb.and(cb.isFalse(root.get("deleted"))));
            return cb.and(predicates.toArray(Predicate[]::new));
        };
    }

    public Specification<ServicePackage> getListShareService(List<String> lstCompanyCode, List<Long> lstGroupPermissionId) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(cb.equal(root.get(ServicePackage_.serviceType), 2));
            predicates.add(cb.isFalse(root.get(ServicePackage_.deleted)));
            predicates.add(cb.notEqual(root.get(ServicePackage_.status), "DISABLED"));

            if (!lstCompanyCode.contains(CommonConstants.FILTER_SELECT_ALL)) {
                Join<ServicePackage, PermissionDataManagement> permissionDataManagement = root.join(ServicePackage_.permissionDataManagements);
                predicates.add(cb.and(
                        cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.SERVICE_PACKAGE.code),
                        cb.or(
                                // Phân quyền dữ liệu
                                permissionDataManagement.get(PermissionDataManagement_.companyCode).in(lstCompanyCode),
                                cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL),
                                // Phân quyền theo nhóm
                                root.get(ServicePackage_.id).in(lstGroupPermissionId)
                        )
                ));
            }

            query.distinct(true);
            return cb.and(predicates.toArray(Predicate[]::new));
        };
    }

}
