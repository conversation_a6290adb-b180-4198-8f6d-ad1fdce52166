package vn.fis.eapprove.business.specification;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcDefHistory;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcDefHistory_;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef_;
import vn.fis.eapprove.business.model.request.ProcDefHistoryRequest;
import vn.fis.eapprove.business.model.response.BpmProcDefHistoryResponse;

import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class BpmProcDefHistorySpecification {
    @Autowired
    private EntityManagerFactory entityManagerFactory;

    public Map<String, Object> getAllHistory(final ProcDefHistoryRequest criteria) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Map<String, Object> mapFinal = new HashMap<>();
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<BpmProcDefHistoryResponse> query = cb.createQuery(BpmProcDefHistoryResponse.class);
            CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);

            Root<BpmProcDefHistory> root = query.from(BpmProcDefHistory.class);
            Predicate pred = predicateGetAll(criteria, cb, root, query);

            Root<BpmProcDefHistory> countRoot = countQuery.from(BpmProcDefHistory.class);
            Predicate predCount = predicateGetAll(criteria, cb, countRoot, countQuery);

            if (criteria.getSortType().equalsIgnoreCase("asc")) {
                switch (criteria.getSortBy()) {
                    case "version":
                        query.orderBy(cb.asc(root.get(BpmProcDefHistory_.version)));
                        break;
                    case "createdDate":
                        query.orderBy(cb.asc(root.get(BpmProcDefHistory_.createdDate)));
                        break;
                    case "createdUser":
                        query.orderBy(cb.asc(root.get(BpmProcDefHistory_.userCreated)));
                    case "contentEdit":
                        query.orderBy(cb.asc(root.get(BpmProcDefHistory_.contentEdit)));
                        break;
                    default:
                        query.orderBy(cb.asc(root.get(BpmProcDefHistory_.id)));
                        break;
                }
            } else {
                switch (criteria.getSortBy()) {
                    case "version":
                        query.orderBy(cb.desc(root.get(BpmProcDefHistory_.version)));
                        break;
                    case "createdDate":
                        query.orderBy(cb.desc(root.get(BpmProcDefHistory_.createdDate)));
                        break;
                    case "createdUser":
                        query.orderBy(cb.desc(root.get(BpmProcDefHistory_.userCreated)));
                    case "contentEdit":
                        query.orderBy(cb.desc(root.get(BpmProcDefHistory_.contentEdit)));
                        break;
                    default:
                        query.orderBy(cb.desc(root.get(BpmProcDefHistory_.id)));
                        break;
                }
            }

            query.multiselect(
                    root.get(BpmProcDefHistory_.id),
                    root.get(BpmProcDefHistory_.version),
                    root.get(BpmProcDefHistory_.createdDate),
                    root.get(BpmProcDefHistory_.userCreated),
                    root.get(BpmProcDefHistory_.contentEdit),
                    root.get(BpmProcDefHistory_.statusHistory)
            ).where(pred);
            query.distinct(true);
            List<BpmProcDefHistoryResponse> listResult = em.createQuery(query) != null ? em.createQuery(query)
                    .setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList() : new ArrayList<>();
            countQuery.select(cb.countDistinct(countRoot)).where(predCount);
            Long count = em.createQuery(countQuery).getSingleResult();

            mapFinal.put("count", count);
            mapFinal.put("data", listResult);
            return mapFinal;
        } catch (Exception e) {
            return null;
        }finally {
            if(em!= null)
                em.close();
        }
    }

    public Predicate predicateGetAll(ProcDefHistoryRequest criteria, CriteriaBuilder cb,
                                     Root<BpmProcDefHistory> root, CriteriaQuery<?> query) {
        List<Predicate> predicates = new ArrayList<>();
        Root<BpmProcdef> procDef = query.from(BpmProcdef.class);
        predicates.add(cb.equal(procDef.get(BpmProcdef_.ID), root.get(BpmProcDefHistory_.orgProcessId)));
        predicates.add(cb.equal(root.get(BpmProcDefHistory_.orgProcessId), criteria.getProcessId()));

        if (!ValidationUtils.isNullOrEmpty(criteria.getListDateFilter())) {
            for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                    try {
                        LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                        LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                        toDateLocal = toDateLocal.plusDays(1);
                        predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                    try {
                        LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                        toDateLocal = toDateLocal.plusDays(1);
                        predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                } else if (DateDto.getFromDate() != null) {
                    try {
                        LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                        predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        }

        if (!ValidationUtils.isNullOrEmpty(criteria.getListCreatedUser())) {
            predicates.add(root.get(BpmProcDefHistory_.userCreated).in(criteria.getListCreatedUser()));
        }

        if (!ValidationUtils.isNullOrEmpty(criteria.getSearch())) {
            predicates.add(cb.like(cb.lower(root.get(BpmProcDefHistory_.contentEdit)), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
        }

        return cb.and(predicates.toArray(Predicate[]::new));
    }

    public List<BpmProcDefHistoryResponse> getAllFilter(ProcDefHistoryRequest criteria) {
        List<BpmProcDefHistoryResponse> listResult;
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<BpmProcDefHistoryResponse> query = cb.createQuery(BpmProcDefHistoryResponse.class);

            Root<BpmProcDefHistory> root = query.from(BpmProcDefHistory.class);
            Predicate pred = predicateGetAll(criteria, cb, root, query);

            query.multiselect(
                    root.get(BpmProcDefHistory_.id),
                    root.get(BpmProcDefHistory_.version),
                    root.get(BpmProcDefHistory_.createdDate),
                    root.get(BpmProcDefHistory_.userCreated),
                    root.get(BpmProcDefHistory_.contentEdit),
                    root.get(BpmProcDefHistory_.statusHistory)
            ).where(pred);
            query.distinct(true);
            listResult = em.createQuery(query) != null ? em.createQuery(query)
                    .setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList()
                    : new ArrayList<>();

            return listResult;
        } catch (Exception e) {
            return null;
        }finally {
            if(em!= null)
                em.close();
        }
    }
}
