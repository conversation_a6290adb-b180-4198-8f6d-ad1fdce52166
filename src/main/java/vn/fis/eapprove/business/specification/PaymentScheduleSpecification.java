package vn.fis.eapprove.business.specification;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.payment.entity.PaymentSchedule;
import vn.fis.eapprove.business.dto.BaseSearchDto;


import jakarta.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

@Component
public class PaymentScheduleSpecification {
    public Specification<PaymentSchedule> filter(final BaseSearchDto search) {

        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotEmpty(search.getSearch())) {
                if (!StringUtils.isNumeric(search.getSearch())) {
                    predicates.add(cb.or(cb.like(cb.lower(root.get("contractNumber")), "%" + search.getSearch().trim().toLowerCase() + "%")));
                } else {
                    predicates.add(cb.or(cb.equal(cb.lower(root.get("procInstId")), Long.parseLong(search.getSearch()))));
                }
            }
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }
}
