package vn.fis.eapprove.business.specification;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrint;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrintHistory;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrintHistory_;
import vn.fis.eapprove.business.domain.bpm.entity.BpmTemplatePrint_;
import vn.fis.eapprove.business.model.request.TemplatePrintHistoryRequest;
import vn.fis.eapprove.business.model.response.BpmTemplatePrintHistoryResponse;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class BpmTemplatePrintHistorySpecification {

    @Autowired
    private EntityManagerFactory entityManagerFactory;

    public Map<String, Object> getAllHistory(TemplatePrintHistoryRequest criteria) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Map<String, Object> mapFinal = new HashMap<>();
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<BpmTemplatePrintHistoryResponse> query = cb.createQuery(BpmTemplatePrintHistoryResponse.class);
            CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);

            Root<BpmTemplatePrintHistory> root = query.from(BpmTemplatePrintHistory.class);
            Predicate pred = predicateGetAll(criteria, cb, root, query);

            Root<BpmTemplatePrintHistory> countRoot = countQuery.from(BpmTemplatePrintHistory.class);
            Predicate predCount = predicateGetAll(criteria, cb, countRoot, countQuery);

            if (criteria.getSortType().equalsIgnoreCase("asc")) {
                switch (criteria.getSortBy()) {
                    case "version":
                        query.orderBy(cb.asc(root.get(BpmTemplatePrintHistory_.version)));
                        break;
                    case "createdDate":
                        query.orderBy(cb.asc(root.get(BpmTemplatePrintHistory_.createdDate)));
                        break;
                    case "createdUser":
                        query.orderBy(cb.asc(root.get(BpmTemplatePrintHistory_.createdUser)));
                    case "contentEdit":
                        query.orderBy(cb.asc(root.get(BpmTemplatePrintHistory_.contentEdit)));
                        break;
                    default:
                        query.orderBy(cb.asc(root.get(BpmTemplatePrintHistory_.id)));
                        break;
                }
            } else {
                switch (criteria.getSortBy()) {
                    case "version":
                        query.orderBy(cb.desc(root.get(BpmTemplatePrintHistory_.version)));
                        break;
                    case "createdDate":
                        query.orderBy(cb.desc(root.get(BpmTemplatePrintHistory_.createdDate)));
                        break;
                    case "createdUser":
                        query.orderBy(cb.desc(root.get(BpmTemplatePrintHistory_.createdUser)));
                    case "contentEdit":
                        query.orderBy(cb.desc(root.get(BpmTemplatePrintHistory_.contentEdit)));
                        break;
                    default:
                        query.orderBy(cb.desc(root.get(BpmTemplatePrintHistory_.id)));
                        break;
                }
            }

            query.multiselect(
                    root.get(BpmTemplatePrintHistory_.id),
                    root.get(BpmTemplatePrintHistory_.version),
                    root.get(BpmTemplatePrintHistory_.createdDate),
                    root.get(BpmTemplatePrintHistory_.createdUser),
                    root.get(BpmTemplatePrintHistory_.contentEdit),
                    root.get(BpmTemplatePrintHistory_.statusHistory)
            ).where(pred);
            query.distinct(true);

            List<BpmTemplatePrintHistoryResponse> listResult = em.createQuery(query) != null ? em.createQuery(query)
                    .setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList() : new ArrayList<>();
            countQuery.select(cb.countDistinct(countRoot)).where(predCount);
            Long count = em.createQuery(countQuery).getSingleResult();

            mapFinal.put("count", count);
            mapFinal.put("data", listResult);
            return mapFinal;
        } catch (Exception e) {
            return null;
        }finally {
            if(em!= null)
                em.close();
        }
    }

    public Predicate predicateGetAll(TemplatePrintHistoryRequest criteria, CriteriaBuilder cb,
                                     Root<BpmTemplatePrintHistory> root, CriteriaQuery<?> query) {
        List<Predicate> predicates = new ArrayList<>();
        Root<BpmTemplatePrint> templatePrint = query.from(BpmTemplatePrint.class);
        predicates.add(cb.equal(root.get(BpmTemplatePrintHistory_.bpmTemplatePrintId), templatePrint.get(BpmTemplatePrint_.id)));
        predicates.add(cb.equal(root.get(BpmTemplatePrintHistory_.bpmTemplatePrintId), criteria.getTemplatePrintId()));

        if (!ValidationUtils.isNullOrEmpty(criteria.getListDateFilter())) {
            for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                    try {
                        LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                        LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                        toDateLocal = toDateLocal.plusDays(1);
                        predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                    try {
                        LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                        toDateLocal = toDateLocal.plusDays(1);
                        predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                } else if (DateDto.getFromDate() != null) {
                    try {
                        LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                        predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        }

        if (!ValidationUtils.isNullOrEmpty(criteria.getListCreatedUser())) {
            predicates.add(root.get(BpmTemplatePrintHistory_.createdUser).in(criteria.getListCreatedUser()));
        }

        if (!ValidationUtils.isNullOrEmpty(criteria.getSearch())) {
            predicates.add(cb.like(cb.lower(root.get(BpmTemplatePrintHistory_.contentEdit)), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
        }

        return cb.and(predicates.toArray(Predicate[]::new));
    }

    public List<BpmTemplatePrintHistoryResponse> getAllFilter(TemplatePrintHistoryRequest criteria) {
        List<BpmTemplatePrintHistoryResponse> listResult;
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<BpmTemplatePrintHistoryResponse> query = cb.createQuery(BpmTemplatePrintHistoryResponse.class);

            Root<BpmTemplatePrintHistory> root = query.from(BpmTemplatePrintHistory.class);
            Predicate pred = predicateGetAll(criteria, cb, root, query);

            query.multiselect(
                    root.get(BpmTemplatePrintHistory_.id),
                    root.get(BpmTemplatePrintHistory_.version),
                    root.get(BpmTemplatePrintHistory_.createdDate),
                    root.get(BpmTemplatePrintHistory_.createdUser),
                    root.get(BpmTemplatePrintHistory_.contentEdit),
                    root.get(BpmTemplatePrintHistory_.statusHistory)
            ).where(pred);
            query.distinct(true);
            listResult = em.createQuery(query) != null ? em.createQuery(query)
                    .setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList()
                    : new ArrayList<>();

            return listResult;
        }catch (Exception e){
            return null;
        }finally {
            if(em!= null)
                em.close();
        }
    }
}
