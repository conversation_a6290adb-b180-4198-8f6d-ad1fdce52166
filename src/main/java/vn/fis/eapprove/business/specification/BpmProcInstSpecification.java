package vn.fis.eapprove.business.specification;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.criteria.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.constant.BusinessEnum;
import vn.fis.eapprove.business.domain.assistant.entity.Assistant;
import vn.fis.eapprove.business.domain.assistant.entity.Assistant_;
import vn.fis.eapprove.business.domain.authority.entity.AuthorityManagement;
import vn.fis.eapprove.business.domain.authority.entity.AuthorityManagement_;
import vn.fis.eapprove.business.domain.bpm.entity.*;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement;
import vn.fis.eapprove.business.domain.priority.entity.PriorityManagement_;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage_;
import vn.fis.eapprove.business.domain.submission.entity.SubmissionType;
import vn.fis.eapprove.business.dto.GetTicketDto;
import vn.fis.eapprove.business.dto.LoadAssistantTicketDto;
import vn.fis.eapprove.business.dto.LoadTicketDto;
import vn.fis.eapprove.business.dto.TicketDateFilterDto;
import vn.fis.eapprove.business.model.PrivateFilterRequest;
import vn.fis.eapprove.business.model.request.BpmTicketFilterSearchRequest;
import vn.fis.eapprove.business.model.request.TicketAssistantFilter;
import vn.fis.eapprove.business.model.request.TicketFilter;
import vn.fis.eapprove.business.model.response.MyTicketAssistantResponse;
import vn.fis.eapprove.business.model.response.MyTicketResponse;
import vn.fis.eapprove.business.model.response.PrivateFilterResponse;
import vn.fis.eapprove.security.CredentialHelper;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.ProcInstConstants;
import vn.fis.spro.common.constants.TaskConstants;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.QueryUtils;
import vn.fis.spro.common.util.ValidationUtils;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Component
@Slf4j
public class BpmProcInstSpecification {

    private final ApplicationContext appContext;
    @Autowired
    private EntityManagerFactory entityManagerFactory;

    @Autowired
    private CredentialHelper credentialHelper;

    @Autowired
    public BpmProcInstSpecification(ApplicationContext appContext) {
        this.appContext = appContext;
    }

    public Specification<BpmProcInst> filterStartTicket(String procInstId) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("ticketProcInstId"), procInstId));
            predicates.add(cb.isNull(root.get("ticketStartedTime")));
            return cb.and(predicates.toArray(Predicate[]::new));
        };
    }

    public Specification<BpmProcInst> filter(final LoadTicketDto criteria) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotEmpty(criteria.getTicketTitle())) {
                predicates.add(cb.like(root.get("ticketTitle"), "%" + criteria.getTicketTitle().trim() + "%"));
            }
            if (StringUtils.isNotBlank(criteria.getTicketStatus())) {
                if (criteria.getTicketStatus().equalsIgnoreCase("ongoing")) {
                    predicates.add(cb.or(cb.equal(root.get("ticketStatus"), "PROCESSING"), cb.equal(root.get("ticketStatus"), "OPENED")));
                } else if (criteria.getTicketStatus().equalsIgnoreCase("finish")) {
                    predicates.add(cb.or(cb.equal(root.get("ticketStatus"), "CLOSED"), cb.equal(root.get("ticketStatus"), "FINISHED")));
                } else {
                    predicates.add(cb.equal(root.get("ticketStatus"), criteria.getTicketStatus()));
                }
            }
            if (StringUtils.isNotBlank(criteria.getTicketId())) {
                predicates.add(cb.equal(root.get("id"), criteria.getTicketId()));
            }
            return cb.and(predicates.toArray(Predicate[]::new));
        };
    }

    public Long countOngoing(String account, Boolean ownerProcess, String search) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Long> query = cb.createQuery(Long.class);
            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef);
            Join<BpmProcdef, BpmOwnerProcess> bpmOwnerProcess = bpmProcdef.join(BpmProcdef_.bpmOwnerProcesses);

            if (ownerProcess) {
                predicates.add(cb.equal(bpmOwnerProcess.get(BpmOwnerProcess_.idUser), account));
//                predicates.add(bpmProcInst.get(BpmProcInst_.COMPANY_CODE).in(companyCodes));

            } else {
                predicates.add(cb.equal(bpmProcInst.get(BpmProcInst_.ticketStartUserId), account));
            }

            if (!ValidationUtils.isNullOrEmpty(search)) {
                predicates.add(cb.or(
                        cb.like(cb.lower(bpmProcInst.get("ticketTitle")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("requestCode")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("ticketId").as(String.class)), "%" + search.trim().toLowerCase() + "%")
                ));
            }
            predicates.add(bpmProcInst.get(BpmProcInst_.ticketStatus).in(Arrays.asList(ProcInstConstants.Status.OPENED.code, ProcInstConstants.Status.PROCESSING.code, ProcInstConstants.Status.ADDITIONAL_REQUEST.code)));
            query.select(cb.countDistinct(bpmProcInst)).where(cb.and(predicates.toArray(Predicate[]::new))).getSelection();
            Long countResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : 0L;

            return countResult;
        } catch (Exception e) {
            log.error("CountOngoing failed: {}", e.getMessage());
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public Long countCompleted(String account, Boolean ownerProcess, String search) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {

            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Long> query = cb.createQuery(Long.class);
            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef, JoinType.LEFT);
            Join<BpmProcdef, BpmOwnerProcess> bpmOwnerProcess = bpmProcdef.join(BpmProcdef_.bpmOwnerProcesses, JoinType.LEFT);

            //predicate
            if (ownerProcess) {
                predicates.add(cb.equal(bpmOwnerProcess.get(BpmOwnerProcess_.idUser), account));
//                predicates.add(bpmProcInst.get(BpmProcInst_.COMPANY_CODE).in(companyCodes));
            } else {
                predicates.add(cb.equal(bpmProcInst.get(BpmProcInst_.ticketStartUserId), account));
            }

            if (!ValidationUtils.isNullOrEmpty(search)) {
                predicates.add(cb.or(
                        cb.like(cb.lower(bpmProcInst.get("ticketTitle")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("requestCode")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("ticketId").as(String.class)), "%" + search.trim().toLowerCase() + "%")
                ));
            }

            predicates.add((bpmProcInst.get(BpmProcInst_.ticketStatus).in(ProcInstConstants.Status.COMPLETED.code, ProcInstConstants.Status.CLOSED.code)));
            query.select(cb.countDistinct(bpmProcInst)).where(cb.and(predicates.toArray(Predicate[]::new))).getSelection();
            Long countResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : 0L;
            return countResult;
        } catch (Exception e) {
            log.error("countCompleted failed: {}", e.getMessage());
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public Long countDraft(String account, Boolean ownerProcess, String search) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {

            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Long> query = cb.createQuery(Long.class);
            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);

            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef, JoinType.LEFT);
            Join<BpmProcdef, BpmOwnerProcess> bpmOwnerProcess = bpmProcdef.join(BpmProcdef_.bpmOwnerProcesses, JoinType.LEFT);

            //predicate
            if (ownerProcess) {
                predicates.add(cb.equal(bpmOwnerProcess.get(BpmOwnerProcess_.idUser), account));
//                predicates.add(bpmProcInst.get(BpmProcInst_.COMPANY_CODE).in(companyCodes));

            } else {
                predicates.add(cb.equal(bpmProcInst.get(BpmProcInst_.ticketStartUserId), account));
            }

            if (!ValidationUtils.isNullOrEmpty(search)) {
                predicates.add(cb.or(
                        cb.like(cb.lower(bpmProcInst.get("ticketTitle")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("requestCode")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("ticketId").as(String.class)), "%" + search.trim().toLowerCase() + "%")
                ));
            }
            predicates.add(cb.equal(bpmProcInst.get(BpmProcInst_.ticketStatus), ProcInstConstants.Status.DRAFT.code));
            query.select(cb.countDistinct(bpmProcInst)).where(cb.and(predicates.toArray(Predicate[]::new))).getSelection();
            Long countResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : 0L;
            return countResult;
        } catch (Exception e) {
            log.error("countDraft failed: {}", e.getMessage());
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public Long countCancel(String account, Boolean ownerProcess, String search) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {

            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Long> query = cb.createQuery(Long.class);
            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef, JoinType.LEFT);
            Join<BpmProcdef, BpmOwnerProcess> bpmOwnerProcess = bpmProcdef.join(BpmProcdef_.bpmOwnerProcesses, JoinType.LEFT);
            Join<BpmProcInst, BpmHistory> historyJoin = bpmProcInst.join(BpmProcInst_.bpmHistories, JoinType.LEFT);

            //predicate
            if (ownerProcess) {
                predicates.add(cb.equal(bpmOwnerProcess.get(BpmOwnerProcess_.idUser), account));
//                predicates.add(bpmProcInst.get(BpmProcInst_.COMPANY_CODE).in(companyCodes));

            } else {
                predicates.add(cb.equal(bpmProcInst.get(BpmProcInst_.ticketStartUserId), account));
            }
            if (!ValidationUtils.isNullOrEmpty(search)) {
                predicates.add(cb.or(
                        cb.like(cb.lower(bpmProcInst.get("ticketTitle")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("requestCode")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("ticketId").as(String.class)), "%" + search.trim().toLowerCase() + "%")
                ));
            }

            predicates.add(cb.equal(bpmProcInst.get(BpmProcInst_.ticketStatus), ProcInstConstants.Status.CANCEL.code));
            predicates.add(cb.equal(historyJoin.get("action"), "CANCEL_TICKET"));

            query.select(cb.countDistinct(bpmProcInst)).where(cb.and(predicates.toArray(Predicate[]::new))).getSelection();
            Long countResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : 0L;
            return countResult;
        } catch (Exception e) {
            log.error("countCancel failed: {}", e.getMessage());
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public Long countRecalled(String account, Boolean ownerProcess, String search) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {

            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Long> query = cb.createQuery(Long.class);
            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef);
            Join<BpmProcdef, BpmOwnerProcess> bpmOwnerProcess = bpmProcdef.join(BpmProcdef_.bpmOwnerProcesses);

            //predicate
            if (ownerProcess) {
                predicates.add(cb.equal(bpmOwnerProcess.get(BpmOwnerProcess_.idUser), account));
//                predicates.add(bpmProcInst.get(BpmProcInst_.COMPANY_CODE).in(companyCodes));
            } else {
                predicates.add(cb.equal(bpmProcInst.get(BpmProcInst_.ticketStartUserId), account));
            }

            if (!ValidationUtils.isNullOrEmpty(search)) {
                predicates.add(cb.or(
                        cb.like(cb.lower(bpmProcInst.get("ticketTitle")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("requestCode")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("ticketId").as(String.class)), "%" + search.trim().toLowerCase() + "%")
                ));
            }

            predicates.add(bpmProcInst.get(BpmProcInst_.ticketStatus).in(ProcInstConstants.Status.RECALLED.code, ProcInstConstants.Status.DELETED_BY_RU.code));
            query.select(cb.countDistinct(bpmProcInst)).where(cb.and(predicates.toArray(Predicate[]::new))).getSelection();
            Long countResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : 0L;
            return countResult;
        } catch (Exception e) {
            log.error("countRecalled failed: {}", e.getMessage());
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public Long countShared(String account, Boolean ownerProcess, String search) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Long> query = cb.createQuery(Long.class);
            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackage = bpmProcInst.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<BpmProcInst, BpmShared> bpmShared = bpmProcInst.join(BpmProcInst_.bpmShareds, JoinType.LEFT);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef, JoinType.LEFT);
            Join<BpmProcdef, BpmOwnerProcess> bpmOwnerProcess = bpmProcdef.join(BpmProcdef_.bpmOwnerProcesses, JoinType.LEFT);


            //predicate
            if (ownerProcess) {
                predicates.add(cb.equal(bpmOwnerProcess.get(BpmOwnerProcess_.idUser), account));
//                predicates.add(bpmProcInst.get(BpmProcInst_.COMPANY_CODE).in(companyCodes));
            }

            if (!ValidationUtils.isNullOrEmpty(search)) {
                predicates.add(cb.or(
                        cb.like(cb.lower(bpmProcInst.get("ticketTitle")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("requestCode")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("ticketId").as(String.class)), "%" + search.trim().toLowerCase() + "%")
                ));
            }

            predicates.add(cb.equal(bpmShared.get(BpmShared_.sharedUser), account));
            // Xóa chia sẻ
//            predicates.add(cb.equal(bpmShared.get(BpmShared_.type), "SHARED"));
            predicates.add(cb.and(bpmShared.get(BpmShared_.type).in(Arrays.asList(
                    "SHARED",
                    "FOLLOWED",
                    "COMPLETED",
                    "CANCEL",
                    "RECALLED",
                    "RECALLING",
                    "DELETED_BY_RU",
                    "PROCESSING",
                    "-1",
                    "OPENED",
                    "CLOSED",
                    "RECALLING"
            ))));
            predicates.add(cb.or(
                    cb.isNull(bpmShared.get(BpmShared_.isDeleted)),
                    cb.isFalse(bpmShared.get(BpmShared_.isDeleted))
            ));
            predicates.add(cb.not(bpmProcInst.get(BpmProcInst_.ticketStatus).in(ProcInstConstants.Status.DRAFT.code)));
            query.select(cb.countDistinct(bpmProcInst)).where(cb.and(predicates.toArray(Predicate[]::new))).getSelection();
            Long countResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : 0L;
            return countResult;
        } catch (Exception e) {
            log.error("countShared failed: {}", e.getMessage());
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public Long countShare(String account, String search) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {

            CriteriaBuilder cb = em.getCriteriaBuilder();
            List<Predicate> predicates = new ArrayList<>();
            CriteriaQuery<Long> query = cb.createQuery(Long.class);
            Root<BpmProcInst> bpmProcInst = query.from(BpmProcInst.class);
            Join<BpmProcInst, BpmShared> bpmShared = bpmProcInst.join(BpmProcInst_.bpmShareds, JoinType.LEFT);

            predicates.add(cb.equal(bpmProcInst.get(BpmProcInst_.ticketStartUserId), account));
//            predicates.add(cb.and(bpmProcInst.get(BpmProcInst_.ticketStatus).in(
//                    ProcInstConstants.Status.CANCEL.code, ProcInstConstants.Status.RECALLED.code,
//                    ProcInstConstants.Status.CLOSED.code, ProcInstConstants.Status.COMPLETED.code,
//                    ProcInstConstants.Status.OPENED.code, ProcInstConstants.Status.PROCESSING.code,
//                    ProcInstConstants.Status.RECALLING.code, ProcInstConstants.Status.ADDITIONAL_REQUEST.code
//            )));

            if (!ValidationUtils.isNullOrEmpty(search)) {
                predicates.add(cb.or(
                        cb.like(cb.lower(bpmProcInst.get("ticketTitle")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("requestCode")), "%" + search.trim().toLowerCase() + "%"),
                        cb.like(cb.lower(bpmProcInst.get("ticketId").as(String.class)), "%" + search.trim().toLowerCase() + "%")
                ));
            }
            predicates.add(cb.equal(bpmShared.get(BpmShared_.type), "SHARED"));
            predicates.add(cb.equal(bpmShared.get(BpmShared_.createdUser), account));
            query.select(cb.countDistinct(bpmProcInst)).where(cb.and(predicates.toArray(Predicate[]::new))).getSelection();
            Long countResult = em.createQuery(query) != null ? em.createQuery(query).getSingleResult() : 0L;
            return countResult;
        } catch (Exception e) {
            log.error("countShare failed: {}", e.getMessage());
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }


    public Map<String, Object> getMyTicket(LoadTicketDto criteria) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Map<String, Object> mapFinal = new HashMap<>();

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<MyTicketResponse> query = cb.createQuery(MyTicketResponse.class);
            CriteriaQuery<Long> queryCount = cb.createQuery(Long.class);
            Join<BpmProcInst, BpmHistory> bpmHistory = null;
            Join<BpmProcInst, BpmHistory> bpmHistoryCount = null;

            Root<BpmProcInst> root = query.from(BpmProcInst.class);
            Join<BpmProcInst, AuthorityManagement> authRoot = root.join((BpmProcInst_.authorityManagements), JoinType.LEFT);
            Join<BpmProcInst, ServicePackage> servicePackage = root.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef, JoinType.LEFT);
            Join<BpmProcdef, BpmOwnerProcess> bpmOwnerProcess = bpmProcdef.join(BpmProcdef_.bpmOwnerProcesses, JoinType.LEFT);
            Join<BpmProcInst, SubmissionType> submissionType = root.join(BpmProcInst_.submissionType, JoinType.LEFT);
            Join<BpmProcInst, BpmShared> bpmShared = root.join(BpmProcInst_.bpmShareds, JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priority = root.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
            if (criteria.getTicketStatus().equals("CANCEL")) {
                bpmHistory = root.join(BpmProcInst_.bpmHistories, JoinType.LEFT);
            }

            Predicate predicate = getPredMyTicket(criteria, cb, root, servicePackage, bpmShared, bpmOwnerProcess, bpmHistory, query, authRoot, null);

//            //------------------------COUNT-------------------- //
            Root<BpmProcInst> rootCount = queryCount.from(BpmProcInst.class);
            Join<BpmProcInst, AuthorityManagement> authRootCount = rootCount.join((BpmProcInst_.authorityManagements), JoinType.LEFT);
            Join<BpmProcInst, ServicePackage> servicePackageCount = rootCount.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<ServicePackage, BpmProcdef> bpmProcdefCount = servicePackageCount.join(ServicePackage_.bpmProcdef, JoinType.LEFT);
            Join<BpmProcdef, BpmOwnerProcess> bpmOwnerProcessCount = bpmProcdefCount.join(BpmProcdef_.bpmOwnerProcesses, JoinType.LEFT);
            Join<BpmProcInst, BpmShared> bpmSharedCount = rootCount.join(BpmProcInst_.bpmShareds, JoinType.LEFT);
            if (criteria.getTicketStatus().equals("CANCEL")) {
                bpmHistoryCount = rootCount.join(BpmProcInst_.bpmHistories, JoinType.LEFT);
            }

            Predicate predicateCount = getPredMyTicket(criteria, cb, rootCount, servicePackageCount, bpmSharedCount, bpmOwnerProcessCount, bpmHistoryCount, queryCount, authRootCount, null);

            List<Selection<?>> inputs = new ArrayList<>();
            inputs.add(root.get("ticketId"));
            inputs.add(root.get("ticketProcInstId"));
            inputs.add(root.get("ticketEndActId"));
            inputs.add(root.get("ticketStartActId"));
            inputs.add(root.get("ticketStatus"));
            inputs.add(root.get("serviceId"));
            inputs.add(servicePackage.get("serviceName"));
            inputs.add(root.get("ticketTitle"));
            inputs.add(root.get("ticketStartUserId"));
            inputs.add(root.get("ticketStartedTime"));
            inputs.add(root.get("ticketCreatedTime"));
            inputs.add(root.get("ticketFinishTime"));
            inputs.add(root.get("ticketClosedTime"));
            inputs.add(root.get("ticketCanceledTime"));
            inputs.add(root.get("ticketEditTime"));
            inputs.add(root.get("ticketEndTime"));
            inputs.add(root.get("slaFinish"));
            inputs.add(root.get("slaResponse"));
            inputs.add(root.get("cancelReason"));
            inputs.add(root.get("ticketRating"));
            inputs.add(root.get("comment"));
            inputs.add(root.get("ticketProcDefId"));
            inputs.add(root.get("submissionTypeId"));
            inputs.add(submissionType.get("typeName"));
            inputs.add(priority.get("name"));
            inputs.add(bpmProcdef.get("autoClose"));
            inputs.add(root.get("requestCode"));
            inputs.add(root.get("createdUser"));
            inputs.add(root.get("companyCode"));
            if (bpmHistory != null)
                inputs.add(bpmHistory.get("actionUser"));
            inputs.add(root.get("chartId"));
            inputs.add(root.get("chartName"));
            inputs.add(root.get("chartNodeName"));
            inputs.add(servicePackage.get("processId"));
//            inputs.add(bpmShared.get(BpmShared_.ID));

            query.multiselect(inputs).where(predicate);

            query.distinct(true);
            List<MyTicketResponse> listResult = em.createQuery(query) != null ? em.createQuery(query).
                    setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList() : new ArrayList<>();
            queryCount.select(cb.countDistinct(rootCount)).where(predicateCount);
            Long count = em.createQuery(queryCount).getSingleResult();

            mapFinal.put("count", count);
            mapFinal.put("data", listResult);
            return mapFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public List<Object[]> getTicketByServiceAndTicketStatus(GetTicketDto criteria) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            String username = criteria.getUsername();
            if (ValidationUtils.isNullOrEmpty(username)) {
                username = credentialHelper.getJWTPayload().getUsername();
            }
            if (ValidationUtils.isNullOrEmpty(username)) {
                return new ArrayList<>();
            }

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<Object[]> query = cb.createQuery(Object[].class);

            Root<BpmProcInst> root = query.from(BpmProcInst.class);
            Join<BpmProcInst, BpmShared> bpmShared = root.join(BpmProcInst_.bpmShareds, JoinType.LEFT);
            Predicate predicate = getTicketByServiceAndTicketStatus(criteria, cb, root, bpmShared, username);

            List<Selection<?>> inputs = new ArrayList<>();
            inputs.add(root.get(BpmProcInst_.TICKET_ID)); //0
            inputs.add(root.get(BpmProcInst_.TICKET_PROC_INST_ID));
            inputs.add(root.get(BpmProcInst_.TICKET_START_USER_ID));
            inputs.add(root.get(BpmProcInst_.TICKET_PROC_DEF_ID));
            inputs.add(root.get(BpmProcInst_.TICKET_TITLE));
            inputs.add(root.get(BpmProcInst_.TICKET_START_ACT_ID));
            inputs.add(root.get(BpmProcInst_.TICKET_STATUS)); //6
            inputs.add(root.get(BpmProcInst_.REQUEST_CODE)); //7

            query.multiselect(inputs);

            if (predicate != null) {
                query.where(predicate);
            }


            List<Object[]> listResult = em.createQuery(query) != null ? em.createQuery(query).getResultList() : new ArrayList<>();

            return listResult;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public List<Map<String, Object>> getMyTicketFilter(LoadTicketDto criteria) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<Object> query = cb.createQuery(Object.class);
            Join<BpmProcInst, BpmHistory> bpmHistory = null;

            Root<BpmProcInst> root = query.from(BpmProcInst.class);
            Join<BpmProcInst, AuthorityManagement> authRoot = root.join((BpmProcInst_.authorityManagements), JoinType.LEFT);
            Join<BpmProcInst, ServicePackage> servicePackage = root.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef, JoinType.LEFT);
            Join<BpmProcdef, BpmOwnerProcess> bpmOwnerProcess = bpmProcdef.join(BpmProcdef_.bpmOwnerProcesses, JoinType.LEFT);
            Join<BpmProcInst, SubmissionType> submissionType = root.join(BpmProcInst_.submissionType, JoinType.LEFT);
            Join<BpmProcInst, BpmShared> bpmShared = root.join(BpmProcInst_.bpmShareds, JoinType.LEFT);
            if (criteria.getTicketStatus().equals("CANCEL")) {
                bpmHistory = root.join(BpmProcInst_.bpmHistories, JoinType.LEFT);
            }

            Join<BpmProcInst, BpmTask>[] taskJoin = new Join[1];
            Predicate predicate = getPredMyTicket(criteria, cb, root, servicePackage, bpmShared, bpmOwnerProcess, bpmHistory, query, authRoot, taskJoin);


            List<Selection<?>> inputs = new ArrayList<>();
            if (criteria.getSearchBy() == null || criteria.getSearchRoot() == null) {
                return null;
            }
            switch (criteria.getSearchRoot()) {
                case "bpmProcInst":
                    inputs.add(root.get(criteria.getSearchBy()));
                    break;
                case "bpmTask":
                    if (taskJoin[0] == null) {
                        taskJoin[0] = root.join(BpmProcInst_.bpmTasks, JoinType.LEFT);
                    }
                    inputs.add(taskJoin[0].get(criteria.getSearchBy()));
                    break;
                case "bpmHistory":
                    if (bpmHistory != null) {
                        inputs.add(bpmHistory.get(criteria.getSearchBy()));
                    }
                    break;
                case "servicePackage":
                    inputs.add(servicePackage.get(criteria.getSearchBy()));
                    break;
                case "bpmProcdef":
                    inputs.add(bpmProcdef.get(criteria.getSearchBy()));
                    break;
                case "bpmOwnerProcess":
                    inputs.add(bpmOwnerProcess.get(criteria.getSearchBy()));
                    break;
                case "submissionType":
                    inputs.add(submissionType.get(criteria.getSearchBy()));
                    break;
                case "authorityManagement":
                    inputs.add(authRoot.get(criteria.getSearchBy()));
                    break;
                case "bpmShared":
                    inputs.add(bpmShared.get(criteria.getSearchBy()));
                    break;
                default:
                    break;
            }
//            inputs.add(root.get("ticketId"));
//            inputs.add(root.get("ticketProcInstId"));
//            inputs.add(root.get("ticketEndActId"));
//            inputs.add(root.get("ticketStartActId"));
//            inputs.add(root.get("ticketStatus"));
//            inputs.add(root.get("serviceId"));
//            inputs.add(servicePackage.get("serviceName"));
//            inputs.add(root.get("ticketTitle"));
//            inputs.add(root.get("ticketStartUserId"));
//            inputs.add(root.get("ticketStartedTime"));
//            inputs.add(root.get("ticketCreatedTime"));
//            inputs.add(root.get("ticketFinishTime"));
//            inputs.add(root.get("ticketClosedTime"));
//            inputs.add(root.get("ticketCanceledTime"));
//            inputs.add(root.get("ticketEditTime"));
//            inputs.add(root.get("ticketEndTime"));
//            inputs.add(root.get("slaFinish"));
//            inputs.add(root.get("slaResponse"));
//            inputs.add(root.get("cancelReason"));
//            inputs.add(root.get("ticketRating"));
//            inputs.add(root.get("comment"));
//            inputs.add(root.get("ticketProcDefId"));
//            inputs.add(root.get("submissionTypeId"));
//            inputs.add(submissionType.get("typeName"));
//            inputs.add(root.get("priority"));
//            inputs.add(bpmProcdef.get("autoClose"));
//            inputs.add(root.get("requestCode"));
//            inputs.add(root.get("createdUser"));
//            inputs.add(root.get("companyCode"));
//            if (bpmHistory != null)
//                inputs.add(bpmHistory.get("actionUser"));
//            inputs.add(root.get("chartId"));
//            inputs.add(root.get("chartName"));
//            inputs.add(root.get("chartNodeName"));

            query.multiselect(inputs).where(predicate);

            query.distinct(true);
            List<Object> listResult = em.createQuery(query) != null ? em.createQuery(query).
                    setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList() : new ArrayList<>();

            List<Map<String, Object>> rs = new ArrayList<>();
            listResult.forEach(i -> {
                Map<String, Object> data = new HashMap<>();
                data.put(criteria.getSearchBy(), i);
                rs.add(data);
            });
//            mapFinal.put("data", listResult);

            return rs;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public Map<String, Object> getAssistantTicket(LoadAssistantTicketDto criteria) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Map<String, Object> mapFinal = new HashMap<>();

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<MyTicketAssistantResponse> query = cb.createQuery(MyTicketAssistantResponse.class);
            CriteriaQuery<Long> queryCount = cb.createQuery(Long.class);

            Root<BpmProcInst> root = query.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackage = root.join(BpmProcInst_.servicePackage);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef);
            Join<BpmProcInst, SubmissionType> submissionType = root.join(BpmProcInst_.submissionType, JoinType.LEFT);
            Join<BpmProcInst, BpmShared> bpmShared = root.join(BpmProcInst_.bpmShareds, JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priorityManagement = root.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
//            Join<BpmProcInst, BpmTask> bpmTask = root.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
            Join<BpmProcInst, BpmHistory> bpmHistory = null;
            Join<BpmProcInst, Assistant> bpmProcInstAssistantJoin = root.join(BpmProcInst_.assistants);
            Join<BpmProcInst, AuthorityManagement> authRoot = root.join((BpmProcInst_.authorityManagements), JoinType.LEFT);
            if (criteria.getTicketStatus().equals("CANCEL")) {
                bpmHistory = root.join(BpmProcInst_.bpmHistories, JoinType.LEFT);
            }
            Join<BpmProcInst, BpmTask>[] taskJoin = new Join[1];
            Predicate predicate = getPredMyTicketAssistant(criteria, cb, root, servicePackage, bpmShared, priorityManagement, bpmProcInstAssistantJoin, authRoot, bpmHistory, taskJoin, query);

            //------------------------COUNT-------------------- //
            Root<BpmProcInst> rootCount = queryCount.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackageCount = rootCount.join(BpmProcInst_.servicePackage);
//            Join<ServicePackage, BpmProcdef> bpmProcdefCount = servicePackageCount.join(ServicePackage_.bpmProcdef);
//            Join<BpmProcInst, SubmissionType> submissionTypeCount = rootCount.join(BpmProcInst_.submissionType, JoinType.LEFT);
            Join<BpmProcInst, BpmShared> bpmSharedCount = rootCount.join(BpmProcInst_.bpmShareds, JoinType.LEFT);
//            Join<BpmProcInst, BpmTask> bpmTaskCount = rootCount.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priorityManagementCount = rootCount.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
            Join<BpmProcInst, BpmHistory> bpmHistoryCount = null;
            Join<BpmProcInst, Assistant> bpmProcInstAssistantCount = rootCount.join(BpmProcInst_.assistants);
            Join<BpmProcInst, AuthorityManagement> authRootCount = rootCount.join((BpmProcInst_.authorityManagements), JoinType.LEFT);
            if (criteria.getTicketStatus().equals("CANCEL")) {
                bpmHistoryCount = rootCount.join(BpmProcInst_.bpmHistories, JoinType.LEFT);
            }
            Join<BpmProcInst, BpmTask>[] taskJoinCount = new Join[1];
            Predicate predicateCount = getPredMyTicketAssistant(criteria, cb, rootCount, servicePackageCount, bpmSharedCount, priorityManagementCount, bpmProcInstAssistantCount, authRootCount, bpmHistoryCount, taskJoinCount, queryCount);

            query.multiselect(
                    root.get("ticketId"),
                    root.get("ticketProcInstId"),
                    root.get("ticketEndActId"),
                    root.get("ticketStartActId"),
                    root.get("ticketStatus"),
                    root.get("serviceId"),
                    servicePackage.get("serviceName"),
                    root.get("ticketTitle"),
                    root.get("ticketStartUserId"),
                    root.get("ticketStartedTime"),
                    root.get("ticketCreatedTime"),
                    root.get("ticketFinishTime"),
                    root.get("ticketClosedTime"),
                    root.get("ticketCanceledTime"),
                    root.get("ticketEditTime"),
                    root.get("ticketEndTime"),
                    root.get("slaFinish"),
                    root.get("slaResponse"),
                    root.get("cancelReason"),
                    root.get("ticketRating"),
                    root.get("comment"),
                    root.get("ticketProcDefId"),
                    root.get("submissionTypeId"),
                    submissionType.get("typeName"),
                    root.get("priority"),
                    bpmProcdef.get("autoClose"),
                    root.get("requestCode"),
                    root.get("createdUser"),
                    root.get("companyCode"),
                    root.get("chartId"),
                    priorityManagement.get(PriorityManagement_.name),
                    priorityManagement.get(PriorityManagement_.color),
                    bpmHistory != null ? bpmHistory.get(BpmHistory_.actionUser) : root.get(BpmProcInst_.ticketStartUserId),
                    root.get(BpmProcInst_.chartName),
                    root.get(BpmProcInst_.chartNodeName)
//                    bpmTask.get("taskCreatedTime")
            ).where(predicate);

            query.distinct(true);
            List<MyTicketAssistantResponse> listResult = em.createQuery(query) != null ? em.createQuery(query).
                    setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList() : new ArrayList<>();

            queryCount.select(cb.countDistinct(rootCount)).where(predicateCount);
            Long count = em.createQuery(queryCount).getSingleResult();

            mapFinal.put("count", count);
            mapFinal.put("data", listResult);

            return mapFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public List<Map<String, Object>> getAssistantTicketFilter(LoadAssistantTicketDto criteria) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<Object> query = cb.createQuery(Object.class);

            Root<BpmProcInst> root = query.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackage = root.join(BpmProcInst_.servicePackage);
            Join<ServicePackage, BpmProcdef> bpmProcdef = servicePackage.join(ServicePackage_.bpmProcdef);
            Join<BpmProcInst, SubmissionType> submissionType = root.join(BpmProcInst_.submissionType, JoinType.LEFT);
            Join<BpmProcInst, BpmShared> bpmShared = root.join(BpmProcInst_.bpmShareds, JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priorityManagement = root.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
            Join<BpmProcInst, BpmHistory> bpmHistory = null;
            Join<BpmProcInst, Assistant> bpmProcInstAssistantJoin = root.join(BpmProcInst_.assistants);
            Join<BpmProcInst, AuthorityManagement> authRoot = root.join((BpmProcInst_.authorityManagements), JoinType.LEFT);

            Join<BpmProcInst, BpmTask>[] taskJoin = new Join[1];
            if (criteria.getTicketStatus().equals("CANCEL")) {
                bpmHistory = root.join(BpmProcInst_.bpmHistories, JoinType.LEFT);
            }
            Predicate predicate = getPredMyTicketAssistant(criteria, cb, root, servicePackage, bpmShared, priorityManagement, bpmProcInstAssistantJoin, authRoot, bpmHistory, taskJoin, query);

            List<Selection<?>> inputs = new ArrayList<Selection<?>>();
            if (criteria.getSearchBy() == null || criteria.getSearchRoot() == null) {
                return null;
            }
            switch (criteria.getSearchRoot()) {
                case "bpmProcInst":
                    inputs.add(root.get(criteria.getSearchBy()));
                    break;
                case "bpmTask":
                    if (taskJoin[0] == null) {
                        taskJoin[0] = root.join(BpmProcInst_.bpmTasks, JoinType.LEFT);
                    }
                    inputs.add(taskJoin[0].get(criteria.getSearchBy()));
                    break;
                case "bpmHistory":
                    if (bpmHistory != null) {
                        inputs.add(bpmHistory.get(criteria.getSearchBy()));
                    }
                    break;
                case "servicePackage":
                    inputs.add(servicePackage.get(criteria.getSearchBy()));
                    break;
                case "bpmProcdef":
                    inputs.add(bpmProcdef.get(criteria.getSearchBy()));
                    break;
                case "submissionType":
                    inputs.add(submissionType.get(criteria.getSearchBy()));
                    break;
                case "authorityManagement":
                    inputs.add(authRoot.get(criteria.getSearchBy()));
                    break;
                case "bpmShared":
                    inputs.add(bpmShared.get(criteria.getSearchBy()));
                    break;
                case "priorityManagement":
                    inputs.add(priorityManagement.get(criteria.getSearchBy()));
                    break;
                case "bpmProcInstAssistant":
                    inputs.add(bpmProcInstAssistantJoin.get(criteria.getSearchBy()));
                    break;
                default:
                    break;
            }
            if (predicate != null)
                query.multiselect(inputs).where(predicate);

            query.distinct(true);
            List<Object> listResult = em.createQuery(query) != null ? em.createQuery(query).
                    setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList() : new ArrayList<>();
            List<Map<String, Object>> rs = new ArrayList<>();
            listResult.forEach(i -> {
                Map<String, Object> data = new HashMap<>();
                data.put(criteria.getSearchBy(), i);
                rs.add(data);
            });
            return rs;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }

    /**
     * Build query conditional
     *
     * <AUTHOR>
     */
    public Predicate queryFilter(TicketFilter sample,
                                 CriteriaBuilder cb,
                                 CriteriaQuery<?> query,
                                 Root<BpmProcInst> bpmProcInst,
                                 Join<BpmProcInst, ServicePackage> servicePackage,
                                 Join<BpmProcInst, BpmTask> bpmTask) {
        if (sample == null) {
            return null;
        }

        List<Predicate> predicates = new ArrayList<>();

        // date time filter
        if (!ValidationUtils.isNullOrEmpty(sample.getDateTimeFilters())) {
            sample.getDateTimeFilters().forEach(e -> {
                if (e.getFromDate() != null) {
                    predicates.add(cb.greaterThanOrEqualTo(bpmProcInst.get(e.getFieldName()), e.getFromDate()));
                }
                if (e.getToDate() != null) {
                    predicates.add(cb.lessThanOrEqualTo(bpmProcInst.get(e.getFieldName()), e.getToDate()));
                }
            });
        }

        // service_name
        if (!ValidationUtils.isNullOrEmpty(sample.getServices())) {
            predicates.add(servicePackage.get(ServicePackage_.serviceName).in(sample.getServices()));
        }

        // tab and ticket status
        Subquery<Long> bpmSharedSubquery = null;
        if (!ValidationUtils.isNullOrEmpty(sample.getTab())) {
            List<String> statusList = new ArrayList<>();
            boolean isQueryNotIn = false;
            if (sample.getTab().equalsIgnoreCase(ProcInstConstants.Tab.PROCESSING)) {
                statusList.addAll(ProcInstConstants.TabStatus.PROCESSING);
            } else if (sample.getTab().equalsIgnoreCase(ProcInstConstants.Tab.COMPLETE)) {
                statusList.addAll(ProcInstConstants.TabStatus.COMPLETE);
            } else if (sample.getTab().equalsIgnoreCase(ProcInstConstants.Tab.CANCEL)) {
                statusList.add(ProcInstConstants.Status.CANCEL.code);
            } else if (sample.getTab().equalsIgnoreCase(ProcInstConstants.Tab.DRAFT)) {
                statusList.add(ProcInstConstants.Status.DRAFT.code);
            } else if (sample.getTab().equalsIgnoreCase(ProcInstConstants.Tab.SHARED)) {
                statusList.addAll(ProcInstConstants.TabStatus.COMPLETE);
                isQueryNotIn = true;

                // add sub-query bpm_shared
                List<Predicate> subPredicates = new ArrayList<>();
                bpmSharedSubquery = query.subquery(Long.class);
                Root<BpmShared> bpmShared = bpmSharedSubquery.from(BpmShared.class);
                bpmSharedSubquery.select(cb.literal(1L));

                subPredicates.add(cb.equal(bpmShared.get(BpmShared_.procInstId), bpmProcInst.get(BpmProcInst_.ticketProcInstId)));
                if (!ValidationUtils.isNullOrEmpty(sample.getUsers())) {
                    subPredicates.add(bpmShared.get(BpmShared_.sharedUser).in(sample.getUsers()));
                }

                bpmSharedSubquery.where(subPredicates.toArray(Predicate[]::new));

                predicates.add(cb.exists(bpmSharedSubquery));
            }

            if (!ValidationUtils.isNullOrEmpty(statusList)) {
                Path<?> ticketStatusCondition = bpmProcInst.get(BpmProcInst_.ticketStatus);
                Predicate ticketStatusPredicate = ticketStatusCondition.in(statusList);
                if (isQueryNotIn) {
                    ticketStatusPredicate = cb.not(ticketStatusPredicate);
                }

                predicates.add(ticketStatusPredicate);
            }
        }

        // created_user ==> only add if not shared tab
        if (!ValidationUtils.isNullOrEmpty(sample.getUsers()) && bpmSharedSubquery == null) {
            predicates.add(bpmProcInst.get(BpmProcInst_.ticketStartUserId).in(sample.getUsers()));
        }

        // assignee ==> check exists user in bpm_task_user
        if (!ValidationUtils.isNullOrEmpty(sample.getAssignees())) {
            List<Predicate> subPredicates = new ArrayList<>();
            Subquery<Long> bpmTaskUserSubquery = query.subquery(Long.class);
            Root<BpmTaskUser> bpmTaskUser = bpmTaskUserSubquery.from(BpmTaskUser.class);
            bpmTaskUserSubquery.select(cb.literal(1L));

            subPredicates.add(cb.equal(bpmTaskUser.get(BpmTaskUser_.procInstId), bpmProcInst.get(BpmProcInst_.ticketProcInstId)));
            subPredicates.add(cb.equal(bpmTaskUser.get(BpmTaskUser_.taskId), bpmTask.get(BpmTask_.taskId)));
            if (!ValidationUtils.isNullOrEmpty(sample.getAssignees())) {
                subPredicates.add(bpmTaskUser.get(BpmTaskUser_.userName).in(sample.getAssignees()));
            }

            bpmTaskUserSubquery.where(subPredicates.toArray(Predicate[]::new));

            predicates.add(cb.exists(bpmTaskUserSubquery));
        }

        // search key
        if (!ValidationUtils.isNullOrEmpty(sample.getSearchKey())) {
            predicates.add(cb.or(
                    cb.like(cb.lower(servicePackage.get(ServicePackage_.serviceName)), QueryUtils.createLikeValue(sample.getSearchKey().toLowerCase())),
                    cb.like(cb.lower(bpmProcInst.get(BpmProcInst_.ticketTitle)), QueryUtils.createLikeValue(sample.getSearchKey().toLowerCase())),
                    cb.like(cb.lower(bpmProcInst.get(BpmProcInst_.ticketId).as(String.class)), QueryUtils.createLikeValue(sample.getSearchKey().toLowerCase()))
            ));
        }

        return cb.and(predicates.toArray(Predicate[]::new));
    }

    /**
     * Build query conditional
     *
     * <AUTHOR>
     */
    public Predicate queryFilterAssistant(TicketAssistantFilter sample,
                                          CriteriaBuilder cb,
                                          CriteriaQuery<?> query,
                                          Root<BpmProcInst> bpmProcInst,
                                          Join<BpmProcInst, ServicePackage> servicePackage) {
        if (sample == null) {
            return null;
        }

        List<Predicate> predicates = new ArrayList<>();
        // date time filter
        if (!ValidationUtils.isNullOrEmpty(sample.getDateTimeFilters())) {
            sample.getDateTimeFilters().forEach(e -> {
                if (e.getFromDate() != null) {
                    predicates.add(cb.greaterThanOrEqualTo(bpmProcInst.get(e.getFieldName()), e.getFromDate()));
                }
                if (e.getToDate() != null) {
                    predicates.add(cb.lessThanOrEqualTo(bpmProcInst.get(e.getFieldName()), e.getToDate()));
                }
            });
        }

        // service_name
        if (!ValidationUtils.isNullOrEmpty(sample.getServices())) {
            predicates.add(servicePackage.get(ServicePackage_.serviceName).in(sample.getServices()));
        }

        // tab and ticket status
        Subquery<Long> bpmSharedSubquery = null;
        if (!ValidationUtils.isNullOrEmpty(sample.getTab())) {
            List<String> statusList = new ArrayList<>();
            boolean isQueryNotIn = false;
            if (sample.getTab().equalsIgnoreCase(ProcInstConstants.Tab.PROCESSING)) {
                statusList.addAll(ProcInstConstants.TabStatus.PROCESSING);
            } else if (sample.getTab().equalsIgnoreCase(ProcInstConstants.Tab.COMPLETE)) {
                statusList.addAll(ProcInstConstants.TabStatus.COMPLETE);
            } else if (sample.getTab().equalsIgnoreCase(ProcInstConstants.Tab.CANCEL)) {
                statusList.add(ProcInstConstants.Status.CANCEL.code);
            } else if (sample.getTab().equalsIgnoreCase(ProcInstConstants.Tab.DRAFT)) {
                statusList.add(ProcInstConstants.Status.DRAFT.code);
            } else if (sample.getTab().equalsIgnoreCase(ProcInstConstants.Tab.SHARED)) {
                statusList.addAll(ProcInstConstants.TabStatus.COMPLETE);
                isQueryNotIn = true;

                // add sub-query bpm_shared
                List<Predicate> subPredicates = new ArrayList<>();
                bpmSharedSubquery = query.subquery(Long.class);
                Root<BpmShared> bpmShared = bpmSharedSubquery.from(BpmShared.class);
                bpmSharedSubquery.select(cb.literal(1L));

                subPredicates.add(cb.equal(bpmShared.get(BpmShared_.procInstId), bpmProcInst.get(BpmProcInst_.ticketProcInstId)));
                if (!ValidationUtils.isNullOrEmpty(sample.getUsers())) {
                    subPredicates.add(bpmShared.get(BpmShared_.sharedUser).in(sample.getUsers()));
                }

                if (!ValidationUtils.isNullOrEmpty(sample.getAssistantEmail())) {
                    subPredicates.add(bpmShared.get(BpmShared_.sharedUser).in(sample.getAssistantEmail()));
                }

                bpmSharedSubquery.where(subPredicates.toArray(Predicate[]::new));

                predicates.add(cb.exists(bpmSharedSubquery));
            }

            if (!ValidationUtils.isNullOrEmpty(statusList) && !sample.getTab().equalsIgnoreCase(ProcInstConstants.Tab.SHARED)) {
                Path<?> ticketStatusCondition = bpmProcInst.get(BpmProcInst_.ticketStatus);
                Predicate ticketStatusPredicate = ticketStatusCondition.in(statusList);
                if (isQueryNotIn) {
                    ticketStatusPredicate = cb.not(ticketStatusPredicate);
                }

                predicates.add(ticketStatusPredicate);
            }
        }

        // created_user ==> only add if not shared tab
        if (!ValidationUtils.isNullOrEmpty(sample.getUsers()) && bpmSharedSubquery == null) {
            predicates.add(bpmProcInst.get(BpmProcInst_.ticketStartUserId).in(sample.getUsers()));
        }

        // assignee ==> check exists user in bpm_task_user
        if (!ValidationUtils.isNullOrEmpty(sample.getAssignees())) {
            List<Predicate> subPredicates = new ArrayList<>();
            Subquery<Long> bpmTaskUserSubquery = query.subquery(Long.class);
            Root<BpmTaskUser> bpmTaskUser = bpmTaskUserSubquery.from(BpmTaskUser.class);
            bpmTaskUserSubquery.select(cb.literal(1L));

            subPredicates.add(cb.equal(bpmTaskUser.get(BpmTaskUser_.procInstId), bpmProcInst.get(BpmProcInst_.ticketProcInstId)));
//            subPredicates.add(cb.equal(bpmTaskUser.get(BpmTaskUser_.taskId), bpmTask.get(BpmTask_.taskId)));
            if (!ValidationUtils.isNullOrEmpty(sample.getAssignees())) {
                subPredicates.add(bpmTaskUser.get(BpmTaskUser_.userName).in(sample.getAssignees()));
            }

            bpmTaskUserSubquery.where(subPredicates.toArray(Predicate[]::new));

            predicates.add(cb.exists(bpmTaskUserSubquery));
        }

        // search key
        if (!ValidationUtils.isNullOrEmpty(sample.getSearchKey())) {
            predicates.add(cb.or(
                    cb.like(cb.lower(servicePackage.get(ServicePackage_.serviceName)), QueryUtils.createLikeValue(sample.getSearchKey().toLowerCase())),
                    cb.like(cb.lower(bpmProcInst.get(BpmProcInst_.ticketTitle)), QueryUtils.createLikeValue(sample.getSearchKey().toLowerCase())),
                    cb.like(cb.lower(bpmProcInst.get(BpmProcInst_.ticketId).as(String.class)), QueryUtils.createLikeValue(sample.getSearchKey().toLowerCase()))
            ));
        }
        predicates.add(cb.and(bpmProcInst.get(BpmProcInst_.ticketId).in(sample.getLstTicketId())));
        return cb.and(predicates.toArray(Predicate[]::new));
    }

    public Predicate getPredMyTicketAssistant(LoadAssistantTicketDto criteria,
                                              CriteriaBuilder cb,
                                              Root<BpmProcInst> root,
                                              Join<BpmProcInst, ServicePackage> rootService,
                                              Join<BpmProcInst, BpmShared> bpmShared,
                                              Join<BpmProcInst, PriorityManagement> priorityManagement,
                                              Join<BpmProcInst, Assistant> bpmProcInstAssistantJoin,
                                              Join<BpmProcInst, AuthorityManagement> authRoot,
                                              Join<BpmProcInst, BpmHistory> bpmHistory,
                                              Join<BpmProcInst, BpmTask>[] taskJoin,
                                              CriteriaQuery<?> query
    ) {
        try {
            List<Predicate> predicates = new ArrayList<>();

            if (criteria.getListService() != null && !criteria.getListService().isEmpty()) {
                String value = criteria.getListService().get(0);
                if (!value.equalsIgnoreCase(CommonConstants.SELECT_ALL)) {
                    predicates.add(rootService.get("serviceName").in(criteria.getListService()));
                }
            }


            if (criteria.getListStatus() != null && !criteria.getListStatus().isEmpty()
                    && !criteria.getListStatus().contains("SHARED")) {
                predicates.add(root.get("ticketStatus").in(criteria.getListStatus()));
            }

            if (criteria.getListUser() != null && !criteria.getListUser().isEmpty()) {
                if (!criteria.getListUser().contains(CommonConstants.FILTER_SELECT_ALL)) {
                    predicates.add(root.get(BpmProcInst_.ticketStartUserId).in(criteria.getListUser()));
                }
            }


            if (criteria.getUser() != null) {
                if (criteria.getTicketStatus().equalsIgnoreCase("shared")) {
                    predicates.add(cb.equal(bpmShared.get("sharedUser"), criteria.getUser()));
                    predicates.add(cb.not(root.get(BpmProcInst_.ticketStatus).in(Arrays.asList(ProcInstConstants.Status.DRAFT.code, ProcInstConstants.Status.CANCEL.code))));
                } else {
                    if (!ValidationUtils.isNullOrEmpty(criteria.getHandingOverWork())) {
                        predicates.add(cb.and((cb.or(
                                (cb.and(cb.equal(authRoot.get(AuthorityManagement_.TO_ACCOUNT), criteria.getUser()), cb.equal(authRoot.get(AuthorityManagement_.TYPE), 4))),
                                (cb.and(cb.equal(authRoot.get(AuthorityManagement_.FROM_ACCOUNT), criteria.getUser()), cb.equal(authRoot.get(AuthorityManagement_.TYPE), 4))),
//                                cb.equal(root.get(BpmProcInst_.TICKET_START_USER_ID), criteria.getUser()),
//                                cb.equal(root.get(BpmProcInst_.CREATED_USER), criteria.getUser()),
                                cb.equal(bpmProcInstAssistantJoin.get(Assistant_.ASSISTANT_EMAIL), criteria.getUser()))
                        )));
                    } else {
                        predicates.add(cb.equal(bpmProcInstAssistantJoin.get(Assistant_.ASSISTANT_EMAIL), criteria.getUser()));
                    }
                }
            }


            if (!ValidationUtils.isNullOrEmpty(criteria.getSearchBy()) && !ValidationUtils.isNullOrEmpty(criteria.getSearchRoot()) && criteria.getSearchBy().equals("taskAssignee")) {
                if (taskJoin == null) {
                    taskJoin = new Join[1];
                }
                if (taskJoin[0] == null) {
                    taskJoin[0] = root.join(BpmProcInst_.bpmTasks, JoinType.LEFT);
                }
                predicates.add(taskJoin[0].get(BpmTask_.TASK_STATUS).in(TaskConstants.Status.PROCESSING.code, TaskConstants.Status.ACTIVE.code));
            }
            if (StringUtils.isNotBlank(criteria.getSearch())) {
                if (!ValidationUtils.isNullOrEmpty(criteria.getSearchBy())) {
                    switch (criteria.getSearchRoot()) {
                        case "bpmProcinst":
                        case "bpmProcInstAssistant":
                            predicates.add(cb.like(cb.lower(bpmProcInstAssistantJoin.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
                            break;
                        case "bpmTask":
                            if (taskJoin == null) {
                                taskJoin = new Join[1];
                            }
                            if (taskJoin[0] == null) {
                                taskJoin[0] = root.join(BpmProcInst_.bpmTasks, JoinType.LEFT);
                            }
                            predicates.add(cb.like(cb.lower(taskJoin[0].get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
                            break;
                        case "bpmHistory":
                            predicates.add(cb.like(cb.lower(bpmHistory.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
                            break;
                        case "servicePackage":
                            predicates.add(cb.like(cb.lower(rootService.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
                            break;
                        case "priorityManagement":
                            predicates.add(cb.like(cb.lower(priorityManagement.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
                            break;
                        case "authorityManagement":
                            predicates.add(cb.like(cb.lower(authRoot.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
                            break;
                        default:
                            predicates.add(cb.like(cb.lower(root.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
                            break;
                    }

                } else {
                    try {
                        predicates.add(cb.or(
//                                    cb.like(cb.lower(rootService.get("serviceName")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                                cb.like(cb.lower(root.get("ticketTitle")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                                cb.like(cb.lower(root.get("requestCode")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                                cb.like(cb.lower(root.get("ticketId").as(String.class)), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
                    } catch (Exception e) {
                        predicates.add(cb.or(
//                            cb.like(cb.lower(rootService.get("serviceName")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                                cb.like(cb.lower(root.get("requestCode")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                                cb.like(cb.lower(root.get("ticketTitle")), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
                    }
                }
            }
            if (criteria.getSearchRoot() == null)
                if (!criteria.getSortBy().equalsIgnoreCase("remainingTime")) {
                    if (criteria.getSortType().equalsIgnoreCase("asc")) {
                        switch (criteria.getSortBy()) {
                            case "id":
                                query.orderBy(cb.asc(root.get("ticketId")));
                                break;
                            //Bước đang xử lý
                            case "ticketTaskDtoList":
                                if (taskJoin == null) {
                                    taskJoin = new Join[1];
                                }
                                if (taskJoin[0] == null) {
                                    taskJoin[0] = root.join(BpmProcInst_.bpmTasks, JoinType.LEFT);
                                }
//                            predicates.add(cb.equal(root.get("ticketProcInstId"), rootTask.get("taskProcInstId")));
                                query.orderBy(cb.asc(taskJoin[0].get("taskName")));
                                break;
                            case "procServiceName":
                                query.orderBy(cb.asc(rootService.get("serviceName")));
                                break;
                            case "taskPriority":
                                query.orderBy(cb.asc(priorityManagement.get(PriorityManagement_.NAME)));
                                break;
                            default:
                                query.orderBy(cb.asc(root.get(criteria.getSortBy())));
                                break;
                        }
                    } else {
                        switch (criteria.getSortBy()) {
                            case "id":
                                query.orderBy(cb.desc(root.get("ticketId")));
                                break;
                            //Bước đang xử lý
                            case "ticketTaskDtoList":
                                Root<BpmTask> rootTask = query.from(BpmTask.class);
                                predicates.add(cb.equal(root.get("ticketProcInstId"), rootTask.get("taskProcInstId")));
                                query.orderBy(cb.desc(rootTask.get("taskName")));
                                break;
                            case "procServiceName":
                                query.orderBy(cb.desc(rootService.get("serviceName")));
                                break;
                            case "taskPriority":
                                query.orderBy(cb.desc(priorityManagement.get(PriorityManagement_.NAME)));
                                break;
                            default:
                                query.orderBy(cb.desc(root.get(criteria.getSortBy())));
                                break;
                        }
                    }
                }

            if (ValidationUtils.isNullOrEmpty(criteria.getHandingOverWork()) && !ValidationUtils.isNullOrEmpty(criteria.getListTicket())) {
                predicates.add(cb.and(root.get(BpmProcInst_.ticketId).in(criteria.getListTicket())));
            }

            if (criteria.getChartNames() != null && !criteria.getChartNames().contains("-1")) {
                predicates.add(cb.and(root.get("chartName").in(criteria.getChartNames())));
            }

            if (criteria.getChartCodes() != null && !criteria.getChartCodes().contains("-1")) {
                predicates.add(cb.and(root.get("companyCode").in(criteria.getChartCodes())));
            }

            if (criteria.getChartNodeNames() != null && !criteria.getChartNodeNames().contains("-1")) {
                predicates.add(cb.and(root.get("chartNodeName").in(criteria.getChartNodeNames())));
            }

            if (criteria.getTicketStatus().equals("CANCEL") && bpmHistory != null) { // CANCEL thì join lấy ra người hủy
                predicates.add(cb.and(
                        cb.equal(root.get(BpmProcInst_.ticketStatus), "CANCEL"),
                        cb.equal(bpmHistory.get(BpmHistory_.action), "CANCEL_TICKET")));
            }

            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (TicketDateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            if (DateDto.getFromDate().equals(DateDto.getToDate())) {
                                predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                                predicates.add(cb.lessThan(root.get(DateDto.getType()), toDateLocal));
                            } else
                                predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                        }
                    }
                }
            }
            handleAddFilterTicket(criteria, cb, root, rootService, bpmShared,
                    null, bpmHistory, query, authRoot, priorityManagement, taskJoin, predicates);
            return cb.and(predicates.toArray(Predicate[]::new));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Predicate getPredMyTicket(LoadTicketDto criteria,
                                     CriteriaBuilder cb,
                                     Root<BpmProcInst> root,
                                     Join<BpmProcInst, ServicePackage> rootService,
                                     Join<BpmProcInst, BpmShared> bpmShared,
                                     Join<BpmProcdef, BpmOwnerProcess> bpmOwnerProcess,
                                     Join<BpmProcInst, BpmHistory> bpmHistory,
                                     CriteriaQuery<?> query,
                                     Join<BpmProcInst, AuthorityManagement> authRoot,
                                     Join<BpmProcInst, BpmTask>[] taskJoin
    ) {
        try {
            List<Predicate> predicates = new ArrayList<>();
            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1); // PhucVM3: Add 1 day to fix search by date range
                            if (DateDto.getFromDate().equals(DateDto.getToDate())) {
                                predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                                predicates.add(cb.lessThan(root.get(DateDto.getType()), toDateLocal));
                            } else
                                predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1); // PhucVM3: Add 1 day to fix search by date range
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getListService())) {
                predicates.add(rootService.get("serviceName").in(criteria.getListService()));
            }
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListRating())) {
                if (String.join("", criteria.getListRating()).equals("0"))
                    predicates.add(root.get("ticketRating").in(0));
                else
                    predicates.add(root.get("ticketRating").in(0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5));
            }
            if (criteria.getListStatus() != null && !criteria.getListStatus().isEmpty()
                    && !criteria.getListStatus().contains("SHARED") && !criteria.getListStatus().contains("SHARE")
                    && !criteria.getListStatus().contains("FOLLOWED")
                    && criteria.getHandingOverWork() == null // bàn giao check status phiếu riêng
            ) {
                predicates.add(root.get("ticketStatus").in(criteria.getListStatus()));
            }
            if (criteria.getOwnerProcess()) {
                //Lấy ra list companyCode - Chủ sở hữu phải search theo companyCode
//                List<ChartInfoRoleResponse> chartList = customerService.getUserByEmail(Arrays.asList(credentialHelper.getJWTPayload().getUsername()));
//                Set<String> companyCodes = new HashSet<>();
//                chartList.stream().forEach(i -> {
//                    if (i.getCompanyCode() != null) {
//                        companyCodes.add(i.getCompanyCode());
//                        if (i.getShortName() != null) {
//                            companyCodes.add(i.getCompanyCode() + "_" + i.getShortName());
//                        }
//                    }
//                });
//                predicates.add(root.get(BpmProcInst_.COMPANY_CODE).in(companyCodes));

                //Lấy ra list companyCode
                if (criteria.getTicketStatus().equalsIgnoreCase("shared")) {
                    predicates.add(cb.equal(bpmShared.get("sharedUser"), criteria.getUser()));
                    predicates.add(cb.not(root.get(BpmProcInst_.ticketStatus).in(Collections.singletonList(ProcInstConstants.Status.DRAFT.code))));

                    // Xóa chia sẻ
                    predicates.add(cb.or(
                            cb.isNull(bpmShared.get(BpmShared_.isDeleted)),
                            cb.isFalse(bpmShared.get(BpmShared_.isDeleted))
                    ));
                } else predicates.add(cb.equal(bpmOwnerProcess.get(BpmOwnerProcess_.idUser), criteria.getUser()));

//                predicates.add(cb.equal(bpmOwnerProcess.get(BpmOwnerProcess_.idUser), criteria.getUser()));
            } else if (criteria.getUser() != null) {
                if (criteria.getTicketStatus().equalsIgnoreCase("shared")) {
                    predicates.add(cb.equal(bpmShared.get("sharedUser"), criteria.getUser()));
                    predicates.add(cb.not(root.get(BpmProcInst_.ticketStatus).in(Collections.singletonList(ProcInstConstants.Status.DRAFT.code))));
                    List<String> listSubStatus = criteria.getListStatus();
                    if (criteria.getListStatus().stream().anyMatch(i -> !Arrays.asList("FOLLOWED", "SHARED").contains(i))) {
                        listSubStatus.add("FOLLOWED");
                        listSubStatus.add("SHARED");
                        predicates.add(cb.and(root.get(BpmProcInst_.ticketStatus).in(listSubStatus)));
                    }
                    predicates.add(cb.and(bpmShared.get(BpmShared_.type).in(listSubStatus)));
                    // Xóa chia sẻ
                    predicates.add(cb.or(
                            cb.isNull(bpmShared.get(BpmShared_.isDeleted)),
                            cb.isFalse(bpmShared.get(BpmShared_.isDeleted))
                    ));
                } else {
                    if (!ValidationUtils.isNullOrEmpty(criteria.getHandingOverWork())) {
                        predicates.add(
                                cb.or(
                                        cb.and(
                                                cb.equal(authRoot.get(AuthorityManagement_.TO_ACCOUNT), criteria.getUser()),
                                                cb.equal(authRoot.get(AuthorityManagement_.TYPE), 2)
                                        ),
                                        cb.and(
                                                cb.equal(authRoot.get(AuthorityManagement_.FROM_ACCOUNT), criteria.getUser()),
                                                cb.equal(authRoot.get(AuthorityManagement_.TYPE), 2)
                                        ),
                                        // phiếu chưa bàn giao mới check status
                                        cb.and(
                                                cb.equal(root.get(BpmProcInst_.TICKET_START_USER_ID), criteria.getUser()),
                                                root.get("ticketStatus").in(criteria.getListStatus())
                                        )
                                )
                        );

                    } else {
                        predicates.add(cb.or(cb.equal(root.get("ticketStartUserId"), criteria.getUser()), cb.equal(root.get(BpmProcInst_.CREATED_USER), criteria.getUser())));

                    }

                }
            }
            if (!ValidationUtils.isNullOrEmpty(criteria.getSearch())) {
                if (!ValidationUtils.isNullOrEmpty(criteria.getSearchBy())) {
                    switch (criteria.getSearchRoot()) {
                        case "bpmProcInst":
                            predicates.add(cb.or(cb.like(cb.lower(root.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
                            break;
                        case "bpmHistory":
                            predicates.add(cb.or(cb.like(cb.lower(bpmHistory.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
                            break;
                        case "bpmTask":
                            if (taskJoin == null) {
                                taskJoin = new Join[1];
                            }
                            if (taskJoin[0] == null) {
                                taskJoin[0] = root.join(BpmProcInst_.bpmTasks, JoinType.LEFT);
                            }
                            predicates.add(cb.or(cb.like(cb.lower(taskJoin[0].get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
                            break;
                        case "servicePackage":
                            predicates.add(cb.or(cb.like(cb.lower(rootService.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
                            break;
                        case "bpmOwnerProcess":
                            predicates.add(cb.or(cb.like(cb.lower(bpmOwnerProcess.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
                            break;
                        case "authorityManagement":
                            predicates.add(cb.or(cb.like(cb.lower(authRoot.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
                            break;
                        case "bpmShared":
                            predicates.add(cb.or(cb.like(cb.lower(bpmShared.get(criteria.getSearchBy())), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
                            break;
                        default:
                            break;
                    }
                } else {
                    predicates.add(cb.or(
//                            cb.like(cb.lower(rootService.get("serviceName")), "%" + criteria.getSearch().trim().toLowerCase() + "%"), // Bỏ search theo service
                            cb.like(cb.lower(root.get("ticketTitle")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                            cb.like(cb.lower(root.get("requestCode")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                            cb.like(cb.lower(root.get("ticketId").as(String.class)), "%" + criteria.getSearch().trim().toLowerCase() + "%")
                    ));
                }
            }
            if (ValidationUtils.isNullOrEmpty(criteria.getSearchRoot()))
                if (!criteria.getSortBy().equalsIgnoreCase("remainingTime")) {
                    if (criteria.getSortType().equalsIgnoreCase("asc")) {
                        switch (criteria.getSortBy()) {
                            case "id":
                                query.orderBy(cb.asc(root.get("ticketId")));
                                break;
                            //Bước đang xử lý
                            case "ticketTaskDtoList":
                                Root<BpmTask> rootTask = query.from(BpmTask.class);
                                predicates.add(cb.equal(root.get("ticketProcInstId"), rootTask.get("taskProcInstId")));
                                query.orderBy(cb.asc(rootTask.get("taskName")));
                                break;
                            case "procServiceName":
                                query.orderBy(cb.asc(rootService.get("serviceName")));
                                break;
                            case "ticketCreatedTime":
                                query.orderBy(cb.asc(root.get("ticketCreatedTime")));
                                break;
                            default:
                                query.orderBy(cb.asc(root.get(criteria.getSortBy())));
                                break;
                        }
                    } else {
                        switch (criteria.getSortBy()) {
                            case "id":
                                query.orderBy(cb.desc(root.get("ticketId")));
                                break;
                            //Bước đang xử lý
                            case "ticketTaskDtoList":
                                Root<BpmTask> rootTask = query.from(BpmTask.class);
                                predicates.add(cb.equal(root.get("ticketProcInstId"), rootTask.get("taskProcInstId")));
                                query.orderBy(cb.desc(rootTask.get("taskName")));
                                break;
                            case "procServiceName":
                                query.orderBy(cb.desc(rootService.get("serviceName")));
                                break;
                            case "ticketCreatedTime":
                                query.orderBy(cb.desc(root.get("ticketCreatedTime")));
                                break;
                            default:
                                query.orderBy(cb.desc(root.get(criteria.getSortBy())));
                                break;
                        }
                    }
                }
            if (criteria.getTicketStatus().equals("CANCEL") && bpmHistory != null) { // CANCEL thì join lấy ra người hủy
                predicates.add(cb.and(
                        cb.equal(root.get("ticketStatus"), "CANCEL"),
                        cb.equal(bpmHistory.get("action"), "CANCEL_TICKET")));
            }
            if (criteria.getTicketStatus().equals("SHARE")) { // Share thì lấy ra những ticket đã chia sẻ
                predicates.add(cb.and(cb.equal(bpmShared.get(BpmShared_.createdUser), criteria.getUser()), cb.equal(bpmShared.get(BpmShared_.type), "SHARED")));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getChartNames())) {
                predicates.add(cb.and(root.get("chartName").in(criteria.getChartNames())));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getChartCodes())) {
                predicates.add(cb.and(root.get("companyCode").in(criteria.getChartCodes())));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getChartNodeNames())) {
                predicates.add(cb.and(root.get("chartNodeName").in(criteria.getChartNodeNames())));
            }

            handleAddFilterTicket(criteria, cb, root, rootService, bpmShared, bpmOwnerProcess, bpmHistory, query, authRoot, null, taskJoin, predicates);

            return cb.and(predicates.toArray(Predicate[]::new));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Predicate getTicketByServiceAndTicketStatus(GetTicketDto criteria,
                                                       CriteriaBuilder cb,
                                                       Root<BpmProcInst> root,
                                                       Join<BpmProcInst, BpmShared> bpmShared,
                                                       String username
    ) {
        try {
            List<Predicate> predicates = new ArrayList<>();
            List<String> searchList = new ArrayList<>();

            if (!ValidationUtils.isNullOrEmpty(criteria)) {
                if (!ValidationUtils.isNullOrEmpty(criteria.getListCompleteTicketId())) {
                    searchList.addAll(criteria.getListCompleteTicketId());
                }
                if (!ValidationUtils.isNullOrEmpty(criteria.getListTicketId())) {
                    searchList.addAll(criteria.getListTicketId());
                }
                if (!ValidationUtils.isNullOrEmpty(criteria.getServiceId())) {
                    predicates.add(cb.equal(root.get(BpmProcInst_.SERVICE_ID), criteria.getServiceId()));
                } else if (ValidationUtils.isAcceptSearchListIn(criteria.getListServiceId())) {
                    predicates.add(root.get(BpmProcInst_.SERVICE_ID).in(criteria.getListServiceId()));
                }

                if (!ValidationUtils.isNullOrEmpty(criteria.getStatus())) {
                    predicates.add(cb.equal(root.get(BpmProcInst_.TICKET_STATUS), criteria.getStatus()));
                } else if (ValidationUtils.isAcceptSearchListIn(criteria.getListStatus())) {
                    predicates.add(root.get(BpmProcInst_.TICKET_STATUS).in(criteria.getListStatus()));
                }

                if (ValidationUtils.isAcceptSearchListIn(searchList)) {
                    predicates.add(root.get(BpmProcInst_.ticketId).in(searchList));
                }

                if (ValidationUtils.isAcceptSearchListIn(criteria.getListProcinstId())) {
                    predicates.add(root.get(BpmProcInst_.ticketProcInstId).in(criteria.getListProcinstId()));
                }

            }

            Predicate part1 = cb.or(
                    cb.equal(root.get("ticketStartUserId"), username),
                    cb.equal(root.get(BpmProcInst_.CREATED_USER), username),
                    cb.and(
                            cb.or(
                                    cb.and(
                                            cb.equal(bpmShared.get("sharedUser"), username),
                                            cb.equal(bpmShared.get(BpmShared_.type), "SHARED"),
                                            bpmShared.get(BpmShared_.isDeleted).isNull()
                                    ),
                                    cb.and(
                                            cb.equal(bpmShared.get(BpmShared_.createdUser), username),
                                            bpmShared.get(BpmShared_.isDeleted).isNull(),
                                            cb.notEqual(bpmShared.get("sharedUser"), username),
                                            cb.notEqual(bpmShared.get(BpmShared_.type), "SHARED")
                                    )
                            )
                    )

            );

            predicates.add(cb.and(part1));

            predicates.add(cb.not(root.get(BpmProcInst_.TICKET_STATUS).in(ProcInstConstants.Status.DRAFT.code)));

            return cb.and(predicates.toArray(Predicate[]::new));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    private void handleAddFilterTicket(Object criteria, CriteriaBuilder cb,
                                       Root<BpmProcInst> root,
                                       Join<BpmProcInst, ServicePackage> rootService,
                                       Join<BpmProcInst, BpmShared> bpmShared,
                                       Join<BpmProcdef, BpmOwnerProcess> bpmOwnerProcess,
                                       Join<BpmProcInst, BpmHistory> bpmHistory,
                                       CriteriaQuery<?> query,
                                       Join<BpmProcInst, AuthorityManagement> authRoot,
                                       Join<BpmProcInst, PriorityManagement> priorityManagement,
                                       Join<BpmProcInst, BpmTask>[] taskJoin,
                                       List<Predicate> predicates) throws IllegalAccessException {

        List<String> filterList = Arrays.asList(
                "listRequestCode", "listTicketTitle", "listProcServiceName", "listTicketTaskDtoList",
                "listTicketStatus", "listCompanyCode", "listCompanyName", "listId", "listChartName", "listCancelReason", "listChartNodeName", "listTicketId", "listActionUser", "listSharedUser", "listProcTitle", "listServiceName", "listCreatedUser",
                "listModifiedUser", "listDescription", "listTicketStartUserId", "listTaskPriority", "listTicketTaskDtoTaskAssignee", "listChartNodeNameFilter", "listCompanyCode");
        Field[] fields = criteria.getClass().getDeclaredFields();
        for (Field field : fields) {
            String fieldName = field.getName();
            if (filterList.contains(fieldName)) {
                field.setAccessible(true);
                Object fieldValue = field.get(criteria);
                if (fieldValue instanceof ArrayList) {
                    List<String> filterData = (List<String>) fieldValue;
                    if (ValidationUtils.isAcceptSearchListIn(filterData)) {
                        switch (fieldName) {
                            case "listRequestCode":
                                predicates.add(cb.and(root.get(BpmProcInst_.REQUEST_CODE).in(filterData)));
                                break;
                            case "listTaskPriority":
                                predicates.add(cb.and(priorityManagement.get(PriorityManagement_.NAME).in(filterData)));
                                break;
                            case "listTicketStartUserId":
                                predicates.add(cb.and(root.get(BpmProcInst_.TICKET_START_USER_ID).in(filterData)));
                                break;
                            case "listTicketTaskDtoTaskAssignee":
                                if (taskJoin == null) {
                                    taskJoin = new Join[1];
                                }
                                if (taskJoin[0] == null) {
                                    taskJoin[0] = root.join(BpmProcInst_.bpmTasks, JoinType.LEFT);
                                }
                                predicates.add(cb.or(
                                        cb.and(
                                                taskJoin[0].get(BpmTask_.TASK_ASSIGNEE).in(filterData),
                                                cb.and(taskJoin[0].get(BpmTask_.taskStatus).in(TaskConstants.TabStatus.PROCESSING))),
                                        cb.and(
                                                cb.equal(root.get(BpmProcInst_.ticketStatus), TaskConstants.Status.DELETED_BY_RU.code),
                                                cb.and(root.get(BpmProcInst_.createdUser).in(filterData)),
                                                cb.equal(taskJoin[0].get(BpmTask_.taskStatus), TaskConstants.Status.DELETED_BY_RU.code))
                                ));
                                break;
                            case "listTicketTitle":
                                predicates.add(cb.and(root.get(BpmProcInst_.TICKET_TITLE).in(filterData)));
                                break;
                            case "listProcServiceName":
                            case "listServiceName":
                                predicates.add(cb.and(rootService.get(ServicePackage_.serviceName).in(filterData)));
                                break;
                            case "listCompanyCode":
                            case "listCompanyCodeFilter":
                                predicates.add(cb.and(root.get(BpmProcInst_.companyCode).in(filterData)));
                                break;
                            case "listTicketTaskDtoList":
                                if (taskJoin == null) {
                                    taskJoin = new Join[1];
                                }
                                if (taskJoin[0] == null) {
                                    taskJoin[0] = root.join(BpmProcInst_.bpmTasks, JoinType.LEFT);
                                }
                                predicates.add(cb.and(taskJoin[0].get(BpmTask_.taskName).in(filterData)));
                                predicates.add(cb.and(taskJoin[0].get(BpmTask_.taskStatus).in(TaskConstants.TabStatus.PROCESSING)));

                                break;
                            case "listTicketStatus":
                                if (!ValidationUtils.isNullOrEmpty(filterData) && filterData.contains(ProcInstConstants.Status.PROCESSING.code)) {
                                    filterData.add(ProcInstConstants.Status.OPENED.code);
                                }
                                predicates.add(cb.and(root.get(BpmProcInst_.TICKET_STATUS).in(filterData)));
                                break;
                            case "listId":
                            case "listTicketId":
                                predicates.add(cb.and(root.get(BpmProcInst_.TICKET_ID).in(filterData)));
                                break;
                            case "listChartName":
                                predicates.add(cb.and(root.get(BpmProcInst_.CHART_NAME).in(filterData)));
                                break;
                            case "listCancelReason":
                                predicates.add(cb.and(root.get(BpmProcInst_.CANCEL_REASON).in(filterData)));
                                break;
                            case "listChartNodeName":
                            case "listChartNodeNameFilter":
                                predicates.add(cb.and(root.get(BpmProcInst_.CHART_NODE_NAME).in(filterData)));
                                break;
                            case "listActionUser":
                                if (bpmHistory != null) {
                                    predicates.add(cb.and(bpmHistory.get(BpmHistory_.actionUser).in(filterData)));
                                }
                                break;
                            case "listSharedUser":
                                predicates.add(cb.and(bpmShared.get(BpmShared_.createdUser).in(filterData)));
                                break;
                            case "listCreatedUser":
                                predicates.add(cb.and(root.get(BpmProcInst_.createdUser).in(filterData)));
                                break;
                            default:
                                break;
                        }

                    }
                }
            }
        }
    }

    public Map<String, Object> searchPrivateFilterTicket(PrivateFilterRequest filter, BpmTicketFilterSearchRequest criteria) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Map<String, Object> mapFinal = new HashMap<>();
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<PrivateFilterResponse> query = cb.createQuery(PrivateFilterResponse.class);
            CriteriaQuery<Long> queryCount = cb.createQuery(Long.class);

            Root<BpmProcInst> root = query.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackage = root.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priorityManagement = root.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
            Join<BpmProcInst, BpmTask> bpmTask = root.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
            Join<BpmProcInst, BpmShared> bpmShared = root.join((BpmProcInst_.bpmShareds), JoinType.LEFT);
            Join<BpmTask, BpmTaskUser> bpmTaskUser = bpmTask.join((BpmTask_.bpmTaskUsers), JoinType.LEFT);

            Predicate predicate = getPredPrivateFilterTicket(filter, criteria, query, cb, root, servicePackage, bpmTask, bpmTaskUser, bpmShared, priorityManagement);

            Root<BpmProcInst> rootCount = queryCount.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackageCount = rootCount.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priorityManagementCount = rootCount.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
            Join<BpmProcInst, BpmTask> bpmTaskCount = rootCount.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
            Join<BpmProcInst, BpmShared> bpmSharedCount = rootCount.join((BpmProcInst_.bpmShareds), JoinType.LEFT);
            Join<BpmTask, BpmTaskUser> bpmTaskUserCount = bpmTaskCount.join((BpmTask_.bpmTaskUsers), JoinType.LEFT);

            Predicate predicateCount = getPredPrivateFilterTicket(filter, criteria, queryCount, cb, rootCount, servicePackageCount, bpmTaskCount, bpmTaskUserCount, bpmSharedCount, priorityManagementCount);

            List<Selection<?>> inputs = new ArrayList<>();
            inputs.add(root.get("ticketId"));
            inputs.add(root.get("ticketProcInstId"));
            inputs.add(root.get("requestCode"));
            inputs.add(root.get("ticketTitle"));
            inputs.add(root.get("ticketStatus"));
            inputs.add(servicePackage.get("serviceName"));
            inputs.add(root.get("ticketCreatedTime"));
            inputs.add(root.get("createdUser"));
            inputs.add(root.get("companyCode"));
            inputs.add(root.get("chartNodeName"));
            inputs.add(priorityManagement.get("name"));
            inputs.add(cb.coalesce(root.get("ticketCanceledTime"), root.get("ticketFinishTime")));
            inputs.add(cb.coalesce(root.get("cancelUser"), ""));
            inputs.add(root.get("ticketProcDefId"));
            inputs.add(root.get("ticketStartActId"));
            inputs.add(root.get("serviceId"));
            inputs.add(root.get("chartId"));

            query.multiselect(inputs).where(predicate);
            query.distinct(true);

            List<PrivateFilterResponse> listResult = em.createQuery(query) != null ? em.createQuery(query).
                    setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList() : new ArrayList<>();
            queryCount.select(cb.countDistinct(rootCount)).where(predicateCount);
            Long count = em.createQuery(queryCount).getSingleResult();

            mapFinal.put("count", count);
            mapFinal.put("data", listResult);
            return mapFinal;
        } catch (Exception e) {
            return null;
        } finally {
            if (em != null)
                em.close();
        }
    }

    public Long countFilterTicket(PrivateFilterRequest filter, String username) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<Long> queryCount = cb.createQuery(Long.class);

            Root<BpmProcInst> rootCount = queryCount.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackageCount = rootCount.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priorityManagementCount = rootCount.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
            Join<BpmProcInst, BpmTask> bpmTaskCount = rootCount.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
            Join<BpmProcInst, BpmShared> bpmSharedCount = rootCount.join((BpmProcInst_.bpmShareds), JoinType.LEFT);
            Join<BpmTask, BpmTaskUser> bpmTaskUserCount = bpmTaskCount.join((BpmTask_.bpmTaskUsers), JoinType.LEFT);

            BpmTicketFilterSearchRequest criteria = new BpmTicketFilterSearchRequest();
            criteria.setUsername(username);
            criteria.setSortBy("id");
            criteria.setSortType("asc");

            Predicate predicateCount = getPredPrivateFilterTicket(filter, criteria, queryCount, cb, rootCount, servicePackageCount, bpmTaskCount, bpmTaskUserCount, bpmSharedCount, priorityManagementCount);
            queryCount.select(cb.countDistinct(rootCount)).where(predicateCount);
            return em.createQuery(queryCount).getSingleResult();
        } catch (Exception e) {
            return null;
        }finally {
            if(em!= null)
                em.close();
        }
    }

    public Predicate getPredPrivateFilterTicket(
            PrivateFilterRequest filter,
            BpmTicketFilterSearchRequest criteria,
            CriteriaQuery<?> query,
            CriteriaBuilder cb,
            Root<BpmProcInst> root,
            Join<BpmProcInst, ServicePackage> servicePackage,
            Join<BpmProcInst, BpmTask> bpmTask,
            Join<BpmTask, BpmTaskUser> bpmTaskUser,
            Join<BpmProcInst, BpmShared> bpmShared,
            Join<BpmProcInst, PriorityManagement> priorityManagement
    ) {
        try {
            List<Predicate> predicates = new ArrayList<>();

            // isNotification: các phiếu mà user đăng nhập được Thông báo cho
            // isDiscussion: các phiếu mà user đăng nhập được tag yêu cầu tham vấn
            if (filter.getIsNotification() || filter.getIsDiscussion()) {
                if (filter.getIsNotification()) {
                    Root<BpmProInstNotifyUser> notifyUserRoot = query.from(BpmProInstNotifyUser.class);
                    predicates.add(cb.equal(root.get(BpmProcInst_.ticketId), notifyUserRoot.get(BpmProInstNotifyUser_.bpmProcinstId)));
                    predicates.add(cb.equal(notifyUserRoot.get(BpmProInstNotifyUser_.recipient), criteria.getUsername()));
                }
                if (filter.getIsDiscussion()) {
                    Root<BpmDiscussion> subDiscussion = query.from(BpmDiscussion.class);
                    predicates.add(cb.equal(subDiscussion.get(BpmDiscussion_.ticketId), root.get(BpmProcInst_.ticketId)));
                    predicates.add(cb.isFalse(subDiscussion.get(BpmDiscussion_.isAdditionalRequest)));
                    predicates.add(cb.like(subDiscussion.get(BpmDiscussion_.content), "%" + criteria.getUsername() + "%"));
                }
            } else if (ValidationUtils.isNullOrEmpty(filter.getShareStatus())) { // ko có share status thì lấy theo created user
                predicates.add(cb.equal(root.get(BpmProcInst_.createdUser), criteria.getUsername()));
            } else if (filter.getShareStatus().equalsIgnoreCase("SHARE")) {
                predicates.add(cb.and(
                        cb.equal(bpmShared.get(BpmShared_.type), "SHARED"),
                        cb.equal(bpmShared.get(BpmShared_.createdUser), criteria.getUsername()),
                        cb.or(
                                cb.isNull(bpmShared.get(BpmShared_.isDeleted)),
                                cb.isFalse(bpmShared.get(BpmShared_.isDeleted))
                        )
                ));
            } else if (filter.getShareStatus().equalsIgnoreCase("SHARED") || filter.getShareStatus().equalsIgnoreCase("FOLLOWED")) {
                predicates.add(cb.and(
                        cb.equal(bpmShared.get(BpmShared_.type), filter.getShareStatus()),
                        cb.equal(bpmShared.get(BpmShared_.sharedUser), criteria.getUsername()),
                        cb.or(
                                cb.isNull(bpmShared.get(BpmShared_.isDeleted)),
                                cb.isFalse(bpmShared.get(BpmShared_.isDeleted))
                        )
                ));

                if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListChartNodeId())) {
                    predicates.add(root.get(BpmProcInst_.chartNodeId).in(filter.getListChartNodeId()));
                }

                if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListChartId())) {
                    predicates.add(root.get(BpmProcInst_.chartId).in(filter.getListChartId()));
                }

                if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListCreatedUser())) {
                    predicates.add(root.get(BpmProcInst_.createdUser).in(filter.getListCreatedUser()));
                }
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListAssignee())) {
                predicates.add(cb.and(
                        bpmTaskUser.get(BpmTaskUser_.userName).in(filter.getListAssignee()),
                        bpmTask.get(BpmTask_.TASK_STATUS).in(TaskConstants.TabStatus.PROCESSING)
                ));
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListTicketStatus())) {
                if (filter.getListTicketStatus().contains(ProcInstConstants.Status.PROCESSING.code)) {
                    filter.getListTicketStatus().add(ProcInstConstants.Status.OPENED.code);
                }
                if (filter.getListTicketStatus().contains(ProcInstConstants.Status.COMPLETED.code)) {
                    filter.getListTicketStatus().add(ProcInstConstants.Status.CLOSED.code);
                }
                predicates.add(root.get(BpmProcInst_.ticketStatus).in(filter.getListTicketStatus()));
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListTaskDefKey())) {
                predicates.add(bpmTask.get(BpmTask_.taskDefKey).in(filter.getListTaskDefKey()));
            }

            // search theo serviceId parent
            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListService())) {
                predicates.add(cb.or(
                        servicePackage.get(ServicePackage_.id).in(filter.getListService()),
                        servicePackage.get(ServicePackage_.specialParentId).in(filter.getListService())
                ));
            }

            if (!ValidationUtils.isNullOrEmpty(filter.getIsAssistant()) && filter.getIsAssistant()) {
                predicates.add(cb.equal(root.get(BpmProcInst_.isAssistant), filter.getIsAssistant()));
                if (filter.getIsAssistant()) {
                    Root<Assistant> assistantRoot = query.from(Assistant.class);
                    predicates.add(cb.equal(assistantRoot.get(Assistant_.ticketId), root.get(BpmProcInst_.ticketId).as(String.class)));
                }
            }

            if (!ValidationUtils.isNullOrEmpty(filter.getTicketTitle())) {
                predicates.add(cb.like(root.get(BpmProcInst_.ticketTitle), "%" + filter.getTicketTitle().trim() + "%"));
            }

            if (!ValidationUtils.isNullOrEmpty(filter.getDateType())) {
                if (!ValidationUtils.isNullOrEmpty(filter.getFromDate()) && !ValidationUtils.isNullOrEmpty(filter.getToDate())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    LocalDateTime fromDate = LocalDate.parse(filter.getFromDate(), formatter).atStartOfDay();
                    LocalDateTime toDate = LocalDate.parse(filter.getToDate(), formatter).plusDays(1).atStartOfDay();

                    predicates.add(cb.between(root.get(filter.getDateType()), fromDate, toDate));

                } else if (ValidationUtils.isNullOrEmpty(filter.getFromDate()) && !ValidationUtils.isNullOrEmpty(filter.getToDate())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    LocalDateTime toDate = LocalDate.parse(filter.getToDate(), formatter).plusDays(1).atStartOfDay();

                    predicates.add(cb.lessThanOrEqualTo(root.get(filter.getDateType()), toDate));

                } else if (!ValidationUtils.isNullOrEmpty(filter.getFromDate())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    LocalDateTime fromDate = LocalDate.parse(filter.getFromDate(), formatter).atStartOfDay();

                    predicates.add(cb.greaterThanOrEqualTo(root.get(filter.getDateType()), fromDate));
                }
            }

            if (!ValidationUtils.isNullOrEmpty(criteria.getSearch())) {
                predicates.add(cb.or(
                        cb.like(cb.lower(root.get(BpmProcInst_.ticketTitle)), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get(BpmProcInst_.requestCode)), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get(BpmProcInst_.ticketId).as(String.class)), "%" + criteria.getSearch().trim().toLowerCase() + "%")
                ));
            }

            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1); // PhucVM3: Add 1 day to fix search by date range
                            if (DateDto.getFromDate().equals(DateDto.getToDate())) {
                                predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                                predicates.add(cb.lessThan(root.get(DateDto.getType()), toDateLocal));
                            } else
                                predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }

            if (criteria.getSortType().equalsIgnoreCase("asc")) {
                switch (criteria.getSortBy()) {
                    case "id":
                        query.orderBy(cb.asc(root.get("ticketId")));
                        break;
                    //Bước đang xử lý
                    case "ticketTaskDtoList":
                        Root<BpmTask> rootTask = query.from(BpmTask.class);
                        predicates.add(cb.equal(root.get("ticketProcInstId"), rootTask.get("taskProcInstId")));
                        query.orderBy(cb.asc(rootTask.get("taskName")));
                        break;
                    case "procServiceName":
                    case "serviceName":
                        query.orderBy(cb.asc(servicePackage.get("serviceName")));
                        break;
                    case "ticketCreatedTime":
                        query.orderBy(cb.asc(root.get("ticketCreatedTime")));
                        break;
                    case "ticketStatus":
                        query.orderBy(cb.asc(root.get("ticketStatus")));
                        break;
                    case "createdUser":
                        query.orderBy(cb.asc(root.get("createdUser")));
                        break;
                    case "cancelUser":
                        query.orderBy(cb.asc(root.get("cancelUser")));
                        break;
                    case "companyCode":
                        query.orderBy(cb.asc(root.get("companyCode")));
                        break;
                    case "chartNodeName":
                        query.orderBy(cb.asc(root.get("chartNodeName")));
                        break;
                    case "ticketPriority":
                        query.orderBy(cb.asc(priorityManagement.get("name")));
                        break;
                    case "requestCode":
                        query.orderBy(cb.asc(root.get("requestCode")));
                        break;
                    case "ticketEditTime":
                        query.orderBy(cb.asc(cb.coalesce(root.get("ticketCanceledTime"), root.get("ticketFinishTime"))));
                        break;
                    default:
                        query.orderBy(cb.asc(root.get(criteria.getSortBy())));
                        break;
                }
            } else {
                switch (criteria.getSortBy()) {
                    case "id":
                        query.orderBy(cb.desc(root.get("ticketId")));
                        break;
                    //Bước đang xử lý
                    case "ticketTaskDtoList":
                        Root<BpmTask> rootTask = query.from(BpmTask.class);
                        predicates.add(cb.equal(root.get("ticketProcInstId"), rootTask.get("taskProcInstId")));
                        query.orderBy(cb.desc(rootTask.get("taskName")));
                        break;
                    case "procServiceName":
                    case "serviceName":
                        query.orderBy(cb.desc(servicePackage.get("serviceName")));
                        break;
                    case "ticketCreatedTime":
                        query.orderBy(cb.desc(root.get("ticketCreatedTime")));
                        break;
                    case "ticketStatus":
                        query.orderBy(cb.desc(root.get("ticketStatus")));
                        break;
                    case "createdUser":
                        query.orderBy(cb.desc(root.get("createdUser")));
                        break;
                    case "cancelUser":
                        query.orderBy(cb.desc(root.get("cancelUser")));
                        break;
                    case "companyCode":
                        query.orderBy(cb.desc(root.get("companyCode")));
                        break;
                    case "chartNodeName":
                        query.orderBy(cb.desc(root.get("chartNodeName")));
                        break;
                    case "ticketPriority":
                        query.orderBy(cb.desc(priorityManagement.get("name")));
                        break;
                    case "requestCode":
                        query.orderBy(cb.desc(root.get("requestCode")));
                        break;
                    case "ticketEditTime":
                        query.orderBy(cb.desc(cb.coalesce(root.get("ticketCanceledTime"), root.get("ticketFinishTime"))));
                        break;
                    default:
                        query.orderBy(cb.desc(root.get(criteria.getSortBy())));
                        break;
                }
            }

            handleAddFilterTicket(criteria, cb, root, servicePackage, bpmShared,
                    null, null, query, null, priorityManagement, null, predicates);

            return cb.and(predicates.toArray(Predicate[]::new));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Map<String, Object> searchPrivateFilterAssistant(PrivateFilterRequest filter, BpmTicketFilterSearchRequest criteria, List<Long> additionalTicketIds) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Map<String, Object> mapFinal = new HashMap<>();

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<PrivateFilterResponse> query = cb.createQuery(PrivateFilterResponse.class);
            CriteriaQuery<Long> queryCount = cb.createQuery(Long.class);

            Root<BpmProcInst> root = query.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackage = root.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priorityManagement = root.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
            Join<BpmProcInst, BpmTask> bpmTask = root.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
            Join<BpmTask, BpmTaskUser> bpmTaskUser = bpmTask.join((BpmTask_.bpmTaskUsers), JoinType.LEFT);
            Join<BpmProcInst, Assistant> assistant = root.join(BpmProcInst_.assistants);

            Predicate predicate = getPredPrivateFilterAssistant(filter, criteria, query, cb, root, servicePackage, bpmTask, bpmTaskUser, assistant, priorityManagement, additionalTicketIds);

            Root<BpmProcInst> rootCount = queryCount.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackageCount = rootCount.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priorityManagementCount = rootCount.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
            Join<BpmProcInst, BpmTask> bpmTaskCount = rootCount.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
            Join<BpmTask, BpmTaskUser> bpmTaskUserCount = bpmTaskCount.join((BpmTask_.bpmTaskUsers), JoinType.LEFT);
            Join<BpmProcInst, Assistant> assistantCount = rootCount.join(BpmProcInst_.assistants);

            Predicate predicateCount = getPredPrivateFilterAssistant(filter, criteria, queryCount, cb, rootCount, servicePackageCount, bpmTaskCount, bpmTaskUserCount, assistantCount, priorityManagementCount, additionalTicketIds);

            List<Selection<?>> inputs = new ArrayList<>();
            inputs.add(root.get("ticketId"));
            inputs.add(root.get("ticketProcInstId"));
            inputs.add(root.get("requestCode"));
            inputs.add(root.get("ticketTitle"));
            inputs.add(root.get("ticketStatus"));
            inputs.add(servicePackage.get("serviceName"));
            inputs.add(root.get("ticketCreatedTime"));
            inputs.add(root.get("createdUser"));
            inputs.add(root.get("companyCode"));
            inputs.add(root.get("chartNodeName"));
            inputs.add(priorityManagement.get("name"));
            inputs.add(cb.coalesce(root.get("ticketCanceledTime"), root.get("ticketFinishTime")));
            inputs.add(cb.coalesce(root.get("cancelUser"), ""));
            inputs.add(root.get("ticketProcDefId"));
            inputs.add(root.get("ticketStartActId"));
            inputs.add(root.get("serviceId"));
            inputs.add(root.get("chartId"));

            query.multiselect(inputs).where(predicate);
            query.distinct(true);

            List<PrivateFilterResponse> listResult = em.createQuery(query) != null ? em.createQuery(query).
                    setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList() : new ArrayList<>();
            queryCount.select(cb.countDistinct(rootCount)).where(predicateCount);
            Long count = em.createQuery(queryCount).getSingleResult();

            mapFinal.put("count", count);
            mapFinal.put("data", listResult);
            return mapFinal;
        } catch (Exception e) {
            return null;
        } finally {
            if (em != null)
                em.close();
        }

    }

    public Predicate getPredPrivateFilterAssistant(
            PrivateFilterRequest filter,
            BpmTicketFilterSearchRequest criteria,
            CriteriaQuery<?> query,
            CriteriaBuilder cb,
            Root<BpmProcInst> root,
            Join<BpmProcInst, ServicePackage> servicePackage,
            Join<BpmProcInst, BpmTask> bpmTask,
            Join<BpmTask, BpmTaskUser> bpmTaskUser,
            Join<BpmProcInst, Assistant> assistant,
            Join<BpmProcInst, PriorityManagement> priorityManagement,
            List<Long> additionalTicketIds
    ) {
        try {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(cb.equal(assistant.get(Assistant_.ASSISTANT_EMAIL), criteria.getUsername()));
            predicates.add(cb.equal(root.get(BpmProcInst_.ticketStatus), ProcInstConstants.Status.DRAFT.code).not());

            if (!ValidationUtils.isNullOrEmpty(filter.getTicketTitle())) {
                predicates.add(cb.like(root.get(BpmProcInst_.ticketTitle), "%" + filter.getTicketTitle().trim() + "%"));
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListTicketStatus())) {
                if (filter.getListTicketStatus().contains(ProcInstConstants.Status.PROCESSING.code)) {
                    filter.getListTicketStatus().add(ProcInstConstants.Status.OPENED.code);
                }
                if (filter.getListTicketStatus().contains(ProcInstConstants.Status.COMPLETED.code)) {
                    filter.getListTicketStatus().add(ProcInstConstants.Status.CLOSED.code);
                }
                predicates.add(root.get(BpmProcInst_.ticketStatus).in(filter.getListTicketStatus()));
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListService())) {
                predicates.add(cb.or(
                        servicePackage.get(ServicePackage_.id).in(filter.getListService()),
                        servicePackage.get(ServicePackage_.specialParentId).in(filter.getListService())
                ));
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListTaskDefKey())) {
                predicates.add(bpmTask.get(BpmTask_.taskDefKey).in(filter.getListTaskDefKey()));
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListAssignee())) {
                predicates.add(cb.and(
                        bpmTaskUser.get(BpmTaskUser_.userName).in(filter.getListAssignee()),
                        bpmTask.get(BpmTask_.TASK_STATUS).in(TaskConstants.TabStatus.PROCESSING)
                ));
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListChartNodeId())) {
                predicates.add(root.get(BpmProcInst_.chartNodeId).in(filter.getListChartNodeId()));
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListChartId())) {
                predicates.add(root.get(BpmProcInst_.chartId).in(filter.getListChartId()));
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListCreatedUser())) {
                predicates.add(root.get(BpmProcInst_.createdUser).in(filter.getListCreatedUser()));
            }

            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListTaskPriority())) {
                predicates.add(priorityManagement.get(PriorityManagement_.id).in(filter.getListTaskPriority()));
            }

            // Yêu cầu bổ sung
            if (ValidationUtils.isAcceptSearchListInOrEmpty(filter.getListAdditionalStatus())) {
                List<Predicate> predicatesAdditionalStatus = new ArrayList<>();

                if (filter.getListAdditionalStatus().contains(BusinessEnum.AdditionStatusRequest.NEEDS_UPDATE.value)
                        || filter.getListAdditionalStatus().contains(BusinessEnum.AdditionStatusRequest.UPDATED.value)
                        || filter.getListAdditionalStatus().contains(BusinessEnum.AdditionStatusRequest.COMPLETED_NO_UPDATE.value)
                ) {
                    Root<BpmDiscussion> subDiscussion = query.from(BpmDiscussion.class);
                    predicates.add(
                            cb.and(
                                    cb.equal(subDiscussion.get(BpmDiscussion_.ticketId), root.get(BpmProcInst_.ticketId)),
                                    cb.isTrue(subDiscussion.get(BpmDiscussion_.isAdditionalRequest)),
                                    cb.isFalse(subDiscussion.get(BpmDiscussion_.isAdditionalRequestCompleted))
                            )
                    );

                    // cần bổ sung -> check status ticket = ADDITIONAL_REQUEST
                    if (filter.getListAdditionalStatus().contains(BusinessEnum.AdditionStatusRequest.NEEDS_UPDATE.value)) {
                        predicatesAdditionalStatus.add(cb.and(
                                cb.or(
                                        cb.equal(subDiscussion.get(BpmDiscussion_.statusRequest), BusinessEnum.AdditionStatusRequest.NEEDS_UPDATE.code),
                                        // data cũ check status phiếu = ADDITIONAL_REQUEST + statusRequest = null
                                        cb.and (
                                                cb.isNull(subDiscussion.get(BpmDiscussion_.statusRequest)),
                                                cb.equal(root.get(BpmProcInst_.ticketStatus), ProcInstConstants.Status.ADDITIONAL_REQUEST.code)
                                        )
                                )
                        ));
                    }

                    // đã bổ sung -> check status ticket != ADDITIONAL_REQUEST
                    if (filter.getListAdditionalStatus().contains(BusinessEnum.AdditionStatusRequest.UPDATED.value)) {
                        predicatesAdditionalStatus.add(cb.or(
                                cb.equal(subDiscussion.get(BpmDiscussion_.statusRequest), BusinessEnum.AdditionStatusRequest.UPDATED.code),
                                // data cũ check status phiếu != ADDITIONAL_REQUEST + statusRequest = null
                                cb.and(
                                        cb.notEqual(root.get(BpmProcInst_.ticketStatus), ProcInstConstants.Status.ADDITIONAL_REQUEST.code),
                                        cb.isNull(subDiscussion.get(BpmDiscussion_.statusRequest))
                                )
                        ));
                    }

                    // duyệt không bổ sung
                    if (filter.getListAdditionalStatus().contains(BusinessEnum.AdditionStatusRequest.COMPLETED_NO_UPDATE.value)) {
                        predicatesAdditionalStatus.add(
                                cb.equal(subDiscussion.get(BpmDiscussion_.statusRequest), BusinessEnum.AdditionStatusRequest.COMPLETED_NO_UPDATE.code)
                        );
                    }

                    // không chọn Người yêu cầu bổ sung -> lấy theo user đăng nhập
                    if (ValidationUtils.isNullOrEmpty(filter.getListAdditionalUser())) {
                        filter.getListAdditionalUser().add(criteria.getUsername());
                    }
                    predicates.add(subDiscussion.get(BpmDiscussion_.createdUser).in(filter.getListAdditionalUser()));
                }

                if (filter.getListAdditionalStatus().contains("none")) {
                    predicatesAdditionalStatus.add(root.get(BpmProcInst_.ticketId).in(additionalTicketIds).not());
                }

                // combine predicate
                predicates.add(cb.or(predicatesAdditionalStatus.toArray(new Predicate[0])));
            }

            // có tham vấn
            if (filter.getHasDiscussion()) {
                Root<BpmDiscussion> subDiscussion2 = query.from(BpmDiscussion.class);
                predicates.add(
                        cb.and(
                                cb.equal(subDiscussion2.get(BpmDiscussion_.ticketId), root.get(BpmProcInst_.ticketId)),
                                cb.isFalse(subDiscussion2.get(BpmDiscussion_.isAdditionalRequest))
                        )
                );
            }

            if (!ValidationUtils.isNullOrEmpty(criteria.getSearch())) {
                predicates.add(cb.or(
                        cb.like(cb.lower(root.get(BpmProcInst_.ticketTitle)), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get(BpmProcInst_.requestCode)), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get(BpmProcInst_.ticketId).as(String.class)), "%" + criteria.getSearch().trim().toLowerCase() + "%")
                ));
            }

            if (!ValidationUtils.isNullOrEmpty(filter.getDateType())) {
                if (!ValidationUtils.isNullOrEmpty(filter.getFromDate()) && !ValidationUtils.isNullOrEmpty(filter.getToDate())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    LocalDateTime fromDate = LocalDate.parse(filter.getFromDate(), formatter).atStartOfDay();
                    LocalDateTime toDate = LocalDate.parse(filter.getToDate(), formatter).plusDays(1).atStartOfDay();

                    predicates.add(cb.between(root.get(filter.getDateType()), fromDate, toDate));

                } else if (ValidationUtils.isNullOrEmpty(filter.getFromDate()) && !ValidationUtils.isNullOrEmpty(filter.getToDate())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    LocalDateTime toDate = LocalDate.parse(filter.getToDate(), formatter).plusDays(1).atStartOfDay();

                    predicates.add(cb.lessThanOrEqualTo(root.get(filter.getDateType()), toDate));

                } else if (!ValidationUtils.isNullOrEmpty(filter.getFromDate())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    LocalDateTime fromDate = LocalDate.parse(filter.getFromDate(), formatter).atStartOfDay();

                    predicates.add(cb.greaterThanOrEqualTo(root.get(filter.getDateType()), fromDate));
                }
            }

            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1); // PhucVM3: Add 1 day to fix search by date range
                            if (DateDto.getFromDate().equals(DateDto.getToDate())) {
                                predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                                predicates.add(cb.lessThan(root.get(DateDto.getType()), toDateLocal));
                            } else
                                predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }

            if (criteria.getSortType().equalsIgnoreCase("asc")) {
                switch (criteria.getSortBy()) {
                    case "id":
                        query.orderBy(cb.asc(root.get("ticketId")));
                        break;
                    //Bước đang xử lý
                    case "ticketTaskDtoList":
                        Root<BpmTask> rootTask = query.from(BpmTask.class);
                        predicates.add(cb.equal(root.get("ticketProcInstId"), rootTask.get("taskProcInstId")));
                        query.orderBy(cb.asc(rootTask.get("taskName")));
                        break;
                    case "procServiceName":
                    case "serviceName":
                        query.orderBy(cb.asc(servicePackage.get("serviceName")));
                        break;
                    case "ticketCreatedTime":
                        query.orderBy(cb.asc(root.get("ticketCreatedTime")));
                        break;
                    case "ticketStatus":
                        query.orderBy(cb.asc(root.get("ticketStatus")));
                        break;
                    case "createdUser":
                        query.orderBy(cb.asc(root.get("createdUser")));
                        break;
                    case "companyCode":
                        query.orderBy(cb.asc(root.get("companyCode")));
                        break;
                    case "chartNodeName":
                        query.orderBy(cb.asc(root.get("chartNodeName")));
                        break;
                    case "ticketPriority":
                        query.orderBy(cb.asc(priorityManagement.get("name")));
                        break;
                    case "requestCode":
                        query.orderBy(cb.asc(root.get("requestCode")));
                        break;
                    case "ticketEditTime":
                        query.orderBy(cb.desc(cb.coalesce(root.get("ticketCanceledTime"), root.get("ticketFinishTime"))));
                        break;
                    case "cancelUser":
                        query.orderBy(cb.asc(root.get("cancelUser")));
                        break;
                    default:
                        query.orderBy(cb.asc(root.get(criteria.getSortBy())));
                        break;
                }
            } else {
                switch (criteria.getSortBy()) {
                    case "id":
                        query.orderBy(cb.desc(root.get("ticketId")));
                        break;
                    //Bước đang xử lý
                    case "ticketTaskDtoList":
                        Root<BpmTask> rootTask = query.from(BpmTask.class);
                        predicates.add(cb.equal(root.get("ticketProcInstId"), rootTask.get("taskProcInstId")));
                        query.orderBy(cb.desc(rootTask.get("taskName")));
                        break;
                    case "procServiceName":
                    case "serviceName":
                        query.orderBy(cb.desc(servicePackage.get("serviceName")));
                        break;
                    case "ticketCreatedTime":
                        query.orderBy(cb.desc(root.get("ticketCreatedTime")));
                        break;
                    case "ticketStatus":
                        query.orderBy(cb.desc(root.get("ticketStatus")));
                        break;
                    case "createdUser":
                        query.orderBy(cb.desc(root.get("createdUser")));
                        break;
                    case "companyCode":
                        query.orderBy(cb.desc(root.get("companyCode")));
                        break;
                    case "chartNodeName":
                        query.orderBy(cb.desc(root.get("chartNodeName")));
                        break;
                    case "ticketPriority":
                        query.orderBy(cb.desc(priorityManagement.get("name")));
                        break;
                    case "requestCode":
                        query.orderBy(cb.desc(root.get("requestCode")));
                        break;
                    case "ticketEditTime":
                        query.orderBy(cb.desc(cb.coalesce(root.get("ticketCanceledTime"), root.get("ticketFinishTime"))));
                        break;
                    case "cancelUser":
                        query.orderBy(cb.desc(root.get("cancelUser")));
                        break;
                    default:
                        query.orderBy(cb.desc(root.get(criteria.getSortBy())));
                        break;
                }
            }

            handleAddFilterTicket(criteria, cb, root, servicePackage, null,
                    null, null, query, null, priorityManagement, null, predicates);

            return cb.and(predicates.toArray(Predicate[]::new));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public Long countFilterAssistant(PrivateFilterRequest filter, String username, List<Long> additionalTicketIds) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<Long> queryCount = cb.createQuery(Long.class);

            Root<BpmProcInst> rootCount = queryCount.from(BpmProcInst.class);
            Join<BpmProcInst, ServicePackage> servicePackageCount = rootCount.join(BpmProcInst_.servicePackage, JoinType.LEFT);
            Join<BpmProcInst, PriorityManagement> priorityManagementCount = rootCount.join(BpmProcInst_.priorityManagement, JoinType.LEFT);
            Join<BpmProcInst, BpmTask> bpmTaskCount = rootCount.join((BpmProcInst_.bpmTasks), JoinType.LEFT);
            Join<BpmTask, BpmTaskUser> bpmTaskUserCount = bpmTaskCount.join((BpmTask_.bpmTaskUsers), JoinType.LEFT);
            Join<BpmProcInst, Assistant> assistantCount = rootCount.join(BpmProcInst_.assistants);

            BpmTicketFilterSearchRequest criteria = new BpmTicketFilterSearchRequest();
            criteria.setUsername(username);
            criteria.setSortBy("id");
            criteria.setSortType("asc");

            Predicate predicateCount = getPredPrivateFilterAssistant(filter, criteria, queryCount, cb, rootCount, servicePackageCount, bpmTaskCount, bpmTaskUserCount, assistantCount, priorityManagementCount, additionalTicketIds);
            queryCount.select(cb.countDistinct(rootCount)).where(predicateCount);
            return em.createQuery(queryCount).getSingleResult();
        } catch (Exception e) {
            return null;
        }finally {
            if(em!= null)
                em.close();
        }
    }
}
