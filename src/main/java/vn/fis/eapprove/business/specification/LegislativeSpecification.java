package vn.fis.eapprove.business.specification;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.constant.LegislativeEnum;
import vn.fis.eapprove.business.domain.legislative.model.entity.LegislativeProgram;
import vn.fis.eapprove.business.domain.legislative.model.entity.LegislativeProgramDetail;
import vn.fis.eapprove.business.domain.legislative.model.entity.LegislativeProgramDetail_;
import vn.fis.eapprove.business.domain.legislative.model.entity.LegislativeProgram_;
import vn.fis.eapprove.business.domain.legislative.model.request.LegislativeDashboardRequest;
import vn.fis.eapprove.business.domain.legislative.model.request.LegislativeSearchRequest;
import vn.fis.eapprove.business.domain.legislative.model.request.ReportRequest;
import vn.fis.eapprove.business.domain.legislative.model.response.LegislativePercentChartResponse;
import vn.fis.eapprove.business.domain.legislative.model.response.RankTicketResponse;
import vn.fis.spro.common.constants.ProcInstConstants;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

@Component
@Slf4j
public class LegislativeSpecification {

    @Autowired
    private EntityManagerFactory entityManagerFactory;

    public Specification<LegislativeProgram> search(LegislativeSearchRequest criteria) {
        return ((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            query.distinct(true);

            Join<LegislativeProgram, LegislativeProgramDetail> detailJoin = root.join(LegislativeProgram_.legislativeProgramDetails, JoinType.LEFT);

            // có listChartNodeCode -> user primary
            Predicate processUserPredicate = cb.and(
                    cb.equal(detailJoin.get(LegislativeProgramDetail_.type), LegislativeEnum.LegislativeDetailType.PROCESS_USER.code),
                    cb.equal(detailJoin.get(LegislativeProgramDetail_.username), criteria.getUsername())
            );
            Predicate createdUserPredicate = cb.equal(root.get(LegislativeProgram_.createdUser), criteria.getUsername());

            if (!ValidationUtils.isNullOrEmpty(criteria.getListChartNodeCode())) {
                Predicate chartNodeCodeInPredicate = root.get(LegislativeProgram_.chartNodeCode).in(criteria.getListChartNodeCode());
                predicates.add(cb.or(
                        chartNodeCodeInPredicate,
                        createdUserPredicate,
                        processUserPredicate
                ));
            } else {
                predicates.add(cb.or(
                        createdUserPredicate,
                        processUserPredicate
                ));
            }

            predicates.add(cb.equal(root.get(LegislativeProgram_.type), criteria.getType()));

            if (!ValidationUtils.isNullOrEmpty(criteria.getActivityStatus()) && !criteria.getActivityStatus().equals("-1")) {
                predicates.add(cb.equal(root.get(LegislativeProgram_.activityStatus), criteria.getActivityStatus()));
            }

            if (!ValidationUtils.isNullOrEmpty(criteria.getSearch())) {
                predicates.add(cb.like(cb.lower(root.get(LegislativeProgram_.name)), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
            }

            // filter
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListName())) {
                predicates.add(root.get(LegislativeProgram_.name).in(criteria.getListName()));
            }
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListReleaseYear())) {
                predicates.add(root.get(LegislativeProgram_.releaseYear).in(criteria.getListReleaseYear()));
            }
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListResponsibleAgency())) {
                predicates.add(root.get(LegislativeProgram_.responsibleAgency).in(criteria.getListResponsibleAgency()));
            }
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListFileName())) {
                predicates.add(cb.and(
                        cb.equal(detailJoin.get(LegislativeProgramDetail_.type), LegislativeEnum.LegislativeDetailType.FILE.code),
                        detailJoin.get(LegislativeProgramDetail_.fileName).in(criteria.getListFileName())
                ));
            }
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListApprovalSession())) {
                predicates.add(root.get(LegislativeProgram_.approvalSession).in(criteria.getListApprovalSession()));
            }
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListReviewSession())) {
                predicates.add(root.get(LegislativeProgram_.reviewSession).in(criteria.getListReviewSession()));
            }
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListStatus())) {
                predicates.add(root.get(LegislativeProgram_.status).in(criteria.getListStatus()));
            }
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListRequestCode())) {
                predicates.add(root.get(LegislativeProgram_.requestCode).in(criteria.getListRequestCode()));
            }
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListExecutionCount())) {
                predicates.add(root.get(LegislativeProgram_.executionCount).in(criteria.getListExecutionCount()));
            }
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListCreatedUser())) {
                predicates.add(root.get(LegislativeProgram_.createdUser).in(criteria.getListCreatedUser()));
            }
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListUpdatedUser())) {
                predicates.add(root.get(LegislativeProgram_.updatedUser).in(criteria.getListUpdatedUser()));
            }
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyName())) {
                predicates.add(root.get(LegislativeProgram_.companyName).in(criteria.getListCompanyName()));
            }
            if (!ValidationUtils.isNullOrEmpty(criteria.getListDateFilter())) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                        LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                        toDateLocal = toDateLocal.plusDays(1);
                        predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                        toDateLocal = toDateLocal.plusDays(1);
                        predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                    } else if (DateDto.getFromDate() != null) {
                        LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                        predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                    }
                }
            }

            return cb.and(predicates.toArray(Predicate[]::new));
        });
    }

    public List<LegislativePercentChartResponse> searchPercentChart(LegislativeDashboardRequest criteria) {
        try (EntityManager em = entityManagerFactory.createEntityManager()) {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<LegislativePercentChartResponse> query = cb.createQuery(LegislativePercentChartResponse.class);
            Root<LegislativeProgram> root = query.from(LegislativeProgram.class);

            // convert estimated_time thành date
            Expression<LocalDateTime> estimatedDate = cb.function(
                    "STR_TO_DATE", LocalDateTime.class,
                    root.get(LegislativeProgram_.estimatedTime),
                    cb.literal("%Y-%m-%d")
            );

            // current_timestamp
            Expression<LocalDateTime> currentTime = cb.localDateTime();

            // CASE WHEN ticket_finish_time is not null and activityStatus = COMPLETED and ticket_finish_time <= estimatedDate THEN 1 ELSE 0 END
            Expression<Integer> approvedNotDelayed = cb.sum(
                    cb.<Integer>selectCase()
                            .when(cb.and(
                                    cb.isNotNull(root.get(LegislativeProgram_.ticketFinishTime)),
                                    cb.equal(root.get(LegislativeProgram_.activityStatus), LegislativeEnum.LegislativeActivityStatus.COMPLETED.code),
                                    cb.lessThanOrEqualTo(root.get(LegislativeProgram_.ticketFinishTime), estimatedDate)
                            ), 1)
                            .otherwise(0)
            );

            // ticket_finish_time is not null and activityStatus = COMPLETED and ticket_finish_time > estimatedDate
            Expression<Integer> approvedDelayed = cb.sum(
                    cb.<Integer>selectCase()
                            .when(cb.and(
                                    cb.isNotNull(root.get(LegislativeProgram_.ticketFinishTime)),
                                    cb.equal(root.get(LegislativeProgram_.activityStatus), LegislativeEnum.LegislativeActivityStatus.COMPLETED.code),
                                    cb.greaterThan(root.get(LegislativeProgram_.ticketFinishTime), estimatedDate)
                            ), 1)
                            .otherwise(0)
            );

            // activityStatus = PROCESSING and current_timestamp <= estimatedDate
            Expression<Integer> approvingNotDelayed = cb.sum(
                    cb.<Integer>selectCase()
                            .when(cb.and(
                                    cb.equal(root.get(LegislativeProgram_.activityStatus), LegislativeEnum.LegislativeActivityStatus.PROCESSING.code),
                                    cb.lessThanOrEqualTo(currentTime, estimatedDate)
                            ), 1)
                            .otherwise(0)
            );

            // activityStatus = PROCESSING and current_timestamp > estimatedDate
            Expression<Integer> approvingDelayed = cb.sum(
                    cb.<Integer>selectCase()
                            .when(cb.and(
                                    cb.equal(root.get(LegislativeProgram_.activityStatus), LegislativeEnum.LegislativeActivityStatus.PROCESSING.code),
                                    cb.greaterThan(currentTime, estimatedDate)
                            ), 1)
                            .otherwise(0)
            );

            // predicate
            Predicate pred = searchPercentChartPred(criteria, cb, root);

            // select
            query.multiselect(
                    root.get(LegislativeProgram_.responsibleAgency).alias("companyCode"),
                    approvedNotDelayed.alias("approvedNotDelayed"),
                    approvedDelayed.alias("approvedDelayed"),
                    approvingNotDelayed.alias("approvingNotDelayed"),
                    approvingDelayed.alias("approvingDelayed")
            ).where(pred);

            // group by
            query.groupBy(root.get(LegislativeProgram_.responsibleAgency));

            return em.createQuery(query) != null ? em.createQuery(query).getResultList() : new ArrayList<>();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    private Predicate searchPercentChartPred(LegislativeDashboardRequest criteria, CriteriaBuilder cb, Root<LegislativeProgram> root) {
        List<Predicate> predicates = new ArrayList<>();

        if (ValidationUtils.isAcceptSearchListInOrEmpty(criteria.getListCompanyCode())) {
            predicates.add(root.get(LegislativeProgram_.responsibleAgency).in(criteria.getListCompanyCode()));
        }

        // Khối tổ chức
        if (!ValidationUtils.isNullOrEmpty(criteria.getOrganizationSector())) {
            predicates.add(cb.equal(root.get(LegislativeProgram_.organizationSector), criteria.getOrganizationSector()));
        }

        if (!ValidationUtils.isNullOrEmpty(criteria.getFromYear()) && !ValidationUtils.isNullOrEmpty(criteria.getToYear())) {
            predicates.add(cb.and(
                    cb.greaterThanOrEqualTo(root.get(LegislativeProgram_.releaseYear), criteria.getFromYear()),
                    cb.lessThanOrEqualTo(root.get(LegislativeProgram_.releaseYear), criteria.getToYear())
            ));
        }

        if (ValidationUtils.isAcceptSearchListIn(criteria.getListResponsibleAgency())) {
            predicates.add(root.get(LegislativeProgram_.responsibleAgency).in(criteria.getListResponsibleAgency()));
        }
        if (ValidationUtils.isAcceptSearchListIn(criteria.getListActivityStatus())) {
            predicates.add(root.get(LegislativeProgram_.activityStatus).in(criteria.getListActivityStatus()));
        }

        return cb.and(predicates.toArray(Predicate[]::new));
    }

    public List<RankTicketResponse> searchRankTickets(LegislativeDashboardRequest criteria) {
        try (EntityManager em = entityManagerFactory.createEntityManager()) {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<RankTicketResponse> query = cb.createQuery(RankTicketResponse.class);
            Root<LegislativeProgram> root = query.from(LegislativeProgram.class);

            // predicate
            Predicate pred = searchRankTicketsPred(criteria, cb, root);

            query.distinct(true);

            query.multiselect(
                    root.get(LegislativeProgram_.ticketId).alias("ticketId"),
                    root.get(LegislativeProgram_.ticketTitle).alias("ticketTitle"),
                    root.get(LegislativeProgram_.responsibleAgency).alias("responseAgency"),
                    root.get(LegislativeProgram_.ticketCreatedTime).alias("ticketCreatedTime"),
                    root.get(LegislativeProgram_.ticketFinishTime).alias("ticketFinishTime"),
                    root.get(LegislativeProgram_.processTime).alias("processTime"),
                    root.get(LegislativeProgram_.procDefId).alias("procDefId"),
                    root.get(LegislativeProgram_.startKey).alias("startKey"),
                    root.get(LegislativeProgram_.processType).alias("processType")
            );

            query.where(cb.and(pred));

            // order by process time asc
            query.orderBy(cb.asc(root.get(LegislativeProgram_.processTime)));
            TypedQuery<RankTicketResponse> resultQuery = em.createQuery(query);
            resultQuery.setFirstResult(0); // For top records in ascending order
            resultQuery.setMaxResults(5); // Top 5 records
            List<RankTicketResponse> topAsc = resultQuery.getResultList();

            // order by process time desc
            query.orderBy(cb.desc(root.get(LegislativeProgram_.processTime)));
            resultQuery = em.createQuery(query);
            resultQuery.setFirstResult(0); // For top records in descending order
            resultQuery.setMaxResults(5); // Top 5 records
            List<RankTicketResponse> topDesc = resultQuery.getResultList();

            Set<RankTicketResponse> uniqueResults = new LinkedHashSet<>();
            uniqueResults.addAll(topAsc);
            uniqueResults.addAll(topDesc);

            return new ArrayList<>(uniqueResults);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return new ArrayList<>();
        }

    }

    private Predicate searchRankTicketsPred(LegislativeDashboardRequest criteria, CriteriaBuilder cb, Root<LegislativeProgram> root) {
        List<Predicate> predicates = new ArrayList<>();

        if (ValidationUtils.isAcceptSearchListInOrEmpty(criteria.getListCompanyCode())) {
            predicates.add(root.get(LegislativeProgram_.responsibleAgency).in(criteria.getListCompanyCode()));
        }

        predicates.add(cb.equal(root.get(LegislativeProgram_.ticketStatus), ProcInstConstants.Status.COMPLETED.code));
        predicates.add(cb.isNotNull(root.get(LegislativeProgram_.ticketCreatedTime)));
        predicates.add(cb.isNotNull(root.get(LegislativeProgram_.ticketFinishTime)));

        if (!ValidationUtils.isNullOrEmpty(criteria.getType())) {
            predicates.add(cb.equal(root.get(LegislativeProgram_.type), criteria.getType()));
        }

        // Khối tổ chức
        if (!ValidationUtils.isNullOrEmpty(criteria.getOrganizationSector())) {
            predicates.add(cb.equal(root.get(LegislativeProgram_.organizationSector), criteria.getOrganizationSector()));
        }

        if (!ValidationUtils.isNullOrEmpty(criteria.getFromYear()) && !ValidationUtils.isNullOrEmpty(criteria.getToYear())) {
            predicates.add(cb.and(
                    cb.greaterThanOrEqualTo(root.get(LegislativeProgram_.releaseYear), criteria.getFromYear()),
                    cb.lessThanOrEqualTo(root.get(LegislativeProgram_.releaseYear), criteria.getToYear())
            ));
        }

        if (ValidationUtils.isAcceptSearchListIn(criteria.getListResponsibleAgency())) {
            predicates.add(root.get(LegislativeProgram_.responsibleAgency).in(criteria.getListResponsibleAgency()));
        }

        return cb.and(predicates.toArray(Predicate[]::new));
    }

    public Specification<LegislativeProgram> searchReport(ReportRequest criteria, List<String> lstPermissionCode) {
        return ((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            query.distinct(true);

            predicates.add(cb.isNotNull(root.get(LegislativeProgram_.ticketId)));

            if (ValidationUtils.isAcceptSearchListInOrEmpty(lstPermissionCode)) {
                predicates.add(root.get(LegislativeProgram_.responsibleAgency).in(lstPermissionCode));
            }

            if (!ValidationUtils.isNullOrEmpty(criteria.getOrganizationSector())) {
                predicates.add(cb.equal(root.get(LegislativeProgram_.organizationSector), criteria.getOrganizationSector()));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getListResponsibleAgency())) {
                predicates.add(root.get(LegislativeProgram_.responsibleAgency).in(criteria.getListResponsibleAgency()));
            }

            if (!ValidationUtils.isNullOrEmpty(criteria.getType())) {
                predicates.add(cb.equal(root.get(LegislativeProgram_.type), criteria.getType()));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getListProcessType())) {
                predicates.add(root.get(LegislativeProgram_.processType).in(criteria.getListProcessType()));
            }

            if (!ValidationUtils.isNullOrEmpty(criteria.getSearch())) {
                predicates.add(cb.like(cb.lower(root.get(LegislativeProgram_.name)), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
            }

            if (!ValidationUtils.isNullOrEmpty(criteria.getFromDate()) && !ValidationUtils.isNullOrEmpty(criteria.getDateType())) {
                LocalDateTime fromDateLocal = LocalDateTime.ofInstant(criteria.getFromDate().toInstant(), ZoneId.systemDefault());
                if (criteria.getToDate() != null) {
                    LocalDateTime toDateLocal = LocalDateTime.ofInstant(criteria.getToDate().toInstant(), ZoneId.systemDefault());
                    toDateLocal = toDateLocal.plusDays(1);
                    predicates.add(cb.between(root.get(criteria.getDateType()), fromDateLocal, toDateLocal));
                } else {
                    predicates.add(cb.greaterThanOrEqualTo(root.get(criteria.getDateType()), fromDateLocal));
                }
            }

            if (!ValidationUtils.isNullOrEmpty(criteria.getName())) {
                predicates.add(cb.like(root.get(LegislativeProgram_.name), "%" + criteria.getName() + "%"));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getListPolicyDevProcess())) {
                predicates.add(root.get(LegislativeProgram_.policyDevelopmentProcess).as(String.class).in(criteria.getListPolicyDevProcess()));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getListReleaseYear())) {
                predicates.add(root.get(LegislativeProgram_.releaseYear).in(criteria.getListReleaseYear()));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getListActivityStatus())) {
                predicates.add(root.get(LegislativeProgram_.activityStatus).in(criteria.getListActivityStatus()));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getListName())) {
                predicates.add(root.get(LegislativeProgram_.name).in(criteria.getListName()));
            }

            // Join detail
            if (!ValidationUtils.isNullOrEmpty(criteria.getTaskName())
                    || ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyCode())
                    || ValidationUtils.isAcceptSearchListIn(criteria.getListFileName())
                    || ValidationUtils.isAcceptSearchListIn(criteria.getListTaskName())
            ) {
                Join<LegislativeProgram, LegislativeProgramDetail> detailJoin = root.join(LegislativeProgram_.legislativeProgramDetails, JoinType.LEFT);
                if (!ValidationUtils.isNullOrEmpty(criteria.getTaskName())) {
                    predicates.add(cb.like(cb.lower(detailJoin.get(LegislativeProgramDetail_.taskName)), "%" + criteria.getTaskName().trim().toLowerCase() + "%"));
                }
                if (ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyCode())) {
                    predicates.add(detailJoin.get(LegislativeProgramDetail_.companyCode).in(criteria.getListCompanyCode()));
                }
                if (ValidationUtils.isAcceptSearchListIn(criteria.getListFileName())) {
                    predicates.add(detailJoin.get(LegislativeProgramDetail_.fileName).in(criteria.getListFileName()));
                }
                if (ValidationUtils.isAcceptSearchListIn(criteria.getListTaskName())) {
                    predicates.add(detailJoin.get(LegislativeProgramDetail_.taskName).in(criteria.getListTaskName()));
                }
            }

            return cb.and(predicates.toArray(Predicate[]::new));
        });
    }

}
