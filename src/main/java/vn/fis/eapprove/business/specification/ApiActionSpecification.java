package vn.fis.eapprove.business.specification;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.api.entity.ApiManagement;
import vn.fis.eapprove.business.domain.api.entity.ApiManagement_;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement_;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser_;
import vn.fis.eapprove.business.model.request.ApiManagementFilterRequest;
import vn.fis.eapprove.business.model.request.DateRequest;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ShareUserTypeEnum;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.criteria.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;



@Component
@Slf4j
public class ApiActionSpecification {
    public Specification<ApiManagement> filter(final ApiManagementFilterRequest search, String username) {

        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            query.distinct(true);

            if (StringUtils.isNotEmpty(search.getSearch())) {
                predicates.add(cb.or(cb.like(cb.lower(root.get("name")), "%" + search.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("url")), "%" + search.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("createdUser")), "%" + search.getSearch().trim().toLowerCase() + "%")
                ));
            }

            if (search.getListStatus() != null && !search.getListStatus().contains(CommonConstants.FILTER_SELECT_ALL)) {
                predicates.add(root.get("status").in(search.getListStatus()));
            }
            if (search.getListType() != null && !search.getListType().contains(CommonConstants.FILTER_SELECT_ALL)) {
                predicates.add(root.get("type").in(search.getListType()));
            }
            if (search.getListMethod() != null && !search.getListMethod().contains(CommonConstants.FILTER_SELECT_ALL)) {
                predicates.add(root.get("method").in(search.getListMethod()));
            }
            if (search.getUpdatedUser() != null && !search.getUpdatedUser().contains(CommonConstants.FILTER_SELECT_ALL)) {
                predicates.add(root.get("updatedUser").in(search.getUpdatedUser()));
            }
            if (search.getCreatedUser() != null && !search.getCreatedUser().contains(CommonConstants.FILTER_SELECT_ALL)) {
                predicates.add(root.get("createdUser").in(search.getCreatedUser()));
            }

            List<Predicate> predicatesa = new ArrayList<>();

            if (search.getDateRequest() != null && !search.getDateRequest().isEmpty()) {
                for (DateRequest DateDto : search.getDateRequest()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }

            if(search.getName() != null && !search.getName().contains(CommonConstants.FILTER_SELECT_ALL)){
                predicates.add(root.get("name").in(search.getName()));
            }
            if(search.getShareWith() != null && !search.getShareWith().contains(CommonConstants.FILTER_SELECT_ALL)){
                Join<ApiManagement, SharedUser> sharedUser = root.join(ApiManagement_.sharedUsers, JoinType.LEFT);
                predicates.add(cb.and(
                        cb.equal(sharedUser.get(SharedUser_.referenceType), ShareUserTypeEnum.APIMANAGEMENT.type),
                        sharedUser.get(SharedUser_.email).in(search.getShareWith()))
                );
            }
            if(search.getUrl() != null && !search.getUrl().contains(CommonConstants.FILTER_SELECT_ALL)){
                predicates.add(root.get("url").in(search.getUrl()));
            }
            if(search.getDescription() != null && !search.getDescription().contains(CommonConstants.FILTER_SELECT_ALL)){
                predicates.add(root.get("description").in(search.getDescription()));
            }
            if(search.getCompanyCode() != null && !search.getCompanyCode().contains(CommonConstants.FILTER_SELECT_ALL)){
                predicates.add(root.get("companyCode").in(search.getCompanyCode()));
            }
            if(search.getCompanyName() != null && !search.getCompanyName().contains(CommonConstants.FILTER_SELECT_ALL)){
                predicates.add(root.get("companyName").in(search.getCompanyName()));
            }

            if (search.getListDateFilter() != null && !search.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : search.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1); // PhucVM3: Add 1 day to fix search by date range
                            predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1); // PhucVM3: Add 1 day to fix search by date range
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                        }
                    }
                }
            }

            if (!ValidationUtils.isNullOrEmpty(search.getIsShared()) && search.getIsShared()) {
                Join<ApiManagement, SharedUser> sharedUser = root.join(ApiManagement_.sharedUsers, JoinType.LEFT);

                predicates.add(cb.and(
                        cb.equal(sharedUser.get(SharedUser_.referenceType), ShareUserTypeEnum.APIMANAGEMENT.type),
                        cb.equal(sharedUser.get(SharedUser_.email), username))
                );
            } else
            if (!search.getListCompanyCode().contains(CommonConstants.FILTER_SELECT_ALL)) {
                Join<ApiManagement, PermissionDataManagement> permissionDataManagement = root.join(ApiManagement_.permissionDataManagements);
                if (!ValidationUtils.isNullOrEmpty(search.getLstGroupPermissionId())) {
                    // có phân quyền theo nhóm ưu tiên lấy các bản ghi trong nhóm + theo companyCode member admin (nếu có)
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.API_MANAGEMENT.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(search.getListCompanyCodeMemberAdmin()),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL),
                                    // Phân quyền theo nhóm
                                    root.get(ApiManagement_.id).in(search.getLstGroupPermissionId())
                            ))
                    );
                } else {
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.API_MANAGEMENT.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(search.getListCompanyCode()),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
                            ))
                    );
                }
            }

            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }

}
