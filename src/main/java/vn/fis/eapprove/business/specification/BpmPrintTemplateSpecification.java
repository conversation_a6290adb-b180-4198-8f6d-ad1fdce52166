package vn.fis.eapprove.business.specification;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.bpm.entity.BpmConfigPrintTemplate;
import vn.fis.eapprove.business.dto.BaseDto;


import jakarta.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

@Component
public class BpmPrintTemplateSpecification {
    public Specification<BpmConfigPrintTemplate> filter(final BaseDto criteria) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotEmpty(criteria.getSearch())) {
                predicates.add(cb.or(
                        cb.like(cb.lower(root.get("name")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("createdUser")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("updatedUser")), "%" + criteria.getSearch().trim().toLowerCase() + "%")
                ));
            }
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }
}
