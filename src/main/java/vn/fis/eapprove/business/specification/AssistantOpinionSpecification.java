package vn.fis.eapprove.business.specification;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.assistant.entity.AssistantOpinion;
import vn.fis.eapprove.business.dto.AssistantOpinionDto;


import jakarta.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

/**
 * Author: AnhVTN
 * Date: 01/03/2023
 */
@Component
public class AssistantOpinionSpecification {
    public Specification<AssistantOpinion> filter(final AssistantOpinionDto request) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotEmpty(request.getSearch())) {
                predicates.add(cb.and(
                        cb.like(cb.lower(root.get("opinion")), "%" + request.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("assistantEmail")), "%" + request.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("ticketId")), request.getTicketId())
                ));
            } else {
                predicates.add(cb.and(
                        cb.equal(root.get("ticketId"), request.getTicketId())
                ));
            }
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }
}
