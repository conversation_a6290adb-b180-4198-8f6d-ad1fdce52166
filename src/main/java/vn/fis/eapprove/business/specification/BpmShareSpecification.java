package vn.fis.eapprove.business.specification;

import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.bpm.entity.BpmShared;
import vn.fis.eapprove.business.domain.bpm.entity.BpmShared_;
import vn.fis.eapprove.business.model.request.ShareRequest;



import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class BpmShareSpecification {
    public Specification<BpmShared> filterShare(final ShareRequest criteria) {

        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotEmpty(criteria.getProcInstId())) {
                predicates.add(cb.equal(root.get("procInstId"), criteria.getProcInstId().trim()));
            }
            if (StringUtils.isNotEmpty(criteria.getUser())) {
                predicates.add(cb.equal(root.get("sharedUser"), criteria.getUser().trim()));
            }
            if (StringUtils.isNotEmpty(criteria.getSearch())) {
                predicates.add(cb.like(cb.lower(root.get("sharedUser")), "%" + criteria.getSearch().trim().toLowerCase() + "%"));
            }
            if (StringUtils.isNotEmpty(criteria.getType())) {
                if (criteria.getType().equalsIgnoreCase("shared")) {
                    predicates.add(root.get("type").in(Arrays.asList("SHARED", "DELETED")));
                } else {
                    predicates.add(cb.equal(cb.upper(root.get("type")), criteria.getType().toUpperCase()));
                }
            }
            Predicate isDeletedFalseOrNull = cb.or(
                    cb.isNull(root.get(BpmShared_.isDeleted)),
                    cb.equal(root.get(BpmShared_.isDeleted), false)
            );
            predicates.add(isDeletedFalseOrNull);
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }
}
