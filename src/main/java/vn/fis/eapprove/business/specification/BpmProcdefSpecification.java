package vn.fis.eapprove.business.specification;

import jakarta.persistence.*;
import jakarta.persistence.criteria.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef;
import vn.fis.eapprove.business.domain.bpm.entity.BpmProcdef_;
import vn.fis.eapprove.business.domain.groupTable.entity.GroupTableProTemp;
import vn.fis.eapprove.business.domain.groupTable.entity.GroupTableProTemp_;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement_;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage;
import vn.fis.eapprove.business.domain.servicePackage.entity.ServicePackage_;
import vn.fis.eapprove.business.domain.servicePackage.service.ServicePackageManager;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser;
import vn.fis.eapprove.business.domain.sharedUser.entity.SharedUser_;
import vn.fis.eapprove.business.dto.BpmProcDefFilterDto;
import vn.fis.eapprove.business.dto.BpmProcdefDto;import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.constants.ShareUserTypeEnum;
import vn.fis.spro.common.util.ValidationUtils;


import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class BpmProcdefSpecification {

    @Autowired
    private ServicePackageManager servicePackageManager;

    @Autowired
    private EntityManagerFactory entityManagerFactory;


    public Specification<BpmProcdef> filter(final BpmProcdefDto criteria, String username) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            String status = "DELETED";
            query.distinct(true);

            if (ValidationUtils.isNullOrEmpty(criteria.getListCompanyCode())) {
                criteria.setListCompanyCode(new ArrayList<>());
            }

            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (BpmProcDefFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListServiceName())) {
                Join<BpmProcdef, ServicePackage> servicePackage = root.join(BpmProcdef_.servicePackages);
                predicates.add(servicePackage.get(ServicePackage_.id).in(criteria.getListServiceName()));
            }
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListUserCreated())) {
                predicates.add(root.get(BpmProcdef_.userCreated).in(criteria.getListUserCreated()));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getListUserUpdate())) {
                predicates.add(root.get(BpmProcdef_.userUpdate).in(criteria.getListUserUpdate()));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyCodeFilter())) {
                predicates.add(root.get(BpmProcdef_.companyCode).in(criteria.getListCompanyCodeFilter()));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyName())) {
                predicates.add(root.get(BpmProcdef_.companyName).in(criteria.getListCompanyName()));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getListName())) {
                predicates.add(root.get(BpmProcdef_.name).in(criteria.getListName()));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getListDescription())) {
                predicates.add(root.get(BpmProcdef_.description).in(criteria.getListDescription()));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getListResourceName())) {
                predicates.add(root.get(BpmProcdef_.resourceName).in(criteria.getListResourceName()));
            }

            if (ValidationUtils.isAcceptSearchListIn(criteria.getListStatus())) {
                predicates.add(root.get("status").in(criteria.getListStatus()));
            }
            if (ValidationUtils.isAcceptSearchListIn(criteria.getListTemplateName())) {
                Root<GroupTableProTemp> rootTemp = query.from(GroupTableProTemp.class);
                predicates.add(rootTemp.get(GroupTableProTemp_.proDefId).in(root.get(BpmProcdef_.PROC_DEF_ID)));
                predicates.add(rootTemp.get(GroupTableProTemp_.formKey).in(criteria.getListTemplateName()));
            }
            predicates.add(cb.notEqual(root.get(BpmProcdef_.status), status));
            if (StringUtils.isNotEmpty(criteria.getSearch())) {
                predicates.add(cb.or(
                        cb.like(cb.lower(root.get("name")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.or(cb.like(cb.lower(root.get("id").as(String.class)), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                                cb.like(cb.lower(root.get("userUpdate")), "%" + criteria.getSearch().trim().toLowerCase() + "%")
                        )));
            }

            // check permission data - shared user (companyCode = -1 -> next)
            if (!ValidationUtils.isNullOrEmpty(criteria.getIsShared()) && criteria.getIsShared()) {
                Join<BpmProcdef, SharedUser> sharedUser = root.join(BpmProcdef_.sharedUsers, JoinType.LEFT);
                predicates.add(cb.and(
                        cb.equal(sharedUser.get(SharedUser_.referenceType), ShareUserTypeEnum.PROCESS.type),
                        cb.equal(sharedUser.get(SharedUser_.email), username))
                );
            } else
            if (!criteria.getListCompanyCode().contains(CommonConstants.FILTER_SELECT_ALL)) {
                Join<BpmProcdef, PermissionDataManagement> permissionDataManagement = root.join(BpmProcdef_.permissionDataManagements);
                // có phân quyền theo nhóm ưu tiên lấy các bản ghi trong nhóm + theo companyCode member admin (nếu có)
                if (!ValidationUtils.isNullOrEmpty(criteria.getLstGroupPermissionId())) {
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.BPM_PROCDEF.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(criteria.getListCompanyCodeMemberAdmin()),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL),
                                    // Phân quyền theo nhóm
                                    root.get(BpmProcdef_.id).in(criteria.getLstGroupPermissionId())
                            ))
                    );
                } else {
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.BPM_PROCDEF.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(criteria.getListCompanyCode()),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
                            ))
                    );
                }
            }

            // filter special clone template
            if (!ValidationUtils.isNullOrEmpty(criteria.getSpecialFlow())) {
                if (criteria.getSpecialFlow()) { // search template con
                    predicates.add(cb.isTrue(root.get(BpmProcdef_.specialFlow)));
                } else {
                    predicates.add(cb.isNull(root.get(BpmProcdef_.specialFlow)));
                }
            }

            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };

    }
}
