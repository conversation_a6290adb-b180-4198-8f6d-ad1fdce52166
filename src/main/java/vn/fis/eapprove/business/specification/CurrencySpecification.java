package vn.fis.eapprove.business.specification;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.currency.entity.Currency;
import vn.fis.eapprove.business.domain.currency.entity.Currency_;
import vn.fis.eapprove.business.dto.CurrencyFilterDto;

import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.criteria.*;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Author: AnhVTN
 * Date: 31/03/2023
 */

@Component
public class CurrencySpecification {
    public Specification<Currency> filter(final CurrencyFilterDto criteria) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotEmpty(criteria.getSearch())) {
                Root<Currency> currencyRoot = query.from(Currency.class);
                query.distinct(true);
                predicates.add(cb.or(cb.like(cb.lower(root.get("code")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("name")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("anotherName")), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCode())){
                predicates.add(cb.and(root.get(Currency_.CODE).in(criteria.getListCode())));
            }
            if(ValidationUtils.isAcceptSearchListIn(criteria.getListName())){
                predicates.add(cb.and(root.get(Currency_.NAME).in(criteria.getListName())));
            }
            if(ValidationUtils.isAcceptSearchListIn(criteria.getListAnotherName())){
                predicates.add(cb.and(root.get(Currency_.ANOTHER_NAME).in(criteria.getListAnotherName())));
            }
            if(ValidationUtils.isAcceptSearchListIn(criteria.getListRoundingRules())){
                predicates.add(cb.and(root.get(Currency_.ROUNDING_RULES).in(criteria.getListRoundingRules())));
            }
            if(ValidationUtils.isAcceptSearchListIn(criteria.getListUserCreate())){
                predicates.add(cb.and(root.get(Currency_.USER_CREATE).in(criteria.getListUserCreate())));
            }
            if(ValidationUtils.isAcceptSearchListIn(criteria.getListUserUpdated())){
                predicates.add(cb.and(root.get(Currency_.USER_UPDATED).in(criteria.getListUserUpdated())));
            }
            if(ValidationUtils.isAcceptSearchListIn(criteria.getListDescription())){
                predicates.add(cb.and(root.get(Currency_.DESCRIPTION).in(criteria.getListDescription())));
            }

            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.between(root.get(DateDto.getType()), Date.from(Instant.from(fromDateLocal)), Date.from(Instant.from(toDateLocal))));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), Date.from(Instant.from(toDateLocal))));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), Date.from(Instant.from(fromDateLocal))));
                        } catch (Exception e) {
                        }
                    }
                }
            }
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }
}
