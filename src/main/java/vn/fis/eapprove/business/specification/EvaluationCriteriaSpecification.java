package vn.fis.eapprove.business.specification;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.evaluation.entity.EvaluationCriteria;
import vn.fis.eapprove.business.domain.evaluation.entity.EvaluationCriteria_;
import vn.fis.eapprove.business.domain.evaluation.entity.EvaluationDepartment;
import vn.fis.eapprove.business.domain.evaluation.entity.EvaluationDepartment_;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement_;
import vn.fis.eapprove.business.dto.EvaluationCriteriaDto;
import vn.fis.eapprove.business.dto.EvaluationCriteriaSearchDto;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.criteria.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class EvaluationCriteriaSpecification {

    public Specification<EvaluationCriteria> filter(final EvaluationCriteriaDto criteria, List<String> reviewList) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotEmpty(criteria.getSearch())) {
                predicates.add(cb.or
                        (
                                cb.like(cb.lower(root.get("name")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
//                                cb.like(cb.lower(root.get("reviewItem")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
//                                root.get("reviewItem").in(reviewList),
                                cb.like(cb.lower(root.get("description")), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
            }
            if (StringUtils.isNotEmpty(criteria.getStatus())) {
                predicates.add(cb.and(cb.equal(root.get("status"), criteria.getStatus())));
            }
            Join<EvaluationCriteria, EvaluationDepartment> evaluationDepartment = null;
            if (ValidationUtils.isAcceptSearchListIn(criteria.getLstDepartments())) {
                evaluationDepartment= root.join(EvaluationCriteria_.evaluationDepartments, JoinType.LEFT);
                predicates.add(evaluationDepartment.get(EvaluationDepartment_.departmentCodes).in(criteria.getLstDepartments()));
            }

            if (!criteria.getLstCompanyCode().contains(CommonConstants.FILTER_SELECT_ALL)) {
                Join<EvaluationCriteria, PermissionDataManagement> permissionDataManagement = root.join(EvaluationCriteria_.permissionDataManagements);
                if (!ValidationUtils.isNullOrEmpty(criteria.getLstGroupPermissionId())) {
                    // có phân quyền theo nhóm ưu tiên lấy các bản ghi trong nhóm + theo companyCode member admin (nếu có)
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.EVALUATION_CRITERIA.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(criteria.getListCompanyCodeMemberAdmin()),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL),
                                    // Phân quyền theo nhóm
                                    root.get(EvaluationCriteria_.id).in(criteria.getLstGroupPermissionId())
                            )
                    ));
                } else {
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.EVALUATION_CRITERIA.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(criteria.getLstCompanyCode()),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
                            )
                    ));
                }
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListName())){
                predicates.add(cb.and(root.get(EvaluationCriteria_.NAME).in(criteria.getListName())));
            }

//            if(ValidationUtils.isAcceptSearchListIn(criteria.getListReviewItem())){
//                predicates.add(cb.and(root.get(EvaluationCriteria_.REVIEW_ITEM).in(criteria.getListReviewItem())));
//            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListUpdatedUser())){
                predicates.add(cb.and(root.get(EvaluationCriteria_.UPDATED_USER).in(criteria.getListUpdatedUser())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCreatedUser())){
                predicates.add(cb.and(root.get(EvaluationCriteria_.CREATED_USER).in(criteria.getListCreatedUser())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListDescription())){
                predicates.add(cb.and(root.get(EvaluationCriteria_.DESCRIPTION).in(criteria.getListDescription())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyCodeFilter())){
                predicates.add(cb.and(root.get("companyCode").in(criteria.getListCompanyCodeFilter())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyName())){
                predicates.add(cb.and(root.get("companyName").in(criteria.getListCompanyName())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getLstDepartments())){
                if(evaluationDepartment == null)
                    evaluationDepartment = root.join(EvaluationCriteria_.evaluationDepartments, JoinType.LEFT);
                predicates.add(evaluationDepartment.get(EvaluationDepartment_.departmentCodes).in(criteria.getLstDepartments()));
            }

            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                        }
                    }
                }
            }


            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }

    public Specification<EvaluationCriteria> filterCount(final EvaluationCriteriaDto criteria, List<String> reviewList) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotEmpty(criteria.getSearch())) {
                predicates.add(cb.or(cb.like(cb.lower(root.get("name")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        root.get("reviewItem").in(reviewList),
                        cb.like(cb.lower(root.get("description")), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
            }
            if (StringUtils.isNotEmpty(criteria.getStatus())) {
                predicates.add(cb.and(cb.equal(root.get("status"), criteria.getStatus())));
            }

            if (!criteria.getLstCompanyCode().contains(CommonConstants.FILTER_SELECT_ALL)) {
                Join<EvaluationCriteria, PermissionDataManagement> permissionDataManagement = root.join(EvaluationCriteria_.permissionDataManagements);
                predicates.add(cb.and(
                        cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.EVALUATION_CRITERIA.code),
                        cb.or(
                                permissionDataManagement.get(PermissionDataManagement_.companyCode).in(criteria.getLstCompanyCode()),
                                cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL),
                                // Phân quyền theo nhóm
                                root.get(EvaluationCriteria_.id).in(criteria.getLstGroupPermissionId())
                        )
                ));
            }

//            if(ValidationUtils.isAcceptSearchListIn(criteria.getListReviewItem())){
//                predicates.add(cb.and(root.get(EvaluationCriteria_.REVIEW_ITEM).in(criteria.getListReviewItem())));
//            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListName())){
                predicates.add(cb.and(root.get(EvaluationCriteria_.NAME).in(criteria.getListName())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCreatedUser())){
                predicates.add(cb.and(root.get(EvaluationCriteria_.CREATED_USER).in(criteria.getListCreatedUser())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListUpdatedUser())){
                predicates.add(cb.and(root.get(EvaluationCriteria_.UPDATED_USER).in(criteria.getListUpdatedUser())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListName())){
                predicates.add(cb.and(root.get(EvaluationCriteria_.NAME).in(criteria.getListName())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyCode())){
                predicates.add(cb.and(root.get("companyCode").in(criteria.getListCompanyCode())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyName())){
                predicates.add(cb.and(root.get("companyName").in(criteria.getListCompanyName())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getLstDepartments())){
                Join<EvaluationCriteria, EvaluationDepartment> evaluationDepartment = root.join(EvaluationCriteria_.evaluationDepartments, JoinType.LEFT);
                predicates.add(evaluationDepartment.get(EvaluationDepartment_.departmentCodes).in(criteria.getLstDepartments()));
            }

            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                        }
                    }
                }
            }


            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }

    public Specification<EvaluationCriteria> filterNew(final EvaluationCriteriaDto criteria) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            Join<EvaluationCriteria, EvaluationDepartment> evaluationDepartment = root.join(EvaluationCriteria_.evaluationDepartments, JoinType.LEFT);

            if (StringUtils.isNotEmpty(criteria.getSearch())) {
                predicates.add(cb.or(cb.like(cb.lower(root.get(EvaluationCriteria_.name)), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get(EvaluationCriteria_.reviewItem)), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get(EvaluationCriteria_.description)), "%" + criteria.getSearch().trim().toLowerCase() + "%")));
            }
            if (StringUtils.isNotEmpty(criteria.getStatus())) {
                predicates.add(cb.and(cb.equal(root.get(EvaluationCriteria_.status), criteria.getStatus())));
            }
            if (!ValidationUtils.isNullOrEmpty(criteria.getLstDepartments())) {
                predicates.add(evaluationDepartment.get(EvaluationDepartment_.departmentCodes).in(criteria.getLstDepartments()));
            }
            return cb.and(predicates.toArray(Predicate[]::new));
        };
    }


    public Specification<EvaluationCriteria> filterSearch(final EvaluationCriteriaSearchDto criteria) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotEmpty(criteria.getStatus())) {
                predicates.add(cb.and(cb.equal(root.get(EvaluationCriteria_.status), criteria.getStatus())));
            }
//            if (StringUtils.isNotEmpty(criteria.getReviewCode())) {
//                predicates.add(cb.and(cb.equal(root.get(EvaluationCriteria_.reviewItem), criteria.getReviewCode())));
//            }
            List<String> lstDepartments = new ArrayList<>();
            if (StringUtils.isNotEmpty(criteria.getDepartmentId())) {
                lstDepartments = Arrays.asList(criteria.getDepartmentId());
            } else if (!ValidationUtils.isNullOrEmpty(criteria.getLstDepartments())) {
                lstDepartments = criteria.getLstDepartments();
            }
            if (!ValidationUtils.isNullOrEmpty(lstDepartments)) {
                Join<EvaluationCriteria, EvaluationDepartment> evaluationDepartment = root.join(EvaluationCriteria_.evaluationDepartments, JoinType.LEFT);
                predicates.add(evaluationDepartment.get(EvaluationDepartment_.departmentCodes).in(lstDepartments));
            }
            return cb.and(predicates.toArray(Predicate[]::new));
        };
    }
}
