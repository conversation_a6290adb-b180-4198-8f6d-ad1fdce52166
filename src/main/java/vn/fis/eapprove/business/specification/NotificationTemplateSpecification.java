package vn.fis.eapprove.business.specification;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.notification.entity.NotificationTemplate;
import vn.fis.eapprove.business.domain.notification.entity.NotificationTemplate_;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement;
import vn.fis.eapprove.business.domain.permission.entity.PermissionDataManagement_;
import vn.fis.eapprove.business.model.request.NotificationTemplateSearchRequest;
import vn.fis.spro.common.constants.CommonConstants;
import vn.fis.spro.common.constants.PermissionDataConstants;
import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.criteria.*;
import java.sql.Date;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

/**
 * Author: AnhVTN
 * Date: 05/01/2023
 */
@Component
public class NotificationTemplateSpecification {
    public Specification<NotificationTemplate> filter(final NotificationTemplateSearchRequest criteria, List<String> lstCompanyCode) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (!ValidationUtils.isNullOrEmpty(criteria.getSearch())) {
                predicates.add(cb.or(
                        cb.like(cb.lower(root.get("content")), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get("title")), "%" + criteria.getSearch().trim().toLowerCase() + "%")
                ));
            }
            if (!ValidationUtils.isNullOrEmpty(criteria.getType())) {
                predicates.add(cb.equal(cb.lower(root.get("type")), criteria.getType().trim().toLowerCase()));
            }

            if (!ValidationUtils.isNullOrEmpty(criteria.getActionCode())) {
                predicates.add(
                        cb.or(
                                cb.equal(cb.lower(root.get(NotificationTemplate_.ACTION_CODE)), criteria.getActionCode().toLowerCase()),
                                cb.equal(cb.upper(root.get(NotificationTemplate_.ACTION_CODE)), "ALL")
                        ));
            }

            // filter action code
            if (!ValidationUtils.isNullOrEmpty(criteria.getFilterActionCode())) {
                predicates.add(root.get(NotificationTemplate_.ACTION_CODE).in(criteria.getFilterActionCode()));
            }

            // filter phân quyền dữ liệu
            if (!lstCompanyCode.contains(CommonConstants.FILTER_SELECT_ALL) && !criteria.getType().equals("SYSTEM")) {
                Join<NotificationTemplate, PermissionDataManagement> permissionDataManagement = root.join(NotificationTemplate_.permissionDataManagements);
                if (!ValidationUtils.isNullOrEmpty(criteria.getLstGroupPermissionId())) {
                    // có phân quyền theo nhóm ưu tiên lấy các bản ghi trong nhóm + theo companyCode member admin (nếu có)
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.NOTIFICATION_TEMPLATE.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(criteria.getListCompanyCodeMemberAdmin()),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL),
                                    // Phân quyền theo nhóm
                                    root.get(NotificationTemplate_.id).in(criteria.getLstGroupPermissionId())
                            )
                    ));
                } else {
                    predicates.add(cb.and(
                            cb.equal(permissionDataManagement.get(PermissionDataManagement_.typeName), PermissionDataConstants.Type.NOTIFICATION_TEMPLATE.code),
                            cb.or(
                                    permissionDataManagement.get(PermissionDataManagement_.companyCode).in(lstCompanyCode),
                                    cb.equal(permissionDataManagement.get(PermissionDataManagement_.companyCode), CommonConstants.FILTER_SELECT_ALL)
                            )
                    ));
                }
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyCodeFilter())){
                predicates.add(cb.and(root.get("companyCode").in(criteria.getListCompanyCodeFilter())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListCompanyName())){
                predicates.add(cb.and(root.get("companyName").in(criteria.getListCompanyName())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListUserCreate())){
                predicates.add(cb.and(root.get(NotificationTemplate_.USER_CREATE).in(criteria.getListUserCreate())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListUserUpdate())){
                predicates.add(cb.and(root.get(NotificationTemplate_.USER_UPDATE).in(criteria.getListUserUpdate())));
            }
            if(ValidationUtils.isAcceptSearchListIn(criteria.getListTitle())){
                predicates.add(cb.and(root.get(NotificationTemplate_.TITLE).in(criteria.getListTitle())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListAction())){
                predicates.add(cb.and(root.get(NotificationTemplate_.ACTION_CODE).in(criteria.getActionCode())));
            }

            if(ValidationUtils.isAcceptSearchListIn(criteria.getListContent())){
                predicates.add(cb.and(root.get(NotificationTemplate_.CONTENT).in(criteria.getListContent())));
            }

            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.between(root.get(DateDto.getType()), Date.from(Instant.from(fromDateLocal)), Date.from(Instant.from(toDateLocal))));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                                predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), Date.from(Instant.from(toDateLocal))));
                        } catch (Exception e) {
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                                predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), Date.from(Instant.from(fromDateLocal))));
                        } catch (Exception e) {
                        }
                    }
                }
            }
            query.distinct(true);
            return cb.and(predicates.stream().toArray(Predicate[]::new));
        };
    }
}
