package vn.fis.eapprove.business.specification;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import vn.fis.eapprove.business.domain.template.entity.TemplateHistory;
import vn.fis.eapprove.business.domain.template.entity.TemplateHistory_;
import vn.fis.eapprove.business.domain.template.entity.TemplateManage;
import vn.fis.eapprove.business.domain.template.entity.TemplateManage_;
import vn.fis.eapprove.business.model.request.FilterTemplateHistoryRequest;
import vn.fis.eapprove.business.model.response.TemplateHistoryResponse;

import vn.fis.spro.common.model.request.DateFilterDto;
import vn.fis.spro.common.util.ValidationUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component

public class TemplateHistorySpecification {
    @Autowired
    private ApplicationContext appContext;
    @Autowired
    private EntityManagerFactory entityManagerFactory;


    public Map<String, Object> getAllHistory(final FilterTemplateHistoryRequest criteria) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Map<String, Object> mapFinal = new HashMap<>();

            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<TemplateHistoryResponse> query = cb.createQuery(TemplateHistoryResponse.class);
            CriteriaQuery<Long> queryCount = cb.createQuery(Long.class);

            Root<TemplateHistory> root = query.from(TemplateHistory.class);

            Predicate pred = getPredMAllTemplate(criteria, cb, root, query);

            //----------------------------COUNT PRED---------------------//
            Root<TemplateHistory> rootCount = queryCount.from(TemplateHistory.class);

            Predicate predCount = getPredMAllTemplate(criteria, cb, rootCount, queryCount);
            if (!criteria.getSortBy().equalsIgnoreCase("bpmProcdefDtos")) {
                if (criteria.getSortType().equalsIgnoreCase("asc")) {
                    switch (criteria.getSortBy()) {
                        case "id":
                            query.orderBy(cb.asc(root.get("id")));
                            break;
                        case "templateName":
                            query.orderBy(cb.asc(root.get("templateName")));
                            break;
                        case "urlName":
                            query.orderBy(cb.asc(root.get("urlName")));
                            break;
                        case "statusHistory":
                            query.orderBy(cb.asc(root.get("statusHistory")));
                            break;
                        case "contentEdit":
                            query.orderBy(cb.asc(root.get("contentEdit")));
                            break;
                        case "createdDate":
                            query.orderBy(cb.asc(root.get("createdDate")));
                            break;
                        case "createdUser":
                            query.orderBy(cb.asc(root.get("createdUser")));
                            break;
                        default:
                            query.orderBy(cb.asc(root.get(criteria.getSortBy())));
                            break;
                    }
                } else {
                    switch (criteria.getSortBy()) {
                        case "id":
                            query.orderBy(cb.desc(root.get("id")));
                            break;
                        case "templateName":
                            query.orderBy(cb.desc(root.get("templateName")));
                            break;
                        case "urlName":
                            query.orderBy(cb.desc(root.get("urlName")));
                            break;
                        case "statusHistory":
                            query.orderBy(cb.desc(root.get("statusHistory")));
                            break;
                        case "contentEdit":
                            query.orderBy(cb.desc(root.get("contentEdit")));
                            break;
                        case "createdDate":
                            query.orderBy(cb.desc(root.get("createdDate")));
                            break;
                        case "createdUser":
                            query.orderBy(cb.desc(root.get("createdUser")));
                            break;
                        default:
                            query.orderBy(cb.desc(root.get(criteria.getSortBy())));
                            break;
                    }
                }
            }
            query.multiselect(
                    root.get("id"),
                    root.get("templateId"),
                    root.get("version"),
                    root.get("statusHistory"),
                    root.get("contentEdit"),
                    root.get("urlName"),
                    root.get("templateName"),
                    root.get("template"),
                    root.get("createdDate"),
                    root.get("createdUser")
            ).where(pred);
            query.distinct(true);
            List<TemplateHistoryResponse> listResult = em.createQuery(query) != null ? em.createQuery(query).
                    setFirstResult((criteria.getPage() - 1) * criteria.getLimit())
                    .setMaxResults(criteria.getLimit()).getResultList() : new ArrayList<>();
            queryCount.select(cb.countDistinct(rootCount)).where(predCount);
            Long count = em.createQuery(queryCount).getSingleResult();
            mapFinal.put("count", count);
            mapFinal.put("data", listResult);
            return mapFinal;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;

        } finally {
            if (em != null)
                em.close();
        }
    }

    public Predicate getPredMAllTemplate(FilterTemplateHistoryRequest criteria, CriteriaBuilder cb,
                                         Root<TemplateHistory> root, CriteriaQuery<?> query) {
        try {
            List<Predicate> predicates = new ArrayList<>();
            Root<TemplateManage> rootTemplate = query.from(TemplateManage.class);
            predicates.add(cb.equal(root.get(TemplateHistory_.templateId), rootTemplate.get(TemplateManage_.id)));
            if (criteria.getListDateFilter() != null && !criteria.getListDateFilter().isEmpty()) {
                for (DateFilterDto DateDto : criteria.getListDateFilter()) {
                    if (DateDto.getFromDate() != null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.between(root.get(DateDto.getType()), fromDateLocal, toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() == null && DateDto.getToDate() != null) {
                        try {
                            LocalDateTime toDateLocal = LocalDateTime.ofInstant(DateDto.getToDate().toInstant(), ZoneId.systemDefault());
                            toDateLocal = toDateLocal.plusDays(1);
                            predicates.add(cb.lessThanOrEqualTo(root.get(DateDto.getType()), toDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    } else if (DateDto.getFromDate() != null && DateDto.getToDate() == null) {
                        try {
                            LocalDateTime fromDateLocal = LocalDateTime.ofInstant(DateDto.getFromDate().toInstant(), ZoneId.systemDefault());
                            predicates.add(cb.greaterThanOrEqualTo(root.get(DateDto.getType()), fromDateLocal));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }
            if (!ValidationUtils.isNullOrEmpty(criteria.getCreatedUser())) {
                predicates.add(root.get("createdUser").in(criteria.getCreatedUser()));
            }
            if (!ValidationUtils.isNullOrEmpty(criteria.getListVersion())) {
                predicates.add(root.get(TemplateHistory_.version).in(criteria.getListVersion()));
            }
            if (!ValidationUtils.isNullOrEmpty(criteria.getTemplateId())) {
                predicates.add(cb.equal(root.get(TemplateHistory_.templateId), criteria.getTemplateId()));
            }
            if (!ValidationUtils.isNullOrEmpty(criteria.getSearch())) {
                Predicate predicate = cb.or(cb.like(cb.lower(root.get(TemplateHistory_.version)), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get(TemplateHistory_.createdUser)), "%" + criteria.getSearch().trim().toLowerCase() + "%"),
                        cb.like(cb.lower(root.get(TemplateHistory_.CONTENT_EDIT)), "%" + criteria.getSearch().trim().toLowerCase() + "%")
                );
                predicates.add(predicate);
            }
            return cb.and(predicates.toArray(Predicate[]::new));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }
}
