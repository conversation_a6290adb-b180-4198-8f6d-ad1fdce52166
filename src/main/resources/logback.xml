<?xml version="1.0" encoding="UTF-8"?>
<configuration>

	<!-- Send debug messages to System.out -->
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<!-- By default, encoders are assigned the type ch.qos.logback.classic.encoder.PatternLayoutEncoder -->
		<encoder>
			<pattern>%(%d{yyyy-MM-dd HH:mm:ss.SSS a}) %highlight(%-5level) %yellow([%t]) %green(%logger{36}.%M{0}) %yellow(%X{request_id}): %msg%n%throwable</pattern>
		</encoder>
	</appender>

	<logger name="org.springframework" level="INFO" additivity="false">
		<appender-ref ref="STDOUT" />
	</logger>
	<logger name="org.hibernate" level="INFO" additivity="false">
		<appender-ref ref="STDOUT" />
	</logger>
	<logger name="org.springframework.security" level="INFO" additivity="false">
		<appender-ref ref="STDOUT" />
	</logger>
	<logger name="org.apache" level="WARN" additivity="false">
		<appender-ref ref="STDOUT" />
	</logger>
	<logger name="org.jboss" level="INFO" additivity="false">
		<appender-ref ref="STDOUT" />
	</logger>
	<logger name="vn.fis.eapprove" level="DEBUG" additivity="false">
		<appender-ref ref="STDOUT" />
	</logger>
<!--	<logger name="org.hibernate.type" level="TRACE" additivity="false">-->
<!--		<appender-ref ref="STDOUT" />-->
<!--	</logger>-->

	<!-- By default, the level of the root level is set to DEBUG -->
	<root level="ERROR">
		<appender-ref ref="STDOUT" />
	</root>
</configuration>