SET SESSION FOREIGN_KEY_CHECKS=0;

/* Drop Tables */
DROP TABLE IF EXISTS tenant_datasource;
DROP TABLE IF EXISTS task_action;

/* Create Tables */
CREATE TABLE IF NOT EXISTS tenant_datasource
(
	id bigint NOT NULL AUTO_INCREMENT,
	tenant_code varchar(50) NOT NULL,
	db_name varchar(50) NOT NULL,
	url varchar(255) NOT NULL,
	username varchar(50) NOT NULL,
	password varchar(50) NOT NULL,
	driver_class_name varchar(200) NOT NULL,
	status varchar(50),
	created_by varchar(50),
	created_date datetime,
	modified_by varchar(50),
	modified_date datetime,
	PRIMARY KEY (id),
	UNIQUE (tenant_code),
	UNIQUE (db_name)
);

/* Create Indexes */
CREATE INDEX tenant_datasource_idx ON tenant_datasource (id ASC, tenant_code ASC, db_name ASC);

CREATE TABLE `task_action`
(
    `id`           Bigint                                     NOT NULL AUTO_INCREMENT,
    `code`         varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
    `name`         varchar(250) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `task_type`    varchar(50) COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT 'Loại task: START_EVENT, TASK, END_EVENT',
    `status`       tinyint                                 NOT NULL DEFAULT '1' COMMENT 'Trạng thái: 0 - Inactive, 1 - Active',
    `description`  varchar(500) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `created_time` datetime                                         DEFAULT CURRENT_TIMESTAMP,
    `created_user` varchar(250) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `updated_time` datetime                                         DEFAULT NULL,
    `updated_user` varchar(250) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY            `idx_task_action_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Danh mục các hành động của task';