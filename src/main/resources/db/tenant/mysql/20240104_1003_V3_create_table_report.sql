DROP TABLE IF EXISTS `report_by_group`;
DROP TABLE IF EXISTS `report_by_chart_node`;


CREATE TABLE `report_by_group`
(
    `id`                     bigint NOT NULL AUTO_INCREMENT,
    `service_id`             bigint                                                        DEFAULT NULL,
    `service_name`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `submission_type`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `master_parent_id`       bigint                                                        DEFAULT NULL,
    `proc_inst_id`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `title`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `request_code`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `priority_id`            bigint                                                        DEFAULT NULL,
    `priority_name`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `proc_inst_status`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `sla_finish`             double                                                        DEFAULT NULL,
    `started_time`           datetime                                                      DEFAULT NULL,
    `finished_time`          datetime                                                      DEFAULT NULL,
    `created_user`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `location_id`            bigint                                                        DEFAULT NULL,
    `created_time`           datetime                                                      DEFAULT NULL,
    `created_user_full_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `chart_node_name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `chart_node_code`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `chart_node_id`          bigint                                                        DEFAULT NULL,
    `chart_short_name`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `chart_id`               bigint                                                        DEFAULT NULL,
    `user_title_name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `direct_manager`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `staff_code`             varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `user_status`            varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `is_expire`              tinyint(1)                                                    DEFAULT NULL,
    `manager_level`          varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `email`                  varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `ticket_id`              bigint                                                        DEFAULT NULL,
    `proc_def_id`            varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `sla_finish_time`        datetime                                                      DEFAULT NULL,
    `bp_id`                  bigint                                                        DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 798149
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `report_by_chart_node`
(
    `id`                            bigint NOT NULL AUTO_INCREMENT,
    `task_id`                       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `proc_inst_id`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `task_type`                     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `task_status`                   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `service_id`                    bigint                                                        DEFAULT NULL,
    `service_name`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `submission_type`               bigint                                                        DEFAULT NULL,
    `master_parent_id`              bigint                                                        DEFAULT NULL,
    `request_code`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `title`                         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `priority_id`                   bigint                                                        DEFAULT NULL,
    `priority_name`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `created_user`                  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `created_user_status`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `created_user_full_name`        varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `created_user_chart_id`         bigint                                                        DEFAULT NULL,
    `created_user_chart_short_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `created_user_chart_node_id`    bigint                                                        DEFAULT NULL,
    `created_user_chart_node_name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `created_user_chart_node_code`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `created_user_title_name`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `created_user_staff_code`       varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `created_user_manager_level`    varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `created_user_email`            varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `created_user_direct_manager`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `assignee`                      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `assignee_status`               varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `assignee_full_name`            varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `assignee_chart_id`             bigint                                                        DEFAULT NULL,
    `assignee_chart_short_name`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `assignee_chart_node_id`        varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `assignee_chart_node_name`      varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `assignee_chart_node_code`      varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `assignee_title_name`           varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `assignee_staff_code`           varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `assignee_manager_level`        varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `assignee_email`                varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `assignee_direct_manager`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `location_id`                   bigint                                                        DEFAULT NULL,
    `created_time`                  datetime                                                      DEFAULT NULL,
    `sla_finish_time`               datetime                                                      DEFAULT NULL,
    `finished_time`                 datetime                                                      DEFAULT NULL,
    `is_expire`                     tinyint(1)                                                    DEFAULT NULL,
    `started_time`                  datetime                                                      DEFAULT NULL,
    `task_name`                     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `cancel_reason`                 varchar(500) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `ticket_id`                     bigint                                                        DEFAULT NULL,
    `proc_def_id`                   varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1035694
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

