alter table service_package modify column service_name varchar(100);
DELIMITER //
CREATE FUNCTION ConvertToVietnameseWithoutAccents(_input VARCHAR(255)) RETURNS VARCHAR(255) DETERMINISTIC
BEGIN
    DECLARE _wAccents VARCHAR(255);
    DECLARE _woAccents VARCHAR(255);
    DECLARE _result VARCHAR(255);
    DECLARE i INT;

    SET _wAccents = 'áàảãạâấầẩẫậăắằẳẵặđéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵÁÀẢÃẠÂẤẦẨẪẬĂẮẰẲẴẶĐÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴ';
    SET _woAccents = 'aaaaaaaaaaaaaaaaadeeeeeeeeeeeiiiiiooooooooooooooooouuuuuuuuuuuyyyyyAAAAAAAAAAAAAAAAADEEEEEEEEEEEIIIIIOOOOOOOOOOOOOOOOOUUUUUUUUUUUYYYYY';
    SET _result = _input;

    SET i = 1;
    WHILE i <= LENGTH(_wAccents) DO
            SET _result = REPLACE(_result, SUBSTRING(_wAccents, i, 1), SUBSTRING(_woAccents, i, 1));
            SET i = i + 1;
        END WHILE;

    RETURN _result;
END //
DELIMITER ;
