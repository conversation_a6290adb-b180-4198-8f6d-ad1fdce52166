CREATE TABLE `bpm_procinst_recall`
(
    `id`              bigint                                 NOT NULL AUTO_INCREMENT,
    `bpm_procinst_id` bigint                                 NOT NULL,
    `proc_inst_id`    varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
    `recall_user`     varchar(255) COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `recall_time`     datetime                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `reason`          varchar(2000) COLLATE utf8mb4_unicode_ci        DEFAULT NULL COMMENT 'Lý do',
    `attach_file`     varchar(4000) COLLATE utf8mb4_unicode_ci        DEFAULT NULL COMMENT 'File cách nhau bởi dấu ","',
    PRIMARY KEY (`id`),
    KEY               `bpm_procinst_recall_idx1` (`bpm_procinst_id`) /*!80000 INVISIBLE */,
    KEY               `bpm_procinst_recall_idx2` (`proc_inst_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

create table bpm_procdef_view_file_api
(
    id                bigint auto_increment constraint bpm_procdef_view_file_api_pk primary key (id),
    url               varchar(1000)                           DEFAULT NULL,
    body              varchar(1000)                           DEFAULT NULL,
    button_name       varchar(250) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    method            varchar(100)                            DEFAULT NULL,
    task_def_key      varchar(100)                            DEFAULT NULL,
    status            bit                                     DEFAULT NULL,
    created_time      datetime                                DEFAULT NULL,
    updated_time      datetime                                DEFAULT NULL,
    created_user      varchar(250)                            DEFAULT NULL,
    updated_user      varchar(250)                            DEFAULT NULL,
    bpm_procdef_id    bigint                                  DEFAULT NULL,
    proc_def_id       varchar(100)                            DEFAULT NULL,
    response          varchar(250)                            DEFAULT NULL,
    base_url          varchar(250)                            DEFAULT NULL,
    display_condition varchar(250)                            DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Lưu cấu hình api để view File ở các bước';

CREATE TABLE `change_assignee_history`
(
    `id`                 bigint   NOT NULL AUTO_INCREMENT,
    `bpm_procinst_id`    bigint   NOT NULL,
    `proc_inst_id`       varchar(64) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `bpm_task_id`        bigint                                  DEFAULT NULL,
    `task_id`            varchar(64) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `task_def_key`       varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `change_time`        datetime NOT NULL                       DEFAULT CURRENT_TIMESTAMP,
    `org_assignee`       varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Assignee ban đầu',
    `from_assignee`      varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `to_assignee`        varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `org_assignee_title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Chức danh Assignee ban đầu',
    `type`               tinyint                                 DEFAULT '0' COMMENT '0: ủy quyền trong phiếu, 1: ủy quyền tự động',
    `assign_ticket_id`   bigint                                  DEFAULT NULL
        PRIMARY KEY (`id`),
    KEY                  `change_assignee_history_idx1` (`bpm_procinst_id`) /*!80000 INVISIBLE */,
    KEY                  `change_assignee_history_idx2` (`proc_inst_id`) /*!80000 INVISIBLE */,
    KEY                  `change_assignee_history_idx3` (`bpm_task_id`) /*!80000 INVISIBLE */,
    KEY                  `change_assignee_history_idx4` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Lịch sử thay đổi người thực hiện';

CREATE TABLE `thread_management`
(
    `id`             varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'ID của tiến trình',
    `name`           varchar(250) COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT 'Tên tiến trình',
    `schedule_type`  varchar(50) COLLATE utf8mb4_unicode_ci           DEFAULT 'CRON' COMMENT 'Loại lịch hẹn giờ: CRON, INTERVAL (ISO8601), DELAY (ms)',
    `schedule_value` varchar(100) COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT 'Giá trị thời gian đặt lịch',
    `class_name`     varchar(250) COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT 'Tên class Java, VD: vn.fis.jobrunr.MyService',
    `auto_start`     tinyint                                          DEFAULT '1' COMMENT 'Tự động start tiến trình: 0: Không, 1: Có',
    `params`         varchar(4000) COLLATE utf8mb4_unicode_ci         DEFAULT NULL COMMENT 'Tham số cấu hình tiến trình (JSON)',
    `status`         tinyint                                 NOT NULL DEFAULT '1' COMMENT 'Trạng thái: 0: Không hoạt động, 1: Hoạt động',
    `sched_lock`     tinyint                                          DEFAULT '0' COMMENT 'Scheduler lock: Đảm bảo chỉ thực thi một lời gọi duy nhất tại một thời điểm (0: Không dùng lock, 1: Có dùng lock)',
    `lock_at_least`  varchar(50) COLLATE utf8mb4_unicode_ci           DEFAULT 'PT5S' COMMENT 'Thời gian sched_lock tối thiểu mà node giữ trong bao lâu (cấu hình theo chuẩn ISO 8601)',
    `lock_at_most`   varchar(50) COLLATE utf8mb4_unicode_ci           DEFAULT 'PT15M' COMMENT 'Thời gian sched_lock tối đa mà node giữ trong bao lâu (cấu hình theo chuẩn ISO 8601). \nThời gian này thường sẽ phải lớn hơn thời gian xử lý công việc của thread hiện tại.',
    `description`    varchar(500) COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT 'Mô tả',
    `created_time`   datetime                                         DEFAULT CURRENT_TIMESTAMP,
    `created_user`   varchar(250) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `updated_time`   datetime                                         DEFAULT NULL,
    `updated_user`   varchar(250) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `ticket_auto_log`
(
    `id`           bigint NOT NULL AUTO_INCREMENT,
    `ticket_id`    bigint NOT NULL,
    `type`         varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Loại công việc, VD: auto-cancel, auto-close',
    `process_time` datetime                                DEFAULT NULL,
    `state`        varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Trạng thái: SUCCEEDED, FAILED',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `bpm_procinst_link`
(
    `bpm_procinst_id`      bigint NOT NULL COMMENT 'ID phiếu được liên kết',
    `bpm_procinst_link_id` bigint NOT NULL COMMENT 'ID phiếu liên kết',
    PRIMARY KEY (`bpm_procinst_id`, `bpm_procinst_link_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Thông tin phiếu liên kết';

CREATE TABLE `api_management`
(
    `id`                int     NOT NULL AUTO_INCREMENT,
    `name`              varchar(255) COLLATE utf8mb4_unicode_ci                        DEFAULT NULL,
    `url`               varchar(2000) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `method`            varchar(100) COLLATE utf8mb4_unicode_ci                        DEFAULT NULL COMMENT 'POST, GET, DELETE, PUT...',
    `header`            varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Thông tin header (JSON)',
    `body`              text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'Body template (JSON)',
    `response`          text COLLATE utf8mb4_unicode_ci COMMENT 'Cấu hình map response nhận về (JSON)',
    `return_response`   tinyint                                                        DEFAULT '0' COMMENT 'Có phản hồi response về cho client không?',
    `type`              varchar(50) COLLATE utf8mb4_unicode_ci                         DEFAULT 'NORMAL' COMMENT 'Loại API: Bình thường - NORMAL, Xác thực - AUTHEN, Base url - BASE_URL',
    `token_attribute`   varchar(100) COLLATE utf8mb4_unicode_ci                        DEFAULT NULL COMMENT 'Chỉ cấu hình với loại API là AUTHEN để xác định thuộc tính token cần lấy trong response. Dùng biểu thức MVEL (http://mvel.documentnode.com/) VD: data.token',
    `error_attribute`   varchar(100) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT 'Trường message nhận được từ response',
    `success_condition` varchar(500) COLLATE utf8mb4_bin                               DEFAULT NULL,
    `continue_on_error` tinyint                                                        DEFAULT '1' COMMENT 'Tiếp tục xử lý nếu gặp lỗi (0: Dừng, 1: Đi tiếp)',
    `status`            tinyint NOT NULL                                               DEFAULT '1' COMMENT 'Trạng thái: 0 - Inactive, 1 - Active',
    `authen_api_id`     int                                                            DEFAULT NULL COMMENT 'ID của API xác thực',
    `base_url_id`       int                                                            DEFAULT NULL COMMENT 'ID của BASE_URL',
    `created_time`      datetime                                                       DEFAULT CURRENT_TIMESTAMP,
    `created_user`      varchar(250) COLLATE utf8mb4_unicode_ci                        DEFAULT NULL,
    `updated_time`      datetime                                                       DEFAULT NULL,
    `updated_user`      varchar(250) COLLATE utf8mb4_unicode_ci                        DEFAULT NULL,
    `share_with`        varchar(1000) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `description`       varchar(500) COLLATE utf8mb4_unicode_ci                        DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Danh sách API';

CREATE TABLE `api_log`
(
    `id`                 bigint NOT NULL AUTO_INCREMENT,
    `url`                varchar(2000) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `method`             varchar(100) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `header`             json                                     DEFAULT NULL,
    `request_body`       json                                     DEFAULT NULL,
    `request_time`       datetime                                 DEFAULT CURRENT_TIMESTAMP,
    `response_time`      datetime                                 DEFAULT NULL,
    `response_status`    varchar(255) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `response_data`      json                                     DEFAULT NULL,
    `bpm_procdef_api_id` bigint                                   DEFAULT NULL,
    `bpm_procinst_id`    bigint                                   DEFAULT NULL,
    `api_type`           varchar(255) COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT 'Loại api: NORMAL, TASK_ACTION',
    `retry_time`         datetime                                 DEFAULT NULL,
    `retry_count`        int                                      DEFAULT '0',
    PRIMARY KEY (`id`),
    KEY                  `idx_api_log_request_time` (`request_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Lịch sử gọi API';

CREATE TABLE `bpm_procdef_api`
(
    `id`                bigint                                  NOT NULL AUTO_INCREMENT,
    `bpm_procdef_id`    bigint                                  NOT NULL,
    `proc_def_id`       varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'ID quy trình từ Camunda',
    `task_def_key`      varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Mã task trong quy trình từ Camunda',
    `action_id`         int                                                            DEFAULT NULL,
    `api_id`            int                                                            DEFAULT NULL,
    `header`            varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Nếu có cấu hình riêng tại đây thì sẽ lấy theo cấu hình này chứ không mặc định lấy từ bảng api_management',
    `body`              text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'Nếu có cấu hình riêng tại đây thì sẽ lấy theo cấu hình này chứ không mặc định lấy từ bảng api_management',
    `response`          text COLLATE utf8mb4_unicode_ci COMMENT 'Nếu có cấu hình riêng tại đây thì sẽ lấy theo cấu hình này chứ không mặc định lấy từ bảng api_management',
    `status`            bit(1)                                  NOT NULL               DEFAULT b'1' COMMENT 'Trạng thái: 0 - Inactive, 1 - Active',
    `call_order`        varchar(30) COLLATE utf8mb4_unicode_ci                         DEFAULT 'BEFORE' COMMENT 'Thứ tự gọi trước/sau khi thực hiện nghiệp vụ của action: BEFORE, AFTER',
    `success_condition` varchar(500) COLLATE utf8mb4_unicode_ci                        DEFAULT NULL COMMENT 'Điều kiện kiểm tra call API là thành công, dùng biểu thức MVEL (http://mvel.documentnode.com/) VD: code == ''1''. Nếu không cấu hình thì mặc định coi mã HttpStatus = 200 là thành công.',
    `continue_on_error` tinyint                                                        DEFAULT NULL COMMENT 'Nếu có cấu hình riêng tại đây thì sẽ lấy theo cấu hình này chứ không mặc định lấy từ bảng api_management',
    `created_time`      datetime                                                       DEFAULT CURRENT_TIMESTAMP,
    `created_user`      varchar(250) COLLATE utf8mb4_unicode_ci                        DEFAULT NULL,
    `updated_time`      datetime                                                       DEFAULT NULL,
    `updated_user`      varchar(250) COLLATE utf8mb4_unicode_ci                        DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY                 `bpm_procdef_api_idx01` (`proc_def_id`) /*!80000 INVISIBLE */,
    KEY                 `bpm_procdef_api_idx02` (`task_def_key`) /*!80000 INVISIBLE */,
    KEY                 `bpm_procdef_api_idx03` (`api_id`),
    KEY                 `bpm_procdef_api_idx04` (`bpm_procdef_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Danh sách cấu hình API cho quy trình';

create table `bpm_procdef_inherits`
(
    `id`                    int                                               NOT NULL AUTO_INCREMENT,
    primary key (`id`),
    `bpm_procdef_id`        bigint                                            not null,
    `proc_def_id`           varchar(100) COLLATE utf8mb4_unicode_ci           not null comment 'ID quy trình từ Camunda',
    `task_def_key`          varchar(255) COLLATE utf8mb4_unicode_ci           not null comment 'Mã task trong quy trình từ Camunda',
    `task_def_key_inherits` varchar(255) COLLATE utf8mb4_unicode_ci           not null comment 'Mã task kế thừa trong quy trình từ Camunda',
    `field_inherits`        varchar(4000) COLLATE utf8mb4_unicode_ci null comment 'Nếu có cấu hình riêng tại đây thì sẽ lấy theo cấu hình này',
    `status`                varchar(2) COLLATE utf8mb4_unicode_ci default '1' not null comment 'Trạng thái: 0 - Inactive, 1 - Active',
    `created_time`          datetime                              default CURRENT_TIMESTAMP null,
    `created_user`          varchar(250) COLLATE utf8mb4_unicode_ci null,
    `updated_time`          datetime null,
    `updated_user`          varchar(250) COLLATE utf8mb4_unicode_ci null
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT= 'Danh sách cấu hình KẾ THỪA cho quy trình';

create table `bpm_procdef_notification`
(
    `id`                        int                                                 NOT NULL AUTO_INCREMENT,
    primary key (`id`),
    `bpm_procdef_id`            bigint                                              not null,
    `proc_def_id`               varchar(100) COLLATE utf8mb4_unicode_ci             not null comment 'ID quy trình từ Camunda',
    `action_code`               varchar(255) COLLATE utf8mb4_unicode_ci null comment 'Mã action',
    `notification_object`       varchar(255) COLLATE utf8mb4_unicode_ci default 'ALL' null comment 'Đối tượng nhận',
    `notification_template_id`  bigint null comment 'Template mẫu thông báo',
    `task_def_key`              varchar(255) COLLATE utf8mb4_unicode_ci null comment 'Mã task chọn người thông báo trong quy trình từ Camunda',
    `task_def_key_notification` varchar(255) COLLATE utf8mb4_unicode_ci null comment 'Mã task notification trong quy trình từ Camunda',
    `field_notification`        varchar(4000) COLLATE utf8mb4_unicode_ci comment 'Nếu có cấu hình riêng tại đây thì sẽ lấy theo cấu hình này',
    `status`                    varchar(2) COLLATE utf8mb4_unicode_ci   default '1' not null comment 'Trạng thái: 0 - Inactive, 1 - Active',
    `add_more_config`           tinyint(1) default false null,
    `off_notification`          tinyint(1) default false null,
    `created_time`              datetime                                default CURRENT_TIMESTAMP null,
    `created_user`              varchar(250) COLLATE utf8mb4_unicode_ci null,
    `updated_time`              datetime null,
    `updated_user`              varchar(250) COLLATE utf8mb4_unicode_ci null
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT= 'Danh sách cấu hình thông báo cho quy trình';

create table `bpm_procdef_notification_detail`
(
    `id`                          int    NOT NULL AUTO_INCREMENT,
    primary key (`id`),
    `bpm_procdef_notification_id` bigint not null,
    `notification_template_id`    bigint not null,
    `action_code`                 varchar(2000) COLLATE utf8mb4_unicode_ci null
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT= 'Danh mục lưu cấu hình thông báo';

create table `bpm_notify_user`
(
    `id`           int                                                 NOT NULL AUTO_INCREMENT,
    primary key (`id`),
    `ticket_id`    bigint                                              not null,
    `recipient`    varchar(255) COLLATE utf8mb4_unicode_ci             not null comment 'Người nhận',
    `address`      varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Địa chỉ email, SĐT',
    `title`        varchar(1000) COLLATE utf8mb4_unicode_ci null comment 'Tiêu đề người',
    `message`      varchar(16000) COLLATE utf8mb4_unicode_ci null comment 'Tin nhắn gửi',
    `type`         varchar(100) COLLATE utf8mb4_unicode_ci             not null comment 'Kiểu tin nhắn gửi: SMS, EMAIL,..',
    `status`       varchar(2) COLLATE utf8mb4_unicode_ci   default '1' not null comment 'Trạng thái: 0 - Inactive, 1 - Active, 2 - Sending, 3 - Done, 4 - Error',
    `created_time` datetime                                default CURRENT_TIMESTAMP null,
    `created_user` varchar(250) COLLATE utf8mb4_unicode_ci null,
    `updated_time` datetime null,
    `updated_user` varchar(250) COLLATE utf8mb4_unicode_ci null,
    `company_code` varchar(10) null,
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT= 'Danh sách user cần gửi thông báo';

create table `evaluation_criteria`
(
    `id`           int                                               NOT NULL AUTO_INCREMENT,
    primary key (`id`),
    `review_item`  varchar(2000) COLLATE utf8mb4_unicode_ci          null,
    `name`         varchar(1000) COLLATE utf8mb4_unicode_ci null,
    `description`  varchar(2000) COLLATE utf8mb4_unicode_ci null,
    `status`       varchar(2) COLLATE utf8mb4_unicode_ci default '1' not null comment 'Trạng thái: 0 - Inactive, 1 - Active',
    `created_time` datetime                              default CURRENT_TIMESTAMP null,
    `created_user` varchar(2000) COLLATE utf8mb4_unicode_ci null,
    `updated_time` datetime null,
    `updated_user` varchar(2000) COLLATE utf8mb4_unicode_ci null
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT= 'Danh mục soát xét';

create table `evaluation_department`
(
    `id`                     int NOT NULL AUTO_INCREMENT,
    primary key (`id`),
    `evaluation_criteria_id` int not null,
    `department_codes`       varchar(2000) COLLATE utf8mb4_unicode_ci null
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT= 'Danh mục lưu phòng ban soát xét';

CREATE TABLE `sequence_data`
(
    `sequence_name`      varchar(100) NOT NULL,
    `sequence_increment` int(11) unsigned NOT NULL DEFAULT 1,
    `sequence_cur_value` bigint(20) unsigned DEFAULT 1,
    `created_date`       datetime,
    `last_update`        datetime,
    PRIMARY KEY (`sequence_name`)
) ENGINE=MyISAM;

CREATE TABLE `priority_management`
(
    `id`                   int                                    NOT NULL AUTO_INCREMENT,
    `code`                 varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
    `name`                 varchar(250) COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `status`               tinyint                                NOT NULL DEFAULT '1' COMMENT 'Status: 0 - Inative, 1 - Active',
    `sla_value` double DEFAULT '1' COMMENT 'Trọng số SLA',
    `time_complete`        VARCHAR(45) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `color`                VARCHAR(45) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `description`          VARCHAR(500) COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `created_user`         varchar(100) COLLATE utf8mb4_unicode_ci,
    `created_date`         datetime,
    `modified_user`        varchar(100) COLLATE utf8mb4_unicode_ci,
    `modified_date`        datetime,
    `reminder_being_time`  int                                             DEFAULT NULL COMMENT 'Cấu hình thông báo sắp trễ hạn',
    `reminder_time`        int                                             DEFAULT NULL COMMENT 'Cấu hình thông báo trễ hạn',
    `reminder_being_type`  varchar(5)                                      DEFAULT NULL COMMENT 'Cấu hình thông báo sắp trễ hạn',
    `reminder_type`        varchar(5)                                      DEFAULT NULL COMMENT 'Cấu hình thông báo trễ hạn',
    `reminder_being_value` int                                             DEFAULT NULL COMMENT 'Cấu hình thông báo sắp trễ hạn',
    `reminder_value`       int                                             DEFAULT NULL COMMENT 'Cấu hình thông báo trễ hạn',
    `config_reminder`      tinyint                                NOT NULL DEFAULT '0' COMMENT 'Có cấu hình nhắc nhở không',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Quản lý độ ưu tiên';

CREATE TABLE `notify_user`
(
    `id`              bigint NOT NULL AUTO_INCREMENT,
    `bpm_procinst_id` bigint,
    `recipient`       varchar(250) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Người nhận thông báo',
    `created_time`    datetime                                DEFAULT CURRENT_TIMESTAMP,
    `created_user`    varchar(250) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY               `notify_user_bpm_procints_id` (`bpm_procinst_id`) /*!80000 INVISIBLE */
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Danh sách cá nhân nhận thông báo';

CREATE TABLE `notify_group`
(
    `id`              bigint NOT NULL AUTO_INCREMENT,
    `bpm_procinst_id` bigint NOT NULL,
    `group_id`        int                                     DEFAULT NULL COMMENT 'ID nhóm nhận thông báo',
    `created_time`    datetime                                DEFAULT CURRENT_TIMESTAMP COMMENT 'ID của phòng ban',
    `created_user`    varchar(250) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `chart_id`        bigint                                  DEFAULT NULL COMMENT 'Id SDTC',
    PRIMARY KEY (`id`),
    KEY               `notify_group_bpm_procinst_id` (`bpm_procinst_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Danh sách nhóm nhận thông báo';

CREATE TABLE `task_action`
(
    `id`           int                                     NOT NULL AUTO_INCREMENT,
    `code`         varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
    `name`         varchar(250) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `task_type`    varchar(50) COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT 'Loại task: START_EVENT, TASK, END_EVENT',
    `status`       tinyint                                 NOT NULL DEFAULT '1' COMMENT 'Trạng thái: 0 - Inactive, 1 - Active',
    `description`  varchar(500) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `created_time` datetime                                         DEFAULT CURRENT_TIMESTAMP,
    `created_user` varchar(250) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `updated_time` datetime                                         DEFAULT NULL,
    `updated_user` varchar(250) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY            `idx_task_action_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Danh mục các hành động của task';

CREATE TABLE `bpm_procdef`
(
    `id`                               bigint NOT NULL AUTO_INCREMENT,
    `proc_def_id`                      varchar(64) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `name`                             varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `description`                      varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `key`                              varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `deployment_id`                    varchar(64) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `resource_name`                    varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `bytes`                            longblob,
    `prioritized`                      varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `user_created`                     varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `created_date`                     datetime                                DEFAULT NULL,
    `user_updated`                     varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `updated_date`                     datetime                                DEFAULT NULL,
    `auto_closed`                      numeric(10, 1)                          DEFAULT NULL,
    `auto_cancel`                      numeric(10, 1)                          DEFAULT NULL,
    `status`                           varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `step_by_step_results_for_created` tinyint(1) DEFAULT NULL,
    `inform_to`                        tinyint(1) DEFAULT NULL,
    `location`                         tinyint(1) DEFAULT NULL,
    `update`                           tinyint(1) DEFAULT NULL,
    `request_update`                   tinyint(1) DEFAULT NULL,
    `hide_ru_tasks`                    varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `auto_inherits`                    tinyint(1) DEFAULT NULL,
    `off_notification`                 tinyint(1) DEFAULT NULL,
    `create_new_and_double`            tinyint(1) DEFAULT NULL,
    `parents_id`                       bigint                                  DEFAULT NULL,
    `service_count`                    bigint                                  DEFAULT NULL,
    `type`                             int                                     DEFAULT NULL,
    `priority_id`                      int                                     DEFAULT NULL,
    `cancel_tasks`                     varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `cancel`                           tinyint(1) DEFAULT NULL,
    `additional_request`               tinyint(1) DEFAULT NULL,
    `recall`                           tinyint(1) DEFAULT NULL,
    `hide_related_ticket`              bit(1)                                  DEFAULT NULL,
    `hide_related_ticket_value`        varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `change_implementer_value`         varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `show_info`                        bit(1)                                  DEFAULT NULL,
    `hide_info`                        bit(1)                                  DEFAULT NULL,
    `is_assistant`                     bit(1)                                  DEFAULT NULL,
    `is_edit_assistant`                bit(1)                                  DEFAULT NULL,
    `hide_info_tasks`                  varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `show_info_tasks`                  varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `is_auto_cancel`                   bit(1)                                  DEFAULT NULL,
    `authority_on_ticket`              bit(1)                                  DEFAULT NULL,
    `authority_on_ticket_value`        varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `authority_on_ticket_step`         varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `show_input_task`                  tinyint(1) DEFAULT NULL,
    `show_input_task_def_keys`         varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `hide_inherit`                     bit(1)                                  DEFAULT NULL,
    `hide_inherit_tasks`               varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY                                `idx_bpm_procdef_PROC_DEF_ID` (`proc_def_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE bpm_owner_process
(
    `ID`          BIGINT AUTO_INCREMENT,
    `PROC_DEF_ID` BIGINT,
    `ID_USER`     VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
    PRIMARY KEY (`ID`),
    FOREIGN KEY (`PROC_DEF_ID`) REFERENCES bpm_procdef (ID) ON DELETE CASCADE
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE `bpm_procinst`
(
    `id`                         bigint NOT NULL AUTO_INCREMENT,
    `service_id`                 bigint                                                        DEFAULT NULL,
    `proc_inst_id`               varchar(64) COLLATE utf8mb4_unicode_ci                        DEFAULT NULL,
    `title`                      varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `is_auto`                    bit(1)                                                        DEFAULT NULL,
    `is_assistant`               bit(1)                                                        DEFAULT NULL,
    `proc_def_key`               varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `proc_def_id`                varchar(64) COLLATE utf8mb4_unicode_ci                        DEFAULT NULL,
    `created_user`               varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL COMMENT 'Người tạo phiếu',
    `created_time`               datetime                                                      DEFAULT NULL,
    `started_time`               datetime                                                      DEFAULT NULL COMMENT 'Thời gian bắt đầu xử lý phiếu',
    `end_time`                   datetime                                                      DEFAULT NULL COMMENT 'Thời gian kết thúc phiếu',
    `duration`                   bigint                                                        DEFAULT NULL COMMENT 'Tổng thời gian xử lý PYC',
    `start_user_id`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Người bắt đầu thực hiện phiếu',
    `start_act_id`               varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `end_act_id`                 varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `delete_reason`              varchar(4000) COLLATE utf8mb4_unicode_ci                      DEFAULT NULL,
    `tenant_id`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT 'ID của tenant (hiện tại không sử dụng)',
    `status`                     varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `sla_response` double DEFAULT NULL,
    `sla_finish` double DEFAULT NULL,
    `canceled_time`              datetime                                                      DEFAULT NULL,
    `closed_time`                datetime                                                      DEFAULT NULL,
    `finish_time`                datetime                                                      DEFAULT NULL,
    `edit_time`                  datetime                                                      DEFAULT NULL,
    `rating` double DEFAULT NULL,
    `comment`                    varchar(2000) COLLATE utf8mb4_unicode_ci                      DEFAULT NULL,
    `cancel_reason`              varchar(2000) COLLATE utf8mb4_unicode_ci                      DEFAULT NULL,
    `description`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `priority`                   varchar(50) COLLATE utf8mb4_unicode_ci                        DEFAULT NULL,
    `priority_id`                bigint                                                        DEFAULT NULL COMMENT 'ID của độ ưu tiên bên bảng priority_management',
    `email_notification`         tinyint                                                       DEFAULT '1' COMMENT 'Nhận thông báo mail',
    `location_id`                bigint                                                        DEFAULT NULL COMMENT 'ID vị trí',
    `link_procinst_id`           bigint                                                        DEFAULT NULL COMMENT 'ID phiếu yêu cầu liên kết',
    `submission_type_id`         bigint                                                        DEFAULT NULL COMMENT 'ID Loại tờ trình',
    `app_code`                   varchar(100) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `request_code`               varchar(255) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `chart_id`                   bigint                                                        DEFAULT NULL,
    `ru_time`                    datetime                                                      DEFAULT NULL,
    `company_code`               varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `submission_type_name`       varchar(100) COLLATE utf8mb4_unicode_ci                       DEFAULT NULL,
    `ticket_assign`              bit(1)                                                        DEFAULT NULL COMMENT 'Lưu trạng thái xác nhận ủy quyền',
    `chart_name`                 varchar(100)                                                  DEFAULT NULL COMMENT 'Lưu tên công ty',
    `chart_node_name`            varchar(100)                                                  DEFAULT NULL COMMENT 'Lưu phòng ban',
    `chart_node_id`              bigint                                                        DEFAULT NULL COMMENT 'Lưu chart id',
    `chart_node_code`            varchar(255)                                                  DEFAULT NULL COMMENT 'Lưu mã phòng ban',
    `template_version_id`        bigint                                                        DEFAULT NULL,
    `parent_template_version_id` bigint                                                        DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `PROC_INST_ID` (`proc_inst_id`),
    KEY                          `idx_bpm_procinst_start_user_id` (`start_user_id`) /*!80000 INVISIBLE */,
    KEY                          `idx_bpm_procinst_proc_def_id` (`proc_def_id`),
    KEY                          `idx_bpm_procinst_service_id` (`service_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `bpm_task`
(
    `id`                         bigint NOT NULL AUTO_INCREMENT,
    `task_id`                    varchar(64) COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT 'ID của task (từ Camunda)',
    `execution_id`               varchar(64) COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT 'ID của execution (từ Camunda)',
    `proc_inst_id`               varchar(64) COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT 'ID của phiếu yêu cầu (process-instance-id từ Camunda)',
    `proc_def_id`                varchar(64) COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT 'ID của quy trình BPMN (process-definition-id từ Camunda)',
    `case_inst_id`               varchar(64) COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT 'ID từ Camunda (chưa rõ mục đích sử dụng)',
    `name`                       varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Tên task',
    `task_def_key`               varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Mã task theo BPMN',
    `priority`                   varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Độ ưu tiên (hiện tại không sử dụng)',
    `candidate_user`             text COLLATE utf8mb4_unicode_ci COMMENT 'Người đồng duyệt',
    `created_time`               datetime                                DEFAULT NULL COMMENT 'Thời gian tạo task',
    `started_time`               datetime                                DEFAULT NULL COMMENT 'Thời gian bắt đầu task',
    `sla_response_time`          datetime                                DEFAULT NULL COMMENT 'SLA thời gian phản hồi',
    `sla_finish_time`            datetime                                DEFAULT NULL COMMENT 'SLA thời gian kết thúc task',
    `sla_response` double DEFAULT NULL COMMENT 'SLA số giờ phản hồi',
    `sla_finish` double DEFAULT NULL COMMENT 'SLA số giờ kết thúc',
    `response_duration`          bigint                                  DEFAULT NULL COMMENT 'Tổng thời gian (giây) phản hồi tính từ lúc tạo task (created_time) tới khi bấm bắt đầu task',
    `finish_duration`            bigint                                  DEFAULT NULL COMMENT 'Tổng thời gian kết thúc (giây) tính từ lúc bắt đầu task (started_time) tới lúc kết thúc task (trạng thái COMPLETED)',
    `finished_time`              datetime                                DEFAULT NULL COMMENT 'Thời gian kết thúc task',
    `done_time` double DEFAULT NULL COMMENT 'Tổng thời gian hoàn thành (trường này hiện không hiểu lắm, hiện tại đang đặt = finished_time)',
    `type`                       varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Loại task: EXECUTION, APPROVAL',
    `created_user`               varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Người tạo task',
    `assignee`                   varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Người được giao task',
    `status`                     varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Trạng thái task: ACTIVE, PROCESSING, COMPLETED, DELETED_BY_RU, CANCEL',
    `sign_status`                bigint                                  DEFAULT NULL COMMENT 'Trạng thái trình ký (Hiện tại không dùng?)',
    `is_first`                   tinyint(1) DEFAULT NULL COMMENT 'Có phải là task đầu tiên trong luồng quy trình: 0 - False, 1 - True',
    `assign_type`                bit(1)                                  DEFAULT NULL COMMENT 'Uỷ quyền: 0 - Không uỷ quyền 1: Uỷ quyền',
    `action_user`                varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `template_version_id`        bigint                                  DEFAULT NULL,
    `parent_template_version_id` bigint                                  DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Lưu danh sách các task của phiếu yêu cầu';

CREATE TABLE `bpm_task_user`
(
    `id`           bigint                                  NOT NULL AUTO_INCREMENT,
    `bpm_task_id`  bigint                                  NOT NULL,
    `task_id`      varchar(64) COLLATE utf8mb4_unicode_ci  NOT NULL,
    `task_def_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `task_name`    varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `proc_inst_id` varchar(64) COLLATE utf8mb4_unicode_ci  NOT NULL,
    `user_name`    varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Người dùng được gán',
    PRIMARY KEY (`id`),
    KEY            `idx_bpm_task_user_bpm_task_id` (`bpm_task_id`),
    KEY            `idx_bpm_task_user_task_id` (`task_id`) /*!80000 INVISIBLE */,
    KEY            `idx_bpm_task_user_proc_inst_id` (`proc_inst_id`),
    KEY            `idx_bpm_task_user_user_name` (`user_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Danh sách người dùng được gán cho task';

CREATE TABLE `bpm_discussion`
(
    `id`                  bigint NOT NULL AUTO_INCREMENT,
    `parent_id`           bigint                                 DEFAULT NULL,
    `proc_inst_id`        varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `group_id`            bigint                                 DEFAULT NULL,
    `content`             longtext                               DEFAULT NULL,
    `created_time`        datetime                               DEFAULT NULL,
    `created_user`        varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `Long typeDiscussion` tinyint NULL,
    `is_additional_request` tinyint null
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Nội dung bình luận';

CREATE TABLE `bpm_discussion_file`
(
    `ID`            bigint PRIMARY KEY AUTO_INCREMENT,
    `DISCUSSION_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `DOWNLOAD_URL`  varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `FILES_PATH`    varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `FILES_NAME`    varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE `bpm_variables`
(
    `id`             bigint NOT NULL AUTO_INCREMENT,
    `task_id`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT 'ID của task (từ camunda)',
    `proc_inst_id`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `name`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT 'Tên biến',
    `type`           varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL COMMENT 'Kiểu dữ liệu: STRING, INTEGER, DOUBLE, LONG, JSON, FILE',
    `string_val`     varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Giá trị kiểu String',
    `double_val` double DEFAULT NULL COMMENT 'Giá trị kiểu Double',
    `long_val`       bigint                                                  DEFAULT NULL COMMENT 'Giá trị kiểu Long, Integer',
    `json_val`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin,
    `download_url`   varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT 'URL tải file',
    `files_path`     varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT 'Thư mục lưu file',
    `additional_val` text COLLATE utf8mb4_unicode_ci,
    `is_draft`       int                                                     DEFAULT NULL COMMENT 'Loại variable: 0 - Thường, 1 - Nháp',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `bpm_history`
(
    `id`                bigint NOT NULL AUTO_INCREMENT,
    `ticket_id`         bigint                                   DEFAULT NULL,
    `proc_inst_id`      varchar(64) COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `task_inst_id`      varchar(64) COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `task_def_key`      varchar(255) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `from_task_key`     varchar(64) COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `to_task_key`       varchar(64) COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `from_task`         varchar(64) COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `to_task`           varchar(64) COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `task_assignee`     varchar(64) COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `action`            varchar(64) COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `action_user`       varchar(100) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `note`              varchar(500) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `affected_task`     varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `created_time`      datetime                                 DEFAULT NULL,
    `task_type`         varchar(45) COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `old_task_inst_id`  varchar(64) COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `old_procinst_id`   varchar(64) COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `attach_files`      varchar(4000) COLLATE utf8mb4_unicode_ci DEFAULT NULL comment 'file đính kèm case hủy/ trả về',
    `old_default_field` json                                     DEFAULT NULL,
    `action_user_info`  json                                     DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY                 `idx_bpm_history_ticket_id` (`ticket_id`) /*!80000 INVISIBLE */,
    KEY                 `idx_bpm_history_procinst_id` (`proc_inst_id`) /*!80000 INVISIBLE */,
    KEY                 `idx_bpm_history_task_id` (`task_inst_id`) /*!80000 INVISIBLE */,
    KEY                 `idx_bpm_history_task_def_key` (`task_def_key`) /*!80000 INVISIBLE */,
    KEY                 `idx_bpm_history_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Lưu lịch sử PYC';


CREATE TABLE `bpm_ru_history`
(
    `ID`           bigint PRIMARY KEY AUTO_INCREMENT,
    `TASK_ID`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `PROC_INST_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `SHARED_USER`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `CREATED_USER` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `CREATED_TIME` datetime                                               DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE `bpm_shared`
(
    `ID`           bigint PRIMARY KEY AUTO_INCREMENT,
    `PROC_INST_ID` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `TASK_ID`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `TYPE`         varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `SHARED_USER`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `CREATED_USER` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `CREATED_TIME` datetime                                               DEFAULT NULL,
    `IS_DELETED`   bit(1)                                                 DEFAULT NULL,
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE `bpm_config_template_print`
(
    `ID`           bigint PRIMARY KEY AUTO_INCREMENT,
    `NAME`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `DESCR`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `TEMPLATE`     TEXT                                                  DEFAULT NULL,
    `CREATED_USER` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `CREATED_DATE` datetime                                              DEFAULT NULL,
    `UPDATED_USER` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `UPDATED_DATE` datetime                                              DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

/*Print_TYPE: Phan loai To trinh chu truong voi Mau In*/
CREATE TABLE `bpm_template_print`
(
    `ID`                  bigint PRIMARY KEY AUTO_INCREMENT,
    `IS_DELETED`          boolean                                                NOT NULL,
    `PRINT_TYPE`          int                                                    NOT NULL,
    `PROC_DEF_ID`         VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
    `NAME`                varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `PROCESS_NAME`        varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `DESCR`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL,
    `HTML_CONTENT`        TEXT                                                    DEFAULT NULL,
    `HISTORY_CHANGE`      TEXT                                                    DEFAULT NULL,
    `PDF_CONTENT`         TEXT                                                    DEFAULT NULL,
    `SHARE_WITH`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `UPLOAD_WORDS`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `UPLOAD_WORDS_CHANGE` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `CREATED_USER`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL,
    `CREATED_DATE`        datetime                                                DEFAULT NULL,
    `UPDATED_USER`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL,
    `UPDATED_DATE`        datetime                                                DEFAULT NULL,
    `PROCESS_ID`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL,
    `status`            TINYINT default 0 null comment 'Trạng thái xem đã có phiếu tạo chưa',

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

/*bpm_tp_task -> bpm_template_print_task*/
CREATE TABLE `bpm_tp_task`
(
    `ID`                    bigint PRIMARY KEY AUTO_INCREMENT,
    `BPM_TEMPLATE_PRINT_ID` bigint,
    `TASK_TYPE`             int                                                   NOT NULL,
    `NAME`                  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `PROC_DEF_ID`           VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
    `TASK_DEF_KEY`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `FORM_KEY`              varchar(100) CHARACTER SET utf8 COLLATE utf8_bin      NOT NULL,
    `STATUS`                varchar(100) CHARACTER SET utf8 COLLATE utf8_bin      NOT NULL,
    FOREIGN KEY (`BPM_TEMPLATE_PRINT_ID`) REFERENCES bpm_template_print (ID) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE `bpm_tp_sign_task`
(
    `ID`           bigint PRIMARY KEY AUTO_INCREMENT,
    `SIGN`         varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `TASK_DEF_KEY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `PROC_INST_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
    `IS_DELETED`   boolean                                         NOT NULL,
    `CREATED_USER` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `CREATED_DATE` datetime                                               DEFAULT NULL,
    `UPDATED_USER` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `UPDATED_DATE` datetime                                               DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE `bpm_tp_sign_zone`
(
    `id`                    bigint NOT NULL AUTO_INCREMENT,
    `bpm_template_print_id` bigint                                   DEFAULT NULL,
    `task_def_key`          varchar(64) COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `proc_inst_id`          varchar(64) COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `order_sign`            bigint                                   DEFAULT NULL,
    `email`                 varchar(255) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `sign_page`             bigint                                   DEFAULT NULL,
    `x`                     bigint                                   DEFAULT NULL,
    `y`                     bigint                                   DEFAULT NULL,
    `w`                     bigint                                   DEFAULT NULL,
    `h`                     bigint                                   DEFAULT NULL,
    `scale`                 float                                    DEFAULT NULL,
    `first_name`            varchar(255) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `last_name`             varchar(255) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `position`              varchar(255) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `sign`                  varchar(500) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `signed_file`           varchar(500) COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT 'File đã ghép chữ ký',
    `signed_date`           datetime                                 DEFAULT NULL,
    `comment`               varchar(2000) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `created_user`          varchar(255) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `created_date`          datetime                                 DEFAULT NULL,
    `updated_user`          varchar(255) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `updated_date`          datetime                                 DEFAULT NULL,
    `sign_type`             varchar(20) COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `org_asignee_title`     varchar(255) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY                     `BPM_TEMPLATE_PRINT_ID` (`bpm_template_print_id`),
    CONSTRAINT `bpm_tp_sign_zone_ibfk_1` FOREIGN KEY (`bpm_template_print_id`) REFERENCES `bpm_template_print` (`ID`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `bpm_tp_sign_zone_history`
(
    `ID`                    bigint PRIMARY KEY AUTO_INCREMENT,
    `BPM_TP_SIGN_ZONE_ID`   bigint,
    `BPM_TEMPLATE_PRINT_ID` bigint,
    `FIRST_NAME`            varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
    `LAST_NAME`             varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
    `POSITION`              varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
    `TASK_DEF_KEY`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `PROC_INST_ID`          varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
    `ORDER_SIGN`            bigint,
    `EMAIL`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `SIGN_PAGE`             bigint,
    `X`                     bigint,
    `Y`                     bigint,
    `W`                     bigint,
    `H`                     bigint,
    `SIGN`                  varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `CREATED_USER`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `CREATED_DATE`          datetime                                               DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE `bpm_sign`
(
    `ID`           bigint PRIMARY KEY AUTO_INCREMENT,
    `Img`          TEXT                                                  DEFAULT NULL,
    `IS_DELETED`   boolean NOT NULL,
    `IS_DEFAULT`   boolean NOT NULL,
    `CREATED_USER` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `CREATED_DATE` datetime                                              DEFAULT NULL,
    `UPDATED_USER` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `UPDATED_DATE` datetime                                              DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE `temp_ticket`
(
    `ID`        bigint PRIMARY KEY AUTO_INCREMENT,
    `TEMP_DATA` LONGTEXT DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE `service_package`
(
    `id`                  bigint NOT NULL AUTO_INCREMENT,
    `parent_id`           bigint                                   DEFAULT NULL,
    `service_name`        varchar(100) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `color`               varchar(10) COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `icon`                varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `service_type`        int                                      DEFAULT NULL,
    `process_id`          bigint                                   DEFAULT NULL,
    `url`                 varchar(500) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `description`         varchar(200) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `note`                varchar(500) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `not_showing_website` bit(1)                                   DEFAULT NULL,
    `not_showing_moblie`  bit(1)                                   DEFAULT NULL,
    `hide_name`           varchar(200) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `visible_name`        varchar(200) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `hide_group`          varchar(200) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `visible_group`       varchar(200) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `position_package`    int                                      DEFAULT NULL,
    `created_date`        datetime                                 DEFAULT NULL,
    `created_user`        varchar(100) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `modified_date`       datetime                                 DEFAULT NULL,
    `modified_user`       varchar(100) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `id_orgChart`         bigint                                   DEFAULT NULL,
    `deleted`             bit(1)                                   DEFAULT b'0',
    `master_parent_id`    bigint                                   DEFAULT NULL,
    `block_location`      varchar(200) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `status`              VARCHAR(50)                              DEFAULT 'DEACTIVE',
    `hide_all_user`       bit(0)                                   DEFAULT NULL,
    `visible_all_user`    bit(0)                                   DEFAULT NULL,
    `submission_type`     Bigint                                   DEFAULT NULL,
    `visible_chart`       varchar(200) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `hide_chart`          varchar(200) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY                   `idx_service_package_process_id` (`process_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

create table IF NOT EXISTS template_manage
(
    id
    bigint
    primary
    key
    AUTO_INCREMENT,
    template_name
    varchar
(
    200
),
    description varchar
(
    200
),
    status int,
    template LONGTEXT,
    created_date datetime,
    created_user varchar
(
    100
),
    modified_date datetime,
    modified_user varchar
(
    100
),
    url_name varchar
(
    1000
)
    );

create table IF NOT EXISTS master_data
(
    id
    bigint
    primary
    key,
    master_name
    varchar
(
    100
),
    description varchar
(
    200
),
    status varchar
(
    45
),
    sheet varchar
(
    10
),
    filter_quantity integer,
    file varchar
(
    1000
),
    protocol varchar
(
    1000
),
    data_type varchar
(
    45
),
    api_key varchar
(
    3000
),
    time_master varchar
(
    10
),
    url varchar
(
    1000
),
    header_param json,
    body_content json,
    error varchar
(
    100
),
    cycle_master varchar
(
    45
),
    email varchar
(
    45
),
    allowSystem BIT,
    status_MD BIT,
    type_MD integer,
    enter_type bit,
    body_login json,
    url_login varchar
(
    1000
),
    md_hour INTEGER,
    md_minute INTEGER,
    created_date datetime,
    created_user varchar
(
    100
),
    modified_date datetime,
    modified_user varchar
(
    100
)
    );

create table IF NOT EXISTS master_data_filter
(

    id
    bigint
    primary
    key
    AUTO_INCREMENT,
    filter_name
    varchar
(
    100
),
    description varchar
(
    500
),
    filter json,
    master_id bigint,
    created_date datetime,
    created_user varchar
(
    100
),
    modified_date datetime,
    modified_user varchar
(
    100
)
    );

create table IF NOT EXISTS master_data_import
(
    id
    bigint
    primary
    key
    AUTO_INCREMENT,
    master_data_id
    bigint,
    status
    varchar
(
    10
) DEFAULT 'active',
    # sub_key bigint,
    group_id bigint,
    key_detail varchar
(
    200
),
    value_detail varchar
(
    500
)
    # CONSTRAINT `master_data_import_UNIQUE` UNIQUE
(
    master_data_id,
    sub_key,
    key_detail,
    value_detail
)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE =utf8mb4_unicode_ci COMMENT='Master Data Import';

#
create table IF NOT EXISTS master_data_template
    #
(
    #
    id
    #
    bigint
    #
    primary
    #
    key
    #
    AUTO_INCREMENT,
    #
    type_data
    #
    integer,
    #
    select_data
    #
    TEXT,
    #
    type_display
    #
    varchar
(
    1000
),
    # condition_MD TEXT,
    # add_conditon_MD varchar
(
    1000
),
    # save_condition bit,
    # name_condition varchar
(
    200
),
    # description varchar
(
    500
),
    # created_date datetime,
    # created_user varchar
(
    100
),
    # modified_date datetime,
    # modified_user varchar
(
    100
)
    # );

create table IF NOT EXISTS manage_API
(
    id
    bigint
    primary
    key
    AUTO_INCREMENT,
    nameAPI
    varchar
(
    100
),
    url varchar
(
    100
),
    ticket_phase integer,
    description varchar
(
    1000
),
    ticketId bigint,
    ticket_name varchar
(
    100
),
    status integer,
    created_date DATE,
    created_user varchar
(
    100
),
    modified_date DATE,
    modified_user varchar
(
    100
)
    );

create table if not exists task_setting_config
(
    id
    bigint
    primary
    key
    AUTO_INCREMENT,
    expired_task_value_normal
    int
    default
    5,
    expired_task_value_warning
    int
    default
    6,
    expired_task_value_danger
    int
    default
    10,
    expired_hour_value_normal
    int
    default
    3,
    expired_hour_value_warning
    int
    default
    4,
    expired_hour_value_danger
    int
    default
    5,
    email
    varchar
(
    50
) not null unique
    );

create table if not exists mr_filter
(
    id
    bigint
    primary
    key
    auto_increment,
    name
    varchar
(
    100
),
    description varchar
(
    500
),
    type int,
    group_share json,
    user_share json,
    department json,
    service json,
    priority json,
    groupp json,
    gshare json,
    ushare json,
    user json,
    filter_time int not null,
    from_date datetime,
    to_date datetime,
    chart_id bigint,
    created_user varchar
(
    100
),
    created_date datetime,
    modified_user varchar
(
    100
),
    modified_date datetime
    );

CREATE TABLE IF NOT EXISTS group_table_procTemp
(
    id
    BIGINT
    PRIMARY
    KEY
    AUTO_INCREMENT,
    pro_def_id
    VARCHAR
(
    1000
),
    form_key VARCHAR
(
    1000
)
    );

CREATE TABLE `location_management`
(
    id                bigint AUTO_INCREMENT COMMENT 'Ma vi tri',
    abbreviations     varchar(10) COMMENT 'Ten viet tat',
    location_name     varchar(100) COMMENT 'Ten vi tri',
--     chart_name varchar(100) COMMENT'Ten SDTC',
--     chart_id int(10) COMMENT'Id sdtc',
    address           varchar(200) COMMENT 'Dia chi',
    working_time_code varchar(100) COMMENT 'ma lich lam viec',
--     file_import varchar(300) COMMENT'import excel',
    working_time_name varchar(100) COMMENT 'ten ma lich lam viec',
    status            varchar(100),
    created_user      varchar(100),
    created_date      datetime,
    modified_user     varchar(100),
    modified_date     datetime,
    company_code      varchar(45),
    company_name      varchar(100),
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Quan li vi tri';

CREATE TABLE IF NOT EXISTS payment_schedule
(
    id
    BIGINT
    AUTO_INCREMENT
    COMMENT
    'Ma lich thanh toan',

    contract_number
    varchar
(
    100
) COMMENT 'so hop dong',

    contract_value BIGINT COMMENT 'gia tri hop dong',

    paid_percent varchar
(
    45
) COMMENT 'phan tram da thanh toan',

    procinst_id BIGINT COMMENT 'ma phieu yeu cau',

    paid_total BIGINT COMMENT 'tong gia tri da thanh toan',

    create_date datetime COMMENT 'ngay tao',

    create_user varchar
(
    45
) COMMENT 'nguoi tao',
    PRIMARY KEY
(
    id
)

    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE =utf8mb4_unicode_ci COMMENT='Danh sách quản lí lịch thanh toán';

CREATE TABLE `submission_type`
(
    `id`                bigint AUTO_INCREMENT NOT NULL,
    `type_name`         varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
    `department_create` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL,
    `scope_apply`       tinyint COMMENT 'Status: 0 - Trong 1 pháp nhân , 1 - Liên công ty',
    `company_code`      VARCHAR(20) COLLATE utf8mb4_unicode_ci,
    `description`       VARCHAR(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `share_with`        VARCHAR(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `created_user`      varchar(100) COLLATE utf8mb4_unicode_ci,
    `created_date`      datetime,
    `modified_user`     varchar(100) COLLATE utf8mb4_unicode_ci,
    `modified_date`     datetime,
    `status`            tinyint,

    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Quản lý loại tờ trình';

CREATE TABLE `assign_management`
(
    id                  bigint AUTO_INCREMENT NOT NULL COMMENT 'Mã ủy quyền',
    assign_name         varchar(500) COMMENT 'Ten ủy quyền',
    assign_user         varchar(500) COMMENT 'Người ủy quyền',
    assigned_user       varchar(500) COMMENT 'Người được ủy quyền',
    ticket_id           bigint COMMENT 'Mã phiếu yêu cầu',
    start_date          DATE COMMENT 'Ngày bắt đầu',
    end_date            DATE COMMENT 'Ngày kết thúc',
    service_range       varchar(500) COMMENT 'Phạm vi dịch vụ ủy quyền',
    status              tinyint COMMENT 'Trạng thái: 0- Thêm mới, 1- Gia hạn, 2- Hủy, -1- Hết hiệu lực do gia hạn/ hủy ',
    effect              tinyint COMMENT 'Hiệu lực ủy quyền: 0- Trong thời gian ủy quyền, 1- Đến hoàn thành',
    created_date        DATETIME COMMENT 'Ngày tạo',
    created_user        varchar(100) COMMENT 'Người tạo',
    updated_date        DATETIME,
    updated_user        varchar(100),
    description         varchar(500),
    file_name           varchar(200),
    new_ticket          varchar(50),
    type                tinyint COMMENT '0 : Tờ trình ủy quyền,1 :Tờ trình nghiệp vụ + ủy quyền',
    assign_title        varchar(500) COMMENT 'mã chức danh của người ủy quyền',
    assign_duty         varchar(500) COMMENT 'Nghĩa vụ của người ủy quyền',
    assign_company_name varchar(500) COMMENT 'Tên công ty',
    assign_decision     varchar(500) COMMENT 'Căn cứ quyết định của người ủy quyền',
    assigned_title      varchar(500) COMMENT 'mã chức danh của người được ủy quyền',
    assigned_storage    varchar(500) COMMENT 'Phòng nhận bản lưu ủy quyền',
    request_code        varchar(255) COMMENT 'Mã tờ trình',
    new_request_code    varchar(50) COMMENT 'Tờ trình liên quan',
    history_status      tinyint COMMENT 'Trạng thái tờ trình tác động của lịch sử ủy quyền'
        PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Quản lý ủy quyền';

CREATE TABLE `assign_history`
(
    id           bigint AUTO_INCREMENT NOT NULL COMMENT 'Mã lịch sử ủy quyền',
    assign_id    varchar(50) NOT NULL COMMENT 'Mã ủy quyền',
    version      varchar(50) COMMENT 'Phiên bản',
    ticket_id    bigint COMMENT 'Mã phiếu yêu cầu',
    assign_time  varchar(50) COMMENT 'Khoảng thời gian ủy quyền',
    assign_user  varchar(100) COMMENT 'Người được ủy quyền',
    created_date DATETIME COMMENT 'Ngày tạo',
    created_user varchar(100) COMMENT 'Người tạo',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Lịch sử ủy quyền';

create table bpm_additional_request
(
    id              bigint auto_increment
        primary key,
    proc_inst_id    varchar(64) null comment 'ID của phiếu yêu cầu (process-instance-id từ Camunda)',
    start_user_id   varchar(50) null comment 'Người nhận',
    content_request varchar(200) null comment 'Nội dung yêu cầu',
    auto_return     int null comment 'Tự động trả về',
    is_auto         bit null comment 'Xác định có trả về hay không. 1: true, 0: false',
    reminder_date   datetime null comment 'Nhắc nhở lúc',
    expired_date    datetime null comment 'Thời gian hết hạn',
    created_date    datetime null,
    created_user    varchar(50) null,
    modified_date   datetime null,
    modified_user   varchar(50) null
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci comment 'Yêu cầu bổ sung';

CREATE TABLE `condition_file`
(
    `ID`                    bigint                                           NOT NULL AUTO_INCREMENT,
    `BPM_TEMPLATE_PRINT_ID` bigint                                           NOT NULL,
    `UPLOAD_WORDS`          varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `UPLOAD_WORDS_CHANGE`   varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `TASK_TYPE`             int                                              NOT NULL,
    `STEP_NAME`             varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `TASK_DEF_KEY`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL,
    `FORM_KEY`              varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
    `CONDITION`             varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `bpm_template_print_config`
(
    `ID`          bigint                                           NOT NULL AUTO_INCREMENT,
    `TYPE`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `JSON_CONFIG` varchar(500) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

create table template_history
(
    id             bigint auto_increment primary key,
    created_date   datetime null,
    created_user   varchar(50) COLLATE utf8mb4_unicode_ci null,
    template_id    bigint,
    version        varchar(10) COLLATE utf8mb4_unicode_ci null,
    status_history bit,
    content_edit   varchar(50) COLLATE utf8mb4_unicode_ci null,
    url_name       varchar(100) COLLATE utf8mb4_unicode_ci null,
    template_name  varchar(100) COLLATE utf8mb4_unicode_ci null,
    template       LONGTEXT,
    description    varchar(200) null,
    share_with     text null,
    apply_for      text null,
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci comment 'Lịch sử biểu mẫu';

create table bpm_template_task
(
    id           bigint auto_increment primary key,
    Task_id      bigint null,
    Template_id  bigint COLLATE utf8mb4_unicode_ci null,
    created_date datetime COLLATE utf8mb4_unicode_ci null
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

create table shared_user
(
    id             bigint auto_increment primary key,
    reference_id   bigint      not null comment 'là các id liên quan đến shared-user',
    reference_type varchar(50) not null comment 'là các module có sử dụng chức năng shared-user đó',
    email          TEXT(
) null comment 'là các email đc share',
    addition       json         not null comment ' nếu sau này có thêm các trường khác thì có thể add vào đó theo dạng json để còn query được'
) comment 'chức năng chia sẻ với';

create table notification_template
(
    id                  int auto_increment comment 'Mã template'
        primary key,
    type                varchar(50) collate utf8mb4_unicode_ci                not null comment 'Loại thông báo',
    title               varchar(1000) collate utf8mb4_unicode_ci              not null comment 'Tên template',
    action_code         varchar(255) collate utf8mb4_unicode_ci               not null comment 'Action của mẫu thông báo',
    notification_object varchar(255) collate utf8mb4_unicode_ci default 'ALL' not null comment 'Đối tượng gửi',
    source_type         varchar(50) collate utf8mb4_unicode_ci null comment 'Nguồn gửi từ đâu',
    content             mediumtext collate utf8mb4_unicode_ci                 not null comment 'Nội dung mẫu cho template',
    create_at           datetime null comment 'Thời gian tạo',
    update_at           datetime null comment 'Thời gian chỉnh sửa',
    user_create         varchar(255) collate utf8mb4_unicode_ci null,
    type_noti           varchar(45) collate utf8mb4_unicode_ci                not null comment 'Loại thông báo'
        user_update varchar (255) collate utf8mb4_unicode_ci null,
) comment 'Cấu hinh thông báo';

create table notification_template_detail
(
    id                       bigint auto_increment
        primary key,
    notification_template_id int not null,
    title                    varchar(1000) null,
    content                  longtext null,
    source_type              varchar(50) null,
    type                     varchar(10) null,
    create_at                datetime null,
    update_at                datetime null,
    user_create              varchar(50) null,
    user_update              varchar(50) null,
    file_icon                varchar(4000) COLLATE utf8mb4_unicode_ci DEFAULT NULL comment 'icon thông báo web',
    constraint template_detail_notification_template_id_fk
        foreign key (notification_template_id) references notification_template (id)
            on delete cascade
) comment 'Chi tiết mẫu thông báo' collate = utf8mb4_unicode_ci;

create table notification
(
    id          bigint auto_increment
        primary key,
    email       varchar(255) collate utf8mb4_unicode_ci not null comment 'Email người dùng',
    content     mediumtext collate utf8mb4_unicode_ci null comment 'Nội dung thông báo',
    source_type varchar(50) collate utf8mb4_unicode_ci null comment 'Loại liên kết thông báo. EX: MOBILE_FIREBASE',
    type        varchar(50) null comment 'Loại thông báo.EX: UPDATE_TICKET, CONFIRM ...',
    status      tinyint null comment 'Trạng thái thông báo. \n0 - Chưa đọc\n1 - Đã đọc\n2 - Đã xoá',
    create_at   datetime null comment 'Thời gian tạo',
    update_at   datetime null comment 'Thời gian cập nhật'
) comment 'Lưu trữ thông báo người dùng';

create table assistant
(
    id              bigint auto_increment
        primary key,
    ticketId        varchar(50) collate utf8mb4_unicode_ci not null comment 'Mã ticket',
    assistant_email varchar(100) collate utf8mb4_unicode_ci null comment 'Email của trợ lý',
    create_at       datetime null comment 'Thời gian tạo',
    update_at       datetime null comment 'Thời gian cập nhật'
) comment 'Lưu trữ thông tin trợ lý theo mã ticket';

create table assistant_opinion
(
    id              bigint auto_increment
        primary key,
    ticket_id       bigint not null comment 'Mã ticket',
    assistant_email varchar(100) collate utf8mb4_unicode_ci null comment 'Email của trợ lý',
    opinion         mediumtext collate utf8mb4_unicode_ci null comment 'Ý kiến trợ lý',
    file_url        mediumtext collate utf8mb4_unicode_ci null comment 'Đường dẫn file upload',
    file_name       varchar(500) null comment 'Tên file upload',
    status          int null comment 'Trạng thái ý kiến:
 0: là nháp
 1: đã thực hiện
 ',
    create_at       datetime null comment 'Thời gian tạo',
    update_at       datetime null comment 'Thời gian cập nhật'
) comment 'Lưu trữ thông tin ý kiến trợ lý theo email trợ lý';


create table authority_management
(
    id           bigint null comment 'Mã ủy quyền',
    ticket_id    bigint null comment 'mã ticket',
    from_account varchar(500) null comment 'Người nhượng',
    to_account   varchar(500) null comment 'người được nhượng',
    reason       mediumtext null comment 'lý do nhượng',
    type         varchar(50) null comment 'Kiểu nhượng',
    create_at    datetime null,
    update_at    int null,
    user_create  varchar(500) null,
    user_update  varchar(500) null
) comment 'Uỷ quyền';

create table code_gen_config
(
    id            bigint auto_increment
        primary key,
    code          varchar(100) null,
    structor_code varchar(2000) null,
    description   varchar(500) null comment 'mô tả',
    create_at     datetime null,
    update_at     datetime null,
    user_create   varchar(100) null,
    status        varchar(10) default 'deactive',
    user_update   varchar(100) null,
) comment 'Cấu hình sinh mã';

create table code_gen_struct
(
    id                 bigint auto_increment
        primary key,
    code_gen_config_id bigint null comment 'Mã cẫu hình',
    data_type          varchar(50) null comment 'Kiểu dữ liệu',
    structor_code      varchar(2000) null comment 'Cấu trúc mã',
    value              varchar(100) null comment 'Giá trị',
    format             varchar(100) null,
    length             int null,
    current_value      varchar(50) null,
    schedule           int null comment 'Chu kỳ
0: Không reset
1: Ngaỳ
2: Tuần
3: Tháng
4: Quý
5: Nam
',
    reset_time         datetime null,
    sort_order         int null comment 'Thứ tự',
    default_value      varchar(1000) null comment 'giá trị mặc định',
    constraint code_gen_config_id_fk
        foreign key (code_gen_config_id) references ctshippertest0001gmailcom13122022.code_gen_config (id)
            on update cascade on delete cascade
) comment 'cấu trúc sinh mã';

create table currency
(
    id             bigint auto_increment
        primary key,
    name           varchar(50) null comment 'Tên tiền tệ',
    code           varchar(50) null comment 'Mã tiền tệ',
    another_name   varchar(100) null comment 'Tên khác',
    rounding_rules int null comment 'quy tắc làm tròn',
    description    varchar(500) null comment 'Mô tả',
    create_at      datetime null,
    update_at      datetime null,
    user_create    varchar(100) null
) comment 'Danh mục tiền tệ';

CREATE TABLE `permission_data_management`
(
    `id`           bigint NOT NULL AUTO_INCREMENT,
    `type_id`      bigint NOT NULL COMMENT 'ID danh mục',
    `type_name`    varchar(100)                            DEFAULT NULL COMMENT 'Loại danh mục',
    `company_code` varchar(100)                            DEFAULT NULL COMMENT 'Áp dụng cho',
    `created_user` varchar(250) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `created_time` datetime                                DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY            `permission_management_type_name` (`type_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Quản lý phân quyền dữ liệu';

create table system_group
(
    id            bigint auto_increment
        primary key,
    name          varchar(200) null,
    username      varchar(200) null,
    fromDate      datetime     null,
    toDate        datetime     null,
    group_type    varchar(50)  null comment 'Loại nào : Biểu mẫu, Quy trình , ...',
    group_field   varchar(50)  null comment '//Trường nào lưu vào db để query theo type .
Ví dụ : type = biểu mẫu. group_field = id =>
query biểu mẫu theo id',
    group_value   varchar(200) null comment 'Lưu value để query theo type và field',
    status        tinyint(1)   null,
    company_name  varchar(200) null,
    company_code  varchar(200) null,
    parent        bigint       null,
    created_user  varchar(100) null,
    modified_user varchar(100) null,
    created_date  datetime     null,
    modified_date datetime     null,
    description   varchar(500) null
)
    comment 'Phân nhóm';

create table system_group_history
(
    id           bigint auto_increment
        primary key,
    created_date datetime     null,
    created_user varchar(50)  null,
    description  longtext     null,
    group_type   varchar(200) null,
    parent       bigint       null,
    version      int          null
);


/* INSERT DATA */
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (1, 'CREATE_TICKET', 'Tạo phiếu', 'START_EVENT', 1, NULL, '2022-10-16 18:50:22', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (2, 'UPDATE_TICKET', 'Cập nhật', 'START_EVENT', 1, NULL, '2022-10-16 18:50:22', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (3, 'CANCEL_TICKET', 'Hủy phiếu', 'START_EVENT', 1, NULL, '2022-10-16 18:50:22', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (4, 'START_TASK', 'Bắt đầu task', 'TASK', 1, NULL, '2022-10-16 18:51:49', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (5, 'DO_TASK', 'Thực hiện/Phê duyệt', 'TASK', 1, NULL, '2022-10-16 18:54:30', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (6, 'REQUEST_UPDATE', 'Trả về', 'TASK', 1, NULL, '2022-10-16 18:54:30', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (7, 'ADDITIONAL_REQUEST', 'Yêu cầu bổ sung', 'TASK', 1, NULL, '2022-10-16 18:54:30', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (8, 'CHANGE_EXECUTOR', 'Đổi người thực hiện', 'TASK', 1, NULL, '2022-10-16 18:54:30', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (9, 'RATING', 'Đánh giá', 'END_EVENT', 1, NULL, '2022-10-16 18:54:30', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (10, 'ADD_INFO', 'Bổ sung thông tin', 'START_EVENT', 1, NULL, '2023-01-31 10:39:59', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (11, 'REDO', 'Thực hiện lại', 'START_EVENT', 1, NULL, '2023-01-31 15:55:10', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (12, 'CREATE_DRAFT_TICKET', 'Tạo phiếu nháp', 'START_EVENT', 1, NULL, '2023-02-14 17:22:43', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (13, 'UPDATE_DRAFT_TICKET', 'Cập nhật nháp', 'START_EVENT', 1, NULL, '2023-02-14 17:22:43', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (14, 'DELETE_DRAFT_TICKET', 'Xoá phiếu nháp', 'START_EVENT', 1, NULL, '2023-02-16 08:56:38', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (15, 'COMPLETE', 'Hoàn thành phiếu', 'START_EVENT', 1, NULL, '2023-03-01 22:12:21', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (16, 'DRAFT_TO_OPEN', 'Chuyển trạng thái ticket từ nháp sang phiếu thật', 'START_EVENT', 1, NULL,
        '2023-03-01 22:20:59', NULL, NULL, NULL);
INSERT INTO `task_action` (`id`, `code`, `name`, `task_type`, `status`, `description`, `created_time`, `created_user`,
                           `updated_time`, `updated_user`)
VALUES (17, 'REQUEST_UPDATE_TO_START', 'Trả về bước Start', 'START_EVENT', 1, NULL, '2023-04-04 21:41:16', NULL, NULL,
        NULL);

CREATE TABLE `manage_share_ticket` (
                                       id BIGINT AUTO_INCREMENT NOT NULL,
                                       name VARCHAR(500),
                                       description VARCHAR(5000),
                                       from_date DATE COMMENT 'Ngày bắt đầu',
                                       to_date DATE COMMENT 'Ngày kết thúc',
                                       status VARCHAR(10) COMMENT 'Trạng thái: active/deactive ',
                                       created_date DATETIME COMMENT 'Ngày tạo',
                                       created_user VARCHAR(100) COMMENT 'Người tạo',
                                       updated_date DATETIME,
                                       updated_user VARCHAR(100),
                                       company_code VARCHAR(100),
                                       company_name VARCHAR(200),
                                       PRIMARY KEY (id)
)  ENGINE=INNODB DEFAULT CHARSET=UTF8MB4 COLLATE = UTF8MB4_UNICODE_CI COMMENT='Quản lý quyền chia sẻ pyc';

CREATE TABLE `manage_share_ticket_detail` (
                                              id BIGINT AUTO_INCREMENT NOT NULL,
                                              manage_share_ticket_id BIGINT,
                                              type VARCHAR(100) COMMENT 'service: Dịch vụ được chia sẻ, companyCode: công ty người đệ trình, chartNodeCode: phòng ban người đệ trình, createdUser: người đệ trình, assignee: người duyệt, shareUser: người được chia sẻ',
                                              value VARCHAR(100),
                                              PRIMARY KEY (id)
)  ENGINE=INNODB DEFAULT CHARSET=UTF8MB4 COLLATE = UTF8MB4_UNICODE_CI;
