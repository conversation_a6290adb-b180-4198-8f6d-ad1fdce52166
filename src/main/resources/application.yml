########## HTTP encoding Configuration ##########
server:
  port: 8083
  servlet:
    context-path: /business
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  shutdown: graceful
  tomcat:
    max-http-form-post-size: 100MB
    max-swallow-size: 100MB

########## Spring Configuration ##########
spring:
  application:
    name: business-process-service
  main:
    allow-bean-definition-overriding: true
    web-application-type: SERVLET
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  datasource:
    username: admin
    password: I72GHw82%
    url: *************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      pool-name: auth-service-connection-pool
      maximum-pool-size: 300
      minimum-idle: 10
      connection-timeout: 60000
      idle-timeout: 300000
      max-lifetime: 900000
      connection-test-query: SELECT 1
  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        show_sql: true
        format_sql: true
        jdbc:
          batch_size: 30
        hbm2ddl:
          auto: none
        dialect: org.hibernate.dialect.MySQL8Dialect
    open-in-view: false

  ########## Redis Configuration ##########
  data:
    redis:
      host: *************
      port: 6379
      database: 0 #0-dev, 1-btp
      authentication:
        enable: true
        password: I72GHw82%

  task:
    scheduling:
      pool:
        size: 10
  management:
    health:
      redis:
        enabled: false
  logging:
    level:
      io:
        lettuce: info
        netty: info
  kafka:
    bootstrap-servers: *************:9092
    authentication:
      enable: true
      username: admin
      password: I72GHw82%
    consumer:
      auto-offset-reset: earliest
      group-id:
        group-action-api-after: group.action.api.after.dev
        group-notification-user: group.notification.user.dev
        group-share-ticket: group.share.ticket.dev
        group-auto-complete-task: group.auto.complete.task.dev
        insert-report-by-group: report-by-group.dev
        insert-report-by-chart-node: report-by-chart-node.dev
        create-sign-zone: group.create.sign.zone.dev
      topic:
        action-api-after: topic.action.api.after.dev
        insert-report-by-group: topic.report.by.group.dev
        insert-report-by-chart-node: topic.report.by.chart.node.dev
        notification-user: topic.notification.user.dev
        share-ticket: topic.share.ticket.dev
        create-sign-zone: topic.create.sign.zone.dev

########## SPRO Configuration ##########
spro:
  default-page: 0
  default-page-size: 10
  service-urls:
    auth: https://api-btp.eapprove.online/employee/auth
    customer: https://api-btp.eapprove.online/employee
#    auth: http://localhost:8081/employee/auth
#    customer: http://localhost:8081/employee
    pdf-service: https://api.eapprove.online/pdf
    bpm-service: http://localhost:8084/camunda
#    bpm-service: https://api.eapprove.online/camunda
    app-client-url: https://uat-eapp.datxanh.com.vn/sign-in
  api-timeout: 30000
  storage:
    url: https://s3-sgn09.fptcloud.com:443
    access-key: 00848ca618660fbff4fa
    secret-key: BN8axdx1Zx9Pzbp6kY/iZW1s4i0B1Bu/FFjUh1c8
    region: sgn09
  full-name-mode: VN
  kafka:
    topic-partitions: 1
    topic-replicas: 1

########## Scheduler Configuration ##########
scheduler:
  notification-push:
    delay: 1000
  api-log-task:
    delay: 1000
  reload-task:
    cron: 0 0/15 * * * ?

########## Other Configuration ##########
kafka:
  enabled: false
action-api:
  remove-additional-headers: false
  always-allow-post-process: false

######## Config Call Log Admin #########
call-log:
  account:
    username: API-USER
    password: FPT@123
  url: http://dxg-admin-api-service.admin:8610/api/master-data/master-data-log/recieve-log

###########  Configuration ##########
app:
  superAdmin:
    account: employee
    realm: employee
    username: admin
    password: password123
  sso:
    realm: employee
  s3:
    bucket: eapp-uat
md:
  sync:
    enable: true
job:
  apiLogTask:
    enable: true
sync:
  admin:
    log: false
api-after:
  call-kafka:
    enable: false

security:
  scope: microservice

auth:
  jwt:
    secretKey: 4nn6jMVMK4+TK0o32uyDOIRbkq/osZewH6ycAYnoc+8=
    tokenPrefix: Bearer
    expiresIn: 3600000
    notBefore: 300000
    refreshExpiresIn: 3600000
    refreshNotBefore: 300000

push-noti:
  url-noti-proxy: http://localhost:8087/noti-proxy/push-multiple