error.tenant.datasource.create.fail = T\u1EA1o tenant datasource kh\u00F4ng th\u00E0nh c\u00F4ng
message.tenant.datasource.create.success = T\u1EA1o tenant datasource th\u00E0nh c\u00F4ng
message.masterdata.create.success = T\u1EA1o master data th\u00E0nh c\u00F4ng
error.masterdata.create.fail = T\u1EA1o master data th\u1EA5t b\u1EA1i
error.masterdata.create.exist = \u0110\u00E3 t\u1ED3n t\u1EA1i m\u00E3 master data
message.masterdata.filter.success = Filter master data th\u00E0nh c\u00F4ng
error.masterdata.filter.fail= Filter master data th\u1EA5t b\u1EA1i
message.masterdata.getall.success=Th\u00E0nh c\u00F4ng
message.masterdata.delete.success=Xo\u00E1 th\u00E0nh c\u00F4ng
error.masterdata.delete.fail = Xo\u00E1 th\u1EA5t b\u1EA1i
error.masterdata.check.fail = File master data \u0111ang \u00E1p d\u1EE5ng bi\u1EC3u m\u1EABu, kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p xo\u00E1
message.masterdata.update.success = C\u1EADp nh\u1EADt master data th\u00E0nh c\u00F4ng
error.masterdata.update.exist.name= T\u00EAn masterdata \u0111\u00E3 t\u1ED3n t\u1EA1i. C\u1EADp nh\u1EADt kh\u00F4ng th\u00E0nh c\u00F4ng.
error.masterdata.update.exist_id.name= Id \u0111\u00E3 t\u1ED3n t\u1EA1i. C\u1EADp nh\u1EADt kh\u00F4ng th\u00E0nh c\u00F4ng.
error.masterdata.update.fail = C\u1EADp nh\u1EADt master data th\u1EA5t b\u1EA1i
message.masterdata.import.start = Ti\u1EBFn tr\u00ECnh import cho Master Data {0} \u0111ang b\u1EAFt \u0111\u1EA7u. K\u1EBFt qu\u1EA3 s\u1EBD \u0111\u01B0\u1EE3c c\u1EADp nh\u1EADt sau
error.masterdata.import.fail = Import th\u1EA5t b\u1EA1i
message.masterdata.show.success = Show th\u00E0nh c\u00F4ng
error.masterdata.show.fail = Show th\u1EA5t b\u1EA1i
message.masterdata.checkname.fail = Tr\u00F9ng t\u00EAn master data vui l\u00F2ng nh\u1EADp t\u00EAn kh\u00E1c
message.masterdata.checkIdExist.fail = ID \u0111\u00E3 t\u1ED3n t\u1EA1i trong h\u1EC7 th\u1ED1ng
message.masterdata.checkname.success = T\u00EAn h\u1EE3p l\u1EC7
error.masterdatafilter.checkname.fail = Tr\u00F9ng t\u00EAn b\u1ED9 l\u1ECDc vui l\u00F2ng nh\u1EADp t\u00EAn kh\u00E1c
message.masterdatafilter.checkname.success = T\u00EAn h\u1EE3p l\u1EC7
message.masterdatafilter.create.success = T\u1EA1o b\u1ED9 l\u1ECDc th\u00E0nh c\u00F4ng
error.masterdatafilter.create.fail = T\u1EA1o b\u1ED9 l\u1ECDc th\u1EA5t b\u1EA1i
message.masterdatafilter.delete.success = Xo\u00E1 b\u1ED9 l\u1ECDc th\u00E0nh c\u00F4ng
error.masterdatafilter.delete.fail = B\u1ED9 l\u1ECDc \u0111ang \u00E1p d\u1EE5ng bi\u1EC3u m\u1EABu, kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p xo\u00E1
message.masterdatafilter.getall.success= Th\u00E0nh c\u00F4ng
error.masterdatafilter.getall.fail= Th\u00E0nh c\u00F4ng
message.masterdatafilter.getfilter.success= Th\u00E0nh c\u00F4ng
error.masterdatafilter.getfilter.fail=Th\u1EA5t b\u1EA1i
message.masterdatafilter.update.success = C\u1EADp nh\u1EADt b\u1ED9 l\u1ECDc th\u00E0nh c\u00F4ng
error.masterdatafilter.update.fail = C\u1EADp nh\u1EADt b\u1ED9 l\u1ECDc th\u1EA5t b\u1EA1i
message.masterdata.sync.disable = Disable
internal_server_error = L\u1ED7i server
error.templatePrint.valid.dataValid = kh\u00F4ng th\u1EC3 t\u00ECm th\u1EA5y th\u00F4ng tin
message.template.create.saveSuccess = l\u01B0u th\u00E0nh c\u00F4ng
error.templatePrint.valid.fileUploadNotEmpty = kh\u00F4ng t\u00ECm th\u1EA5y file
error.templatePrint.valid.fileCheck = file t\u1EA3i l\u00EAn kh\u00F4ng ph\u1EA3i .docx,doc
message.template.delete.stillUse = kh\u00F4ng th\u1EC3 x\u00F3a m\u1EABu tr\u00ECnh n\u00E0y, phi\u1EBFu y\u00EAu c\u1EA7u \u0111ang s\u1EED d\u1EE5ng m\u1EABu tr\u00ECnh k\u00FD n\u00E0y
error.templatePrint.valid.nameUpdateDuplicate = t\u00EAn \u0111\u00E3 t\u1ED3n t\u1EA1i
message.template.checkName = t\u00EAn ok
message.template.delete.success = Xo\u00E1 bi\u1EC3u m\u1EABu th\u00E0nh c\u00F4ng
error.template.delete.checkProdef = Bi\u1EC3u m\u1EABu \u0111\u00E3 g\u1EAFn v\u00E0o quy tr\u00ECnh, b\u1EA1n kh\u00F4ng th\u1EC3 x\u00F3a
error.template.delete.fail = Xo\u00E1 bi\u1EC3u m\u1EABu th\u1EA5t b\u1EA1i
message.template.create.success = T\u1EA1o bi\u1EC3u m\u1EABu th\u00E0nh c\u00F4ng
error.template.create.checkname = T\u00EAn bi\u1EC3u m\u1EABu \u0111\u00E3 t\u1ED3n t\u1EA1i trong h\u1EC7 th\u1ED1ng
error.template.create.fail = T\u1EA1o bi\u1EC3u m\u1EABu th\u1EA5t b\u1EA1i
message.template.contentFileFail = n\u1ED9i dung file ch\u01B0a \u0111\u00FAng
message.bpmProcdefApi.delete.success = X\u00F3a th\u00E0nh c\u00F4ng
error.bpmProcdefApi.delete.fail = X\u00F3a th\u1EA5t b\u1EA1i
message.template.history.success = kh\u00F4i ph\u1EE5c th\u00E0nh c\u00F4ng !
message.template.history.fail = kh\u00F4i ph\u1EE5c th\u1EA5t b\u1EA1i !
message.template.update.name = Thay \u0111\u1ED5i t\u00EAn t\u00EAn bi\u1EC3u m\u1EABu.
message.template.update.url = Thay \u0111\u1ED5i m\u00E3 s\u1EED d\u1EE5ng.
message.template.update.description = Thay \u0111\u1ED5i n\u1ED9i dung m\u00F4 t\u1EA3.
message.template.update.template = Thay \u0111\u1ED5i n\u1ED9i dung bi\u1EC3u m\u1EABu.
message.template.update.shareUser = Thay \u0111\u1ED5i chia s\u1EBB ng\u01B0\u1EDDi d\u00F9ng.

message.priority-management.updatePriority.success = Ch\u1EC9nh s\u1EEDa \u0111\u1ED9 \u01B0u ti\u00EAn th\u00E0nh c\u00F4ng
message.priority-management.updatePriority.fail = Ch\u1EC9nh s\u1EEDa \u0111\u1ED9 \u01B0u ti\u00EAn th\u1EA5t b\u1EA1i
message.priority-management.createPriority.failName = \u0110\u1ED9 \u01B0u ti\u00EAn \u0111\u00E3 t\u1ED3n t\u1EA1i trong h\u1EC7 th\u1ED1ng
message.priority-management.createPriority.success = Th\u00EAm m\u1EDBi \u0111\u1ED9 \u01B0u ti\u00EAn th\u00E0nh c\u00F4ng
message.priority-management.createPriority.fail = Th\u00EAm m\u1EDBi \u0111\u1ED9 \u01B0u ti\u00EAn th\u1EA5t b\u1EA1i
message.priority-management.deletePriority.success = X\u00F3a \u0111\u1ED9 \u01B0u ti\u00EAn th\u00E0nh c\u00F4ng
message.priority-management.deletePriority.fail = \u0110\u1ED9 \u01B0u ti\u00EAn \u0111ang \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng trong quy tr\u00ECnh/phi\u1EBFu y\u00EAu c\u1EA7u
message.priority-management.searchTemplate.success = Th\u00E0nh c\u00F4ng
message.priority-management.searchTemplate.fail = Kh\u00F4ng c\u00F3 k\u1EBFt qu\u1EA3 tra c\u1EE9u ph\u00F9 h\u1EE3p
message.priority-management.activePriority.fail = V\u00F4 hi\u00EA\u0323u ho\u0301a th\u00E2\u0301t ba\u0323i
message.priority-management.activePriority.success = V\u00F4 hi\u00EA\u0323u ho\u0301a tha\u0300nh c\u00F4ng
message.priority-management.activePriority.active = Ki\u0301ch hoa\u0323t tha\u0300nh c\u00F4ng

message.PaymentSchedule.createPaymentSchedule.success = T\u1EA1o l\u1ECBch thanh to\u00E1n th\u00E0nh c\u00F4ng
message.PaymentSchedule.createPaymentSchedule.fail = T\u1EA1o l\u1ECBch thanh to\u00E1n th\u1EA5t b\u1EA1i

message.api-management.createApiAction.success = Th\u00E0nh c\u00F4ng
message.api-management.createApiAction.fail = Th\u1EA5t b\u1EA1i
message.api-management.updateApiAction.success = Ch\u1EC9nh s\u1EEDa th\u00E0nh c\u00F4ng
message.api-management.updateApiAction.fail = Ch\u1EC9nh s\u1EEDa th\u1EA5t b\u1EA1i
message.api-management.searchApiAction.success = Th\u00E0nh c\u00F4ng
message.api-management.searchApiAction.fail = Kh\u00F4ng c\u00F3 k\u1EBFt qu\u1EA3 tra c\u1EE9u ph\u00F9 h\u1EE3p
message.api-management.cloneApiAction.success = Th\u00E0nh c\u00F4ng
message.api-management.cloneApiAction.fail = T\u00EAn v\u01B0\u01A1\u0323t ky\u0301 t\u01B0\u0323 cho phe\u0301p, Nh\u00E2n ba\u0309n kh\u00F4ng tha\u0300nh c\u00F4ng
message.api-management.deleteApiAction.success = X\u00F3a th\u00E0nh c\u00F4ng
message.api-management.deleteApiAction.fail = API \u0111ang \u00E1p d\u1EE5ng trong bi\u1EC3u m\u1EABu, kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p x\u00F3a

message.searchApiLog.searchApiAction.success = Th\u00E0nh c\u00F4ng
message.searchApiLog.searchApiAction.fail = Kh\u00F4ng c\u00F3 k\u1EBFt qu\u1EA3 tra c\u1EE9u ph\u00F9 h\u1EE3p

message.submission_type.searchSubmissionType.success = Th\u00E0nh c\u00F4ng
message.submission_type.searchSubmissionType.fail = Kh\u00F4ng c\u00F3 k\u1EBFt qu\u1EA3 tra c\u1EE9u ph\u00F9 h\u1EE3p
message.submission_type.createSubmissionType.success = Th\u00EAm m\u1EDBi lo\u1EA1i t\u1EDD tr\u00ECnh th\u00E0nh c\u00F4ng
message.submission_type.createSubmissionType.fail = Th\u00EAm m\u1EDBi lo\u1EA1i t\u1EDD tr\u00ECnh th\u1EA5t b\u1ECB
message.submission_type.updateSubmissionType.success = Ch\u1EC9nh s\u1EEDa lo\u1EA1i t\u1EDD tr\u00ECnh th\u00E0nh c\u00F4ng
message.submission_type.updateSubmissionType.fail = Ch\u1EC9nh s\u1EEDa lo\u1EA1i t\u1EDD tr\u00ECnh th\u1EA5t b\u1EA1i
message.submission_type.deleteSubmissionType.success = X\u00F3a lo\u1EA1i t\u1EDD tr\u00ECnh th\u00E0nh c\u00F4ng
message.submission_type.deleteSubmissionType.fail = Lo\u1EA1i t\u1EDD tr\u00ECnh \u0111ang \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng trong qu\u1EA3n l\u00FD phi\u1EBFu y\u00EAu c\u1EA7u, h\u00E3y x\u00F3a c\u00E1c phi\u1EBFu y\u00EAu c\u1EA7u c\u00F3 s\u1EED d\u1EE5ng lo\u1EA1i t\u1EDD tr\u00ECnh tr\u01B0\u1EDBc !
message.submission_type.cloneSubmissionType.success = Nh\u00E2n b\u1EA3n lo\u1EA1i t\u1EDD tr\u00ECnh th\u00E0nh c\u00F4ng
message.submission_type.cloneSubmissionType.fail = Nh\u00E2n b\u1EA3n lo\u1EA1i t\u1EDD tr\u00ECnh th\u1EA5t b\u1EA1i
message.submission_type.getAllSubmissionType.success = Th\u00E0nh c\u00F4ng
message.submission_type.getAllSubmissionType.fail = Th\u1EA5t b\u1EA1i

error.masterdata.eap001 = "N\u1EBFu action l\u00E0 DEL th\u00EC ph\u1EA3i c\u00F3 code v\u00E0 kh\u00F4ng \u0111\u01B0\u1EE3c c\u00F3 name"
error.masterdata.eap002 = "N\u1EBFu action l\u00E0 NEW ho\u1EB7c UPD th\u00EC ph\u1EA3i c\u00F3 c\u1EA3 code v\u00E0 name"
error.masterdata.eap003 = "masterDataId l\u00E0 tr\u01B0\u1EDDng b\u1EAFt bu\u1ED9c"
error.masterdata.eap004 = "masterDataItems ph\u1EA3i c\u00F3 \u00EDt nh\u1EA5t 1 ph\u1EA7n t\u1EED"
error.masterdata.eap005 = "action kh\u00F4ng h\u1EE3p l\u1EC7"

message.location.exsitCode = m\u00E3 s\u01A1 \u0111\u1ED3 kh\u00F4ng t\u00F4n t\u1EA1i
message.location.exsitLocationName =t\u00EAn v\u1ECB tr\u00ED \u0111\u00E3 t\u1ED3n t\u1EA1i
message.location.success = T\u1EA1o v\u1ECB tr\u00ED th\u00E0nh c\u00F4ng!
message.location.fileCheck = file t\u1EA3i l\u00EAn kh\u00F4ng ph\u1EA3i .xlsx,xls
message.location.isUsedBpmProcInst = V\u1ECB tr\u00ED \u0111ang \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng b\u1EDFi c\u00E1c phi\u1EBFu y\u00EAu c\u1EA7u
message.location.deleteFail = V\u1ECB tr\u00ED \u0111ang \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng, kh\u00F4ng th\u1EC3 x\u00F3a
message.location.deleteSuccess = X\u00F3a v\u1ECB tr\u00ED th\u00E0nh c\u00F4ng !
message.location.checkName = T\u00EAn v\u1ECB tr\u00ED \u0111\u00E3 t\u1ED3n t\u1EA1i !
message.location.nameOK = T\u00EAn ok!
message.location.isNotEmpty = \u0110\u00E2y l\u00E0 tr\u01B0\u1EDDng b\u1EAFt bu\u1ED9c kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng !
message.location.notDup = T\u00EAn v\u00ED tr\u00ED kh\u00F4ng \u0111\u01B0\u1EE3c tr\u00F9ng v\u1EDBi c\u00E1c t\u00EAn v\u1ECB tr\u00ED kh\u00E1c trong excel !
message.location.totalError = T\u1ED5ng s\u1ED1 b\u1EA3n ghi l\u1ED7i
message.location.totalSuccess = v\u00E0 t\u1ED5ng s\u1ED1 b\u1EA3n ghi \u0111\u00FAng
message.location.correct = B\u1EA3n ghi h\u1EE3p l\u1EC7 nh\u01B0ng ch\u01B0a ghi v\u00E0o h\u1EC7 th\u1ED1ng
message.location.fileMess = File nh\u1EADp l\u1ED7i d\u1EEF li\u1EC7u, ch\u1EC9nh s\u1EEDa d\u1EEF li\u1EC7u file t\u1EA3i v\u1EC1

# signature
sign.postion=Ch\u1EE9c danh:
sign.date=Ng\u00E0y k\u00FD:
sign.orgAssign=TUQ

# ticket validation message
ticket.missing-title=T\u00EAn phi\u00EA\u0301u y\u00EAu c\u00E2\u0300u kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng!
ticket.missing-account=Account l\u00E0 tr\u01B0\u1EDDng b\u1EAFt bu\u1ED9c !
ticket.missing-service=Kh\u00F4ng t\u00ECm th\u1EA5y d\u1ECBch v\u1EE5 t\u01B0\u01A1ng \u1EE9ng !
ticket.maxlength-title=T\u00EAn phi\u00EA\u0301u y\u00EAu c\u00E2\u0300u kh\u00F4ng \u0111\u01B0\u1EE3c v\u1EE3t qu\u00E1 400 k\u00ED t\u1EF1!
ticket.missing-serviceId=serviceId kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng!
ticket.missing-variables=variables kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng!
ticket.error-chart=Th\u00F4ng tin account v\u00E0 ph\u00F2ng ban kh\u00F4ng h\u1EE3p l\u1EC7!
ticket.processing-cant-update=Ticket kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p update!
ticket.different-user=B\u1EA1n kh\u00F4ng c\u00F3 quy\u1EC1n thay \u0111\u1ED5i Ticket n\u00E0y!
ticket.no-permission-user=B\u1EA1n kh\u00F4ng c\u00F3 quy\u1EC1n t\u1EA1o ticket n\u00E0y!
ticket.save-version-template-false=L\u01B0u version c\u1EE7a t\u1EDD tr\u00ECnh th\u1EA5t b\u1EA1i!
ticket.deleted-ticket-draft=Phi\u1EBFu nh\u00E1p \u0111\u00E3 \u0111\u01B0\u1EE3c x\u00F3a, kh\u00F4ng th\u1EC3 \u0111\u1EC7 tr\u00ECnh!

notification.delete-error=M\u1EABu th\u00F4ng b\u00E1o \u0111\u00E3 \u0111\u01B0\u1EE3c g\u00E1n cho quy tr\u00ECnh b\u1EA1n kh\u00F4ng th\u1EC3 x\u00F3a!
notification.save-error=M\u1EABu th\u00F4ng b\u00E1o cho action {0} v\u00E0 \u0111\u1ED1i t\u01B0\u1EE3ng {1} \u0111\u00E3 t\u1ED3n t\u1EA1i!
notification.save-title-error=T\u00EAn m\u1EABu th\u00F4ng b\u00E1o {0} \u0111\u00E3 t\u1ED3n t\u1EA1i!

saveEvaluationCriteria.save-error= T\u00EAn danh m\u1EE5c so\u00E1t x\u00E9t \u0111\u00E3 t\u1ED3n t\u1EA1i!
saveEvaluationCriteria.save-not-found=Kh\u00F4ng t\u00ECm th\u1EA5y so\u00E1t x\u00E9t c\u00F3 id {0}!

# history
history.note.create-ticket=T\u1EA1o m\u1EDBi phi\u1EBFu y\u00EAu c\u1EA7u
history.note.create-task=T\u1EA1o m\u1EDBi b\u01B0\u1EDBc

#assign
message.assign.createAssign.success = Th\u00E0nh c\u00F4ng
message.assign.createAssign.fail = Th\u1EA5t b\u1EA1i
message.assign.updateAssign.success = Th\u00E0nh c\u00F4ng
message.assign.updateAssign.fail = Th\u1EA5t b\u1EA1i
message.assign.loadAssign.success = Th\u00E0nh c\u00F4ng
message.assign.loadAssign.fail = Th\u1EA5t b\u1EA1i
message.assign.deletebyid.success = Th\u00E0nh c\u00F4ng
error.assign.deletebyid.fail = Th\u1EA5t b\u1EA1i
message.assign.searchAssign.success = Th\u00E0nh c\u00F4ng
message.assign.searchAssign.fail = Th\u1EA5t b\u1EA1i
message.assign.getAllAssignHistory.success = Th\u00E0nh c\u00F4ng
message.assign.getAllAssignHistory.fail = Th\u1EA5t b\u1EA1i
message.bpmProcdef.create.success = T\u1EA1o th\u00E0nh c\u00F4ng!
message.bpmProcdef.create.fail = t\u1EA1o th\u1EA5t b\u1EA1i!
message.assign.getAllProInst.success = Th\u00E0nh c\u00F4ng
message.assign.getAllProInst.fail = Th\u1EA5t b\u1EA1i
message.bpmProInst.delete.success = x\u00F3a th\u00E0nh c\u00F4ng
message.bpmProInst.delete.fail = x\u00F3a th\u1EA5t b\u1EA1i
message.servicePackage.clone.success = Nh\u00E2n b\u1EA3n th\u00E0nh c\u00F4ng
message.servicePackage.clone.fail = Nh\u00E2n b\u1EA3n th\u1EA5t b\u1EA1i

#CodeGenConfig
message.codeGenConfig.success = Th\u00E0nh c\u00F4ng
message.codeGenConfig.codeNull = Code must not be null !!
message.codeGenConfig.idNull = Id must not be null !!
message.codeGenConfig.existsCode = Code is Exists !!
message.codeGenConfig.existsCodeAndId = Code and Id is Exists !!

### Ticket Message ###
message.ticket.not-exists=Phi\u1EBFu y\u00EAu c\u1EA7u kh\u00F4ng t\u1ED3n t\u1EA1i!
message.ticket.recall.not-allow=Kh\u00F4ng th\u1EC3 th\u1EF1c hi\u1EC7n thu h\u1ED3i phi\u1EBFu y\u00EAu c\u1EA7u!
message.ticket.error=Th\u1EA5t b\u1EA1i!
message.ticket.errorApi=Failed to call api: {0}
message.ticket.completed = Phi\u1EBFu \u0111\u00E3 ho\u00E0n th\u00E0nh, kh\u00F4ng th\u1EF1c hi\u1EC7n b\u00E0n giao
message.ticket.status-change = Tr\u1EA1ng th\u00E1i phi\u1EBFu \u0111\u00E3 c\u1EADp nh\u1EADt. T\u1EA3i l\u1EA1i trang \u0111\u1EC3 xem

### Task Message ###
message.task.additional-request.completed.not-allow=B\u01B0\u1EDBc {0} \u0111\u00E3 ho\u00E0n th\u00E0nh. Kh\u00F4ng th\u1EF1c hi\u1EC7n y\u00EAu c\u1EA7u b\u1ED5 sung!
message.task.request-update.completed.not-allow=B\u01B0\u1EDBc {0} \u0111\u00E3 ho\u00E0n th\u00E0nh, \u0111\u00F3ng ho\u1EB7c hu\u1EF7. Kh\u00F4ng th\u1EF1c hi\u1EC7n tr\u1EA3 v\u1EC1!
message.task.additional-request.completed.fail= Phi\u1EBFu \u0111\u00E3 hu\u1EF7, ho\u00E0n th\u00E0nh ho\u1EB7c \u0111\u00F3ng. Kh\u00F4ng th\u1EC3 th\u1EF1c hi\u1EC7n y\u00EAu c\u1EA7u b\u1ED5 sung!
message.task.close.incompleted.not-allow=Phi\u1EBFu ch\u01B0a ho\u00E0n th\u00E0nh. Kh\u00F4ng th\u1EF1c hi\u1EC7n \u0111\u00E1nh gi\u00E1!
message.task.recall.not-allow=B\u01B0\u1EDBc {0} kh\u00F4ng \u1EDF tr\u1EA1ng th\u00E1i \u0111ang x\u1EED l\u00FD!
message.task.not-exists=B\u01B0\u1EDBc kh\u00F4ng t\u1ED3n t\u1EA1i!
message.task.completed.not-allow=Tr\u1EA1ng th\u00E1i phi\u1EBFu/ b\u01B0\u1EDBc thay \u0111\u1ED5i. Kh\u00F4ng th\u1EF1c hi\u1EC7n/ ph\u00EA duy\u1EC7t b\u01B0\u1EDBc!
message.task.change-assignee.not-allow=Tr\u1EA1ng th\u00E1i phi\u1EBFu/ b\u01B0\u1EDBc thay \u0111\u1ED5i. Kh\u00F4ng th\u1EF1c hi\u1EC7n \u1EE7y quy\u1EC1n!
message.ticket.request-update.not-allow=Tr\u1EA1ng th\u00E1i phi\u1EBFu/ b\u01B0\u1EDBc thay \u0111\u1ED5i. Kh\u00F4ng th\u1EF1c hi\u1EC7n tr\u1EA3 v\u1EC1!
message.task.completed=B\u01B0\u1EDBc {0} \u0111\u00E3 ho\u00E0n th\u00E0nh. Kh\u00F4ng th\u1EF1c hi\u1EC7n b\u00E0n giao!
### PaymentSchedule ###

message.payment.success = T\u1EA1o l\u1ECBch thanh to\u00E1n th\u00E0nh c\u00F4ng
message.payment.pending = T\u1EDD tr\u00ECnh \u0111\u00E3 \u0111\u01B0\u1EE3c thanh to\u00E1n, kh\u00F4ng \u0111\u01B0\u1EE3c thanh to\u00E1n l\u1EA1i
message.payment.null = T\u1EA1o l\u1ECBch thanh to\u00E1n th\u1EA5t b\u1EA1i, data null


### TemplateManager ###

message.manageTemplate.clone.success = Nh\u00E2n b\u1EA3n bi\u1EC3u m\u1EABu th\u00E0nh c\u00F4ng
message.manageTemplate.clone.fail = Nh\u00E2n b\u1EA3n bi\u1EC3u m\u1EABu th\u1EA5t b\u1EA1i
message.manageTemplate.clone.notExist = Kh\u00F4ng tin th\u1EA5y bi\u1EC3u m\u1EABu
message.manageTemplate.clone.nameLength = T\u00EAn bi\u1EC3u m\u1EABu v\u01B0\u1EE3t qu\u00E1 100 k\u00FD t\u1EF1 cho ph\u00E9p
message.manageTemplate.clone.urlNameLength = M\u00E3 s\u1EED d\u1EE5ng v\u01B0\u1EE3t qu\u00E1 50 k\u00FD t\u1EF1 cho ph\u00E9p

message.systemGroup.duplicate = \u0110\u00E3 t\u1ED3n t\u1EA1i b\u1EA3n ghi \u1EDF nh\u00F3m hi\u1EC7n t\u1EA1i
message.systemGroup.groupNotFound = Kh\u00F4ng t\u00ECm th\u1EA5y nh\u00F3m
message.systemGroup.duplicateName = \u0110\u00E3 t\u1ED3n t\u1EA1i nh\u00F3m
message.systemGroup.createNew = - T\u1EA1o m\u1EDBi nh\u00F3m
message.systemGroup.changeName = - Ch\u1EC9nh s\u1EEDa t\u00EAn nh\u00F3m t\u1EEB {0} th\u00E0nh {1}
message.systemGroup.addUser = - Th\u00EAm m\u1EDBi ng\u01B0\u1EDDi d\u00F9ng: {0}
message.systemGroup.deleteUser = - X\u00F3a ng\u01B0\u1EDDi d\u00F9ng: {0}
message.systemGroup.addList = - Th\u00EAm m\u1EDBi: {0}
message.systemGroup.deleteList = - X\u00F3a: {0}
message.systemGroup.changeDescription = - Ch\u1EC9nh s\u1EEDa m\u00F4 t\u1EA3 t\u1EEB {0} th\u00E0nh {1}
message.systemGroup.changeTime = - Ch\u1EC9nh th\u1EDDi gian ph\u00E2n quy\u1EC1n t\u1EEB {0} th\u00E0nh {1}

message.bpmTemplatePrint.deleteExistTicket = M\u1EABu tr\u00ECnh k\u00FD \u0111\u00E3 t\u1EA1o phi\u1EBFu y\u00EAu c\u1EA7u kh\u00F4ng cho ph\u00E9p x\u00F3a
message.bpmTemplatePrint.notFound = Kh\u00F4ng t\u00ECm th\u1EA5y m\u1EABu tr\u00ECnh k\u00FD